import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import { CULT_CLIENT_TYPES, ICultServiceOld } from "@curefit/cult-client"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { Action, DescriptionWidget, InfoCard, ProductDetailPage } from "../../common/views/WidgetView"
import { ALBUS_CLIENT_TYPES, IHealthfaceService, SessionReportExtendedResponse } from "@curefit/albus-client"
import * as _ from "lodash"
import { getLiveReportFilters, LIVE_REPORT_V2_ASPECT_RATIO, LIVE_REPORT_V2_ASPECT_RATIO_STR } from "../../util/LiveUtil"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../../util/AppUtil"
import { PageWidget } from "../../page/Page"
import { Tag, TagViewWidget } from "../fitnessreport/FitnessReportPageView"
import CultUtil from "../../util/CultUtil"
import { InfoWidgetV3, WidgetView, CardContainerWidget, HeaderWidget } from "@curefit/apps-common"


@injectable()
class LivePTReportPageViewBuilder {
    shareMessage: {
        shareTitle: string
        shareMessage: string
    }
    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultServiceOld,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService) { }


    async getReportStatus(bookingId: number): Promise<boolean> {
        const reportResponse = await this.cultPTService.getSessionReport(bookingId, "CULTFIT")
        if (!_.isEmpty(reportResponse.sessionReport)) {
            return true
        }
        return false
    }

    async updateFitnessReport(bookingId: number, updateParams: {
        imageUrl: string;
    }): Promise<boolean> {
        const updatedResponse = await this.cultPTService.updateSessionReport(bookingId, updateParams, "CULTFIT")
        if (updatedResponse) {
            return true
        }
        return false
    }

    async buildView(userContext: UserContext, bookingId: number): Promise<ProductDetailPage> {
        const widgets = []
        const reportPage = new ProductDetailPage()
        const reportResponse = await this.cultPTService.getSessionReport(bookingId, "CULTFIT")
        if (!reportResponse.sessionDetails) {
            return reportPage
        }
        this.shareMessage = this.getShareTitleAndMessage(userContext, reportResponse)
        if (reportResponse.sessionReport.state === "PARTIAL") {
            widgets.push(this.reportNotGeneratedWidget(reportResponse))
        }
        widgets.push(await this.buildReportShareCardWidget(userContext, reportResponse, bookingId))
        const performanceWidget = this.buildPerformanceWidget(reportResponse)
        const focusAreaWidget = { ...this.buildFocusAreasWidget(reportResponse), hasDividerBelow: false }
        const wodInfoWidget = await this.buildWodInfoWidget(reportResponse)
        const performanceShareCardWidget: WidgetView[] = [performanceWidget, focusAreaWidget]
        if (wodInfoWidget) {
            performanceShareCardWidget.push(wodInfoWidget)
        }
        widgets.push(this.getPerformanceShareCardWidget(performanceShareCardWidget))

        reportPage.actions = this.getPageActions(userContext, reportResponse)
        reportPage.widgets = widgets.filter(widget => !_.isEmpty(widget))
        return reportPage
    }

    private reportNotGeneratedWidget(reportResponse: SessionReportExtendedResponse): InfoWidgetV3 {
        return {
            widgetType: "INFO_WIDGET_V3",
            description: reportResponse.sessionReport.alert.finalReportMsg,
        }
    }

    private getShareTitleAndMessage(userContext: UserContext, reportResponse: SessionReportExtendedResponse) {
        const defaultUrl = "https://cure.app.link/NlVWvxNhN9"
        const shareMessage = `Just completed a ${reportResponse.sessionDetails.productName} Online Personal Training with ${reportResponse.sessionDetails.trainerName} on the cult.fit app! You too can workout with your own personal trainer on ${defaultUrl}!`
        return {
            shareTitle: `Class Report`,
            shareMessage
        }
    }

    private async buildReportShareCardWidget(userContext: UserContext, reportResponse: SessionReportExtendedResponse, bookingId: number) {
        const userData = await userContext.userPromise
        const userName = _.isNil(userData) ? "" : userData.firstName
        const userNameCapitals = userName ? userName.toUpperCase() : ""
        const formattedTime = reportResponse.sessionReport.sessionDurationInMinutes
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const workoutDurationInfolet = {
            cellType: "INFOLET",
            title: "WORKOUT DURATION",
            data: `${formattedTime}`,
            units: "Mins",
            headerColor: "#ffffff"
        }
        const metrics = [], footerMetrics = []
        metrics.push(workoutDurationInfolet)
        metrics.push({
            cellType: "INFOLET",
            title: "APPROX. CALORIES",
            data: reportResponse.sessionReport.state === "PARTIAL" ? "_" : reportResponse.sessionReport.caloriesBurned,
            units: "Cal",
            headerColor: "#ffffff",
        })
        const data = reportResponse.sessionDetails.trainerExperienceInYears ? `${reportResponse.sessionDetails.trainerExperienceInYears} yrs exp. ` : ""
        footerMetrics.push({
            cellType: "IMAGE_INFOLET",
            title: "TRAINER",
            subTitle: `${reportResponse.sessionDetails.trainerName}`,
            data,
            image: reportResponse.sessionDetails.trainerDisplayImage,
            headerColor: "#ffffff",
            backgroundColor: "#f2f4f8"
        })
        const timeFormatted = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, reportResponse.sessionDetails.startTime, "ddd MMM D | hh:mm A")
        const reportShareCardWidget: PageWidget = {
            widgetType: "DIGITAL_SHARE_CARD_WIDGET",
            header: {
                title: reportResponse.sessionDetails.productName,
                subTitle: `${timeFormatted} | ${Math.round(reportResponse.sessionDetails.expectedDurationInMinutes)} Min`,
                style: {
                    borderWidth: 1,
                    borderColor: "rgba(0,0,0,0.07)"
                },
            },
            isHeaderHeightDynamic: true,
            showButtons: true,
            showClose: false,
            noFilterEditing: true,
            metrics: metrics,
            footerMetrics: footerMetrics,
            bookingId: bookingId,
            button: {
                text: "EDIT AND SHARE YOUR ACHIEVEMENT",
                secondaryText: "SHARE YOUR ACHIEVEMENT",
                shareIcon: "/image/pulse/share-icon-android.png",
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        ...this.shareMessage
                    }
                },
                actionButtonOuterStyle: {
                    marginTop: -4,
                    marginBottom: 28,
                    alignSelf: "flex-start",
                },
                actionButtonInnerStyle: {
                    backgroundColor: "#ECF0F6",
                    marginHorizontal: 14,
                }
            },
            isReportV2: true,
            isMoment: false,
            imageRatio: LIVE_REPORT_V2_ASPECT_RATIO,
            imageRatioStr: LIVE_REPORT_V2_ASPECT_RATIO_STR,
            cardV2SceneStyle: {
                marginVertical: 0
            },
            useGAuto: true,
            imageUrl: reportResponse.sessionReport.imageUrl,
            imageFilter: "WE_ARE_CULT_STAR",
            achievement: reportResponse.sessionReport.state === "PARTIAL" ? reportResponse.sessionReport.alert.detailedReportMsg : "",
            isReportIncomplete: false,
            contentId: reportResponse.sessionReport.id,
            productType: "LIVE_PT",
            fitnessReportFilters: getLiveReportFilters(userNameCapitals, null, tenant),
        }
        return reportShareCardWidget
    }

    private buildPerformanceWidget(reportResponse: SessionReportExtendedResponse): PageWidget {
        const meta = {
            title: "PERFORMANCE",
            shareConfig: {
                text: "SHARE",
                action: {
                    actionType: "SHARE_SCREENSHOT",
                    meta: {
                        ...this.shareMessage,
                        source: "performace-report"
                    }
                }
            }
        }
        const intensityInfo = {
            fancyTextHeader: "WORKOUT\nDURATION",
            data: `${reportResponse.sessionReport.sessionDurationInMinutes}`,
            units: "Min",
            headerColor: "#ffc431",
            big: true,
        }
        const auxiliaryData = [
            {
                header: "Calories\nBurnt",
                dataText: reportResponse.sessionReport.state === "PARTIAL" ? "-" : `${reportResponse.sessionReport.caloriesBurned}`,
                subText: "Cal",
                vertical: false,
                wide: true
            }
        ]
        const containerStyle = {
            shadowOpacity: 0,
            marginHorizontal: 0,
            elevation: 0
        }
        return {
            widgetType: "DIGITAL_CLASS_PERFORMANCE_WIDGET",
            hasDividerBelow: false,
            containerStyle: containerStyle,
            intensityInfo: intensityInfo,
            auxiliaryData: auxiliaryData,
            intensity_info: intensityInfo,
            auxiliary_data: auxiliaryData,
            meta
        }
    }

    private buildFocusAreasWidget(reportResponse: SessionReportExtendedResponse) {
        let showBodyParts
        const tags: Tag[] = []
        reportResponse.sessionReport.bodyParts.forEach(bodyPart => {
            if (bodyPart.isActive) {
                showBodyParts = true
            }
            if (bodyPart.display) {
                tags.push({
                    title: bodyPart.title,
                    value: "tick",
                    disabled: !bodyPart.isActive,
                    style: bodyPart.isActive ? { borderWidth: 1, borderColor: "#e2e4e8" } : { backgroundColor: "#f2f4f8" }
                })
            }
        })
        if (showBodyParts) {
            const focusAreasWidget: TagViewWidget = {
                widgetType: "TAG_VIEW_WIDGET",
                backgroundColor: "#ffffff",
                dividerType: "NONE",
                header: "YOU FOCUSSED ON",
                headerTextStyle: { color: "#888E9E", fontSize: 16 },
                tags,
                hasShadow: false,
                tagValueType: "ICON",
                noPaddingLeft: true,
                tagContainerStyle: {
                    marginTop: 10,
                    marginBottom: 40,
                }
            }
            return focusAreasWidget
        }
    }

    private async buildWodInfoWidget(reportResponse: SessionReportExtendedResponse) {
        const wodId = reportResponse.sessionReport.wodId
        if (!_.isEmpty(wodId)) {
            const wod = await this.herculesService.getSimpleWodById(wodId)
            const headerWidget: HeaderWidget = {
                widgetType: "HEADER_WIDGET",
                widgetTitle: {
                    title: "WORKOUT OF THE DAY",
                    subTitle: CultUtil.getWodMovements(wod),
                    subTitleNumberOfLine: 5
                },
                titleStyle: { color: "#888e9e", fontSize: 16, fontFamily: "BrandonText-Black" },
                subTitleStyle: { fontFamily: "BrandonText-Regular", fontSize: 12, color: "#888E9E", lineHeight: 18, marginTop: 0, marginBottom: 26 },
                hasDividerBelow: false,
            }
            return headerWidget
        }
    }

    private getPerformanceShareCardWidget(widgets: WidgetView[]): CardContainerWidget {
        return {
            widgets: widgets,
            widgetType: "CARD_CONTAINER_WIDGET",
            isShareCard: true,
            containerStyle: {
                borderWidth: 1,
                borderColor: "#e2e4e8",
            }
        }
    }

    private getPageActions(userContext: UserContext, reportResponse: SessionReportExtendedResponse) {
        const isAndroid = _.get(userContext, "sessionInfo.osName", "").toLowerCase() === "android"

        const mutipleShareAction: Action = {
            title: "SHARE YOUR ACHIEVEMENT",
            actionType: "SHARE_SCREENSHOT",
            meta: {
                ...this.shareMessage
            }
        }

        const metaAction: Action = {
            actionType: "NAVIGATION",
            url: "curefit://sharingcarousel?title=Share Report",
            meta: {
                action: mutipleShareAction
            },
            analyticsData: {
                eventKey: "button_click_event",
                eventData: {
                    source: "reportsharefloatingCTA",
                    productType: "liveptreport",
                    actionType: "sharefullreport"
                }
            }
        }

        const submitAction: Action = {
            actionType: "SHARE_MUTIPLE_CARD",
            title: isAndroid ? "SHARE FULL REPORT" : "SHARE REPORT",
            meta: {
                action: metaAction
            }
        }
        return [submitAction]
    }
}

export default LivePTReportPageViewBuilder
