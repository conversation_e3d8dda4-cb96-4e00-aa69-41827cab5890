import { inject, injectable } from "inversify"
import {
    ActionType,
    Activity,
    ActivityListWidget,
    HeaderWidget,
    InfoActionWidget,
    ListWidgetV2, MyLiveClassesWidget,
    PageTypes,
    PTGoalSummaryWidget,
    WidgetView
} from "@curefit/apps-common"
import { LPTGoalPage } from "../../common/views/WidgetView"
import { UserContext } from "@curefit/vm-models"
import * as _ from "lodash"
import { LivePTUserProfile, UserScoresAndState } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld } from "@curefit/cult-client"
import {
    ALBUS_CLIENT_TYPES,
    CareTeam,
    ConsultationSellableProduct,
    HealthfaceProductInfo,
    IHealthfaceService,
    LivePTProductSpec
} from "@curefit/albus-client"
import { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../util/CareUtil"
import { ListItem, MyLiveClassesWidgetV2 } from "@curefit/apps-common/dist/src/widgets/interfaces"
import { TimeUtil } from "@curefit/util-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { ICareBusiness } from "../../care/CareBusiness"
import { MyLiveClassesWidgetView } from "../../page/vm/widgets/MyLiveClassesWidgetView"
import { MyLiveClassesWidgetV2View } from "../../page/vm/widgets/MyLiveClassesWidgetV2View"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ProductType } from "@curefit/product-common"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import CultUtil from "../../util/CultUtil"
import AppUtil from "../../util/AppUtil"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import FitnessReportPageConfig from "../fitnessreport/FitnessReportPageConfig"
import { FitnessReportSummaryWidgetView } from "../../page/vm/widgets/FitnessReportSummaryWidgetView"

@injectable()
class LivePtGoalPageViewBuilder {
    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultServiceOld,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) private cultPTService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) public catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.FitnessReportPageConfig) public fitnessReportPageConfig: FitnessReportPageConfig) { }

    async buildView(userContext: UserContext, productType: ProductType): Promise<LPTGoalPage> {
        const userId = userContext.userProfile.userId
        const appName = "CUREFIT_APP"
        const goalPage = new LPTGoalPage()
        goalPage.header = {
            title: "Fitness Journey"
        }
        const goals = this.cultFitService.getAllLivePTGoals(userId, appName)
        const userGoal: LivePTUserProfile = await this.cultFitService.getLivePTUserProfile(userId, appName)
        const assessment: UserScoresAndState = await this.cultFitService.getLivePTUserAssessmentScore(userId, appName)
        const subCategoryCode = productType === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        const productId = productType === "LIVE_PERSONAL_TRAINING" ? LIVE_PT_SNC_PRODUCT_ID : LIVE_SGT_SNC_PRODUCT_ID
        const goalSummaryWidget: PTGoalSummaryWidget = {
            fullWidth: true,
            widgetType: "PT_GOAL_SUMMARY_WIDGET",
            fitnessDetails: [{
                icon: userGoal.imageUrl,
                title: "GOAL",
                subTitle: userGoal.goal,
                action: {
                    title: "CHANGE",
                    actionType: "CHANGE_PT_GOAL",
                    meta: {
                        currentGoalId: userGoal.goalId,
                        goals: await goals,
                        recommendationSteps: CultUtil.getGenderSelectionRecommendationSteps(),
                        header: {
                            title: "Change your fitness goal?",
                            subTitle: "Select One"
                        },
                        actionTitle: "CHANGE",
                        toastMessage: "Please select atleast one goal to proceed"
                    }
                }
            }]
        }

        const fitnessReportWidget = await new FitnessReportSummaryWidgetView().getWidgetView(userContext, this.cultFitService, this.fitnessReportPageConfig)

        goalPage.actions = [await this.careBusiness.getLivePTSessionBookAction(userContext, { productId, actionTitle: "BOOK SESSION", subCategoryCode })]

        if (productType === "LIVE_SGT") {
            goalPage.widgets.push(goalSummaryWidget)
            goalPage.widgets.push(fitnessReportWidget)
            goalPage.widgets.push(await this.getfooterWorkoutsWidget(userContext, productType))
        } else {
            if (assessment.state === "CLASS_NOT_BOOKED" || assessment.state === "CLASS_NOT_STARTED" || assessment.state === "ASSESSMENT_NOT_FILLED") {
                if (productType === "LIVE_PERSONAL_TRAINING") {
                    const footerInfoWidget: InfoActionWidget = {
                        widgetType: "INFO_ACTION_WIDGET",
                        containerStyle: {
                            backgroundColor: "#F2F4F8"
                        },
                        info: assessment.message
                    }
                    goalSummaryWidget.footerWidget = footerInfoWidget
                    goalPage.widgets.push(goalSummaryWidget)
                }
                goalPage.widgets.push(fitnessReportWidget)
                goalPage.widgets.push(await this.getfooterWorkoutsWidget(userContext, productType))
            } else if (assessment.state == "ASSESSMENT_AVAILABLE") {
                const goingWell = _.join(_.map(assessment.userScores.goingWell.qualities, quality => quality.name), ", ")
                const improvmentsNeeded = _.join(_.map(assessment.userScores.improveAt.qualities, quality => quality.name), ", ")
                if (!_.isEmpty(goingWell)) {
                    goalSummaryWidget.fitnessDetails.push({
                        icon: "/image/icons/going_well.png",
                        title: "WHAT'S GOING WELL",
                        subTitle: goingWell,
                    })
                }
                if (!_.isEmpty(improvmentsNeeded)) {
                    goalSummaryWidget.fitnessDetails.push({
                        icon: "/image/icons/improvement.png",
                        title: "GET STRONGER AT",
                        subTitle: improvmentsNeeded,
                    })
                }

                goalPage.widgets.push(goalSummaryWidget)
                goalPage.widgets.push(fitnessReportWidget)
                goalPage.widgets.push(...this.getDetailedAssessmentWidget(userContext, assessment))
            }
        }
        return goalPage
    }

    private async getfooterWorkoutsWidget(userContext: UserContext, productType: ProductType): Promise<WidgetView> {
        const patientsList = this.healthfaceService.getAllPatients(userContext.userProfile.userId)
        const products: HealthfaceProductInfo[] = await this.cultPTService.getConsultationProductsInfoByGroupType(userContext.userProfile.cityId, productType)
        const workoutIds = _.map(products, product => {
            const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
            return (consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec).cultWorkoutId
        })
        const workoutIdsString = _.join(workoutIds, ",")
        const subCategoryCode = productType === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        const activities: Activity[] = await Promise.all(_.map(products, async product => {
            let subTitle

            const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
            const patients = await patientsList
            if (!_.isEmpty(patients)) {
                const selfPatient = patients.find(patient => patient.relationship === "Self")
                if (!_.isEmpty(selfPatient)) {
                    const preferedDoctor: CareTeam[] = await this.cultPTService.getPreferredDoctorForDoctorType(userContext.userProfile.userId, selfPatient.id, consultationSellableProduct.consultationProduct.doctorType, false, "CULTFIT")
                    if (!_.isEmpty(preferedDoctor)) {
                        subTitle = `with ${preferedDoctor[0].doctor.name}`
                    }
                }
            }
            const workout = consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec
            const seoParams: SeoUrlParams = {
              productName: workout.cultWorkoutName
            }
            const url = ActionUtil.cultWorkoutV2(workout.cultWorkoutId.toString(), undefined, userContext.sessionInfo.userAgent, seoParams, productType, PageTypes.CultWorkoutPageV2)
            const rightAction = await this.careBusiness.getLivePTSessionBookAction(userContext, { productId: consultationSellableProduct.productCode, actionTitle: "BOOK", subCategoryCode })
            const navigationActionType: ActionType = "NAVIGATION"
            return {
                title: workout.cultWorkoutName,
                subTitle: subTitle,
                image: workout.workoutSmallImageUrl,
                action: {
                    actionType: navigationActionType,
                    url: url
                },
                rightAction
            }
        }))
        const footerWorkoutsWidget: ActivityListWidget = {
            widgetType: "ACTIVITY_LIST_WIDGET",
            header: {
                title: "Book Next Session"
            },
            activities: activities
        }
        return footerWorkoutsWidget
    }


    private getDetailedAssessmentWidget(userContext: UserContext, assessment: UserScoresAndState): WidgetView[] {
        const widgets: WidgetView[] = []
        const tz = userContext.userProfile.timezone
        const time = TimeUtil.formatDateStringInTimeZone(assessment.lastAssessmentDetails.lastAssessmentAt, tz, "D MMM")
        const headerSubtitle = `Last Assessment by ${assessment.lastAssessmentDetails.lastAssessmentBy} for ${assessment.lastAssessmentDetails.workoutName} on ${time}`
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            style: { marginTop: 20 },
            titleStyle: { marginLeft: 20, fontSize: 18, lineHeight: 26 },
            subTitleStyle: { marginLeft: 20, fontSize: 12, marginTop: 1, lineHeight: 15 },
            widgetTitle: {
                title: "Detailed Assessment",
                subTitle: headerSubtitle
            }
        }
        widgets.push(headerWidget)
        if (!_.isEmpty(assessment.userScores.goingWell) && !_.isEmpty(assessment.userScores.goingWell.qualities)) {
            const qualities: ListItem[] = assessment.userScores.goingWell.qualities.map(quality => {
                return {
                    title: quality.name,
                    subTitle: quality.message,
                    icon: quality.imageUrl
                }
            })
            const goingWellWidget: ListWidgetV2 = {
                header: {
                    title: "WHAT’S GOING WELL",
                    titleStyle: {
                        marginTop: 20,
                        marginBottom: 5,
                        color: "#67c893",
                        fontFamily: "BrandonText-Bold",
                        fontSize: 12,
                        lineHeight: 18
                    }
                },
                widgetType: "LIST_WIDGET_V2",
                items: qualities
            }
            widgets.push(goingWellWidget)
        }
        if (!_.isEmpty(assessment.userScores.improveAt) && !_.isEmpty(assessment.userScores.improveAt.qualities)) {
            const improvements: ListItem[] = assessment.userScores.improveAt.qualities.map(improvement => {
                return {
                    title: improvement.name,
                    subTitle: improvement.message,
                    icon: improvement.imageUrl
                }
            })
            const improvementWidget: ListWidgetV2 = {
                header: {
                    title: "GET STRONGER AT",
                    titleStyle: {
                        marginTop: 20,
                        marginBottom: 5,
                        color: "#f7b45f",
                        fontFamily: "BrandonText-Bold",
                        fontSize: 12,
                        lineHeight: 18
                    }
                },
                widgetType: "LIST_WIDGET_V2",
                items: improvements
            }
            widgets.push(improvementWidget)
        }
        return widgets
    }
}

export default LivePtGoalPageViewBuilder
