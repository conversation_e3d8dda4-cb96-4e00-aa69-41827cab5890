

import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { injectable, inject } from "inversify"
import { UserContext, Action } from "@curefit/vm-models"
import { AnnouncementDetails } from "../../announcement/AnnouncementViewBuilder"
import { AnnouncementBusiness } from "../../announcement/AnnouncementBusiness"
import { ALBUS_CLIENT_TYPES, DOCTOR_TYPE, IHealthfaceService } from "@curefit/albus-client"
import { ActionUtil, CareUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { PageTypes, User } from "@curefit/apps-common"
import { CareUtil as AppCareUtil, LIVE_PT_SNC_PRODUCT_ID, LIVE_PT_YOGA_PRODUCT_ID } from "../../../app/util/CareUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { CFUserProfile } from "../../page/vm/CFUserProfile"
import { SUB_CATEGORY_CODE } from "@curefit/care-common"
import { CdnUtil } from "@curefit/util-common"


@injectable()
class PtAtHomeViewBuilder {

  constructor(@inject(ALBUS_CLIENT_TYPES.HealthfaceService) public healthfaceService: IHealthfaceService,
    @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness) {
  }

  async getRecommendationSteps(userContext: UserContext, type: string): Promise<any> { // pageUrl curefit://classrecommendationgenerator?type=PT_AT_HOME
    const steps = [
      {
        title: "Logging your fitness goal",
        time: 1000
      },
      {
        title: "Taking into account your fitness",
        time: 1000
      },
      {
        title: "Personalising your journey plan",
        time: 1000
      },
      {
        title: "All set. Welcome aboard, athlete!",
        time: 1000
      }
    ]
    if (type === "SGT") {
      return {
        title: "Bringing it together",
        steps,
        action: {
          actionType: "NAVIGATION",
          url: "curefit://tabpage?pageId=cult&selectedTab=LiveSGT"
        }
      }
    } else if (type === "PT_AT_HOME") {
      return {
        title: "Bringing it together",
        steps,
        action: {
          actionType: "NAVIGATION",
          url: "curefit://tabpage?pageId=cult&selectedTab=LivePT"
        }
      }
    }
  }

  async getLandingPage(userContext: UserContext): Promise<any> {  // curefit://nuxlandingpage?type=PT_AT_HOME
    return {
      title: `Welcome to Personal Training!`,
      subtitle: "Just answer 2 questions to help us find the best personal trainer for you.",
      note: "*Note: Online Personal Training will be held on Zoom app. It will be a video led Personal Training session at home.",
      pageAction: {
        title: "Let's Begin",
        actionType: "NAVIGATION",
        url: "curefit://userform?formId=LIVE_PT_ONBOARDING",
        navigationType: "NAVIGATE_REPLACE"
      },
      backgroundImageUrlOne: CdnUtil.getCdnUrl("curefit-content/image/icons/ptAtHomeOnboarding/live_pt.png")
    }
  }


  async getLivePtOnboarding(nextActionUrl: string, userContext: UserContext, user: User, patientId: number, doctorType: DOCTOR_TYPE, bookingId: number, zoomParticipantId: string) { // curefit://liveptonboardingone
    const nextScreenTime = 10000
    const skipAction = { ...AppCareUtil.getZoomSdkActionFromUrl(userContext, nextActionUrl, user, patientId, doctorType, bookingId, zoomParticipantId), title: "SKIP", }
    return {
      screenOne: {
        title: "Place the phone in landscape mode",
        subTitle: "Place your phone horizontally on the floor and move few steps back so that you are entirely visible to the trainer",
        icon: CdnUtil.getCdnUrl("curefit-content/image/icons/ptAtHomeOnboarding/mobile_landscape.png"),
        nextScreenTime: nextScreenTime,
        skipAction: skipAction,
        action: {
          actionType: "NAVIGATION",
          navigationType: "NAVIGATE_REPLACE",
          url: `curefit://${PageTypes.LivePtOnboardingTwo}?patientId=${patientId}&doctorType=${doctorType}`
        }
      },
      screenTwo: {
        title: "Position yourself such that your head, feet and mat are visible on the screen",
        nextScreenTime: 10000,
        skipAction: skipAction,
        action: skipAction
      }
    }
  }

}



export default PtAtHomeViewBuilder
