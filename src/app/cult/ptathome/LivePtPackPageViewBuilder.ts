import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    AggregatedMembershipInfo,
    ALBUS_CLIENT_TYPES,
    BookingDetail,
    BundleFilterType,
    BundleSessionSellableProduct,
    BundleStartDateDetails,
    IHealthfaceService,
    Patient,
    SUB_CATEGORY_CODE
} from "@curefit/albus-client"
import {
    ConsultationProduct,
    DiagnosticProduct,
    DiagnosticProductResponse,
    HealthfaceTenant,
    ManagedPlanPackInfo
} from "@curefit/care-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { GMF_CLIENT_TYPES, IGMFClient as IGoalManagementService } from "@curefit/gmf-client"
import { PackOffersResponse } from "@curefit/offer-service-client"
import {
    DatePickerWidget,
    DescriptionWidget,
    Header,
    ManageOptionPayload,
    ManageOptions,
    ProductListWidget
} from "../../common/views/WidgetView"
import AppUtil from "../../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import ICRMIssueService from "../../crm/ICRMIssueService"
import { CareUtil } from "../../util/CareUtil"
import IssueBusiness, { IssueDetailView } from "../../crm/IssueBusiness"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService, ICultServiceOld } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { MembershipPaymentType } from "@curefit/order-common"
import {
    Action,
    CultActivePackInfoWidget,
    HeaderWidget,
    IInfoCard,
    ImageOverlayCardContainerWidget,
    InfoCard,
    MyLiveClassesWidget,
    MyLiveClassesWidgetV2,
    ProductBenefit,
    ProductBenefitWidget,
    ProductRadioSelectionWidget,
    ToggleBarWidget,
    WidgetView
} from "@curefit/apps-common"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { RoundedImageGridWidgetView } from "../../page/vm/widgets/RoundedImageGridWidgetView"
import { PayByMembershipDetails, ProductPayByMembershipMap, } from "@curefit/oms-api-client"
import { TimeUtil } from "@curefit/util-common"
import CultUtil from "../../util/CultUtil"
import { RoundedIconListWidgetView } from "../../page/vm/widgets/cult/RoundedIconListWidgetView"
import { PtGoalSummaryWidgetView } from "../../page/vm/widgets/cult/PtGoalSummaryWidgetView"
import { ICareBusiness } from "../../care/CareBusiness"
import { MyLiveClassesWidgetView } from "../../page/vm/widgets/MyLiveClassesWidgetView"
import { MyLiveClassesWidgetV2View } from "../../page/vm/widgets/MyLiveClassesWidgetV2View"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ErrorFactory } from "@curefit/error-client"
import { ProductType } from "@curefit/product-common"
import { CultPackStartDateOptions } from "../../pack/CultPackDetailViewBuilder"
import { Banner, BannerCarouselWidget } from "../../page/PageWidgets"
import { CareWidgetUtil } from "@curefit/vm-models"


@injectable()
export class LivePtPackPageViewBuilder {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
        @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGoalManagementService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) public herculesService: IHerculesService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService
    ) {

    }

    async getBeforeBookingBundleSessionsPage(userContext: UserContext, isNotLoggedIn: boolean, userId: string, deviceId: string, productId: string, subCategoryCode: SUB_CATEGORY_CODE, filterType?: BundleFilterType, useCultMemberShipForPayment?: boolean, membershipPaymentType?: MembershipPaymentType, selectedDate?: string) {
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        let bundleProductIds: string[] = []
        if (filterType) {
            const bundleSellableProductResponse = await this.healthfaceService.getBundleSellableProducts(subCategoryCode, healthfaceTenant)
            const filteredBundleType = _.find(bundleSellableProductResponse.bundleTypes, bundleType => bundleType.type === filterType)
            if (!_.isEmpty(filteredBundleType)) {
                bundleProductIds = filteredBundleType.products
            }
        } else {
            const products: DiagnosticProductResponse[] = await this.healthfaceService.browseProducts("BUNDLE", subCategoryCode, healthfaceTenant, true, undefined, undefined, undefined, undefined, true)
            bundleProductIds = products.map(product => product.productCode)
        }
        const patientsListPromise = this.healthfaceService.getAllPatients(userId)
        const patientsList = await patientsListPromise
        const selfPatient: Patient = patientsList.find(patient => patient.relationship === "Self")
        const isIOS: boolean = userContext.sessionInfo.osName.toLowerCase() === "ios"
        const isLiveSGT = subCategoryCode === "LIVE_SGT"
        const isLivePT = subCategoryCode === "LIVE_PERSONAL_TRAINING"
        const disableBuying = isIOS && isLiveSGT
        const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            bundleProductIds,
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const bundleProductsPromises = _.map(bundleProductIds, async (productId) => {
            const healthfaceProduct = await this.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: subCategoryCode, productCodeCsv: productId }, healthfaceTenant)
            return <BundleSessionSellableProduct>(healthfaceProduct[0].baseSellableProduct)
        })
        const bundleProducts = await Promise.all(bundleProductsPromises)
        let selectedProduct = _.find(bundleProducts, product => product.productCode === productId)
        if (_.isEmpty(selectedProduct)) {
            selectedProduct = bundleProducts[0]
        }
        const bundleoffers = await offerPromise
        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(selectedProduct, bundleoffers)
        const offerIds = _.map(offerDetails.offers, (offer: any) => { return offer.offerId })
        const appliedOffers: any[] = CareUtil.getOffersApplied(bundleProducts, selectedProduct, bundleoffers)
        const widgets = []
        const isLivePTPackPageWithOffersSupported = AppUtil.isLivePTPackPageWithOffersSupported(userContext)
        const productPayByMembershipMap: ProductPayByMembershipMap = {}
        let payByMembershipDetails
        let existingCultMemberWidget, existingCultMemberToggleWidget, toggleSelected
        let showOffersWidget = true
        if (!_.isEmpty(productPayByMembershipMap)) {
            payByMembershipDetails = productPayByMembershipMap[selectedProduct.productCode]
            const isCultMember = (payByMembershipDetails.cult && payByMembershipDetails.cult.isMember)
            const isMindMember = (payByMembershipDetails.mind && payByMembershipDetails.mind.isMember)
            if ((isCultMember && payByMembershipDetails.cult.isEligible) || (isMindMember && payByMembershipDetails.mind.isEligible)) {
                showOffersWidget = false
            }
            if ((isCultMember && payByMembershipDetails.cult.isEligible) || (isCultMember && !isMindMember)) {
                // check for auto selecting cult membership option for first time
                if (!membershipPaymentType) {
                    useCultMemberShipForPayment = true
                    membershipPaymentType = MembershipPaymentType.CULT
                }
                if (isLivePTPackPageWithOffersSupported) {
                    const existingCultMemberToggleWidgetData = this.getExistingCultMemberToggleWidget(membershipPaymentType, MembershipPaymentType.CULT, useCultMemberShipForPayment, productPayByMembershipMap, bundleProducts, payByMembershipDetails)
                    existingCultMemberToggleWidget = existingCultMemberToggleWidgetData.toggleBarWidget
                    toggleSelected = existingCultMemberToggleWidgetData.toggleSelected
                } else {
                    existingCultMemberWidget = await this.getExistingCultMemberWidget(membershipPaymentType, MembershipPaymentType.CULT, useCultMemberShipForPayment, payByMembershipDetails.cult.numDaysToDeduct, selectedProduct.duration, subCategoryCode, selectedProduct, payByMembershipDetails)
                }
            } else if (isMindMember) {
                if (!membershipPaymentType) {
                    useCultMemberShipForPayment = true
                    membershipPaymentType = MembershipPaymentType.MIND
                }
                if (isLivePTPackPageWithOffersSupported) {
                    const existingCultMemberToggleWidgetData = this.getExistingCultMemberToggleWidget(membershipPaymentType, MembershipPaymentType.MIND, useCultMemberShipForPayment, productPayByMembershipMap, bundleProducts, payByMembershipDetails)
                    existingCultMemberToggleWidget = existingCultMemberToggleWidgetData.toggleBarWidget
                    toggleSelected = existingCultMemberToggleWidgetData.toggleSelected
                } else {
                    existingCultMemberWidget = await this.getExistingCultMemberWidget(membershipPaymentType, MembershipPaymentType.MIND, useCultMemberShipForPayment, payByMembershipDetails.mind.numDaysToDeduct, selectedProduct.duration, subCategoryCode, selectedProduct, payByMembershipDetails)
                }
            }
        }

        let howItWorksItem, whatsInPackItem, packBenefits, packDescription, packPolicies, offerWidgetExist = false
        selectedProduct.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "PACK_STEPS": howItWorksItem = infoSection; break
                case "PACK_CONTENTS_DETAILED": whatsInPackItem = infoSection; break
                case "FORMATTED_DESCRIPTION": packDescription = infoSection; break
                case "PACK_BENEFITS": packBenefits = infoSection; break
                case "POLICIES": packPolicies = infoSection; break
            }
        })

        const livePtWorkoutWidget = await this.getLivePtWorkoutWidget(userContext, subCategoryCode)
        let offerWidget, startDateOption
        if (!isLivePTPackPageWithOffersSupported && !_.isEmpty(appliedOffers) && !disableBuying) {
            offerWidget = CareUtil.getOffersWidget("Offers applied", appliedOffers, userContext.sessionInfo.userAgent)
        }
        if (isLiveSGT || isLivePT) {
            startDateOption = await this.getStartDateOptions(userContext, selectedProduct, healthfaceTenant, selfPatient, selectedDate)
        }
        const isWebContext = AppUtil.isWeb(userContext)
        widgets.push(this.getPackDetailWidget(disableBuying, bundleProducts, selectedProduct, bundleoffers, appliedOffers, packDescription, productPayByMembershipMap, useCultMemberShipForPayment, existingCultMemberToggleWidget, startDateOption, isLivePTPackPageWithOffersSupported, isWebContext))
        if (isLivePTPackPageWithOffersSupported && !disableBuying) {
            widgets.push(this.getPackSelectionWidget(userContext, bundleProducts, selectedProduct, bundleoffers, appliedOffers, productPayByMembershipMap, useCultMemberShipForPayment, membershipPaymentType, toggleSelected))
        }
        if (existingCultMemberWidget && !disableBuying) {
            widgets.push(existingCultMemberWidget)
        }
        if (showOffersWidget && offerWidget) {
            offerWidgetExist = true
            widgets.push(offerWidget)
        }
        // if (disableBuying) {
        //     widgets.push(this.getIOSSGTBannerWidget(userContext))
        // }
        widgets.push(this.getProductBenefitWidget(packBenefits, offerWidgetExist, isLiveSGT))
        if (!_.isEmpty(packPolicies) && await AppUtil.isLivePTSGTNoShowEnabled(userContext)) {
            widgets.push(this.getPackPoliciesWidget(packPolicies, userContext))
        }
        if (!isLiveSGT) {
            widgets.push(this.getHowItWorksWidget(howItWorksItem))
        }
        if (!_.isEmpty(livePtWorkoutWidget)) {
            widgets.push(livePtWorkoutWidget)
        }
        if (disableBuying) {
            return { widgets: widgets, actions: undefined }
        }
        const actions = this.getPreBookingActions(userContext, isNotLoggedIn, patientsList, selectedProduct, offerIds, useCultMemberShipForPayment, membershipPaymentType, offerDetails, payByMembershipDetails, startDateOption)
        return { widgets: widgets, actions: actions }
    }

    private async getStartDateOptions(userContext: UserContext, selectedProduct: BundleSessionSellableProduct, healthfaceTenant: HealthfaceTenant, selfPatient: Patient, selectedDate?: string): Promise<CultPackStartDateOptions> {
        const userId = userContext.userProfile.userId
        const selfPatientId = selfPatient?.id ? selfPatient.id : null
        const startDateDetail: BundleStartDateDetails = await this.healthfaceService.getBundleStartDateDetails(userId, selfPatientId, selectedProduct.categoryCode, selectedProduct.subCategoryCode, healthfaceTenant)
        const isMandatoryStartDateSupported = true
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.todaysDateWithTimezone(tz) // "2020-08-04"
        const startDate: string = today
        const canChangeStartDate: boolean = true
        const minEligibleDate = TimeUtil.formatDateInTimeZone(tz, new Date(startDateDetail.suggestedStartDate))
        const maximumDate = TimeUtil.addDays(tz, minEligibleDate, startDateDetail.bufferDays)
        return {
            minEligibleDate: minEligibleDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDate || minEligibleDate,
            canChangeStartDate: canChangeStartDate
        }
    }

    getPackDetailWidget(disableBuying: boolean, bundleProducts: BundleSessionSellableProduct[], selectedProduct: BundleSessionSellableProduct, bundleoffers: PackOffersResponse, appliedOffers: any[], packDescription: ManagedPlanPackInfo, productPayByMembershipMap?: ProductPayByMembershipMap, useCultMemberShipForPayment?: boolean, existingCultMemberToggleWidget?: ToggleBarWidget, startDateOption?: CultPackStartDateOptions, isLivePTPackPageWithOffersSupported?: boolean, isWebContext?: boolean): ImageOverlayCardContainerWidget {
        const isLiveSgt = selectedProduct.subCategoryCode === "LIVE_SGT"
        const isLivePt = selectedProduct.subCategoryCode === "LIVE_PERSONAL_TRAINING"
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: selectedProduct.heroImageUrl,
        })
        imageOverlayContainerWidget.cardContainerStyle = { marginHorizontal: 20 }
        let productInfoSubTitleContainerStyle
        if (isLivePTPackPageWithOffersSupported) {
            productInfoSubTitleContainerStyle = { marginBottom: 0 }
            imageOverlayContainerWidget.cardContainerStyle = { paddingVertical: 10, paddingHorizontal: 15, marginHorizontal: 20 }
        }
        imageOverlayContainerWidget.widgets.push(CareUtil.getProductInfoWidget(selectedProduct, packDescription, productInfoSubTitleContainerStyle, isLivePTPackPageWithOffersSupported))

        const isDatePickerSupportedForPt = isLivePt && (isWebContext || isLivePTPackPageWithOffersSupported)
        const isDatePickerSupportedForSgt = isLiveSgt && isLivePTPackPageWithOffersSupported
        const isDatePickerSupported = isDatePickerSupportedForSgt || isDatePickerSupportedForPt
        if (isDatePickerSupported && !disableBuying && !_.isEmpty(startDateOption)) {
            const buildStartDateWidget = this.buildStartDateWidget(startDateOption, existingCultMemberToggleWidget)
            imageOverlayContainerWidget.widgets.push(buildStartDateWidget)
        }
        if (!(isLivePTPackPageWithOffersSupported || disableBuying)) {
            imageOverlayContainerWidget.widgets.push(CareUtil.getProductRadioSelectionWidget(bundleProducts, selectedProduct, bundleoffers, appliedOffers, productPayByMembershipMap, useCultMemberShipForPayment))
        }
        if (existingCultMemberToggleWidget && isLivePTPackPageWithOffersSupported && !disableBuying) {
            imageOverlayContainerWidget.widgets.push(existingCultMemberToggleWidget)
        }
        imageOverlayContainerWidget.productType = <ProductType>selectedProduct.subCategoryCode
        return imageOverlayContainerWidget
    }

    private async getExistingCultMemberWidget(membershipPaymentType: MembershipPaymentType, productType: MembershipPaymentType, useCultMemberShipForPayment: boolean, numDaysToDeduct: number, newMembershipDaysDuration: number, subCategoryCode: SUB_CATEGORY_CODE, selectedProduct: BundleSessionSellableProduct, payByMembershipDetails?: PayByMembershipDetails): Promise<ProductRadioSelectionWidget> {
        let selected, isDisabled, disabledDescription
        if (productType === "CULT") {
            isDisabled = payByMembershipDetails.cult ? !payByMembershipDetails.cult.isEligible : false
            selected = (membershipPaymentType === MembershipPaymentType.CULT && !isDisabled) ? useCultMemberShipForPayment : false
            disabledDescription = "Your cult membership has insufficient days"
        } else {
            isDisabled = payByMembershipDetails.mind ? !payByMembershipDetails.mind.isEligible : false
            selected = (membershipPaymentType === MembershipPaymentType.MIND && !isDisabled) ? useCultMemberShipForPayment : false
            disabledDescription = "Your mind membership has insufficient days"
        }
        let description = ""
        if (subCategoryCode === "LIVE_PERSONAL_TRAINING") {
            description = `Swap ${numDaysToDeduct} days of my ${productType.toLowerCase()} membership for ${selectedProduct.infoSection.numberOfSessions} Online Personal Training sessions`
        } else {
            description = `Swap ${numDaysToDeduct} days of my ${productType.toLowerCase()} membership for ${selectedProduct.infoSection.sellingTitle}`
        }
        return {
            data: [{
                id: "EXISTING_MEMBERSHIP",
                title: "Use my Cult Membership days",
                titleStyle: {
                    color: "#000000"
                },
                isSelected: selected,
                description: isDisabled ? disabledDescription : description,
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        useCultMemberShipForPayment: !selected,
                        membershipPaymentType: productType
                    }
                },
                isDisabled: isDisabled,
                descriptionIcon: isDisabled ? "/image/icons/cult/warning.png" : ""
            }],
            containerStyle: {
                paddingHorizontal: 20, paddingBottom: 20, marginTop: 5, backgroundColor: "#f2f4f8", borderColor: "#e2e4e8", borderWidth: 1, marginHorizontal: 20,
                borderRadius: 10
            },
            widgetType: "PRODUCT_RADIO_SELECTION_WIDGET",
            type: "SINGLE_SELECTION"
        }
    }

    private getExistingCultMemberToggleWidget(membershipPaymentType: MembershipPaymentType, productType: MembershipPaymentType, useCultMemberShipForPayment: boolean, productPayByMembershipMap: ProductPayByMembershipMap, bundleProducts: BundleSessionSellableProduct[], payByMembershipDetails?: PayByMembershipDetails): {
        toggleSelected: boolean,
        toggleBarWidget: ToggleBarWidget
    } {
        let selected, isDisabled, toggleSelected: boolean
        bundleProducts.forEach(product => {
            if (toggleSelected) {
                return true
            } else {
                const membershipDetails = productPayByMembershipMap[product.productCode]
                if (productType === "CULT") {
                    toggleSelected = membershipDetails.cult && membershipDetails.cult.isEligible && membershipPaymentType === MembershipPaymentType.CULT
                } else {
                    toggleSelected = membershipDetails.mind && membershipDetails.mind.isEligible && membershipPaymentType === MembershipPaymentType.MIND
                }
            }
        })
        if (productType === "CULT") {
            isDisabled = payByMembershipDetails.cult ? !payByMembershipDetails.cult.isEligible : false
            selected = (membershipPaymentType === MembershipPaymentType.CULT && !isDisabled) ? useCultMemberShipForPayment : false
        } else {
            isDisabled = payByMembershipDetails.mind ? !payByMembershipDetails.mind.isEligible : false
            selected = (membershipPaymentType === MembershipPaymentType.MIND && !isDisabled) ? useCultMemberShipForPayment : false
        }
        const toggleBarWidget: ToggleBarWidget = {
            widgetType: "TOGGLE_BAR_WIDGET",
            title: `Swap with ${productType.toLowerCase()} membership days: `,
            isEnabled: toggleSelected ? useCultMemberShipForPayment : false,
            currentValueText: (toggleSelected && useCultMemberShipForPayment) ? "ON" : "OFF",
            description: !toggleSelected ? "Your membership has insufficient days" : "",
            toggleAction: {
                actionType: "REFRESH_PAGE",
                meta: {
                    useCultMemberShipForPayment: !selected,
                    membershipPaymentType: productType
                }
            },
            isDisabled: !toggleSelected,
            containerStyle: { marginHorizontal: 0 },
            icon: "/image/icons/cult/swap.png",
        }
        return { toggleBarWidget: toggleBarWidget, toggleSelected: toggleSelected }
    }

    private getProductOfferWidget(appliedOffers: any) {
        if (appliedOffers.length > 0) {
            const header = {
                title: "Offers",
                style: { marginTop: 30 },
                titleProps: { style: { fontSize: 22, fontFamily: "BrandonText-Bold", lineHeight: 30 } }
            }
            const subTitleStyle = {
                color: "#33363f",
                fontSize: 12,
                fontFamily: "BrandonText-Regular",
                lineHeight: 20,
                flex: 1
            }
            const actionCards = appliedOffers.map((offer: any) => {
                return {
                    title: offer.description,
                    icon: "/image/icons/cult/offer.png",
                    iconSize: 14,
                    subTitleStyle,
                    removeMaxWidth: true,
                    rowContainer: { flex: 1 }
                }
            })
            const productInfo = new ProductListWidget("DYNAMIC_ICON", header, actionCards)
            productInfo["noTopPadding"] = true
            return productInfo
        }
        return undefined
    }

    private getLiveSgtNote() {
        return {
            tag: {
                color: "#000000",
                title: "NOTE"
            },
            data: [{
                info: "In case you are unable to attend a session, we request you to cancel 1 hr in advance. Missing a booked session is marked as a 'No-Show' and will lead to reduction in membership duration by 1 day",
                bullets: ""
            }]
        }
    }

    private getProductBenefitWidget(packBenefits: ManagedPlanPackInfo, offerWidgetExist: boolean, isLiveSGT: boolean): ProductBenefitWidget {
        const data: ProductBenefit[] = []
        packBenefits.children.forEach(item => {
            data.push({
                title: item.title,
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        return {
            widgetType: "PRODUCT_BENEFIT_WIDGET",
            header: { title: isLiveSGT ? "Why Online Group Classes" : "Why buy a pack" },
            data: data,
            layoutType: "GRID",
            containerStyle: { marginTop: offerWidgetExist ? 10 : 40 },
            note: isLiveSGT ? this.getLiveSgtNote() : {
                tag: {
                    color: "#000000",
                    title: "NOTE"
                },
                data: [{
                    info: "Trainers for the first session will be allocated based on their availability. After the first session, users will have a choice to continue with the same trainer",
                    bullets: ""
                }]
            }
        }
    }

    private getHowItWorksWidget(howItWorksItem: ManagedPlanPackInfo): ProductListWidget {
        const header: Header = {
            title: "How it works",
            color: "#000000",
            titleProps: { style: { fontSize: 22, fontFamily: "BrandonText-Bold", lineHeight: 30 } }
        }
        const infoCards: InfoCard[] = []
        howItWorksItem.children.forEach(item => {
            item.desc = item.desc.replace("${DEGREE}", "\u00b0")
            infoCards.push({
                subTitle: item.desc,
                icon: item.imageUrl
            })
        })
        const widget = new ProductListWidget("SMALL", header, infoCards)
        widget.hasDividerBelow = false
        return widget
    }

    private getPreBookingActions(userContext: UserContext, isNotLoggedIn: boolean, patientsList: Patient[], sellableProduct: BundleSessionSellableProduct, offerIds: string[], useCultMemberShipForPayment: boolean, membershipPaymentType: MembershipPaymentType, offerDetails: any, payByMembershipDetails?: PayByMembershipDetails, startDateOption?: CultPackStartDateOptions): Action[] {
        if (isNotLoggedIn === true) {
            return [
                {
                    actionType: "SHOW_ALERT_MODAL",
                    title: `${sellableProduct.infoSection.sellingTitle}`,
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                }
            ]
        } else {
            const leftInfo: any = {
                title: `${sellableProduct.infoSection.sellingTitle}`,
            }
            const rightInfo = {
                title: `PAY ${RUPEE_SYMBOL}${offerDetails.price.listingPrice}`,
            }
            const isEligibleToPayByMembership = ((payByMembershipDetails.cult && payByMembershipDetails.cult.isEligible) || (payByMembershipDetails.mind && payByMembershipDetails.mind.isEligible))
            if (useCultMemberShipForPayment && isEligibleToPayByMembership) {
                if (membershipPaymentType === MembershipPaymentType.CULT) {
                    leftInfo["subTitle"] = `Swap ${payByMembershipDetails.cult.numDaysToDeduct} cult days`
                } else {
                    leftInfo["subTitle"] = `Swap ${payByMembershipDetails.mind.numDaysToDeduct} mind days`
                }
                rightInfo.title = `PAY ${RUPEE_SYMBOL}0`
            }
            let actionString = `curefit://carecartcheckout?productId=${sellableProduct.productCode}`
            if (!_.isEmpty(offerIds)) {
                actionString += `&offerIds=${offerIds.join(",")}`
            }
            if (startDateOption?.selectedDate) {
                actionString += `&selectedDate=${startDateOption.selectedDate}`
            }
            let action: Action = {
                leftInfo: leftInfo,
                rightInfo: rightInfo,
                actionType: "NAVIGATION",
                url: actionString,
                meta: {
                    optionMeta: {
                        cultMemberShipPaymentOptions: {
                            useCultMemberShipForPayment: isEligibleToPayByMembership ? useCultMemberShipForPayment : false,
                            membershipPaymentType: membershipPaymentType,
                        }
                    }
                }
            }
            if (startDateOption) {
                action = {
                    ...action,
                    meta: {
                        ...action.meta,
                        optionMeta: {
                            ...action.meta.optionMeta,
                            startTime: TimeUtil.getEpochInMillis(userContext.userProfile.timezone, startDateOption.selectedDate)
                        }
                    }
                }
            }
            const pageAction: Action = AppUtil.isSkipCreatePatientSupported(userContext) ? action :
                CareUtil.getBundleSessionPurchaseAction(userContext, undefined, patientsList, action, "", sellableProduct.subCategoryCode)
            return [{ ...pageAction, leftInfo, rightInfo }]
        }
    }

    private async getLivePtWorkoutWidget(userContext: UserContext, subCategoryCode: SUB_CATEGORY_CODE): Promise<any> {
        const roundedImageGridWidgetView: RoundedImageGridWidgetView = new RoundedImageGridWidgetView()
        roundedImageGridWidgetView["productType"] = subCategoryCode === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        roundedImageGridWidgetView["header"] = {
            title: "Workouts"
        }
        const livePtWorkoutWidget = await roundedImageGridWidgetView.buildView(this.serviceInterfaces, userContext, { layoutType: "GRID" })
        return livePtWorkoutWidget
    }

    async getAfterBookingPage(userContext: UserContext, productId: string, bookingId: number, userId: string, deviceId: string) {
        const product: DiagnosticProduct = <DiagnosticProduct>await this.catalogueService.getProduct(productId)
        const widgets: WidgetView[] = []
        const livePtWorkoutWidget = await this.getLivePtWorkoutWidget(userContext, product.subCategoryCode)
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(product.subCategoryCode)
        const bookingInfo = await this.healthfaceService.getBookingDetailV2({
            bookingId,
            tenant: healthfaceTenant,
            options: {
                isParentBookingRequired: false,
                isPaymentStatusRequired: true,
                isForceUpdate: false
            }
        })
        const sellableProductPromise = this.healthfaceService.getProductInfoDetailsCached("BUNDLE", bookingInfo.booking.subCategoryCode, bookingInfo.booking.productCode, healthfaceTenant)
        const packStatus = bookingInfo.bundleOrderResponse.packStatus
        let aggregatedMembershipInfo
        if (packStatus !== "EXPIRED") {
            const aggregatedMembershipInfosPromise = this.healthfaceService.getAggregatedMembershipsByRootBookingId(bookingInfo.booking.id, bookingInfo.booking.patientId, "CONSULTATION", undefined, healthfaceTenant)
            aggregatedMembershipInfo = (await aggregatedMembershipInfosPromise)[0]
        }
        const isLiveSGT = product.subCategoryCode === "LIVE_SGT"

        const cultActivePackInfoWidget = await this.buildCultActivePackInfoWidget(userContext, product, bookingId, bookingInfo, aggregatedMembershipInfo)
        const upcomingSessionsWidgetPromise = this.getUpcomingSessionsWidget(userContext, product)
        const upcomingSessionsWidget = await upcomingSessionsWidgetPromise
        widgets.push(cultActivePackInfoWidget)
        // if (isLiveSGT) {
        //     const cultSGTUpgradeBannerWidget = this.buildCultSGTUpgradeBannerWidget(userContext, bookingInfo)
        //     if (!_.isEmpty(cultSGTUpgradeBannerWidget)) {
        //         widgets.push(cultSGTUpgradeBannerWidget)
        //     }
        // }
        if (!isLiveSGT) {
            widgets.push(...await this.getLivePtGoalWidgets(userContext, isLiveSGT, this.cultFitService, this.healthfaceService, this.careBusiness))
        }
        if (upcomingSessionsWidget) {
            widgets.push({ ...upcomingSessionsWidget })
        }
        const sellableProduct = <BundleSessionSellableProduct>(await sellableProductPromise)[0].baseSellableProduct
        let packPolicies
        sellableProduct.infoSection.children.map(infoSection => {
            switch (infoSection.type) {
                case "POLICIES": packPolicies = infoSection; break
            }
        })
        if (!_.isEmpty(packPolicies) && await AppUtil.isLivePTSGTNoShowEnabled(userContext)) {
            widgets.push(this.getPackPoliciesWidget(packPolicies, userContext))
        }
        widgets.push(livePtWorkoutWidget)
        const ctaTitle = isLiveSGT ? "BOOK A CLASS" : "BOOK SESSION"
        const pageActions: Action[] = []
        if (packStatus !== "EXPIRED" && packStatus !== "COMPLETED") {
            const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                productMeta.productDetail = <ConsultationProduct>(await this.catalogueService.getProduct(productMeta.productCode))
                return productMeta
            })
            const productMetas = await Promise.all(membershipProductsPromises)
            const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
            const sellableProducts = await this.cultPTService.getConsultationSellableProductsNotCached(userContext.userProfile.cityId, product.subCategoryCode)
            const productCode = sellableProducts.consultationTypes[0]?.products[0]?.code
            pageActions.push(await this.careBusiness.getLivePTSessionBookAction(userContext, { productId: productCode || consultationProducts[0].productId, actionTitle: ctaTitle, parentBookingId: bookingId, subCategoryCode: product.subCategoryCode }))
        } else if (!_.isEmpty(bookingInfo.bundleOrderResponse.bundleAction) && bookingInfo.bundleOrderResponse.bundleAction.renewPack) {
            pageActions.push({
                actionType: "NAVIGATION",
                title: "RENEW PACK",
                navigationType: "NAVIGATE_REPLACE",
                url: `curefit://bundlesession?id=${bookingInfo.booking.productCode}&subCategoryCode=${product.subCategoryCode}`
            })
        }
        return { widgets: widgets, actions: pageActions }
    }


    async getIssueManageOptions(userContext: UserContext, bookingInfo: BookingDetail): Promise<{
        newReportIssueManageOption: ManageOptionPayload
    }> {
        let newReportIssueManageOption: ManageOptionPayload
        let issues: IssueDetailView[]
        if (bookingInfo.booking.subCategoryCode === "LIVE_PERSONAL_TRAINING") {
            issues = await this.issueBusiness.getLivePersonalTrainingPacksIssue(bookingInfo, userContext)
        } else if (bookingInfo.booking.subCategoryCode === "LIVE_SGT") {
            issues = await this.issueBusiness.getLiveSGTPacksIssue(bookingInfo, userContext)
        } else {
            issues = await this.issueBusiness.getManagedPlanIssue(bookingInfo, userContext)
        }
        newReportIssueManageOption = this.issueBusiness.toManageOptionPayload(issues, true)
        return { newReportIssueManageOption }
    }

    private getManageOptions(bookingDetail: BookingDetail, orderId: string, productId: string, newReportIssueManageOption: ManageOptionPayload): { manageOptions: ManageOptions, meta: any } {
        const options: ManageOptionPayload[] = []
        options.push(newReportIssueManageOption)

        const manageOptions: ManageOptions = {
            displayText: "...",
            options: options
        }

        const meta: any = {
            orderId: orderId,
            productId: productId,
            tcBookingId: bookingDetail.booking.id
        }
        return { manageOptions: manageOptions, meta: meta }
    }

    private async getLivePtGoalWidgets(userContext: UserContext, isLiveSGT: boolean, cultFitService: ICultServiceOld, healthfaceService: IHealthfaceService, careBuisness: ICareBusiness): Promise<WidgetView[]> {
        const goalWidgets: WidgetView[] = []
        const userId = userContext.userProfile.userId
        const appName = "CUREFIT_APP"
        const goalExists = (await cultFitService.getLivePTOnboardingComplete({ userId, appName })).isOnboarded
        if (!goalExists) {
            goalWidgets.push(await new RoundedIconListWidgetView().getWidgetView(userContext, isLiveSGT ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING", cultFitService))
        } else {
            goalWidgets.push(await new PtGoalSummaryWidgetView().getWidgetView(userContext, isLiveSGT ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING", cultFitService, healthfaceService, careBuisness))
        }
        return goalWidgets
    }


    private async getUpcomingSessionsWidget(userContext: UserContext, product: DiagnosticProduct): Promise<MyLiveClassesWidget | MyLiveClassesWidgetV2> {
        if (AppUtil.isMyClassesWidgetV2Supported(userContext)) {
            const productType = product.categoryId === "LIVE_SGT" ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING"
            return new MyLiveClassesWidgetV2View().getNewPTClassesWidgetView(userContext, productType, this.healthfaceService, this.catalogueService, this.careBusiness, this.hamletBusiness)
        } else {
            if (product.categoryId === "LIVE_SGT") {
                return new MyLiveClassesWidgetView().getPTClassesWidgetView(userContext, "LIVE_SGT", this.healthfaceService, this.catalogueService, this.herculesService, this.hamletBusiness, this.careBusiness)
            }
            return new MyLiveClassesWidgetView().getPTClassesWidgetView(userContext, "LIVE_PERSONAL_TRAINING", this.healthfaceService, this.catalogueService, this.herculesService, this.hamletBusiness, this.careBusiness)
        }
    }

    private async buildCultActivePackInfoWidget(userContext: UserContext, product: DiagnosticProduct, bookingId: number, bookingInfo: BookingDetail, aggregatedMembership: AggregatedMembershipInfo): Promise<CultActivePackInfoWidget> {
        const isLiveSGT = product.subCategoryCode === "LIVE_SGT"
        const tz = userContext.userProfile.timezone
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingInfo.bundleOrderResponse.expiryTimeEpoch)), tz)
        const endDateFormatted = endDate.format("D MMM YYYY")
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(bookingInfo.bundleOrderResponse.startTimeEpoch)), tz)
        const startDateFormatted = startDate.format("D MMM YYYY")
        const numDaysToEndFromToday = endDate.diff(today, "days")
        const membershipState = bookingInfo.bundleOrderResponse.packStatus
        const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
        const totalSessions = aggregatedMembership?.totalTickets
        const completedSessions = aggregatedMembership?.totalTicketsConsumed
        let leftText, rightText
        let total = endDate.diff(startDate, "days")
        let completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
        if (membershipState === "EXPIRED") {
            leftText = `Expired on ${endDateFormatted}`
            rightText = ""
        } else if (membershipState === "COMPLETED") {
            leftText = "All sessions completed"
            rightText = ""
            total = totalSessions
            completed = completedSessions
        } else {
            const isLivePT = product.subCategoryCode === "LIVE_PERSONAL_TRAINING"
            const inactiveLivePTPack = isLivePT && (startDate.diff(today, "days") > 0)
            leftText = (isLiveSGT || inactiveLivePTPack) ? `Start: ${startDateFormatted}` : `${completedSessions} / ${totalSessions} Completed`
            rightText = `Ends : ${endDateFormatted}`
            total = isLiveSGT ? total : totalSessions
            completed = isLiveSGT ? completed : completedSessions
        }
        let manageOptionsView: { manageOptions: ManageOptions; meta: any } = null
        const { newReportIssueManageOption } = await this.getIssueManageOptions(userContext, bookingInfo)
        manageOptionsView = this.getManageOptions(bookingInfo, bookingInfo.booking.cfOrderId,
            bookingInfo.booking.productCode, newReportIssueManageOption)
        return {
            widgetType: "CULT_ACTIVE_PACK_INFO_WIDGET",
            progressBar: {
                leftText,
                rightText,
                total,
                completed,
                type: product.subCategoryCode,
                noPadding: true,
                progressBarColor: packTagAndColor.color
            },
            tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
            title: product.title,
            actions: [],
            image: product.heroImageUrl,
            moreAction: {
                actions: [
                    {
                        title: "Need Help",
                        actionType: "REPORT_ISSUE",
                        meta: {
                            title: "Help",
                            issues: newReportIssueManageOption.meta.issues
                        },
                    },
                ], actionType: "ACTION_LIST"
            },
        }
    }

    private getPackSelectionWidget(userContext: UserContext, bundleProducts: BundleSessionSellableProduct[], selectedProduct: BundleSessionSellableProduct, bundleoffers: PackOffersResponse, appliedOffers: any[], productPayByMembershipMap: ProductPayByMembershipMap, useCultMemberShipForPayment: boolean, membershipPaymentType: MembershipPaymentType, toggleSelected: boolean) {
        const headerWidget = new HeaderWidget({ title: "SELECT A PACK" })
        headerWidget.titleStyle = {
            fontSize: 12,
            color: "#000000",
            fontFamily: "BrandonText-Black",
            marginLeft: 15,
        }
        const rowContainerStyle = {
            borderBottomWidth: 1,
            borderBottomColor: "#e2e4e8",
            padding: 19,
            paddingHorizontal: 15,
        }
        headerWidget.hasDividerBelow = false
        let payByMembershipDetails, isMindMember, isCultMember
        if (!_.isEmpty(productPayByMembershipMap)) {
            payByMembershipDetails = productPayByMembershipMap[selectedProduct.productCode]
            isCultMember = (payByMembershipDetails.cult && payByMembershipDetails.cult.isMember)
            isMindMember = (payByMembershipDetails.mind && payByMembershipDetails.mind.isMember)
        }
        const packSelectionWidget = CareUtil.getProductRadioSelectionWidget(bundleProducts, selectedProduct, bundleoffers, appliedOffers, productPayByMembershipMap, useCultMemberShipForPayment, false, true, {}, rowContainerStyle, membershipPaymentType, true, toggleSelected)
        return {
            widgets: [packSelectionWidget],
            widgetType: "CARD_CONTAINER_WIDGET",
            containerStyle: {
                marginTop: 0,
            }
        }
    }

    private buildStartDateWidget(startDateOptions: any, existingCultMemberToggleWidget?: ToggleBarWidget): DatePickerWidget {
        const datePickerWidget: DatePickerWidget = {
            title: "Pick a Start Date",
            startDate: startDateOptions.minEligibleDate,
            endDate: startDateOptions.maxEligibleDate,
            selectedDate: startDateOptions.selectedDate || startDateOptions.minEligibleDate,
            canChangeStartDate: startDateOptions.canChangeStartDate,
            iconStyle: { tintColor: "#000000" },
            widgetType: "DATE_PICKER_WIDGET",
            contentContainerStyle: {
                paddingLeft: 0,
                paddingRight: 0,
                paddingVertical: 20,
            },
            hasDividerBelow: existingCultMemberToggleWidget ? true : false,
        }
        return datePickerWidget
    }

    private getIOSSGTBannerWidget(userContext: UserContext) {
        return new BannerCarouselWidget("335:85", [{
            id: "/image/banners/cult/ios_sgt_banner_3.png",
            image: "/image/banners/cult/ios_sgt_banner_3.png",
            action: undefined,
            contentMetric: {
                contentId: "IOS_SGT_BANNER"
            }
        }], {
            bannerWidth: 335,
            bannerHeight: 85,
            verticalSpacing: 20,
            backgroundColor: undefined
        }, 1, false, false)
    }

    private getPackPoliciesWidget(packPolicies: ManagedPlanPackInfo, userContext: UserContext) {
        const infoCards: IInfoCard[] = []
        const modalHeader: Header = {
            title: `${packPolicies.title}`,
            color: "#000000",
            titleProps: { style: { fontSize: 22, fontFamily: "BrandonText-Bold" } },
        }
        const modalWidgets: WidgetView[] = []
        const modalTitleWidget = new ProductListWidget("SMALL", modalHeader, [])
        modalTitleWidget.hasDividerBelow = false
        modalWidgets.push(modalTitleWidget)
        packPolicies.children.forEach(item => {
            infoCards.push({
                title: item.title,
                subTitle: item.shortDescription,
                icon: item.imageUrl,
                subtitleStyle: { color: "#888e9e", fontSize: 12 }
            })
            const descriptions = [{
                title: item.title,
                subTitle: item.desc,
                titleStyle: { fontSize: 16, color: "#33363f" },
                readMoreProps: {
                    textStyle: { color: "#888e9e" }
                }
            }]
            const descriptionWidget = new DescriptionWidget(descriptions)
            descriptionWidget.hasDividerBelow = false
            descriptionWidget.containerStyle = { paddingTop: 15, paddingBottom: 15 }
            descriptionWidget.rowStyle = { flex: 0 }
            modalWidgets.push(descriptionWidget)
        })
        const header: Header = {
            title: "Policies",
            color: "#000000",
            titleProps: { style: { fontSize: 22, fontFamily: "BrandonText-Bold", lineHeight: 30 } },
            topAction: {
                action: {
                    actionType: "SHOW_WIDGETIZED_MODAL",
                    meta: {
                        position: "bottom",
                        data: {
                            widgets: modalWidgets,
                            actions: [
                                {
                                    actionType: "HIDE_WIDGETIZED_MODAL",
                                    title: "GOT IT"
                                }
                            ]
                        }
                    }
                },
                actionString: "VIEW"
            }
        }
        const widget = new ProductListWidget(
            "SMALL",
            header,
            infoCards
        )
        widget.hideSepratorLines = true
        return widget
    }

    private buildCultSGTUpgradeBannerWidget(userContext: UserContext, bookingDetail: BookingDetail): BannerCarouselWidget {
        const banners: Banner[] = [{
            id: "/image/icons/cult/gx_pack_page_banner_2.png",
            image: "/image/icons/cult/gx_pack_page_banner_2.png",
            action: {
                actionType: "NAVIGATION",
                url: CultUtil.getSGTUpgradeCommUrl()
            }
        }]
        return new BannerCarouselWidget("335:110", banners, {
            bannerWidth: 335,
            bannerHeight: 110,
            v2: true,
            verticalPadding: 20,
            backgroundColor: "#ffffff",
            noAutoPlay: true,
        }, 4, false, false)
    }
}

export default LivePtPackPageViewBuilder