import { injectable } from "inversify"
import * as _ from "lodash"
import * as moment from "moment"
import * as momentTz from "moment-timezone"
import {
  MembershipAudit,
  MembershipAuditEventType,
  MembershipAuditEventObject,
} from "@curefit/cult-common"
import { UserContext } from "@curefit/vm-models"
import CultUtil from "../util/CultUtil"
import {
  TimeUtil,
  Timezone,
  pluralizeStringIfRequired,
} from "@curefit/util-common"

function auditEventName(code: MembershipAuditEventType): string {
  switch (code) {
    case MembershipAuditEventType.CENTER_SHUTDOWN:
      return "Center Maintenance"
    case MembershipAuditEventType.FREE_AWAY_CLASSES:
      return "Free Away Classes"
    case MembershipAuditEventType.MEMBERSHIP_PAUSED:
      return "Paused Membership"
    case MembershipAuditEventType.MEMBERSHIP_PURCHASED:
      return "Membership Purchase"
    case MembershipAuditEventType.MEMBERSHIP_RESUMED:
      return "Resumed Membership"
    case MembershipAuditEventType.MEMBERSHIP_START_DATE_CHANGE:
      return "Start Date Changed"
    case MembershipAuditEventType.NO_SHOW:
      return "No Show Penalty"
    case MembershipAuditEventType.NO_SHOW_REVERTED:
      return "No Show Reversed"
    case MembershipAuditEventType.OFFER:
      return "Offer"
    case MembershipAuditEventType.OTHER_REASON:
      return "Other"
    case MembershipAuditEventType.PARQ_SUBMISSION:
      return "Doctor Approval Submission"
    case MembershipAuditEventType.TOA:
      return "Customer Happiness"
    default:
      return code
        .split("_")
        .map((x) => x.charAt(0).toUpperCase() + x.substring(1).toLowerCase())
        .join(" ")
  }
}

function auditEventSubtitle(
  event: MembershipAuditEventObject,
  tz: Timezone,
  packName: string
): string {
  const { entity, value } = event.delta
  const eventDate = TimeUtil.getMomentForDateString(event.eventDate, tz).format(
    "ddd Do MMM, YYYY"
  )
  switch (event.type) {
    case MembershipAuditEventType.CENTER_SHUTDOWN:
      if (entity === "endDate") {
        return `${
          event.centerDetails ? event.centerDetails.name : ""
        }\nMaintenance Started on ${eventDate}`
      }
      return "Center closed for maintenance"
    case MembershipAuditEventType.FREE_AWAY_CLASSES:
      if (entity === "awayClassesPerMonth") {
        return `Added ${value} extra away ${pluralizeStringIfRequired(
          "class",
          value,
          "es"
        )}`
      }
      return "Classes in another city"
    case MembershipAuditEventType.MEMBERSHIP_PAUSED:
      return "Paused Membership"
    case MembershipAuditEventType.MEMBERSHIP_PURCHASED:
      return packName
    case MembershipAuditEventType.MEMBERSHIP_RESUMED:
      const pausedOn = TimeUtil.getMomentForDateString(
        event.typeMeta!.pauseStartDate,
        tz
      ).format("ddd, Do MMM")
      return `Paused on ${pausedOn}`
    case MembershipAuditEventType.MEMBERSHIP_START_DATE_CHANGE:
      if (entity === "startDate") {
        const previousStartDate = TimeUtil.getMomentForDateString(
          event.typeMeta.oldMembershipStartDate,
          tz
        ).format("Do MMM")
        const newStartDate = TimeUtil.getMomentForDateString(
          event.typeMeta.newMembershipStartDate,
          tz
        ).format("Do MMM, YYYY")
        return `From ${previousStartDate} to ${newStartDate}`
      }
    case MembershipAuditEventType.NO_SHOW:
      if (entity === "endDate" && event.classDetails) {
        const { workoutName, startTime, date } = event.classDetails
        const startTimeModified = TimeUtil.formatDateStringInTimeZone(
          `${date} ${startTime}`,
          tz,
          "h:mm A"
        )
        const centerName = event.centerDetails?.name
        return `${workoutName}, ${startTimeModified}\n${centerName}`
      }
    case MembershipAuditEventType.NO_SHOW_REVERTED:
      if (entity === "endDate" && event.classDetails) {
        const { workoutName, startTime, date } = event.classDetails
        const startTimeModified = TimeUtil.formatDateStringInTimeZone(
          `${date} ${startTime}`,
          tz,
          "h:mm A"
        )
        const centerName = event.centerDetails?.name
        return `${workoutName}, ${startTimeModified}\n${centerName}`
      }
    case MembershipAuditEventType.OFFER:
      if (entity === "endDate" && value > 0) {
        return `Membership extended by ${value} ${pluralizeStringIfRequired(
          "day",
          value
        )}\n`
      }
      return `Offer rewards\n`
    case MembershipAuditEventType.OTHER_REASON:
      return "Other Reason"
    case MembershipAuditEventType.PARQ_SUBMISSION:
      if (entity === "endDate") {
        return `Memberships extended by ${value} ${pluralizeStringIfRequired(
          "day",
          value
        )}`
      }
      return `PARQ form submitted\n`
    case MembershipAuditEventType.TOA:
      if (entity === "endDate" && event.classDetails) {
        const { workoutName, startTime, date } = event.classDetails
        const startTimeModified = TimeUtil.formatDateStringInTimeZone(
          `${date} ${startTime}`,
          tz,
          "h:mm A"
        )
        const centerName = event.centerDetails?.name
        return `${workoutName}, ${startTimeModified}\n${centerName}\nClass Cancelled`
      }
      return "Customer Happiness"
    case MembershipAuditEventType.PAUSE_DAYS:
      return `${value} ${pluralizeStringIfRequired(
        "day",
        value
      )} of extra pause added\n`
    default:
      return ""
  }
}

function auditEventDelta(delta: MembershipAuditEventObject["delta"]): string {
  if (delta && delta.value) {
    const { value, entityType } = delta
    if (value >= 0) {
      return `+${value} ${entityType}`
    }
    return `${value} ${entityType}`
  }
  return "+0 Days"
}

@injectable()
class MembershipAuditTrailViewBuilder {
  constructor() {}

  async buildView(
    auditTrailResponse: MembershipAudit,
    userContext: UserContext
  ): Promise<any> {
    const {
      membershipDetails,
      originalMembershipEndDate,
      originalMembershipStartDate,
      currentMembershipStartDate,
      currentMembershipEndDate,
      netMovement,
      originalPackDuration,
      totalExtension,
      totalDeduction,
      netChange,
      events,
    } = auditTrailResponse

    const { packName, purchaseDate, state } = membershipDetails
    const tz = userContext.userProfile.timezone
    const endDate = TimeUtil.getMomentForDateString(
      currentMembershipEndDate,
      tz
    )
    const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
    const numDaysToEndFromToday = endDate.diff(today, "days")
    const packTagAndColor = CultUtil.getCultPackTagAndColor(
      state,
      numDaysToEndFromToday
    )

    const packNameWidget = {
      widgetType: "PACK_NAME_WIDGET",
      packName,
      purchaseDate: `Purchased on ${momentTz
        .tz(purchaseDate, userContext.userProfile.timezone)
        .format("Do MMM YYYY, hh:mm a")}`,
      tag: {
        state,
        color: packTagAndColor.color,
      },
      hasDividerBelow: true,
    }

    let endDateDelta: number | undefined

    if (Number.isInteger(netChange) && Number.isInteger(netMovement)) {
      endDateDelta = netMovement + netChange
    }

    const endDateChanges = {
      widgetType: "AUDIT_CARD_WIDGET",
      title: "End Date Changes",
      cardBody: [
        {
          label: "Original Start Date :",
          value: moment(originalMembershipStartDate, "YYYY-MM-DD").format(
            "Do MMM YYYY"
          ),
        },
        {
          label: "Original End Date :",
          value: moment(originalMembershipEndDate, "YYYY-MM-DD").format(
            "Do MMM YYYY"
          ),
        },
        originalMembershipStartDate !== currentMembershipEndDate
          ? {
              label: "Current Start Date :",
              value: moment(currentMembershipStartDate, "YYYY-MM-DD").format(
                "Do MMM YYYY"
              ),
            }
          : undefined,
        {
          label: "Current End Date :",
          value: moment(currentMembershipEndDate, "YYYY-MM-DD").format(
            "Do MMM YYYY"
          ),
          highlight: currentMembershipEndDate !== originalMembershipEndDate,
        },
      ].filter((x) => Boolean(x)),
      footer: {
        label: "Net Movement :",
        value: endDateDelta
          ? `Ending ${Math.abs(endDateDelta)} day${
              Math.abs(endDateDelta) === 1 ? "" : "s"
            } ${endDateDelta > 0 ? "late" : "early"}`
          : "No changes",
        highlight: Boolean(endDateDelta), // netChange cannot be 0 or null
      },
    }

    const durationChanges = {
      widgetType: "AUDIT_CARD_WIDGET",
      title: "Duration Changes",
      cardBody: [
        {
          label: "Original Pack Duration :",
          value: `${originalPackDuration} ${pluralizeStringIfRequired(
            "Day",
            originalPackDuration
          )}`,
        },
        {
          label: "Total Extensions :",
          value: Number.isInteger(totalExtension)
            ? `${totalExtension} ${pluralizeStringIfRequired(
                "Day",
                totalExtension
              )}`
            : "0 Days",
        },
        {
          label: "Total Deductions :",
          value: Number.isInteger(totalDeduction)
            ? `${
                totalDeduction > 0 ? "-" : ""
              }${totalDeduction} ${pluralizeStringIfRequired(
                "Day",
                totalDeduction
              )}`
            : "0 Days",
          highlight: Number.isInteger(totalDeduction) && totalDeduction > 0,
        },
        {
          label: "Net Change In Pack Duration :",
          value: Number.isInteger(netChange)
            ? `${netChange} ${pluralizeStringIfRequired("Day", netChange)}`
            : "0 Days",
          highlight: Number.isInteger(netChange),
        },
      ],
      footer: {
        label: "Current Pack Duration:",
        value: Number.isInteger(netChange)
          ? `${originalPackDuration + netChange} ${pluralizeStringIfRequired(
              "Day",
              originalPackDuration + netChange
            )}`
          : `${originalPackDuration} ${pluralizeStringIfRequired(
              "Day",
              originalPackDuration
            )}`,
        highlight: Number.isInteger(netChange),
      },
      hasDividerBelow: true,
    }

    const auditTitle = {
      widgetType: "TEXT_WIDGET",
      content: "Membership History",
      fontFace: "bold",
      containerStyle: {
        backgroundColor: "rgb(255, 255, 255)",
        marginBottom: 28,
      },
      contentStyle: {
        fontSize: 19,
      },
    }

    const auditEvents = events.map((event) => ({
      title: auditEventName(event.type),
      subtitle: auditEventSubtitle(event, tz, packName),
      increment: event.delta && event.delta.value >= 0,
      delta: auditEventDelta(event.delta),
      date: moment(event.eventDate).format("Do MMM"),
      hasDividerBelow: true,
      widgetType: "AUDIT_ENTRY_WIDGET",
    }))

    return {
      widgets: [
        packNameWidget,
        endDateChanges,
        durationChanges,
        auditTitle,
        ...auditEvents,
      ],
      hasMore: auditEvents.length === CultUtil.getMembershipAudientEventLimit(), // has more events to query
    }
  }
  async buildMoreEventsView(
    response: MembershipAuditEventObject[],
    userContext: UserContext
  ): Promise<any> {
    const auditEvents = response.map((event) => ({
      title: auditEventName(event.type),
      subtitle: auditEventSubtitle(
        event,
        userContext.userProfile.timezone,
        event.typeMeta.packName
      ),
      increment: event.delta && event.delta.value >= 0,
      delta: auditEventDelta(event.delta),
      date: moment(event.eventDate).format("Do MMM"),
      hasDividerBelow: true,
      widgetType: "AUDIT_ENTRY_WIDGET",
    }))

    return {
      widgets: auditEvents,
      hasMore: auditEvents.length === CultUtil.getMembershipAudientEventLimit(), // more events are present
    }
  }
}

export default MembershipAuditTrailViewBuilder
