import { Address } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { CenterResponse } from "@curefit/center-service-common"
import LocationUtil from "../util/LocationUtil"
import { Action } from "@curefit/apps-common"
import * as _ from "lodash"
import AppUtil from "../util/AppUtil"
import { TransferPackFlowTypes } from "../util/CultUtil"

class CenterView {
    constructor(userContext: UserContext, center: CenterResponse, productType: ProductType, cityName?: string, flow ?: string) {
        let centerId = center?.meta?.cultCenterId ?? center.id
        if (productType === "FITNESS")
            centerId = center?.meta?.cultCenterId ?? center.id
        else if (productType === "GYMFIT_FITNESS_PRODUCT")
            centerId = center?.meta?.gymfitCenterId ?? center.id

        this.id = centerId
        this.name = center.name
        this.centerServiceId = center.id

        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent

        if (center.launchDate != null && center.launchDate) {
            const isUpcomingCenter = center.launchDate.toString() > new Date().getTime().toString()
            if (isUpcomingCenter) {
                this.centerTag = "COMING SOON"
            }
        }

        const seoParams: SeoUrlParams = {
            city: cityName ? cityName : undefined,
            locality: center.locality,
            productName: center.name
        }


        if (productType === "FITNESS" || productType === "GYMFIT_FITNESS_PRODUCT") {
            if (userContext.sessionInfo.appSource == "flutter") {
                if (flow === TransferPackFlowTypes.select_transfer || flow == TransferPackFlowTypes.elite_transfer) {
                    this.newAction = {
                        actionType: "UPDATE_TRANSFER_MEM_DATA",
                        payload: {
                            "key": "CENTER_SELECTION",
                            "value": {
                                "name": center?.name,
                                "value": center?.id
                            }
                        }
                    }
                } else {
                    this.newAction = {
                        actionType: "UPDATE_CHECKOUT_V2_BLOC",
                        meta: {
                            centerId: this.id,
                            centerServiceCenterId: this.centerServiceId,
                            centerName: center?.name,
                            refreshPage: true,
                            resetDate: true
                        }
                    }
                }
            } else {
                if (AppUtil.isWeb(userContext)) {
                    this.action = ActionUtil.cultCenter(centerId.toString(), undefined, userAgent, seoParams)
                } else {
                    this.action = ActionUtil.cultCenter(center.id.toString(), undefined, userAgent, seoParams)
                }
            }
        } else {
            this.action = ActionUtil.mindCenter(center.id.toString(), undefined, userAgent, seoParams)
        }
        if (!_.isNil(userContext.sessionInfo.lat) && !_.isNil(userContext.sessionInfo.lon) && !isNaN(userContext.sessionInfo.lat) && !isNaN(userContext.sessionInfo.lon)) {
            this.distance = LocationUtil.getDistanceFromLatLonInKm(
                center.latitude,
                center.longitude,
                userContext.sessionInfo.lat,
                userContext.sessionInfo.lon
            ).toFixed(2) + " Km"
        }
        this.address = {
            addressString: center.fullAddress1,
            locality: center.locality,
            latLong: {
                lat: center.latitude,
                long: center.longitude
            },
            mapUrl: center.mapUrl,
            pincode: "",
        }

        this.cultCenterServiceId = center.id

        // To do add alertMeta be
        // this.alertMeta = CultUtil.getEmployeeOnlyModalMetaForCenter(center.id.toString())


        this.mapUrl = center.mapUrl
        // this.images.push(`/image/centers/CULTCENTER${center.id}/7.jpg`)
        // if (showWorkouts && !_.isEmpty(center.workouts)) {
        //     this.tagView = {
        //         tagTitle: `Workouts available at this centre`,
        //         tags: _.map(center.workouts, workout => workout.name)
        //     }
        // }
    }
    public id: number
    public name: string
    public action: string
    public newAction: Action | any
    public address: Address
    public distance: string
    public mapUrl: string
    public images?: string[] = []
    public alertMeta?: {
        title: string
        message: string
    }
    public showIcon: boolean = false
    public tagView?: {
        tagTitle: string
        tags: string[]
    }
    public cultCenterServiceId: number
    public centerServiceId?: number
    public centerTag?: string
}

export default CenterView
