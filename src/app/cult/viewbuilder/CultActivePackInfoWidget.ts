import { injectable } from "inversify"
import * as _ from "lodash"
import { Action } from "../../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { UrlPathBuilder, ImageCategory, ProductTypes } from "@curefit/product-common"
import { CultActivePackInfoWidget, NoteInfo, PreferredCenterInfo } from "@curefit/apps-common"
import { UserContext } from "@curefit/userinfo-common"
import CultUtil from "../../util/CultUtil"
import { Membership, MembershipType } from "@curefit/membership-commons"
import { ICenterService,  } from "@curefit/center-service-client"
import { MONTHS_LIST } from "../../gymfit/GymfitClassScheduleViewBuilder"
import { MembershipItemUtil } from "../../util/MembershipItemUtil"
import PlayUtil from "../../util/PlayUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"


export interface ICultActivePackInfoParams {
    userContext: UserContext,
    membership: Membership,
    actions: Action[],
    tz: Timezone,
    packInfo: OfflineFitnessPack,
    centerInfo?: PreferredCenterInfo,
    rightInfoText?: string
    noteInfo?: NoteInfo
}

@injectable()
class CultActivePackInfoWidgetViewBuilder {
    constructor() {
    }

    async buildView(
        activePackInfoParams: ICultActivePackInfoParams,
        centerService?: ICenterService
    ): Promise<CultActivePackInfoWidget> {
        const { userContext, membership, actions, tz, packInfo, rightInfoText } = activePackInfoParams
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), tz)
        const endDateFormatted = endDate.format("D MMM YYYY")
        const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd"), tz)
        const startDateFormatted = startDate.format("D MMM YYYY")
        const numDaysToEndFromToday = endDate.diff(today, "days")
        const membershipState = CultUtil.getMembershipStateV2(membership, tz)
        const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
        const category: ImageCategory = ImageCategory.HERO
        const userAgent = userContext.sessionInfo.userAgent
        const nextMonth = (new Date().getMonth() + 1) % 12

        let rightInfo = undefined
        if (rightInfoText) {
            rightInfo = {
                title: rightInfoText
            }
        }
        let image = CatalogueServiceUtilities.getFitnessProductImage(packInfo, category, userAgent)
        if (membership.type == MembershipType.COMPLIMENTARY) {
            image = "/image/packs/cult/CULTPACK37/80.jpg"
        }
        if (packInfo.productType === "PLAY") {
            image = PlayUtil.PLAY_PACK_HERO_IMAGE
        }
        let isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
        const slpMembership = MembershipItemUtil.isPlaySportLevelMembership(membership)
        const playLimitedMembership = MembershipItemUtil.isPlayLimitedSLPMembership(membership)
        if (membership?.metadata?.jpmcMembership) {
            isSelectMembership = false
        }
        let selectCenterName: string
        if (isSelectMembership || slpMembership?.isSLPPack || playLimitedMembership?.isPlayLimitedSLPMembership) {
            selectCenterName = await CultUtil.getSelectCenterNameForMembership(userContext, membership, centerService)
        }

        if (slpMembership?.isSLPPack) {
            image = PlayUtil.getPackImageByWorkoutId(slpMembership.accessWorkout)
        }
        if (playLimitedMembership?.isPlayLimitedSLPMembership) {
            image = PlayUtil.getPackImageByWorkoutId(playLimitedMembership.accessWorkout)
        }

        const islimitedMembership = membership != null && membership.metadata?.limitedSessions === true
        const isCultpassXMembership = membership != null && membership.metadata?.isCultpassX === true
        let maxTickets, usedTickets, progressValue, secondProgressBarRightText
        if (islimitedMembership || isCultpassXMembership) {
            const benefitName = packInfo.productType === "PLAY" ? "PLAY" : "CULT"
            const filteredBenefit = membership.benefits.find(a => a.name === benefitName)
            maxTickets = filteredBenefit.maxTickets
            usedTickets = filteredBenefit.ticketsUsed
            progressValue = (usedTickets ?? 0) / (maxTickets ?? 1)

            if ((benefitName !== "PLAY") || (benefitName === "PLAY" && filteredBenefit.type === "MONTHLY")) {
                secondProgressBarRightText = `Sessions renew on 1st ${MONTHS_LIST[nextMonth]}`
            }
        }

        const packWorkoutId: string = slpMembership?.accessWorkout ?? playLimitedMembership?.accessWorkout
        const widgetTitle = this.getWidgetTitle(
            isSelectMembership,
            slpMembership.isSLPPack,
            selectCenterName,
            membership,
            playLimitedMembership.isPlayLimitedSLPMembership,
            PlayUtil.getSportNameById(packWorkoutId)
        )
        return {
            widgetType: "CULT_ACTIVE_PACK_INFO_WIDGET",
            progressBar: {
                leftText: membershipState === "UPCOMING" ? `Starts: ${startDateFormatted}` : `Started:  ${startDateFormatted}`,
                rightText: `Ends: ${endDateFormatted} ${(membership.remainingPauseCount === membership.maxPauseCount && _.isEmpty(membership.activePause)) ? "" : "(updated)" }`,
                total: endDate.diff(startDate, "days"),
                completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
                type: "FITNESS",
                noPadding: true,
                progressBarColor: packTagAndColor.color
            },
            tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
            title: widgetTitle,
            actions: actions,
            image: image,
            centerInfo: activePackInfoParams.centerInfo,
            rightInfo: rightInfo,
            noteInfo: null,
            secondProgressBar: (islimitedMembership || isCultpassXMembership) ? {
                leftText: `${maxTickets - usedTickets}/${maxTickets} session${maxTickets > 1 ? "s" : ""} left`,
                rightText: secondProgressBarRightText,
                total: maxTickets,
                completed: usedTickets,
                noPadding: true,
                type: maxTickets <= 6 ? "split_view" : null,
                progressBarColor: CultUtil.getProgressColor(progressValue, true),
            } : null
         }
    }

    private getWidgetTitle(
        isSelectMembership: boolean,
        isSlpMembership: boolean,
        selectCenterName: string,
        membership: Membership,
        isPlayLimitedSLPMembership: boolean,
        workoutName: string
    ): string {
        if (isSlpMembership || isPlayLimitedSLPMembership) {
            return  `cultpass ${workoutName} \n${selectCenterName}`
        }
        if (isSelectMembership) {
            return `cultpass SELECT ${selectCenterName}`
        }
        if (MembershipItemUtil.isPlusMembership(membership) && !membership.name.toUpperCase().includes("PLUS")) {
            return membership.name + " PLUS"
        } else if (MembershipItemUtil.isBoosterMembership(membership) && !membership.name.toUpperCase().includes("BOOSTER")) {
            return membership.name + " + Booster"
        }

        return membership.name
    }
}

export default CultActivePackInfoWidgetViewBuilder
