import { injectable, inject } from "inversify"
import * as _ from "lodash"
import { UserContext } from "@curefit/userinfo-common"
import { CultMoment, CultMomentFileType, CultMomentPaginationResponse, CultMomentsResponse } from "@curefit/cult-common"
import { CdnUtil, TimeUtil } from "@curefit/util-common"
import CultUtil, { CULT_MEMORY_IMAGE_ASPECT_RATIO } from "../../util/CultUtil"
import { UrlPathBuilder } from "@curefit/product-common"
import { CultMemoriesDataItem } from "../../page/vm/widgets/CultMemoriesCarouselWidgetView"
import { Action } from "@curefit/apps-common"

interface CultMemorySection {
    key: string
    title: string
    aspectRatio: number
    items: CultMemoriesDataItem[]
}

export interface CultMemoriesResponse {
    sections: CultMemorySection[]
    nextQuery?: {
        pageNumber: number
        pageSize: number
    }
    action?: Action
}

@injectable()
class CultMemoriesViewBuilder {

    getMemories(userContext: UserContext, response: { cultMoments: CultMomentsResponse[], pagination: CultMomentPaginationResponse}, pageNumber: number, pageSize: number): CultMemoriesResponse {
        const tz = userContext.userProfile.timezone
        const data: {[month: string]: CultMemorySection} = {}
        _.map(response.cultMoments, moment => {
            const classDate = moment.CultMoment.CultClass.date
            const key = TimeUtil.formatDateStringInTimeZone(classDate, tz, "MMM YYYY")
            let sectionData: CultMemorySection = data[key]
            if (!sectionData) {
                sectionData = {
                    key,
                    title: key,
                    aspectRatio: CULT_MEMORY_IMAGE_ASPECT_RATIO,
                    items: []
                }
                data[key] = sectionData
            }
            sectionData.items.push(CultUtil.getCultMemoryItem(moment, userContext))
        })
        const sections: CultMemorySection[] = _.values(data)
        let nextQuery
        if (pageNumber < response.pagination.totalPages) {
            nextQuery = {
                pageNumber: pageNumber + 1,
                pageSize: pageSize
            }
        }
        return {
            sections,
            nextQuery
        }
    }
}



export default CultMemoriesViewBuilder
