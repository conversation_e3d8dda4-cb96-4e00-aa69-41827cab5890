import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import {
    CenterAddressWidget,
    CenterInfoWidget,
    CenterSummaryWidget,
    DescriptionWidget,
    GymMedia,
    Header,
    InfoCard,
    ManageOptionsWidgetV2,
    MediaData,
    ProductDetailPage,
    ProductGridWidget,
    RoundedImageGridWidget
} from "../../common/views/WidgetView"
import { CultCenter, CultDocument, CultPackType, CultWorkoutV2 } from "@curefit/cult-common"
import { ProductType } from "@curefit/product-common"
import { Action, ImageOverlayCardContainerWidget, PageTypes, WidgetHeader, WidgetView } from "@curefit/apps-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import * as _ from "lodash"
import { HeaderWidget } from "../../page/PageWidgets"
import CenterView from "../CenterView"
import { TimeUtil } from "@curefit/util-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { CultPackBrowseWidgetView } from "../../page/vm/widgets/CultPackBrowseWidgetView"
import { IBaseWidget, RoundedImageGridItem } from "@curefit/vm-models"
import { BaseOfferRequestParams } from "@curefit/offer-common"
import AppUtil from "../../util/AppUtil"
import { CacheHelper } from "../../util/CacheHelper"
import CultUtil, { CULT_UPCOMING_TAG_VERSION, isUserActiveMember } from "../../util/CultUtil"
import { MediaType } from "@curefit/gymfit-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"

export class CultCenterPage extends ProductDetailPage {
    meta?: Record<string, any>
}

export interface CultCenterPageBuilderParams {
    productType: ProductType
    center: CultCenter
    workoutsPromise?: Promise<CultWorkoutV2[]>
}

@injectable()
class CultCenterPageViewBuilder {

    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) { }

    async buildView(userContext: UserContext, center: CultCenter, productType: ProductType): Promise<CultCenterPage> {
        const pageBuilderParams: CultCenterPageBuilderParams = {
            center,
            productType
        }
        const widgetPromises: Promise<WidgetView>[] = []
        this.addPagePromises(userContext, pageBuilderParams)

        if (CultUtil.isNewCenterDetailPageSupported(userContext)) {
            widgetPromises.push(this.getCenterSummaryWidgetV2(userContext, pageBuilderParams))
            widgetPromises.push(this.getCenterAddressWidgetV2(userContext, pageBuilderParams))
            const facilitiesWidget = this.getFacilitiesWidgetV2(pageBuilderParams.center)
            if (facilitiesWidget) {
                widgetPromises.push(facilitiesWidget)
            }
        } else {
            widgetPromises.push(this.getCenterSummaryWidget(userContext, pageBuilderParams))
        }

        const workoutsWidget = this.getWorkoutsWidget(userContext, pageBuilderParams)
        if (workoutsWidget) {
            widgetPromises.push(workoutsWidget)
        }
        if (productType === "FITNESS") {
            widgetPromises.push(this.getCultPackBrowseWidget(userContext, pageBuilderParams))
        } else {
            widgetPromises.push(this.getMindPackBrowseWidget(userContext, pageBuilderParams))
        }
        const cultCenterPage = new CultCenterPage()
        cultCenterPage.widgets = await Promise.all(widgetPromises)
        cultCenterPage.widgets = _.filter(cultCenterPage.widgets, widget => !_.isEmpty(widget))
        cultCenterPage.actions = await this.getPageActions(userContext, center, productType)
        cultCenterPage.meta =  {
            status: center?.state
        }
        return cultCenterPage
    }

    private addPagePromises(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams) {
        const baseService = pageBuilderParams.productType === "FITNESS" ? this.cultFitService : this.mindFitService
        pageBuilderParams.workoutsPromise = baseService.browseWorkoutsV2(undefined, false, true, true,  "CUREFIT_API", userContext.userProfile.userId, userContext.userProfile.city.cultCityId, [pageBuilderParams.center.id.toString()])
    }

    private async getCenterSummaryWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams): Promise<ImageOverlayCardContainerWidget> {
        const centerSummaryWidget = new ImageOverlayCardContainerWidget()
        centerSummaryWidget.widgets.push(this.getManageOptionsV2Widget(pageBuilderParams.center))
        centerSummaryWidget.widgets.push(this.getHeaderWidget(pageBuilderParams.center))
        centerSummaryWidget.widgets.push(this.getCenterAddressWidget(userContext, pageBuilderParams))
        centerSummaryWidget.widgets.push(this.getCenterTimingWidget(userContext, pageBuilderParams))
        const facilitiesWidget = this.getFacilitiesWidget(pageBuilderParams.center)
        if (facilitiesWidget) {
            centerSummaryWidget.widgets.push(this.getFacilitiesWidget(pageBuilderParams.center))
        }
        const document = _.find(pageBuilderParams.center.documents, { tagName: "PRODUCT_BNR" })
        centerSummaryWidget.assets[0] = {
            assetType: "IMAGE",
            assetUrl: document.URL
        }
        return centerSummaryWidget
    }

    private async getCenterSummaryWidgetV2(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams): Promise<CenterSummaryWidget> {
        const center: CultCenter = pageBuilderParams.center
        const isCenterOpen: boolean = center.status == "OPEN" ? true : false
        const isCenterUpcoming: boolean = center.status == "UPCOMING" ? true : false

        // Create Array of images and Videos.
        const mediaData: MediaData[] = []
        const images: GymMedia[] = []

        const centerDocuments: CultDocument[] = _.sortBy(center.documents, (document) => {return document.relativeOrder})

        const imageDocuments: CultDocument[] = _.filter(centerDocuments, (document) => {
            if (document.tagName === "PRODUCT_BNR") {
                return true
            }
            return false
        })

        const videoDocuments: CultDocument[] = _.filter(centerDocuments, (document) => {
            if (document.tagName === "M_VIDEO") {
                return true
            }
            return false
        })

        const videoThumbnails: CultDocument[] = _.filter(centerDocuments, (document) => {
            if (document.tagName === "M_VIDEO_THUMBNAIL") {
                return true
            }
            return false
        })

        if (!_.isEmpty(videoDocuments)) {
            videoDocuments.map((videoDocument, index) => {
                const mediaUrl = CultUtil.getCultVideoCdnUrl(videoDocument.URL)
                mediaData.push({
                    media: {
                        mediaUrl: mediaUrl,
                        type: MediaType.VIDEO,
                        thumbnailUrl: videoThumbnails[index]?.URL,
                    },
                    action: { actionType: "PLAY_VIDEO", url: `curefit://videoplayer?absoluteVideoUrl=${encodeURIComponent(mediaUrl)}`},
                    fullScreenIcon: "/image/gymfit/fullscreen.png",
                })

                images.push({
                    type: MediaType.VIDEO,
                    mediaUrl: mediaUrl,
                })
            })
        }

        if (!_.isEmpty(imageDocuments)) {
            imageDocuments.map((imageDocument, index) => {
                mediaData.push({
                    media: {
                        type: MediaType.IMAGE,
                        mediaUrl: imageDocument.URL
                    }
                })
                images.push({
                    type: MediaType.IMAGE,
                    mediaUrl: imageDocument.URL,
                })
            })
        }

        // Create Timing String.
        const tz = userContext.userProfile.timezone
        const centerStartTime = TimeUtil.formatDateStringInTimeZone(
            TimeUtil.todaysDate(tz) + " " + center.businessStartTime,
            tz,
            "hh:mm A"
        )
        const centerEndTime = TimeUtil.formatDateStringInTimeZone(
            TimeUtil.todaysDate(tz) + " " + center.businessEndTime,
            tz,
            "hh:mm A"
        )

        const timing: string = `${centerStartTime} - ${centerEndTime}`
        const { appVersion } = userContext.sessionInfo
        const centerNotActiveText = isCenterUpcoming && appVersion >= CULT_UPCOMING_TAG_VERSION ? "LAUNCHING SOON" : "CLOSED"
        const centerNotActiveColour = isCenterUpcoming && appVersion >= CULT_UPCOMING_TAG_VERSION ? "#3f86ff" : "#8d93a0"
        const summaryWidget: CenterSummaryWidget = {
            widgetType: "GYM_PACK_SUMMARY",
            title: center.name,
            subTitle: {
                text: isCenterOpen ? "ACTIVE" : centerNotActiveText,
                color: isCenterOpen ? "#4ab74a" : centerNotActiveColour
            },
            mediaData,
            images: images,
            todaysSchedule: timing,
            scheduleContainerStyle: {
                justifyContent: "space-between",
            },
            timingContainerStyle: {
                flexShrink: 1
            }
        }

        return summaryWidget
    }

    getManageOptionsV2Widget(center: CultCenter): ManageOptionsWidgetV2 {
        let openCenterProps = {}
        if (center.status === "OPEN") {
            openCenterProps = {
                title: "ACTIVE",
                style: {
                    backgroundColor: "#4ab74a",
                    paddingVertical: 3,
                }
            }
        }

        return {
            widgetType: "MANAGE_OPTIONS_WIDGET_V2",
            title: "CLOSED",
            style: {
                paddingVertical: 3,
            },
            titleStyle: {
                fontFamily: "Inter-Medium",
            },
            sceneStyle: {
                marginBottom: 0,
            },
            ...openCenterProps,
        }
    }

    getHeaderWidget(center: CultCenter): HeaderWidget {
        const headerWidget = new HeaderWidget(
            {
                title: center.name
            },
            false
        )
        headerWidget.hasBottomPadding = true
        headerWidget.hasTopPadding = false
        headerWidget.style = {
            marginLeft: 0
        }
        headerWidget.titleStyle = {
            marginLeft: 0,
            marginTop: 10,
            marginBottom: 6
        }
        headerWidget.subTitleStyle = {
            marginLeft: 0
        }
        return headerWidget
    }

    private getCenterAddressWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams) {
        const centerView: CenterView = new CenterView(
            userContext,
            pageBuilderParams.center,
            pageBuilderParams.productType
        )
        const centerInfoWidget = new CenterInfoWidget(centerView.id, centerView.name, centerView.action, centerView.address, centerView.mapUrl)
        centerInfoWidget.style = {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 25
        }
        return centerInfoWidget
    }

    private async getCenterAddressWidgetV2(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams): Promise<CenterAddressWidget> {
        const centerView: CenterView = new CenterView(
            userContext,
            pageBuilderParams.center,
            pageBuilderParams.productType
        )

        const addressWidget: CenterAddressWidget = {
            widgetType: "CENTER_MAP_WIDGET",
            header: {
                title: "Address",
            },
            mapUrl: centerView.mapUrl,
            latLong: {
                lat: centerView.address?.latLong.lat,
                long: centerView.address?.latLong.long
            },
            addressText: centerView.address.addressString,
            footer: "NAVIGATE",
        }
        return addressWidget

    }

    private async getFacilitiesWidgetV2(center: CultCenter): Promise<ProductGridWidget> {
        return this.getFacilitiesWidget(center, true)
    }

    private getCenterTimingWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams) {
        const tz = userContext.userProfile.timezone
        const centerStartTime = TimeUtil.formatDateStringInTimeZone(
            TimeUtil.todaysDate(tz) + " " + pageBuilderParams.center.businessStartTime,
            tz,
            "hh:mm A"
        )
        const centerEndTime = TimeUtil.formatDateStringInTimeZone(
            TimeUtil.todaysDate(tz) + " " + pageBuilderParams.center.businessEndTime,
            tz,
            "hh:mm A"
        )
        const infoCard: InfoCard = {
            title: "Timing",
            subTitle: `${centerStartTime} - ${centerEndTime}`
        }
        const descriptionWidget = new DescriptionWidget([infoCard])
        descriptionWidget.containerStyle = {
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 0,
            paddingBottom: 0
        }
        descriptionWidget.iconType = undefined
        return descriptionWidget
    }

    private getFacilitiesWidget(center: CultCenter, showPadding?: boolean): ProductGridWidget {
        const header: Header = {
            title: "Facilities at this centre",
            style: {
                paddingLeft: showPadding ? 20 : 0,
            }
        }
        const facilities: InfoCard[] = []
        const availableFacilities = center.facilities.filter(facility => { return facility.available })
        availableFacilities.forEach(facility => {
            facilities.push({
                title: facility.name,
                image: "/image/icons/centerFacility/" + facility.type.toLowerCase() + "/2.png"
            })
        })
        if (!_.isEmpty(facilities)) {
            const facilitiesWidget: ProductGridWidget = new ProductGridWidget("SMALL_ICON", header, facilities)
            facilitiesWidget.containerStyle = {
                paddingLeft: showPadding ? 20 : 0,
            }
            return facilitiesWidget
        }
    }

    private async getWorkoutsWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams) {
        const workouts = await pageBuilderParams.workoutsPromise
        if (_.isEmpty(workouts)) {
            return
        }
        const workoutActions: RoundedImageGridItem[] = []
        const header: WidgetHeader = {
            title: "Workouts at this centre"
        }
        workouts.forEach(workout => {
            const seoParams: SeoUrlParams = {
                productName: workout.name
            }
            const workoutAction = ActionUtil.cultWorkoutV2(workout.id.toString(), undefined, userContext.sessionInfo.userAgent, seoParams, pageBuilderParams.productType, PageTypes.CultWorkoutPageV2, undefined, pageBuilderParams.center.id.toString())
            let thumbDoc = undefined
            if (userContext.sessionInfo.userAgent === "APP") {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_CARD")
            } else {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_CARD")
            }
            workoutActions.push({
                title: workout.name,
                image: thumbDoc ? "/" + thumbDoc.URL : undefined,
                action: { actionType: "NAVIGATION", url: workoutAction }
            })
        })
        const workoutsWidget: RoundedImageGridWidget = new RoundedImageGridWidget(header, workoutActions, "#FFFFFF")
        return workoutsWidget
    }

    private async getCultPackBrowseWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams): Promise<IBaseWidget> {
        const cultCityId = userContext.userProfile.city.cultCityId
        const cityId = userContext.userProfile.city.cityId
        userContext.userProfile.cultFitnessPacksPromise = CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userContext.userProfile.userId, cityId)
        const centerServiceId = await CultUtil.getCenterServiceIdFromCultCenterId(pageBuilderParams.center.id, this.centerService)
        this.logger.info(`offerLog: getCultPackBrowseWidget centerServiceId ${centerServiceId}`)
        Object.assign(userContext.userProfile, {cultProductPricesPromise: this.serviceInterfaces.offerServiceV3.getCultPackPrices({
                cultCityId,
                cityId: userContext.userProfile.cityId,
                userInfo: {
                    userId: userContext.userProfile.userId,
                    deviceId: userContext.sessionInfo.deviceId
                },
                source: AppUtil.callSourceFromContext(userContext),
                centerId: pageBuilderParams.center.id.toString(),
                centerServiceId: centerServiceId ? String(centerServiceId) : undefined
        })})
        const queryParams = { centerId: pageBuilderParams.center.id.toString() }
        const widgetId = await CultUtil.getCultPackWidgetId(userContext, this.serviceInterfaces.segmentService)
        const widgetsResponse = await this.serviceInterfaces.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, queryParams, null)
        if (!_.isEmpty(widgetsResponse) && !_.isEmpty(widgetsResponse.widgets)) {
            return widgetsResponse.widgets[0]
        }
    }

    private async getMindPackBrowseWidget(userContext: UserContext, pageBuilderParams: CultCenterPageBuilderParams): Promise<IBaseWidget> {
        const cultPackBrowseWidget: CultPackBrowseWidgetView = new CultPackBrowseWidgetView()
        cultPackBrowseWidget.header = {
            title: "Membership"
        }
        cultPackBrowseWidget.background = {
            image: "image/vm/c743f9ee-dcde-48c6-8066-c7251dab6842.png",
            color: "#eafcff"
        }
        cultPackBrowseWidget.items = [
            {
                productType: "MIND_UNLIMITED",
                header: {
                    title: "Mind Center Unlimited Packs"
                },
                maxPacksToShow: 3,
                packBackground: {
                    gradientColors: [
                        "#bdd9ff",
                        "#b6fdff"
                    ],
                    color: "#424253"
                },
                seeMore: undefined,
                packs: [],
                highlights: [
                    {
                        title: "",
                        subTitle: "Unlimited\nClasses",
                        icon: "/image/icons/cult/infinity.png"
                    },
                    {
                        title: "5",
                        subTitle: "Classes in\nother city"
                    }]
            }]
        if (userContext.userProfile.city.countryId === "IN") {
            cultPackBrowseWidget.items[0].highlights.unshift({
                title: "35+",
                subTitle: "Center\nAccess"
            })
        }
        const cultCityId = userContext.userProfile.city.cultCityId
        // userContext.userProfile.mindPacksPromise = Promise.resolve([])
        const baseOfferRequestParam: BaseOfferRequestParams = {
            source: AppUtil.callSourceFromContext(userContext),
            userId: userContext.userProfile.userId,
            deviceId: userContext.sessionInfo.deviceId,
            cityId: userContext.userProfile.cityId
        }
        Object.assign(userContext.userProfile, {mindProductPricesPromise: this.serviceInterfaces.offerServiceV3.getMindPackPrices({
            cultCityId,
            cityId: userContext.userProfile.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: pageBuilderParams.center.id.toString()
        })})
        const queryParams = { centerId: pageBuilderParams.center.id.toString() }
        return cultPackBrowseWidget.buildView(this.serviceInterfaces, userContext, queryParams)
    }

    private async getPageActions(userContext: UserContext, center: CultCenter, productType: ProductType): Promise<Action[]> {
        const sportsWorkouts = center.workouts ? center.workouts.filter((item) => item.isSportCategory) : []
        const cultSummary = await this.cacheHelper.getCultSummary(userContext.userProfile.userId)
        const isActiveMember = await isUserActiveMember(userContext, this.membershipService)
        let pageAction: Action
        if (sportsWorkouts.length > 0 &&  !isActiveMember) {
            pageAction = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BOOK NOW",
                meta: {
                    title: "Attention",
                    subTitle: "This format is currently available for cultpass ELITE members only.",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                    meta: { subTitleStyle: { fontSize: 14 } },
                }
            }
        } else {
            pageAction = {
                actionType: "NAVIGATION",
                title: "BOOK NOW",
                url: ActionUtil.bookCultClassV2("FITNESS", "centerPage", center.id.toString())
            }
            if (productType === "FITNESS" && cultSummary.trialEligibility.cult) {
                pageAction.title = "BOOK TRIAL CLASS"
            } else if (productType === "MIND" && cultSummary.trialEligibility.mind) {
                pageAction.title = "BOOK TRIAL CLASS"
            }
        }
        return [pageAction]
    }
}



export default CultCenterPageViewBuilder
