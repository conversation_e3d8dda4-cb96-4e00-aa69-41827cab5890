import { inject, injectable } from "inversify"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import {
  ICultServiceOld as ICultService,
  CULT_CLIENT_TYPES,
  CultMembership
} from "@curefit/cult-client"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import {
  ProductListWidget,
  CenterSelectionWidget,
} from "../common/views/WidgetView"
import {
  ICatalogueService,
  CATALOG_CLIENT_TYPES
} from "@curefit/catalog-client"
import { CultUpgradePack, CultCenter, BookingState } from "@curefit/cult-common"
import {
  PageTypes,
  TextWidget,
  MembershipUpgradeSummaryWidget,
  MembershipPostUpdateStatusWidget,
  MembershipFeesDetailWidget,
  MembershipFeeInfoLineItemWidget,
  WidgetView, Action
} from "@curefit/apps-common"
import moment = require("moment")
import { upgradePackName } from "../util/CultUtil"
import {
  CultMindMembershipUpgradeMetadata,
  Order,
  isMembershipUpgradeClientMetadata
} from "@curefit/order-common"
import { Product, ProductType, DeliveryCharge, ExtraCharge } from "@curefit/product-common"
import { UserContext } from "@curefit/vm-models"
import { BillingInfo } from "@curefit/finance-common"
import { OrderSummaryWidget, PaymentDetail, PriceComponent } from "../order/OrderViewBuilder"
import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { MembershipItemUtil } from "../util/MembershipItemUtil"
import { BookingSearchRequest } from "@curefit/cult-common/dist/src/Models"
import { TimeUtil } from "@curefit/util-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export interface UpgradeFeeBreakdownDetail {
  currentPackMRP: number
  nDaysLeft: number
  remainingDaysCost: number
  upgradeFees: number
  totalCost: number
  currency: string
}

@injectable()
class UpgradeMembershipViewBuilder {
  constructor(
    @inject(CULT_CLIENT_TYPES.CultFitService)
    private cultFitService: ICultService,
    @inject(CULT_CLIENT_TYPES.MindFitService)
    private mindFitService: ICultService,
    @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
    @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
    @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader)
    private catalogueService: ICatalogueService,
    @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
    @inject(BASE_TYPES.ILogger) private logger: ILogger,
    @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService
  ) {}

  async buildView(
    originalMembership: CultMembership | Membership,
    originalPack: OfflineFitnessPack,
    destinationPack: CultUpgradePack,
    cultCenter?: CultCenter,
    isMembershipServiceId?: boolean
  ) {
    this.logger.error("PMS::DEPR Deprecated method UpgradeMembershipViewBuilder.buildView called")
    throw new Error("Deprecated method UpgradeMembershipViewBuilder.buildView called")
    // const isGymFitPack = originalPack.productType === "GYMFIT_FITNESS_PRODUCT"
    // const isUpgradeToElitePack = originalPack.productType === "GYMFIT_FITNESS_PRODUCT" || originalPack.productType === "FITNESS"
    // const textWidget: TextWidget = {
    //   widgetType: "TEXT_WIDGET",
    //   content:
    //       !isUpgradeToElitePack
    //           ? "Before you upgrade to the unlimited pack, please pick your preferred center"
    //           : "Access all centers but pick a preferred ELITE center to help us plan better",
    //   fontFace: "medium",
    //   maxNumberOfLines: 4
    // }
    // const userId: string = (isMembershipServiceId) ? (originalMembership as Membership).userId : (originalMembership as CultMembership).userID
    // let alertWidgetAction: Action = null
    // if (originalPack.productType === "FITNESS") {
    //   const isUserHavingFutureBookings: boolean = await AppUtil.isUserHavingActiveBooking(
    //       (isMembershipServiceId) ? (originalMembership as Membership).id : (originalMembership as CultMembership).membershipId,
    //       userId,
    //       (isMembershipServiceId) ? TimeUtil.epochToZonedTime("UTC", (originalMembership as Membership).end) : TimeUtil.parseDate((originalMembership as CultMembership).endDate, "UTC"),
    //       this.cultFitService, this.rollbarService
    //   )
    //   if (isUserHavingFutureBookings) {
    //     alertWidgetAction = {
    //       actionType: "SHOW_ALERT_MODAL",
    //       title: "Upgrade Membership",
    //       meta: {
    //         title: "Upgrade Membership",
    //         subTitle: "All of your existing class bookings will be cancelled automatically once you upgrade the pack",
    //       }
    //     }
    //   }
    // }

    // const upgradeDetailAction: Action = {
    //   title: "Pick a center",
    //   showHelp: false,
    //   showFavourite: false,
    //   actionType: "SELECT_CENTER",
    //   meta: {
    //     pageFrom: `${PageTypes.UpgradeMembership}`,
    //     cityId: isMembershipServiceId ? (originalMembership as Membership).metadata.cityId : this.cityService.getCityByCultCityId((originalMembership as CultMembership).cityID).cityId,
    //     useCenterServiceId: true
    //   },
    //   productType: "FITNESS" // always open Cult center selection
    // }
    // let upgradeAction: Action = null
    // if (!_.isNil(alertWidgetAction) && alertWidgetAction != null) {
    //   alertWidgetAction.meta.actions = [upgradeDetailAction]
    //   upgradeAction = alertWidgetAction
    // } else {
    //   upgradeAction = upgradeDetailAction
    // }
    // const widgets = [
    //   new ProductListWidget(
    //     "SMALL",
    //     {
    //       title: "How it works",
    //       color: "#000000",
    //       titleFontSize: 18
    //     },
    //       !isUpgradeToElitePack ? [
    //       {
    //         subTitle:
    //           "You pay a fee to upgrade to a Cult unlimited pack of the same duration",
    //         icon: "/image/pack-upgrade/wallet1%402x.png",
    //         subTitleFontSize: 14
    //       },
    //       {
    //         subTitle: "You only pay for the remaining days in your pack",
    //         icon: "/image/pulse/pulse-return1.png",
    //         subTitleFontSize: 14
    //       },
    //       {
    //         subTitle:
    //           "The total number of remaining days and pause days of the existing pack are carried over to the new pack",
    //         icon: "/image/pack-upgrade/tickbox2%402x.png",
    //         subTitleFontSize: 14
    //       },
    //       {
    //         subTitle:
    //           "You will be able to access all cult and mind centres in your city",
    //         icon: "/image/pack-upgrade/location1%402x.png",
    //         subTitleFontSize: 14
    //       }
    //     ] : [
    //         {
    //           subTitle:
    //               "Upgrade unlocks unlimited access to all ELITE & PRO cult centers/gyms in your city",
    //           icon: "/image/icons/howItWorks/up.png",
    //           subTitleFontSize: 14
    //         },
    //         {
    //           subTitle: "ELITE costs more. You need to pay an upgrade fee as shown on next page",
    //           icon: "/image/pack-upgrade/wallet1%402x.png",
    //           subTitleFontSize: 14
    //         },
    //         {
    //           subTitle:
    //               "Remaining membership days will be same after upgrade",
    //           icon: "/image/pack-upgrade/tickbox2%402x.png",
    //           subTitleFontSize: 14
    //         },
    //         {
    //           subTitle:
    //               "Remaining pause days will be carried forward post the upgrade",
    //           icon: "/image/icons/howItWorks/pause_3.png",
    //           subTitleFontSize: 14
    //         }
    //       ]
    //   ),
    //   {
    //     ...textWidget,
    //     hasDividerBelow: true
    //   },
    //   {
    //     title: "Pick Preferred Center",
    //     prefixText: "Preferred Center: ",
    //     canChangeCenter: true,
    //     preferredCenterId: cultCenter ? cultCenter.id : undefined,
    //     preferredCenterName: cultCenter ? cultCenter.name : undefined,
    //     widgetType: "CENTER_PICKER_WIDGET",
    //     action: upgradeAction,
    //     infoAction: {
    //       actionType: "SHOW_ALERT_MODAL",
    //       meta: {
    //         title: "What is a preferred centre?",
    //         subTitle:
    //           "When you pick a preferred centre, your booking for that centre is given a higher preference. This will especially help when you want to book a class that’s filling up fast in that centre. Picking a preferred centre does not prevent you from booking classes in any other Cult Centre.",
    //         actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
    //       }
    //     },
    //     hasDividerBelow: true
    //   }
    // ].filter(widget => widget !== null)
    // const { title, productId, productType, imageVersion } = originalPack
    // const { id, endDate, remainingPauseDays, startDate } = originalMembership as CultMembership
    // let originalMembershipData = {
    //   id,
    //   endDate,
    //   startDate,
    //   remainingPauseDays
    // }
    // if (isMembershipServiceId) {
    //   const { id: memId, end, remainingPauseDuration, start } = originalMembership as Membership
    //   originalMembershipData = {
    //     id: memId,
    //     endDate: moment(end).format("YYYY-MM-DD"),
    //     startDate: moment(start).format("YYYY-MM-DD"),
    //     remainingPauseDays: Math.floor( remainingPauseDuration / (24 * 60 * 60 * 1000) ),
    //   }
    // }
    // const meta: CultMindMembershipUpgradeMetadata = {
    //   productId: destinationPack.productId,
    //   membershipId: originalMembershipData.id,
    //   isUpgradeMembership: true,
    //   isMembershipServiceId: isMembershipServiceId,
    //   originalMembership: originalMembershipData,
    //   originalPack: {
    //     title,
    //     productId,
    //     productType,
    //     imageVersion
    //   }
    // }
    // if (cultCenter) {
    //   meta.cultCenter = {
    //     id: cultCenter.id,
    //     name: cultCenter.name
    //   }
    // }
    // return {
    //   widgets,
    //   actions: [
    //     {
    //       title: "NEXT",
    //       actionType: "UPGRADE_MEMBERSHIP",
    //       meta,
    //       disabled: !Boolean(cultCenter)
    //     }
    //   ]
    // }
  }

  async buildUpgradeMembershipOrderCheckoutSummary(
    clientMetadata: Order["clientMetadata"],
    userContext: UserContext
  ): Promise<WidgetView[]> {
    if (!isMembershipUpgradeClientMetadata(clientMetadata)) {
      return []
    }
    const { originalPack, originalMembership, cultCenter, isMembershipServiceId = false } = clientMetadata
    const upgradeFeeBreakdown: UpgradeFeeBreakdownDetail = (await this.cultFitService.getPackUpgradeFees(
      userContext.userProfile.userId,
      String(originalMembership.id),
      "CUREFIT_APP",
        isMembershipServiceId
    )) as UpgradeFeeBreakdownDetail
    const currencySymbol: string = (() => {
      switch (upgradeFeeBreakdown.currency) {
        case "INR":
          return "₹"
        default:
          return ""
      }
    })()
    const originalMembershipData = await this.membershipService.getMembershipById(originalMembership.id)
    const isSelectMembership = MembershipItemUtil.isSelectMembership(originalMembershipData)
    const isGymFitPack = originalPack.productType === "GYMFIT_FITNESS_PRODUCT"
    const upgradeMembershipSummaryWidget: MembershipUpgradeSummaryWidget = {
      widgetType: "MEMBERSHIP_UPGRADE_SUMMARY_WIDGET",
      title: "UPGRADE PACK",
      originalPack: isSelectMembership ? {
        title: "",
        image: "image/packs/upgrade/cultpass_select.png"
      } : !isGymFitPack ? {
        title: originalPack.title,
        image: `/image/packs/${
          originalPack.productType === "FITNESS" ? "cult" : "mind"
        }/${originalPack.productId}/${originalPack.imageVersion}_mag.jpg`
      } : {
        title: "",
        image: "/image/packs/gymfit/GYMFIT25/2_mag.jpg"
      },
      destinationPack: !isGymFitPack && !isSelectMembership ? {
        /**
         * @todo: this is a hack to generate the name here
         * we should get it from Cult backend
         */
        title: upgradePackName(originalPack.title, originalPack.productType),
        image: `/image/packs/upgrade/upgrade-pack%402x.png`
      } : {
        title: "",
        image: "/image/packs/cult/CULTPACK38/80_mag.jpg"
      },
      arrowImageUrl: "image/down_arrow.png"
    }
    const membershipPostUpdateStatusWidget: MembershipPostUpdateStatusWidget = !isGymFitPack ? {
      widgetType: "MEMBERSHIP_POST_UPDATE_STATUS_WIDGET",
      status: [
        {
          header: "Membership Ends on",
          infoText: moment(originalMembership.endDate).format("D MMM YYYY")
        },
        {
          header: "Remaining days",
          infoText: `${upgradeFeeBreakdown.nDaysLeft}`
        },
        {
          header: "Pause days left",
          infoText: originalMembership.remainingPauseDays.toString()
        }
      ]
    } : {
      widgetType: "MEMBERSHIP_POST_UPDATE_STATUS_WIDGET",
      status: [
        {
          header: "Membership Ends on",
          infoText: moment(originalMembership.endDate).format("D MMM YYYY")
        },
        {
          header: "Remaining days",
          infoText: `${upgradeFeeBreakdown.nDaysLeft}`
        },
      ]
    }
    const centerPickerWidget: CenterSelectionWidget = {
      title: "Pick Preferred Center",
      prefixText: "Preferred Center: ",
      canChangeCenter: false,
      preferredCenterId: cultCenter ? cultCenter.id : null,
      preferredCenterName: cultCenter ? cultCenter.name : null,
      widgetType: "CENTER_PICKER_WIDGET",
      action: {
        title: "Pick a center",
        showHelp: false,
        showFavourite: false,
        actionType: "SELECT_CENTER",
        meta: {},
        productType: originalPack.productType
      },
      infoAction: {
        actionType: "SHOW_ALERT_MODAL",
        meta: {
          title: "What is a preferred centre?",
          subTitle:
            "When you pick a preferred centre, your booking for that centre is given a higher preference. This will especially help when you want to book a class that’s filling up fast in that centre. Picking a preferred centre does not prevent you from booking classes in any other Cult Centre.",
          actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
        }
      },
      colocatedCenter: null
    }
    const feesDetailWidget: MembershipFeesDetailWidget = !isGymFitPack ? {
      widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
      header: "Upgrade Fees Details",
      subHeader:
        "An amount is being charged because the pack you want to upgrade to costs more than your current pack",
      feeItemRows: [
        {
          label: `Cost of ${originalPack.title}`,
          data: `${currencySymbol}${upgradeFeeBreakdown.currentPackMRP}`
        },
        {
          label: "Number of days remaining",
          data: `${upgradeFeeBreakdown.nDaysLeft}`
        },
        {
          label: `Estimated price difference for ${upgradeFeeBreakdown.nDaysLeft} days`,
          data: `${currencySymbol}${upgradeFeeBreakdown.totalCost -
            upgradeFeeBreakdown.upgradeFees}`
        },
        {
          label: "Upgrade fee",
          data: `${currencySymbol}${upgradeFeeBreakdown.upgradeFees}`
        }
      ],
      bottomLine: {
        label: "Total upgrade fee to be paid",
        data: `${currencySymbol}${upgradeFeeBreakdown.totalCost}`
      }
    } : {
      widgetType: "MEMBERSHIP_FEE_INFO_DETAIL_WIDGET",
      header: "Why upgrade fee?",
      subHeader:
          "You are being charged to upgrade because cultpass ELITE costs more than your current cultpass PRO",
      feeItemRows: [
        {
          label: `Remaining days`,
          data: `${upgradeFeeBreakdown.nDaysLeft}`
        },
        {
          label: "Value of current cultpass",
          data: `${currencySymbol}${upgradeFeeBreakdown.currentPackMRP}`
        },
        {
          label: `Price difference for ${upgradeFeeBreakdown.nDaysLeft} days`,
          data: `${currencySymbol}${upgradeFeeBreakdown.totalCost -
          upgradeFeeBreakdown.upgradeFees}`
        },
      ],
      bottomLine: {
        label: "Total upgrade fee",
        data: `${currencySymbol}${upgradeFeeBreakdown.totalCost}`
      }
    }
    const totalFeeLineItemWidget: MembershipFeeInfoLineItemWidget = {
      widgetType: "MEMBERSHIP_FEE_INFO_LINE_WIDGET",
      text: "Total Payable",
      backgroundColor: "rgba(247, 247, 247, 1.0)",
      fees: `${currencySymbol}${upgradeFeeBreakdown.totalCost}`,
      infoIcon: true,
      infoIconAction: {
        type: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
        actionType: "SHOW_UPGRADE_MEMBERSHIP_FEE_DETAILS_MODAL",
        meta: {
          widgets: [{ ...feesDetailWidget }]
        }
      },
      faded: false
    }
    const widgets = [
      upgradeMembershipSummaryWidget,
      membershipPostUpdateStatusWidget,
      centerPickerWidget,
      totalFeeLineItemWidget
    ]
    return widgets
  }

  buildUpgradeOrderSummaryWidget(product: Product, billingInfo: BillingInfo, paymentDetail: PaymentDetail, getPriceDetails: (
    productType: ProductType, billingInfo: BillingInfo, deliveryCharge: DeliveryCharge, packagingCharge: ExtraCharge, removeBasePrice: boolean, removeTax: boolean, areDetailsForCheckout: boolean
  ) => PriceComponent[]): OrderSummaryWidget {
    const orderSummaryWidget: OrderSummaryWidget = {
      productId: product.productId,
      widgetType: "ORDER_SUMMARY_WIDGET",
      title:  product.title,
      thumbnailImages: [],
      magazineImage: "",
      icon: "FITNESS",
      iconUrl: `/image/icons/checkout/${product.productType.toLowerCase()}.png`,
      price: {
          listingPrice: _.round(billingInfo.total, 2),
          mrp: _.round(billingInfo.total, 2),
          amountPaid: _.round(billingInfo.total, 2),
          currency: billingInfo.currency
      },
      priceDetails: getPriceDetails(product.productType, billingInfo, undefined, undefined, false, false, false),
      paymentDetail: paymentDetail
    }
    return orderSummaryWidget
  }
}

export default UpgradeMembershipViewBuilder
