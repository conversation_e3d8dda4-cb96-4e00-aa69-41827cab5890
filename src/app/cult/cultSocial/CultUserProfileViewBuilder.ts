import { inject, injectable } from "inversify"
import {
    CultUserProfilePage,
    ProfileTag,
    ProfileTagSelectWidget,
    SocialProfileVisibilityWidget,
    User,
    UserProfileDetailWidget
} from "@curefit/apps-common"
import { InfoCard, ProductListWidget } from "../../common/views/WidgetView"
import { ProfileAttributeEntryCode, ProfileVisibility, TagEntry, UserProfileEntry } from "@curefit/social-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { CacheHelper } from "../../util/CacheHelper"
import { Header, UserContext } from "@curefit/vm-models"
import AppUtil, { CUREFIT_WHITE_LOGO } from "../../util/AppUtil"
import IssueBusiness, { IssueDetailView } from "../../crm/IssueBusiness"
import CultUtil from "../../util/CultUtil"
import * as _ from "lodash"


export const TAG_COLORS = [["#17d8e5", "#ac9aff"], ["#fb8a72", "#f64cac"], ["#8cc5f3", "#c495e0"], ["#e5b017", "#ff9a9a"]]

@injectable()
class CultUserProfileViewBuilder {
    constructor(@inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness) { }

    async buildView(userSocialSummary: UserProfileEntry, userContext: UserContext): Promise<CultUserProfilePage> {

        const userInfo = await this.userCache.getUser(userSocialSummary.userId)
        const socialIssues = await this.issueBusiness.getCultSocialIssues(userContext)
        const profileId = userSocialSummary.id

        const socialProfileVisibilityWidget = this.socialProfileVisibilityWidget(profileId, userSocialSummary.visibility, socialIssues)
        const userProfileDetailWidget: UserProfileDetailWidget = await this.getUserProfileDetailWidget(userInfo, profileId, userSocialSummary, userContext)
        const howItWorksWidget: ProductListWidget = this.howItWorksWidget()
        const socialTnc: ProductListWidget = this.socialTnc()

        const widgets = [socialProfileVisibilityWidget, userProfileDetailWidget, howItWorksWidget, socialTnc]
        const userProfileDetailPage = new CultUserProfilePage(widgets)
        return userProfileDetailPage
    }

    private socialProfileVisibilityWidget(profileId: number, visibility: ProfileVisibility, socialIssues: IssueDetailView[]): SocialProfileVisibilityWidget {
        return {
            widgetType: "SOCIAL_PROFILE_VISIBILITY_WIDGET",
            title: "Your Profile",
            image: "/image/icons/cult/cult-profile-page.jpg",
            manageOptions: {
                icon: "/image/icons/cult/dot3.png",
                displayText: "Manage",
                options: [{ displayText: "Dismiss" }, {
                    displayText: "Need Help",
                    action: {
                        actionType: "REPORT_ISSUE",
                        meta: { issues: socialIssues }
                    }
                }]
            },
            profileVisibility: {
                title: "Profile Visibility",
                action: {
                    title: visibility === "PRIVATE" ? "PRIVATE" : "PUBLIC",
                    actionType: "REST_API",
                    meta: {
                        method: "PUT",
                        url: `/cult/social/profileUpdate`,
                        body: { "cultUserProfile": { "visibility": visibility === "PRIVATE" ? "PUBLIC" : "PRIVATE" } }
                    }
                },
                description: visibility === "PRIVATE" ? "Change this to public to see profiles of athletes you workout with" : "",
                isProfilePublic: visibility === "PRIVATE" ? false : true,
            },
            analyticsData: {
                profileVisibility: visibility === "PRIVATE" ? "PUBLIC" : "PRIVATE",
                pageFrom: "Social Profile page"
            }
        }
    }

    private getSelectedTags(tags: TagEntry[]): ProfileTag[] {
        const selectedTags: ProfileTag[] = []
        const tagColorsLength = TAG_COLORS.length
        let selectedTagCount = 0
        tags.forEach(tag => {
            if (tag.mapped) {
                selectedTags.push({ title: tag.displayName.toUpperCase(), selectedColors: TAG_COLORS[selectedTagCount % tagColorsLength], disabledColors: ["#d8d8d8", "#d8d8d8"], isSelected: tag.mapped, id: tag.id })
                selectedTagCount++
            }
        })
        return selectedTags
    }

    private getSelectTagWidget(userSocialSummary: UserProfileEntry): ProfileTagSelectWidget {
        const tags: ProfileTag[] = userSocialSummary.tags.map(tag => {
            return { title: tag.displayName.toUpperCase(), selectedColors: TAG_COLORS[Math.floor(Math.random() * 4)], disabledColors: ["#d8d8d8", "#d8d8d8"], isSelected: tag.mapped, id: tag.id }
        })

        return {
            widgetType: "PROFILE_TAG_SELECT_WIDGET",
            title: "Profile Tags",
            description: "What do you associate yourself as. Let everyone know your interest areas and hobbies. (Select Upto 4)",
            tags: tags,
            maxTagSelectionCount: 4
        }
    }

    private async getUserProfileDetailWidget(userInfo: User, profileId: number, userSocialSummary: UserProfileEntry, userContext: UserContext): Promise<UserProfileDetailWidget> {
        const streak = (await AppUtil.isUserProfilStreakSupported(userContext)) ? CultUtil.getStreak(userSocialSummary.profileAttributes.find( (attribute) => {
            return _.get(attribute, "attribute.code") === ProfileAttributeEntryCode.STREAK
        }), userContext.userProfile.timezone) : undefined
        return {
            widgetType: "USER_PROFILE_DETAIL_WIDGET",
            imageUrl: userInfo.profilePictureUrl,
            dummyImageUrl: "/image/icons/cult/dummy-user-icon.png",
            curefitLogoUrl: CUREFIT_WHITE_LOGO,
            name: `${userInfo.firstName ? userInfo.firstName : ""} ${userInfo.lastName ? userInfo.lastName : ""}`,
            profileId: profileId,
            attributes: await CultUtil.getAttributes(userSocialSummary.profileAttributes, userContext),
            streak,
            header: {
                title: "Your Profile Card",
                seemore: {
                    actionType: "SHARE_SCREENSHOT",
                    title: "SHARE PROFILE",
                    meta: {
                        shareTitle: "My Profile",
                        shareMessage: "",
                    }
                }
            },
            tagsHeader: {
                title: "Profile Tags ",
                description: "What do you associate yourself as. Let everyone know your interest areas and hobbies. (Select Upto 4)"
            },
            tags: this.getSelectedTags(userSocialSummary.tags),
            footerAction: {
                title: "EDIT PROFILE",
                actionType: "SHOW_EDIT_PROFILE_MODAL",
                meta: {
                    header: {
                        title: "Edit Profile",
                        closeAction: {
                            title: "DONE",
                            actionType: "HIDE_EDIT_PROFILE_MODAL"
                        },
                    },
                    profileUrl: userInfo.profilePictureUrl,
                    dummyImageUrl: "/image/icons/cult/dummy-user-icon.png",
                    editAction: {
                        actionType: "NAVIGATION",
                        iconUrl: "/image/icons/cult/edit.png",
                        url: "curefit://editprofilepic",
                        meta: {
                            profileUrl: userInfo.profilePictureUrl,
                            dummyImageUrl: "/image/icons/cult/dummy-user-icon.png",
                        }
                    },
                    profileId: profileId,
                    displayName: {
                        title: "Display Name",
                        name: `${userInfo.firstName ? userInfo.firstName : ""} ${userInfo.lastName ? userInfo.lastName : ""}`
                    },
                    widgets: [this.getSelectTagWidget(userSocialSummary)]
                }
            }
        }

    }

    private howItWorksWidget(): ProductListWidget {
        try {
            const header: Header = {
                title: "How it works",
                titleProps: {
                    style: {
                        fontSize: 18,
                        paddingLeft: 20
                    }
                }
            }
            const cellStyle = {
                paddingRight: 20,
                paddingLeft: 20
            }
            const infoCards: InfoCard[] = [
                {
                    subTitle: "Make your profile public to view the profiles of other Athletes",
                    icon: "/image/icons/cult/switch-1.png",
                    cellStyle: cellStyle
                },
                {
                    subTitle: "When you attend a class, profiles of other Athletes in your class will appear on your wall, give them a cheer",
                    icon: "/image/icons/cult/circle-1.png",
                    cellStyle: cellStyle
                },
                {
                    subTitle: "Click and share your photos with the group (Coming Soon)",
                    icon: "/image/icons/cult/camera-1.png",
                    cellStyle: cellStyle
                }
            ]
            return {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                hideSepratorLines: true,
                header: header,
                items: infoCards,
                noBottomPadding: true
            }
        }
        catch (e) {
            return undefined
        }
    }

    private socialTnc(): ProductListWidget {
        const numberStyle = {
            fontFamily: "BrandonText-Bold",
            fontSize: 18,
            color: "#000000"
        }
        const textStyle = {
            flex: 1
        }
        const cellStyle = { paddingLeft: 20, paddingRight: 20 }
        const tnc: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            hideSepratorLines: true,
            type: "NUMBERED",
            noTopPadding: true,
            header: {
                title: "Terms & Conditions",
                topAction: {
                    actionString: "DETAILED T&C",
                    action: {
                        actionType: "INTERNAL_LINK",
                        title: "SETUP NOW",
                        url: "https://static.cure.fit/#5"
                    }
                },
                titleProps: {
                    style: {
                        fontSize: 18,
                        paddingLeft: 20,
                        paddingRight: 20,
                        letterSpacing: 0
                    }
                },
                rowStyling: {
                    marginRight: 20
                }
            },
            items: [
                {
                    number: "1",
                    numberStyle: numberStyle,
                    subTitle: "Once you make your profile ‘Public’, all members with public profiles can view your profile when they attend a class with you. You can choose to make your profile ‘Private’ anytime",
                    textStyle: textStyle,
                    cellStyle: cellStyle
                },
                {
                    number: "2",
                    numberStyle: numberStyle,
                    subTitle: "If your profile is ‘Private’ then, you will not be able to view the profiles of other members in your class",
                    textStyle: textStyle,
                    cellStyle: cellStyle
                },
                {
                    number: "3",
                    numberStyle: numberStyle,
                    subTitle: "All terms and conditions shall remain same as provided in cult.fit Terms of Use",
                    textStyle: textStyle,
                    cellStyle: cellStyle
                }
            ]
        }
        return tnc
    }

}

export default CultUserProfileViewBuilder
