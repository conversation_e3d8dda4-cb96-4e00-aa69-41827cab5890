import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { CultClass, CultMomentPaginationResponse, CultMomentsForClass } from "@curefit/cult-common"
import {
    CultMemoriesListWidget,
    CultSocialProfileListPageResponse,
    IWidgetType,
    MemoriesList,
    ProfileTag,
    SocialMemoriesListPage,
    UserProfileDetailWidget,
    WorkoutMember,
    WorkoutMemberHistoryListWidget
} from "@curefit/apps-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { CacheHelper } from "../../util/CacheHelper"
import AppUtil, { CUREFIT_WHITE_LOGO } from "../../util/AppUtil"
import { HeaderWidget } from "../../page/PageWidgets"
import { TimeUtil, Timezone } from "@curefit/util-common"
import CultUtil, {
    BLUR_PROFILE_IMAGES,
    CULT_MEMORY_IMAGE_ASPECT_RATIO,
    SHOW_INITIAL_IN_CULT_MEMORIES_VERSION
} from "../../util/CultUtil"
import * as _ from "lodash"
import { ProfileAttributeEntryCode, UserProfileEntry } from "@curefit/social-common"
import { TAG_COLORS } from "./CultUserProfileViewBuilder"
import { Action } from "../../common/views/WidgetView"


@injectable()
class CultMomentsViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
    ) { }
    async buildView(builderParams: {
        cultMoments: CultMomentsForClass,
        pagination: CultMomentPaginationResponse
    }, userContext: UserContext): Promise<any> {
        const widgets = []
        const cultMoments: CultMomentsForClass = builderParams.cultMoments
        const { attendedUsers, classDetails, userProfile } = cultMoments
        const tz = userContext.userProfile.timezone

        widgets.push(this.getHeaderWidget(classDetails, tz))
        if (attendedUsers.length > 0) {
            const memberListWidget = await this.getMemberListWidget(attendedUsers, classDetails, userProfile, userContext)
            widgets.push(memberListWidget)
        }
        const cultMemoriesListWidget: CultMemoriesListWidget = this.getCultMemoriesListWidget(cultMoments, tz)
        widgets.push(cultMemoriesListWidget)
        return {
            widgets: widgets
        }
    }

    getCultMemoriesListWidget(cultMoments: CultMomentsForClass, tz: Timezone): CultMemoriesListWidget {
        const workoutName = cultMoments.classDetails.Workout.name
        const memories = CultUtil.getMomentsV2(cultMoments.moments, cultMoments.classDetails, workoutName, tz,
            true, false, false, true)


        return {
            widgetType: "CULT_MEMORIES_LIST_WIDGET",
            memories: memories,
            aspectRatio: CULT_MEMORY_IMAGE_ASPECT_RATIO,
            header: {
                title: "Class Memories"
            }
        }
    }

    async getMemberListWidget(attendedUsers: UserProfileEntry[], classDetails: CultClass, userSocialProfile: UserProfileEntry, userContext: UserContext): Promise<WorkoutMemberHistoryListWidget> {
        const isCurrentUserProfilePublic = userSocialProfile.visibility === "PUBLIC"
        const memberList: WorkoutMember[] = await Promise.all(attendedUsers.map(async (user, index) => {
            const isOtherUserProfilePublic = user.visibility === "PUBLIC"
            const userDetails = await this.userCache.getUser(user.userId)
            const profileAction: Action = CultUtil.getProfileClickAction(isCurrentUserProfilePublic, isOtherUserProfilePublic, index, classDetails.id)
            const showMemberInitial = userContext.sessionInfo.appVersion >= SHOW_INITIAL_IN_CULT_MEMORIES_VERSION
            return CultUtil.createWorkoutMember(isCurrentUserProfilePublic, isOtherUserProfilePublic, userDetails, profileAction, showMemberInitial)
        }))

        const workoutMemberWidget: WorkoutMemberHistoryListWidget = {
            widgetType: "WORKOUT_MEMBER_HISTORY_LIST_WIDGET",
            header: {
                title: "You worked out with",
                seemore: {
                    title: "VIEW ALL",
                    actionType: "SHOW_WORKOUT_MEMBER_HISTORY_LIST_MODAL",
                    meta: {
                        header: {
                            title: "You worked out with",
                        },
                        memberList,
                        isCurrentUserProfilePublic: isCurrentUserProfilePublic
                    }
                }
            },
            isCurrentUserProfilePublic: isCurrentUserProfilePublic,
            memberList
        }
        if (!isCurrentUserProfilePublic) {
            workoutMemberWidget.header["seemore"] = {
                title: "VIEW ALL",
                ...CultUtil.getMakeProfilePublicAction(userSocialProfile.visibility === "PUBLIC")
            }
        }
        return workoutMemberWidget
    }


    getHeaderWidget(classDetails: CultClass, tz: Timezone): HeaderWidget {
        const classStartTime = TimeUtil.formatDateStringInTimeZone(classDetails.date + " " + classDetails.startTime, tz, "h:mm A")
        const subTitle = `${TimeUtil.formatDateStringInTimeZone(classDetails.date, tz, "ddd MMM D")}, ${classStartTime}, ${classDetails.Center.name} `
        return {
            hasTopPadding: false,
            hasBottomPadding: true,
            titleStyle: {
                fontFamily: "BrandonText-Bold",
                fontSize: 22,
                color: "#000000",
            },
            subTitleStyle: {
                fontFamily: "BrandonText-Regular",
                fontSize: 12,
                color: "#666666",
            },
            widgetTitle: {
                title: classDetails.Workout && classDetails.Workout.name,
                subTitle: subTitle
            },
            widgetType: "HEADER_WIDGET",
        }
    }

    async getUsersProfileByClass(usersProfile: UserProfileEntry[], selectedProfileIndex: number, userContext: UserContext): Promise<CultSocialProfileListPageResponse> {
        const widgetType: IWidgetType = "USER_PROFILE_DETAIL_WIDGET"
        let snapToIndex = 0, filteredProfileIndex = 0
        const filterPublicProfile = usersProfile.filter((profile, index) => {
            if (selectedProfileIndex === index) {
                snapToIndex = filteredProfileIndex
            }
            const isProfilePublic = profile.visibility === "PUBLIC"
            if (isProfilePublic) {
                filteredProfileIndex++
            }
            return isProfilePublic
        })
        const workedOutUsers: UserProfileDetailWidget[] = await Promise.all(filterPublicProfile.map(async (user) => {
            const userDetails = await this.userCache.getUser(user.userId)
            const tagColorsLength = TAG_COLORS.length
            const userTags: ProfileTag[] = []
            if (!_.isEmpty(user.tags)) {
                user.tags.forEach((tag, index) => {
                    if (tag.mapped === true) {
                        userTags.push({ title: tag.displayName.toUpperCase(), selectedColors: TAG_COLORS[index % tagColorsLength], isSelected: tag.mapped, id: tag.id })
                    }
                })
            }
            const streak = (await AppUtil.isUserProfilStreakSupported(userContext)) ? CultUtil.getStreak(user.profileAttributes.find( (attribute) => {
                return _.get(attribute, "attribute.code") === ProfileAttributeEntryCode.STREAK
            }), userContext.userProfile.timezone) : undefined
            return {
                widgetType: widgetType,
                imageUrl: userDetails.profilePictureUrl,
                curefitLogoUrl: CUREFIT_WHITE_LOGO,
                name: `${userDetails.firstName ? userDetails.firstName : ""} ${userDetails.lastName ? userDetails.lastName : ""}`,
                attributes: await CultUtil.getAttributes(user.profileAttributes, userContext),
                tags: userTags,
                streak
            }
        }))
        return {
            header: {
                title: "You worked out with",
            },
            data: workedOutUsers,
            snapToIndex: snapToIndex
        }
    }

    async  getMemoriesListView(userContext: UserContext, response: { cultMoments: CultMomentsForClass[], pagination: CultMomentPaginationResponse }, pageNumber: number, pageSize: number): Promise<SocialMemoriesListPage> {
        const tz = userContext.userProfile.timezone
        const { cultMoments, pagination } = response
        const data: { [month: string]: MemoriesList } = {}
        await Promise.all(cultMoments.map(async (moment) => {
            const classDate = moment.classDetails.date
            const key = TimeUtil.formatDateStringInTimeZone(classDate, tz, "MMM YYYY")
            let memoryListSectionData: MemoriesList = data[key]
            if (!memoryListSectionData) {
                memoryListSectionData = {
                    title: key,
                    data: []
                }
                data[key] = memoryListSectionData
            }
            const memoryItemV2 = await CultUtil.getCultMemoryItemV2(moment, userContext, this.userCache, true, true)
            memoryListSectionData.data.push(memoryItemV2)
        }))
        const sections: MemoriesList[] = _.values(data)
        let nextQuery
        if (pageNumber < response.pagination.totalPages) {
            nextQuery = {
                pageNumber: pageNumber + 1,
                pageSize: pageSize
            }
        }
        const memoriesSection: MemoriesList[] = sections || []

        return {
            header: { title: "Memories" },
            memoriesSection: memoriesSection,
            nextQuery,
        }
    }
}

export default CultMomentsViewBuilder
