import { SimpleWod } from "@curefit/fitness-common"
import * as _ from "lodash"
import { inject, injectable } from "inversify"
import { CdnUtil } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { BookingDetail } from "@curefit/albus-client"
import { WODInfoWidget } from "@curefit/apps-common"
import { WodDetailsResponse } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ICultServiceOld as ICultService } from "@curefit/cult-client/dist/src/ICultServiceOld"
import { BASE_TYPES, Logger } from "@curefit/base"
import CareUtil from "../util/CareUtil"
import CultUtil from "../util/CultUtil"

export interface IVideo {
    videoUrl: string
    absoluteVideoUrl: string
    thumbnail: string,
    title: string,
    subTitle?: string
}

export interface IWODDetail {
    movements?: IVideo[],
    preview: IVideo
}

const NO_MOVEMENTS_WORKOUT_IDS = [1, 4, 18, 28, 99, 102, 113, 216]

@injectable()
class WodViewBuilder {

    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
                @inject(BASE_TYPES.ILogger) private logger: Logger) {
    }

    getView(wod: SimpleWod, workoutID?: number): IWODDetail {
        const movements: IVideo[] = []
        let preview = undefined

        const bodyParts: string[] = []

        let hasMovements: boolean = false

        const video = _.find(wod.media, { type: "VIDEO" })
        let thumb = _.find(wod.media, item => item.type === "IMAGE" && item.url.includes("_V2."))
        if (!thumb) {
            thumb = _.find(wod.media, { type: "IMAGE" })
        }

        // if (!video || !thumb) {
        //     return undefined
        // }

        wod.parts.forEach(part => {
            part.movements.forEach(movement => {
                if (movement && movement.publish) {
                    const video = _.find(movement.media, { type: "VIDEO" })
                    if (video) {
                        hasMovements = true
                        movements.push({
                            videoUrl: video.url,
                            absoluteVideoUrl: CdnUtil.getCdnUrl(video.url),
                            thumbnail: !_.isNil(thumb) ? "/" + thumb.url : undefined,
                            title: undefined,
                            subTitle: movement.title
                        })
                    }

                    if (movement.bodyParts) {
                        movement.bodyParts.forEach(tag => {
                            bodyParts.push(tag)
                        })
                    }
                }
            })
        })

        if (!(NO_MOVEMENTS_WORKOUT_IDS.includes(workoutID)) && !hasMovements) {
            return undefined
        }
        const uniqueBodyParts = _.uniq(bodyParts)
        if (video) {
            preview = {
                videoUrl: video ? video.url : undefined,
                absoluteVideoUrl: video ? CdnUtil.getCdnUrl(video.url) : undefined,
                thumbnail: thumb ? "/" + thumb.url : undefined,
                title: uniqueBodyParts.length > 0 ? "Focus for workout of the day:" : "About:",
                subTitle: uniqueBodyParts.length > 0 ? uniqueBodyParts.join(", ") : wod.description
            }
        }
        else if (thumb && !_.isEmpty(movements)) {
            movements[0].thumbnail = "/" + thumb.url
        }
        const wodDetail = <IWODDetail>{
            preview: preview,
            movements: movements
        }

        return wodDetail

    }

    async getLivePTWodWidget(userContext: UserContext, bookingDetail: BookingDetail): Promise<WODInfoWidget> {
        const isLiveSGTConsultation = CareUtil.isLiveSGTDoctorType(bookingDetail.consultationOrderResponse.consultationProduct.doctorType)
        if (isLiveSGTConsultation && !_.isEmpty(bookingDetail.consultationOrderResponse.metadata) && !_.isEmpty(bookingDetail.consultationOrderResponse.metadata.wodId)) {
            const wodId = bookingDetail.consultationOrderResponse.metadata.wodId
            const classDurationInHrs = 1
            const wod: WodDetailsResponse = await this.cultFitService.getWodDetails(userContext.userProfile.userId, wodId, true, classDurationInHrs)
            return CultUtil.getWodInfoWidget(wod, wod.calorieValue)
        }
    }
}
export default WodViewBuilder
