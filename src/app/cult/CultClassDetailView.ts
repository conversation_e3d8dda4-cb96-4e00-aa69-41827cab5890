import Wod<PERSON>iew<PERSON>uilder, { IWODDetail } from "./WodViewBuilder"
import { CultCenter, CultClass, CultDocument, PulseProduct } from "@curefit/cult-common"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import {
  Action,
  ActionCardWidget,
  DescriptionWidget, Header,
  InfoCard,
  MembershipContextWidget,
  PulseBenefitWidget,
  PulseRentalRemainingWidget,
  WidgetView
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { Banner, HeaderWidget, BannerCarouselWidget } from "../page/PageWidgets"
import CultUtil, {
  CULT_CLASS_BOOK,
  CULT_FREE_CLASS_BOOK,
  CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME,
  getTimeSlotsForWaitlistExtensionWidget,
  CULT_FULL_CAPACITY_UPCOMING_CENTER_IDS,
  CULT_FULL_CAPACITY_ACTIVE_FROM_DATE
} from "../util/CultUtil"
import CenterView from "./CenterView"
import { ProductType } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { ActionUtil, CareUtil, SeoUrlParams } from "@curefit/base-utils"
import { User } from "@curefit/user-common"
import { CultBuddiesJoiningListSmallWidget, CultWaitlistExtensionWidget, CultWaitlistSlotInfo } from "@curefit/apps-common"
import { ICultBusiness } from "./CultBusiness"
import { inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { SimpleWod } from "@curefit/fitness-common"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { ISegmentService } from "@curefit/vm-models"
import { AddOnRecos, Address } from "@curefit/eat-common"
import { IMembershipService } from "@curefit/membership-client"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"

export interface CultMembershipDetails {
  centerName?: string
  cityName?: string
}

class CultClassDetailView {
  ppcPulsePack: PulseProduct

  async buildView(
    userContext: UserContext,
    cultClass: CultClass,
    wod: SimpleWod,
    productType: ProductType,
    user: User,
    cultMembershipDetails: CultMembershipDetails,
    isInternalUser: boolean,
    membershipService: IMembershipService,
    ppcPulsePack?: PulseProduct,
    buddiesJoining?: CultBuddiesJoiningListSmallWidget,
    cultBusiness?: ICultBusiness,
    rescheduleSourceBookingNumber?: string,
    isWaitlistConfirmationProbabilitySupported?: boolean,
    isWaitlistExtensionSupported?: boolean,
    segmentService?: ISegmentService,
    isUnpauseAndBookClassCtaSupported?: boolean,
  ): Promise<CultClassDetailView> {
    this.cultClass = cultClass
    this.changeBackgroundColorIndex = 100
    if (ppcPulsePack) {
      this.ppcPulsePack = ppcPulsePack
    }
    const classStartTime = TimeUtil.formatDateStringInTimeZone(
      cultClass.date + " " + cultClass.startTime,
      userContext.userProfile.timezone,
      "h:mm"
    )
    if (
      cultClass.cultAppAvailableSeats <= 0 &&
      !cultClass.isWaitlistAvailable
    ) {
      if (cultClass.isWaitlistFull) {
        this.message = ` Waitlist for ${cultClass.Workout.name} ${classStartTime} slot is currently full. Please choose another slot.`
      } else {
        this.message = `${cultClass.Workout.name} ${classStartTime} slot is no longer available. Please choose another slot.`
      }
    } else {
      let wodWidget
      if (!cultClass.hasDefaultWod) {
        wodWidget = CultUtil.getWodInfoWidget(wod, CultUtil.getWorkoutCaloriesInfo(cultClass.Workout))
      }
      this.isWodAvailable = !_.isEmpty(wodWidget)
      this.isFullScreen = true // show full screen page always
      this.widgets = []

      let centerBannerCarouselWidget
      centerBannerCarouselWidget = this.getSportsBannerCarouselWidget(cultClass)

      const isCenterBannerCarouselWidgetPresent: boolean = !_.isEmpty(centerBannerCarouselWidget)
      if (centerBannerCarouselWidget) {
        this.widgets.push(centerBannerCarouselWidget)
      }

      this.widgets.push(
        this.getHeaderWidget(
          cultClass,
          userContext,
          this.isWodAvailable,
          buddiesJoining,
          rescheduleSourceBookingNumber,
          isCenterBannerCarouselWidgetPresent
        )
      )

      if (buddiesJoining) {
        this.widgets.push(buddiesJoining)
      }
      const isPulseEnabled = CultUtil.isClassAvailableForPulse(
        cultClass,
        AppUtil.isCultPulseFeatureSupported(userContext)
      )
      if (isPulseEnabled) {
        this.widgets.push(this.getPulseKitRequirementWidget(cultClass, userContext))
      }
      /**
       * @todo: decide based on backend API if user needs to see this
       */
      if (isPulseEnabled) {
        this.widgets.push(this.getPulseComeEarlyNoteWidget())
        this.widgets.push(this.getPulseBenefitWidget(userContext))
      }

      let isWaitlistExtensionWidgetPresent: boolean = false
      if (
        CultUtil.isClassAvailableForWaitlist(cultClass)
      ) {
        this.widgets.push(this.getWaitlistWidget(cultClass, isWaitlistConfirmationProbabilitySupported, isWaitlistExtensionSupported, userContext.userProfile.timezone))

        // Code for waitlist extension experiment.
        if (
            isWaitlistExtensionSupported &&
            !_.isEmpty(cultClass?.wlNotificationSlots)
        ) {
          const currentTime: string = TimeUtil.todaysDate(userContext.userProfile.timezone, "YYYY-MM-DD HH:mm:ss")
          const classStartTimeFormatted: string = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime,
              cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).format()
          const timeRemainingToClass: number = TimeUtil.diffInMinutes(userContext.userProfile.timezone, currentTime, classStartTimeFormatted)
          const waitlistExtensionWidget = this.getWaitlistExtensionWidget(cultClass, userContext.userProfile.timezone, isWaitlistConfirmationProbabilitySupported)
          if (waitlistExtensionWidget) {
            isWaitlistExtensionWidgetPresent = true
            this.widgets.push(waitlistExtensionWidget)
          } else if (timeRemainingToClass < 60) {
            const leaveNotificationWidget = this.getLeaveNotificationWidget(cultClass, isWaitlistConfirmationProbabilitySupported)
            this.widgets.push(leaveNotificationWidget)
          }
        }
      }

      if (
        cultClass.membershipClassRejectionCause &&
        AppUtil.isMembershipContextSupported(userContext, user.isInternalUser)
      ) {
        const cultPackBrowseActionUrl = await AppActionUtil.getCultPackBrowseWidgetNavigationUrl(userContext, segmentService)
        const membershipContextWidget = await this.getMembershipContextWidget(
          cultClass,
          userContext,
          productType,
          cultMembershipDetails,
          isInternalUser,
          cultPackBrowseActionUrl,
          membershipService,
          isUnpauseAndBookClassCtaSupported
        )
        membershipContextWidget && this.widgets.push(membershipContextWidget)
      }
      if (!_.isEmpty(cultClass.Workout.preWorkoutGears)) {
        const workoutKitWidget = CultUtil.getWorkoutKitWidget(cultClass.Workout.preWorkoutGears, { paddingLeft: 20 }, cultClass.date, cultClass.Center.id)
        if (!_.isNil(workoutKitWidget)) {
          this.widgets.push(workoutKitWidget)
        }
      }

      if (CultUtil.isChildrensDayWorkout(cultClass.workoutID)) {
        this.widgets.push(this.getInstructionBannerCarouselWidget(cultClass))
      }

      if (CULT_FULL_CAPACITY_UPCOMING_CENTER_IDS.includes(Number(cultClass.centerID))) {
        if (cultClass.date >= CULT_FULL_CAPACITY_ACTIVE_FROM_DATE) {
          this.widgets.push(CultUtil.getNoteWidget(cultClass))
        }
      }
      else {
        this.widgets.push(CultUtil.getNoteWidget(cultClass))
      }
      if (AppUtil.isInstructionsWithMediaSupported(userContext)) {
        this.widgets.push(CultUtil.getInstructionsWithMediaWidget(cultClass))
      }
      this.widgets.push(CultUtil.getImportantUpdatesWidget(userContext,
          cultClass.Workout.isSportCategory,
          {
            marginLeft: 25,
            contentContainerCustomStyle: {
              marginLeft: 20
            }
          },
          {marginLeft: 25},
          {paddingLeft: 5},
          cultClass.Center,
          cultClass.date,
          CultUtil.isCultRunWorkout(cultClass.workoutID)
      ))
      // if (!_.isEmpty(wods)) {
      //   const widget = new CultWodWidget(wods, cultClass, userContext, true)
      //   if (widget.isWodPresent) {
      //     this.widgets.push(widget)
      //   }
      // }
      if (this.isWodAvailable && !CultUtil.isCultRunWorkout(cultClass.workoutID)) {
        this.widgets.push(wodWidget)
      }
      if (!_.isNil(cultClass.Center)) {
      if (cultClass.Center?.metadata?.parkingLocation) {
        this.widgets.push(
            this.getCenterInfoWidgetForCultRunParkingLocation(userContext, cultClass.Center, "FITNESS", "Parking location:")
        )
      }
      if (CultUtil.isCultRunWorkout(cultClass.workoutID)) {
        this.widgets.push(
            this.getCenterInfoWidgetForCultRunMeetupLocation(userContext, cultClass.Center, "FITNESS", "Meet-up location:")
        )
      }
      if (!CultUtil.isCultRunWorkout(cultClass.workoutID)) {
        this.widgets.push(
            this.getCenterInfoWidget(userContext, cultClass.Center, "FITNESS")
        )
      }
    }
      this.widgets = _.filter(this.widgets, widget => !_.isEmpty(widget))
      await this.buildPageActions(cultClass, userContext, cultBusiness, rescheduleSourceBookingNumber, productType,
          isWaitlistExtensionSupported && isWaitlistExtensionWidgetPresent,
          isUnpauseAndBookClassCtaSupported)
    }
    return this
  }

  /**
   *
   * @param {cultClass} CultClass detailed info of the class
   * Based on membership / PPC
   * Or, if user has trial / bulk / no access
   * Pulse kit requirements are determined
   */
  private getPulseKitRequirementWidget(
    cultClass: CultClass,
    userContext: UserContext
  ): ActionCardWidget | PulseRentalRemainingWidget {
    // check if member has pulse access
    // and its type
    let displayKnowMore: boolean
    if (
      cultClass.PulseContext &&
      !_.isEmpty(cultClass.PulseContext.UserAccess.type)
    ) {
      // user is PPC
      // A PPC user typically has "free" packs or "no_access" packs
      // but can also have "bulk" packs if membership expired
      if (CultUtil.isUserPPC(cultClass)) {
        switch (cultClass.PulseContext.UserAccess.type) {
          case "trial":
          // still hasn't used up all their trials
          // show know more
          case "no_access":
          // user would have to buy new rental
          case "bulk":
          // user was a member when they purchased rental
          // can use this
          default:
            displayKnowMore = true
        }
      } else {
        // user is member
        switch (cultClass.PulseContext.UserAccess.type) {
          case "bulk":
            // user needs to see remaining bulk access
            displayKnowMore = false
            break
          case "trial":
          // still hasn't used up all their trials
          // show know more
          case "no_access":
          // user would have to buy new rental
          default:
            displayKnowMore = true
        }
      }
    }
    if (displayKnowMore === true) {
      return {
        widgetType: "ACTION_CARD_WIDGET",
        displayText: "Requires PULSE Kit",
        icon: {
          iconType: "PULSE_DEVICE"
        },
        action: {
          title: "KNOW MORE",
          actionType: "SHOW_PULSE_KNOW_MORE"
          // url: "curefit://pulsefaq"
        },
        isDisabled: false,
        showDivider: true
      }
    }
    if (cultClass.PulseContext.UserAccess.isUnlimitedPack) {
      return {
        ...new PulseRentalRemainingWidget(
          "PULSE Kit Rental Pack",
          "Expires: ",
          momentTz.tz(
            cultClass.PulseContext.UserAccess.validity,
            userContext.userProfile.timezone
          ).format("Do MMM"),
          "rgba(0, 0, 0, 1.0)"
        ),
        showDivider: true
      }
    }
    return {
      ...new PulseRentalRemainingWidget(
        "PULSE Kit Rental Pack",
        "Remaining: ",
        cultClass.PulseContext.UserAccess.remainingAccess,
        "rgba(0, 0, 0, 1.0)"
      ),
      showDivider: true
    }
  }

  /**
   * @returns Description Note asking people to come 10 min early
   */
  private getPulseComeEarlyNoteWidget() {
    const info: InfoCard = {
      title: "NOTE",
      subTitle: `Reach ${CultUtil.getPulseClassReachEarlyCutoff()} minutes early to collect PULSE kit`
    }
    const descriptionWidget = {
      ...new DescriptionWidget([info]),
      iconType: "INFO",
      showDivider: true
    }
    return descriptionWidget
  }

  private getSportsBannerCarouselWidget(cultClass: CultClass): BannerCarouselWidget {
    if (!_.isEmpty(cultClass?.Center?.documents)) {
      const centerDocuments = _.sortBy(cultClass.Center.documents, (document) => { return  document.relativeOrder })

      const imageDocuments: CultDocument[] = _.filter(centerDocuments, (document) => {
        if (document.tagName === "PRODUCT_BNR") {
          return true
        }
        return false
      })

      const videoDocuments: CultDocument[] = _.filter(centerDocuments, (document) => {
        if (document.tagName === "M_VIDEO") {
          return true
        }
        return false
      })

      const videoThumbnails: CultDocument[] = _.filter(centerDocuments, (document) => {
        if (document.tagName === "M_VIDEO_THUMBNAIL") {
          return true
        }
        return false
      })

      const banners: Banner[] = []

      if (!_.isEmpty(videoDocuments)) {
        videoDocuments.map((videoDocument, index) => {
          const mediaUrl = CultUtil.getCultVideoCdnUrl(videoDocument.URL)
          const videoPlayerUrl = `curefit://videoplayer?absoluteVideoUrl=${encodeURIComponent(mediaUrl)}`

          banners.push({
            id: videoDocument.id,
            image: videoThumbnails[index]?.URL,
            action: {
              actionType: "PLAY_VIDEO",
              url: videoPlayerUrl,
            },
            // videoUrl: mediaUrl,
          })
        })
      }

      if (!_.isEmpty(imageDocuments)) {
        imageDocuments.map((imageDocument, index) => {
          banners.push({
            id: imageDocument.id,
            image: imageDocument.URL,
            action: undefined
          })
        })
      }

      return {
        ...new BannerCarouselWidget("375:269", banners, {
          bannerWidth: 375,
          bannerHeight: 202,
          verticalPadding: 0,
          edgeToEdge: true,
          noAutoPlay: false,
          v2: true,
          autoScroll: true,
          showPagination: true,
        }, 5, true)
      }
    }

    return null
  }

  private getInstructionBannerCarouselWidget(cultClass: CultClass): BannerCarouselWidget {
      const banners: Banner[] = []
      const imageURL = "image/vm/childrens_day_banner.png"
      banners.push({
        id: "1",
        image: imageURL,
        action: {
          actionType: "EMPTY_ACTION",
        },
      })
      return {
        ...new BannerCarouselWidget("375:269", banners, {
          bannerWidth: 375,
          bannerHeight: 202,
          verticalPadding: 0,
          edgeToEdge: true,
          noAutoPlay: false,
          v2: true,
          showPagination: false,
        }, 1, true)
      }
  }

  private getPulseBenefitWidget(userContext: UserContext): PulseBenefitWidget {
    return new PulseBenefitWidget("WHY PULSE", [
      {
        iconUrl: "/image/pulse/pulsebenefit13.png",
        text: "Track heart-rate & calories burnt in real-time",
        width: 31,
        height: 32
      },
      {
        iconUrl: "/image/pulse/pulsebenefit23.png",
        text: "Compete, push more & achieve better results",
        width: 36,
        height: 32
      },
      {
        iconUrl: "/image/pulse/pulsebenefit33.png",
        text: "Get detailed workout summary & track progress",
        width: 28,
        height: 32
      }
    ])
  }

  private getHeaderWidget(
    cultClass: CultClass,
    userContext: UserContext,
    isWodAvailable: boolean,
    buddiesJoining: CultBuddiesJoiningListSmallWidget | undefined,
    rescheduleSourceBookingNumber?: string,
    isCenterBannerCarouselWidgetPresent?: boolean
  ): PageWidget {
    const tz = userContext.userProfile.timezone
    const classStartTime = TimeUtil.formatDateStringInTimeZone(
      cultClass.date + " " + cultClass.startTime,
      tz,
      "h:mm A"
    )
    const classEndTime = TimeUtil.formatDateStringInTimeZone(
      cultClass.date + " " + cultClass.endTime,
      tz,
      "h:mm A"
    )
    const subTitle = `${TimeUtil.formatDateStringInTimeZone(
      cultClass.date,
      tz,
      "ddd D MMM"
    )}, ${classStartTime} - ${classEndTime}, ${cultClass.Center.name}`
    const headerWidget = new HeaderWidget(
      {
        title: CultUtil.pulsifyClassName(cultClass, userContext),
        subTitle,
        headerTopInfo: rescheduleSourceBookingNumber ? "Rescheduling To" : ""
      },
      true
    )
    headerWidget.hasBottomPadding = !cultClass.membershipClassRejectionCause
    headerWidget.hasTopPadding = true
    headerWidget.showDivider =
      CultUtil.isClassAvailableForWaitlist(cultClass) ||
      CultUtil.isClassAvailableForPulse(
        cultClass,
        AppUtil.isCultPulseFeatureSupported(userContext)
      )

    if (buddiesJoining) {
      headerWidget.hasBottomPadding = false
      headerWidget.hasDividerBelow = false
    }

    headerWidget.style = {
      paddingTop: 48,
    }

    if (isCenterBannerCarouselWidgetPresent) {
      headerWidget.widgetTitle.headerTopInfo = cultClass.Workout.isNewWorkout ? "NEW" : undefined
      headerWidget.headerTopInfoStyle = {
        marginLeft: 0,
        color: "#ffffff",
        fontFamily: "Inter-Bold",
        fontSize: 12,
        marginBottom: 0,
        paddingHorizontal: 5,
        paddingVertical: 2
      }
      headerWidget.headerTopInfoViewStyle = {
        backgroundColor: "#3888ff",
        flexDirection: "column",
        alignSelf: "flex-start",
        marginLeft: 20,
        borderRadius: 3
      }
      headerWidget.titleStyle = {
        fontSize: 18,
        fontFamily: "Inter-Regular",
        color: "#333747",
        lineHeight: 24,
        marginTop: 6,
      }
      headerWidget.subTitleStyle = {
        fontSize: 14,
        fontFamily: "Inter-Regular",
        color: "#333747",
        lineHeight: 20,
        marginTop: 4,
      }
      headerWidget.style = {
        paddingTop: 15,
        paddingBottom: 9
      }
      headerWidget.showDivider = true
    }

    return headerWidget
  }

  private getWaitlistWidget(
      cultClass: CultClass,
      isWaitlistConfirmationProbabilitySupported?: boolean,
      isWaitlistExtensionSupported?: boolean,
      timezone?: Timezone
  ): ActionCardWidget {
    const title =
      cultClass.waitlistedUserCount === 1
        ? "Person in Waitlist"
        : "People in Waitlist"

    const displayText = title

    const showWaitlistConfirmationProbability = cultClass.wlConfirmationThreshold && isWaitlistConfirmationProbabilitySupported
    let showWaitlistConfirmationProps = {}
    if (showWaitlistConfirmationProbability) {
      showWaitlistConfirmationProps = {
        title: title,
        displayText: "(Upto #" + cultClass.wlConfirmationThreshold + " usually gets confirmed)",
        displayTextStyle: {
          fontFamily: "BrandonText-Regular",
          fontSize: 11,
          height: 18,
          color: "#000000"
        },
        displayTextValue: "*You’ll be notified",
        displayTextValueStyle: {
          fontSize: 11,
          fontFamily: "BrandonText-Regular",
          marginTop: 12,
          color: "#000000",
          height: 16,
          flex: 1,
        },
        iconViewStyle: {
          paddingRight: 10,
        },
        titleTextStyle: {
          fontFamily: "BrandonText-Medium",
          fontWeight: "500",
          fontSize: 12
        },
        textContainerStyle: {
          alignItems: "flex-start",
          flexDirection: "column"
        },
        textValueEnd: " in advance",
        textValueMiddle: " at least 1 hour",
        textValueMiddleStyle: {
          fontFamily: "BrandonText-Bold",
          fontSize: 11
        },
        actionContainerStyle: {
          alignSelf: "flex-start",
        },
        imageTextContainerStyle: {
          alignItems: "flex-start",
        },
      }
    }

    let showWaitlistExtensionWidgetProps = {}
    if (isWaitlistExtensionSupported && showWaitlistConfirmationProbability) {
      const classStartTime: string = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime,
          cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).format()
      const currentTime: string = TimeUtil.todaysDate(timezone, "YYYY-MM-DD HH:mm:ss")
      const timeRemainingToClass: number = TimeUtil.diffInMinutes(timezone, currentTime, classStartTime)

      if (timeRemainingToClass > 60) {
        showWaitlistExtensionWidgetProps = {
          ...showWaitlistExtensionWidgetProps,
          displayTextValue: undefined,
          displayTextValueStyle: undefined,
          textValueEnd: undefined,
          textValueMiddle: undefined,
          textValueMiddleStyle: undefined,
        }
      } else {
        const wlNotificationSlots = cultClass.wlNotificationSlots
        wlNotificationSlots.sort()

        showWaitlistExtensionWidgetProps = {
          ...showWaitlistExtensionWidgetProps,
          textValueMiddle: ` at least ${wlNotificationSlots[0]} mins`,
        }
      }
    }

    return {
      widgetType: "ACTION_CARD_WIDGET",
      displayText: displayText,
      showDivider: true,
      meta: {
        number: cultClass.waitlistedUserCount
      },
      icon: {
        iconType: "NUMBER",
        bgColor: "#fadc4b",
        textColor: "#000000"
      },
      action: {
        actionType: "NAVIGATION",
        title: "KNOW MORE",
        url: "curefit://listpage?pageId=waitlisthiw"
      },
      analyticsData: {
        eventType: "WAITLIST_KNOW_MORE_CLICK"
      },
      ...showWaitlistConfirmationProps,
      ...showWaitlistExtensionWidgetProps,
    }
  }

  private getWaitlistExtensionWidget(
      cultClass: CultClass,
      timezone: Timezone,
      isWaitlistConfirmationProbabilitySupported: boolean,
  ): CultWaitlistExtensionWidget | ActionCardWidget {

    const showWaitlistConfirmationProbability = cultClass.wlConfirmationThreshold && isWaitlistConfirmationProbabilitySupported
    const classStartTime: string = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime,
        cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT).format()
    const { timeRemainingToClass, notificationTimeSlots } = getTimeSlotsForWaitlistExtensionWidget(cultClass, timezone, classStartTime)
    const title: string = "Choose your waitlist confirmation time:"
    const subTitle: string = "Note : The closer you are to the class starting time, the higher the chances of your waitlist getting confirmed"
    const classId: number = cultClass.id


    if (!_.isEmpty(notificationTimeSlots) && cultClass?.wlNotificationTime && timeRemainingToClass > 60) {
      const notificationSlots: CultWaitlistSlotInfo[] = notificationTimeSlots.map((time) => {
        return {
          title: `${time} mins before the class`,
          time: time,
          value: cultClass?.wlNotificationTime === time,
          isRecommended: CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME === time
        }
      })

      let showWaitlistConfirmationProbabilityProps = {}
      if (showWaitlistConfirmationProbability) {
        showWaitlistConfirmationProbabilityProps = {
          iconViewStyle: {
            paddingRight: 10,
            marginRight: 0
          }
        }
      }

      return {
        widgetType: "ACTION_CARD_WIDGET",
        displayTextValue: "Your waitlist will be confirmed",
        textValueMiddle: ` ${cultClass.wlNotificationTime} mins `,
        textValueEnd: "prior to your class",
        textValueMiddleStyle: {
          fontFamily: "BrandonText-Bold",
          fontSize: 11
        },
        action: {
          actionType: "SHOW_CULT_WAITLIST_NOTIFICATION_MODAL",
          title: "EDIT SLOT",
          meta: {
            notificationSlots: notificationSlots,
            modalTitle: "Choose your waitlist confirmation time:",
            modalSubtitle: "Note : The closer you are to the class starting time, the higher the chances of your waitlist getting confirmed",
            ctaTitle: "Save changes",
            classId: classId,
            wlNotificationTime: cultClass.wlNotificationTime
          },
          shouldRefreshPage: true
        },
        analyticsData: {
          eventKey: "widget_click",
          eventData: {
            type: "waitlist_extension_modal",
          }
        },
        textContainerStyle: {
          alignItems: "flex-start",
          flexDirection: "column",
        },
        actionContainerStyle: {
          alignSelf: "flex-start",
          marginLeft: 40,
        },
        showDivider: true,
        displayTextValueStyle: {
          fontSize: 11,
          fontFamily: "BrandonText-Regular",
          color: "#000000",
          flex: 1,
          flexDirection: "row",
          flexShrink: 1
        },
        icon: {
          iconType: "IMAGE_URL",
          url: "/image/icons/cult/waitlist_time.png"
        },
        wlNotificationTime: cultClass.wlNotificationTime,
        ...showWaitlistConfirmationProbabilityProps
      }
    } else if (!_.isEmpty(notificationTimeSlots) && timeRemainingToClass > 60 && !cultClass.wlNotificationTime) {
      const onboardingSlots = notificationTimeSlots.map((time) => {
        return {
          title: `${time} mins before the class`,
          time: time,
          value: false,
          isRecommended: CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME === time
        }
      })

      return {
        widgetType: "CULT_WAITLIST_EXTENSION_WIDGET",
        onboardingTitle: title,
        onboardingSubtitle: subTitle,
        showDivider: true,
        tagName: "NEW",
        classId: cultClass.id,
        onboardingSlots: onboardingSlots
      }
    }
    return null
  }

  private getLeaveNotificationWidget(cultClass: CultClass, isWaitlistConfirmationProbabilitySupported: boolean): ActionCardWidget {
    const showWaitlistConfirmationProbability = cultClass.wlConfirmationThreshold && isWaitlistConfirmationProbabilitySupported
    let showWaitlistConfirmationProbabilityProps = {}
    if (showWaitlistConfirmationProbability) {
      showWaitlistConfirmationProbabilityProps = {
        iconViewStyle: {
          paddingRight: 10,
          marginRight: 0
        }
      }
    }

    const wlNotificationTimeSlots = cultClass.wlNotificationSlots
    wlNotificationTimeSlots.sort()

    return {
      widgetType: "ACTION_CARD_WIDGET",
      icon: {
        iconType: "CANCEL",
        gradientColors: ["#47ebc6", "#49f6be"]
      },
      displayTextValue: "Option to leave or dropout would not be available, as the booking was done within one hour of class start",
      displayTextValueStyle: {
        fontSize: 11,
        fontFamily: "BrandonText-Regular",
        color: "#393939",
        flex: 1,
        flexDirection: "row",
        flexShrink: 1
      },
      analyticsData: {
        eventData: {
          type: "waitlist_extension_leave_widget",
        }
      },
      showDivider: true,
      wlNotificationTime: wlNotificationTimeSlots[0],
      ...showWaitlistConfirmationProbabilityProps
    }
  }

  private getCenterInfoWidget(
    userContext: UserContext,
    cultCenter: CultCenter,
    productType: ProductType
  ): CenterView & WidgetView {
    const centerView: CenterView = new CenterView(
      userContext,
      cultCenter,
      productType
    )
    const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
    centerView.showIcon = false
    return Object.assign(widgetView, centerView)
  }

  private getCenterViewForCultRun(userContext: UserContext,
                        cultCenter: CultCenter,
                        productType: ProductType): CenterView {
    const address: Address = {
      addressString: cultCenter?.metadata?.parkingLocation?.address,
      latLong: {
        lat: cultCenter?.Address?.latitude,
        long: cultCenter?.Address?.longitude
      },
      mapUrl: cultCenter?.metadata?.parkingLocation?.navigation,
      pincode: cultCenter?.metadata?.parkingLocation?.pincode,
    }
    const userAgent = userContext.sessionInfo.userAgent
    const seoParams: SeoUrlParams = {
      locality: cultCenter?.locality,
      productName: cultCenter?.name,
    }
    const  centerView: CenterView = {
      id: cultCenter?.id,
      name: cultCenter?.name,
      action:
          productType === "FITNESS"
              ? ActionUtil.cultCenter(
              cultCenter?.id?.toString(),
              undefined,
              userAgent,
              seoParams
              )
              : ActionUtil.mindCenter(
              cultCenter?.id?.toString(),
              undefined,
              userAgent,
              seoParams
              ),
      address: address,
      distance: "",
      mapUrl: cultCenter?.metadata?.parkingLocation?.navigation,
      showIcon: false,
    }
    return centerView
  }

  private getCenterInfoWidgetForCultRunParkingLocation(
      userContext: UserContext,
      cultCenter: CultCenter,
      productType: ProductType,
      title: string,
  ): CenterView & WidgetView & Header {

    const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
    const header: Header = { title: title }
    const centerView = this.getCenterViewForCultRun(userContext, cultCenter, productType)
    return Object.assign(widgetView, centerView, header)
  }

  private getCenterInfoWidgetForCultRunMeetupLocation(
      userContext: UserContext,
      cultCenter: CultCenter,
      productType: ProductType,
      title: string
  ): CenterView & WidgetView & Header {
    const centerView: CenterView = new CenterView(
        userContext,
        cultCenter,
        productType
    )
    const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
    centerView.showIcon = false
    const header: Header = {title: title}
    return Object.assign(widgetView, centerView, header)
  }

  private async getMembershipContextWidget(
    cultClass: CultClass,
    userContext: UserContext,
    productType: ProductType,
    cultMembershipDetails: CultMembershipDetails,
    isInternalUser: boolean,
    cultPackBrowseActionUrl: string,
    membershipService: IMembershipService,
    isUnpauseAndBookClassCtaSupported?: boolean,
  ): Promise<MembershipContextWidget> {
    let title = "",
      description = "",
      action: Action = null
    const tz = userContext.userProfile.timezone
    let categoryType = ""
    switch (cultClass.membershipClassRejectionCause) {
      case "PAUSE": {
        const descriptionSecondLine = isUnpauseAndBookClassCtaSupported ? "Unpause your pack to book a class"
            : "You can choose to pay for this class or unpause your pack to book it for free"

        title = "Membership paused"
        description = `Your membership is paused till ${momentTz
          .tz(cultClass.membership.ActivePause.maxEndDate, tz)
          .format(
            "DD MMM YYYY"
          )}. ${descriptionSecondLine}`
        const packUnpauseAction = CultUtil.getMembershipUnpauseAction(
          cultClass.membership,
          productType,
          userContext,
          isInternalUser
        )
        action = !isUnpauseAndBookClassCtaSupported ? {
          ...packUnpauseAction,
          title: "UNPAUSE"
        } : undefined
        categoryType = "PAUSE"
        break
      }
      case "SELECT": {
        const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, cultClass.membership.id.toString(), membershipService)
        title = "You have limited access"
        description = `Your membership has access to only ${cultMembershipDetails.centerName}. To book this class for free, switch to the schedule of your preferred centre`
        if (cultClass.membership.state === "ADVANCE_PAID") {
          action = {
            actionType: "NAVIGATION",
            title: "Upgrade",
            url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
          }
        }
        categoryType = "LIMITED_ACCESS"
        break
      }
      case "EXPIRED": {
        title = "Membership expired"
        description =
          "Your membership has expired. You can choose to pay for this class or renew your pack to book unlimited classes for free"
        action = CultUtil.getBuyCultPassEliteAction("RENEW")
        categoryType = "MEMBERSHIP_EXPIRED"
        break
      }
      case "OUTSTATION_EXHAUSTED": {
        title = "Free outstation classes used"
        description = `Your membership gives you access to only 5 classes in cities apart from ${cultMembershipDetails.cityName}. You would need to pay to book this class.`
        categoryType = "OUTSTATION_EXHAUSTED"
        break
      }

      case "ELITE_TICKETS_USED": {
        const proMembership = await CultUtil.getActiveProMembership(userContext, membershipService)
        const cultBenefit = proMembership && !_.isEmpty(proMembership) ? proMembership.benefits.find(benefit => benefit.name === "CULT") : null
        if (cultBenefit == null || cultBenefit == undefined) {
          break
        }
        title = "No free ELITE sessions left"
        description = `You have consumed all ${cultBenefit.maxTickets} ELITE sessions this month. You’ll get ${cultBenefit.maxTickets} more sessions next month if your PRO membership is still active.`
        categoryType = "ELITE_TICKETS_USED"
        break
      }

      case "ALL_TRIALS_USED": {
        const productName = productType === "FITNESS" ? "Cult" : "Mind"
        title = "All free trials used "
        description = `You have used all your free trial classes. To book this session for free, kindly upgrade to ${productName} Unlimited pack`
        action = CultUtil.getBuyCultPassEliteAction("BUY CULTPASS")
        categoryType = "TRIALS_EXHAUSTED"
        break
      }

      case "DEVICE_LEVEL_ALL_TRIALS_USED": {
        const productName = productType === "FITNESS" ? "Cult" : "Mind"
        title = "All free trials used "
        description = "This device has already been used to use all free trial classes. To book this session for free, kindly purchase a cultpass Elite pack."
        action = CultUtil.getBuyCultPassEliteAction("BUY CULTPASS")
        categoryType = "TRIALS_EXHAUSTED"
        break
      }

      case "NO_MEMBERSHIP": {
        break
      }

      case "UPCOMING": {
        const startDate = cultClass.membership.startDate
          ? momentTz.tz(cultClass.membership.startDate, userContext.userProfile.timezone).format("DD MMM YYYY")
          : ""
        title = "Upcoming membership"
        description = `Your membership starts on ${startDate}. You can choose to pay for this session or change start date of your pack to book it for free`
        categoryType = "UPCOMING"
        break
      }

      case "SWIMMING_PPC":
        title = "Why am I being charged?"
        description = "We on-board only the best-in-class swimming facilities to ensure a great workout experience. Swimming sessions are available at a discounted price only for Cult members."
        categoryType = "SWIMMING_PPC"
        break
    }
    if (title && description) {
      const analyticsData = {
        eventName: "MEMBERSHIP_CONTEXT_VIEWED",
        meta: {
          category: categoryType
        }
      }
      const analyticsDataOnClick = {
        eventName: "MEMBERSHIP_CONTEXT_CLICKED",
        meta: {
          category: categoryType
        }
      }
      const widget: MembershipContextWidget = {
        widgetType: "INFO_ACTION_ACCORDION_WIDGET",
        title: title,
        description: description,
        analyticsData: analyticsData,
        analyticsDataOnClick: analyticsDataOnClick
      }
      if (action) {
        widget["action"] = action
      }
      return widget
    }
    return null
  }

  private async buildPageActions(
    cultClass: CultClass,
    userContext: UserContext,
    cultBusiness: ICultBusiness,
    rescheduleSourceBookingNumber?: string,
    productType?: ProductType,
    isWaitlistExtensionSupported?: boolean,
    isUnpauseAndBookClassCtaSupported?: boolean,
  ) {
    this.actions = []
    const showWaitlistExtensionOnboarding: boolean = (isWaitlistExtensionSupported && !cultClass?.wlNotificationTime)

    let pageAction: Action
    const { cult, mind } = await cultBusiness.isBookFreeCultClassSupported(userContext)
    if (isUnpauseAndBookClassCtaSupported && cultClass.membershipClassRejectionCause && cultClass.membershipClassRejectionCause === "PAUSE") {
      const isUpcomingPause: boolean = cultClass.membership?.state !== "PAUSED" && CultUtil.isUpcomingPause(cultClass.membership)
      const isWaitlistClass: boolean = CultUtil.isClassAvailableForWaitlist(cultClass)

      if (isWaitlistClass && await CultUtil.isWaitlistUnpauseBookSupported(userContext)) {
        pageAction = {
          actionType: "CULT_UNPAUSE_AND_JOIN_WAITLIST",
          title: "UNPAUSE & JOIN WAITLIST",
          meta: {
            membershipId: cultClass?.membership?.id.toString(),
            productType: productType,
            toastMessage: "Membership unpaused successfully",
            isUpcomingPause: isUpcomingPause
          }
        }
      } else if (!isWaitlistClass) {
        pageAction = {
          actionType: "CULT_RESUME_MEMBERSHIP_BOOK_CLASS",
          title: "UNPAUSE AND BOOK CLASS",
          meta: {
            membershipId: cultClass?.membership?.id.toString(),
            productType: productType,
            toastMessage: "Membership unpaused successfully",
            isUpcomingPause: isUpcomingPause
          }
        }
      } else if (isWaitlistClass) {
        // Condition added for backward compatibility with previous app versions for waitlist unpause and book.
        pageAction = {
          actionType: "CULT_JOIN_WAITLIST",
          title: "JOIN WAITLIST",
        }
      }
    } else if (
      CultUtil.isClassAvailableForWaitlist(cultClass)
    ) {
      pageAction = {
        actionType: "CULT_JOIN_WAITLIST",
        title: "JOIN WAITLIST",
      }
    } else if (cultClass.amount > 0) {
      const currency: string = _.get(cultClass, "currency", "")
      pageAction = {
        actionType: "CULT_BUY_CLASS",
        title: _.isString(currency)
          ? `Book for ${currency} ${cultClass.amount}`
          : `Book for ${cultClass.amount}`
      }
    } else {
      pageAction = {
        actionType: "CULT_BOOK_CLASS",
        title: (cult || mind) ? CULT_FREE_CLASS_BOOK : "BOOK NOW"
      }
    }
    if (pageAction) {
      pageAction.meta = {
        ...pageAction.meta,
        rescheduleSourceBookingNumber: rescheduleSourceBookingNumber
      }
    }
    if (
      Number(cultClass.centerID) === 157 &&
      (cultClass.workoutID === 153 || cultClass.workoutID === 154) &&
      CultUtil.isCultInstructionModalSupported(userContext)
    ) {
      this.actions.push(
        CareUtil.getInstructionModalAction(
          userContext,
          pageAction,
          pageAction.title,
          CultUtil.getInstructionForClass(cultClass.workoutID),
          "BACK"
        )
      )
    } else if (
        (cultClass.workoutID === 308) &&
        CultUtil.isCultInstructionModalSupported(userContext)
    ) {
      pageAction.meta = {
        ...pageAction.meta,
        cultClass: cultClass,
        slot: {
          id: cultClass.id.toString(),
          productType: productType,
          startTime: cultClass.startTime,
          workoutName: cultClass.Workout.name,
          classTime: cultClass.startTime,
        },
      }

      this.actions.push(
          CareUtil.getInstructionModalAction(
            userContext,
            pageAction,
            pageAction.title,
            CultUtil.getInstructionForClass(cultClass.workoutID, cultClass.centerID),
            "BACK"
          )
      )
    } else if (pageAction.actionType === "CULT_JOIN_WAITLIST" && showWaitlistExtensionOnboarding) {
      const waitlistExtensionAction: Action = {
        actionType: "UPDATE_CULT_WAITLIST_EXTENSION_WIDGET",
        title: "CONFIRM SLOT & JOIN WAITLIST",
        meta: {
          action: pageAction,
          changeBgColor: true,
        }
      }

      this.actions.push(waitlistExtensionAction)
    }
    else {
      this.actions.push(pageAction)
    }
  }

  public cultClass: CultClass
  public message?: string
  public isWodAvailable?: boolean
  public widgets: (WidgetView | PageWidget)[]
  public actions?: Action[]
  public changeBackgroundColorIndex: number
  public isFullScreen?: boolean
}

export default CultClassDetailView
