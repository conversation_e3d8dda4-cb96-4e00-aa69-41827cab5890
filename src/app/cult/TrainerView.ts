import { CultTrainer } from "@curefit/cult-common"
import { ActionUtil } from "@curefit/base-utils"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import CultUtil from "../util/CultUtil"

class TrainerView {
  constructor(trainer: CultTrainer, userAgent: UserAgent, isCLP: boolean) {
    const description = CultUtil.getCultTrainerDescription(trainer.description)
    this.id = trainer.id
    this.name = trainer.name
    this.description = description
    this.image = trainer.imageURL ? "/" + trainer.imageURL : undefined
    this.action = ActionUtil.trainerInfoCard(
      trainer.name,
      "",
      description,
      this.image
    )
  }

  public id: string
  public name: string
  public description: string
  public image: string
  public action: string
}

export default TrainerView
