import * as express from "express"
import { controller, httpGet, httpPost, httpPut } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import LocationUtil from "../util/LocationUtil"
import {
    CallReminderSlotInfo,
    CreateOgmRequest,
    CultBooking,
    CultBookingResponse,
    CultCenter,
    CultClass,
    CultClassesResponse, CultClassesResponseV2,
    CultMembership,
    CultPack,
    CultUserPreference,
    CultWorkoutCategory,
    OgmDetail,
} from "@curefit/cult-common"
import {
    CULT_CLIENT_TYPES,
    CULT_CUSTOMER_TYPE,
    ICultService as ICultServiceNew,
    ICultServiceOld as ICultService,
    IPulseService,
    SquadClassBookingLite
} from "@curefit/cult-client"
import CenterView from "./CenterView"
import TrainerView from "./TrainerView"
import { IClassListViewBuilder } from "./ClassListViewBuilder"
import ClassListView from "./ClassListView"
import { FoodPack } from "@curefit/eat-common"
import { ProductType, ProgramPackProduct } from "@curefit/product-common"
import { Session, UserContext } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Feedback } from "@curefit/feedback-common"
import { ALFRED_CLIENT_TYPES, IFulfilmentService, IShipmentService } from "@curefit/alfred-client"
import BookingDetailViewBuilder from "./BookingDetailViewBuilder"
import ICRMIssueService from "../crm/ICRMIssueService"
import { ActionUtil, ISessionBusiness as ISessionService, MealUtil } from "@curefit/base-utils"
import BaseOrderConfirmationViewBuilder, {
    ConfirmationRequestParams,
    ConfirmationView
} from "../order/BaseOrderConfirmationViewBuilder"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import CultUtil, {
    CenterServiceSKUs, CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_PROD, CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_STAGE,
    CultInviteLazyPostBody,
    KICKSTART_WORKOUT_ID,
    RUNNING_EVENT_WORKOUT_ID,
    PILATES_WORKOUT_IDS
} from "../util/CultUtil"
import { FEEDBACK_MONGO_TYPES, IFeedbackReadOnlyDao } from "@curefit/feedback-mongo"
import {
    CATALOG_CLIENT_TYPES,
    CatalogueServiceV2Utilities,
    ICatalogueService,
    ICatalogueServicePMS
} from "@curefit/catalog-client"
import {
    Action,
    ActionPageWidgetView,
    AddTransferMemberDetails,
    AlertInfo,
    CultBuddiesJoiningListSmallView,
    CultWorkoutDetailPage,
    CultWorkoutPage,
    LPTGoalPage,
    ProductDetailPage,
    ProductListWidget,
    SGTOneStepBookingPage,
    WidgetView
} from "../common/views/WidgetView"
import { BASE_TYPES, CLSUtil, FetchUtilV2, Logger } from "@curefit/base"
import CenterDetailView from "./CenterDetailView"
import { WorkoutCategoryDetailView, WorkoutDetailView } from "./WorkoutDetailView"
import CultOfferDetailViewBuilder, { CenterWiseSlot, CultOfferProductDetailPage } from "./CultOfferDetailViewBuilder"
import { Banner } from "../page/PageWidgets"
import { Header, IBannerService, ISegmentService } from "@curefit/vm-models"
import { OrderSource } from "@curefit/order-common"
import AppUtil, { CITY_SPLIT_ENABLED_CITY_IDS } from "../util/AppUtil"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"

import { CenterNotSelectedError } from "../common/errors/CenterNotSelectedError"
import { IProgramBusiness } from "../program/IProgramBusiness"
import OrderUtil from "../util/OrderUtil"
import * as _ from "lodash"
import { IOfferServiceV2, OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { SimpleWod } from "@curefit/fitness-common"
import WodViewBuilder, { IWODDetail } from "./WodViewBuilder"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { CenterDetails, ICultBusiness, NearByCentersInfo, Preference, PreferenceDetail } from "./CultBusiness"
import { CacheHelper } from "../util/CacheHelper"
import ClassListViewBuilderV2, { ClassScheduleResponse } from "./ClassListViewBuilderV2"
import { OfferIdOfferInventoryMap, PackOfferType } from "@curefit/offer-common"
import IssueBusiness from "../crm/IssueBusiness"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import CultClassDetailView, { CultMembershipDetails } from "./CultClassDetailView"
import BookingDetailViewBuilderV2 from "./BookingDetailViewBuilderV2"
import { DerivedCityDetails } from "../pack/CultPackDetailViewBuilder"
import TransferMembershipViewBuilder from "./TransferMembershipViewBuilder"
import UpgradeMembershipViewBuilder from "./UpgradeMembershipViewBuilder"
import MembershipAuditTrailViewBuilder from "./MembershipAuditTrailViewBuilder"
import LiveClassDetailViewBuilder, { LiveClassDetailView } from "./LiveClassDetailViewBuilder"
import LiveClassDetailViewBuilderV2, { LiveClassDetailViewV2 } from "./LiveClassDetailViewBuilderV2"
import { OTP_MEDIUM } from "../user/UserController"
import { eternalPromise, LogUtil, TimeUtil } from "@curefit/util-common"

import NuxViewBuilder from "./NuxViewBuilder"
import PtAtHomeViewBuilder from "./ptathome/PtAtHomeViewBuilder"
import { KiosksDemandService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FitnessReportPageView, ReportType } from "./fitnessreport/FitnessReportPageView"
import FitnessReportPageViewBuilder from "./fitnessreport/FitnessReportPageViewBuilder"
import { SignedUrlResponse } from "@curefit/user-client"
import IUserBusiness from "../user/IUserBusiness"
import CultMemoriesViewBuilder, { CultMemoriesResponse } from "./viewbuilder/CultMemoriesViewBuilder"
import CultCenterPageViewBuilder from "./viewbuilder/CultCenterPageViewBuilder"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { CultWorkoutsViewBuilder } from "./CultWorkoutsViewBuilder"
import { CultWorkoutDetailViewBuilder } from "./CultWorkoutDetailViewBuilder"
import CultUserProfileViewBuilder from "./cultSocial/CultUserProfileViewBuilder"
import CultMomentsViewBuilder from "./cultSocial/CultMomentsViewBuilder"
import { CultBuddiesJoiningListSmallWidget, PageTypes, SocialMemoriesListPage, User } from "@curefit/apps-common"
import { UserProfileEntry } from "@curefit/social-common"
import { Tenant } from "@curefit/user-common"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import LivePtGoalPageViewBuilder from "./ptathome/LivePtGoalPageViewBuilder"
import { ICareBusiness } from "../care/CareBusiness"
import LivePTReportPageViewBuilder from "./ptathome/LivePTReportPageViewBuilder"
import AuthUtil from "../util/AuthUtil"
import { DOCTOR_TYPE } from "@curefit/care-common"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import SGTOneStepBookingPageViewBuilder from "./SGTOneStepBookingPageViewBuilder"
import {
    IMediaGatewayService,
    MEDIA_GATEWAY_CLIENT_TYPES,
    MediaType,
    ObjectAcl
} from "@curefit/media-gateway-js-client"
import { MediaType as MediaTypeMediaGateway } from "@curefit/media-gateway-js-client/dist/src/MediaGatewayService"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import CenterViewV2 from "./CenterViewV2"
import { CenterStatus, CenterVertical } from "@curefit/center-service-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CircuitBreakerProfileTypes, CircuitBreakerUtil, HOFUND_TYPES } from "@curefit/hofund"
import CultClassDetailViewV2 from "./CultClassDetailViewV2"
import { IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import UserUtil, { LocationDataKey } from "../util/UserUtil"
import { HeadersInit, RequestInit, Response } from "node-fetch"
import CultSchedulePageConfig from "./CultSchedulePageConfig"
import { PromiseCache } from "../util/VMUtil"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { MultiDeviceHandlingMiddleware } from "../middleware/MultiDeviceHandlingMiddleware"
import { ApiErrorCountMiddleware } from "../auth/ApiErrorCountMiddleware"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import GymfitUtil from "../util/GymfitUtil"
import { CenterBookingCache } from "./CenterBookingCache"
import * as moment from "moment"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import CultClassUtil from "../util/CultClassUtil"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"
import CircuitBreaker = require("opossum")
import { HamletConfigRequest } from "@curefit/hamlet-common"
import CFAPIJavaService from "../CFAPIJavaService"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"

export function controllerFactory(kernel: Container) {
    @controller("/cult",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class CultController {
        private redisVMCrudDao: ICrudKeyValue
        constructor(
            @inject(REDIS_TYPES.RedisDao) private crudDao: ICrudKeyValue,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
            @inject(CULT_CLIENT_TYPES.ICultService) protected cultService: ICultServiceNew,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private interfaces: CFServiceInterfaces,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
            @inject(CULT_CLIENT_TYPES.PulseService) private pulseService: IPulseService,
            @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService,
            @inject(CUREFIT_API_TYPES.BookingDetailViewBuilder) private bookingDetailViewBuilder: BookingDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.BookingDetailViewBuilderV2) private bookingDetailViewBuilderV2: BookingDetailViewBuilderV2,
            @inject(CUREFIT_API_TYPES.WodViewBuilder) private wodViewBuilder: WodViewBuilder,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
            @inject(ALFRED_CLIENT_TYPES.ShipmentService) private shipmentService: IShipmentService,
            @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
            @inject(CUREFIT_API_TYPES.ClassListViewBuilder) private classListViewBuilder: IClassListViewBuilder,
            @inject(CUREFIT_API_TYPES.ClassListViewBuilderV1) private classListViewBuilderV1: IClassListViewBuilder,
            @inject(CUREFIT_API_TYPES.ClassListViewBuilderV2) private classListViewBuilderV2: ClassListViewBuilderV2,
            @inject(CUREFIT_API_TYPES.CRMIssueService) private CRMIssueService: ICRMIssueService,
            @inject(FEEDBACK_MONGO_TYPES.FeedbackReadOnlyDao) private feedbackDao: IFeedbackReadOnlyDao,
            @inject(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache) private feedbackPageConfigV2Cache: FeedbackPageConfigV2Cache,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilder) private orderConfirmationViewBuilder: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: BaseOrderConfirmationViewBuilder,
            @inject(CUREFIT_API_TYPES.BannerService) protected bannerService: IBannerService,
            @inject(CUREFIT_API_TYPES.CultOfferDetailViewBuilder) private cultOfferDetailViewBuilder: CultOfferDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.ProgramBusiness) private programBusiness: IProgramBusiness,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) private offerServiceV2: IOfferServiceV2,
            @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
            @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
            @inject(ALFRED_CLIENT_TYPES.FulfilmentService) private fulfilmentService: IFulfilmentService,
            @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.TransferMembershipViewBuilder) private transferMembershipViewBuilder: TransferMembershipViewBuilder,
            @inject(CUREFIT_API_TYPES.UpgradeMembershipViewBuilder) private upgradeMembershipViewBuilder: UpgradeMembershipViewBuilder,
            @inject(CUREFIT_API_TYPES.MembershipAuditTrailViewBuilder) private membershipAuditTrailViewBuilder: MembershipAuditTrailViewBuilder,
            @inject(CUREFIT_API_TYPES.LiveClassDetailViewBuilder) private liveClassDetailViewBuilder: LiveClassDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.LiveClassDetailViewBuilderV2) private liveClassDetailViewBuilderV2: LiveClassDetailViewBuilderV2,
            @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) private kiosksDemandService: KiosksDemandService,
            @inject(CUREFIT_API_TYPES.NuxViewBuilder) private nuxViewBuilder: NuxViewBuilder,
            @inject(CUREFIT_API_TYPES.PtAtHomeViewBuilder) private ptathomeViewBuilder: PtAtHomeViewBuilder,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.FitnessReportPageViewBuilder) private fitnessReportPageViewBuilder: FitnessReportPageViewBuilder,
            @inject(CUREFIT_API_TYPES.CultMemoriesViewBuilder) private cultMemoriesViewBuilder: CultMemoriesViewBuilder,
            @inject(LOCATION_SERVICE_TYPES.LocationService) private locationService: ILocationService,
            @inject(CUREFIT_API_TYPES.CultCenterPageViewBuilder) private cultCenterPageViewBuilder: CultCenterPageViewBuilder,
            @inject(CUREFIT_API_TYPES.CultWorkoutsViewBuilder) private cultWorkoutsViewBuilder: CultWorkoutsViewBuilder,
            @inject(CUREFIT_API_TYPES.CultWorkoutDetailViewBuilder) private cultWorkoutDetailViewBuilder: CultWorkoutDetailViewBuilder,
            @inject(CUREFIT_API_TYPES.CultUserProfileViewBuilder) private cultUserProfileViewBuilder: CultUserProfileViewBuilder,
            @inject(CUREFIT_API_TYPES.CultMomentsViewBuilder) private cultMomentsViewBuilder: CultMomentsViewBuilder,
            @inject(CUREFIT_API_TYPES.LivePtGoalPageViewBuilder) private livePtGoalPageViewBuilder: LivePtGoalPageViewBuilder,
            @inject(CUREFIT_API_TYPES.LivePTReportPageViewBuilder) private livePTReportPageViewBuilder: LivePTReportPageViewBuilder,
            @inject(CUREFIT_API_TYPES.SGTOneStepBookingPageViewBuilder) private sgtOneStepBookingPageViewBuilder: SGTOneStepBookingPageViewBuilder,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
            @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) protected classInviteLinkCreator: ClassInviteLinkCreator,
            @inject(CUREFIT_API_TYPES.CultSchedulePageConfig) private cultSchedulePageConfig: CultSchedulePageConfig,
            @inject(MEDIA_GATEWAY_CLIENT_TYPES.IMediaGatewayService) private mediaGatewayClient: IMediaGatewayService,
            @inject(CUREFIT_API_TYPES.SegmentService) protected segmentService: ISegmentService,
            @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
            @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
            @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
            @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
            @inject(HOFUND_TYPES.CircuitBreakerUtil) private circuitBreakerUtil: CircuitBreakerUtil,
            @inject(BASE_TYPES.FetchUtilV2) private fetchUtilV2: FetchUtilV2,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
            @inject(CUREFIT_API_TYPES.CenterBookingCache) public centerBookingCache: CenterBookingCache,
            @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
            @inject(LOGGING_CLIENT_TYPES.LoggingService) private loggingService: ILoggingService,
            @inject(CUREFIT_API_TYPES.CultClassUtil) private cultClassUtil: CultClassUtil,
            @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
            @inject(CUREFIT_API_TYPES.CFAPIJavaService) private cFAPIJavaService: CFAPIJavaService,
            @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStoreService: IAppConfigStoreService
        ) {
            this.redisVMCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("VM-CACHE")
        }

        @httpGet("/center")
        public async getFitnessCenters(req: express.Request): Promise<CenterDetails | CenterView[]> {
            const session: Session = req.session
            const cityId: string = req.query.cityId || session.sessionData.cityId
            const osName: string = req.headers["osname"] as string
            const userContext: UserContext = req.userContext as UserContext
            const appVersion: number = Number(req.headers["appversion"])
            let productId: string = req.query.productId as string
            const ageCategory = req.query.ageCategory
            let productType = req.query.productType ?? "FITNESS"
            const packId = req.query.packId
            const centerIds: string[] = req.query.centerIds ? req.query.centerIds.split(",") : []
            const viewOnly: boolean = req.query.viewOnly === "true"
            const useCenterServiceId = req.query.useCenterServiceId
            const pageFrom = req.query?.pageFrom
            const flow = req.query?.flow
            const isCultUnbound = req.query?.isCultUnbound as boolean
            const isPilateGymPage = req.query?.isPilateGymPage as boolean
            this.logger.info("Request Query for center " + JSON.stringify(req.query))
            if (!_.isEmpty(centerIds)) {
                const centerMap = await this.cultBusiness.getAllCentersCached()
                const cultCenters = centerIds.map(centerId => {
                    const centerView = new CenterView(userContext, centerMap[Number(centerId)], productType ?? "FITNESS")
                    if (viewOnly) {
                        centerView.action = undefined
                    }
                    return centerView
                })
                const centerDetailView: CenterDetails = {
                    centers: cultCenters,
                }
                if (AppUtil.isNewCenterResponseSupported(appVersion, osName)) {
                    return centerDetailView
                } else {
                    return centerDetailView.centers
                }
            }

            if (_.isEmpty(productId) && !_.isNil(packId)) {
                this.logger.error(`PMS::DEPR /cult/centers:: Not sending productId for packId - ${packId}`, {pageFrom, appVersion, osName})
                productId = `CULTPACK${packId}`
            }
            if ((productType != null && productType === "GYMFIT_FITNESS_PRODUCT") || productId?.startsWith("GYMFIT")) {
                productType = "GYMFIT_FITNESS_PRODUCT"
            }

            // if (product?.productType === "PROGRAM") { // Deprecated
            //     const programProduct = <ProgramPackProduct>product
            //     const centers = await this.programBusiness.getCentersForProgramPack(programProduct.packId, programProduct)
            //     this.sortCentersByLocation(req, centers)
            //     const cultCenters = centers.map(center => { return new CenterView(userContext, center, "FITNESS") })
            //     const centerDetailView: CenterDetails = {
            //         centers: cultCenters,
            //     }
            //     if (ageCategory === "JUNIOR") {
            //         centerDetailView["footer"] = {
            //             gradientColor: ["rgba(158, 156, 156, 0.08)", "rgba(0, 0, 0, 0.08)"],
            //             title: "Cult Junior classes are available only at these centers"
            //         }
            //     }
            //     if (AppUtil.isNewCenterResponseSupported(appVersion, osName)) {
            //         return centerDetailView
            //     } else {
            //         return centerDetailView.centers
            //     }
            // }

            if (useCenterServiceId && await CultUtil.isCultCenterServiceMigrationSupported(userContext, this.hamletBusiness)) {
                let centerServiceCenters
                if (productType != null && productType === "GYMFIT_FITNESS_PRODUCT") {
                    centerServiceCenters = await this.centerService.getCentersBySkus([CenterServiceSKUs.CULTPASS_GOLD], [CenterStatus.ACTIVE, CenterStatus.PRELAUNCH], cityId)
                } else {
                    centerServiceCenters = await this.centerService.getCentersBySkus([CenterServiceSKUs.CULTPASS_BLACK], [CenterStatus.ACTIVE], cityId)
                }
                centerServiceCenters  = _.filter(centerServiceCenters, (center) => {
                    if ((!_.isEmpty(productId) || (pageFrom && pageFrom === "SelectPackPreferredCenter")) && center.meta?.isVirtualCenter) {
                        return false
                    }
                    if (_.isNil(center?.meta?.cultCenterId) && productType != "GYMFIT_FITNESS_PRODUCT") {
                        this.logger.error("center does not have cult center id - ", {center})
                        return false
                    }
                    return center?.meta?.isOperational !== false
                })
                if (req?.query?.flow === "LITE") {
                    centerServiceCenters = centerServiceCenters.filter(center => (center?.vertical === CenterVertical.GYMFIT || !_.isNil(center?.linkedCenterId)))
                }
                // centerServiceCenters = CultUtil.filterHybridGymCenters(centerServiceCenters, userContext)

                const centerServiceBlackCenters: CenterViewV2[] = _.map(centerServiceCenters, centerServiceCenter => {
                    if (_.isNil(centerServiceCenter?.meta?.cultCenterId && productType != "GYMFIT_FITNESS_PRODUCT")) {
                        this.logger.error("center does not have cult center id - ", centerServiceCenter.id)
                    }
                    return new CenterViewV2(userContext, centerServiceCenter, productType ?? "FITNESS", undefined, flow)
                })

                const centerDetailView: CenterDetails = {
                    centers: centerServiceBlackCenters,
                }

                if (ageCategory === "JUNIOR") {
                    centerDetailView["footer"] = {
                        gradientColor: ["rgba(158, 156, 156, 0.08)", "rgba(0, 0, 0, 0.08)"],
                        title: "Cult Junior classes are available only at these centers"
                    }
                }
                if (AppUtil.isNewCenterResponseSupported(appVersion, osName)) {
                    return centerDetailView
                } else {
                    return centerDetailView.centers
                }
            } else {
                let centers = await this.cultBusiness.getAllCenters(userContext,  cityId, isCultUnbound)
                centers = _.filter(centers, (center) => {
                    if ((!_.isEmpty(productId) || (pageFrom && pageFrom === "SelectPackPreferredCenter")) && center.meta?.isVirtualCenter) {
                        return false
                    }
                    if (_.isNil(center?.meta?.cultCenterId) && productType != "GYMFIT_FITNESS_PRODUCT") {
                        this.logger.error("center does not have cult center id - ", {centerId: center.id})
                        return false
                    }
                    return center?.meta?.isOperational !== false
                })
                if (req?.query?.flow === "LITE") {
                    centers = centers.filter(center => (center?.vertical === CenterVertical.GYMFIT || !_.isNil(center?.linkedCenterId)))
                }
                if (isCultUnbound) {
                    const cultUnboundEnabledCenterIds: String[] = await this.cultFitService.getUnboundCenterIds()
                    centers = centers.filter(center => cultUnboundEnabledCenterIds.includes(center.meta.cultCenterId.toString()))
                }
                if (isPilateGymPage) {
                    centers = centers.filter(center => {
                        const centerMeta = center.meta as any
                        return centerMeta.isPilatesCenter
                    })
                }
                const cultCenters = centers.map(center => {
                    if (_.isNil(center?.meta?.cultCenterId && productType != "GYMFIT_FITNESS_PRODUCT")) {
                        this.logger.error("center does not have cult center id - ", center.id)
                    }
                    return new CenterViewV2(userContext, center, productType ?? "FITNESS", undefined, flow)
                })
                const centerDetailView: CenterDetails = {
                    centers: cultCenters,
                }
                if (ageCategory === "JUNIOR") {
                    centerDetailView["footer"] = {
                        gradientColor: ["rgba(158, 156, 156, 0.08)", "rgba(0, 0, 0, 0.08)"],
                        title: "Cult Junior classes are available only at these centers"
                    }
                }
                if (AppUtil.isNewCenterResponseSupported(appVersion, osName)) {
                    return centerDetailView
                } else {
                    return centerDetailView.centers
                }
            }
        }

        @httpPost("/cultClassDismissToolTip")
        async dismissDanceFitnessToolTip(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const redisKey: string = "dance_fitness_tooltip_status_" + userId
            const val = await this.crudDao.update(redisKey, `{"status":"DISMISSED"}`)
            return {"response": val}
        }

        @httpPost("/cultClassDismissToolTipStrength")
        async dismissStrengthToolTip(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const redisKey: string = "strength_tooltip_status_" + userId
            const val = await this.crudDao.update(redisKey, `{"status":"DISMISSED"}`)
            return {"response": val}
        }

        @httpPost("/cultClassImpressionCount")
        async increaseBootcampImpressionCount(req: express.Request): Promise<any> {
            const userId = req.session.userId
            const userContext: UserContext = req.userContext as UserContext
            const redisKey: string = "dance_fitness_impression_count_" + userId
            let count = 1
            await this.crudDao.read(redisKey).then(async function (payload) {
                if (payload) {
                    const impressionCount = JSON.parse(payload)
                    count = parseInt(impressionCount["count"]) + 1
                }
            })
            const val = await this.crudDao.update(redisKey, `{"count":"${count}"}`)
            return {"response": val}
        }

        @httpGet("/centers")
        public async getAllCenters(req: express.Request): Promise<CenterView[]> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userContext = req.userContext as UserContext
            let centers
            if (req.query?.pageFrom && req.query?.pageFrom === "SelectPackPreferredCenter") {
                centers = await this.centerService.getCentersBySkus([CenterServiceSKUs.CULTPASS_BLACK], [CenterStatus.ACTIVE], cityId)
            } else {
                centers = await this.cultBusiness.getAllCenters(userContext, cityId)
            }
            return centers.map(center => {
                if (_.isNil(center?.meta?.cultCenterId)) {
                    this.logger.error("center does not have cult center id - ", center.id)
                }
                return new CenterViewV2(userContext, center, "FITNESS", null)
            })
        }


        @httpGet("/preference")
        public async getPreferences(req: express.Request): Promise<PreferenceDetail> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const productType = req.query.productType
            return this.cultBusiness.getClassSchedulePreference(req.userContext as UserContext, cityId, userId, productType)
        }


        @httpPost("/preference")
        public async savePreferences(req: express.Request): Promise<{ status: boolean }> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const preference = <Preference>req.body
            const userContext = req.userContext as UserContext
            return this.cultBusiness.savePreference(userContext, cityId, userId, preference)
        }

        @httpPost("/updateFavoritesPreference")
        public async updateFavoritesPreferences(req: express.Request): Promise<{ status: boolean }> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const centerIds = req.body.centerIds
            const productType = req.body.productType
            const userContext = req.userContext as UserContext
            const currentPreference = await this.cultBusiness.getClassSchedulePreference(req.userContext as UserContext, cityId, userId, productType)
            const oldFavoriteCenterIds = currentPreference.favourites.selectedFavourites.map(centers => centers.centerId)
            const preference = <Preference>{
                selectedFavourites: [...oldFavoriteCenterIds, ...centerIds],
                currentSelection: "favourites",
                productType: productType
            }
            return this.cultBusiness.savePreference(userContext, cityId, userId, preference)
        }

        @httpPost("/bookingPreference")
        public async saveBookingConfirmationPreference(req: express.Request): Promise<{ status: boolean }> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            let value, key
            if (req.body.hasOwnProperty("syncCalender")) {
                key = "USER_SEND_BOOKING_CONFIRMATION_EMAIL"
                value = req.body.syncCalender
            } else if (req.body.hasOwnProperty("ivrCall")) {
                key = "USER_BOOKING_IVR_STATUS"
                value = req.body.ivrCall
                LogUtil.info("callReminder:: The value and userId are " + value + userId)
                if (!value) {
                    LogUtil.info("callReminder:: The userId for which call reminder is turned of is " + userId)
                    await this.cultService.disableAlarmForUserID(userId)
                }
            } else if (req.body.hasOwnProperty("ivrCallTime")) {
                key = "USER_BOOKING_IVR_MINUTES_BEFORE"
                value = req.body.ivrCallTime
            }
            const preference = { "settings": [{ "key": key, "value": value }] }
            const userContext = req.userContext as UserContext
            return this.cultBusiness.saveBookingConfirmationPreference(userContext, cityId, userId, preference)
        }

        @httpGet("/mind/center")
        public async getMindCenters(req: express.Request): Promise<CenterView[]> {
            const session: Session = req.session
            const cityId: string = req.query.cityId || session.sessionData.cityId
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const userAgent: UserAgent = session.userAgent
            const productId = req.query.productId
            const centerIds: string[] = req.query.centerIds ? req.query.centerIds.split(",") : []
            const viewOnly: boolean = req.query.viewOnly === "true"
            const showAvailableWorkouts: boolean = req.query.showAvailableWorkouts === "true"
            if (!_.isEmpty(centerIds)) {
                const centerMap = await this.cultBusiness.getAllCentersCached()
                const centerViews = centerIds.map(centerId => {
                    const center = centerMap[Number(centerId)]
                    const centerView = new CenterView(userContext, center, "FITNESS")
                    if (viewOnly) {
                        centerView.action = undefined
                    }
                    return centerView
                })
                return centerViews
            }
            if (productId) {
                const product = await this.catalogueService.getProduct(productId)
                if (product.productType === "PROGRAM") {
                    const programProduct = <ProgramPackProduct>product
                    const centers = await this.programBusiness.getCentersForProgramPack(programProduct.packId, programProduct)
                    this.sortCentersByLocation(req, centers)
                    return centers.map(center => { return new CenterView(userContext, center, "MIND") })
                }
            } else {
                const isWorkoutViewSupported = await AppUtil.isWorkoutViewSupportedInCenterSelection(userContext, this.hamletBusiness)
                return this.mindFitService.browseFitnessCenter(userContext.userProfile.timezone, "CUREFIT_API", false, cultCityId, true,
                    undefined, false, userContext.userProfile.subUserId || userContext.userProfile.userId, showAvailableWorkouts).then(centers => {
                        this.sortCentersByLocation(req, centers)
                        return centers.map(center => { return new CenterView(userContext, center, "MIND", undefined, showAvailableWorkouts && isWorkoutViewSupported) })
                    })
            }
        }

        @httpGet("/center/:centerId")
        public async getFitnessCenter(req: express.Request): Promise<ProductDetailPage> {
            const centerId: string = req.params.centerId
            const userContext = req.userContext as UserContext
            const centerPromise = this.cultFitService.getFitnessCenter(centerId, "CUREFIT_API")
            const center = await centerPromise
            if (AppUtil.isNewCenterPageSupported(userContext)) {
                return this.cultCenterPageViewBuilder.buildView(userContext, center, "FITNESS")
            }
            return new CenterDetailView(center, "FITNESS", userContext, await userContext.userPromise)
        }

        @httpGet("/mind/center/:centerId")
        public async getMindCenter(req: express.Request): Promise<ProductDetailPage> {
            const userContext = req.userContext as UserContext
            const centerId: string = req.params.centerId
            const centerPromise = this.mindFitService.getFitnessCenter(centerId, "CUREFIT_API")
            const center = await centerPromise
            if (AppUtil.isNewCenterPageSupported(userContext)) {
                return this.cultCenterPageViewBuilder.buildView(userContext, center, "MIND")
            }
            return new CenterDetailView(center, "MIND", userContext, await req.userContext.userPromise)
        }

        @httpGet("/workout/:workoutId")
        public async getCultWorkout(req: express.Request): Promise<WorkoutDetailView | WorkoutCategoryDetailView> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const workoutId: string = req.params.workoutId
            const workoutCategoryId: string = req.query.workoutCategoryId
            const user = await this.userCache.getUser(session.userId)
            const userContext = req.userContext
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            if (workoutCategoryId) {
                const workoutCategory = await this.cultFitService.workoutCategory(workoutCategoryId, "CUREFIT_API", cultCityId)
                return new WorkoutCategoryDetailView().buildView(workoutCategory, "FITNESS", req.userContext as UserContext, await req.userContext.userPromise, this.cultBusiness)
            } else {
                const subProductType: ProductType = req.query.subProductType
                const ptProductId: string = req.query.ptProductId
                const workout = await this.cultFitService.getWorkout(workoutId, "CUREFIT_API", cultCityId)
                const childPromises = Array.isArray(user.subUserRelations) && user.subUserRelations.map(item => {
                    return this.userCache.getUser(item.subUserId)
                })
                const subUsers = childPromises && await Promise.all(childPromises)
                let action
                if (subProductType === "LIVE_PERSONAL_TRAINING") {
                    action = await this.careBusiness.getLivePTSessionBookAction(userContext, { productId: ptProductId, actionTitle: "Book Session", subCategoryCode: "LIVE_PERSONAL_TRAINING" })
                }
                return new WorkoutDetailView(workout, "FITNESS", req.userContext as UserContext, user, subUsers, subProductType, action)
            }

        }

        @httpGet("/workoutCategory/:workoutCategoryId")
        public async getCultWorkoutCategory(req: express.Request): Promise<WorkoutCategoryDetailView> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const workoutCategoryId: string = req.params.workoutCategoryId
            const userContext = req.userContext
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const cultWorkoutCategory: CultWorkoutCategory = await this.cultFitService.workoutCategory(workoutCategoryId, "CUREFIT_API", cultCityId)
            return new WorkoutCategoryDetailView().buildView(cultWorkoutCategory, "FITNESS", req.userContext as UserContext, await req.userContext.userPromise, this.cultBusiness)
        }

        @httpGet("/mind/workout/:workoutId")
        public async getMindWorkout(req: express.Request): Promise<WorkoutDetailView | WorkoutCategoryDetailView> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const workoutId: string = req.params.workoutId
            const workoutCategoryId: string = req.query.workoutCategoryId
            const user = await this.userCache.getUser(session.userId)
            const userContext = req.userContext
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            if (workoutCategoryId) {
                const workoutCategory = await this.mindFitService.workoutCategory(workoutCategoryId, "CUREFIT_API", cultCityId)
                return new WorkoutCategoryDetailView().buildView(workoutCategory, "MIND", req.userContext as UserContext, await req.userContext.userPromise, this.cultBusiness)
            } else {
                const workout = await this.mindFitService.getWorkout(workoutId, "CUREFIT_API", cultCityId)
                const childPromises = Array.isArray(user.subUserRelations) && user.subUserRelations.map(item => {
                    return this.userCache.getUser(item.subUserId)
                })
                const subUsers = childPromises && await Promise.all(childPromises)
                return new WorkoutDetailView(workout, "MIND", req.userContext as UserContext, user, subUsers)
            }
        }

        @httpGet("/mind/workoutCategory/:workoutCategoryId")
        public async getMindWorkoutCategory(req: express.Request): Promise<WorkoutCategoryDetailView> {
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const workoutCategoryId: string = req.params.workoutCategoryId
            const userContext = req.userContext
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const workoutCategory: CultWorkoutCategory = await this.mindFitService.workoutCategory(workoutCategoryId, "CUREFIT_API", cultCityId)
            return new WorkoutCategoryDetailView().buildView(workoutCategory, "MIND", req.userContext as UserContext, await req.userContext.userPromise, this.cultBusiness)
        }

        @httpGet("/trainer")
        public getFitnessTainers(req: express.Request): Promise<TrainerView[]> {
            const session: Session = req.session
            return this.cultFitService.browseFitnessTrainer(req.query.center).then(trainers => {
                return trainers.map(trainer => { return new TrainerView(trainer, session.userAgent, false) })
            })
        }

        @httpGet("/mind/trainer")
        public getMindTainers(req: express.Request): Promise<TrainerView[]> {
            const session: Session = req.session
            return this.mindFitService.browseFitnessTrainer(req.query.center).then(trainers => {
                return trainers.map(trainer => { return new TrainerView(trainer, session.userAgent, false) })
            })
        }

        @httpGet("/packOffer")
        public async getPreRegistrationOffers(req: express.Request): Promise<{
            offerBanner: string
            centerWiseSlots: CenterWiseSlot[]
        }> {
            const session: Session = req.session
            const userContext = req.userContext as UserContext
            const bannerId: string = req.query.bannerId || req.query.bId
            const banner = await this.getBanner(bannerId, userContext.sessionInfo.userAgent)
            return this.cultOfferDetailViewBuilder.getViewForPreRegistrationPacks(userContext, "FITNESS", banner)
        }

        @httpGet("/packOffer/:packId") // PACK_ID_DEPRECATION TODO: Deprecate this API
        public async getPackOffers(req: express.Request): Promise<CultOfferProductDetailPage> {
            const packId = req.params.packId
            const session: Session = req.session
            const userId: string = session.userId
            const offerIds = req.query.offerIds
            const bannerId: string = req.query.bId
            const offerType: PackOfferType = req.query.offerType
            const navigateToNewPackPage = req.query.navigateToNewPackPage
            if (offerType === "CENTERWISE_PACK") {
                const productId = CatalogueServiceV2Utilities.getCultPackProductId(packId)
                this.logger.info("PMS::DEPR /packOffer/:packId is deprecated", {packId, offerIds, bannerId, offerType, productId})
                const packDetail = await this.catalogueServicePMS.getProduct(productId)
                return this.cultOfferDetailViewBuilder.getViewForCenterSpecificPack(packDetail, req.userContext as UserContext, "FITNESS", navigateToNewPackPage)
            }
            return this.slottedOfferPage(userId, offerIds, packId, bannerId, req.userContext, "FITNESS")
        }

        @httpGet("/mind/packOffer")
        public async getMindPreRegistrationOffers(req: express.Request): Promise<CultOfferProductDetailPage> {
            // TODO: Add a UI builder and abstract out common code between cult and mind pack offer api
            const type: string = req.query.type
            const promises: Promise<any>[] = []
            const session: Session = req.session
            const cityId: string = session.sessionData.cityId
            const userId: string = session.userId
            const userContext = req.userContext
            // Deprecated
            this.logger.error("PMS::DEPR /mind/packOffer is deprecated", {type, userId})
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Deprecated API - /mind/packOffer").build()
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const preregistrationPacks = [] as CultPack[]
            const bannerId: string = req.query.bannerId || req.query.bId
            const banner = await this.getBanner(bannerId, userContext.sessionInfo.userAgent)
            // return this.cultOfferDetailViewBuilder.getViewForPreRegistrationPacks(preregistrationPacks, userContext, "MIND", banner)
        }

        @httpGet("/mind/packOffer/:packId")
        public async getMindPackOffers(req: express.Request): Promise<CultOfferProductDetailPage> {
            const packId = req.params.packId
            const session: Session = req.session
            const userId: string = session.userId
            const offerIds = req.query.offerIds
            const bannerId: string = req.query.bId
            const offerType: PackOfferType = req.query.offerType
            this.logger.error("/mind/packOffer/:packId is deprecated", {packId, offerIds, bannerId, offerType})
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Deprecated API - /mind/packOffer/:id").build()
            // if (offerType === "CENTERWISE_PACK") {
            //     const packDetail = await this.mindFitService.getPack(packId, userId)
            //     return this.cultOfferDetailViewBuilder.getViewForCenterSpecificPack(packDetail, req.userContext as UserContext, "MIND")
            // }
            // return this.slottedOfferPage(userId, offerIds, packId, bannerId, req.userContext, "MIND")
        }

        private async slottedOfferPage(userId: string, offerIds: string, packId: string, bannerId: string, userContext: UserContext, productType: ProductType): Promise<CultOfferProductDetailPage> {
            if (_.isEmpty(offerIds)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Offer ids not passed in getting mind pack offers for packId: " + packId).build()
            }
            const userAgent = userContext.sessionInfo.userAgent
            const offerId = (offerIds.split(","))[0]
            const centersMapPromise = productType === "FITNESS" ? this.catalogueService.getCultCentersMap() : this.catalogueService.getCultMindCentersMap()
            const centersMap = await centersMapPromise
            this.logger.info("CARE::DEBUG Call attempted to offer-service-v2 to fetch offer inventories", {offerIds})
            const offerInventories = {} as OfferIdOfferInventoryMap
            const offerInventory = offerInventories[offerId]
            if (_.isNil(offerInventory)) {
                throw this.errorFactory.withCode(ErrorCodes.OFFER_NOT_VALID_ERR, 400).withDebugMessage("Offer not valid anymore for offerId: " + offerId).build()
            }
            const centerWiseSlots: CenterWiseSlot[] = []
            const productId = offerInventory.offer.productIds[0]
            const fitnessPackProduct = await this.catalogueServicePMS.getProduct(productId)
            const basePack = CatalogueServiceUtilities.getPackPageAction(fitnessPackProduct)
            offerInventory.inventories.forEach(inventory => {
                const centerProductId = productType === "FITNESS" ? CatalogueServiceV2Utilities.getCultCenterProductId(inventory.centerId.toString()) : CatalogueServiceV2Utilities.getMindCenterProductId(inventory.centerId.toString())
                const center = centersMap.get(centerProductId)
                const subTitle = CultUtil.getCenterLaunchDateText(userContext, center.launchDate)
                centerWiseSlots.push({
                    remaingSlots: inventory.remainingCount,
                    centerName: center.name,
                    subTitle: subTitle,
                    centerId: inventory.centerId.toString(),
                    centerAddress: "",
                    action: inventory.remainingCount > 0 ? basePack + "&centerId=" + inventory.centerId + "&canChangeCenter=false" : ""
                })
            })

            const banner = await this.getBanner(bannerId, userAgent)
            const tnc = offerInventory.offer.tNc.join("\n\n")
            return this.cultOfferDetailViewBuilder.getView(centerWiseSlots, banner, userAgent, tnc)


        }

        private async getBanner(bannerId: string, userAgent: UserAgent): Promise<Banner> {
            if (!bannerId) {
                return undefined
            }
            const bannerById = this.bannerService.getBannerById(bannerId)
            if (!bannerById) {
                return undefined
            }
            const banner: Banner = {
                id: bannerById.bannerId,
                action: await this.bannerService.getBannerAction(bannerById, userAgent),
                image: "/" + bannerById.appImage,
                desktopWebImage: "/" + bannerById.webImage,
                mobileWebImage: "/" + bannerById.mWebImage
            }
            return banner
        }

        @httpGet("/class")
        public getFitnessClasses(req: express.Request): Promise<ClassListView> {
            const session: Session = req.session
            const center = req.query.center
            const userContext = req.userContext as UserContext
            // Temp hack to avoid crash after closing or  selling new center packs
            let caterToCloseAndNewCenter = center
            if (center === "8"
                || center === "13") {
                caterToCloseAndNewCenter = "3"
            }
            const fitnessClassPromise: Promise<CultClassesResponse> = this.cultFitService.browseFitnessClass(caterToCloseAndNewCenter, session.userId)

            const fitnessCenterPromise: Promise<CultCenter> = this.catalogueService.getCultCenter(caterToCloseAndNewCenter)
            const membershipPromise: Promise<{ memberships: CultMembership[], isEligibleForFreeClass: boolean }> = this.cultFitService.activeMembership(session.userId)
            session.sessionData.cultCenterId = center
            const sessionUpdatePromise: Promise<Session> = this.sessionBusiness.updateSessionData(session.at, session.sessionData)

            return Promise.all([fitnessClassPromise, fitnessCenterPromise, membershipPromise, sessionUpdatePromise]).then(results => {
                const fitnessClassDetails = results[0]
                const fitnessCenter = results[1]
                const membershipDetails = results[2]
                let customerType: CULT_CUSTOMER_TYPE
                const currentMembership = CultUtil.getCurrentMembership(membershipDetails.memberships)
                if (currentMembership && currentMembership.state !== "PAUSED") {
                    customerType = "MEMBERSHIP"
                } else if (membershipDetails.isEligibleForFreeClass) {
                    customerType = "FREE"
                } else {
                    customerType = "PAID"
                }
                fitnessClassDetails.classes = fitnessClassDetails.classes.filter(cultclass => {
                    return (Number(cultclass.centerID) === fitnessCenter.id) ? true : false
                })

                return this.classListViewBuilder.build(userContext, "FITNESS", fitnessClassDetails, fitnessCenter, customerType)
            })
        }

        @httpGet("/classes")
        public async getFitnessClassesV1(req: express.Request): Promise<ClassListView> {
            const session: Session = req.session
            const center = req.query.center && req.query.center !== "undefined" ? req.query.center : session.sessionData.cultCenterId
            const userId = session.userId
            const cityId = session.sessionData.cityId
            const userContext = req.userContext as UserContext
            const filterWorkoutId = req.query?.filterWorkoutId as number
            if (!center) {
                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a cult center")
            }
            let fitnessCenterPromise: Promise<CultCenter> = this.catalogueService.getCultCenter(center)
            const membershipPromise: Promise<{ memberships: CultMembership[], isEligibleForFreeClass: boolean }> = this.cultFitService.activeMembership(session.userId)

            let fitnessCenterId = center
            let fitnessCenter = await fitnessCenterPromise
            const membershipDetails = await membershipPromise
            let customerType: CULT_CUSTOMER_TYPE
            const currentMembership = CultUtil.getCurrentMembership(membershipDetails.memberships)
            if (currentMembership && currentMembership.state !== "PAUSED") {
                customerType = "MEMBERSHIP"
            } else if (membershipDetails.isEligibleForFreeClass) {
                customerType = "FREE"
            } else {
                customerType = "PAID"
            }
            // Fix to handle the case where user preferred center got shut down or wrong center id being passed
            if (!fitnessCenter || fitnessCenter.state !== "ACTIVE") {
                const derivedCityDetails = await this.getDerivedCityDetails(userContext, cityId, userId, membershipDetails.memberships)
                const defaultCultCenterId = derivedCityDetails.defaultCultCenter
                fitnessCenterId = defaultCultCenterId
                fitnessCenterPromise = this.catalogueService.getCultCenter(defaultCultCenterId)
            }

            session.sessionData.cultCenterId = fitnessCenterId
            const fitnessClassPromise: Promise<CultClassesResponse> = this.cultFitService.browseFitnessClass(fitnessCenterId, session.userId, undefined, undefined, undefined, {workoutId: filterWorkoutId})
            const sessionUpdatePromise: Promise<Session> = this.sessionBusiness.updateSessionData(session.at, session.sessionData)

            await sessionUpdatePromise
            fitnessCenter = await fitnessCenterPromise
            const fitnessClassDetails = await fitnessClassPromise

            // filtering out bootcamp classes
            fitnessClassDetails.classes = fitnessClassDetails.classes.filter(cultClass => {
                return !CultUtil.isBootcampClass(cultClass.workoutID)
            })
            const classView: ClassListView = await this.classListViewBuilderV1.build(userContext, "FITNESS", fitnessClassDetails, fitnessCenter, customerType)
            return classView
        }

        @httpGet("/classes/v2/:filterWorkoutId")
        @httpGet("/classes/v2")
        public async getFitnessClassesV2(req: express.Request): Promise<ClassScheduleResponse> {
            const session: Session = req.session
            const appVersion: number = Number(req.headers["appversion"])
            const osName: string = req.headers["osname"] as string
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userId = session.userId
            const cityId = session.sessionData.cityId
            const productType = req.query.productType
            // TODO add app version check and depricate this
            const filterWorkoutId = req.params.filterWorkoutId
            const filterWorkoutIds = req.query.filterWorkoutIds
            const isCultUnbound = req.query.isCultUnbound
            let isPilateGymPage = req.query.isPilateGymPage
            const scheduleDate = req.query.date
            const isNuxFlow = req.query.isNuxFlow
            const isLiveBookingPage = req.query.isLiveBookingPage
            const userContext = req.userContext
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)
            const pageFrom = req.query.pageFrom
            const tz = userContext.userProfile.timezone

            isPilateGymPage = (isPilateGymPage === "true")

            const filterWorkoutIdList: string[] = filterWorkoutIds ? filterWorkoutIds.split(",") : []
            if (_.isEmpty(filterWorkoutIdList) && !_.isEmpty(filterWorkoutId)) {
                filterWorkoutIdList.push(filterWorkoutId.toString())
            }

            if (AppUtil.isInternationalTLApp(userContext) || AppUtil.isInternationalTLTVApp(userContext)) {
                return this.classListViewBuilderV2.buildIntlView(userContext, productType)
            }

            const hamletConfigRequest: HamletConfigRequest<boolean> = {
                query: {
                    experimentId: "1938",
                    configKey: "isV2Enabled",
                    defaultValue: false
                },
                context: AppUtil.getHamletContext(userContext, ["1938"])
            }
            const isV2Enabled: boolean = await this.hamletBusiness.getConfig(hamletConfigRequest)
            if (productType !== "FITNESS") {
                this.logger.info("Requests for non fitness product type are not supported in V2, productType: " + productType)
            }
            const isCultUnboundWorkoutId = !_.isEmpty(filterWorkoutIdList)  && await CultUtil.isUnboundWorkoutIdV2(filterWorkoutIdList, this.cultFitService, isCultUnbound)
            const isHabitGameUser = await CultUtil.doesUserBelongToHabitGameSegment(userContext, this.segmentService)
            const isUserPartOfCultClassRedirection = await AppUtil.isUserPartOfCultClassRedirection(userContext, this.segmentService)
            if ((isV2Enabled || isHabitGameUser || isUserPartOfCultClassRedirection) && productType === "FITNESS" && !isLiveBookingPage && !isCultUnboundWorkoutId) {
                // redirect to cf-api-v2/cult/classes
                req.query.filterWorkoutId = filterWorkoutId
                this.logger.info("Redirecting to cf-api-v2/cult/classes for user id - " + userId)
                return this.cFAPIJavaService.cultClasses(req)
            }
            this.logger.info("Redirecting to cf-api/cult/classes for user id - " + userId)

            if (isPilateGymPage) {
                filterWorkoutIdList.push(...(_.map(PILATES_WORKOUT_IDS, (workoutId) => workoutId.toString())))
            }


            const rescheduleSourceBookingNumber = req.query.rescheduleSourceBookingNumber
            const centerIds = req.query.centerId && req.query.centerId !== "undefined" ? req.query.centerId.split(",") : undefined
            const centerServiceIds = req.query.centerServiceId && req.query.centerServiceId !== "undefined" ? req.query.centerServiceId.split(",") : undefined
            const filterApplied = req.query.filterApplied
            const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
            const cultCityId = productType === "FITNESS" ? await this.cultBusiness.getCultCityId(userContext, cityId, userId)
                : await this.cultBusiness.getMindCityId(userContext, cityId, userId)
            const cultCity = await this.cityService.getCityByCultCityId(cultCityId)
            const cultCityName = cultCity?.cityId
            let classSchedulePromise: Promise<CultClassesResponseV2>
            let centerViewOnly = false
            const { sessionData } = session
            const bookingScreenNudgeEternalPromise: any = null
            // if (await AppUtil.isBookingScreenNudgesSupported(userContext, this.hamletBusiness) && !isLiveBookingPage) {
            //     bookingScreenNudgeEternalPromise = eternalPromise(this.cultBusiness.getBookingScreenNudges(userContext, productType))
            // }

            let oneDaySchedule = false,  date = null
            const isInOneDayScheduleTimeRange = TimeUtil.compareHourMin(this.cultSchedulePageConfig.oneDayScheduleStart, TimeUtil.now(tz)) &&
                TimeUtil.compareHourMin(TimeUtil.now(tz), this.cultSchedulePageConfig.oneDayScheduleEnd)
            if (AppUtil.isOneDayScheduleSupported(userContext) && this.cultSchedulePageConfig.oneDayScheduleEnabled
                    && isInOneDayScheduleTimeRange) {
                oneDaySchedule = true
                date = scheduleDate
            }
            let updatedUserSettings = null
            const filterWorkoutIdListStringified = filterWorkoutIdList.join(",")
           if (AuthUtil.isGuestUser(userContext)) {
                const preference: CultUserPreference = productType === "FITNESS" ? sessionData.cultPreference : sessionData.mindPreference
                const settings = preference?.settings ?? null
                let currentSelection = ""
                let queryParams = {}
                if (settings) {
                    settings.forEach((setting) => {
                        if (setting.key === "USER_BOOKING_V2_ACTIVE_SETTING") {
                            currentSelection = setting.value
                        }
                    })
                    settings.forEach((setting) => {
                        if (setting.key === currentSelection) {
                            if (currentSelection === "USER_BOOKING_V2_LAT_LONG") {
                                const { latitude, longitude } = setting.value
                                queryParams = { latitude, longitude }
                            } else if (currentSelection === "USER_BOOKING_V2_FAVOURITE_CENTER") {
                                queryParams = { favouriteCenters: setting.value }
                            } else if (currentSelection === "USER_BOOKING_V2_LOCALITY_V3") {
                                queryParams = { localityName: setting.value }
                            }
                        }
                    })
                    queryParams = {...queryParams, oneDaySchedule}
                } else {
                    queryParams = {localityName: await GymfitUtil.getDefaultLocality(this.interfaces.gymfitService, userContext?.userProfile?.cityId)}
                }
                if (date) queryParams = {...queryParams, date}
                classSchedulePromise =
                    productType === "FITNESS"
                        ? this.cultFitService.browseFitnessClassV2WithParams(
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            queryParams,
                            session.deviceId
                        )
                        : this.mindFitService.browseFitnessClassV2WithParams(
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            queryParams,
                            session.deviceId
                        )
                 updatedUserSettings = settings
            } else if (!centerIds && !centerServiceIds) {
                classSchedulePromise =
                    productType === "FITNESS"
                        ? this.cultFitService.browseFitnessClassV2(
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            (date) ? {oneDaySchedule, date, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })} : {oneDaySchedule, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })},
                            session.deviceId
                        )
                        : this.mindFitService.browseFitnessClassV2(
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            {oneDaySchedule, date, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })},
                            session.deviceId
                        )
            } else {
                centerViewOnly = true
                classSchedulePromise =
                    productType === "FITNESS"
                        ? this.cultFitService.browseFitnessClassV2ForCenters(
                            centerIds,
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            (date) ? {oneDaySchedule, date, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })} : {oneDaySchedule, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })},
                            session.deviceId,
                            centerServiceIds,
                        )
                        : this.mindFitService.browseFitnessClassV2ForCenters(
                            centerIds,
                            cultCityId,
                            userId,
                            "CUREFIT_APP",
                            userContext.userProfile.subUserId,
                            {oneDaySchedule, date, ...(!_.isEmpty(filterWorkoutIdListStringified) && { filterWorkoutIdListStringified })},
                            session.deviceId
                        )
            }

            let squadData
            let socialDataEternalPromise: Promise<{ obj: SquadClassBookingLite[] }>
            try {
                if ( productType === "FITNESS" && AppUtil.isSquadsSupportedInBookingV2(userContext)) {
                    socialDataEternalPromise = eternalPromise(baseService.getUpcomingClassesForSquadFromCache(session.userId, "CUREFIT_APP"), `Error in fetching getUpcomingClassesForSquad for userId: ${session.userId}`)
                }
            } catch (error) {
                this.logger.error(`getUpcomingClassesForSquad failure for user: ${session.userId}`)
            }
            if (!userContext.userProfile.promiseMapCache) {
                userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
            }
            const widgetId = isLiveBookingPage
                ? null :
                (process.env.APP_ENV === "PRODUCTION" || process.env.APP_ENV === "ALPHA" ? CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_PROD : CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_STAGE)
            const [classSchedule, socialData, widgetResponseEternal] = await Promise.all([
                classSchedulePromise,
                socialDataEternalPromise ? socialDataEternalPromise : Promise.resolve(undefined),
                widgetId ? eternalPromise(this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)) : Promise.resolve(undefined)
            ])
            const widgetResponse = widgetResponseEternal?.obj
            squadData = socialData?.obj
            if (updatedUserSettings) {
                classSchedule.userSettings = updatedUserSettings
            }
            let rescheduleClassBookingResponse: CultBookingResponse = null
            if (rescheduleSourceBookingNumber) {
                rescheduleClassBookingResponse = await baseService.getBookingV2(rescheduleSourceBookingNumber, userId, true, "CUREFIT_APP", userContext.userProfile.subUserId)
            }
            return this.classListViewBuilderV2.buildView(userContext, productType, classSchedule, centerViewOnly, filterApplied,
                isLiveBookingPage, rescheduleClassBookingResponse, orderSource, bookingScreenNudgeEternalPromise, isNuxFlow, req.query, squadData, oneDaySchedule, cultCityName, widgetResponse, filterWorkoutId, isCultUnbound)
        }

        @httpGet("/live/classes/v2")
        public async getLiveFitnessClassesV2(req: express.Request): Promise<ClassScheduleResponse> {
            req.query.isLiveBookingPage = true
            return this.getFitnessClassesV2(req)
        }

        private async getDerivedCityDetails(userContext: UserContext, cityId: string, userId: string, memberships: CultMembership[]): Promise<DerivedCityDetails> {
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            const city = userContext.userProfile.city || await this.cityService.getDefaultCity(tenant)
            if (this.cityService.checkIfCityIsOtherCity(city.cityId)) {
                const membership = CultUtil.cultPackProgress(userContext, memberships)
                if (membership) {
                    const derivedCity = await this.cityService.getCityByCultCityId(membership.currentMembership.cityID)
                    return {
                        cultCityId: derivedCity.cultCityId, cityId: derivedCity.cityId, defaultCultCenter: derivedCity.defaultCultCenter,
                        defaultMindCenter: derivedCity.defaultMindCenter
                    }
                }
            }
            return { cultCityId: city.cultCityId, cityId: city.cityId, defaultCultCenter: city.defaultCultCenter, defaultMindCenter: city.defaultMindCenter }
        }

        @httpGet("/mind/classes")
        public async getCultMindClassesV1(req: express.Request): Promise<ClassListView> {
            const session: Session = req.session
            const center = req.query.center && req.query.center !== "undefined" ? req.query.center : session.sessionData.mindCenterId
            const userId = session.userId
            const cityId = session.sessionData.cityId
            const userContext = req.userContext as UserContext

            if (!center) {
                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a mind center")
            }

            let mindCenterPromise: Promise<CultCenter> = this.catalogueService.getCultMindCenter(center)
            const membershipPromise: Promise<{ memberships: CultMembership[], isEligibleForFreeClass: boolean }> = this.mindFitService.activeMembership(session.userId)

            let mindCenter = await mindCenterPromise
            let mindCenterId = center
            const membershipDetails = await membershipPromise

            // Fix to handle the case where user preferred center got shut down or wrong center id being passed
            if (!mindCenter || mindCenter.state !== "ACTIVE") {
                const derivedCityDetails = await this.getDerivedCityDetails(userContext, cityId, userId, membershipDetails.memberships)
                const defaultMindCenterId = derivedCityDetails.defaultMindCenter
                mindCenterPromise = this.catalogueService.getCultMindCenter(defaultMindCenterId)
                mindCenterId = defaultMindCenterId
            }

            session.sessionData.mindCenterId = mindCenterId
            const updateSessionPromise = this.sessionBusiness.updateSessionData(session.at, session.sessionData)

            const mindClassPromise: Promise<CultClassesResponse> = this.mindFitService.browseFitnessClass(mindCenterId, session.userId)
            await updateSessionPromise
            mindCenter = await mindCenterPromise
            const mindClassDetails = await mindClassPromise
            let customerType: CULT_CUSTOMER_TYPE
            const currentMembership = CultUtil.getCurrentMembership(membershipDetails.memberships)
            if (currentMembership && currentMembership.state !== "PAUSED") {
                customerType = "MEMBERSHIP"
            } else if (membershipDetails.isEligibleForFreeClass) {
                customerType = "FREE"
            } else {
                customerType = "PAID"
            }
            return this.classListViewBuilderV1.build(userContext, "MIND", mindClassDetails, mindCenter, customerType)
        }

        @httpGet("/booking/:bookingId")
        getBookingDetail(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId: string = session.userId
            const bookingId: string = req.params.bookingId
            return this.getCultBookingDetail(req.userContext as UserContext, bookingId, userId)
        }

        @httpGet("/mind/booking/:bookingId")
        mindBookingDetail(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId: string = session.userId
            const bookingId: string = req.params.bookingId
            return this.getMindBookingDetail(req.userContext as UserContext, bookingId, userId)
        }

        @httpGet("/booking/v2/:bookingNumber", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async classBookingDetailV2(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId: string = session.userId
            const bookingNumber: string = req.params.bookingNumber
            const liveClassId: string = req.query.liveClassId
            const category: string = req.query.category
            const productType: ProductType = req.query.productType
            const nodeRelationID = req.query.nodeRelationID
            const ip: string = req.ip ?? req.connection.remoteAddress
            const isSuccessiveClassCall: boolean = req.query.isSuccessiveClassCall === "true" ? true : false
            const isNoSlotSelected: boolean = req.query.isNoSlotSelected === "true" ? true : false
            const userContext: UserContext = req.userContext as UserContext
            const blockIfInternationalUser: boolean = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            if (productType === "LIVE_FITNESS") {
                if (liveClassId && liveClassId !== "undefined" && AppUtil.isNewLiveClassProductPageSupported(userContext)) {
                    return this.liveClassDetailViewBuilderV2.buildView(userContext, category, liveClassId, bookingNumber, true, blockIfInternationalUser, nodeRelationID, isSuccessiveClassCall, isNoSlotSelected)
                }
                return this.liveClassDetailViewBuilder.buildView(userContext, bookingNumber, true, nodeRelationID)
            }
            return this.getBookingDetailV2(false, req.userContext as UserContext, productType, bookingNumber, userId, session.deviceId, this.hamletBusiness)
        }

        @httpGet("/booking/v3/:bookingNumber")
        async classBookingDetailV3(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const userId: string = session.userId
            const bookingNumber: string = req.params.bookingNumber
            const liveClassId: string = req.query.liveClassId
            const category: string = req.query.category
            const productType: ProductType = req.query.productType
            const nodeRelationID = req.query.nodeRelationID
            const isSuccessiveClassCall: boolean = req.query.isSuccessiveClassCall === "true" ? true : false
            const isNoSlotSelected: boolean = req.query.isNoSlotSelected === "true" ? true : false
            const userContext: UserContext = req.userContext as UserContext
            const ip: string = req.ip ?? req.connection.remoteAddress
            const blockIfInternationalUser: boolean = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)

            if (productType === "LIVE_FITNESS") {
                if (liveClassId && liveClassId !== "undefined" && AppUtil.isNewLiveClassProductPageSupported(userContext)) {
                    return this.liveClassDetailViewBuilderV2.buildView(userContext, category, liveClassId, bookingNumber, true, blockIfInternationalUser, nodeRelationID, isSuccessiveClassCall, isNoSlotSelected)
                }
                return this.liveClassDetailViewBuilder.buildView(userContext, bookingNumber, true, nodeRelationID)
            }
            return this.getBookingDetailV2(true, req.userContext as UserContext, productType, bookingNumber, userId, session.deviceId, this.hamletBusiness)
        }



        @httpGet("/class/v3/:classId")
        async getCultClassDetailV3(req: express.Request): Promise<CultClassDetailViewV2> {
            const productType: ProductType = "FITNESS"
            const rescheduleSourceBookingNumber: string = req.query.rescheduleSourceBookingNumber

            const session: Session = req.session
            const classId: string = req.params.classId
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const cityId = userContext.userProfile.cityId
            const nodeRelationID = req.query.nodeRelationID

            const baseService = this.cultFitService
            userContext.userProfile.promiseMapCache ??= new PromiseCache(this.interfaces)


            const [cultClass, membershipList, prefLocationType] = await Promise.all([
                baseService.getCultClass(classId, session.userId, "CUREFIT_APP", false, userContext.userProfile.subUserId, undefined, session.deviceId),
                CultUtil.getMembershipsWithCultBenefitFromMembershipService(userContext, this.membershipService),
                UserUtil.getLocationDataFromRashi(userId, cityId, LocationDataKey.LOC_PREF_TYPE, this.userAttributeClient)
            ])

            let wod: SimpleWod = null
            try {
                wod = await this.herculesService.getSimpleWodById(cultClass.wodId)
            }
            catch (error) {
                this.logger.error(error)
            }

            let socialDataEternalPromise: Promise<{ obj: SquadClassBookingLite[] }>
            try {
                if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
                    if (nodeRelationID) {
                        await baseService.addReferenceNodeRelation(session.userId, parseInt(classId), nodeRelationID, "CUREFIT_APP")
                    }
                    socialDataEternalPromise = eternalPromise(baseService.getUpcomingClassesForSquadFromCache(session.userId, "CUREFIT_APP"), `Error in fetching socialData for classId: ${classId}, userId: ${session.userId}`)
                }
            } catch (error) {
                this.logger.error(`getUpcomingClassesForSquadFromCache failure for user: ${session.userId}`)
            }
            let buddiesJoiningSmallListWidget: CultBuddiesJoiningListSmallWidget
            if (socialDataEternalPromise) {
                let socialDataForClassIds = (await socialDataEternalPromise).obj
                socialDataForClassIds = (socialDataForClassIds) ? socialDataForClassIds.filter(squadData => squadData.classId.toString() === classId) : null
                if (socialDataForClassIds && !_.isEmpty(socialDataForClassIds)) {
                    const userIds = socialDataForClassIds.map(socialDataForClassId => socialDataForClassId.userId)
                    buddiesJoiningSmallListWidget = {
                        widgetType: "CULT_BUDDIES_JOINING_LIST_SMALL_WIDGET",
                        ...(await CultUtil.getBuddiesJoiningListSmallView(userIds, this.userCache, PageTypes.PreBookClass) as CultBuddiesJoiningListSmallView),
                    }
                }
            }

            const cultMembershipDetails: CultMembershipDetails = {}

            if (cultClass.membership && cultClass.membership.centerID) {
                const centerDetails = await this.catalogueService.getCultCenter(cultClass.membership.centerID.toString())
                cultMembershipDetails["centerName"] = centerDetails && centerDetails.name
            }
            if (cultClass.membership && cultClass.membership.cityID) {
                const city = this.cityService.getCityByCultCityId(cultClass.membership.cityID)
                cultMembershipDetails["cityName"] = city.name}
            return new CultClassDetailViewV2().buildView(
                userContext,
                cultClass,
                wod,
                cultMembershipDetails,
                productType,
                rescheduleSourceBookingNumber,
                membershipList,
                this.userAttributeClient,
                prefLocationType,
                this.segmentService,
                this.logger,
                this.clsUtil,
                buddiesJoiningSmallListWidget,
                this.membershipService,
                this.serviceInterfaces,
                this.widgetBuilder,
                this.redisVMCrudDao,
                this.centerBookingCache,
                this.segmentationCacheClient,
                this.cultClassUtil,
                this.cultFitService
            )
        }

        @httpGet("/class/v2/:classId")
        async getCultClassDetailV2(req: express.Request): Promise<CultClassDetailView | LiveClassDetailView | LiveClassDetailViewV2> {
            const productType: ProductType = req.query.productType
            const ip: string = req.ip ?? req.connection.remoteAddress
            const rescheduleSourceBookingNumber: string = req.query.rescheduleSourceBookingNumber
            let baseService
            switch (productType) {
                case "FITNESS":
                    baseService = this.cultFitService
                    break
                case "MIND":
                    baseService = this.mindFitService
                    break
                default:
                    baseService = undefined
            }
            const session: Session = req.session
            const classId: string = req.params.classId
            const nodeRelationID = req.query.nodeRelationID
            const liveClassId: string = req.query.liveClassId
            const category: string = req.query.category
            const showFullScreen: boolean = req.query.isFullScreen === "true"
            const userContext: UserContext = req.userContext as UserContext
            const isSuccessiveClassCall: boolean = req.query.isSuccessiveClassCall === "true" ? true : false
            const isNoSlotSelected: boolean = req.query.isNoSlotSelected === "true" ? true : false
            const blockIfInternationalUser: boolean = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
            if (productType === "LIVE_FITNESS") {
                if (liveClassId && liveClassId !== "undefined" && AppUtil.isNewLiveClassProductPageSupported(userContext)) {
                    return this.liveClassDetailViewBuilderV2.buildView(userContext, category, liveClassId, classId, showFullScreen, blockIfInternationalUser, nodeRelationID, isSuccessiveClassCall, isNoSlotSelected)
                }
                return await this.liveClassDetailViewBuilder.buildView(userContext, classId, showFullScreen, nodeRelationID)
            }
            if (!baseService) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage(`Bad Request: Must be Cult or Mind Class, received ${productType}`).build()
            }
            let socialDataEternalPromise: Promise<{ obj: SquadClassBookingLite[] }>
            if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
                if (nodeRelationID) {
                    await baseService.addReferenceNodeRelation(session.userId, parseInt(classId), nodeRelationID, "CUREFIT_APP")
                }
                socialDataEternalPromise = eternalPromise(baseService.getUpcomingClassesForSquadFromCache(session.userId, "CUREFIT_APP"), `Error in fetching socialData for classId: ${classId}, userId: ${session.userId}`)
            }

            const isWaitlistExtensionSupported = await AppUtil.isWaitlistExtensionSupported(userContext, this.hamletBusiness)

            const cultClass: CultClass = await baseService.getCultClass(classId, session.userId, "CUREFIT_APP", false, userContext.userProfile.subUserId, undefined, session.deviceId)
            let wod: SimpleWod = null
            try {
                wod = await this.herculesService.getSimpleWodById(cultClass.wodId)
                if (_.isNil(cultClass.Center) && cultClass.centerID) {
                    cultClass.Center = await this.catalogueService.getCultCenter(cultClass.centerID)
                }
            }
            catch (error) {
                this.logger.error(error)
            }
            const user = await this.userCache.getUser(session.userId)
            // let wods: IWODDetail
            // if (!_.isNil(wod)) {
            //     wods = this.wodViewBuilder.getView(wod, cultClass.workoutID)
            // }
            const cultMembershipDetails: CultMembershipDetails = {}
            let buddiesJoiningSmallListWidget: CultBuddiesJoiningListSmallWidget
            if (socialDataEternalPromise) {
                let socialDataForClassIds = (await socialDataEternalPromise).obj
                socialDataForClassIds = (socialDataForClassIds) ? socialDataForClassIds.filter(squadData => squadData.classId.toString() === classId) : null
                if (socialDataForClassIds && !_.isEmpty(socialDataForClassIds)) {
                    const userIds = socialDataForClassIds.map(socialDataForClassId => socialDataForClassId.userId)
                    buddiesJoiningSmallListWidget = {
                        widgetType: "CULT_BUDDIES_JOINING_LIST_SMALL_WIDGET",
                        ...(await CultUtil.getBuddiesJoiningListSmallView(userIds, this.userCache, PageTypes.PreBookClass) as CultBuddiesJoiningListSmallView),
                    }
                }
            }
            if (cultClass.membership && cultClass.membership.centerID && AppUtil.isMembershipContextSupported(userContext, user.isInternalUser)) {
                const centerDetailsPromise = productType === "FITNESS" ? this.catalogueService.getCultCenter(cultClass.membership.centerID.toString()) : this.catalogueService.getCultMindCenter(cultClass.membership.centerID.toString())
                const centerDetails = await centerDetailsPromise
                cultMembershipDetails["centerName"] = centerDetails && centerDetails.name
            }
            if (cultClass.membership && cultClass.membership.cityID && AppUtil.isMembershipContextSupported(userContext, user.isInternalUser)) {
                const cityPromise = this.cityService.getCityByCultCityId(cultClass.membership.cityID)
                const city = await cityPromise
                cultMembershipDetails["cityName"] = city.name
            }

            const isWaitlistConfirmationProbabilitySupported = await AppUtil.isWaitlistConfirmationSupported(userContext, this.hamletBusiness)
            const isUnpauseAndBookClassCtaSupported = await CultUtil.isUnpauseAndBookClassCtaSupported(userContext, this.hamletBusiness)

            if (CultUtil.isUserPPC(cultClass) && CultUtil.isClassAvailableForPulse(cultClass, AppUtil.isCultPulseFeatureSupported(userContext)) && cultClass.PulseContext && cultClass.PulseContext.UserAccess) {
                const { type, remainingAccess } = cultClass.PulseContext.UserAccess
                const ppcUserHasBulkAccess = type === "bulk"
                const ppcUserHasNoAccess = type === "no_access" && remainingAccess === 0

                if (ppcUserHasBulkAccess || ppcUserHasNoAccess) {
                    // get Pulse Pack for the user and send to view builder
                    const pulsePacks = await this.catalogueService.getPulsePacks(true)
                    return new CultClassDetailView().buildView(
                        userContext,
                        cultClass,
                        wod,
                        productType,
                        user,
                        cultMembershipDetails,
                        (await userContext.userPromise).isInternalUser,
                        this.membershipService,
                        pulsePacks.filter(pack => pack.totalAccessCount === 1)[0],
                        buddiesJoiningSmallListWidget,
                        this.cultBusiness,
                        rescheduleSourceBookingNumber,
                        isWaitlistConfirmationProbabilitySupported,
                        isWaitlistExtensionSupported,
                        this.segmentService,
                        isUnpauseAndBookClassCtaSupported
                    )
                }
            }
            return new CultClassDetailView().buildView(
                userContext,
                cultClass,
                wod,
                productType,
                user,
                cultMembershipDetails,
                (await userContext.userPromise).isInternalUser,
                this.membershipService,
                undefined,
                buddiesJoiningSmallListWidget,
                this.cultBusiness,
                rescheduleSourceBookingNumber,
                isWaitlistConfirmationProbabilitySupported,
                isWaitlistExtensionSupported,
                this.segmentService,
                isUnpauseAndBookClassCtaSupported
            )
        }

        // TODO - Use v2 api and Remove this api once app version 7.31 gets adopted as this is being used in only app
        @httpGet("/class/v1/:classId")
        async getCultClassDetailV1(req: express.Request): Promise<CultClass & { message: string } & { wods?: IWODDetail }> {
            const cultclass = await this.getClassDetail(req)
            let wod: SimpleWod = null
            if (!_.isNil(cultclass.wodId)) {
                wod = await this.herculesService.getSimpleWodById(cultclass.wodId)
            }
            let wods: IWODDetail
            if (!_.isNil(wod)) {
                wods = this.wodViewBuilder.getView(wod)
            }
            const result = Object.assign({}, cultclass, { wods: wods })
            return result
        }

        @httpGet("/class/:classId")
        async getClassDetail(req: express.Request): Promise<CultClass & { message: string, widgets?: WidgetView[], actionTitle: string }> {
            const session: Session = req.session
            const classId: string = req.params.classId
            const userContext: UserContext = req.userContext as UserContext
            const cultClass: CultClass = await this.cultFitService.getCultClass(classId, session.userId, "CUREFIT_APP", false, userContext.userProfile.subUserId, undefined, session.deviceId)
            const { message, actionTitle } = this.getClassMessageAndActionTitle(cultClass, userContext)
            const widgets = []
            if (!_.isEmpty(cultClass.Workout.preWorkoutGears)) {
                const workoutKitWidget = CultUtil.getWorkoutKitWidget(cultClass.Workout.preWorkoutGears, null, cultClass.date, cultClass.Center.id)
                if (!_.isNil(workoutKitWidget)) {
                    widgets.push(workoutKitWidget)
                }
            }
            const result = Object.assign(cultClass, { message, widgets, actionTitle })
            return result
        }

        private getClassMessageAndActionTitle(cultClass: CultClass, userContext: UserContext): { message: string, actionTitle: string } {
            let message, actionTitle
            const tz = userContext.userProfile.timezone
            const classStartTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.startTime, tz, "h:mm")
            const classEndTime = TimeUtil.formatDateStringInTimeZone(cultClass.date + " " + cultClass.endTime, tz, "h:mm A")
            const classDate = TimeUtil.formatDateStringInTimeZone(cultClass.date, tz, "Do MMM")
            if (cultClass.cultAppAvailableSeats <= 0) {
                message = `${cultClass.Workout.name} ${classStartTime} slot is no longer available. Please choose another slot.`
            } else {
                switch (cultClass.classType) {
                    case "MEMBERSHIP":
                        message = `Book the ${cultClass.Workout.name} ${classStartTime}-${classEndTime} slot on ${classDate}?`
                        actionTitle = "CONFIRM & BOOK"
                        break
                    case "PAID":
                        message = `Buy your ${cultClass.Workout.name} ${classStartTime}-${classEndTime} slot on ${classDate}?`
                        actionTitle = "PROCEED TO PAY"
                        break
                    case "FREE":
                        message = `Book your free ${cultClass.Workout.name} ${classStartTime}-${classEndTime} slot on ${classDate}?`
                        actionTitle = "BOOK FOR FREE"
                        break
                    default:
                        message = "Confirm class booking"
                        actionTitle = "BOOK"
                }
            }
            if (cultClass.workoutID === KICKSTART_WORKOUT_ID) { // Special handling for kick starter workout
                message = message + "\n\n" + "Note: Kickstart is recommended for new members"
            }
            if (cultClass.centerID.toString() === "75") {
                message = message + "\n\n" + "Note: This class is for microsoft employees only"
            }
            if (cultClass.centerID.toString() === "76" || cultClass.centerID.toString() === "77") {
                message = message + "\n\n" + "Note: This class is only for employees working in ETV"
            }
            if (cultClass.workoutID === RUNNING_EVENT_WORKOUT_ID) { // Special handling for running event
                message = "This session will be at Cubbon Park." + "\n\n" + message
            }

            return {
                message,
                actionTitle
            }
        }
        @httpPost("/class/:classId/book",
            kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession,
            kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware).checkErrorCountMiddleware)
        async bookClass(req: express.Request): Promise<ConfirmationView> {
            const session: Session = req.session
            const classId: string = req.params.classId
            const userId: string = session.userId
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const codepushversion: number = Number(req.headers["codepushversion"])
            const osName: string = req.headers["osname"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userContext = req.userContext as UserContext
            const userAgent: UserAgent = _.get(userContext, "sessionInfo.userAgent", "APP")
            const pulseOptOut: boolean = req.query.pulseOptOut as boolean
            const rescheduleSourceBookingNumber: string = req.body.rescheduleSourceBookingNumber
            const user = await this.userCache.getUser(session.userId)
            const result = AppUtil.getUserAlertInfo(user, userContext)
            const code: string = result.code
            if (!_.isEmpty(code)) {
                throw this.errorFactory.withCode(result.code, result.alertInfo.statusCode).build()
            }
            let fitnessBooking
            try {
                const clientMetaData = {
                    subUserId: userContext.userProfile.subUserId,
                    enableCultPARQ: AppUtil.isCultParQEnabled(userAgent, appVersion, osName, codepushversion, user.isInternalUser),
                    rescheduleSourceBookingNumber,
                    attributionSource: session.sessionData.attributionSource,
                    deviceId: session.deviceId
                }
                const advertiserId: string = req.body.advertiserId
                const reqBody: any  = {
                    cultClassID: classId,
                    classVersion: "0",
                    amountPaid: 0,
                    source: orderSource,
                    clientMetadata: clientMetaData,
                    advertiserId,
                    pulseOptOut
                }
                fitnessBooking = await this.cultFitService.createFitnessBooking(reqBody, userId, "CUREFIT_API", session.deviceId)
                this.logger.debug("fitness booking got response ", {fitnessBooking})
            } catch (ex) {
                this.logger.info("error fitness booking got response ", {ex})
                await this.cultBusiness.checkForParQ(ex, userContext, "cult")
            }
            const params: ConfirmationRequestParams = {
                orderSource,
                userContext: userContext
            }
            if (OrderUtil.isNewOrderConfirmationScreenSupported(osName, appVersion, codepushversion) || AppUtil.isTVAppWithApiKey(apiKey)) {
                return await this.orderConfirmationViewBuilderV1.buildCultClassConfirmationView(req.userContext, fitnessBooking.id.toString(), fitnessBooking, userId, params)
            }
            const confirmationView = await this.orderConfirmationViewBuilder.buildCultClassConfirmationView(req.userContext, fitnessBooking.id.toString(), fitnessBooking, userId)
            return confirmationView
        }

        @httpPost("/booking/:bookingId/cancel")
        cancelBooking(req: express.Request): Promise<ProductDetailPage> {
            const session: Session = req.session
            const bookingId: string = req.params.bookingId
            const userId: string = session.userId
            const userContext = req.userContext as UserContext

            return this.cultFitService.cancelBookingV2(bookingId, session.userId, "CUREFIT_APP", userContext.userProfile.subUserId, session.deviceId).then(() => {
                return this.getCultBookingDetail(userContext, bookingId, userId)
            })
        }

        @httpPost("/booking/v2/:bookingNumber/cancel")
        async cancelBookingV2(req: express.Request) {
            const session: Session = req.session
            const bookingNumber: string = req.params.bookingNumber
            const userId: string = session.userId
            const productType: ProductType = req.body.productType
            const userContext = req.userContext as UserContext
            const fulfilmentId: string = req.body.cultCafeFulfilmentId
            const date: string = req.body.date

            const bookingData = await this.cultService.getBookingDetailsByBookingNumber({appName: "CUREFIT_APP", bookingNumber})
            if (bookingData?.CultClass?.startDateTimeUTC) {
                const classTimeInMs = (new Date("" + bookingData.CultClass.startDateTimeUTC + " UTC")).valueOf()
                const currentTimeInMs = (new Date()).valueOf()
                const classTimeDiffInMin = CultUtil.convertMillisToMinutes(Math.abs(currentTimeInMs - classTimeInMs))
                this.logger.info(`dropoutBooking ${userId}`, {bookingData, classTimeDiffInMin, classTimeInMs, currentTimeInMs, startDateTimeUTC: bookingData.CultClass.startDateTimeUTC})
                if (bookingData?.CultClass?.Center?.City?.id === 3 && classTimeDiffInMin > 30 && classTimeDiffInMin <= 60) {
                    const cancelResponse = await this.cultFitService.cancelBookingV2(bookingNumber, userId, "CUREFIT_APP", userContext.userProfile.subUserId, session.deviceId, true)
                    return this.getCancelCompletionActionAndAlertInfo(userContext, cancelResponse, fulfilmentId, date)
                }
            }
            const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
            const cancelResponse = await baseService.cancelBookingV2(bookingNumber, userId, "CUREFIT_APP", userContext.userProfile.subUserId, session.deviceId)
            return this.getCancelCompletionActionAndAlertInfo(userContext, cancelResponse, fulfilmentId, date)
        }

        @httpPost("/booking/cancelReason")
        async updateClassCancellationResponse(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userId: string = session.userId
            const response = await this.cultFitService.updateUserResponseForCancellation(req.body, userId, "CUREFIT_API")
            return response
        }

        @httpPost("/class/waitlistnotification")
        async updateUserWaitlistNotificationDetails(req: express.Request): Promise<{success: boolean}> {
            const session: Session = req.session
            const userId: string = session.userId
            const response = await this.cultFitService.updateUserWaitlistNotificationDetails(req.body, userId, "CUREFIT_API")
            return response
        }

        // TODO - Use v2 api and Remove this api once app version 7.31 gets adopted as this is being used in only app
        @httpGet("/mind/class/v1/:classId")
        async getMindClassDetailV1(req: express.Request): Promise<CultClass & { message: string } & { wods?: IWODDetail }> {
            const cultclass = await this.getMindClassDetail(req)
            let wod: SimpleWod = null
            try {
                wod = await this.herculesService.getSimpleWodById(cultclass.wodId)
            }
            catch (error) {
                this.logger.error(error)
            }
            let wods: IWODDetail
            if (!_.isNil(wod)) {
                wods = this.wodViewBuilder.getView(wod)
            }
            const result = Object.assign({}, cultclass, { wods: wods })
            return result
        }

        @httpGet("/mind/class/:classId")
        async getMindClassDetail(req: express.Request): Promise<CultClass & { message: string, widgets: WidgetView[], actionTitle: string }> {
            const session: Session = req.session
            const classId: string = req.params.classId
            const userContext: UserContext = req.userContext as UserContext
            const cultClass: CultClass = await this.mindFitService.getCultClass(classId, session.userId, "CUREFIT_APP",
                false, userContext.userProfile.subUserId, undefined, session.deviceId)
            const { message, actionTitle } = this.getClassMessageAndActionTitle(cultClass, userContext)
            const widgets = []
            if (!_.isEmpty(cultClass.Workout.preWorkoutGears)) {
                const workoutKitWidget = CultUtil.getWorkoutKitWidget(cultClass.Workout.preWorkoutGears, null, cultClass.date, cultClass.Center.id)
                if (!_.isNil(workoutKitWidget)) {
                    widgets.push(workoutKitWidget)
                }
            }
            const result = Object.assign(cultClass, { message: message, widgets, actionTitle })
            return result
        }

        @httpPost("/class/:classId/waitlist", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async joinWaitlistClass(req: express.Request): Promise<ConfirmationView> {
            const session: Session = req.session
            const classId: string = req.params.classId
            const productType: ProductType = req.body.productType
            const userId: string = session.userId
            const apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string
            const rescheduleSourceBookingNumber: string = req.body.rescheduleSourceBookingNumber
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            const userContext = req.userContext as UserContext
            const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
            let waitlistBooking
            const isWaitlistExtensionSupported = await AppUtil.isWaitlistExtensionSupported(userContext, this.hamletBusiness)
            let wlNotificationTime = req.body.wlNotificationTime
            if (!wlNotificationTime) {
                try {
                    const lastWaitlistPreference = await baseService.getUserWaitlistNotificationDetails({classID: classId}, userId, "CUREFIT_API")
                    wlNotificationTime = lastWaitlistPreference?.slot ?? 30
                } catch (err) {
                    wlNotificationTime = 30
                    this.logger.error(err)
                }
            }
            try {
                const clientMetadata = {
                    rescheduleSourceBookingNumber,
                    enableCultPARQ: AppUtil.isCultParQEnabled(userContext.sessionInfo.userAgent, userContext.sessionInfo.appVersion, userContext.sessionInfo.osName, userContext.sessionInfo.cpVersion),
                    wlNotificationTime: wlNotificationTime
                }
                const reqBody = {
                    cultClassID: classId,
                    classVersion: "0",
                    source: orderSource,
                    clientMetadata
                }
                waitlistBooking = await baseService.createWaitlistBooking(reqBody, userId, "CUREFIT_API", session.deviceId)

                // Fetch last waitlist preference if app didn't pass this
                if (!wlNotificationTime) {
                    try {
                    const lastWaitlistPreference = await baseService.getUserWaitlistNotificationDetails({classID: classId}, userId, "CUREFIT_API")
                    wlNotificationTime = lastWaitlistPreference?.slot ?? 30
                    } catch (err) {
                        wlNotificationTime = 30
                        this.logger.error(err)
                    }
                }

                if (isWaitlistExtensionSupported && wlNotificationTime) {
                    const notificationBody = {
                        classID: classId,
                        slot: wlNotificationTime,
                    }
                    await baseService.updateUserWaitlistNotificationDetails(notificationBody, userId, "CUREFIT_API")
                }
            } catch (ex) {
                this.logger.error(ex)
                await this.cultBusiness.checkForParQ(ex, userContext, "cult")
            }
            const params: ConfirmationRequestParams = {
                orderSource: orderSource,
                userContext: userContext
            }
            return this.orderConfirmationViewBuilderV1.buildWaitlistClassConfirmationView(waitlistBooking.wlBookingNumber, productType, userContext, params)
        }

        @httpPost("/booking/:bookingNumber/dropout")
        async dropoutBooking(req: express.Request) {
            const session: Session = req.session
            const bookingNumber: string = req.params.bookingNumber
            const userId: string = session.userId
            const productType: ProductType = req.body.productType
            const userContext = req.userContext as UserContext
            const fulfilmentId: string = req.body.cultCafeFulfilmentId
            const date: string = req.body.date
            // const bookingData = await this.cultService.getBookingDetailsByBookingNumber({appName: "CUREFIT_APP", bookingNumber})
            // const classTimeInMs = (new Date("" + bookingData.CultClass.startDateTimeUTC + " UTC")).valueOf()
            // const currentTimeInMs = (new Date()).valueOf()
            // const classTimeDiffInMin = CultUtil.convertMillisToMinutes(Math.abs(currentTimeInMs - classTimeInMs))
            // this.logger.info(`dropoutBooking ${userId}`, {bookingData, classTimeDiffInMin, classTimeInMs, currentTimeInMs, startDateTimeUTC: bookingData.CultClass.startDateTimeUTC})
            // if (bookingData?.CultClass?.Center?.City?.id === 3 && classTimeDiffInMin > 30 && classTimeDiffInMin <= 60) {
            //     const cancelResponse = await this.cultFitService.cancelBookingV2(bookingNumber, userId, "CUREFIT_APP", userContext.userProfile.subUserId, session.deviceId, true)
            //     return this.getCancelCompletionActionAndAlertInfo(userContext, cancelResponse, fulfilmentId, date)
            // }
            const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
            const dropoutResponse = await baseService.dropoutBooking(bookingNumber, userId, "CUREFIT_APP", userContext.userProfile.subUserId, session.deviceId)
            const alertInfo = await this.cancelCafeFulfillment(userContext, fulfilmentId, date, "DROPOUT", dropoutResponse?.data?.booking?.creditCost ?? null)
            let completionAction
            if (dropoutResponse.data && dropoutResponse.data.booking) {
                try {
                    completionAction = CultUtil.getDeleteCalendarEventAction(userContext, dropoutResponse.data.booking)
                } catch (error) {
                    this.logger.error("Error in delete calendar event action dropout: ", error)
                }
            }
            return { alertInfo, completionAction }
        }

        @httpGet("/transfermembership")
        async getTransferMembership(req: express.Request): Promise<{ widgets: WidgetView[] }> {
            const session: Session = req.session
            const userId: string = session.userId
            const membershipId = req.query.membershipId
            const cityId: string = session.sessionData.cityId
            const cultCity = await this.cityService.getCityById(cityId)
            const packId = req.query.packId
            const productType = req.query.productType
            const userContext: UserContext = req.userContext as UserContext
            const fitnessTransferPacks = await this.cultFitService.browsePackFitnessTransfers("CUREFIT_APP", userId, cultCity.cultCityId, true) // PMS::TODO Move to PMS
            return this.transferMembershipViewBuilder.buildView(membershipId, packId, fitnessTransferPacks, productType, userContext)
        }

        @httpGet("/transfermembership/details")
        async transferPackToOtherUser(req: express.Request): Promise<ActionPageWidgetView> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const { membershipTransferType, productType, selectedCityId, selectedUserId, selectedCenterId, membershipId, packId, transferProductId, transferId } = req.query

            // Disable another user membership transfer.
            if (membershipTransferType === "ANOTHER_USER") {
                throw this.errorFactory.withCode(ErrorCodes.CULT_ANOTHER_USER_TRANSFER_NOT_ALLOWED, 403).build()
            }

            if (selectedCityId && CITY_SPLIT_ENABLED_CITY_IDS.includes(selectedCityId) && await AppUtil.isCitySplitFeatureSupported(userContext) && !AppUtil.isCitySplitSelectionSupported(userContext)) {
                const osName = userContext.sessionInfo.osName
                throw osName.toLowerCase() === "ios"
                    ? this.errorFactory.withCode(ErrorCodes.CULT_CITY_TRANSFER_NOT_ALLOWED_IOS, 403).build()
                    : this.errorFactory.withCode(ErrorCodes.CULT_CITY_TRANSFER_NOT_ALLOWED_ANDROID, 403).build()
            }

            const packProductId = productType === "MIND" ? CatalogueServiceV2Utilities.getMindPackProductId(packId) : CatalogueServiceV2Utilities.getCultPackProductId(packId)
            return this.transferMembershipViewBuilder.transferDetails(membershipId, productType, userId, membershipTransferType, transferProductId, transferId, selectedUserId, selectedCityId, selectedCenterId)
        }

        @httpGet("/transfermembership/addMemberDetails")
        async addMemberDetails(req: express.Request): Promise<AddTransferMemberDetails> {
            return this.transferMembershipViewBuilder.addMemberDetails()
        }

        // Deprecated
        @httpGet("/upgrademembership")
        async upgradeMembershipDetails(req: express.Request): Promise<{ widgets: Array<any> }> {
            const userId: string = req.session.userId as string
            const membershipId: string = req.query.membershipId as string
            const packId: string = req.query.packId as string
            const productType: ProductType = req.query.productType as ProductType
            const centerId: number = req.query.centerId as number
            this.logger.error("Deprecated endpoint called: /cult/upgrademembership", {userId, membershipId, packId, productType, centerId})
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Deprecated endpoint").build()
            // const destinationPacks: Array<CultUpgradePack> = await this.cultFitService.browseUpgradePacks("CUREFIT_APP", userId)
            // const isMembershipServiceId: boolean = productType === "GYMFIT_FITNESS_PRODUCT" || productType === "FITNESS"
            // /**
            //  * We want to make it extensible in future
            //  * To allow users to select a specific pack
            //  * Hence Upgrade target is returned as an array
            //  * For now, we'll use the first element of the array
            //  */
            // const destinationPack = destinationPacks[0]
            // if (!destinationPack) {
            //     throw this.errorFactory.withCode(ErrorCodes.CULT_UPGRADE_MEMBERSHIP_TARGET_PACK_NOT_FOUND_ERR, 400).withDebugMessage("Target pack not found while trying to upgrade membership").build()
            // }
            // /**
            //  * Since target of a Membership Upgrade is always to a Cult center
            //  * We would only concern ourselves only with CultCenters
            //  */
            // let cultCenter: CultCenter
            // if (centerId) {
            //     // we can get a cult center
            //     cultCenter = await this.catalogueService.getCultCenter(String(centerId))
            // }
            // const originalPack = await this.catalogueServicePMS.getProduct(packId)
            //     : productType === "GYMFIT_FITNESS_PRODUCT"
            //         ? await this.catalogueService.getGymfitFitnessProductById(packId)
            //         : await this.catalogueService.getMindPack(packId)
            // const originalMembership = isMembershipServiceId ? await this.membershipService.getMembershipById(Number(membershipId)) : await this.cultFitService.getMembershipById(Number(membershipId), req.session.userId, "CUREFIT_APP")

            // return this.upgradeMembershipViewBuilder.buildView(originalMembership, originalPack, destinationPack, cultCenter, isMembershipServiceId)
        }

        @httpGet("/auditTrail/:membershipId")
        async auditTrail(req: express.Request): Promise<any> {
            const membershipId: CultMembership["id"] = req.params.membershipId as CultMembership["id"]
            const userContext: UserContext = req.userContext as UserContext
            const userId: User["id"] = userContext.userProfile.userId as User["id"]

            // pagination
            const pageNumber: number = req.query.pageNumber as number

            if (pageNumber) {
                const moreAuditEventResponse = await this.cultFitService.getMembershipAuditEvents(
                    userId,
                    String(membershipId),
                    pageNumber,
                    CultUtil.getMembershipAudientEventLimit(),
                    "CUREFIT_APP"
                )
                // const moreAuditEventResponse: MembershipAuditEventType[] = [] as MembershipAuditEventType[]

                return this.membershipAuditTrailViewBuilder.buildMoreEventsView(moreAuditEventResponse, userContext)

            }
            /**
             * Make a request to Audit Trail API with user ID and membership ID
             */
            // const auditTrailResponse = await this.cultFitService.getMembershipAudit(userId, String(membershipId), "CUREFIT_APP")
            // const auditTrailResponse = {membershipDetails: {packName: ""}} as MembershipAudit

            const auditTrailResponse = await this.cultFitService.getMembershipAudit(userId, String(membershipId), "CUREFIT_APP")


            /**
             * Construct view builder with response
             */
            return this.membershipAuditTrailViewBuilder.buildView(auditTrailResponse, userContext)
        }

        @httpPost("/sendOtpToMember")
        async sendMemberOtp(req: express.Request): Promise<any> {
            const mobileNumber = req.body.mobileNumber
            const countryCallingCode = req.body.countryCallingCode || "+91"
            const medium = "sms"
            const session: Session = req.session
            const userId: string = session.userId
            if (session.sessionData.membershipTransferFailedOtpTry) {
                if (session.sessionData.membershipTransferFailedOtpTry > 3 && (new Date().getTime() - session.sessionData.membershipTransferLastFailedOtpTime.getTime()) / (60 * 1000) < 1) {
                    throw this.errorFactory.withCode(ErrorCodes.OTP_LIMIT_REACHED_ERR, 400).withDebugMessage("OTP limit reached").build()
                }
            }
            return this.cultFitService.sendOtpForMembershipTransfer(mobileNumber, countryCallingCode, medium, userId, "CUREFIT_APP").then(() => {
                return this.transferMembershipViewBuilder.enterMemberOtpDetailsView(mobileNumber)
            })
        }

        @httpPost("/resendOtpToMember")
        async resendMemberOtp(req: express.Request): Promise<any> {
            const mobileNumber = req.body.mobileNumber
            const countryCallingCode = req.body.countryCallingCode || "+91"
            const medium: OTP_MEDIUM = req.body.medium || "sms"
            const session: Session = req.session
            const userId: string = session.userId
            if (session.sessionData.membershipTransferFailedOtpTry) {
                if (session.sessionData.membershipTransferFailedOtpTry > 3 &&
                    (new Date().getTime() - session.sessionData.membershipTransferLastFailedOtpTime.getTime()) / (60 * 1000) < 5) {
                    throw this.errorFactory.withCode(ErrorCodes.OTP_LIMIT_REACHED_ERR, 400).withDebugMessage("OTP limit reached").build()
                }
            }
            return await this.cultFitService.sendOtpForMembershipTransfer(mobileNumber, countryCallingCode, medium, userId, "CUREFIT_APP")
        }

        @httpPost("/confirmAndValidateCurefitMember")
        async validateCurefitUser(req: express.Request): Promise<ActionPageWidgetView> {
            const session: Session = req.session
            const userId: string = session.userId
            const mobileNumber = req.body.mobileNumber
            const countryCallingCode = req.body.countryCallingCode || "+91"
            const otp: string = req.body.otp
            return this.cultFitService.verifyOTPForMembershipTransfer(mobileNumber, countryCallingCode, otp, userId, "CUREFIT_APP").then(async () => {
                session.sessionData.membershipTransferFailedOtpTry = 0
                await this.sessionBusiness.updateSessionData(session.at, session.sessionData)
                return this.cultFitService.validateUserForMembershipTransfer(mobileNumber, countryCallingCode, userId, "CUREFIT_APP").then((response) => {
                    return this.transferMembershipViewBuilder.memberDetailsView(response, mobileNumber)
                })
            }).catch(reason => {
                session.sessionData.membershipTransferFailedOtpTry = (session.sessionData.membershipTransferFailedOtpTry) ? session.sessionData.membershipTransferFailedOtpTry + 1 : 1
                session.sessionData.membershipTransferLastFailedOtpTime = new Date()
                return this.sessionBusiness.updateSessionData(session.at, session.sessionData).then(updatedSession => {
                    return Promise.reject(reason)
                })
            })
        }

        @httpPost("/booking/:bookingNumber/ivr")
        async setIvr(req: express.Request): Promise<{ error: { message: string } } | CallReminderSlotInfo> {
            const session: Session = req.session
            const bookingNumber: string = req.params.bookingNumber
            const userId: string = session.userId
            const status = req.body.status
            const minutesBefore = req.body.minsBefore
            const userContext = req.userContext
            const productType = req.body.productType
            const success = await this.cultFitService.setIVRForBooking({ userId, appName: "CUREFIT_APP", bookingNumber, status, minutesBefore })
            if (success) {
                const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
                const bookingResponse: CultBookingResponse = await baseService.getBookingV2(bookingNumber, userId, true, "CUREFIT_APP", userContext.userProfile.subUserId)
                const booking = bookingResponse.booking || bookingResponse.waitlist
                return this.bookingDetailViewBuilderV2.getCallReminderSlots(booking.ivrConfig, booking.CultClass.startTime, bookingNumber, (booking.wlBookingNumber !== null && booking.wlBookingNumber !== undefined))
            }
            return { error: { message: "Call reminder settings could not be changed" } }
        }

        @httpGet("/classBegin")
        async getClassBegin(req: express.Request): Promise<any> {
            const userContext = req.userContext
            const { type } = req.query
            if (type === "PT_AT_HOME") {
                return this.ptathomeViewBuilder.getLandingPage(userContext)
            }
            return this.nuxViewBuilder.getLandingPage(userContext)
        }

        @httpGet("/classRecommendationSteps")
        async getClassRecommendationSteps(req: express.Request): Promise<any> {
            const userContext = req.userContext
            const { type } = req.query
            if (type === "PT_AT_HOME" || type === "SGT") {
                return this.ptathomeViewBuilder.getRecommendationSteps(userContext, type)
            }
            return this.nuxViewBuilder.getRecommendationSteps(userContext)
        }

        @httpGet("/classRecommendationStatus")
        async classRecommendationGenerated(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const classRecommendationStatus = await this.cultFitService.getNuxClassRecommendationStatus(userId, "CUREFIT_APP")
            return classRecommendationStatus
        }

        @httpGet("/classRecommendation")
        async classRecommendation(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const classRecommendation = await this.cultFitService.getNuxClassRecommendations(userId, "CUREFIT_APP")
            return this.nuxViewBuilder.getRecommendationsViewForFirstClass(classRecommendation)
        }

        @httpPost("/createNuxRecommendation")
        async createNuxRecommendation(req: express.Request): Promise<any> {
            const userId: string = req.session.userId
            const requestBody: any = req.body
            const userContext = req.userContext
            const cultCityId = userContext.userProfile.city.cultCityId
            const nuxResponse = await this.cultFitService.createNUXRecommendationForUser(requestBody, userId, "CUREFIT_APP", cultCityId)
            return true
        }

        @httpPost("/bookingNudges/submitAction")
        async bookingNudgesSubmitAction(req: express.Request): Promise<any> {
            const userId: string = req.session.userId
            const requestBody: any = req.body
            const userContext = req.userContext
            const cultCityId = userContext.userProfile.city.cultCityId
            const submitResponse = await this.cultFitService.recordActionTakenByUser(requestBody, userId, "CUREFIT_APP", cultCityId)
            return true
        }

        @httpGet("/bookings/preWorkoutClassTips")
        async firstClassPreWorkoutTips(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const preWorkoutResponse = await this.cultFitService.getPreWorkoutFirstClassTips(userId, "CUREFIT_APP")
            const { booking: { preWorkoutTips, Center } } = preWorkoutResponse
            return this.nuxViewBuilder.getPreWorkoutTips(preWorkoutTips, Center)
        }

        @httpGet("/bookings/postWorkoutClassTips")
        async firstClassPostWorkoutTips(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const postWorkoutTips = await this.cultFitService.getPostWorkoutFirstClassTips(userId, "CUREFIT_APP")
            return this.nuxViewBuilder.getPostWorkoutClassTips(postWorkoutTips, userId)
        }

        @httpGet("/livePtOnboarding")
        async getPtOnboardingSteps(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const nextAction: string = req.query.nextAction
            const patientId: number = req.query.patientId
            const doctorType: DOCTOR_TYPE = req.query.doctorType
            const bookingId: number = req.query.bookingId
            const zoomParticipantId: DOCTOR_TYPE = req.query.zoomParticipantId
            const userContext: UserContext = req.userContext as UserContext
            const user = await this.userCache.getUser(req.session.userId)
            return this.ptathomeViewBuilder.getLivePtOnboarding(nextAction, userContext, user, patientId, doctorType, bookingId, zoomParticipantId)
        }

        @httpGet("/memories")
        async getCultMemoriesPaginated(req: express.Request): Promise<CultMemoriesResponse> {
            const pageNumber: number = Number(req.query.pageNumber || 1)
            const pageSize: number = Number(req.query.pageSize || 10)
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const response = await this.cultFitService.getCultMoments({ userId, appName: "CUREFIT_API", pageNumber, pageSize })
            return this.cultMemoriesViewBuilder.getMemories(userContext, response, pageNumber, pageSize)
        }
        @httpGet("/memories/v2")
        async getCultMemoriesV2Paginated(req: express.Request): Promise<SocialMemoriesListPage> {
            const pageNumber: number = Number(req.query.pageNumber || 1)
            const pageSize: number = Number(req.query.pageSize || 10)
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const response = await this.cultFitService.getCultMomentsGroupedByClass({ userId, appName: "CUREFIT_API", pageNumber, pageSize })
            return this.cultMomentsViewBuilder.getMemoriesListView(userContext, response, pageNumber, pageSize)
        }

        @httpGet("/moments/:classId")
        async getCultMomentsByClass(req: express.Request): Promise<any> {
            const pageNumber: number = Number(req.query.pageNumber || 1)
            const pageSize: number = Number(req.query.pageSize || 10)
            const classId: number = Number(req.params.classId)
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const response = await this.cultFitService.getCultMomentsForClass({ userId, classId, appName: "CUREFIT_API", pageNumber, pageSize })
            return this.cultMomentsViewBuilder.buildView(response, userContext)
        }

        @httpGet("/usersProfileByClass")
        async getUsersProfileForClass(req: express.Request): Promise<any> {
            const classId: number = Number(req.query.classId)
            const userContext: UserContext = req.userContext as UserContext
            const selectedProfileIndex = Number(req.query.profileIndex)
            const userId: string = userContext.userProfile.userId
            const usersProfile = await this.cultFitService.getUserProfilesForClass({ classId, userId, appName: "CUREFIT_API" })
            return this.cultMomentsViewBuilder.getUsersProfileByClass(usersProfile, selectedProfileIndex, userContext)
        }

        @httpPut("/social/profileUpdate")
        async updateSocialProfile(req: express.Request): Promise<any> {
            const cultUserProfile: UserProfileEntry = req.body.cultUserProfile
            const userContext: UserContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            const userSummary = await this.cultFitService.getUserProfileByUserId({ userId: userId, appName: "CUREFIT_APP" })
            const profileId: number = userSummary.id
            const response = await this.socialService.patchUpdateUserProfile(profileId, cultUserProfile)
            return true
        }

        @httpPost("/resendParqEmail")
        async resendParqEmail(req: express.Request): Promise<{ success: boolean }> {
            const session: Session = req.session
            const userId: string = session.userId
            const { sent } = await this.cultFitService.resendParqMail(userId, "CUREFIT_APP")
            return { success: sent }
        }

        @httpPost("/notifyCenterReopen")
        async notifyCenterReopen(req: express.Request): Promise<{ title: string, subtitle: string }> {
            const session: Session = req.session
            const userId: string = session.userId
            const centerIds = req.body.centerIds
            const { message } = await this.cultFitService.createCenterStatusAlertsForUser(userId, centerIds, "CUREFIT_APP")
            if (centerIds.length > 1) {
                return { title: "Alert set successfully", subtitle: "We will notify you if one of the center opens" }
            }
            return { title: "Alert set successfully", subtitle: "We will notify you once the center opens" }
        }

        @httpPost("/ogm")
        async createOgm(req: express.Request): Promise<OgmDetail & { action: string }> {
            const session: Session = req.session
            const creatOgmRequest: CreateOgmRequest = req.body
            const userId: string = session.userId
            const cityId: string = session.sessionData.cityId
            const productType: ProductType = req.body.productType
            const userContext = req.userContext
            const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            let action
            let ogmDetail
            if (productType === "FITNESS") {
                action = ActionUtil.cultPackBrowse(session.userAgent, true)
                ogmDetail = await this.cultFitService.createOgm(userId, cultCityId, creatOgmRequest)
            } else if (productType === "MIND") {
                action = ActionUtil.mindPackBrowse(session.userAgent, true)
                ogmDetail = await this.mindFitService.createOgm(userId, cultCityId, creatOgmRequest)
            }
            return Object.assign({}, { action: action }, ogmDetail)
        }

        @httpGet("/ogm")
        async getOgm(req: express.Request): Promise<OgmDetail> {
            const session: Session = req.session
            const userId: string = session.userId
            const cityId: string = session.sessionData.cityId
            const productType: ProductType = req.query.productType
            const userContext = req.userContext
            const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            if (productType === "FITNESS") {
                return this.cultFitService.ogmDetail(userId, cultCityId)
            } else if (productType === "MIND") {
                return this.mindFitService.ogmDetail(userId, cultCityId)
            } else {
                return undefined
            }

        }

        @httpPost("/ogm/:id/discard")
        async discardOgm(req: express.Request): Promise<{ status: boolean }> {
            const session: Session = req.session
            const ogmId: string = req.params.id
            const userId: string = session.userId
            const productType: ProductType = req.body.productType
            const cityId: string = session.sessionData.cityId
            const userContext = req.userContext
            const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            if (productType === "FITNESS") {
                return this.cultFitService.discardOgm(userId, cultCityId, ogmId)
            } else if (productType === "MIND") {
                return this.mindFitService.discardOgm(userId, cultCityId, ogmId)
            } else {
                return undefined
            }
        }

        @httpGet("/ogm/calculateDays")
        async ogmCalculateDays(req: express.Request): Promise<{ days: number }> {
            const session: Session = req.session
            const userId: string = session.userId
            const productType: ProductType = req.query.productType
            const otherGymMembershipStartDate: string = req.query.otherGymMembershipStartDate
            const otherGymMembershipExpiryDate: string = req.query.otherGymMembershipExpiryDate
            const otherGymMembershipAmountPaid: number = req.query.otherGymMembershipAmountPaid
            const cityId: string = session.sessionData.cityId
            const userContext = req.userContext
            const cultCityId: number = await this.cultBusiness.getCultCityId(userContext, cityId, userId)

            if (productType === "FITNESS") {
                return this.cultFitService.ogmCalculateDays(userId, cultCityId, otherGymMembershipStartDate,
                    otherGymMembershipExpiryDate, otherGymMembershipAmountPaid)
            } else if (productType === "MIND") {
                return this.mindFitService.ogmCalculateDays(userId, cultCityId, otherGymMembershipStartDate,
                    otherGymMembershipExpiryDate, otherGymMembershipAmountPaid)
            } else {
                return undefined
            }
        }

        @httpGet("/cafe/getActiveKioskId")
        async getActiveKioskIdAction(req: express.Request): Promise<{ action: string }> {
            const session: Session = req.session
            const userId: string = session.userId
            const startDate = TimeUtil.todaysDate(req.userContext.timezone)
            const endDate = TimeUtil.addDays(req.userContext.timezone, startDate, 7)
            const bookingResponse = await this.cultFitService.bookingsV2(userId, endDate, startDate)
            if (_.get(bookingResponse, userId)) {
                let clpUrl: string
                const cultBookingsResponse = bookingResponse[userId]
                cultBookingsResponse.bookings.sort((a: CultBooking, b: CultBooking) => {
                    const date1 = TimeUtil.parseDate(a.CultClass.date, req.userContext.timezone)
                    const date2 = TimeUtil.parseDate(b.CultClass.date, req.userContext.timezone)
                    return date1.getTime() < date2.getTime() ? -1 : 1
                }).forEach((booking => {
                    const centerID = String(booking.CultClass.centerID)
                    const kioskId = this.kiosksDemandService.getKioskIdGivenCenterId(centerID)
                    if (!_.isNil(kioskId) && _.isNil(clpUrl)) {
                        clpUrl = AppActionUtil.getEatClpUrl(kioskId, booking, req.userContext.timezone, undefined)
                        return
                    }
                }))
                if (!_.isNil(clpUrl)) {
                    return { action: clpUrl }
                }
            }
            return { action: undefined }
        }

        @httpGet("/fitnessReport")
        async getDetailedFitnessReport(req: express.Request): Promise<FitnessReportPageView> {
            let startDate = req.query.startDate // format: YYYY-MM-DD
            if (!moment(startDate, "YYYY-MM-DD", true).isValid()) {
                startDate = moment().startOf("isoWeek").subtract(1, "week").format("YYYY-MM-DD")
            }
            const endDate = req.query.endDate
            const productType = req.query.productType
            const reportType: ReportType = req.query.reportType
            const userContext: UserContext = req.userContext as UserContext
            const reportData = await this.cultFitService.getWeeklyFitnessReport(startDate, userContext.userProfile.userId, "CUREFIT_API")
            if (reportType === "WEEKLY") {
                return this.fitnessReportPageViewBuilder.buildWeeklyReportView(userContext, reportData, reportType, startDate, productType)
            }
        }

        @httpGet("/fitnessReportProfile/signedUrl")
        async getProfilePictureUploadSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            const bucketName = (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ? "cult-api-fitness-report-pictures-stage" : "cult-api-fitness-report-pictures-prod"
            return this.mediaGatewayClient.getPresignedPutUrl({
                bucketName,
                path: "",
                fileName,
                contentType : MediaTypeMediaGateway.IMAGE,
                maxUploadSize : 8097152, // need to be decided
                objectAcl : ObjectAcl.PUBLIC_READ
            })
        }

        @httpPost("/updateFitnessReportById")
        async uploadProfilePicture(req: express.Request): Promise<any> {
            const reportId = req.body.reportId
            const userId: string = req.session.userId
            const fileName: string = req.body.fileName && await this.mediaGatewayClient.validateAndGetDestinationUrl(req.body.fileName)
            const feedback: string = req.body.feedback
            const imageFilter: string = req.body.imageFilter
            const fitnessReport = await this.cultFitService.updateFitnessReportById(reportId, userId, "CUREFIT_APP", {
                imageUrl: fileName,
                imageFilter: imageFilter
            })
            return true
        }

        @httpGet("/findCenter")
        async findCenter(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const { userProfile, sessionInfo } = userContext
            const cityId = userProfile.cityId
            const userId = userProfile.userId
            const resolveLocationName: boolean = req.query.resolveLocationName === "true"
            const productType = req.query.productType

            const isCultCenterReopenPageSupported = await CultUtil.isCultCenterReopenPageSupported(userContext)

            if (!isCultCenterReopenPageSupported) {
                const osName = sessionInfo.osName.toLowerCase()
                let errorCode
                if (osName === "android") {
                    errorCode = ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR
                } else if (osName === "ios") {
                    errorCode = ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR
                }
                throw this.errorFactory.withCode(errorCode, HTTP_CODE.FORBIDDEN).build()
            }

            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)

            const lat = !_.isNil(req.query.centerSearchLat) ? parseFloat(req.query.centerSearchLat) : (sessionInfo.lat ? sessionInfo.lat : userProfile.city.representativeLatLong.lat)
            const lon = !_.isNil(req.query.centerSearchLon) ? parseFloat(req.query.centerSearchLon) : (sessionInfo.lon ? sessionInfo.lon : userProfile.city.representativeLatLong.long)

            const nearByCentersInfo: NearByCentersInfo = await this.cultBusiness.getNearbyCenters(userId, cultCityId, lat, lon, productType, userContext, true)

            const place = resolveLocationName ? await LocationUtil.getLocationData(this.locationService, lat, lon) : undefined
            const searchHeader: Header = {
                title: resolveLocationName ? (place && !_.isEmpty(place.name) ? place.name : "Click here to search for your locality") : undefined,
                seemore: { actionType: "SHOW_CULT_FIND_CENTER_SEARCH_MODAL", meta: { title: "Search" } }
            }
            const isCenterAwayFromCity = nearByCentersInfo.isCenterAwayFromCity

            return {
                lat: lat,
                lon: lon,
                centerList: nearByCentersInfo.centers,
                nearByCenterIndex: nearByCentersInfo.nearByCenterIndex,
                searchHeader: searchHeader,
                errorMessage: isCenterAwayFromCity ? "Location selected is outside your current city. Change city here to see centers in your preferred location" : undefined
            }
        }

        @httpGet("/workouts/v2/all")
        async getAllWorkouts(req: express.Request): Promise<CultWorkoutPage> {
            const userContext: UserContext = req.userContext as UserContext
            const centerIds: string[] = !_.isEmpty(req.query.centerIds) ? req.query.centerIds.split(",") : []
            const productType: ProductType = req.query.productType
            return this.cultWorkoutsViewBuilder.getView(userContext, centerIds, productType)
        }

        @httpGet("/workout/v2/:workoutId")
        async getWorkoutDetail(req: express.Request): Promise<CultWorkoutDetailPage> {
            const userContext: UserContext = req.userContext as UserContext
            const workoutId: string = req.params.workoutId
            const workoutFamilyId: string = req.query.workoutFamilyId
            const productType: ProductType = req.query.productType
            const centerIds: string[] = !_.isEmpty(req.query.centerIds) ? req.query.centerIds.split(",") : []
            const workoutIds: number[] = !_.isEmpty(req.query.workoutIds) ? req.query.workoutIds.split(",") : []
            return this.cultWorkoutDetailViewBuilder.getView(userContext, workoutId, workoutFamilyId, centerIds, workoutIds, productType)
        }

        @httpGet("/userProfile")
        async getCultUserProfile(req: express.Request): Promise<{ widgets: WidgetView[] }> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId: string = session.userId
            const userSocialSummary = await this.cultFitService.getUserProfileByUserId({ userId: userId, appName: "CUREFIT_APP" })
            return this.cultUserProfileViewBuilder.buildView(userSocialSummary, userContext)
        }

        @httpGet("/lpt/goal/:goalId")
        async getGoalPage(req: express.Request): Promise<LPTGoalPage> {
            const userContext: UserContext = req.userContext as UserContext
            const productType: ProductType = req.query.productType || "LIVE_PERSONAL_TRAINING"
            return this.livePtGoalPageViewBuilder.buildView(userContext, productType)
        }
        @httpPut("/lpt/changeGoal/:goalId")
        async changeGoal(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const goalId: number = Number(req.params.goalId)
            return await this.cultFitService.changeLivePTGoal(userContext.userProfile.userId, goalId, "CUREFIT_APP")
        }

        @httpGet("/lpt/reportStatus/:bookingId")
        async getLivePTReportStatus(req: express.Request): Promise<boolean> {
            const bookingId = req.params.bookingId
            const reportStatus = await this.livePTReportPageViewBuilder.getReportStatus(bookingId)
            return reportStatus
        }

        @httpGet("/lpt/report/:bookingId")
        async getLivePTReportPage(req: express.Request): Promise<ProductDetailPage> {
            const userContext: UserContext = req.userContext as UserContext
            const bookingId = req.params.bookingId
            return this.livePTReportPageViewBuilder.buildView(userContext, bookingId)
        }

        @httpGet("/lpt/reportSignedUrl")
        async getLivePTProfilePictureUploadSignedUrl(req: express.Request): Promise<SignedUrlResponse> {
            const fileName: string = req.query.fileName as string
            const bucketName = (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ? "cult-api-fitness-report-pictures-stage" : "cult-api-fitness-report-pictures-prod"
            return this.mediaGatewayClient.getPresignedPutUrl({
                bucketName,
                path: "",
                fileName,
                contentType : MediaType.IMAGE,
                maxUploadSize : 8097152, // need to be decided
                objectAcl : ObjectAcl.PUBLIC_READ
            })
        }

        @httpPut("/lpt/updateReport/:bookingId")
        async updateLivePTReport(req: express.Request): Promise<boolean> {
            const bookingId = req.params.bookingId
            req.body.updatedParams.imageUrl = await this.mediaGatewayClient.validateAndGetDestinationUrl(req.body.updatedParams.imageUrl)
            const updatedParams = req.body.updatedParams
            return this.livePTReportPageViewBuilder.updateFitnessReport(bookingId, updatedParams)
        }

        @httpGet("/lpt/crossGender")
        async getCrossGenderDetails(req: express.Request): Promise<ProductDetailPage> {
            const userContext: UserContext = req.userContext as UserContext
            const patientId: number = Number(req.query.patientId)
            const productId: string = req.query.productId
            const crossGenderDetails = await this.cultBusiness.getLivePTCrossGenderMatchingDetails(userContext, patientId)
            const widgets = []
            const textStyle = {
                flex: 1
            }
            const cellStyle = { paddingLeft: 20, paddingRight: 20 }
            let genderAssignmentText = "Trainers, irrespective of gender will be assigned. If you are uncomfortable, you can change your preference whenever you want"
            if (!crossGenderDetails.isCrossGenderEnabled) {
                genderAssignmentText = `Only ${crossGenderDetails.genderText} trainers will be assigned. You can choose to change the same to have access to more classes and trainers`
            }
            const tnc: ProductListWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                hideSepratorLines: true,
                type: "NUMBERED",
                noTopPadding: true,
                header: {
                    title: "Trainer Assignment",
                    titleProps: {
                        style: {
                            fontSize: 22,
                            paddingLeft: 20,
                            paddingRight: 20,
                            letterSpacing: 0
                        }
                    },
                    rowStyling: {
                        marginRight: 20
                    }
                },
                items: [
                    {
                        number: "1",
                        subTitle: genderAssignmentText,
                        textStyle: textStyle,
                        cellStyle: cellStyle
                    },
                    {
                        number: "2",
                        subTitle: `Your Personal trainer will be assigned based on his/her availability at the time slot of your choice`,
                        textStyle: textStyle,
                        cellStyle: cellStyle
                    }
                ]
            }
            widgets.push(tnc)
            widgets.push(CultUtil.getLivePTCrossGenderToggleBarWidget(crossGenderDetails, productId))
            const actions: Action[] = [{
                actionType: "HIDE_WIDGETIZED_MODAL",
                title: "OK, GOT IT"
            }]
            const page = new ProductDetailPage()
            page.widgets = widgets
            page.actions = actions
            return page
        }

        // @httpGet("/fitnessReport/helpQuestion")
        // async getFitnessReportHelpQuestions(req: express.Request): Promise<any> {
        //     return this.fitnessReportPageViewBuilder.getHelpQuestion()
        // }

        @httpGet("/getQRCode/:bookingId", kernel.get<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).validateSession)
        async getQRCodeForBookingId(req: express.Request): Promise<any> {
            const userContext: UserContext = req.userContext as UserContext
            const session: Session = req.session
            const userId = userContext.userProfile.userId
            const bookingId: number = req.params.bookingId
            const qrCodeInfo = await this.cultFitService.getQRCodeForBookingId(bookingId, userId, "CUREFIT_APP", session.deviceId)
            return {
                qrCodeString: qrCodeInfo.qrString,
                refreshTimeStamp: qrCodeInfo.refreshTimeStamp
            }
        }

        @httpPost("/inviteLink")
        async getInviteAction(req: express.Request): Promise<{ action: Action }> {
            const userContext: UserContext = req.userContext as UserContext
            const userId = userContext.userProfile.userId
            const { message, productType, bookingNumber, classId }: CultInviteLazyPostBody = req.body
            let url
            const baseService = productType === "MIND" ? this.mindFitService : this.cultFitService
            if (classId) {
                url = (await baseService.getSocialDataByClass(userId, classId, "CUREFIT_APP", true)).classLink
            }
            let booking
            if (!url) {
                this.logger.info(`Class link not generated`, { classId, userId, productType })
                if (bookingNumber) {
                    const bookingResponse = await baseService.getBookingV2(bookingNumber, userId)
                    booking = bookingResponse.booking || bookingResponse.waitlist
                }
            }
            return {
                action: await CultUtil.getShareAction(userContext, productType, url, message, booking, this.classInviteLinkCreator)
            }
        }

        private async getCultBookingDetail(userContext: UserContext, bookingId: string, userId: string): Promise<ProductDetailPage> {
            const booking: CultBooking = await this.cultFitService.getBooking(bookingId, userId, "CUREFIT_APP", userContext.userProfile.subUserId)
            let feedback: Feedback = undefined
            if (booking && booking.id) {
                feedback = await this.feedbackDao.findOne({ shipmentId: booking.id })
            }
            const issuesMap = await this.CRMIssueService.getIssuesMap()
            return this.bookingDetailViewBuilder.getView(userContext, "FITNESS", booking, issuesMap, feedback, this.feedbackPageConfigV2Cache)
        }

        private async getMindBookingDetail(userContext: UserContext, bookingId: string, userId: string): Promise<ProductDetailPage> {
            const booking: CultBooking = await this.mindFitService.getBooking(bookingId, userId, "CUREFIT_APP", userContext.userProfile.subUserId)
            let feedback: Feedback = undefined
            if (booking && booking.id) {
                feedback = await this.feedbackDao.findOne({ shipmentId: booking.id })
            }
            const issuesMap = await this.CRMIssueService.getIssuesMap()
            return this.bookingDetailViewBuilder.getView(userContext, "MIND", booking, issuesMap, feedback, this.feedbackPageConfigV2Cache)
        }

        private async getBookingDetailV2(isV2: boolean, userContext: UserContext, productType: ProductType, bookingNumber: string, userId: string, deviceId?: string, hamletBusiness?: HamletBusiness): Promise<ProductDetailPage> {
            const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
            const bookingResponse: CultBookingResponse = await baseService.getBookingV2(bookingNumber, userId, true, "CUREFIT_APP", userContext.userProfile.subUserId, deviceId)
            let feedbackPromise
            if (bookingResponse.booking && bookingResponse.booking.id) {
                feedbackPromise = this.feedbackDao.findOne({ shipmentId: bookingResponse.booking.id, rating: { $in: ["NOT_RATED", "DISMISSED"] } })
            }
            const booking = bookingResponse.booking || bookingResponse.waitlist
            let socialDataEternalPromise: Promise<{ obj: SquadClassBookingLite[] }>
            if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
                socialDataEternalPromise = eternalPromise(baseService.getUpcomingClassesForSquadFromCache(userId,  "CUREFIT_APP"), `Error in fetching socialData for userId:${userId}, bookingNumber: ${bookingNumber}`)
            }
            let wod: SimpleWod = null
            try {
                wod = await this.herculesService.getSimpleWodById(booking.Class.wodId)
            }
            catch (error) {
                this.logger.error(error)
            }
            const cityId = userContext.userProfile.cityId
            const prefLocationType = await UserUtil.getLocationDataFromRashi(userId, cityId, LocationDataKey.LOC_PREF_TYPE, this.userAttributeClient)
            return isV2 ? this.bookingDetailViewBuilderV2.getViewV2(userContext, productType, bookingResponse, await feedbackPromise, wod, this.feedbackPageConfigV2Cache, socialDataEternalPromise, prefLocationType) :
                this.bookingDetailViewBuilderV2.getView(userContext, productType, bookingResponse, await feedbackPromise, wod, this.feedbackPageConfigV2Cache, socialDataEternalPromise)
        }

        private sortCentersByLocation(req: express.Request, centers: CultCenter[]) {
            if (_.isNil(req.headers["lat"]) || _.isNil(req.headers["lon"]))
                return
            const lat: number = Number(req.headers["lat"])
            const lon: number = Number(req.headers["lon"])
            LocationUtil.sortCentersByLocation(lat, lon, centers)
        }
        private async getCancelCompletionActionAndAlertInfo(userContext: UserContext, cancelResponse: any, fulfilmentId: string, date: string): Promise <{ alertInfo: AlertInfo, completionAction: Action }> {
            const alertInfo = await this.cancelCafeFulfillment(userContext, fulfilmentId, date, "CANCEL", cancelResponse?.data?.booking?.creditCost ?? null)
            let completionAction
            if (cancelResponse && cancelResponse.data && cancelResponse.data.booking) {
                try {
                    completionAction = CultUtil.getDeleteCalendarEventAction(userContext, cancelResponse.data.booking)
                } catch (error) {
                    this.logger.error("Error in delete calendar event action: ", error)
                }
                if (cancelResponse.data.booking.amountPaid > 0) {
                    let cancelClassListItem
                    cancelClassListItem = {
                        title: "Class Refund",
                        subTitle: `Your workout has been cancelled. Refund of ${RUPEE_SYMBOL}${cancelResponse.data.booking.amountPaid} has been initiated.`
                    }
                    if (alertInfo.listItems) {
                        alertInfo.listItems.unshift(cancelClassListItem)
                    } else {
                        cancelClassListItem.title = "Refund"
                        alertInfo["listItems"] = [cancelClassListItem]
                    }
                    alertInfo.meta = {
                        modalHeight: alertInfo.listItems && alertInfo.listItems.length > 1 ? 400 : 300
                    }
                }
            }
            return { alertInfo, completionAction }
        }
        private getCancelModalTitle(isDropoutFlow: boolean, bookingCredit?: number) {
            if (_.isNil(bookingCredit)) {
                return (isDropoutFlow ? "You have dropped out of the class" : "Class Cancelled")
            } else if (bookingCredit == 1) {
                return (isDropoutFlow ? `You have dropped out of the class\n ${bookingCredit} Credit refunded` : `Class Cancelled\n${bookingCredit} Credit refunded`)
            } else {
                return (isDropoutFlow ? `You have dropped out of the class\n ${bookingCredit} Credits refunded` : `Class Cancelled\n${bookingCredit} Credits refunded`)
            }
        }

        private async cancelCafeFulfillment(userContext: UserContext, fulfilmentId: string, date: string, classCancellationType: "CANCEL" | "DROPOUT" = "CANCEL", bookingCredit?: number): Promise<AlertInfo> {
            const isDropoutFlow = classCancellationType === "DROPOUT"
            const listItems = []
            const alertInfoTitle = this.getCancelModalTitle(isDropoutFlow, bookingCredit)
            let alertInfo: AlertInfo = {
                title: alertInfoTitle,
                subTitle: "",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                meta: {
                    modalHeight: 150
                }
            }
            if (fulfilmentId && date) {
                const cancelMealResponse = await this.fulfilmentService.cancelFulfilmentForDate(userContext.userProfile.userId, fulfilmentId, date)
                if (cancelMealResponse.foodBooking) {
                    const subTitle = ""
                    if (MealUtil.isAddMealSupported(userContext)) {
                        if (cancelMealResponse.mealReset) {
                            listItems.push({
                                title: "Meal reset",
                                subTitle: "Your changed meal has been reset and cancelled. " + `${AppUtil.getCurrencySymbol(cancelMealResponse.currency)} ${cancelMealResponse.originalMealPrice}` + " will be refunded / adjusted at the end of your billing cycle"
                            })
                        } else {
                            if (cancelMealResponse.foodBooking.packId) {
                                listItems.push({
                                    title: "Cancellation",
                                    subTitle: "Your order has been successfully cancelled. " + `${AppUtil.getCurrencySymbol(cancelMealResponse.currency)} ${cancelMealResponse.originalMealPrice}` + " will be refunded / adjusted at the end of your billing cycle"
                                })
                            }
                        }
                        if (cancelMealResponse.amountRefunded > 0) {
                            listItems.push({
                                title: "Meal Refund",
                                subTitle: cancelMealResponse.foodBooking.packId ? ("The additional " + `${AppUtil.getCurrencySymbol(cancelMealResponse.currency)} ${cancelMealResponse.amountRefunded}` + " you paid for changed / added items has been refunded to you. Please check refund memo sent to your registered email id for more details.") : (`${AppUtil.getCurrencySymbol(cancelMealResponse.currency)} ${Math.round(cancelMealResponse.amountRefunded)}` + " has been refunded to you. Please check refund memo sent to your registered email id for more details.")
                            })
                        }
                        if (cancelMealResponse.foodBooking.packId) {
                            const foodPack: FoodPack = await this.catalogueService.getFoodPack(cancelMealResponse.foodBooking.packId)
                            if (foodPack.tags.includes("WEIGHT_LOSS")) {
                                listItems.push({
                                    title: "Cancellation Fee",
                                    subTitle: "A cancellation fee of " + AppUtil.getCurrencySymbol(cancelMealResponse.currency) + " 50 is charged for weight loss plans"
                                })
                            }
                        }
                    }
                    alertInfo = {
                        title: isDropoutFlow ? "Dropped out and Meal cancelled" : "Class and Meal Cancelled",
                        subTitle: subTitle,
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                        listItems: listItems,
                        meta: {
                            modalHeight: listItems && listItems.length > 1 ? 400 : 300
                        }
                    }
                }
                // Since text is little large in dropout message
                if (isDropoutFlow) {
                    alertInfo.meta.modalHeight += 30
                }
            }
            return alertInfo
        }

        @httpGet("/sgtprebooking")
        async getOneStepBookingPage(req: express.Request): Promise<SGTOneStepBookingPage> {
            const userContext: UserContext = req.userContext as UserContext
            const { startTime, endTime, productId } = req.query
            const patientId = req.query.patientId !== "undefined" && !_.isEmpty(req.query.patientId) ? Number(req.query.patientId) : undefined
            const parentBookingId = req.query.parentBookingId !== "undefined" && !_.isEmpty(req.query.parentBookingId) ? Number(req.query.parentBookingId) : -1
            return this.sgtOneStepBookingPageViewBuilder.buildView(userContext, startTime, endTime, productId, patientId, parentBookingId)
        }

        @httpGet("/circuitBreakerStats")
        async getCircuitBreakerStats(req: express.Request): Promise<CircuitBreaker.Stats> {
            const { cbName} = req.query
            return this.circuitBreakerUtil.getCircuitStats(cbName)
        }

        @httpGet("/circuitBreakerStats/all")
        async getAllCircuitBreakerStats(req: express.Request): Promise<any> {
            const { sleepTime } = req.query
            this.logger.info(`******** sleepTime:${sleepTime}`)
            if (sleepTime) await AppUtil.timeout(sleepTime)
            return this.circuitBreakerUtil.getAllCircuitStats()
        }

        @httpGet("/circuitBreaker/fire")
        async getFireCircuitBreaker(req: express.Request): Promise<any> {
            const { url, sleepTime } = req.query
            const circuitBreaker = this.circuitBreakerUtil.getInstance("test", CircuitBreakerProfileTypes.FAST_RECOVERY_HIGH_TOLERANCE)
            const requestUrl = url || `http://localhost:3000/cult/circuitBreakerStats/all${sleepTime ? `?sleepTime=${sleepTime}` : ``}`
            const headers: HeadersInit = {}
            Object.assign(headers, req.headers)
            const requestInit: RequestInit = this.fetchUtilV2.get({ headers })
            const apiResponse = await this.circuitBreakerUtil.fetch(circuitBreaker as CircuitBreaker<any, Response>, requestUrl, requestInit)
            return this.fetchUtilV2.parseResponse(apiResponse)
        }
    }

    return CultController
}

export default controllerFactory
