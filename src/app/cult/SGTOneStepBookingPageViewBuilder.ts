import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import { SGTOneStepBookingPage } from "../common/views/WidgetView"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { Duration, SimpleWod } from "@curefit/fitness-common"
import * as _ from "lodash"
import {
    CultWorkoutSessionBreakupWidget,
    NoteListWidget,
    WorkoutDetail,
    WorkoutSelectionListWidget
} from "@curefit/apps-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import CultUtil from "../util/CultUtil"
import { capitalizeFirstLetter } from "@curefit/base"

@injectable()
class SGTOneStepBookingPageViewBuilder {

    constructor(
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService
    ) { }

    async buildView(userContext: UserContext, startTime: string, endTime: string, productCode: string, patientId: number, parentBookingId: number): Promise<SGTOneStepBookingPage> {

        const bookingPage = new SGTOneStepBookingPage()
        bookingPage.widgets = []
        const userId = userContext.userProfile.userId
        if (_.isNil(patientId)) {
            const patientsList = await this.healthfaceService.getAllPatients(userId)
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            patientId = selfPatient.id
        }
        const slotDetails = await this.healthfaceService.getSlotDetails(
            userId,
            patientId,
            parentBookingId,
            startTime,
            productCode,
            "CULTFIT"
        )

        const workoutSelectionListWidget: WorkoutSelectionListWidget = {
            widgetType: "WORKOUT_SELECTION_LIST_WIDGET",
            title: slotDetails.consultationProduct.productSpecs.cultWorkoutName,
            image: slotDetails.consultationProduct.productSpecs.workoutImageUrl,
            date: this.getTimeString(startTime, endTime, userContext.userProfile.timezone),
            workouts: [],
            defaultFocusAreaId: "",
            sessionBreakupWidget: slotDetails.userType === "FIRST_TIME" ? await this.getWorkoutBreakupWidget(slotDetails.consultationProduct.productSpecs.defaultWodId) : undefined
        }
        workoutSelectionListWidget.workouts = await this.getWorkouts(slotDetails.wodSchedule, slotDetails.sgtRecommendedFocusArea)
        workoutSelectionListWidget.defaultFocusAreaId = workoutSelectionListWidget.workouts[0].focusAreaId
        bookingPage.widgets.push(workoutSelectionListWidget)
        bookingPage.widgets.push(this.getNoteListWidget(slotDetails, userContext, productCode))
        bookingPage.widgets = bookingPage.widgets.filter(widget => !_.isEmpty(widget))
        bookingPage.actions = [{
            title: "CONFIRM & BOOK",
            actionType: "BOOK_SGT_CLASS"
        }]
        return bookingPage
    }

    getTimeString(startTime: string, endTime: string, tz: Timezone): string {
        return `${TimeUtil.formatEpochInTimeZone(tz, parseInt(startTime), "ddd DD MMM, hh.mm")} - ${TimeUtil.formatEpochInTimeZone(tz, parseInt(endTime), "hh.mm A")}`
    }

    getNoteListWidget(slotDetails: any, userContext: UserContext, productCode: string): NoteListWidget {
        const userType: string = slotDetails.userType
        const widget: NoteListWidget = {
            widgetType: "NOTE_LIST_WIDGET",
            note: {
                title: "NOTE",
                color: "#000000",
            },
            data: [
                {
                    icon: "/image/icons/livept/video_30.png",
                    info: "This is a live workout session. It is mandatory to switch on your camera for this session.",
                    description: "Video link will be shared 5 mins before the session.",
                },
                {
                    icon: "/image/icons/cult/tshirt.png",
                    info: "Wear comfortable clothes and shoes. Keep a water bottle and towel close by.",
                },
                {
                    icon: "/image/icons/livept/cancel_30.png",
                    info: "Cancel the class upto 1 hour before the class.",
                    description: `Cut-off: ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, slotDetails.startTime - (slotDetails.consultationProduct.cancellationWindowInMins * 60000), "Do MMM, hh:mm a")}`
                },
                {
                    icon: "/image/icons/livept/Laptop_big.png",
                    info: "We recommend joining the session from the website/laptop for the best experience.",
                },
            ]
        }
        if (productCode === "CONS_CULT_LIVE_SGT_BOXING") {
            widget.data.unshift({
                icon: "image/live-pt/sgt-focus-area-images/<EMAIL>",
                info: "No boxing equipment is needed.",
            })
        }
        if (userType !== "FIRST_TIME") {
            widget.data.forEach(note => {
                note.title = undefined
            })
        }
        return widget
    }

    async getWorkoutBreakupWidget(wodId: string) {
        let wod: SimpleWod = null
        if (wodId) {
            wod = await this.herculesService.getSimpleWodById(wodId)
        }
        const cultWorkoutSessionBreakupWidget = new CultWorkoutSessionBreakupWidget()
        cultWorkoutSessionBreakupWidget.header = { title: "" }
        cultWorkoutSessionBreakupWidget.startText = "Begin"
        cultWorkoutSessionBreakupWidget.breakup = []
        let endTime = 0
        const colors = ["#ff8a8c", "#ffdc18", "#00e5ff", "#e7b6fe"]
        for (let i = 0; i < wod.parts.length; i++) {
            const part = wod.parts[i]
            endTime += this.getDurationInSecs(part.duration)
            const breakup = {
                info: part.heading,
                duration: this.getDurationInSecs(part.duration),
                durationText: part.duration.value + " " + capitalizeFirstLetter(part.duration.units),
                color: colors[i % colors.length]
            }
            cultWorkoutSessionBreakupWidget.breakup.push(breakup)
        }
        cultWorkoutSessionBreakupWidget.endText = (endTime / 60) + " Min"
        cultWorkoutSessionBreakupWidget.containerStyle = {
            backgroundColor: "#f2f4f8",
            borderRadius: 10,
            paddingVertical: 5,
            marginBottom: 40
        }
        cultWorkoutSessionBreakupWidget.rowStyle = {
            paddingVertical: 5
        }
        cultWorkoutSessionBreakupWidget.legendInfoStyle = {
            color: "33363f",
            fontSize: 12,
            fontFamily: "BrandonText-Regular"
        }
        return cultWorkoutSessionBreakupWidget
    }

    getDurationInSecs(duration: Duration) {
        if (duration.units === "SECONDS") {
            return duration.value
        }
        return duration.value * 60
    }

    async getWorkouts(wodSchedule: any, recommendedArea: string): Promise<WorkoutDetail[]> {
        let index = -1
        const workoutDetailsPromises = _.map(wodSchedule, async workoutDetail => {
            let wod: SimpleWod = null
            if (workoutDetail.sgtWodSchedule.wodId) {
                wod = await this.herculesService.getSimpleWodById(workoutDetail.sgtWodSchedule.wodId)
            }
            index = index + 1
            const mainWorkoutsString: string = CultUtil.getWodMovements(wod)
            let mainWorkouts: string[] = []
            if (mainWorkoutsString) mainWorkouts = mainWorkoutsString.split(", ")
            return {
                title: workoutDetail.sgtWodSchedule.focusAreaName,
                image: workoutDetail.sgtWodSchedule.focusAreaImageUrl,
                caloriesBurnt: `${workoutDetail.calorieCount} kcal`,
                mainWorkouts: mainWorkouts,
                isExpanded: index === 0,
                isRecommended: workoutDetail.sgtWodSchedule.focusAreaName === recommendedArea,
                focusAreaId: workoutDetail.sgtWodSchedule.focusAreaId
            }
        })
        const workoutDetails = await Promise.all(workoutDetailsPromises)
        return workoutDetails
    }
}

export default SGTOneStepBookingPageViewBuilder
