import { Product, ProductPrice, ProductType } from "@curefit/product-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Action, ProductDetailPage, WidgetView } from "../common/views/WidgetView"
import { Banner, BannerWidget } from "../page/PageWidgets"

import * as _ from "lodash"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AppUtil, { CULT_BANNER_RATIO_MAP } from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { CatalogueServiceUtilities } from "./../util/CatalogueServiceUtilities"
import {
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    PackOfferInventoryRequestParams
} from "@curefit/offer-service-client"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService } from "@curefit/catalog-client"
import CultUtil from "../util/CultUtil"
import { PageTitle } from "../page/Page"
import CultPackPageConfig from "../pack/CultPackPageConfig"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export interface CenterWiseSlot {
    remaingSlots?: number
    subTitle?: string
    centerName: string
    centerAddress: string
    centerId: string
    action: string
    buttonAction?: Action
    price?: ProductPrice
    alertMeta?: {
        title: string
        message: string
    }
    tagView?: {
        tagTitle: string
        tags: string[]
    }
}

export interface CenterWiseSlotWidget extends WidgetView {
    title: string
    centerWiseSlots: CenterWiseSlot[]
}

export interface TermsAndConditionWidget extends WidgetView {
    offerTerms: string
}

export class CultOfferProductDetailPage extends ProductDetailPage {
    breadcrumb?: { text: string, link?: string }[]
    header?: PageTitle
    offerBanner: string
    offerBannerRatio: string
    title?: string
    centerWiseSlots: CenterWiseSlot[]
}
@injectable()
class CultOfferDetailViewBuilder {

    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) private cultPackPageConfig: CultPackPageConfig,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
    ) {
    }

    async getView(centerWiseSlots: CenterWiseSlot[], banner: Banner, userAgent: UserAgent, offerTerms?: string): Promise<CultOfferProductDetailPage> {
        const productDetailPage = new CultOfferProductDetailPage()
        let bannerImage
        if (userAgent === "DESKTOP" && banner)
            bannerImage = banner.desktopWebImage
        else if (userAgent === "MBROWSER" && banner)
            bannerImage = banner.mobileWebImage
        else if (banner)
            bannerImage = banner.image

        productDetailPage.centerWiseSlots = centerWiseSlots
        productDetailPage.offerBanner = bannerImage
        productDetailPage.offerBannerRatio = "1:1" // hard code for now, make it based on user agent
        productDetailPage.widgets = []
        if (banner)
            productDetailPage.widgets.push(this.getBannerWidget(banner, userAgent))
        productDetailPage.widgets.push(this.getCenterWiseSlotWidget("Available Centers", centerWiseSlots))
        if (offerTerms) {
            productDetailPage.widgets.push(this.getTermsAndConditionWidget(offerTerms))
        }
        return productDetailPage
    }

    async getViewForCenterSpecificPack(packDetail: OfflineFitnessPack, userContext: UserContext, productType: ProductType, navigateToNewPackPage?: boolean): Promise<CultOfferProductDetailPage> { // TODO: PACK_ID_DEPRECATION deprecate function
        const baseService = productType === "FITNESS" ? this.cultFitService : this.mindFitService
        const centersWithWorkoutsPromise = baseService.browseFitnessCentersByIDs([], true, true, "CUREFIT_API") // DEPRECATED availableCenters will be empty for any active packs
        const productDetailPage = new CultOfferProductDetailPage()
        const centersPricing = await this.getCenterWisePricingMapForPackOffer(packDetail, [], userContext)

        const centers = await centersWithWorkoutsPromise
        const isWorkoutViewSupported = await AppUtil.isWorkoutViewSupportedInCenterSelection(userContext, this.hamletBusiness)
        const centerWiseSlotPromises = _.map(centers, async center => {
            let url = CatalogueServiceUtilities.getPackPageAction(packDetail, userContext.sessionInfo?.userAgent, undefined, false, "packOffer")
            url += `&centerId=${center.id}&canChangeCenter=false`
            if (navigateToNewPackPage) {
                url = AppUtil.getNewPackPageActionUrl(url)
            }
            const action: Action = {
                title: "SELECT CENTRE",
                actionType: "NAVIGATION",
                url: url
            }
            const centerWiseSlot: CenterWiseSlot = {
                centerId: center.id.toString(),
                centerName: center.name,
                action: url,
                buttonAction: action,
                centerAddress: CultUtil.getCultCenterAddress(center),
                price: centersPricing[center.id.toString()]
            }
            centerWiseSlot.alertMeta = CultUtil.getEmployeeOnlyModalMetaForCenter(center.id.toString())
            if (!_.isEmpty(center.workouts) && isWorkoutViewSupported) {
                centerWiseSlot.tagView = {
                    tagTitle: `Workouts available at this centre`,
                    tags: _.map(center.workouts, workout => workout.name)
                }
            }
            return centerWiseSlot
        })
        const productName = productType === "FITNESS" ? "Cult" : "Mind.fit"
        productDetailPage.header = {
            title: this.cultPackPageConfig.centerSelectionHeader.title,
            subTitle: this.cultPackPageConfig.centerSelectionHeader.subTitle,
            description: packDetail.displayName
        }
        productDetailPage.title = packDetail.displayName
        const centerWiseSlots = await Promise.all(centerWiseSlotPromises)
        // since app is not consuming widgets data
        if (userContext.sessionInfo.userAgent === "APP") {
            productDetailPage.centerWiseSlots = centerWiseSlots
        }
        else {
            const linkObject = productType === "FITNESS" ? { text: "Cult.fit", link: "/cult" } : { text: "Mind.fit", link: "/mind" }
            productDetailPage.breadcrumb = [{ text: "Home", link: "/" }, linkObject, { text: "Single Centre Memberships" }]
            productDetailPage.widgets = [this.getCenterWiseSlotWidget(packDetail.displayName, centerWiseSlots)]
        }
        return productDetailPage
    }

    async getViewForPreRegistrationPacks(userContext: UserContext, productType: ProductType, banner: Banner): Promise<CultOfferProductDetailPage> {
        this.logger.error("PMS::DEPR getViewForPreRegistrationPacks", {})
        // const baseService = productType === "MIND" ? this.mindFitService : this.cultFitService
        // const activeMembership = await baseService.activeMembership(userContext.userProfile.userId, true)
        // let centerWiseSlots: CenterWiseSlot[] = []
        // if (activeMembership.isEligibleForAPreRegistrationOffer && !_.isEmpty(preregistrationPacks)) {
        //     const packDetail = preregistrationPacks[0]
        //     const slotsPromises: Promise<CenterWiseSlot>[] = _.map(packDetail.availableCenterSlots, async centerSlotInfo => {
        //         const centerInfo = productType === "MIND" ? await this.catalogueService.getCultMindCenter(centerSlotInfo.centerID.toString()) : await this.catalogueService.getCultCenter(centerSlotInfo.centerID.toString())
        //         const subTitle = CultUtil.getCenterLaunchDateText(userContext, centerInfo.launchDate)
        //         const packPageUrl = productType === "FITNESS" ? `curefit://cultpack?packId=${packDetail.id}&centerId=${centerInfo.id}&canChangeCenter=false`
        //             : `curefit://cultmindpack?packId=${packDetail.id}&centerId=${centerInfo.id}&canChangeCenter=false`
        //         return {
        //             remaingSlots: centerSlotInfo.availableSlots,
        //             subTitle: subTitle,
        //             centerName: centerInfo.name,
        //             centerId: centerInfo.id.toString(),
        //             centerAddress: CultUtil.getCultCenterAddress(centerInfo),
        //             action: centerSlotInfo.availableSlots > 0 ? packPageUrl : "",
        //             alertMeta: CultUtil.getEmployeeOnlyModalMetaForCenter(centerInfo.id.toString())
        //         }
        //     })
        //     centerWiseSlots = await Promise.all(slotsPromises)
        // }
        return this.getView([], banner, userContext.sessionInfo.userAgent)
    }

    public getCenterWiseSlotWidget(title: string, centerWiseSlots: CenterWiseSlot[]): CenterWiseSlotWidget {
        return {
            widgetType: "CENTER_WISE_SLOT_WIDGET",
            title: title,
            centerWiseSlots: centerWiseSlots
        }
    }

    public getTermsAndConditionWidget(offerTerms: string): TermsAndConditionWidget {
        return {
            widgetType: "TERMS_TEMP_WIDGET",
            offerTerms: offerTerms
        }

    }

    public getBannerWidget(banner: Banner, userAgent: UserAgent): BannerWidget {
        const clonedBanner = _.clone(banner)
        const bannerRatio = CULT_BANNER_RATIO_MAP.get(userAgent)

        if (userAgent === "DESKTOP")
            clonedBanner.image = clonedBanner.desktopWebImage
        else if (userAgent === "MBROWSER")
            clonedBanner.image = clonedBanner.mobileWebImage
        return {
            widgetType: "BANNER_WIDGET",
            hasBottomPadding: false,
            hasTopPadding: false,
            bannerRatio: bannerRatio,
            banners: [clonedBanner],
            hasDividerBelow: true
        }
    }

    private async getCenterWisePricingMapForPackOffer(product: OfflineFitnessPack, centerIds: number[], userContext: UserContext): Promise<{ [centerId: string]: ProductPrice }> {
        const user = await userContext.userPromise
        const centerServiceIds = await CultUtil.getCenterServiceIdsFromCultCenterIds(centerIds, this.centerService)
        this.logger.info(`offerLog: getCenterWisePricingMapForPackOffer centerIds`, {centerServiceIds})
        const {centerPriceMap, centerServicePriceMap} = product.productType == "FITNESS" ? await this.offerServiceV3.getCultCenterPrices({
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                phone: user?.phone,
                email: user?.email,
                workEmail: user?.workEmail
            },
            cityId: userContext.userProfile.cityId,
            packProductId: product.productId,
            centerIds: centerIds.map(c => c.toString()),
            centerServiceIds: centerServiceIds.map(c => c.toString()),
            source: AppUtil.callSourceFromContext(userContext)
        }) : await this.offerServiceV3.getMindCenterPrices({
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                phone: user?.phone,
                email: user?.email,
                workEmail: user?.workEmail
            },
            cityId: userContext.userProfile.cityId,
            packProductId: product.productId,
            centerIds: centerIds.map(c => c.toString()),
            source: AppUtil.callSourceFromContext(userContext)
        })
        const centerWisePricing: { [centerId: string]: ProductPrice } = {}
        // assign default price
        _.forEach(centerIds, centerId => {
            centerWisePricing[centerId.toString()] = {
                mrp: product.price.mrp,
                listingPrice: product.price.listingPrice,
                currency: product.price.currency
            }
        })
        if (!_.isEmpty(centerServicePriceMap)) {
            for (const centerServiceId of _.keys(centerServicePriceMap)) {
                const centerServiceResponse = await this.centerService.getCenterById(+centerServiceId)
                const cultCenterId = centerServiceResponse.meta.cultCenterId
                this.logger.info(`offerLog: getCenterWisePricingMapForPackOffer cultCenterId:`, {cultCenterId})
                if (!_.isNil(centerServicePriceMap[centerServiceId]?.price?.listingPrice && cultCenterId)) {
                    centerWisePricing[cultCenterId].listingPrice = centerServicePriceMap[centerServiceId].price.sellingPrice
                }
            }
        } else if (!_.isEmpty(centerPriceMap)) {
            for (const centerId of _.keys(centerPriceMap)) {
                if (!_.isNil(centerPriceMap[centerId].price.listingPrice)) {
                    centerWisePricing[centerId].listingPrice = centerPriceMap[centerId].price.sellingPrice
                }
            }
        }
        return centerWisePricing
    }
}
export default CultOfferDetailViewBuilder
