import { inject, injectable } from "inversify"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { C<PERSON>Workout, CultWorkoutV2 } from "@curefit/cult-common"
import { CultWorkoutDetailPage, DescriptionWidget, InfoCard } from "../common/views/WidgetView"
import { BASE_TYPES, capitalizeFirstLetter, ILogger } from "@curefit/base"
import { BannerCarouselWidget, HeaderWidget } from "../page/PageWidgets"
import {
    CultWorkoutBenefitsWidget,
    CultWorkoutCaloriesWidget,
    CultWorkoutSessionBreakupWidget,
    CultWorkoutTagsWidget,
    ImageOverlayCardContainerWidget,
    NoteWidget,
    WorkoutBreakup
} from "@curefit/apps-common"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { Duration, SimpleWod } from "@curefit/fitness-common"
import { ActionUtil } from "@curefit/base-utils"
import { CdnUtil } from "@curefit/util-common"
import { ICultBusiness } from "./CultBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ProductType } from "@curefit/product-common"
import {
    ALBUS_CLIENT_TYPES,
    ConsultationSellableProduct,
    HealthfaceProductInfo,
    IHealthfaceService,
    LivePTProductSpec
} from "@curefit/albus-client"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { ICareBusiness } from "../care/CareBusiness"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil from "../util/AppUtil"
import CultUtil, { isUserActiveMember } from "../util/CultUtil"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"

const colors = ["#ff8a8c", "#ffdc18", "#00e5ff", "#e7b6fe"]

@injectable()
export class CultWorkoutDetailViewBuilder {

    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculeService: IHerculesService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) public healthfaceService: IHealthfaceService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
        @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
        @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
    ) {
    }

    async getView(userContext: UserContext, workoutId: string, workoutFamilyId: string, centerIds: string[], workoutIds: number[], productType: ProductType) {
        const userId = userContext.userProfile.userId
        const cultCityId = userContext.userProfile.city.cultCityId
        const baseService = productType === "MIND" ? this.mindFitService : this.cultFitService
        let workouts: CultWorkoutV2[]
        let livePTProducts: HealthfaceProductInfo[]
        if (productType === "LIVE_PERSONAL_TRAINING" || productType === "LIVE_SGT") {
            livePTProducts = await this.cultPTService.getConsultationProductsInfoByGroupType(userContext.userProfile.cityId, productType)
            workoutIds = livePTProducts.map(product => {
                const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
                const workout = consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec
                return workout.cultWorkoutId
            })
        }
        if (!_.isEmpty(workoutIds)) {
            workouts = await baseService.browseWorkoutsV2ByIDs(workoutIds, "CUREFIT_API", cultCityId, userId)
        } else {
            workouts = _.isEmpty(workoutFamilyId) ?
                await baseService.browseWorkoutsV2(undefined, true, false, true, "CUREFIT_API", userId, cultCityId, centerIds) :
                await baseService.browseWorkoutsV2(parseInt(workoutFamilyId), false, false, true, "CUREFIT_API", userId, cultCityId, centerIds)
        }

        // Changes done for dogfooding sports category. Remove Sport Workout if it's not in dogfooding.
        const isCultSportsCategorySupported: boolean = await CultUtil.isCultSportsCategorySupported(userContext,
            this.hamletBusiness)
        if (!isCultSportsCategorySupported) {
            const sportCategoryWorkouts: number[] = await CultUtil.getSportCategoryWorkoutIds()

            workouts = _.filter(workouts, (workout) => {
                const workoutPresent: number = _.find(sportCategoryWorkouts, (workoutId: number) => {
                    if (workoutId === workout.id) {
                        return true
                    }
                    return false
                })
                if (workoutPresent) {
                    return false
                }
                return true
            })
        }

        const isBaddyFitSupported: boolean = await CultUtil.isBaddyFitSupported(userContext,
            this.hamletBusiness)
        if (!isBaddyFitSupported) {
            const baddyFitWorkouts: number[] = await CultUtil.getBaddyFitWorkoutIds()

            workouts = _.filter(workouts, (workout) => {
                const workoutPresent: number = _.find(baddyFitWorkouts, (workoutId: number) => {
                    if (workoutId === workout.id) {
                        return true
                    }
                    return false
                })
                if (workoutPresent) {
                    return false
                }
                return true
            })
        }

        const workoutDetailPage = new CultWorkoutDetailPage()
        if (_.isEmpty(workouts)) {
            return workoutDetailPage
        }
        let selectedWorkoutIndex = 0
        let selectedWorkout = undefined
        const workoutNameVsId: { category: string, workoutId: string }[] = []
        for (let i = 0; i < workouts.length; i++) {
            if (workouts[i].id.toString() === workoutId) {
                selectedWorkoutIndex = i
                selectedWorkout = workouts[i]
            }
            workoutNameVsId.push({
                category: workouts[i].name,
                workoutId: workouts[i].id.toString()
            })
        }

        if (AppUtil.isCultWorkoutTabPageInitialIndexFixNeeded(userContext)) {
            /* Till the initial index starts working on app, we'll make the selected category the first category */
            selectedWorkoutIndex = 0
            workoutNameVsId.some((item, index) => {
                if (item?.workoutId === workoutId) {
                    [workoutNameVsId[0], workoutNameVsId[index]] = [workoutNameVsId[index], workoutNameVsId[0]]
                    return true
                }
            })
        }

        workoutDetailPage.categories = workoutNameVsId
        workoutDetailPage.selectedWorkoutIndex = selectedWorkoutIndex
        if (_.isEmpty(selectedWorkout)) {
            this.logger.error("No workout found for selected workout Id: " + workoutId)
            return workoutDetailPage
        }
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        const document = _.find(selectedWorkout.documents, { tagName: "PRODUCT_BNR" })
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: document.URL
        })
        imageOverlayContainerWidget.productType = productType
        if (AppUtil.isWeb(userContext)) {
            workoutDetailPage.images = [document.URL]
        }
        const headerWidget = new HeaderWidget({ title: selectedWorkout.name }, false)
        headerWidget.hasTopPadding = false
        headerWidget.hasDividerBelow = false
        headerWidget.headerStyle = {
            marginLeft: 0,
            fontSize: 24,
            color: "#000000"
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)
        imageOverlayContainerWidget.widgets.push(this.getCultWorkoutCalorieWidget(selectedWorkout))
        imageOverlayContainerWidget.widgets.push(this.getCultWorkoutBenefitsWidget(selectedWorkout))
        imageOverlayContainerWidget.widgets.push(this.getCultWorkoutFamilyTagsWidget(selectedWorkout))

        workoutDetailPage.widgets.push(imageOverlayContainerWidget)
        if (productType !== "LIVE_PERSONAL_TRAINING" && productType !== "LIVE_SGT") {
            workoutDetailPage.widgets.push(this.getVideoBannerCarouselWidget(userContext, selectedWorkout))
        }

        const workoutBreakUpWidget = await this.getWorkoutBreakupWidget(selectedWorkout)
        if (workoutBreakUpWidget) {
            workoutDetailPage.widgets.push(workoutBreakUpWidget)
        }
        if (productType !== "LIVE_PERSONAL_TRAINING" && productType !== "LIVE_SGT") {
            workoutDetailPage.widgets.push(this.getNotesWidget(selectedWorkout))
        }
        workoutDetailPage.widgets.push(this.getDescriptionWidget(selectedWorkout))
        workoutDetailPage.widgets = workoutDetailPage.widgets.filter(widget => widget !== undefined)

        if (productType === "LIVE_PERSONAL_TRAINING" || productType === "LIVE_SGT") {
            const selectedProduct = _.find(livePTProducts, product => {
                const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
                const workout = consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec
                return workout.cultWorkoutId.toString() === workoutId
            })
            workoutDetailPage.action = await this.careBusiness.getLivePTSessionBookAction(userContext, {productId: selectedProduct.baseSellableProduct.productCode, actionTitle: "BOOK SESSION", subCategoryCode: productType === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT", useFormat: true})
        } else {
            const isActiveMember = await isUserActiveMember(userContext, this.membershipService)
            if (selectedWorkout.isSportCategory && !isActiveMember) {
                workoutDetailPage.action = {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "BOOK NOW",
                    meta: {
                        title: "Attention",
                        subTitle: "This format is currently available for cultpass ELITE members only.",
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "OK" }],
                        meta: { subTitleStyle: { fontSize: 14 } },
                    }
                }
            } else {
                const classBookingUrl = ActionUtil.getBookCultClassUrl("FITNESS", AppUtil.isNewClassBookingSuppoted(userContext), "workoutPage", selectedWorkout.workoutCategoryID.toString(), userContext.userProfile.cultCenterId, undefined, undefined)
                const { cult, mind } = await this.cultBusiness.isBookFreeCultClassSupported(userContext)
                workoutDetailPage.action = { actionType: "NAVIGATION", url: classBookingUrl, title: (cult && mind) ? "TRY FOR FREE" : "BOOK CLASS" }
            }
        }
        // TODO: 1) Makes notes array.  2) Multiple video support from backend
        return workoutDetailPage
    }

    private getCultWorkoutCalorieWidget(workout: CultWorkoutV2) {
        const cultWorkoutCaloriesWidget = new CultWorkoutCaloriesWidget()

        cultWorkoutCaloriesWidget.calorieTitle = "CALORIES"
        cultWorkoutCaloriesWidget.calorieIcon = "/image/icons/cult/calorie.png"
        cultWorkoutCaloriesWidget.calorieText = workout.otherAttributes.averageCaloriesBurnt ? workout.otherAttributes.averageCaloriesBurnt.toString() : workout.otherAttributes.minCaloriesBurnt + " - " + workout.otherAttributes.maxCaloriesBurnt

        cultWorkoutCaloriesWidget.intensityTitle = "INTENSITY"
        cultWorkoutCaloriesWidget.intensityLevels = 3
        cultWorkoutCaloriesWidget.currentIntensity = workout.otherAttributes.intensity
        cultWorkoutCaloriesWidget.intensityIconType = "BAR"
        cultWorkoutCaloriesWidget.defaultColor = "#9b9b9b"
        cultWorkoutCaloriesWidget.highlightColor = "#000000"

        return cultWorkoutCaloriesWidget
    }

    private getCultWorkoutBenefitsWidget(workout: CultWorkoutV2) {
        const cultWorkoutBenefitsWidget = new CultWorkoutBenefitsWidget()

        cultWorkoutBenefitsWidget.title = "BENEFIT"
        cultWorkoutBenefitsWidget.benefits = workout.benefits.join(" | ")
        return cultWorkoutBenefitsWidget
    }

    private getCultWorkoutFamilyTagsWidget(workout: CultWorkoutV2) {
        const cultWorkoutFamilyTagsWidget = new CultWorkoutTagsWidget()
        cultWorkoutFamilyTagsWidget.tags = _.map(workout.workoutFamilies, family => {
            return {
                text: family.name,
                bgColour: "#997fff"
            }
        })
        return cultWorkoutFamilyTagsWidget
    }

    private getVideoBannerCarouselWidget(userContext: UserContext, workout: CultWorkoutV2) {
        const userAgent = userContext.sessionInfo.userAgent
        let videoDoc
        let thumbDoc
        if (userAgent === "APP") {
            thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_VIDEO_THUMBNAIL")
            videoDoc = _.find(workout.documents, { tagName: "M_VIDEO" })
        } else {
            thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_VIDEO_THUMBNAIL")
            videoDoc = _.find(workout.documents, { tagName: "D_VIDEO" })
        }

        if (_.isEmpty(videoDoc) || _.isEmpty(thumbDoc) || _.isEmpty(videoDoc.URL) || _.isEmpty(thumbDoc.URL)) {
            this.logger.error("No videoDoc or thumbNail doc found for workoutId: " + workout.id)
            return undefined
        }
        let videoPlayerUrl = "curefit://videoplayer?videoUrl=" + encodeURIComponent(videoDoc.URL)
        videoPlayerUrl += "&absoluteVideoUrl=" + encodeURIComponent(CdnUtil.getCdnUrl(videoDoc.URL))

        const bannerCarouselWidget = new BannerCarouselWidget("345:202", [{
            id: workout.id.toString(),
            image: thumbDoc.URL,
            action: {
                "actionType": "NAVIGATION",
                "url": videoPlayerUrl
            }
        }], {
            bannerWidth: 375,
            bannerHeight: 202,
            noVerticalPadding: false,
            backgroundColor: "white",
            edgeToEdge: false,
            showVideoPlayIcon: true
        }, 1, true, true, false, { title: "Intro to " + workout.name.toUpperCase() })
        bannerCarouselWidget.hasTopPadding = false
        return bannerCarouselWidget
    }

    private async getWorkoutBreakupWidget(workout: CultWorkoutV2) {
        const wodId = workout.defaultWodID
        let wod: SimpleWod = null
        if (wodId) {
            wod = await this.herculeService.getSimpleWodById(wodId)
        }

        if (wod.parts.length != 0) {
            const cultWorkoutSessionBreakupWidget: CultWorkoutSessionBreakupWidget = new CultWorkoutSessionBreakupWidget()
            cultWorkoutSessionBreakupWidget.header = { title: "A typical " + workout.name.toUpperCase() + " Session" }
            cultWorkoutSessionBreakupWidget.startText = "Begin"
            cultWorkoutSessionBreakupWidget.breakup = []
            let endTime = 0
            for (let i = 0; i < wod.parts.length; i++) {
                const part = wod.parts[i]
                endTime += this.getDurationInSecs(part.duration)
                const breakup: WorkoutBreakup = {
                    info: part.heading,
                    duration: this.getDurationInSecs(part.duration),
                    durationText: part.duration.value + " " + capitalizeFirstLetter(part.duration.units),
                    color: colors[i % colors.length]
                }
                cultWorkoutSessionBreakupWidget.breakup.push(breakup)
            }
            cultWorkoutSessionBreakupWidget.endText = (endTime / 60) + " Min"


            return cultWorkoutSessionBreakupWidget
        }
        return null
    }

    private getNotesWidget(workout: CultWorkoutV2) {
        if (_.isEmpty(workout.otherAttributes.notes)) {
            this.logger.error("No Notes object found inside workout with workoutId: " + workout.id)
            return undefined
        }

        const notesWidget = new NoteWidget()
        notesWidget.title = "NOTE"
        notesWidget.notes = workout.otherAttributes.notes
        return notesWidget
    }

    private getDescriptionWidget(workout: CultWorkoutV2) {
        const infoCards: InfoCard[] = []
        const infoCard: InfoCard = {
            subTitle: workout.info
        }
        infoCards.push(infoCard)
        const descriptionWidget = new DescriptionWidget(infoCards)
        descriptionWidget.iconType = undefined
        return descriptionWidget
    }

    private getDurationInSecs(duration: Duration) {
        if (duration.units === "SECONDS") {
            return duration.value
        }
        return duration.value * 60
    }

}
