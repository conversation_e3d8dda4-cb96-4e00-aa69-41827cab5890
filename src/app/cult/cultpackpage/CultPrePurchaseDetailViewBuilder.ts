import { Image<PERSON>ategory, Url<PERSON>athBuilder } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import {
    Action,
    CenterSelectionWidget,
    DatePickerWidget,
    PickerWidget,
    SelectCenterAction,
    UserSelectionWidget,
    WidgetView
} from "../../common/views/WidgetView"
import { TimeUtil } from "@curefit/util-common"
import { injectable } from "inversify"
import * as _ from "lodash"
import CultUtil, { CULT_NAS_PRICE_HIKE_BANNER_ID, MIND_NAS_PRICE_HIKE_BANNER_ID } from "../../util/CultUtil"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../../util/ActionUtil"
import { OfferV2 } from "@curefit/offer-common"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil, { CITY_SPLIT_ENABLED_CITY_IDS } from "../../util/AppUtil"
import { IssueDetailView } from "../../crm/IssueBusiness"
import { IBaseWidget } from "@curefit/vm-models"
import { CountryData } from "../../user/UserController"
import {
    ActionType,
    CardContainerWidget,
    CultPackInfoWidget,
    CultPackPage,
    ListItem,
    ListWidgetV2,
    ProductNote,
    ProductNoteWidget,
    ProductOffer,
    ProductOfferWidgetV2
} from "@curefit/apps-common"
import CultPackCommonViewBuilder, {
    CultPackPageBuilderParams,
    CultPackPageRequestParams,
    CultPackPageRequestParamsV2,
    CultPackStartDateOptions
} from "./CultPackCommonViewBuilder"
import { CultPackType } from "@curefit/cult-common"
import FitnessUtil from "../../util/FitnessUtil"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"
const clone = require("clone")


@injectable()
class CultPrePurchaseDetailViewBuilder extends CultPackCommonViewBuilder {

    async getPrePurchasePage(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CultPackPage> {
        const widgetPromises: Promise<WidgetView | IBaseWidget>[] = []
        const tz = builderParams.userContext.userProfile.timezone
        builderParams.startDateOptions = await this.getStartDateOptions(userContext, requestParams, builderParams)

        const priceHikeBannerWidgetPromise = this.buildPriceHikeBannerWidget(userContext, requestParams)
        const priceHikeBannerWidget = await priceHikeBannerWidgetPromise
        const isPriceHikeBannerWidgetExist = priceHikeBannerWidget ? true : false

        const packOfferWidgetPromise = this.packOfferWidget(builderParams, isPriceHikeBannerWidgetExist)
        const packOfferWidget = await packOfferWidgetPromise
        const isPackOfferWidgetExist = packOfferWidget ? true : false

        widgetPromises.push(this.cultPackInfoWidget(userContext, builderParams, isPackOfferWidgetExist, isPriceHikeBannerWidgetExist))
        if (packOfferWidgetPromise) {
            widgetPromises.push(packOfferWidgetPromise)
        }
        widgetPromises.push(priceHikeBannerWidgetPromise)

        const centerSelectionWidget = this.buildCenterSelectionWidget(userContext, requestParams, builderParams)
        const startDateWidget = this.buildStartDateWidget(userContext, requestParams, builderParams)
        const wrapperWidgets: WidgetView[] = [centerSelectionWidget, startDateWidget]
        widgetPromises.push(this.cardContainerWidget(wrapperWidgets))
        // widgetPromises.push(this.buildBuddyWidget(userContext, requestParams, builderParams))
        if (AppUtil.isNewPackOrderConformationSupported(userContext, (await userContext.userPromise).isInternalUser)) {
            const prePurchaseProductNote = await this.getPrePurchaseProductNote(builderParams, requestParams.productId, requestParams.productType)
            if (prePurchaseProductNote && prePurchaseProductNote.length > 0) {
                widgetPromises.push(this.buildProductNoteWidget(prePurchaseProductNote))
            }
            // widgetPromises.push(this.buildNoCostEmiOfferWidget(builderParams, requestParams.packId, requestParams.productType))
        }
        // widgetPromises.push(this.buildMembershipBenefitsWidget(userContext, requestParams, builderParams))
        const benefitsWidgets = this.getPackBenefits(userContext, requestParams, builderParams)
        benefitsWidgets && widgetPromises.push(benefitsWidgets)
        const policyWidget = this.buildPolicyWidgets(builderParams, userContext)
        if (policyWidget) {
            widgetPromises.push(policyWidget)
        }
        // widgetPromises.push(this.buildWorkoutsWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.howItWorksWidget())
        widgetPromises.push(this.howItWorksWidget(builderParams, userContext))
        const cultPackPrePurchasePage = new CultPackPage()
        cultPackPrePurchasePage.widgets = await Promise.all(widgetPromises)
        cultPackPrePurchasePage.widgets = _.filter(cultPackPrePurchasePage.widgets, widget => !_.isEmpty(widget))
        cultPackPrePurchasePage.actions = await this.getPrePurchasePageActions(userContext, requestParams, builderParams)

        const issues: IssueDetailView[] = await this.issueBusiness.getCultOrMindSubscriptionPrePurchaseIssues(builderParams.packInfo, userContext)
        const analyticsData = {
            // "packId": CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId),
            "productId": builderParams.packInfo.productId,
            "title": builderParams.packInfo.title
        }
        const result = CultUtil.getPackPriceAndOfferIdV2(builderParams.packInfo, await builderParams.offersV3Promise)
        const offerId = !_.isEmpty(result.offerIds) ? result.offerIds[0] : undefined
        cultPackPrePurchasePage.navigationAction = {
            title: "HELP",
            action: {
                actionType: "REPORT_ISSUE",
                meta: {
                    title: "Help",
                    issues: issues
                }
            },
            textStyle: { color: "#ff3278", paddingHorizontal: 10, paddingVertical: 5 },
            containerStyle: { backgroundColor: "#ffffff" },
            analyticsData: analyticsData
        }
        const preferredCenter = builderParams.preferredCenter
        const centerServicePreferredCenterId = builderParams.centerServicePreferredCenterId
        cultPackPrePurchasePage.packSummary = {
            offerId,
            productId: builderParams.packInfo.productId,
            startDateOptions: builderParams.startDateOptions,
            preferredCenterId: preferredCenter ? preferredCenter.id : undefined,
            centerServicePreferredCenterId: centerServicePreferredCenterId,
            productType: builderParams.packInfo.productType,
        }

        if (builderParams.isBuyFlow) {
            cultPackPrePurchasePage.packSummary = {
                ...cultPackPrePurchasePage.packSummary,
            }
            if (builderParams.startDateOptions.selectedDate) {
                if (builderParams.startDateOptions.canChangeStartDate)
                    cultPackPrePurchasePage.packSummary.startDateOptions = TimeUtil.getDaysFrom(tz, builderParams.startDateOptions.selectedDate, 7, false)
                else
                    cultPackPrePurchasePage.packSummary.startDateOptions = [builderParams.startDateOptions.selectedDate]
            }
        }

        return cultPackPrePurchasePage
    }

    private async getStartDateOptions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CultPackStartDateOptions> {
        const isMandatoryStartDateSupported = AppUtil.isMandatoryStartDateSupported(userContext)
        const tz = userContext.userProfile.timezone
        const cultPack = builderParams.packInfo
        const startDate = await FitnessUtil.getEarliestStartDateForFitnessMembership(userContext, cultPack.product.durationInDays, builderParams.centerServicePreferredCenterId, cultPack.title, this.membershipService, this.centerService)
        const { minimumDate, selectedDate, maximumDate } = CultUtil.getPackStartDateOptions(builderParams.userContext, builderParams.preferredCenter, true, requestParams.selectedStartDate, this.cultPackPageConfig.startDateWindow, startDate, undefined)
        const selectedDateFinal = !isMandatoryStartDateSupported ? minimumDate : selectedDate
        return {
            minEligibleDate: minimumDate,
            maxEligibleDate: maximumDate,
            selectedDate: selectedDateFinal,
            canChangeStartDate: true
        }
    }

    private async cultPackInfoWidget(userContext: UserContext, builderParams: CultPackPageBuilderParams, isPackOfferWidgetExist: boolean, isPriceHikeBannerWidgetExist: boolean): Promise<CultPackInfoWidget> {
        const { packInfo } = builderParams
        let price = packInfo.price, title = ""
        const preferredCenter = builderParams.preferredCenter
        const isPartOfGstSegment = await AppUtil.doesUserBelongToGSTsplitExperiment(userContext, this.serviceInterfaces)
        const taxBreakup = await this.offerUtil.getTaxBreakUpForCultAndGymPacks(await builderParams.offersV3Promise)
        const priceBreakupMap = taxBreakup[packInfo.productId]
        let priceBreakup = null
        if (isPartOfGstSegment && priceBreakupMap) {
            priceBreakup = {
                basePrice: priceBreakupMap.basePrice,
                tax: priceBreakupMap.taxAmount
            }
        }
        if (builderParams.isBuyFlow) {
            const result = CultUtil.getPackPriceAndOfferIdV2(packInfo, await builderParams.offersV3Promise)
            price = result.price
            title = packInfo.title
        }
        const category: ImageCategory = ImageCategory.HERO
        const userAgent = builderParams.userContext.sessionInfo.userAgent
        return {
            widgetType: "CULT_PACK_INFO_WIDGET",
            title: title,
            gradientColor: packInfo.productType === "FITNESS" ? ["#fcc161", "#fa9f86"] : ["#bdd9ff", "#b6fdff"],
            price: price,
            dividerType: "NONE",
            image: CatalogueServiceUtilities.getFitnessProductImage(packInfo, category, userAgent),
            contentContainerStyle: isPackOfferWidgetExist ? {} : { marginBottom: -20 },
            priceBreakup: priceBreakup
        }
    }

    private async packOfferWidget(builderParams: CultPackPageBuilderParams, isPriceHikeBannerWidgetExist: boolean): Promise<ProductOfferWidgetV2> {
        const offersResponse = await builderParams.offersPromise
        const offersV3Response = await builderParams.offersV3Promise
        const offerItem = Object.assign({}, offersV3Response ?
            CultUtil.getOfferItem(offersV3Response, builderParams.packInfo.productId) :
            offersResponse[builderParams.packInfo.productId])
        const actionType: ActionType = "SHOW_OFFERS_TNC_MODAL"
        const filteredOfferItems: OfferV2[] = []
        offerItem.offers = OfferUtil.segregateNoCostEMIOffers(offerItem.offers).get("OTHER_OFFERS")
        // Remove fit-club from offers as there is a separate widget
        offerItem.offers.forEach((offer) => {
            if (offer.addons) {
                filteredOfferItems.push(offer)
            } else if (!_.isEmpty(offer.description)) {
                filteredOfferItems.push(offer)
            }
        })
        const offerItems: ProductOffer[] = []
        filteredOfferItems.forEach(offer => {
            let offerText = undefined
            if (offer.uiLabels && !_.isEmpty(offer.uiLabels.cartLabel)) {
                offerText = offer.uiLabels.cartLabel
            } else {
                _.forEach(offer.addons, addon => {
                    if (addon && addon.uiLabels && !_.isEmpty(addon.uiLabels.cartLabel)) {
                        offerText = addon.uiLabels.cartLabel
                    }
                })
            }
            if (!_.isEmpty(offerText)) {
                offerItems.push({
                    title: offerText,
                    iconType: "/image/icons/cult/tick.png",
                    tnc: {
                        title: "T&C",
                        action: {
                            actionType: actionType,
                            meta: {
                                title: "Offer Details",
                                dataItems: offer.tNc,
                                url: offer.tNcUrl
                            }
                        }
                    }
                })
            }
        })

        if (offerItems.length > 0) {
            return {
                widgetType: "PRODUCT_OFFER_WIDGET_V2",
                offerItems: [...offerItems],
                dividerType: "NONE",
                contentContainerStyle: { marginBottom: 10 }
            }
        }
        return null
    }

    private async cardContainerWidget(widgets: WidgetView[]): Promise<CardContainerWidget> {
        return {
            widgets: widgets,
            widgetType: "CARD_CONTAINER_WIDGET",
            dividerType: "DIVIDER30"
        }
    }

    private buildCenterSelectionWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): CenterSelectionWidget {
        const action: SelectCenterAction = {
            title: "Pick a center",
            showHelp: false,
            showFavourite: false,
            actionType: "SELECT_CENTER",
            meta: {
                // packId: CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId),
                productId: builderParams.packInfo.productId,
                productType: requestParams.productType,
                ageCategory: "ADULT",
                showAvailableWorkouts: true,
                pageFrom: requestParams.productType === "FITNESS" ? "CultPack" : "MindPack",
                useCenterServiceId: true,
            },
            productType: requestParams.productType
        }

        const centerSelectionWidget: CenterSelectionWidget = {
            title: "Select center",
            prefixText: "Preferred centre: ",
            canChangeCenter: requestParams.canChangeCenter,
            preferredCenterId: Number(requestParams.selectedCenterId),
            preferredCenterName: requestParams.selectedCenterId && builderParams.preferredCenter ? builderParams.preferredCenter.name : undefined,
            widgetType: "CENTER_PICKER_WIDGET",
            action: action,
            // infoAction: {
            //     actionType: "SHOW_ALERT_MODAL",
            //     meta: {
            //         title: infoActionTitle,
            //         subTitle: infoActionSubTitle,
            //         actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            //     }
            // },
            showHighlightedText: !requestParams.selectedCenterId
        }
        return centerSelectionWidget
    }

    private buildStartDateWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): DatePickerWidget {
        const startDateOptions = builderParams.startDateOptions
        const datePickerWidget: DatePickerWidget = {
            title: "Pick a Start Date",
            startDate: startDateOptions.minEligibleDate,
            endDate: startDateOptions.maxEligibleDate,
            selectedDate: startDateOptions.selectedDate,
            canChangeStartDate: startDateOptions.canChangeStartDate,
            confirmActionType: "SET_CULT_PACK_START_DATE",
            widgetType: "DATE_PICKER_WIDGET"
        }
        return datePickerWidget
    }

    private async getPrePurchaseProductNote(builderParams: CultPackPageBuilderParams, packId: string, productType: string): Promise<ProductNote[] | undefined> {
        const offersResponse = await builderParams.offersPromise
        const offersV3Response = await builderParams.offersV3Promise
        const offerItem = Object.assign({}, offersV3Response ?
            CultUtil.getOfferItem(offersV3Response, builderParams.packInfo.productId) :
            offersResponse[builderParams.packInfo.productId])
        if (!offerItem) {
            return undefined
        }
        const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
        const emiOffers = offersMap.get("NO_COST_EMI")
        if (!emiOffers.length) {
            return undefined
        }
        const cultPack = builderParams.packInfo
        let maxEmiTenure = 12
        emiOffers.forEach((emiOffer) => {
            emiOffer.addons.forEach((addOn) => {
                // Max supported emi duration in months
                if (addOn.config && addOn.config.maxEmiTenure) {
                    maxEmiTenure = addOn.config.maxEmiTenure
                }
            })
        })
        const banks = emiOffers[0].description
        const numberOfMonths = CultUtil.getEmiTenureForPack(cultPack.product.durationInDays, maxEmiTenure)
        const result = CultUtil.getPackPriceAndOfferIdV2(builderParams.packInfo, offersV3Response)
        return [{
            title: {
                plainText: "No cost EMI from ",
                boldText: `${Math.round(result.price.listingPrice / numberOfMonths)}/month*`
            },
            description: _.isEmpty(banks) ? undefined : banks,
            icon: "/image/icons/cult/emi-v1.png",
            action: {
                actionType: "NAVIGATION",
                url: `curefit://nocostemipage?packId=${packId}&productType=${productType}`,
                title: "DETAILS"
            }
        }]
    }

    async buildProductNoteWidget(data: ProductNote[]): Promise<ProductNoteWidget> {
        return {
            widgetType: "PRODUCT_NOTE_WIDGET",
            data: data,
            header: {
                title: "No Cost EMI"
            }
        }
    }

    // private async buildNoCostEmiOfferWidget(builderParams: CultPackPageBuilderParams, packId: string, productType: string): Promise<NoCostEmiWidget> {
    //     const offersResponse = await builderParams.offersPromise
    //     const offerItem = offersResponse[builderParams.packInfo.productId]
    //     if (!offerItem) {
    //         return undefined
    //     }
    //     const offersMap = OfferUtil.segregateNoCostEMIOffers(offerItem.offers)
    //     const emiOffers = offersMap.get("NO_COST_EMI")
    //     if (!emiOffers.length) {
    //         return undefined
    //     }
    //     const cultPack = builderParams.cultPack
    //     let maxEmiTenure = 12
    //     emiOffers.forEach((emiOffer) => {
    //         emiOffer.addons.forEach((addOn) => {
    //             // Max supported emi duration in months
    //             if (addOn.config && addOn.config.maxEmiTenure) {
    //                 maxEmiTenure = addOn.config.maxEmiTenure
    //             }
    //         })
    //     })
    //     const banks = emiOffers[0].description
    //     const numberOfMonths = Math.min(maxEmiTenure, Math.floor(cultPack.duration / 30))
    //     const result = CultUtil.getPackPriceAndOfferId(cultPack, builderParams.packInfo, builderParams.preferredCenter.id, await builderParams.offersPromise)
    //     return new NoCostEmiWidget(
    //         `No cost EMI from ${Math.round(result.price.listingPrice / numberOfMonths)}/month`,
    //         banks,
    //         "NO_COST_EMI",
    //         {
    //             actionType: "NAVIGATION",
    //             url: `curefit://nocostemipage?packId=${packId}&productType=${productType}`,
    //             title: "Know More"
    //         },
    //         "")
    // }

    private async getPackBenefits(userContext: UserContext, requestParams: CultPackPageRequestParams | CultPackPageRequestParamsV2, builderParams: CultPackPageBuilderParams): Promise<ListWidgetV2> {
        const items: ListItem[] = []
        // const offersResponse = await builderParams.offersPromise
        // const offerItem = offersResponse[builderParams.packInfo.productId] // pre-reg has it's own offer system and only those offers apply here
        // if (CultUtil.isMindPackIncludedInCultPack(builderParams.cultPack)) {
        //     items.push({
        //         title: "Access to Mind.fit",
        //         subTitle: "Attend mindful Yoga and Meditation classes in Mind.fit centers at no extra cost",
        //         icon: "/image/icons/howItWorks/yoga.png"
        //     })
        // }
        // if (requestParams.packInfo.type === CultPackType.PRE_REG || requestParams.packInfo.isRestrictedToCenter) {
        //     items.push({
        //         title: "Unlimited access",
        //         subTitle: `to selected center`,
        //         icon: "/image/icons/howItWorks/<EMAIL>"
        //     })
        // } else {
        items.push({
            title: "Unlimited access",
            subTitle: userContext.sessionInfo.osName.toLowerCase() === "ios" ? `to all group classes centers, ELITE & PRO gyms` : `to all group classes centers, ELITE & PRO gyms & live workouts`,
            icon: "/image/icons/howItWorks/<EMAIL>"
        })
        // }
        items.push({
            title: "100% Safe Workouts",
            subTitle: `We follow strict rules to avoid contact and maintain social distancing in all our classes`,
            icon: "/image/icons/howItWorks/<EMAIL>"
        })

        if (builderParams.packInfo.product.pauseDays > 0) {
            items.push({
                title: "Pause your pack anytime",
                subTitle: `You can pause your pack any time for a maximum of ${builderParams.packInfo.product.pauseDays} days`,
                icon: "/image/icons/howItWorks/<EMAIL>"
            })
        }

        // if (CultUtil.isSGTPartOfCultPack(builderParams.packInfo) && CultUtil.isSGTUpgradeLive()) {
        //     items.push({
        //         title: "Free access to Online GX",
        //         subTitle: "No worries if you are not able to attend classes at the center. You can now join classes online",
        //         icon: "/image/icons/cult/sgt_upgrade.png",
        //         tag: {
        //             title: "NEW"
        //         }
        //     })
        // }

        if (items.length > 0) {
            const header = {
                title: "Highlights",
            }
            const packBenefitsWidget = new ListWidgetV2()
            packBenefitsWidget.items = items
            packBenefitsWidget.header = header
            return packBenefitsWidget
        }
        return null
    }

    // private userSelectionModalAction(builderParams: CultPackPageBuilderParams): Action {
    //     return {
    //         title: "Add child details",
    //         actionType: "USER_SELECTION_MODAL",
    //         meta: {
    //             title: "Who is this for?",
    //             usersList: builderParams && builderParams.allSubUsers || [],
    //             action: {
    //                 actionType: "NAVIGATION",
    //                 url: ActionUtil.addSubUserUrl(),
    //                 meta: {
    //                     reqParams: {
    //                         formUserType: "CULT_JUNIOR_USER",
    //                         packId: CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId),
    //                     },
    //                 },
    //             },
    //             calloutText: "This pack is available for children from age 5 to 13 years only"
    //         }
    //     }
    // }

    // private async buildBuddyWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<PickerWidget> {
    //     const isGiftPack = builderParams.packInfo.isGiftPack
    //     const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
    //     const countries = await this.countryService.listCountries(tenant)
    //     const countriesData: CountryData[] = _.map(countries, country => {
    //         return {
    //             countryId: country.countryId,
    //             name: country.name,
    //             countryCallingCode: country.countryCallingCode,
    //             validationCountryCode: country.countryCode
    //         }
    //     })
    //     if (builderParams.packInfo.isGroupPack || isGiftPack) {
    //         const pickerWidget: PickerWidget = {
    //             widgetType: "PICKER_WIDGET",
    //             title: isGiftPack ? "Gift this to" : "Buddy",
    //             subTitle: isGiftPack ? "Pick your giftee" : "Pick your buddy",
    //             canChangeBuddy: true,
    //             action: {
    //                 actionType: "SELECT_CONTACT"
    //             },
    //             selectedOption: undefined
    //         }

    //         if (AppUtil.isAddBuddyInternationalSupported(userContext)) {
    //             pickerWidget.action = {
    //                 actionType: "NAVIGATION",
    //                 url: "curefit://buddytransferpage",
    //                 meta: {
    //                     selectedOption: undefined,
    //                     countriesData,
    //                     defaultCountryCode: userContext.userProfile.city.country.countryId,
    //                     title: isGiftPack ? "Pick your giftee" : "Pick your buddy",
    //                     relationTitle: isGiftPack ? "Giftee name" : "Buddy name",
    //                 }
    //             }
    //         }
    //         return pickerWidget
    //     }
    // }

    private async getPrePurchasePageActions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<Action[]> {
        // if (!requestParams.selectedSubUserId && requestParams.packInfo.ageCategory === "JUNIOR") {
        //     return [this.userSelectionModalAction(builderParams)]
        // }

        if (!requestParams.selectedCenterId) {
            return [{
                title: "Pick a preferred centre",
                showHelp: false,
                showFavourite: false,
                actionType: "SELECT_CENTER",
                productType: requestParams.productType,
                meta: {
                    // packId: CatalogueServiceUtilities.extractPackId(builderParams.packInfo.productId),
                    productType: requestParams.productType,
                    ageCategory: "ADULT",
                    showAvailableWorkouts: true,
                    pageFrom: requestParams.productType === "FITNESS" ? "CultPack" : "MindPack",
                    useCenterServiceId: true,
                },
            }]
        }

        if (!builderParams.startDateOptions.selectedDate) {
            return [{
                title: "Pick a start date",
                actionType: "SHOW_DATE_PICKER",
                meta: {
                    startDate: builderParams.startDateOptions.minEligibleDate,
                    endDate: builderParams.startDateOptions.maxEligibleDate,
                    selectedDate: builderParams.startDateOptions.selectedDate,
                    canChangeStartDate: builderParams.startDateOptions.canChangeStartDate,
                }
            }]
        }

        // dont go to checkout flow for app version which dont support international payment flows
        if (builderParams.derivedCity.city.countryId !== "IN" && !AppUtil.isInternationalPaymentSupported(userContext)) {
            const action = AppActionUtil.appUpdateAction(userContext)
            action.title = "Get pack"
            return [action]
        }

        const city = userContext.userProfile.city
        if (city && city.parentCityId && CITY_SPLIT_ENABLED_CITY_IDS.includes(city.parentCityId) && await AppUtil.isCitySplitFeatureSupported(userContext) && !AppUtil.isCitySplitSelectionSupported(userContext)) {
            return [AppActionUtil.appUpdateAction(userContext, "Buy Now", "Update app to see the latest pack price. Lower prices in Thane & Navi Mumbai vs rest of Mumbai.", "Mumbai is split into two areas")]
        }
        return [{
            title: "BUY NOW",
            actionType: "GET_NEW_CULT_PACK",
        }]
    }

    private async buildPriceHikeBannerWidget(userContext: UserContext, requestParams: CultPackPageRequestParams): Promise<any> {
        const widgetId = requestParams.productType === "FITNESS" ? CULT_NAS_PRICE_HIKE_BANNER_ID : MIND_NAS_PRICE_HIKE_BANNER_ID
        const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            // updating layoutprops in same object was causing issue in same widget displaying in other place
            const bannerWidget = clone(widget)
            bannerWidget.layoutProps.backgroundColor = null
            bannerWidget.hasDividerBelow = false
            bannerWidget.layoutProps.v2 = true
            bannerWidget.dividerType = "DIVIDER30"
            bannerWidget.layoutProps.useShadow = true
            return bannerWidget
        }
    }

}

export default CultPrePurchaseDetailViewBuilder
