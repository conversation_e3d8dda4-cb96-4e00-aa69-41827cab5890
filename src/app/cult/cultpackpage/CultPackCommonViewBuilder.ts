import { CustomerIssueType } from "@curefit/issue-common"
import {
    CultBooking,
    CultCenter,
    CultMembership,
    CultWorkout,
    CultWorkoutV2,
} from "@curefit/cult-common"
import { ProductType } from "@curefit/product-common"
import { ICultServiceOld as ICultService, MembershipDetail } from "@curefit/cult-client"
import { ProductListWidget, RoundedImageGridWidget } from "../../common/views/WidgetView"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import CultPackPageConfig from "../../pack/CultPackPageConfig"
import { inject, injectable } from "inversify"
import IProductBusiness from "../../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as _ from "lodash"
import CultUtil from "../../util/CultUtil"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { OFFER_SERVICE_CLIENT_TYPES, PackOffersResponse, ProductTaxBreakup, OfferUtil } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import ICRMIssueService from "../../crm/ICRMIssueService"
import { CacheHelper } from "../../util/CacheHelper"
import { ICityService, ICountryService, LOCATION_TYPES } from "@curefit/location-mongo"
import { ICultBusiness, MoneyBackOfferDetail } from "../CultBusiness"
import { User } from "@curefit/user-common"
import { City } from "@curefit/location-common"
import IssueBusiness from "../../crm/IssueBusiness"
import { EmiInterestReadonlyDaoMongoImpl, PAYMENT_MODELS_TYPES } from "@curefit/payment-models"
import WidgetBuilder from "../../page/vm/WidgetBuilder"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { Header, ISegmentService, RoundedImageGridItem } from "@curefit/vm-models"
import {
    InfoCard,
    ListItem,
    ListWidgetV2,
    PageTypes,
    ProductNote,
    ProductNoteWidget,
    TabbedContainerWidget,
    WidgetHeader,
    WidgetView
} from "@curefit/apps-common"
import { RoundedImageGridWidgetView } from "../../page/vm/widgets/RoundedImageGridWidgetView"
import { CultProductPricesResponse } from "@curefit/offer-common"
import { Membership } from "@curefit/membership-commons"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

export interface CultPackPageRequestParams {
    baseService: ICultService
    productType: ProductType
    productId: string
    packInfo: OfflineFitnessPack
    membershipDetail?: MembershipDetail
    canChangeCenter: boolean
    selectedStartDate?: string,
    selectedSubUserId?: string,
    selectedCenterId?: string
    subUserRelations: Array<{
        subUserId?: string,
        relation?: string
    }>
}

export interface CultPackPageRequestParamsV2 {
    // baseService: ICultService
    productType: ProductType
    productId: string
    packInfo: OfflineFitnessPack
    membershipDetail?: MembershipDetail
    canChangeCenter: boolean
    selectedStartDate?: string,
    selectedSubUserId?: string,
    selectedCenterId?: string
    subUserRelations: Array<{
        subUserId?: string,
        relation?: string
    }>
}

export interface CultPackPageBuilderParams {
    isBuyFlow?: boolean
    workoutsPromise: Promise<CultWorkout[] | CultWorkoutV2[]>
    offersPromise?: Promise<PackOffersResponse>
    offersV3Promise?: Promise<CultProductPricesResponse>
    bookingsPromise?: Promise<CultBooking[]>
    issuesMapPromise?: Promise<Map<string, CustomerIssueType[]>>
    startDateOptions?: CultPackStartDateOptions
    packInfo: OfflineFitnessPack
    derivedCity: DerivedCityDetails
    preferredCenterId: string
    centerServicePreferredCenterId?: string
    preferredCenter: CultCenter
    preAdvancePaid: boolean
    membership?: Membership
    allSubUsers: Array<User>,
    selectedSubUserDetails?: User,
    moneyBackOfferPromise?: Promise<MoneyBackOfferDetail>
    userContext: UserContext
    colocatedCenterIDs?: string[]
    isPreRegPack: boolean
    membershipTransferable?: MembershipTransferable
    taxBreakup?: ProductTaxBreakup
}

export interface MembershipTransferable {
    value: boolean
    message: string
}

export interface PausePackBuilderParams {
    membership: CultMembership
    userContext: UserContext
}

export interface DerivedCityDetails {
    city?: City
    cultCityId: number
    cityId: string
    defaultCultCenter: string
    defaultMindCenter: string
}

export interface CultPackStartDateOptions {
    minEligibleDate: string
    maxEligibleDate: string
    selectedDate: string
    canChangeStartDate: boolean
}

@injectable()
class CultPackCommonViewBuilder {
    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) protected CRMIssueService: ICRMIssueService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) protected issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.CultBusiness) protected cultBusiness: ICultBusiness,
        @inject(PAYMENT_MODELS_TYPES.EmiInterestReadonlyDao) private paymentDao: EmiInterestReadonlyDaoMongoImpl,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(LOCATION_TYPES.CountryService) protected countryService: ICountryService,
        @inject(CUREFIT_API_TYPES.SegmentService) protected segmentService: ISegmentService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offerUtil: OfferUtil,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService
    ) {

    }

    // async buildMembershipBenefitsWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CultProductHighlightsWidget> {
    //     let centerCount = 1
    //     if (!builderParams.packInfo.isRestrictedToCenter) {
    //         let centerCountInfo = await requestParams.baseService.getCenterCount(builderParams.derivedCity.cultCityId, userContext.userProfile.userId, "CUREFIT_API", true)
    //         if (requestParams.productType === "MIND") {
    //             centerCountInfo = await requestParams.baseService.getCenterCount(builderParams.derivedCity.cultCityId, userContext.userProfile.userId, "CUREFIT_API")
    //         }
    //         centerCount = centerCountInfo.centerCount
    //     }
    //     const displayCenterCount = centerCount > 10 ? `${Math.floor(centerCount / 10) * 10}+` : appendZeroInSingleDigitNumber(centerCount)
    //     const highlights: Highlight[] = [{
    //         title: displayCenterCount.toString(),
    //         subTitle: "Centre\nAccess"
    //     }]
    //     highlights.push({
    //         icon: "/image/icons/cult/infinity.png",
    //         subTitle: "Unlimited\nClasses"
    //     })
    //     if (!builderParams.packInfo.isRestrictedToCenter) {
    //         highlights.push({
    //             title: `${appendZeroInSingleDigitNumber(builderParams.cultPack.awayClassesPerMonth)}/month`,
    //             subTitle: "Outstation\nclasses"
    //         })
    //     }
    //     if (CultUtil.isMindPackIncludedInCultPack(builderParams.cultPack)) {
    //         highlights.push({
    //             title: "FREE",
    //             subTitle: "mind.fit\nAccess"
    //         })
    //     }
    //     return {
    //         widgetType: "CULT_PRODUCT_HIGHLIGHTS_WIDGET",
    //         header: {
    //             title: "Membership Details",
    //             subTitle: "Your membership gives you access to the following "
    //         },
    //         highlights: highlights,
    //         displayTextColor: "black",
    //         contentContainerStyle: { marginHorizontal: 20 }
    //     }
    // }
    async buildPolicyWidgets(builderParams: CultPackPageBuilderParams, userContext?: UserContext): Promise<ListWidgetV2> {
        const isNewPolicyWidgetSupported = await CultUtil.isNewPolicyWidgetSupported(userContext)

        if (isNewPolicyWidgetSupported) {
            const header: WidgetHeader = {
                title: "Policies",
                titleStyle: {
                    marginBottom: 0,
                },
                seemore: {
                    title: "VIEW " || this.cultPackPageConfig.noshowPolicyInfo.seeMoreDescription,
                    url: ActionUtil.infoPage("cultpassblackpolicy"),
                    actionType: "NAVIGATION",
                }
            }

            const policyListWidget = new ListWidgetV2()
            policyListWidget.items = []
            policyListWidget.header = header
            return policyListWidget
        } else {
            const header: WidgetHeader = {
                title: "Policies",
            }
            const actionCards: ListItem[] = []
            actionCards.push({
                title: this.cultPackPageConfig.noshowPolicyInfo.title,
                subTitle: this.cultPackPageConfig.noshowPolicyInfo.subTitle,
                icon: this.cultPackPageConfig.noshowPolicyInfo.icon,
                seeMore: {
                    title: "VIEW " || this.cultPackPageConfig.noshowPolicyInfo.seeMoreDescription,
                    url: ActionUtil.infoPage("noshowpolicy"),
                    actionType: "NAVIGATION",
                }
            })

            if (CultUtil.isCustomerBiometricSupportedInCenter(builderParams.preferredCenter)) {
                actionCards.push({
                    title: this.cultPackPageConfig.biometricInfo.title,
                    subTitle: this.cultPackPageConfig.biometricInfo.subTitle,
                    icon: this.cultPackPageConfig.biometricInfo.icon,
                    seeMore: {
                        title: "VIEW " || this.cultPackPageConfig.biometricInfo.seeMoreDescription,
                        actionType: "NAVIGATION",
                        url: ActionUtil.infoPage("biometric")
                    }
                })
            }
            if (actionCards.length > 0) {
                const policyListWidget = new ListWidgetV2()
                policyListWidget.items = actionCards
                policyListWidget.header = header
                return policyListWidget
            }
        }
        return null
    }

    // async howItWorksWidget(): Promise<CardVideoWidget> {
    //     const videoWidgetItem: CardVideoItem = {
    //         image: FIRST_CLASS_POST_WORKOUT_THUMBNAIL,
    //         videoUri: FIRST_CLASS_POST_WORKOUT_VIDEO,
    //         thumbnailVideoUri: "",
    //         displayPlayIcon: true,
    //         header: "How it works",
    //         headerStyle: { fontSize: 24, marginTop: 0 }
    //     }
    //     return (new CardVideoWidget(videoWidgetItem))
    // }

    async howItWorksWidget(builderParams: CultPackPageBuilderParams, userContext: UserContext): Promise<ProductListWidget> {
        try {
            const header: Header = {
                title: this.cultPackPageConfig.howItWorksTitle,
                titleProps: {
                    style: {
                        fontSize: 24
                    }
                }
            }

            const infoCards: InfoCard[] = []
            const howItWorksItemList = userContext.sessionInfo.osName.toLowerCase() === "ios" ? this.cultPackPageConfig.howItWorksItemListIOS : this.cultPackPageConfig.howItWorksItemList
            howItWorksItemList.forEach(item => {
                infoCards.push({
                    subTitle: item.text,
                    icon: item.icon,
                    subTitleFontSize: 14
                })
            })
            return {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                hideSepratorLines: true,
                header: header,
                items: infoCards,
                noTopPadding: true
            }
        }
        catch (e) {
            return undefined
        }
    }


    async buildProductNoteWidget(data: ProductNote[]): Promise<ProductNoteWidget> {
        return {
            widgetType: "PRODUCT_NOTE_WIDGET",
            data: data,
            header: {
                title: "Cancel Membership"
            }
        }
    }

    /**
     * @deprecated: Prereg and old select flows are deprecated
     */
    async buildWorkoutsWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<any> {
        // let showWorkoutWidget = true

        // // hide workouts widget in unlimited pack pages till we solve for new widget
        // if (!requestParams.packInfo.isRestrictedToCenter) {
        //     showWorkoutWidget = false
        // }
        // const centerIds: string[] = []

        // const isPreRegPack: boolean = builderParams.isPreRegPack

        // if (requestParams.packInfo.isRestrictedToCenter) {
        //     const cultBenefit = builderParams.membership && builderParams.membership.benefits.find(benefit => benefit.name == "CULT")
        //     const allowedCultCenterIds = cultBenefit ? cultBenefit.meta["allowedCenterIDs"] || [] : []
        //     const centerIdsForWorkouts = (!_.isEmpty(allowedCultCenterIds) ? allowedCultCenterIds : requestParams.packInfo.colocatedCenterIDs) || []
        //     centerIds.push(...centerIdsForWorkouts)
        // } else if (isPreRegPack) {
        //     const membershipCenterId = builderParams.membership && builderParams.membership.metadata["centerServiceCenterId"] ? await CultUtil.getCultCenterIdFromCenterService(builderParams.membership.metadata["centerServiceCenterId"], this.centerService) : undefined
        //     const centerId = membershipCenterId ? membershipCenterId : builderParams.preferredCenterId
        //     centerIds.push(centerId)
        // }

        // if (showWorkoutWidget) {
        //     const showCultSGTWorkouts = CultUtil.isSGTPartOfCultPack(builderParams.packInfo) && AppUtil.isCultSGTUpgradeAppSupported(userContext) && CultUtil.isSGTUpgradeLive()
        //     let liveSGTWorkoutWidgetPromise
        //     if (showCultSGTWorkouts) {
        //         const roundedImageGridWidgetView: RoundedImageGridWidgetView = new RoundedImageGridWidgetView()
        //         roundedImageGridWidgetView["productType"] = "LIVE_SGT"
        //         liveSGTWorkoutWidgetPromise = roundedImageGridWidgetView.buildView(this.serviceInterfaces, userContext)
        //     }
        //     const workouts = await builderParams.workoutsPromise as CultWorkout[]
        //     if (!_.isEmpty(workouts)) {
        //         const header: Header = {
        //             title: "Workouts",
        //             seemore: builderParams.packInfo.isRestrictedToCenter ? null : {
        //                 title: "SEE ALL",
        //                 actionType: "NAVIGATION",
        //                 url: `curefit://${PageTypes.CultWorkoutPageV2All}?centerIds=${centerIds.join(",")}&productType=${requestParams.productType}`
        //             },
        //             titleProps: {
        //                 style: {
        //                     fontSize: 22,
        //                     fontFamily: "BrandonText-Bold"
        //                 }
        //             }
        //         }
        //         const workoutActions: RoundedImageGridItem[] = []
        //         workouts.forEach(workout => {
        //             const seoParams: SeoUrlParams = {
        //                 productName: workout.name
        //             }
        //             const workoutAction = ActionUtil.cultWorkoutV2(workout.id.toString(), undefined, userContext.sessionInfo.userAgent, seoParams, requestParams.productType, PageTypes.CultWorkoutPageV2, undefined, centerIds.join(","))
        //             let thumbDoc = undefined
        //             if (userContext.sessionInfo.userAgent === "APP") {
        //                 thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_CARD")
        //             } else {
        //                 thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_CARD")
        //             }
        //             workoutActions.push({
        //                 title: workout.name,
        //                 image: thumbDoc ? "/" + thumbDoc.URL : undefined,
        //                 action: { actionType: "NAVIGATION", url: workoutAction }
        //             })
        //         })
        //         if (showCultSGTWorkouts) {
        //             const cultWorkoutsWidget = new RoundedImageGridWidget(undefined, workoutActions)
        //             const liveSGTWorkoutsWidget = await liveSGTWorkoutWidgetPromise
        //             const workoutsWidget: TabbedContainerWidget = {
        //                 widgetType: "TABBED_CONTAINER_WIDGET",
        //                 // @ts-ignore
        //                 header,
        //                 items: [
        //                     {
        //                         title: "At Center",
        //                         widgets: [cultWorkoutsWidget]
        //                     },
        //                     {
        //                         title: "Online",
        //                         widgets: [liveSGTWorkoutsWidget]
        //                     }
        //                 ]
        //             }
        //             return workoutsWidget
        //         } else {
        //             // @ts-ignore
        //             const workoutsWidget: RoundedImageGridWidget = new RoundedImageGridWidget(header, workoutActions)
        //             return workoutsWidget
        //         }
        //     }
        // }
    }
}



export default CultPackCommonViewBuilder
