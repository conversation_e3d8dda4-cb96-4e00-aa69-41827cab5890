import {
    Cult<PERSON>enter,
    CultMembership,
} from "@curefit/cult-common"
import { MindPack } from "@curefit/mind-common"
import { ProductDetailPage, } from "../../common/views/WidgetView"
import { CatalogueServiceUtilities } from "../../util/CatalogueServiceUtilities"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService } from "@curefit/catalog-client"
import CultPackPageConfig from "../../pack/CultPackPageConfig"
import { inject, injectable } from "inversify"
import IProductBusiness from "../../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as _ from "lodash"
import CultUtil from "../../util/CultUtil"
import { CultOfferRequestParams, CultProductPricesResponse } from "@curefit/offer-common"
import {
    IOfferServiceV2,
    OFFER_SERVICE_CLIENT_TYPES,
    OfferServiceV3,
    PackOffersResponse,
    OfferUtil
} from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../../util/AppUtil"
import ICRMIssueService from "../../crm/ICRMIssueService"
import { CacheHelper } from "../../util/CacheHelper"
import { ICityService, ICountryService, LOCATION_TYPES } from "@curefit/location-mongo"
import IssueBusiness from "../../crm/IssueBusiness"
import WidgetBuilder from "../../page/vm/WidgetBuilder"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import CultPrePurchaseDetailViewBuilder from "./CultPrePurchaseDetailViewBuilder"
import CultPostPurchaseDetailViewBuilder from "./CultPostPurchaseDetailViewBuilder"
import { CultPackPageBuilderParams, CultPackPageRequestParams, DerivedCityDetails } from "./CultPackCommonViewBuilder"
import { Tenant } from "@curefit/user-common"
import { Membership } from "@curefit/membership-commons"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import { BASE_TYPES, Logger } from "@curefit/base"

@injectable()
abstract class CultPackDetailViewBuilderV2 {
    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.CRMIssueService) protected CRMIssueService: ICRMIssueService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) protected issueBusiness: IssueBusiness,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) protected serviceInterfaces: CFServiceInterfaces,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.CultPrePurchaseDetailViewBuilder) private cultPrePurchaseDetailViewBuilder: CultPrePurchaseDetailViewBuilder,
        @inject(CUREFIT_API_TYPES.CultPostPurchaseDetailViewBuilder) private cultPostPurchaseDetailViewBuilder: CultPostPurchaseDetailViewBuilder,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) protected centerService: ICenterService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferUtil) protected offerUtil: OfferUtil,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
    ) {

    }

    abstract getDefaultCenterId(derivedCity: DerivedCityDetails): string
    abstract getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string
    abstract getCenterServicePreferredCenterId(userContext: UserContext): string
    abstract getOffersPromise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<PackOffersResponse>
    abstract getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse>
    abstract isSourceMembership(membership: CultMembership): boolean

    async getView(userContext: UserContext, requestParams: CultPackPageRequestParams): Promise<ProductDetailPage> {
        let membership: Membership
        let membershipTransferable
        if (requestParams.membershipDetail) {
            membership = requestParams.membershipDetail.membership
            membershipTransferable = requestParams.membershipDetail.membershipTransferable
        }

        const derivedCityDetailsPromise = this.getDerivedCityDetails(userContext, requestParams.packInfo.clientMetadata.cityId)

        const derivedCity = await derivedCityDetailsPromise
        const defaultCenterId = this.getDefaultCenterId(derivedCity)
        const preferredCenterId = this.getPreferredCenterId(userContext, derivedCity)
        const centerServicePreferredCenterId = this.getCenterServicePreferredCenterId(userContext)
        // const isPreRegPack: boolean = requestParams.packInfo.type === CultPackType.PRE_REG
        let fitnessWorkoutPromise

        // if (requestParams.packInfo.isRestrictedToCenter) {
        //     const cultBenefit = membership && membership.benefits.find(benefit => benefit.name == "CULT")
        //     const allowedCultCenterIds = cultBenefit ? cultBenefit.meta["allowedCenterIDs"] || [] : []
        //     const centerIds = [preferredCenterId, ...allowedCultCenterIds]
        //     fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2(undefined, false, true, true, "CUREFIT_API", userContext.userProfile.userId, derivedCity.cultCityId, centerIds)
        // }
        // else if (isPreRegPack) {
        //     const membershipCenterId = membership && membership.metadata["centerServiceCenterId"] ? await CultUtil.getCultCenterIdFromCenterService(membership.metadata["centerServiceCenterId"], this.centerService) : undefined
        //     const centerId = membershipCenterId ? membershipCenterId : preferredCenterId
        //     fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2(undefined, false, true, true, "CUREFIT_API", userContext.userProfile.userId, derivedCity.cultCityId, [centerId])
        // }
        // else {
        fitnessWorkoutPromise = requestParams.baseService.browseWorkoutsV2(undefined, false, true, true, "CUREFIT_API", userContext.userProfile.userId, derivedCity.cultCityId)
        // }
        const preferredCenterPromise = this.getPreferredCenter(requestParams, membership, defaultCenterId, preferredCenterId)
        let selectSubUserPromise, selectedSubUserDetails
        const allSubUsersPromise = Array.isArray(requestParams.subUserRelations) && requestParams.subUserRelations.map(async (subUsers) => {
            const userPromise = this.userCache.getUser(subUsers.subUserId)
            if (requestParams.selectedSubUserId === subUsers.subUserId) {
                selectSubUserPromise = userPromise
            }
            return await userPromise
        })
        if (selectSubUserPromise) {
            selectedSubUserDetails = selectSubUserPromise
        }
        const allSubUsers = allSubUsersPromise && await Promise.all(allSubUsersPromise) || []
        const builderParams: CultPackPageBuilderParams = {
            workoutsPromise: fitnessWorkoutPromise,
            packInfo: requestParams.packInfo,
            derivedCity: derivedCity,
            preferredCenterId: preferredCenterId,
            centerServicePreferredCenterId: centerServicePreferredCenterId,
            preferredCenter: await preferredCenterPromise,
            preAdvancePaid: false,
            membership: membership,
            allSubUsers: allSubUsers,
            selectedSubUserDetails: selectedSubUserDetails,
            userContext: userContext,
            isPreRegPack: false,
            membershipTransferable: membershipTransferable

        }
        builderParams.isBuyFlow = !membership
        if (builderParams.isBuyFlow) {
            // PackId to ProductId migration: ignoring: this flow will be deprecated fully when the checkout page from every flow completely move to flutter
            // anyway later it's just changing this packId to productId
            builderParams.offersPromise = undefined
            builderParams.offersV3Promise = this.getOffersV3Promise(userContext, requestParams.productId, builderParams.preferredCenterId)
            return this.cultPrePurchaseDetailViewBuilder.getPrePurchasePage(userContext, requestParams, builderParams)
        } else {
            return this.cultPostPurchaseDetailViewBuilder.getPostPurchasePage(userContext, requestParams, builderParams, this.centerService, this.serviceInterfaces)
        }
    }

    private async getDerivedCityDetails(userContext: UserContext, packCityId: string): Promise<DerivedCityDetails> {
        const cityId = packCityId || userContext.userProfile.cityId
        const city = await this.cityService.getCityById(cityId)
        return { city, cultCityId: city.cultCityId, cityId: city.cityId,
            defaultCultCenter: city.defaultCultCenter, defaultMindCenter: city.defaultMindCenter
        }
    }

    private async getPreferredCenter(requestParams: CultPackPageRequestParams, membership: Membership, defaultCenterId: string, preferredCenterId: string): Promise<CultCenter> {
        let preferedCenter
        if (membership && membership.metadata["centerServiceCenterId"]) {
            const cultCenterId = await CultUtil.getCultCenterIdFromCenterService(membership.metadata["centerServiceCenterId"], this.centerService)
            if (cultCenterId) {
                preferedCenter = await requestParams.baseService.getFitnessCenter(cultCenterId)
            }
        } else if (preferredCenterId) {
            preferedCenter = await requestParams.baseService.getFitnessCenter(preferredCenterId)
        }
        if (_.isNil(preferedCenter) && defaultCenterId) {
            preferedCenter = await requestParams.baseService.getFitnessCenter(defaultCenterId)
        }
        return preferedCenter
    }
}

@injectable()
export class CultFitPackDetailViewBuilderV2 extends CultPackDetailViewBuilderV2 {

    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultCultCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.cultCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }

    getCenterServicePreferredCenterId(userContext: UserContext): string {
        const centerServiceId = userContext.sessionInfo.sessionData.centerServiceCultCenterId
        return centerServiceId
    }

    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }

    public async getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        let centerServiceId
        if (preferredCenterId) {
            centerServiceId = await CultUtil.getCenterServiceIdFromCultCenterId(+preferredCenterId, this.centerService)
        }
        this.logger.info(`offerLog: getOffersV3Promise centerServiceId ${centerServiceId}`)
        return await this.offerServiceV3.getCultPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user?.email,
                phone: user?.phone,
                workEmail: user?.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            centerServiceId: centerServiceId ? String(centerServiceId) : undefined,
            productIds: [productId],
        })
    }

    isSourceMembership(membership: CultMembership): boolean {
        return membership.sourceTenant === "cult.fit"
    }
}

@injectable()
export class CultMindPackDetailViewBuilderv2 extends CultPackDetailViewBuilderV2 {

    getDefaultCenterId(derivedCity: DerivedCityDetails): string {
        return derivedCity.defaultMindCenter
    }

    getPreferredCenterId(userContext: UserContext, derivedCity: DerivedCityDetails): string {
        const centerId = userContext.sessionInfo.sessionData.mindCenterId
        return (centerId && centerId !== "undefined" ? centerId : this.getDefaultCenterId(derivedCity))
    }

    getCenterServicePreferredCenterId(userContext: UserContext): string {
        const centerServiceId = userContext.sessionInfo.sessionData.centerServiceCultCenterId
        return centerServiceId
    }

    async getOffersPromise(userContext: UserContext, packId: string, preferredCenterId: string): Promise<PackOffersResponse> {
        return undefined
    }

    async getOffersV3Promise(userContext: UserContext, productId: string, preferredCenterId: string): Promise<CultProductPricesResponse> {
        const city = this.cityService.getCityById(userContext.userProfile.cityId)
        const user = await userContext.userPromise
        return await this.offerServiceV3.getMindPackPrices({
            cultCityId: city.cultCityId,
            cityId: city.cityId,
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user?.email,
                phone: user?.phone,
                workEmail: user?.workEmail
            },
            source: AppUtil.callSourceFromContext(userContext),
            centerId: preferredCenterId,
            productIds: [productId]
        })
    }

    isSourceMembership(membership: CultMembership): boolean {
        return membership.sourceTenant === "mind.fit"
    }
}
export default CultPackDetailViewBuilderV2
