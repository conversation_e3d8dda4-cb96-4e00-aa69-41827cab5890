import {
    Action,
    CenterSelectionWidget,
    ManageOptions,
    ManageOptionsWidget,
    SelectCenterAction,
    WidgetView, PauseInfo, ManageOptionPayload
} from "../../common/views/WidgetView"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { injectable } from "inversify"
import * as _ from "lodash"
import CultUtil, { DUBAI_ACTUAL_CITY_ID, DUBAI_CULT_CITY_ID, isMembershipCurrent } from "../../util/CultUtil"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../../util/ActionUtil"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil, { CREDIT_PILL_ICON, getAppUrl, SUPPORT_DEEP_LINK } from "../../util/AppUtil"
import { User } from "@curefit/user-common"
import { IssueDetailView } from "../../crm/IssueBusiness"
import {
    CultActivePackInfoWidget,
    PageTypes,
    CultPackPage,
    HelpWidget,
    HelpItem,
    WidgetHeader,
    ProductNote,
    CardContainerWidget
} from "@curefit/apps-common"
import CultActivePackInfoWidgetViewBuilder from "../viewbuilder/CultActivePackInfoWidget"
import CultPackCommonViewBuilder, { CultPackPageRequestParams, CultPackPageBuilderParams, MembershipTransferable } from "./CultPackCommonViewBuilder"
import { CultMembership } from "@curefit/cult-common"
import { MindPack } from "@curefit/mind-common"
import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import GymfitUtil from "../../util/GymfitUtil"
import { ICenterService } from "@curefit/center-service-client"
import { CenterResponse, CenterVertical } from "@curefit/center-service-common"
import { Product } from "@curefit/product-common"
import { AppTenant } from "@curefit/base-common"
import { MembershipItemUtil } from "../../util/MembershipItemUtil"
import { CFServiceInterfaces } from "../../page/vm/ServiceInterfaces"
import { CardContainerCellWidget } from "../../page/PageWidgets"
import FitnessUtil, { upgradeTypes } from "../../util/FitnessUtil"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"
import { ISegmentService } from "@curefit/vm-models"

@injectable()
class CultPostPurchaseDetailViewBuilder extends CultPackCommonViewBuilder {

    async getPostPurchasePage(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams, centerService: ICenterService, serviceInterfaces: CFServiceInterfaces): Promise<CultPackPage> {
        const tz = builderParams.userContext.userProfile.timezone

        builderParams.issuesMapPromise = this.CRMIssueService.getIssuesMap()
        builderParams.moneyBackOfferPromise = this.cultBusiness.checkMoneyBackOfferOnMembership(builderParams.membership, userContext) // PMS::DEPR Remove (Depricated flow)
        // dont show only purchased center in colocated centers widget

        const cultBenefit = builderParams.membership && builderParams.membership.benefits.find(benefit => benefit.name == "CULT")
        const allowedCultCenterIDs: string[] = cultBenefit ? cultBenefit.meta["allowedCenterIDs"] || [] : []
        if (!_.isEmpty(allowedCultCenterIDs) && allowedCultCenterIDs.length > 1) {
            builderParams.colocatedCenterIDs = allowedCultCenterIDs
        }
        const widgetPromises: Promise<WidgetView>[] = []
        widgetPromises.push(this.buildCultActivePackInfoWidget(userContext, requestParams, builderParams, tz, centerService, serviceInterfaces))
        const postPurchaseProductNote = await this.getPostPurchaseProductNote(userContext, requestParams, builderParams)
        if (postPurchaseProductNote && postPurchaseProductNote.length > 0) {
            widgetPromises.push(this.buildProductNoteWidget(postPurchaseProductNote))
        }
        const membershipCredits = MembershipItemUtil.getMembershipAccessCredits(this.serviceInterfaces.rollbarService, builderParams.membership)
        let remainingCredits
        if (!_.isNil(membershipCredits)) {
            remainingCredits = membershipCredits?.remainingCredits
            const wrapperWidgets: WidgetView[] = [this.creditCardCellWidget(remainingCredits, builderParams.membership.id)]
            widgetPromises.push(this.cardContainerWidget(wrapperWidgets))
        }
        widgetPromises.push(this.buildColocatedCentersWidget(userContext, requestParams, builderParams))
        const issues: IssueDetailView[] = await this.issueBusiness.getCultOrMindSubscriptionPrePurchaseIssues(builderParams.packInfo, userContext)
        widgetPromises.push(this.activePackHelpWidget(issues))
        // widgetPromises.push(this.buildMembershipBenefitsWidget(userContext, requestParams, builderParams))
        // widgetPromises.push(this.buildWorkoutsWidget(userContext, requestParams, builderParams))
        const policyWidget = this.buildPolicyWidgets(builderParams, userContext)
        if (policyWidget) {
            widgetPromises.push(policyWidget)
        }
        // widgetPromises.push(this.howItWorksWidget())
        widgetPromises.push(this.howItWorksWidget(builderParams, userContext))
        const cultPackPostPurchasePage = new CultPackPage()
        cultPackPostPurchasePage.widgets = await Promise.all(widgetPromises)
        cultPackPostPurchasePage.widgets = _.filter(cultPackPostPurchasePage.widgets, widget => !_.isEmpty(widget))
        cultPackPostPurchasePage.actions = await this.getPostPurchasePageActions(userContext, requestParams, builderParams, remainingCredits)
        const packInfo = builderParams.packInfo
        const preferredCenter = builderParams.preferredCenter
        cultPackPostPurchasePage.packSummary = {
            productId: builderParams.packInfo.productId,
            startDateOptions: builderParams.startDateOptions,
            preferredCenterId: preferredCenter ? preferredCenter.id : undefined,
            productType: builderParams.packInfo.productType,
            // packId: packInfo.cultPackId,
            // duration: packInfo.numDays,
            // price: packInfo.price,
            // canChangeCenter: requestParams.canChangeCenter,
            // preferredCenterName: preferredCenter ? preferredCenter.name : undefined,
        }
        if (AppUtil.isProductHelpButtonSupported(userContext)) {
            const membership = builderParams.membership
            const subscriptionIssues = await this.issueBusiness.getCultOrMindSubscriptionIssuesV2(userContext, membership, packInfo.productType)
            cultPackPostPurchasePage.navigationAction = {
                title: "HELP",
                action: {
                    actionType: "NAVIGATION",
                    meta: {
                        title: "Help",
                        issues: subscriptionIssues
                    },
                    url: SUPPORT_DEEP_LINK
                },
                textStyle: {color: "#ff3278", paddingHorizontal: 10, paddingVertical: 5},
                containerStyle: {backgroundColor: "#ffffff"},
            }
        }
        return cultPackPostPurchasePage
    }

    private async getPostPurchaseProductNote(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<ProductNote[] | undefined> {
        const offerDetail = await builderParams.moneyBackOfferPromise
        if (!_.isEmpty(offerDetail) && !_.isEmpty(offerDetail.cancellationWindow)) {
            const tz = userContext.userProfile.timezone
            const cancellationEndDate = TimeUtil.formatDateStringInTimeZone(offerDetail.cancellationWindow.endDate, tz, "DD MMM YYYY")
            if (offerDetail.isOfferApplied) {
                return [{
                    title: {
                        plainText: "Cancellation allowed till: ",
                        boldText: cancellationEndDate
                    },
                    icon: "/image/icons/cult/cancel_refund.png",
                }]
            }
        }
    }

    private creditCardCellWidget(creditsLeft: number, membershipId: number): CardContainerCellWidget {
        return {
            widgetType: "CARD_CONTAINER_CELL_WIDGET",
            title: creditsLeft + " Credit" + ((creditsLeft === 1) ? "" : "s") + " left",
            subTitle: "View transaction history",
            prefixImage: CREDIT_PILL_ICON,
            action: {
                actionType: "NAVIGATION",
                url: "curefit://credit_history_page?membershipId=" + membershipId,
                analyticsData: {
                    creditsLeft,
                    membershipId
                },
                viaDeepLink: true,
                meta: {
                    viaDeeplink: true,
                }
            }
        }
    }

    private async cardContainerWidget(widgets: WidgetView[]): Promise<CardContainerWidget> {
        return {
            widgets: widgets,
            widgetType: "CARD_CONTAINER_WIDGET",
            dividerType: "DIVIDER30"
        }
    }

    private getReportIssueAction(reportIssue: ManageOptionPayload, membership: Membership): Action {
        return {
            title: "HELP",
            actionType: "NAVIGATION",
            iconUrl: "/image/icons/cult/info.png",
            meta: reportIssue.meta,
            url: SUPPORT_DEEP_LINK
        }
    }

    private getStartDateDisableAction(): Action {
        return {
            iconUrl: "/image/icons/cult/calendar_black1.png",
            title: "CHANGE\nSTART DATE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Start date cannot be changed",
                subTitle: "Membership start date cannot be changed as membership already started",
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
    }

    private getPausePackDisableAction(pausePackData: any): Action {
        return {
            iconUrl: "/image/icons/cult/pause_black.png",
            title: "PAUSE",
            actionType: "SHOW_ALERT_MODAL",
            isDisabled: true,
            meta: {
                title: "Pack can not be paused",
                subTitle: pausePackData.manageOptions.subTitle,
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }

    }

    private getDisableTransferMembershipAction(membershipTransferable: MembershipTransferable): Action {
        return {
            iconUrl: "/image/icons/cult/transfer_black.png",
            title: "TRANSFER",
            isDisabled: true,
            actionType: "SHOW_ALERT_MODAL",
            meta: {
                title: "Membership can't be transferred",
                subTitle: membershipTransferable.message,
                actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
            }
        }
    }

    private async getCancelMembershipAction(userContext: UserContext, builderParams: CultPackPageBuilderParams, packInfo: OfflineFitnessPack, membership: Membership, productId: string): Promise<Action | null> {
        // const moneyBackOfferDetail = await builderParams.moneyBackOfferPromise
        // if (!_.isEmpty(moneyBackOfferDetail) && moneyBackOfferDetail.isOfferApplied) { // Deprecated
        //     const cancelMembershipAction: Action = {
        //         iconUrl: "/image/icons/cult/time_pink.png",
        //         actionType: "NAVIGATION",
        //         url: `curefit://cancelsubscriptionpage?pageType=cult&productType=${packInfo.productType}&orderId=${membership.orderId}&membershipId=${membership.metadata["membershipId"]}&packId=${CatalogueServiceUtilities.extractPackId(productId)}&pageFrom=money_back_offer&dummyParam=dummy`,
        //         title: "CANCEL &\nREFUND",
        //         meta: {
        //             title: "Cancel Membership"
        //         }
        //     }
        //     if (moneyBackOfferDetail.isOfferExpired) {
        //         const cancellationEndDate = TimeUtil.getMomentForDateString(moneyBackOfferDetail.cancellationWindow.endDate, userContext.userProfile.timezone).format("D MMM YYYY")
        //         cancelMembershipAction.actionType = "SHOW_ALERT_MODAL"
        //         cancelMembershipAction.iconUrl = "/image/icons/cult/time_disabled.png"
        //         cancelMembershipAction.meta = {
        //             title: "Membership can not be cancelled",
        //             subTitle: `Sorry! Your membership can no longer be cancelled. Last date to cancel your membership was ${cancellationEndDate}`,
        //             actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Okay" }]
        //         }
        //         cancelMembershipAction.isDisabled = true
        //     }
        //     return cancelMembershipAction
        // }
        return null
    }

    private getSelectToEliteUpgradeAction(membership: Membership, product: OfflineFitnessPack, userContext: UserContext, cancelPauseAction?: Action, packPauseResumeAction?: Action): Action {
        const pauseAlert = CultUtil.getAlertWhileUpgradingDuringPause(membership, "Upgrade to Cultpass ELITE", cancelPauseAction, packPauseResumeAction)
        if (pauseAlert) {
            return pauseAlert
        }
        return FitnessUtil.getUpgradeAction(upgradeTypes.ELITE_SELECT_TO_ELITE, membership.id.toString(), product.productId)
    }

    private getUpgradeMembershipAction(membership: Membership, packInfo: OfflineFitnessPack, userContext: UserContext, cancelPauseAction?: Action, packPauseResumeAction?: Action): Action {
        const upgradeTitle = "UPGRADE\nTO UNLIMITED"
        const pauseAlert = CultUtil.getAlertWhileUpgradingDuringPause(membership, upgradeTitle, cancelPauseAction, packPauseResumeAction)
        if (pauseAlert) {
            return pauseAlert
        }
        return FitnessUtil.getUpgradeAction(upgradeTypes.PRO_TO_ELITE, membership.id.toString(), packInfo.productId)
    }

    private async getRenewPackAction(userContext: UserContext, builderParams: CultPackPageBuilderParams, centerService: ICenterService): Promise<Action> {
        let isSelectMembership = MembershipItemUtil.isSelectMembership(builderParams.membership)
        if (builderParams?.membership?.metadata?.jpmcMembership) {
            isSelectMembership = false
        }
        if (isSelectMembership) {
            const selectCenterId = await CultUtil.getSelectCenterIdForMembership(userContext, builderParams.membership, centerService)
            return {
                iconUrl: "/image/icons/cult/renew.png",
                title: "RENEW",
                actionType: "NAVIGATION",
                url: "curefit://fitnesscenter?centerId=" + selectCenterId
            }
        }
        return {
            iconUrl: "/image/icons/cult/renew.png",
            title: "RENEW",
            actionType: "NAVIGATION",
            url: await AppActionUtil.getCultPackBrowseWidgetNavigationUrl(userContext, this.segmentService)
        }
    }

    async getCultPackActions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams, user: User, isSelectMembership: boolean, centerService: ICenterService, isJPMCMembership: boolean, islimitedEliteMembership: boolean, serviceInterfaces: CFServiceInterfaces) {
        const { packInfo } = builderParams
        const membership = builderParams.membership
        const { osName, appVersion, cpVersion } = userContext.sessionInfo
        const actions: Action[] = []
        let showPackTransferDisabledState = false
        let showStartDateDisabledState = false, showPausePackDisableState = false
        if (this.isRenewEnabled(userContext, builderParams, membership.productId) || await AppUtil.isCenterLevelRenewalEnabled(userContext,  this.serviceInterfaces.segmentService)) {
            actions.push(await this.getRenewPackAction(userContext, builderParams, centerService))
        }

        if (AppUtil.isPackStartDateChangeSupported(userContext, user)) {
            /**
             * check if membershipStartDate is Editable
             */
            const membershipStartDateNotEditable = (membership.status === "PURCHASED" && isMembershipCurrent(userContext, membership)) || membership.status === "PAUSED"
            if (!membershipStartDateNotEditable && membership.id) {
                actions.push(CultUtil.getChangeStartDateActionV2(membership, packInfo.productType))
            } else if (AppUtil.isAnyActionSupportedInManageOptions(userContext)) {
                showStartDateDisabledState = true
            }

        }

        const pausePackData = await CultUtil.getPackPauseResumeDetailsV2(membership, requestParams.productType, userContext, serviceInterfaces)
        let packPauseResumeAction
        if (pausePackData && pausePackData.isPauseAllowed) {
            const isNewPauseEnabled = await AppUtil.doesUserBelongsToNewPauseExperiment(userContext, serviceInterfaces)
            packPauseResumeAction = GymfitUtil.getPackPauseResumeAction(pausePackData, membership, isNewPauseEnabled)
            if (packPauseResumeAction) {
                actions.push(packPauseResumeAction)
            }
        } else if (pausePackData && pausePackData.isPauseAllowed === false) {
            showPausePackDisableState = true
        }

        const cancelPauseAction = await this.getCancelPauseAction(membership, requestParams.productType, userContext, membership.productId)
        if (cancelPauseAction) {
            actions.push(cancelPauseAction)
        }

        const transferSupported = !MembershipItemUtil.isPlusMembership(membership) && !AppUtil.isEnterpriseMembership(membership) && !isSelectMembership && !islimitedEliteMembership && AppUtil.isTransferMembershipSupported(userContext, false)
                                    && packInfo.clientMetadata.cityId !== DUBAI_ACTUAL_CITY_ID && membership.metadata["membershipId"] && !GymfitUtil.ENTERPRISE_JPMC_PRODUCT_IDS.includes(requestParams.packInfo.productId)
        if (transferSupported) {
            if (builderParams.membershipTransferable && builderParams.membershipTransferable.value === false) {
                showPackTransferDisabledState = true
            } else {
                actions.push(FitnessUtil.getTransferMembershipPageAction(membership.id.toString()))
            }
        }

        if (!isSelectMembership && !islimitedEliteMembership && AppUtil.isPackUpgradeSupported(userContext) && !MembershipItemUtil.isPlusMembership(membership)) {
            const cultBenefit = membership.benefits.find(benefit => benefit.name == "CULT")
            const isPackUpgradable = !membership.metadata["isTransferredPack"] && (membership.status == "PURCHASED" && cultBenefit?.meta["allowedCenterServiceCenterIDs"])
            if (isPackUpgradable) {
                actions.push(this.getUpgradeMembershipAction(membership, packInfo, userContext, cancelPauseAction, packPauseResumeAction))
            }
        }

        if (isSelectMembership) {
            const product: OfflineFitnessPack = await this.catalogueServicePMS.getProduct(membership.productId)
            actions.push(this.getSelectToEliteUpgradeAction(membership, product, userContext, cancelPauseAction, packPauseResumeAction))
        }


        if (membership.metadata["membershipId"]) {
            const cancelMembershipAction = await this.getCancelMembershipAction(userContext, builderParams, packInfo, membership, membership.productId)
            if (cancelMembershipAction) {
                actions.push(cancelMembershipAction)
            }
        }

        if (!AppUtil.isProductHelpButtonSupported(userContext)) {
            const issues = await this.issueBusiness.getCultOrMindSubscriptionIssuesV2(userContext, membership, packInfo.productType)
            const reportIssue = this.issueBusiness.toManageOptionPayload(issues, true)
            actions.push(this.getReportIssueAction(reportIssue, membership))
        }

        if (showPausePackDisableState) {
            actions.push(this.getPausePackDisableAction(pausePackData))
        }

        if (showStartDateDisabledState) {
            const tz = userContext.userProfile.timezone
            const todayDate = TimeUtil.todaysDate(tz, "YYYY-MM-DD")
            if (todayDate < TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd"))
                actions.push(this.getStartDateDisableAction())
        }

        if (showPackTransferDisabledState) {
            actions.push(this.getDisableTransferMembershipAction(builderParams.membershipTransferable))
        }

        if (isSelectMembership) {
            const status = await serviceInterfaces.cultFitService.getTransferValidationStatus(membership.userId, membership.id.toString(), AppTenant.CUREFIT)
            if (status.result) {
                actions.push(FitnessUtil.getTransferMembershipPageAction(membership.id.toString()))
            } else {
                actions.push(this.getDisableTransferMembershipAction({message: status.message, value: false}))
            }
        }

        return actions
    }
    getViewAuditAction(membershipId: CultMembership["id"]): Action {
        const pageTitle = "View History"
        const linkTitle = pageTitle.toUpperCase()
        const queryParams = {
            membershipId,
            title: pageTitle,
        }
        return {
            iconUrl: "/image/audit/audit-icon5.png",
            isEnabled: true,
            title: linkTitle,
            actionType: "NAVIGATION",
            url: getAppUrl(PageTypes.MembershipAuditTrail, queryParams)
        }
    }

    getRightInfo(membership: Membership, userContext: UserContext, tz: Timezone) {
        if (membership.status === "PAUSED") {
            const pauseEndDate = membership.activePause ? TimeUtil.formatEpochInTimeZone(tz, membership.activePause.end) : undefined
            if (pauseEndDate) {
                const todaysDate = TimeUtil.todaysDate(tz)
                const diffDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDate, pauseEndDate)
                return diffDays === 0 ? `Resumes Tonight` : `Resumes in ${diffDays} days`
            }
        }
        return null
    }

    private async buildCultActivePackInfoWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams, tz: Timezone, centerService: ICenterService, serviceInterfaces: CFServiceInterfaces): Promise<CultActivePackInfoWidget> {
        const { packInfo, membership } = builderParams
        const user = await this.userCache.getUser(userContext.userProfile.userId)
        let actions
        if (membership.type != "COMPLIMENTARY" && MembershipItemUtil.isCurrentOrFutureActiveMembership(membership)) {
            const isSelectMembership = MembershipItemUtil.isSelectMembership(membership)
            const isJPMCMembership = membership?.metadata?.jpmcMembership ? true : false
            const islimitedEliteMembership = membership != null && (membership.metadata?.limitedSessions === true || membership.metadata?.isCultpassX === true)
            actions = await this.getCultPackActions(userContext, requestParams, builderParams, user, isSelectMembership, centerService, isJPMCMembership, islimitedEliteMembership, serviceInterfaces)
        }

        const rightInfoText = this.getRightInfo(membership, userContext, tz)
        const activePackInfoParams = {
            userContext,
            packInfo,
            membership,
            tz,
            actions,
            rightInfoText,
        }

        return new CultActivePackInfoWidgetViewBuilder().buildView(activePackInfoParams, centerService)
    }

    private async activePackHelpWidget(issues: IssueDetailView[]): Promise<HelpWidget> {
        const header: WidgetHeader = {
            title: "FAQs",
            seemore: {
                actionType: "REPORT_ISSUE",
                meta: {
                    title: "Help",
                    issues: issues
                },
                title: "VIEW ALL"
            }
        }
        const items: HelpItem[] = issues.map((issue) => {
            return {
                title: issue.issueDescription,
                action: {
                    ...issue.action,
                    meta: {
                        ...issue.action.meta,
                        issueId: issue.issueId,
                        title: "Help"
                    }
                }
            }
        }).slice(0, 3)
        const helpWidget = new HelpWidget()
        helpWidget.header = header
        helpWidget.items = items

        return helpWidget

    }

    private async buildColocatedCentersWidget(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams): Promise<CenterSelectionWidget> {
        if (AppUtil.isCultCentersViewOnlyModeSupported(userContext)) {
            if (!_.isEmpty(builderParams.colocatedCenterIDs)) {
                const action: SelectCenterAction = {
                    title: "Colocated Centers",
                    showHelp: false,
                    productType: requestParams.productType,
                    actionType: "SELECT_CENTER",
                    showFavourite: false,
                    meta: {
                        centerIds: builderParams.colocatedCenterIDs,
                        viewOnly: true
                    }
                }
                const centerSelectionWidget: CenterSelectionWidget = {
                    title: "See centres you have access to",
                    canChangeCenter: true,
                    widgetType: "CENTER_PICKER_WIDGET",
                    action: action
                }
                return centerSelectionWidget
            }
        }
    }

    private getPauseInfo(tz: Timezone, pausedUsed: number, packEndDate: string, pauseDaysLeft: number): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        return [
            { title: "Pause days used", value: AppUtil.appendDays(pausedUsed) },
            { title: "Membership will be extended by", value: AppUtil.appendDays(pausedUsed) },
            { title: "Membership will now end on", value: endsOn },
            { title: "Pause days left", value: AppUtil.appendDays(pauseDaysLeft) }
        ]
    }

    private async getCancelPauseAction(membership: Membership, productType: string, userContext: UserContext, productId: string): Promise<Action | null> {
        const cancelPauseData = await this.buildUpcomingPauseWidget(membership, productType, userContext, productId)
        if (cancelPauseData) {
            return {
                iconUrl: "/image/icons/cult/resume.png",
                title: "CANCEL\nPAUSE",
                actionType: cancelPauseData.manageOptions.options[0].type,
                meta: cancelPauseData.meta
            }
        }
        return null
    }

    private async buildUpcomingPauseWidget(membership: Membership, productType: string, userContext: UserContext, productId: string): Promise<ManageOptionsWidget> {
        const tz = userContext.userProfile.timezone
        if (GymfitUtil.isUpcomingPause(membership)) {
            const isOptionEnabled = AppUtil.isCultPauseCancelSupported(userContext)
            const pauseStartDate = membership.activePause ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.start) : undefined
            const pauseEndDate = membership.activePause ? membership.activePause.end ? TimeUtil.parseDateFromEpochWithTimezone(tz, membership.activePause.end) : undefined : undefined
            const pauseEndDateFormatted = TimeUtil.formatDateInTimeZoneDateFns(tz, pauseEndDate, "yyyy-MM-dd")
            const pauseStartDateFormatted = TimeUtil.formatDateInTimeZoneDateFns(tz, pauseStartDate, "yyyy-MM-dd")
            let pauseEndDateWithTime
            if (pauseEndDate) {
                pauseEndDateWithTime = TimeUtil.getDefaultMomentForDateString(pauseEndDateFormatted, tz).startOf("day").subtract(1, "minute").toDate()
            }
            const startDateText = `${TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "DD MMM hh:mm a")}`
            const daysDiff = TimeUtil.diffInDaysReal(tz, TimeUtil.todaysDate(tz), pauseStartDateFormatted)
            const remainingDaysInCurrentDuration = daysDiff < 0 ? 0 : daysDiff
            const limit = TimeUtil.addDays(tz, TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), remainingDaysInCurrentDuration)
            const remainingPauseDays = GymfitUtil.convertMillisToDays(membership.remainingPauseDuration)
            const meta: any = {
                membershipId: membership.id,
                productType: productType,
                title: "Cancel Pause",
                pauseMaxDays: GymfitUtil.convertMillisToDays(membership.maxPauseDuration),
                remainingPauseDays: remainingPauseDays,
                remainingDaysInCurrentDuration,
                pauseEndDate: pauseEndDate ? TimeUtil.formatDateInTimeZoneDateFns(tz, pauseEndDate, "yyyy-MM-dd") : undefined,
                startDateParams: {
                    date: TimeUtil.todaysDate(tz),
                    limit,
                    canEdit: false,
                    pauseEndText: "Your pack is paused till"
                },
                action: {
                    primaryText: "YES",
                    secondaryText: "NO"
                },
                pauseInfoTitles: {
                    pauseUsed: "Pause days used",
                    membershipExtended: "Membership will be extended by",
                    membershipEndsOn: "Membership will now end on",
                    pauseLeft: "Pause days left"
                },
                pauseInfo: this.getPauseInfo(tz, remainingDaysInCurrentDuration, limit, (remainingPauseDays - remainingDaysInCurrentDuration)),
                dateParam: {
                    date: TimeUtil.formatDateInTimeZone(tz, pauseStartDate, "YYYY-MM-DD hh:mm A"),
                    limit,
                    pauseEndDate: pauseEndDateWithTime ? TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a") : undefined,
                    pauseEndText: `Pause starting ${startDateText} till`
                },
                subTitle: `You are cancelling pause starting tonight till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}. Your pause days remain intact`,
                refreshPageOnCompletion: true
            }
            const manageOptions: ManageOptions = {
                displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                icon: "PAUSE",
                options: [{
                    isEnabled: isOptionEnabled,
                    type: "CANCEL_CULT_PACK_PAUSE",
                    displayText: AppUtil.isCultPauseCancelSupported(userContext) ? "Cancel Pause" : "Pause Scheduled",
                    meta
                }],
                info: {
                    title: "About Pause",
                    subTitle: "You can pause your pack when you can't use it like during travel or illness.\n\nOnce you resume, your pack is extended by the number of days you were on pause"
                }
            }
            manageOptions.subTitle = pauseStartDate && pauseEndDateWithTime ? `Pause starting ${startDateText} till ${TimeUtil.formatDateInTimeZone(tz, pauseEndDateWithTime, "DD MMM hh:mm a")}` : ""
            const pauseWidget = new ManageOptionsWidget(manageOptions, meta)
            pauseWidget.isDisabled = !isOptionEnabled
            return pauseWidget
        }
    }

    private async getPostPurchasePageActions(userContext: UserContext, requestParams: CultPackPageRequestParams, builderParams: CultPackPageBuilderParams, centerCredits?: number): Promise<Action[]> {
        let isSelectMembership = MembershipItemUtil.isSelectMembership(builderParams.membership)
        if (builderParams?.membership?.metadata?.jpmcMembership) {
            isSelectMembership = false
        }
        const isCultpassXMembership = builderParams.membership != null && builderParams.membership.metadata?.isCultpassX === true
        if (isSelectMembership) {
            let actionurl
            let actionTitle
            const selectCenters: string[] = []
            builderParams.membership.attributes.forEach(a => {
                if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                    selectCenters.push(a.attrValue)
                }
            })
            let gym
            let cultCenter
            for (const centerId of selectCenters) {
                    const center: CenterResponse = await this.centerService?.getCenterById(Number(centerId))
                    if (center.vertical == CenterVertical.CULT) {
                        cultCenter = center
                    } else {
                        gym = center
                    }
            }
            if (!_.isEmpty(gym)) {
                const centerId = gym.meta.gymfitCenterId
                actionurl = "curefit://gymquickcheckin?centerId=" + centerId
                if (!_.isNil(centerCredits)) {
                    actionurl += "&centerCredit" + centerCredits
                }
                actionTitle = "Check in"
            } else if (!_.isEmpty(cultCenter)) {
                const centerId = cultCenter.meta.cultCenterId
                actionurl = "curefit://classbookingv2?centerId=" + centerId
                actionTitle = "Book Now"
            }
            return [{
                title: actionTitle,
                url: actionurl,
                actionType: "NAVIGATION"
            }]
        }
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext)
        const classBookingAction = ActionUtil.getBookCultClassUrl(builderParams.packInfo.productType, isNewClassBookingSupported, "packDetailPage", undefined)
        if (isCultpassXMembership) {
            return [{
                title: "BOOK CLASS",
                url: classBookingAction,
                actionType: "NAVIGATION"
            }, {
                title: "VIEW GYMS",
                url: "curefit://allgyms?centerType=ONEPASS",
                actionType: "NAVIGATION"
            }]
        }
        return [{
            title: "BOOK CLASS",
            url: classBookingAction,
            actionType: "NAVIGATION"
        }]
    }

    private isRenewEnabled(userContext: UserContext, builderParams: CultPackPageBuilderParams, productId: string): boolean {
        const membership = builderParams.membership
        const membershipState = CultUtil.getMembershipStateV2(membership, userContext.userProfile.timezone)
        return (membershipState === "EXPIRED" || membershipState === "EXPIRING") && productId === builderParams.packInfo.productId
    }
}
export default CultPostPurchaseDetailViewBuilder
