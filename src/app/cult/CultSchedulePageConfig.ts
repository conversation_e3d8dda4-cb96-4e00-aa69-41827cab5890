import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import * as _ from "lodash"
import { UrlPathBuilder } from "@curefit/product-common"
import { HowItWorksItem, WhatsInThePackItem } from "@curefit/product-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class CultSchedulePageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 10 * 60)
        this.load("CultSchedulePageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "CultSchedulePageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.oneDayScheduleEnabled = data.oneDayScheduleEnabled
            this.oneDayScheduleStart = data.oneDayScheduleStart
            this.oneDayScheduleEnd = data.oneDayScheduleEnd
            return data
        })
    }

    public oneDayScheduleEnabled: boolean
    public oneDayScheduleStart: {hour: number, min: number}
    public oneDayScheduleEnd: {hour: number, min: number}

}

export default CultSchedulePageConfig
