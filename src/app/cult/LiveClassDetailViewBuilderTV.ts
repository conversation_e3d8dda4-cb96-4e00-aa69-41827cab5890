import * as _ from "lodash"
import { UserContext, BaseWidget, TemplateWidget } from "@curefit/vm-models"
import {
    ProductDetailPage,
    Action,
    WidgetView,
    CultBuddiesJoiningListLargeView
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { injectable, inject } from "inversify"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import {
    DigitalCatalogueEntryV1,
    ImageData,
    LiveClass,
    SocialDataResponse
} from "@curefit/diy-common"
import { BannerWidget } from "../page/PageWidgets"
import { User } from "@curefit/user-common"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import LiveUtil, { CULT_LIVE_CAMPAIGN_ID } from "../util/LiveUtil"
import { UserAgentType, HourMin } from "@curefit/base-common"
import AppUtil from "../util/AppUtil"
import { ICultBusiness, PreferenceDetail } from "./CultBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import { SlotsInfo, LiveSlotSelectionWidget, DateWiseLiveSlots, IWidgetType, PageTypes } from "@curefit/apps-common"
import WodViewBuilder, { IWODDetail } from "./WodViewBuilder"
import { Timezone } from "@curefit/util-common"
import { LiveClassSlotCreator } from "./liveClassSlot/LiveClassSlotCreator"
import { UNAUTHORIZED_ERROR_CODE } from "@curefit/error-client"
import { builVanillaLiveClassViewV2 } from "../digital/VanillaLiveClassDetailViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { LiveClassDetailViewV2, getDatewiseSlotInfo, getBuddiesInfo } from "./LiveClassDetailViewBuilderV2"
import { LivePackUtil } from "../util/LivePackUtil"

export class LiveClassDetailViewTV extends ProductDetailPage {
    pageType: string
    isFullScreen: boolean
    changeBackgroundColorIndex: number
    bannerImages: ImageData
    isRetelecast?: boolean
    refreshPageEpoch: number
    classDuration: number
    isLocked: boolean

    constructor(widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[], actions: Action[], isFullScreen: boolean, bannerImages: ImageData, isRetelecast: boolean, refreshPageEpoch: number, classDuration: number, isLocked: boolean) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.bannerImages = bannerImages
        this.pageType = "liveSessionDetail"
        this.isFullScreen = isFullScreen
        this.isRetelecast = isRetelecast
        this.refreshPageEpoch = refreshPageEpoch
        this.classDuration = classDuration
        if (!this.isFullScreen) {
            this.changeBackgroundColorIndex = 1
        }
        this.isLocked = isLocked
    }
}

@injectable()
class LiveClassDetailViewBuilderTV {
    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) private wodViewBuilder: WodViewBuilder,
        @inject(CUREFIT_API_TYPES.LiveClassSlotCreator) protected liveClassSlotCreator: LiveClassSlotCreator,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness
    ) {

    }

    async buildView(userContext: UserContext, liveClassId: string, selectedClassId: string, isFullScreen: boolean, blockIfInternationalUser?: boolean, nodeRelationId?: number, isSuccessiveClassCall?: boolean, isNoSlotSelected?: boolean): Promise<LiveClassDetailViewTV | LiveClassDetailViewV2> {
        let widgets: any[] = []
        const actions: Action[] = []
        const userId = userContext.userProfile.userId
        const countryId = AppUtil.getCountryId(userContext)
        let classResponse: LiveClass = null
        if (isSuccessiveClassCall) {
            classResponse = await this.liveClassSlotCreator.getLiveCLassTimeSlot(liveClassId, userId)
        }

        if (!classResponse) {
            try {
                classResponse = await this.diyFulfilmentService.getLiveClass(liveClassId, userId, AppUtil.getTenantFromUserContext(userContext), countryId)
                await this.liveClassSlotCreator.createLiveCLassTimeSlot(liveClassId, classResponse, userId)
            } catch (err) {
                if (err.statusCode && err.statusCode === UNAUTHORIZED_ERROR_CODE) {
                    this.logger.error("Failure while creating live class time slot ", err)
                    return null
                }
            }
        }
        // this.logger.debug("class response " + JSON.stringify(classResponse))
        const classId = selectedClassId !== "undefined" ? selectedClassId : classResponse.slots.length > 0 ? classResponse.slots[0].classId : undefined
        let selectedTimeSlot = undefined
        if (classResponse && classResponse.slots && classResponse.slots.length > 0) {
            selectedTimeSlot = _.find(classResponse.slots, slot => slot.classId === classId)
        }

        let selectedClassResponse: DigitalCatalogueEntryV1 = null
        if (!isNoSlotSelected) {
            selectedClassResponse = await this.diyFulfilmentService.getDigitalCatalogueEntry(classId)
        }
        // this.logger.debug("selected class response " + JSON.stringify(selectedClassResponse))
        const subscribedVideos: string[] = await this.diyFulfilmentService.getSubscribedVideos(userContext.userProfile.userId)
        const cultPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
        const user: User = await userContext.userPromise
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)

        const isSubscribed = selectedClassResponse ? _.includes(subscribedVideos, (<any>selectedClassResponse)._id) : false
        const bannerImages = classResponse.bannerImages
        const isLocked = selectedTimeSlot ? AppUtil.isLiveContentLocked(userContext, selectedTimeSlot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial) : false

        const { card } = await this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)

        if (LiveUtil.isVanillaLiveFitFormat(classResponse.format)) {
            return await builVanillaLiveClassViewV2(classResponse, subscribedVideos, cultPreference, userContext, classId, this.classInviteLinkCreator, this.serviceInterfaces, selectedClassResponse, isNoSlotSelected, isLocked, isUserEligibleForMonetisation)
        }

        let socialData: SocialDataResponse
        if (nodeRelationId) {
            await this.diyFulfilmentService.addSocialNode(userContext.userProfile.userId, classId, nodeRelationId)
        }
        const socialDataEternalPromise = eternalPromise(this.diyFulfilmentService.getSocialDataForSession(userContext.userProfile.userId, classId, !AppUtil.isLiveLazyInviteLinkActionSupported(userContext), AppUtil.getTenantFromUserContext(userContext)))
        if (socialDataEternalPromise) {
            socialData = (await socialDataEternalPromise).obj
        }

        const bookingPref = (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
        let pageAction: Action = null
        if (isNoSlotSelected) {
            pageAction = {
                title: "PICK A SLOT",
                actionType: "NAVIGATION",
                disabled: true
            }
        } else if (classResponse.slots.length > 0) {
            pageAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, selectedClassResponse, subscribedVideos, userContext.sessionInfo, 2, "session_detail_page_cta",
                bookingPref, this.classInviteLinkCreator, isLocked, this.serviceInterfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, "0", socialData, null,
                card && { ...card, source: "class-details-invite" }, isSubscribed, false, this.announcementBusiness))).obj
            // const sessionStartDetails = await this.diyFulfilmentService.getSessionStartDetails(classResponse.liveClassId, { userId }, "LIVE")
            // let liveUrl
            // if (sessionStartDetails.contentType === "LIVE") {
            //     const isInternalUser = (await userContext.userPromise).isInternalUser
            //     const isVOD = LiveUtil.isVodSession(sessionStartDetails.preferredStreamType)
            //     const streamData = isVOD ? sessionStartDetails.vodStreamDetails.streamData : sessionStartDetails.liveStreamDetails.streamData
            //     liveUrl = LiveUtil.getLiveVideoUrl(userContext.sessionInfo, streamData, isVOD, isInternalUser, false)
            // } else {
            //     liveUrl = sessionStartDetails.diyStreamDetails.url
            // }
            // pageAction.meta = { ...pageAction.meta, liveUrl }
        } else {
            pageAction = { actionType: "POP_ACTION", title: "EXPLORE MORE" }
        }
        if (pageAction) {
            actions.push(pageAction)
        }
        widgets.push(await getBuddiesInfo(this.cultBusiness, userContext, selectedClassResponse, classId, socialData, isSubscribed, this.userCache, this.logger, this.classInviteLinkCreator))
        widgets.push(this.buildBenefitsWidget(classResponse))
        widgets.push(this.getSlotsInfo(userContext, classResponse, classId, isNoSlotSelected))
        widgets.push(this.buildDescriptionWidget(classResponse))
        widgets = widgets.filter(x => x != null)
        const liveClassDetailViewTV = new LiveClassDetailViewTV(widgets, actions, isFullScreen, bannerImages, false, selectedClassResponse.playerStartTimeEpoch, classResponse.duration, isLocked)
        return liveClassDetailViewTV
    }

    private getSlotsInfo(userContext: UserContext, classResponse: LiveClass, classId: string, isNoSlotSelected: boolean): LiveSlotSelectionWidget {
        const tz = userContext.userProfile.timezone
        return {
            widgetType: "LIVE_SLOT_SELECTION_WIDGET",
            datesAvailable: getDatewiseSlotInfo(classResponse, tz, classId, isNoSlotSelected)
        }
    }

    private buildDescriptionWidget(classResponse: LiveClass) {
        return {
            widgetType: "DESCRIPTION_WIDGET",
            title: "About",
            description: classResponse.description
        }
    }

    private buildBenefitsWidget(classResponse: LiveClass) {
        try {
            const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(classResponse.duration / 1000)
            let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
            if (durationHourMin.hour > 0) {
                formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
            }
            const guranteeInfo = [{
                "title": classResponse.intensityLevel,
                "icon": "/image/tvAppIcons/low.png"
            },
            {
                "title": "Supports Energy Meter",
                "icon": "/image/tvAppIcons/energy.png"
            }]
            const title = "LIVE • " + formattedTimeString
            const guranteeWidget = {
                widgetType: "HEADER_WIDGET",
                topTitle: title,
                widgetTitle: {
                    title: classResponse.title,
                },
                image: classResponse.bannerImages?.mobileImage,
                items: guranteeInfo
            }
            return guranteeWidget
        } catch (e) {
            return undefined
        }
    }

}

export default LiveClassDetailViewBuilderTV
