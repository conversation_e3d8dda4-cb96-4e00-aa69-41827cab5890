import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { CultWorkoutPage } from "../common/views/WidgetView"
import { CultWorkoutV2 } from "@curefit/cult-common"
import * as _ from "lodash"
import { PageTypes } from "@curefit/apps-common"
import { RoundedImageGridItem } from "@curefit/vm-models"
import { ProductType } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import CultUtil from "../util/CultUtil"

@injectable()
export class CultWorkoutsViewBuilder {

    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultService,
                @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultService,
                @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness) {
    }

    async getView(userContext: UserContext, centerIds: string[], productType: ProductType) {
        const userId = userContext.userProfile.userId
        const cultCityId = userContext.userProfile.city.cultCityId
        const baseService = productType === "MIND" ? this.mindFitService : this.cultFitService
        let allWorkouts: CultWorkoutV2[] = await baseService.browseWorkoutsV2(undefined, true, false, true, "CUREFIT_API", userId, cultCityId, centerIds)
        const workoutFamilies = new Map<string, RoundedImageGridItem[]>()
        const isBaddyFitSupported: boolean = await CultUtil.isBaddyFitSupported(userContext, this.hamletBusiness)

        if (!isBaddyFitSupported) {
            const baddyFitWorkouts: number[] = await CultUtil.getBaddyFitWorkoutIds()
            allWorkouts = _.filter(allWorkouts, (workout) => {
                const workoutPresent: number = _.find(baddyFitWorkouts, (workoutId: number) => {
                    if (workoutId === workout.id) {
                        return true
                    }
                    return false
                })
                if (workoutPresent) {
                    return false
                }
                return true
            })
        }

        _.forEach(allWorkouts, workout => {
            const families = workout.workoutFamilies
            _.forEach(families, family => {
                if (!workoutFamilies.has(family.name)) {
                    workoutFamilies.set(family.name, [])
                }
                workoutFamilies.get(family.name).push(this.getWorkoutItem(workout, family.id, centerIds, productType, userContext))
            })
        })

        const workoutInfo: { family: string, info: RoundedImageGridItem[] }[] = []
        workoutInfo.push({
            family: "All",
            info: _.map(allWorkouts, workout => this.getWorkoutItem(workout, undefined, centerIds, productType, userContext))
        })
        workoutFamilies.forEach((value: RoundedImageGridItem[], key: string) => {
            workoutInfo.push({
                family: key,
                info: value
            })
        })
        const workoutPage = new CultWorkoutPage()
        workoutPage.title = "Explore all our workouts"
        workoutPage.workoutFamilies = ["All", ...Array.from(workoutFamilies.keys())]
        workoutPage.workoutFamilyVsWorkout = workoutInfo
        return workoutPage
    }


    private getWorkoutItem(workout: CultWorkoutV2, workoutFamilyId: number, centerIds: string[], productType: ProductType, userContext: UserContext): RoundedImageGridItem {
        const document = _.find(workout.documents, {tagName: "PRODUCT_BNR"})
        const seoParams: SeoUrlParams = {
          productName: workout.name
        }
        let url = ActionUtil.cultWorkoutV2(workout.id.toString(), undefined, userContext.sessionInfo.userAgent, seoParams, productType, PageTypes.CultWorkoutPageV2)
        if (!_.isNil(workoutFamilyId)) {
            url = url + "&workoutFamilyId=" + workoutFamilyId
        }
        return {
            title: workout.name,
            image: !_.isEmpty(document) ? document.URL : undefined,
            action: {
                actionType: "NAVIGATION",
                url: url
            }
        }
    }
}
