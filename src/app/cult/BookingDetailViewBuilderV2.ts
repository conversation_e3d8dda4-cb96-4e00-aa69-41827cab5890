import { Cdn<PERSON>til, <PERSON>Util, Timezone } from "@curefit/util-common"
import {
    CultBooking,
    CultCenter,
    CultMembership,
    CultBookingResponse, SocialDataResponse, BookingTypes, CultWorkoutV2, CultWorkout, CultClass, WaitlistProbability
} from "@curefit/cult-common"
import { Address, FoodProduct as Product } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { StartEndTime, UserAgentType } from "@curefit/base-common"
import { Feedback } from "@curefit/feedback-common"
import {
    Action,
    ActionCard,
    ActionCardWidget,
    CalloutWidget, CallReminderSlotTime, CallReminderWidget,
    DescriptionWidget,
    InfoCard, ShareActionWidget,
    ManageOptionPayload, ManageOptions, ManageOptionsWidget,
    OTPInfoWidget,
    PackInfoWidget,
    ProductDetailPage,
    ProductFeedbackWidget,
    WidgetType,
    WidgetView,
    WorkoutSnackWidget, ProductGridWidget, <PERSON><PERSON>, BookingAction, CultBuddiesJoiningListSmallView
} from "../common/views/WidgetView"
import CenterView from "./CenterView"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import CultUtil, {
    CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME,
    CULTSCORE_WORKOUT_IDS,
    getCafeEatClpUrl,
    getCultCafeWidget,
    getNewCultCafeWidget,
    getTimeSlotsForWaitlistExtensionWidget,
    CULT_SHOW_NOTES_POST_BOOKING_CENTER_IDS,
    CULT_SHOW_NOTES_POST_BOOKING_WORKOUT_IDS,
    QR_CODE_DISABLED_SEGMENT,
    BOXING_BAG_WORKOUT_ID,
    BOXING_GLOVES_PROVIDED_CENTER_IDS,
    HATHA_YOGA_WORKOUT_ID,
    YOGA_WORKOUT_ID,
    CLASS_POST_CLASS_PAGE_BANNER_WIDGET_ID_PROD,
    STRENGTH_PULSE,
    PILATES_WORKOUT_IDS
} from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import { DisplayMovement, SimplePart, SimpleWod } from "@curefit/fitness-common"
import WodViewBuilder, { IWODDetail } from "./WodViewBuilder"
import { CultWodWidget } from "../common/views/CultWodWidget"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { CacheHelper } from "../util/CacheHelper"
import { KiosksDemandService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import VMPageBuilder from "../page/vm/VMPageBuilder"
import { FoodFulfilment } from "@curefit/order-common"
import {
    BaseOfferRequestParams,
    BulkApiOfferType,
    BulkOfferAPIRequest,
    BulkOfferAPIResponse
} from "@curefit/offer-common"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { DeliveryArea, Kiosk, MealSlot, MenuType } from "@curefit/eat-common"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import { UrlPathBuilder } from "@curefit/product-common"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import AppUtil from "../util/AppUtil"
import { ICultBusiness } from "./CultBusiness"
import EatUtil from "../util/EatUtil"
import { CAESAR_CLIENT_TYPES, IMenuService } from "@curefit/caesar-client"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import { IWidgetType, PageTypes, WODInfoWidget, ManageOption, CultWaitlistSlotInfo, CultBuddiesJoiningListSmallWidget } from "@curefit/apps-common"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import { MEMBERSHIP_CLIENT_TYPES, IMembershipService } from "@curefit/membership-client"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import {
    CollapsibleProperties,
    DescriptionWidgetV2,
    FacilitiesWidget,
    Facility,
    InstructionsWithMediaV2,
    MapWidgetV2,
    ProductGridWidgetV2,
    WODInfoWidgetV2,
    WaitlistConfirmationTimePicker,
    WaitlistInfo,
    WaitlistWidget,
    WorkoutComponent,
    WorkoutMediaData,
    WorkoutMovementInfo,
    WorkoutWaitlistTimingSlot,
    CreditPillData
} from "../page/PageWidgets"
import { MediaType } from "@curefit/gymfit-common"
import { IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import UserUtil, { LocationDataKey, LocationPreferenceResponseEntity } from "../util/UserUtil"
import { LatLong, LocationUtil } from "@curefit/location-common"
import { SquadClassBookingLite } from "@curefit/cult-client/dist/src/ICultServiceOld"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import { BaseWidget } from "@curefit/vm-models"
import CultClassUtil from "../util/CultClassUtil"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"
import { Membership } from "@curefit/membership-commons"
import { BoxFit } from "@curefit/vm-common"

@injectable()
class BookingDetailViewBuilderV2 extends ProductDetailPage {

    constructor(@inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) private wodViewBuilder: WodViewBuilder,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) private kiosksDemandService: KiosksDemandService,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
        @inject(CUREFIT_API_TYPES.VMPageBuilder) private pageBuilder: VMPageBuilder,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CAESAR_CLIENT_TYPES.MenuService) private menuService: IMenuService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) protected classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) protected membershipService: IMembershipService,
        @inject(CUREFIT_API_TYPES.CultClassUtil) private cultClassUtil: CultClassUtil) {
        super()
    }

    async getViewV2(userContext: UserContext,
        productType: ProductType,
        bookingResponse: CultBookingResponse,
        feedback: Feedback,
        wod: SimpleWod,
        feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache,
        socialDataEternalPromise?: Promise<{ obj: SquadClassBookingLite[] }>,
        prefLocationType?: any): Promise<ProductDetailPage> {
        const productDetailPage = new ProductDetailPage()
        const confirmedBooking = bookingResponse.booking
        const waitlistBooking = bookingResponse.waitlist
        let bookingCredit: number | undefined = undefined
        if (!_.isNil(confirmedBooking)) {
            bookingCredit = confirmedBooking?.creditCost
        } else if (!_.isNil(waitlistBooking)) {
            bookingCredit = waitlistBooking?.creditCost
        }
        const booking = confirmedBooking || waitlistBooking
        const tz = userContext.userProfile.timezone
        const startTime = TimeUtil.getMomentForDateString(booking.Class.date + " " + booking.Class.startTime, tz, "YYYY-MM-DD hh:mm:ss")
        const isClassStarted = TimeUtil.getMomentNow(tz).isAfter(startTime)
        const userSegmentsPromise = this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
        const cafeFoodDetails: FoodFulfilment = undefined
        let cultManageOptions
        if (confirmedBooking) {
            cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, confirmedBooking, undefined, true, false, cafeFoodDetails)
        } else {
            cultManageOptions = await this.productBusiness.getCultWaitlistManageOptions(userContext, productType, waitlistBooking, true)
        }

        const isUserEligibleForMiniGoal = await CultUtil.isUserEligibleForMiniGoal(userContext, this.serviceInterfaces, booking.CultClass.workoutID)
        const isUserEligibleForMiniGoalBanner = await CultUtil.isUserEligibleForMiniGoalBanner(userContext, this.serviceInterfaces, booking.CultClass.workoutID)
        const isUserEligibleForGXLogging = await CultUtil.isUserEligibleForGXLogging(userContext, this.serviceInterfaces)

        let miniGoalDetails: {
            miniGoal?: {
                goalTitle: string
                minClass: number
                numClassesAttended: number
                goalDescription?: string
              },
              focusArea?: {
                title?: string
                focusDescription?: string
                imageUrl?: string
            }
          }
        let loggedMovements: any

        if (isUserEligibleForMiniGoal || isUserEligibleForMiniGoalBanner) {
            miniGoalDetails = await this.cultClassUtil.getMiniGoalDetails(userContext.userProfile.userId, this.serviceInterfaces, wod)
        }

        const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled || option.showDisabled
        })
        productDetailPage.title = booking.Class.Workout.name

        const formatSummarywidget = await this.getFormatSummaryWidget(userContext, bookingCredit, booking, !_.isNil(confirmedBooking))
        if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.focusArea)) {
            formatSummarywidget.mediaDetails = []
            formatSummarywidget.layoutProps = {spacing: { top: 10, bottom: 0 } }
          }
        productDetailPage.widgets.push(formatSummarywidget)
        if (booking.CultClass.workoutID === STRENGTH_PULSE) {
            const smartWatchBanner = await this.widgetBuilder.buildWidgets(["3a88d250-9183-452b-9e4b-c75e00fce2bb"], this.serviceInterfaces, userContext, undefined, undefined)
            productDetailPage.widgets.push(smartWatchBanner.widgets[0])
        }
        productDetailPage.isPilateGymPage = _.some(PILATES_WORKOUT_IDS, (categoryId: number) => {
            return categoryId === booking.CultClass.workoutID
        })
        this.logger.info("isPilateGymPage for workout category: " + booking.CultClass.workoutID + " = " + productDetailPage.isPilateGymPage)

        if (!_.isNil(waitlistBooking)) {
            const isAppNewWailistColorSupported = await AppUtil.isAppNewWaitlistColorCodingSupported(this.serviceInterfaces.segmentService, userContext)
            productDetailPage.widgets.push(this.getWaitlistWidget(booking, tz, false, isAppNewWailistColorSupported))
        }

        // mini goal banner widget
        try {
            if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.focusArea)) {
                const miniGoalBannerWidget = this.cultClassUtil.getMiniGoalBannerWidgetPromise(miniGoalDetails, userContext)
                if (miniGoalBannerWidget) {
                    miniGoalBannerWidget.layoutProps = {spacing: { top: 20, bottom: 0 } }
                    productDetailPage.widgets.push(miniGoalBannerWidget)
                }
            }

            // get prelogged weights
            if (isUserEligibleForGXLogging && !_.isEmpty(wod) && !_.isEmpty(wod.parts)) {
            loggedMovements = await this.cultClassUtil.getPreLoggedMovementInfo(userContext, this.serviceInterfaces, wod, booking.CultClass)
            }
        } catch (error) {
            this.serviceInterfaces.logger.error(`Error while creating miniGoal banner for user in post booking: ${userContext.userProfile.userId}`, error)
        }

        const squadWidget = await this.getSquadWidget(socialDataEternalPromise, booking, isClassStarted)
        if (!_.isNil(squadWidget)) {
            squadWidget.hasDividerBelow = false
            squadWidget.addSpaceBelow = true
            productDetailPage.widgets.push(squadWidget)
        }
        const membershipDetails: Membership[] = await this.membershipService.getMembershipsForUser(userContext.userProfile.userId, "curefit", ["CULT"])
        productDetailPage.widgets.push(await this.getBookingActionListWidget(userContext, booking, enabledOptions, cultManageOptions, productType, isClassStarted, !_.isNil(confirmedBooking), tz, membershipDetails))
        if (!_.isNil(wod) && !booking.Class.hasDefaultWod && !CultUtil.isCultRunWorkout(booking.CultClass.workoutID)) {
            const wodWidget = this.cultClassUtil.getWodInfoWidgetV2(userContext, wod, false, loggedMovements)
            if (!_.isNil(wodWidget)) {
                productDetailPage.widgets.push(wodWidget)
            }
        } else {
            productDetailPage.widgets.push(this.getDescriptionWidget(booking.Class.Workout.info))
        }

        // mini goal widget
        try {
            if (!_.isEmpty(miniGoalDetails) && !_.isEmpty(miniGoalDetails.miniGoal)) {
                const miniGoalWidget = this.cultClassUtil.getMiniGoalWidgetPromise(miniGoalDetails, booking.CultClass.date, userContext)
                if (miniGoalWidget) {
                    miniGoalWidget.layoutProps = {spacing: { top: 30, bottom: 0 }}
                    productDetailPage.widgets.push(miniGoalWidget)
                }
            }

            // get prelogged weights
            if (isUserEligibleForGXLogging && !_.isEmpty(wod) && !_.isEmpty(wod.parts)) {
            loggedMovements = await this.cultClassUtil.getPreLoggedMovementInfo(userContext, this.serviceInterfaces, wod, booking.CultClass)
            }
        } catch (error) {
            this.serviceInterfaces.logger.error(`Error while creating miniGoal in post booking for user: ${userContext.userProfile.userId}`, error)
        }

        const preWorkoutWidget = this.getPreWorkoutGearWidget(booking.CultClass, false)
        if (preWorkoutWidget) {
         productDetailPage.widgets.push(preWorkoutWidget)
        }
        productDetailPage.widgets.push(this.getFacilitiesWidget(booking.CultClass.Center, false))
        if (!_.isNil(bookingCredit)) productDetailPage.widgets.push(this.dynamicDividerWidget())
        const notesWidget = CultUtil.getWorkoutNotesWidgetV2(booking.Class, this.getSpacing("60", "0"))
        if (!_.isNil(notesWidget)) {
        notesWidget.hasDividerBelow = false
        productDetailPage.widgets.push(notesWidget)
        }
        const cultClass: CultClass = booking.CultClass
        if (CultUtil.isCultRunWorkout(cultClass.workoutID)) {
            const address = `${cultClass.Center.Address.addressLine1} ${cultClass.Center.Address.addressLine2}`
            productDetailPage.widgets.push(await this.getCenterMapWidget(userContext, cultClass.Center, cultClass.Center.Address.latitude, cultClass.Center.Address.longitude, address, this.userAttributeClient, this.logger, prefLocationType, "Meetup location:", cultClass.Center?.placeUrl))
                if (booking.CultClass?.Center?.metadata?.parkingLocation) {
                    productDetailPage.widgets.push(await this.getCenterMapWidget(userContext, cultClass.Center, cultClass.Center.Address.latitude, cultClass.Center.Address.longitude, cultClass.Center?.metadata?.parkingLocation?.address, this.userAttributeClient, this.logger, prefLocationType, "Parking location:", cultClass.Center?.metadata?.parkingLocation?.navigation))
                }
            } else {
            productDetailPage.widgets.push(await this.getCenterMapWidgetPromise(userContext, cultClass.Center, this.userAttributeClient, this.logger, prefLocationType))
          }

          const widgetId = CLASS_POST_CLASS_PAGE_BANNER_WIDGET_ID_PROD
          const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
        if (!_.isNil(bookingCredit)) {
            productDetailPage.widgets.push(this.dynamicDividerWidget())
            productDetailPage.widgets.push(this.getCreditInfoWidget(bookingCredit))
        }
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
              try {
                  const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined

                  if (widget?.data && widget?.data?.length > 0) {
                      let data: any[]
                      data = widget.data.filter((banner: any) => {
                          const split = banner?.bannerIdentifier?.split("_")
                          const formatID = split.length > 1 ? split[1] : null
                          return formatID === cultClass.workoutID.toString()
                      })

                      if (data.length === 0) {
                          data = widget?.data.filter((banner: any) => {
                              const split = banner?.bannerIdentifier?.split("_")
                              const formatID = split.length > 1 ? split[1] : null
                              return formatID === "generic"
                          })
                      }

                      widget.data = data
                  }
                  if (!_.isEmpty(widget)) {
                      productDetailPage.widgets.push(widget)
                  }
              } catch (error) {
                  this.logger.info("BookingDetailViewBuilderV2 add spotify banner error", error)
              }
          }

          if (!_.isNil(confirmedBooking) || (booking?.state  && booking.state === "CONFIRMED")) {
            const userSegments = await userSegmentsPromise
            if (_.isEmpty(userSegments) || !userSegments.includes(QR_CODE_DISABLED_SEGMENT)) {
                productDetailPage.actions = this.getPageActions(booking, userContext)
            }
        }
        return productDetailPage
    }
    private dynamicDividerWidget(): any {
        return {
            "widgetType": "DYNAMIC_DIVIDER_WIDGET",
            "paddingLTBR": [0.0, 20.0, 0.0, 0.0]
        }
    }
    private getCreditInfoWidget(bookingCredit: number): WidgetView {
        return {
            "widgetType": "PRODUCT_LIST_WIDGET",
            "header": {
                "title": bookingCredit + " Credit" + ((bookingCredit === 1) ? "" : "s") + " Used",
                "style": {
                    "fontSize": 18
                },
                "color": "#000000"
            },
            "items": [
                {
                    "subTitle": "• Credits will be refunded if waitlist is not confirmed.",
                    "topSpacing": 10
                },
                {
                    "subTitle": "• Credits will be deducted for a no show.",
                    "topSpacing": 10
                },
                {
                    "subTitle": "• Credits will be refunded if you cancel the class.",
                    "topSpacing": 10
                }
            ],
            "hideSepratorLines": true,
            "hasDividerBelow": false,
            "showSquareIcons": true
        }
    }

    private async getSquadWidget(socialDataEternalPromise: Promise<{ obj: SquadClassBookingLite[] }>, booking: CultBooking, isClassStarted: boolean): Promise<CultBuddiesJoiningListSmallWidget> {
        if (socialDataEternalPromise) {
            if (!isClassStarted && socialDataEternalPromise) {
                let socialDataForBookingIds = (await socialDataEternalPromise).obj
                socialDataForBookingIds = socialDataForBookingIds ? _.filter(socialDataForBookingIds, (socialData) => socialData.classId === booking.CultClass.id) : null
                if (socialDataForBookingIds && !_.isEmpty(socialDataForBookingIds)) {
                    const userIds = socialDataForBookingIds.map(squadData => squadData.userId)
                    return {
                        widgetType: "CULT_BUDDIES_JOINING_LIST_SMALL_WIDGET",
                        ...(await CultUtil.getBuddiesJoiningListSmallView(userIds, this.userCache, PageTypes.PreBookClass) as CultBuddiesJoiningListSmallView),
                    }
                }
            }
        }
        return null
    }

    private async getCenterMapWidget(userContext: UserContext, cultCenter: CultCenter, latitude: number, longitude: number, address: string, userAttributeClient: IUserAttributeClient, logger: Logger, prefLocationType: any,  headerTitle?: string, mapUrl?: string): Promise<MapWidgetV2> {
        const action: Action = {url: mapUrl, title: "NAVIGATE", actionType: "EXTERNAL_DEEP_LINK"}
        let title: string = ""
    try {
          const userId = userContext.userProfile.userId
          const cityId = userContext.userProfile.cityId
          const locationPreference: LocationPreferenceResponseEntity = {
            prefLocationType: undefined
          }
          if (!userContext.sessionInfo.isUserLoggedIn) {
            UserUtil.getLocationPreferenceResponseEntityFromSessionData(userContext.sessionInfo.sessionData, cityId, locationPreference)
          } else {
            await UserUtil.getLocationPreferenceForCoordinates(userId, cityId, userAttributeClient, locationPreference, prefLocationType)
          }
          let coordinates: LatLong
          if (locationPreference != null) {
            if (locationPreference.prefLocationType === LocationDataKey.CURRENT_LOC) {
              coordinates = {lat: userContext.sessionInfo.lat, long: userContext.sessionInfo.lon}
            } else if (locationPreference.prefLocationType === LocationDataKey.COORDINATES && !_.isNil(locationPreference.coordinates)) {
              coordinates = locationPreference.coordinates
            }
          }
          if (!_.isNil(coordinates) && UserUtil.isValidCoordinates(coordinates.lat, coordinates.long)) {
            const distance = LocationUtil.getDistanceFromLatLonInKm(coordinates.lat, coordinates.long, latitude, longitude)
            title = `${distance.toFixed(1)} KM • `
          }
        } catch (error) {
          logger.info("In CultClassDetailViewV2::getCenterMapWidgetPromise, user location preference coordinate not set")
        }
        title += cultCenter.name
        return new MapWidgetV2(address, action, !_.isNil(headerTitle) ? headerTitle : title, this.getSpacing("25", "0"))
      }

      private async getCenterMapWidgetPromise(userContext: UserContext, cultCenter: CultCenter, userAttributeClient: IUserAttributeClient, logger: Logger, prefLocationType: any): Promise<MapWidgetV2> {
        const address = `${cultCenter.Address.addressLine1} ${cultCenter.Address.addressLine2}`
        return this.getCenterMapWidget(userContext, cultCenter, cultCenter.Address.latitude, cultCenter.Address.longitude, address, userAttributeClient, logger, prefLocationType, null, cultCenter.placeUrl)
      }

    private getFacilitiesWidget(cultCenter: CultCenter, isFirstTimeUser: boolean): FacilitiesWidget {
        const facilities = CultUtil.getCenterFacilitiesList(cultCenter)
        if (_.isEmpty(facilities)) { return null }
        const title = "Center Facilities"
        const subTitle = this.getFacilitiesSubtile(facilities)
        const collapsibleProperties: CollapsibleProperties = {
          isCollapsible: true,
          isCollapsed: !isFirstTimeUser
        }
        return new FacilitiesWidget(title, subTitle, facilities, collapsibleProperties, this.getSpacing("20", "0"))
    }

    private getFacilitiesSubtile(facilities: Facility[]) {
        let subTitle = facilities[0].name + " • "
        if (facilities!.length >= 2) {
          subTitle += facilities[1].name
        }
        if (facilities.length >= 3) {
          subTitle += " • +" + (facilities.length - 2).toString()
        }
        return subTitle
    }

    private getPreWorkoutGearWidget(cultClass: CultClass, isFirstTimeUser: boolean): ProductGridWidgetV2 {
        const workoutGearList = !_.isEmpty(cultClass.Workout.preWorkoutGears) ? CultUtil.getWorkoutGearAuroraInfoCardList(cultClass.Workout.preWorkoutGears, cultClass.Center.id) : []
        if (_.isEmpty(workoutGearList)) {
            return null
        }
        const collapsibleProperties: CollapsibleProperties = {
          isCollapsible: true,
          isCollapsed: !isFirstTimeUser
        }
        let footer: string = null
        let header: Header
        const centerId = Number(cultClass.centerID)
        const subTitle =  !_.isNil(workoutGearList) && !_.isEmpty(workoutGearList) ? this.getWorkoutGearSubtitle(workoutGearList) : ""
        header = {
          title: "What to bring",
          subTitle,
        }
        if (cultClass.workoutID === BOXING_BAG_WORKOUT_ID  && !BOXING_GLOVES_PROVIDED_CENTER_IDS.includes(centerId)) {
          footer = "Boxing gloves are mandatory. Members without them will not be allowed to participate."
        } else if (cultClass.workoutID === YOGA_WORKOUT_ID || cultClass.workoutID === HATHA_YOGA_WORKOUT_ID) {
          header = {
            title: "What to bring",
            subTitle
          }
          footer = "Yoga mat is mandatory. Members without them will not be allowed to participate."
        }
        const instructions = this.getInstructionMediaList(cultClass)
        const widget = new ProductGridWidgetV2(header, workoutGearList, footer, instructions, collapsibleProperties, this.getSpacing("20", "0"))
        if (!_.isNil(widget) && !_.isNil(widget.hasDividerBelow)) {
          widget.hasDividerBelow = false
        }
        return widget
      }

      private getWorkoutGearSubtitle(workoutGearList: InfoCard[]) {
        let subTitle = workoutGearList[0].title + " • "
        if (workoutGearList!.length >= 2) {
          subTitle += workoutGearList[1].title
        }
        if (workoutGearList.length >= 3) {
          subTitle += " • +" + (workoutGearList.length - 2).toString()
        }
        return subTitle
      }

      private getInstructionMediaList(cultClass: CultClass): InstructionsWithMediaV2[] {
        const instructionsWithMedia: InstructionsWithMediaV2[] = _.map(cultClass.instructions, (instruction) => this.getInstructionMedia(instruction))
        return instructionsWithMedia.filter(Boolean)
      }

      private getInstructionMedia(instruction: any): InstructionsWithMediaV2 {

        if (_.isNil(instruction) || _.isNil(instruction.text) || _.isEmpty(instruction.media)) {
          return null
        }
        const media = instruction.media[0]
        if (_.isNil(media.url) || _.isNil(media.type) || media.type !== "VIDEO") {
          return null
        }

        const videoUrl = CultUtil.getCultVideoCdnUrl(media.url)
        const action: Action = {actionType: "PLAY_VIDEO_FLUTTER" as any, url: videoUrl}
        const fullScreenIcon = "/image/gymfit/fullscreen.png"

        return {
          text: instruction.text,
          media: {
            url: media.url,
            type: media.type,
            videoUrl: videoUrl,
            action: action,
            fullScreenIcon: fullScreenIcon
          }
        }
      }

    private async getWodInfoWidgetV2(userContext: UserContext, wod: SimpleWod, isFirstTimeUser: boolean, hamletBusiness: HamletBusiness, loggedMovements?: any): Promise<WODInfoWidgetV2> {
        if (_.isEmpty(wod.parts)) {
          return null
        }
        const title = "Workout of the day"
        const focus = wod.focus
        const collapsibleProperties: CollapsibleProperties = {
          isCollapsible: true,
          isCollapsed: !isFirstTimeUser || (!_.isEmpty(loggedMovements))
        }
        const workoutInfo = await this.getWorkoutInfoForWOD(userContext, wod, hamletBusiness, loggedMovements)
        if (_.isNil(workoutInfo) || _.isEmpty(workoutInfo)) {
          return null
        }

        // const warmUp = wod.parts.find(part => part.partType === "MOBILITY")
        // const coolDown = wod.parts.find(part => part.partType === "COOL DOWN")
        // const preWorkoutInstructions = this.getPreWorkoutInstructions(warmUp)
        // const postWorkoutInstructions = this.getPostWorkoutInstructions(coolDown)

        return new WODInfoWidgetV2(title, focus, workoutInfo, null, null, collapsibleProperties, null)
      }

       private getPreWorkoutInstructions(warmUp: SimplePart): WorkoutMovementInfo {
        let preWorkoutInstructions: WorkoutMovementInfo
        if (!_.isEmpty(warmUp) && !_.isEmpty(warmUp.movements)) {
        const warmUpMovementsArray = _.map(warmUp.movements, movement => movement.title)
        preWorkoutInstructions = {
            title: "WARM UP",
            subtitle: warmUpMovementsArray.join(", ")
        }
        }
        return preWorkoutInstructions
    }

    private async getWorkoutInfoForWOD(userContext: UserContext, wod: SimpleWod, hamletBusiness: HamletBusiness, loggedMovements?: any): Promise<WorkoutMovementInfo[]> {
        const workoutInfo: WorkoutMovementInfo[] = []

        const mainWorkoutList = wod.parts.filter(part => part.partType === "MAIN" || part.partType === "MAIN WORKOUT")
        const finisher = wod.parts.find(part => part.partType === "FINISHER")

        const mainWorkoutInstructionsList = mainWorkoutList.map(async (part, index) => await this.getWorkoutInstructions(userContext, part, `MAIN WORKOUT ${index + 1}`, loggedMovements))
        const finisherInstructions = await this.getWorkoutInstructions(userContext, finisher, "FINISHER")

        if (!_.isEmpty(mainWorkoutInstructionsList)) {
          for (let index = 0; index < mainWorkoutInstructionsList.length; index++) {
            workoutInfo.push(await mainWorkoutInstructionsList[index])
          }
        }
        if (!_.isNil(finisherInstructions)) { workoutInfo.push(finisherInstructions) }

        return _.filter(workoutInfo, movementInfo => !_.isEmpty(movementInfo))
      }

  private getPostWorkoutInstructions(coolDown: SimplePart): WorkoutMovementInfo {
    let postWorkoutInstructions: WorkoutMovementInfo
    if (!_.isEmpty(coolDown) && !_.isEmpty(coolDown.movements)) {
      const coolDownMovementsArray = _.map(coolDown.movements, movement => movement.title)
      postWorkoutInstructions = {
        title: "COOL DOWN",
        subtitle: coolDownMovementsArray.join(", ")
      }
    }
    return postWorkoutInstructions
  }

  private getWorkoutInstructions(userContext: UserContext, part: SimplePart, title: string, loggedMovements?: any): WorkoutMovementInfo {
    if (_.isNil(part) || _.isNil(part.movements) || _.isEmpty(part.movements)) {
      return null
    }
    let workoutComponents: WorkoutComponent[] = part.movements.map(movement => this.getWorkoutComponentsFromMovements(userContext, movement, loggedMovements))

    if (_.find(workoutComponents, (component: WorkoutComponent)  => _.isNil(component.image))) {
        workoutComponents = workoutComponents.map((component: WorkoutComponent) => {
            return {...component, image: null}
        })
    }

    return {
      title: title,
      note: part.notes,
      goal: part.goal ?? part.subPartType ?? title,
      subPartType: part.goal != null ? part.subPartType : null,
      strategy: part.strategy,
      showGoalView: true,
      workoutComponents: workoutComponents
    }
  }

  private getWorkoutComponentsFromMovements(userContext: UserContext, movement: DisplayMovement, loggedMovements?: any): WorkoutComponent {
    const video = _.find(movement.media, { type: "VIDEO" })
    let action: Action
    if (!_.isNil(video) && !_.isNil(video.url)) {
      action = {
        actionType: "NAVIGATION",
        url: `curefit://fl_videoplayer?videoUrl=${encodeURIComponent(video.url)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(video.url))}`,
        analyticsData: { widgetName: "SGT_WOD_VIDEO_PLAYED" }
      }
    }
    const image = _.find(movement.media, { type: "THUMBNAIL_IMAGE"})
    let imageUrl
    if (!_.isNil(image)) {
      imageUrl = image.url
      if (!_.isNil(imageUrl) && imageUrl.startsWith("curefit-content/")) {
        imageUrl = imageUrl.split("curefit-content/")[1]
      }
      imageUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/w_350,f_auto,q_auto:eco/" + imageUrl
    }

    return {
      title: movement.title,
      image: imageUrl,
      action: action
    }
  }

    private getWaitlistWidget(booking: CultBooking, tz: Timezone, isFirstTimeUser: boolean, isAppNewWailistColorSupported: boolean): WaitlistWidget {
        const cultClass = booking.Class
        const waitlistInfo = this.getWaitlistInfo(cultClass, booking, isAppNewWailistColorSupported)
        const timePicker = this.getWaitlistTimePicker(cultClass, tz, isFirstTimeUser)
        const bookingNumber = booking.bookingNumber ? booking.bookingNumber : booking.wlBookingNumber
        return new WaitlistWidget(`WAITLIST`, booking.waitlistNumber, waitlistInfo, timePicker, bookingNumber, null, true)
      }

      private getwaitlistCnfProbability(cultClass: CultClass, booking: CultBooking): WaitlistProbability {
        let wlStatus
        if (booking.waitlistNumber < cultClass?.wlConfirmationThreshold) {
            wlStatus = WaitlistProbability.MEDIUM
        } else {
            wlStatus = WaitlistProbability.LOW
        }
        return wlStatus
    }
      private getwaitlistCnfProbabilityColor(cultClass: CultClass, booking: CultBooking, isAppNewWailistColorSupported: boolean): string {
        let wlStatus
        if (booking.waitlistNumber < cultClass?.wlConfirmationThreshold) {
            if (!isAppNewWailistColorSupported) wlStatus = "#FFB876"
            else wlStatus = "#FFFFFF"
        } else {
            if (!isAppNewWailistColorSupported) wlStatus = "#FF6B74"
            else wlStatus = "#FFB876"
        }
        return wlStatus
    }

      private getWaitlistInfo(cultClass: CultClass, booking: CultBooking, isAppNewWailistColorSupported: boolean): WaitlistInfo {
        const waitlistCount = booking.waitlistNumber
        const title = !_.isNil(cultClass.wlConfirmationThreshold) ? `WL upto #${cultClass.wlConfirmationThreshold} usually gets confirmed` : ""
        const infoAction: Action = {actionType: "NAVIGATION", url: "curefit://listpage?pageId=waitlisthiw"}
        const waitlistPrediction = "You will be informed in case of cancellation"
        const waitlistCnfProbability = this.getwaitlistCnfProbability(cultClass, booking)
        const waitlistColor = this.getwaitlistCnfProbabilityColor(cultClass, booking, isAppNewWailistColorSupported)

        if (cultClass.wlConfirmationThreshold) {
          return {
            waitlistCount: waitlistCount,
            title: title,
            infoAction: infoAction,
            waitlistPrediction: waitlistPrediction,
            waitlistCnfProbability,
            waitlistColor
          }
        } else {
          return {
            waitlistCount: waitlistCount,
            title: title,
            infoAction: infoAction,
          }
        }
      }

      private getWaitlistTimePicker(cultClass: CultClass, tz: Timezone, isFirstTimeUser: boolean): WaitlistConfirmationTimePicker {

        const classStartTime = TimeUtil.getMomentForDateString(cultClass.date + " " + cultClass.startTime, cultClass.timezone as Timezone, TimeUtil.HH_MM_SS_DATE_FORMAT)
        const startTimeInEpoch = TimeUtil.getEpochFromDate(classStartTime.toDate())
        const { notificationTimeSlots } = getTimeSlotsForWaitlistExtensionWidget(cultClass, tz, cultClass.date + "T" + cultClass.startTime + cultClass.timezone)
        const isWaitlistTimePreSelected = !_.isNil(cultClass.wlNotificationTime)
        // Adding 330 min as time per timeZone is 5hr30min forward
        const wlConfirmTime = isWaitlistTimePreSelected ? TimeUtil.subtractFromDate(classStartTime.toDate(), tz, cultClass?.wlNotificationTime + 330, "minute") : null

        const timeSlot: WorkoutWaitlistTimingSlot[] = _.map(notificationTimeSlots, (time) => {
          return {
            title: `${time} mins before the class`,
            time: time,
            isSelected: cultClass?.wlNotificationTime === time,
            isRecommended: CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME === time
          }
        })

        return {
          title: isWaitlistTimePreSelected ? "Edit waitlist confirmation time" : "Pick waitlist confirmation time",
          cultClassId: cultClass.id,
          description: "The closer you are to the class time, higher the chances of confirmation",
          collapsedDescription: `Waitlist will be confirmed by ${wlConfirmTime?.format("hh:mm A")}`,
          classTimeInEpochs: startTimeInEpoch,
          timeSlots: timeSlot,
          collapsibleProperties: {
            isCollapsible: true,
            isCollapsed: isWaitlistTimePreSelected
          }
        }
      }

    private async getFormatSummaryWidget(userContext: UserContext, bookingCredit: number | undefined, booking: CultBooking, confirmedBooking: boolean): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        const day = TimeUtil.formatDateStringInTimeZone(booking.Class.date, tz, "ddd D MMM")
        const durationStart = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("hh:mm")
        const durationEnd = TimeUtil.getMomentForDateString(booking.Class.endTime, tz, "HH:mm:ss").format("hh:mm A")
        const centerName = booking.Center.name
        const subTitle = day + ", " + durationStart + "-" + durationEnd
        const document = _.find(booking.Class.Workout.documents, document => {
            return document.tagID === 11
        })
        const cultUnderlyingClass = booking.CultClass
        const mediaDetails: WorkoutMediaData[] = [{media: {type: MediaType.IMAGE, mediaUrl: document?.URL}}]
        const navigateAction: Action = {url: booking.Center.placeUrl, title: "NAVIGATE", actionType: "EXTERNAL_DEEP_LINK"}
        const summaryWidget: WidgetView & {
            title: string,
            bookingNumber: string,
            timingDetails: string,
            centerName: string,
            mediaDetails: WorkoutMediaData[],
            isV2Widget: boolean,
            intensityLevel?: string,
            navigateAction?: Action,
            creditPillData?: CreditPillData
        } = {
            widgetType: "FORMAT_SUMMARY_WIDGET",
            bookingNumber: booking.bookingNumber,
            title: CultUtil.pulsifyClassName(cultUnderlyingClass, userContext),
            timingDetails: subTitle,
            centerName: centerName,
            mediaDetails: mediaDetails,
            isV2Widget: true,
            intensityLevel: confirmedBooking ? "CONFIRMED" : null,
            navigateAction,
            creditPillData: (_.isNil(bookingCredit)) ? null : {
                credit: bookingCredit.toString()
            }
        }

        return summaryWidget
    }

    private async getBookingActionListWidget(userContext: UserContext, booking: CultBooking, enabledOptions: ManageOptionPayload[], cultManageOptions: any, productType: ProductType, isClassStarted: boolean, isConfirmedBooking: boolean, tz: Timezone, membershipList: Membership[]): Promise<WidgetView> {
        const isNonMember = _.isNil(membershipList) || membershipList.length == 0
        const bookingActions: BookingAction[] = []
        if (isNonMember) {
            const prepareForYourSessionAction: BookingAction = {
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://prepare_for_class"
                },
                title: "Prepare for your session",
                trailingIcon: "chevron-right",
                leadingIcon: "gear-1",
                titleType: "NORMAL",
                leadingIconImg: {
                    "url": "/image/membership/cultstore.svg",
                    "width": 20.0,
                    "height": 30.0,
                    "boxFit": BoxFit.CONTAIN,
                    "action": undefined,
                    "scaleWithContext": true
                }
            }
            const whatToExpectAction: BookingAction = {
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://expectation_at_center"
                },
                title: "What to expect",
                trailingIcon: "chevron-right",
                leadingIcon: "home",
                titleType: "NORMAL",
                leadingIconImg: {
                    "url": "/image/membership/live.svg",
                    "width": 20.0,
                    "height": 30.0,
                    "boxFit": BoxFit.CONTAIN,
                    "action": undefined,
                    "scaleWithContext": true
                }
            }
            bookingActions.push(prepareForYourSessionAction)
            bookingActions.push(whatToExpectAction)
        }
        // Cancel Action
        const cancelBookingAction = await this.getCancellationAction(userContext, booking, enabledOptions)
        if (cancelBookingAction) {
           bookingActions.push(cancelBookingAction)
        }
        // Invite Buddies
        if (!isClassStarted) {
            const shareActionWidget = await this.getShareActionWidget(userContext, booking, productType, false)
            if (!_.isNil(shareActionWidget)) {
                const inviteBuddiesAction: BookingAction = {
                    action: shareActionWidget.action,
                    title: "Invite buddies for workout",
                    trailingIcon: "chevron-right",
                    leadingIcon: "share-2",
                    titleType: "NORMAL",
                }
                bookingActions.push(inviteBuddiesAction)
            }
        }
        if (!isNonMember) {
            const orderWorkoutGearAction = await this.getPreorderWorkoutGear(userContext, booking, isConfirmedBooking)
            if (orderWorkoutGearAction) {
                bookingActions.push(orderWorkoutGearAction)
            }
        }
        const bookingNumber = booking.bookingNumber ? booking.bookingNumber : booking.wlBookingNumber
        let IVRReminderCardWidget
        if (booking.ivrConfig == null ) {
            booking.ivrConfig = {isIVREditable: false, ivrState: "", status: false, isIVRApplicable: false}
        }
        if (AppUtil.isCallReminderSupported(userContext) && booking.ivrConfig && booking.ivrConfig.isIVRApplicable) {
            const isAppIVRReminderSupported: boolean = AppUtil.isAppIVRReminderSupported(userContext)
            const timeZone = userContext.userProfile.timezone
            // if the user didn't enable ivr and appVersion >= 10.57 for the booking show new IVR_REMINDER_CARD_WIDGET else show older widget
            if (isAppIVRReminderSupported && !booking.ivrConfig.status && CultUtil.canShowNewIVRReminderWidget(booking.CultClass.date, booking.CultClass.startTime, timeZone)) {
                IVRReminderCardWidget = CultUtil.getIVRReminderCardWidget(userContext, booking.ivrConfig, bookingNumber)
            } else {
                const callReminderWidget = this.getCallReminderWidget(booking.ivrConfig, booking.CultClass.startTime, bookingNumber, (booking.wlBookingNumber !== null && booking.wlBookingNumber !== undefined), tz)
                const title = callReminderWidget.isReminderSet ? `Reminder set for ${callReminderWidget.selectedCallTime}` : "Set Call Reminder time"
                const callReminderAction: BookingAction = {
                    title: title,
                    trailingIcon: "chevron-right",
                    leadingIcon: "call",
                    titleType: "NORMAL",
                    action: {
                        actionType: "SET_CALL_REMINDER" as any,
                        meta: {...callReminderWidget},
                    }
                }
                bookingActions.push(callReminderAction)
            }
        }
        if (isConfirmedBooking) {
            const dropoutWidget = this.getClassDropoutWidget(userContext, booking, enabledOptions, cultManageOptions.meta, undefined)
            if (!_.isNil(dropoutWidget)) {
                const dropoutAction: BookingAction = {
                    action: dropoutWidget.action,
                    title: dropoutWidget.displayText,
                    trailingIcon: dropoutWidget.isDisabled ? "info" : "chevron-right",
                    leadingIcon: "close-line",
                    titleType: "NORMAL",
                    trailingIconType: dropoutWidget.isDisabled ? "ALERT" : null,
                }
                bookingActions.push(dropoutAction)
            }
        }
        const bookingActionListWidget: WidgetView & {
            bookingActions: BookingAction[]
        } = {
            widgetType: "BOOKING_ACTION_LIST_WIDGET" as any,
            bookingActions: bookingActions,
            topDynamicWidget: IVRReminderCardWidget ? [IVRReminderCardWidget] : null
        }
        return bookingActionListWidget
    }

    private async getPreorderWorkoutGear(userContext: UserContext, booking: CultBooking, isConfirmedBooking: boolean): Promise<BookingAction> {
        const tz = userContext.userProfile.timezone
        const classId: string = booking.Center.id.toString()
        const kioskId: string = classId ? this.kiosksDemandService.getKioskIdGivenCenterId(classId) : undefined
        const kiosk: Kiosk = kioskId ? this.kiosksDemandService.getKiosk(kioskId) : undefined
        const cultClass = booking.Class
        const classStartTime: string = cultClass.startTime
        const classEndTime: string = cultClass.endTime
        const start: string[] = classStartTime.split(":")
        const end: string[] = classEndTime.split(":")
        const startEndTime: StartEndTime = {
            startingHours: { hour: parseInt(start[0]), min: parseInt(start[1]) },
            closingHours: { hour: parseInt(end[0]), min: parseInt(end[1]) }
        }
        const showAddSnackWidget: boolean = await this.kiosksDemandService.shouldShowAddSnackWidget(classId, booking.Class.date, startEndTime)
        const areaForCafe = kiosk ? await this.deliveryAreaService.findAreaForKiosk(kiosk.kioskId) : undefined
        const isDateValidForAreaId = !areaForCafe ? true : this.menuService.isDateValidForAreaId(booking.Class.date, areaForCafe.areaId)
        const isCafe = kiosk && kiosk.cafeConfig && kiosk.cafeConfig.enabled && showAddSnackWidget && isDateValidForAreaId
        if (isCafe && isConfirmedBooking) {
                const cafeEatClpUrl = getCafeEatClpUrl(userContext, this.kiosksDemandService, booking, tz)
                const cafeWidget = getNewCultCafeWidget(cafeEatClpUrl, false, false, booking)
                const cafeBookingAction: BookingAction = {
                    action: cafeWidget.action,
                    title: cafeWidget.displayText,
                    trailingIcon: "chevron-right",
                    leadingIcon: "gear",
                    titleType: "NORMAL",
                }
                return cafeBookingAction
        }
        return null
    }

    // TODO: TG: add the why cancel modal in this bookingAction here
    private async getCancellationAction(userContext: UserContext, booking: CultBooking, enabledOptions: ManageOptionPayload[]): Promise<BookingAction> {
        const cancelWidget = await this.getCancelClassWidget(userContext, booking, enabledOptions, undefined, undefined)
        if (cancelWidget) {
            const cancelBookingAction: BookingAction = {
                action: cancelWidget.action,
                title: booking.bookingNumber ? "Cancel Class" : "Leave Waitlist",
                attribute: cancelWidget.displayText,
                trailingIcon: "chevron-right",
                leadingIcon: "no-show",
                titleType: "NORMAL"
            }
            return cancelBookingAction
        }
        return null
    }


  private getSpacing(top: string, bottom: string): any {
    return {
      "spacing": {
        "top": top,
        "bottom": bottom
      }
    }
  }

    async getView(userContext: UserContext, productType: ProductType, bookingResponse: CultBookingResponse, feedback: Feedback, wod: SimpleWod, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache, socialDataEternalPromise?: Promise<{ obj: SquadClassBookingLite[] }>): Promise<ProductDetailPage> {
        const productDetailPage = new ProductDetailPage()
        const isWaitlistExtensionSupported = await AppUtil.isWaitlistExtensionSupported(userContext, this.hamletBusiness)
        const confirmedBooking = bookingResponse.booking
        const waitlistBooking = bookingResponse.waitlist
        const booking = confirmedBooking || waitlistBooking
        productDetailPage.widgets.push(await this.getSummaryWidget(userContext, productType, booking))
        const tz = userContext.userProfile.timezone
        const userSegmentsPromise = this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)

        const startTime = TimeUtil.getMomentForDateString(booking.Class.date + " " + booking.Class.startTime, tz, "YYYY-MM-DD hh:mm:ss")
        const isClassStarted = TimeUtil.getMomentNow(tz).isAfter(startTime)
        const classStatusWidget = this.getClassStatusWidget(bookingResponse)
        if (classStatusWidget) {
            productDetailPage.widgets.push(classStatusWidget)
        }

        const startTimeArray = booking.CultClass.startTime.split(":")
        const cafeFoodDetails: FoodFulfilment = undefined
        let cultManageOptions
        if (confirmedBooking) {
            cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, confirmedBooking, undefined, true, false, cafeFoodDetails)
        } else {
            cultManageOptions = await this.productBusiness.getCultWaitlistManageOptions(userContext, productType, waitlistBooking, isWaitlistExtensionSupported)
        }
        const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled || option.showDisabled
        })

        let cultWaitlistNotificationWidget
        if (isWaitlistExtensionSupported) {
            cultWaitlistNotificationWidget = await this.getWaitlistNotificationWidget(booking, enabledOptions, userContext)
        }

        const isUserEligibleForDropout = AppUtil.isClassDropoutSupported(userContext)
        const cancelClassWidget = await this.getCancelClassWidget(userContext, booking, enabledOptions, cultManageOptions.meta, cafeFoodDetails)
        if (confirmedBooking) {
            const pulseWidget = this.getPulseDeviceInfoWidget(bookingResponse, userContext)
            if (pulseWidget) {
                productDetailPage.widgets.push(pulseWidget)
            }
            if (!isClassStarted) {
                if (pulseWidget && AppUtil.isPulseNoteWidgetSupported(userContext.sessionInfo.osName, userContext.sessionInfo.appVersion)) {
                    // add a note to arrive early
                    productDetailPage.widgets.push(this.getPulseNoteWidget(bookingResponse, userContext) as ActionCardWidget)
                }
                if (isUserEligibleForDropout || confirmedBooking.isBookingCancellable) {
                    productDetailPage.widgets.push(cancelClassWidget)
                } else if (!confirmedBooking.isBookingCancellable) {
                    productDetailPage.widgets.push(new CalloutWidget("Cancel class 60 mins before start"))
                }
                const dropoutWidget = this.getClassDropoutWidget(userContext, confirmedBooking, enabledOptions, cultManageOptions.meta, cafeFoodDetails)
                productDetailPage.widgets.push(dropoutWidget)
            }
            else if (CULTSCORE_WORKOUT_IDS.indexOf(confirmedBooking.Class.Workout.id) > -1) {
                const logCultScoreWidget = this.getLogScoreWidget(enabledOptions, cultManageOptions.meta)
                if (logCultScoreWidget) {
                    productDetailPage.widgets.push(logCultScoreWidget)
                }
            }
            if (!_.isEmpty(confirmedBooking.workoutCell)) {
                productDetailPage.widgets.push(this.getWorkoutStationWidget(confirmedBooking.workoutCell))
            }
        } else if (!isClassStarted) {
            // for waitlist classes
            if (isWaitlistExtensionSupported && cultWaitlistNotificationWidget) {
                productDetailPage.widgets.push(cultWaitlistNotificationWidget)
            }
            if (cancelClassWidget) {
                productDetailPage.widgets.push(cancelClassWidget)
            }
        }

        if (!_.isEmpty(booking.info) && !_.isNil(booking.info.signOutOTP)) {
            productDetailPage.widgets = _.filter(productDetailPage.widgets, widget => !_.isEmpty(widget))
            const numberOfWidgets = productDetailPage.widgets.length
            const widget = (<WidgetView>productDetailPage.widgets[numberOfWidgets - 1])
            widget.hasDividerBelow = false
            productDetailPage.widgets.push(this.getJuniorOtpWidget(booking.info.signOutOTP))
        }
        const classId: string = booking.Center.id.toString()
        const kioskId: string = classId ? this.kiosksDemandService.getKioskIdGivenCenterId(classId) : undefined
        const kiosk: Kiosk = kioskId ? this.kiosksDemandService.getKiosk(kioskId) : undefined
        const cultClass = booking.Class
        const classStartTime: string = cultClass.startTime
        const classEndTime: string = cultClass.endTime
        const start: string[] = classStartTime.split(":")
        const end: string[] = classEndTime.split(":")
        const startEndTime: StartEndTime = {
            startingHours: { hour: parseInt(start[0]), min: parseInt(start[1]) },
            closingHours: { hour: parseInt(end[0]), min: parseInt(end[1]) }
        }
        const showAddSnackWidget: boolean = await this.kiosksDemandService.shouldShowAddSnackWidget(classId, booking.Class.date, startEndTime)
        const areaForCafe = kiosk ? await this.deliveryAreaService.findAreaForKiosk(kiosk.kioskId) : undefined
        const isDateValidForAreaId = !areaForCafe ? true : this.menuService.isDateValidForAreaId(booking.Class.date, areaForCafe.areaId)
        const isCafe = kiosk && kiosk.cafeConfig && kiosk.cafeConfig.enabled && showAddSnackWidget && isDateValidForAreaId
        let socialDataForBookingIds
        if (!isClassStarted && socialDataEternalPromise) {
            socialDataForBookingIds = (await socialDataEternalPromise).obj
            socialDataForBookingIds = socialDataForBookingIds ? _.filter(socialDataForBookingIds, (socialData) => socialData.classId === booking.CultClass.id) : null
        }
        const showingShareActionWidget = !isClassStarted && AppUtil.isShareActionWidgetSupported(userContext) // && _.get(socialDataForBookingId, "classLink")
        if (isCafe && !_.isNil(confirmedBooking)) {
            if (!_.isNil(cafeFoodDetails)) {
                const cafeFoodWidget = await this.getCafeFoodWidget(cafeFoodDetails, tz, userContext.sessionInfo.userAgent)
                productDetailPage.widgets.push(cafeFoodWidget)
            }
            else {
                const cafeEatClpUrl = getCafeEatClpUrl(userContext, this.kiosksDemandService, booking, tz)
                productDetailPage.widgets.push(getNewCultCafeWidget(cafeEatClpUrl, false, !showingShareActionWidget, confirmedBooking))
            }
        }
        if (showingShareActionWidget) {
            const shareActionWidget = await this.getShareActionWidget(userContext, booking, productType, false)
            productDetailPage.widgets.push(shareActionWidget)
        }
        if (_.isNil(cafeFoodDetails) && !_.isEmpty(booking.CultClass.Workout.preWorkoutGears)) {
            const workoutKitWidget = CultUtil.getWorkoutKitWidget(booking.CultClass.Workout.preWorkoutGears, null, booking.CultClass.date, booking.Center.id)
            if (!_.isNil(workoutKitWidget)) {
                productDetailPage.widgets.push(workoutKitWidget)
            }
        }

        if (AppUtil.isCallReminderSupported(userContext) && booking.ivrConfig && booking.ivrConfig.isIVRApplicable && booking.state !== "REJECTED" && booking.state !== "CANCELLED") {
            const bookingNumber = booking.bookingNumber ? booking.bookingNumber : booking.wlBookingNumber
            productDetailPage.widgets.push(this.getCallReminderWidget(booking.ivrConfig, booking.CultClass.startTime, bookingNumber, (booking.wlBookingNumber !== null && booking.wlBookingNumber !== undefined), tz))
        }
        productDetailPage.widgets.push(CultUtil.getNoteWidget(booking.Class))
        if (AppUtil.isInstructionsWithMediaSupported(userContext)) {
            productDetailPage.widgets.push(CultUtil.getInstructionsWithMediaWidget(cultClass))
        }
        const classDate = booking.Class.date
        const centerID = booking.Center.id
        productDetailPage.widgets.push({ ...CultUtil.getImportantUpdatesWidget(userContext, booking.Class.Workout.isSportCategory, undefined, undefined, undefined, booking.Center, classDate, CultUtil.isCultRunWorkout(booking.CultClass.workoutID)), hasDividerBelow: false })
        if (socialDataForBookingIds && !_.isEmpty(socialDataForBookingIds) && (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext))) {
            const userIds = socialDataForBookingIds.map(socialDataForBookingId => socialDataForBookingId.userId)
            productDetailPage.widgets.push({
                widgetType: "CULT_BUDDIES_JOINING_LIST_LARGE_WIDGET" as IWidgetType,
                ...(await CultUtil.getBuddiesJoiningListLargeView(userIds, this.userCache, PageTypes.CultDetail))
            })
        }
        if (feedback) {
            productDetailPage.widgets.push(new ProductFeedbackWidget(await feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId))
        }
        let wodWidget
        if (!booking.Class.hasDefaultWod) {
            wodWidget = CultUtil.getWodInfoWidget(wod, CultUtil.getWorkoutCaloriesInfo(booking.CultClass.Workout))
        }
        if (!_.isEmpty(wodWidget) && !CultUtil.isCultRunWorkout(booking.CultClass.workoutID)) {
            productDetailPage.widgets.push(wodWidget)
        } else {
            productDetailPage.widgets.push(this.getDescriptionWidget(booking.Class.Workout.info))
        }
        if (!_.isNil(cafeFoodDetails) && cafeFoodDetails.fulfilmentId && !_.isEmpty(booking.CultClass.Workout.preWorkoutGears)) {
            const workoutKitWidget = CultUtil.getWorkoutKitWidget(booking.CultClass.Workout.preWorkoutGears, null, booking.CultClass.date, booking.Center.id)
            if (!_.isNil(workoutKitWidget)) {
                productDetailPage.widgets.push(workoutKitWidget)
            }
        }
        if (booking.CultClass?.Center?.metadata?.parkingLocation) {
            productDetailPage.widgets.push(
                this.getCenterInfoWidgetForCultRunParkingLocation(userContext, booking.CultClass.Center, productType, "Parking location:")
            )
        }
        if (CultUtil.isCultRunWorkout(booking.CultClass.workoutID)) {
            productDetailPage.widgets.push(
                this.getCenterInfoWidgetForCultRunMeetupLocation(userContext, booking.CultClass.Center, productType, "Meet-up location:")
            )
        }
        if (!CultUtil.isCultRunWorkout(booking.CultClass.workoutID)) {
            productDetailPage.widgets.push(this.getCenterInfoWidget(userContext, booking.Center, productType))
        }
        if (booking.Membership)
            productDetailPage.widgets.push(await this.getPackInfoWidget(userContext, booking.Membership))
        productDetailPage.widgets = _.filter(productDetailPage.widgets, widget => !_.isEmpty(widget))
        if (!_.isNil(confirmedBooking) || (booking?.state  && booking.state === "CONFIRMED")) {
            const userSegments = await userSegmentsPromise
            if (_.isEmpty(userSegments) || !userSegments.includes(QR_CODE_DISABLED_SEGMENT)) {
                productDetailPage.actions = this.getPageActions(booking, userContext)
            }
        }
        return productDetailPage
    }

    getCallReminderSlots(ivrConfig: any, classStartTime: string, bookingNumber: string, isWaitlisted: boolean) {
        let selectedCallTimeBefore, selectedCallTime
        const ivrStatus = ivrConfig && ivrConfig.status
        selectedCallTimeBefore = ivrConfig.minutesBefore ? ivrConfig.minutesBefore.toString() : ""
        if (ivrStatus) {
            selectedCallTime = ivrConfig.minutesBefore ? CultUtil.getCallTime(ivrConfig.minutesBefore, classStartTime) : ""
        } else {
            selectedCallTime = ""
        }
        return {
            bookingNumber,
            isEditable: ivrConfig.isIVREditable,
            isReminderSet: ivrStatus,
            isWaitlisted,
            selectedCallTime,
            selectedCallTimeBefore,
            slots: {
                title: "Set time for call reminder",
                slotTimes: CultUtil.getSlotTimes(ivrConfig, true, "-", classStartTime)
            },
            slotSelectedAction: CultUtil.getSlotSelectedAction(isWaitlisted, ivrStatus)
        }
    }

    private async getSummaryWidget(userContext: UserContext, productType: ProductType, booking: CultBooking): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        const day = TimeUtil.formatDateStringInTimeZone(booking.Class.date, tz, "ddd, D MMM")
        const duration = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "HH:mm:ss").format("hh:mm A")
        const centerName = booking.Center.name
        let subTitle = duration + ", " + day + ", " + centerName
        const document = _.find(booking.Class.Workout.documents, document => {
            return document.tagID === 11
        })
        // check if its subuser booking, show subuser name if it is
        if (booking.userID.toString() !== userContext.userProfile.userId) {
            const subUser = await this.userCache.getUser(booking.userID.toString())
            const subUserName = `For ${subUser.firstName} ${subUser.lastName}`
            subTitle = `${subUserName}\n${subTitle}`
        }
        const cultUnderlyingClass = booking.CultClass

        const summaryWidget: WidgetView & {
            title: string,
            bookingNumber: string,
            subTitle: string,
            date: string,
            startTime: string,
            endTime: string,
            meta?: any,
            image: string,
            manageOptions?: { displayText: string, options: ManageOptionPayload[] }
        } = {
            widgetType: "CULT_BOOKING_SUMMARY",
            bookingNumber: booking.bookingNumber,
            title: CultUtil.pulsifyClassName(cultUnderlyingClass, userContext),
            subTitle: subTitle,
            date: booking.Class.date,
            startTime: booking.Class.startTime,
            endTime: booking.Class.endTime,
            image: document ? "/" + document.URL : undefined
        }

        const cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, booking, undefined, false, true)
        summaryWidget.manageOptions = cultManageOptions.manageOptions
        summaryWidget.meta = cultManageOptions.meta
        summaryWidget.hasDividerBelow = false
        return summaryWidget
    }

    private async getWaitlistNotificationWidget(
        cultBooking: CultBooking,
        enabledOptions: ManageOptionPayload[],
        userContext: UserContext
    ): Promise<ActionCardWidget> {
        const actionType = "SHOW_CULT_WAITLIST_NOTIFICATION_MODAL"
        const waitlistNotificationOption = _.find(enabledOptions, manageOption => {
            return manageOption.type === actionType
        })
        const cultClass = cultBooking.Class

        if (waitlistNotificationOption && cultClass?.wlNotificationTime) {
            const classId = cultClass.id
            const timezone = userContext.userProfile.timezone
            const { timeRemainingToClass, notificationTimeSlots } =  getTimeSlotsForWaitlistExtensionWidget( cultClass,
                timezone, cultClass.date + "T" + cultClass.startTime + cultClass.timezone)
            const isActionDisabled = !waitlistNotificationOption.isEnabled

            let notificationSlots: CultWaitlistSlotInfo[]

            notificationSlots = notificationTimeSlots.map((time) => {
                return {
                    title: `${time} mins before the class`,
                    time: time,
                    value: cultClass?.wlNotificationTime === time,
                    isRecommended: CULT_WAITLIST_EXTENSION_RECOMMENDED_TIME === time
                }
            })

            const action: Action = {
                actionType: waitlistNotificationOption.type,
                title: "EDIT SLOT",
                meta: {
                    notificationSlots: notificationSlots,
                    modalTitle: "Choose your waitlist confirmation time:",
                    modalSubtitle: "Note : The closer you are to the class starting time, the higher the chances of your waitlist getting confirmed",
                    ctaTitle: "Save changes",
                    classId: classId,
                },
                shouldRefreshPage: true,
            }

            return {
                widgetType: "ACTION_CARD_WIDGET",
                action: !isActionDisabled && timeRemainingToClass > 60 ? action : undefined,
                isDisabled: isActionDisabled,
                displayTextValue: "Your waitlist will be confirmed",
                displayTextValueStyle: {
                    fontSize: 14,
                    fontFamily: "BrandonText-Regular",
                    color: "#000000",
                    flex: 1,
                    flexDirection: "row",
                    flexShrink: 1
                },
                textValueMiddle: ` ${cultClass?.wlNotificationTime} mins `,
                textValueEnd: "prior to your class",
                textValueMiddleStyle: {
                    fontFamily: "BrandonText-Bold",
                    fontSize: 14
                },
                analyticsData: {
                    eventKey: "widget_click",
                    eventData: {
                        type: "waitlist_extension_modal",
                    }
                },
                textContainerStyle: {
                    alignItems: "flex-start",
                    flexDirection: "column",
                },
                actionContainerStyle: {
                    alignSelf: "flex-start",
                    marginLeft: 40,
                },
                showDivider: true,
                rowContainerStyle: {
                    paddingVertical: 0,
                    paddingTop: 11,
                    paddingBottom: 10,
                },
                icon: {
                    iconType: "IMAGE_URL",
                    url: "/image/icons/cult/waitlist_time.png"
                },
            }
        }
        return undefined
    }

    private async getCancelClassWidget(userContext: UserContext, cultBooking: CultBooking, enabledOptions: ManageOptionPayload[], meta: any, cafeFoodDetails?: FoodFulfilment): Promise<ActionCardWidget> {
        const actionType = "CANCEL_CULT_CLASS"
        const isRescheduleClassSupported = await CultUtil.isRescheduleClassSupported(userContext, cultBooking, this.hamletBusiness, cafeFoodDetails)
        let cancelClassOption = undefined
        cancelClassOption = _.find(enabledOptions, manageOption => {
                return manageOption.type === actionType
        })
        const tz = userContext.userProfile.timezone
        if (cancelClassOption) {
            const cancelCutOffTime = TimeUtil.getMomentForDateString(cultBooking.Class.startTime, tz, "hh:mm:ss")
                .subtract(cultBooking.bookingNumber ? cultBooking.Class.bookingCancellationLimit : cultBooking.Class.wlNotificationTime, "minutes").format("h:mm A")
            const displayText = cultBooking.bookingNumber ? `Cancel by ${cancelCutOffTime}` : `Leave waitlist by ${cancelCutOffTime}`
            const cancelClassAction: Action = {
                title: cultBooking.bookingNumber ? "CANCEL" : "LEAVE",
                actionType: cancelClassOption.type,
                meta: cancelClassOption.meta,
                analyticsData: {
                    classCredit: cultBooking?.creditCost
                }
            }
            let action = cancelClassAction

            if (cafeFoodDetails && cafeFoodDetails.fulfilmentId && AppUtil.isCultCafeSupported(userContext)) {
                const cancelCafeActionType: ManageOption = "CANCEL_CULT_MEAL_CLASS_MODAL"
                const cancelCafeAction = {
                    title: cultBooking.bookingNumber ? "CANCEL" : "LEAVE",
                    actionType: cancelCafeActionType,
                    meta: {
                        title: `Cancel class at ${cultBooking.Center.name}`,
                        subTitle: `${TimeUtil.formatDateStringInTimeZone(cultBooking.Class.date, tz, "ddd, D MMM")} ${momentTz.tz(cultBooking.Class.startTime, "hh:mm:ss", tz).format("hh:mm A")} - ${momentTz.tz(cultBooking.Class.endTime, "hh:mm:ss", tz).format("hh:mm A")}`,
                        cancelOptions: [{
                            title: "Cancel Workout Only",
                            type: "CANCEL_CLASS"
                        }, {
                            title: "Cancel Workout + Workout Snacks",
                            type: "CANCEL_CLASS_AND_MEAL",
                            meta: {
                                fulfilmentId: cafeFoodDetails.fulfilmentId,
                                date: TimeUtil.formatDateInTimeZone(tz, cafeFoodDetails.validity.start)
                            }
                        }],
                        action: { ...cancelClassAction, actionType: "CANCEL_CULT_CLASS" }
                    }
                }
                if (isRescheduleClassSupported && cancelClassOption?.type === "SHOW_CLASS_RESCHEDULE_MODAL") {
                    action = {
                        ...cancelClassAction,
                        meta: {
                            ...cancelClassAction.meta,
                            actions: cancelClassAction.meta.actions.map((action: Action) => {
                                if (action.actionType === "CANCEL_CULT_CLASS") {
                                    return cancelCafeAction
                                }
                                return action
                            })
                        }
                    }
                } else {
                    action = cancelCafeAction
                }
            }
            action.meta = {...action.meta, fromFlutterBookingpage: true}
            return {
                widgetType: "ACTION_CARD_WIDGET",
                displayText: displayText,
                icon: {
                    iconType: "CANCEL",
                    gradientColors: ["#47ebc6", "#49f6be"]
                },
                action: cancelClassOption.isEnabled ? action : undefined,
                isDisabled: !cancelClassOption.isEnabled
            }
        }
    }

    private getWorkoutStationWidget(workoutCell: string): ActionCardWidget {
        return {
            widgetType: "ACTION_CARD_WIDGET",
            displayText: `Workout & equipment station : `,
            displayTextStyle: {
                fontFamily: "BrandonText-medium"
            },
            displayTextValue: workoutCell,
            displayTextValueStyle: {
                fontFamily: "BrandonText-Bold",
                fontSize: 14,
                color: "#000000"
            },
            icon: {
                iconType: "IMAGE_URL",
                url: "/image/icons/cult/cult_spot_number.png"
            }
        }
    }

    private getClassDropoutWidget(userContext: UserContext, cultBooking: CultBooking, enabledOptions: ManageOptionPayload[], meta: any, cafeFoodDetails?: FoodFulfilment): ActionCardWidget {
        const actionType = "CULT_CLASS_DROPOUT"
        const classDropoutOption = _.find(enabledOptions, manageOption => {
            return manageOption.type === actionType
        })
        const tz = userContext.userProfile.timezone
        if (classDropoutOption) {
            const isAlreadyDroppedOut = cultBooking.label === "Dropped out"
            let displayText = "Request last minute cancellation"
            let action: Action = {
                title: "DROPOUT",
                actionType: classDropoutOption.type,
                meta: classDropoutOption.meta
            }
            if (this.isMockDropoutEnabled(cultBooking)) {
                this.logger.info(`dropoutBooking in mock`)
                action.actionType = "CANCEL_CULT_CLASS"
                action.meta.cancelWithoutAlert = true
            }
            let footer: { text: string, seeMore?: Action }
            if (isAlreadyDroppedOut) {
                displayText = "You’ve dropped out of this class"
                action = {
                    title: "KNOW MORE",
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=dropouthiw"
                }
            } else if (!classDropoutOption.isEnabled) {
                const dropoutStartTime = momentTz.tz(cultBooking.Class.dropoutStartTime, "hh:mm:ss", tz).format("h:mm A")
                displayText = `Dropout after ${dropoutStartTime}`
                action = {
                    title: "KNOW MORE",
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=dropouthiw"
                }
            } else {
                footer = {
                    text: "If another member books the slot you dropped out of, No-show will be waived for you. ",
                    seeMore: {
                        title: "KNOW MORE",
                        actionType: "NAVIGATION",
                        url: "curefit://listpage?pageId=dropouthiw"
                    }
                }
            }
            if (classDropoutOption.isEnabled && cafeFoodDetails && cafeFoodDetails.fulfilmentId && AppUtil.isCultCafeSupported(userContext)) {
                action = {
                    title: "DROPOUT",
                    actionType: "CANCEL_CULT_MEAL_CLASS_MODAL",
                    meta: {
                        title: `Dropout class at ${cultBooking.Center.name}`,
                        subTitle: `${TimeUtil.formatDateStringInTimeZone(cultBooking.Class.date, tz, "ddd, D MMM")} ${momentTz.tz(cultBooking.Class.startTime, "hh:mm:ss", tz).format("hh:mm A")} - ${momentTz.tz(cultBooking.Class.endTime, "hh:mm:ss", tz).format("hh:mm A")}`,
                        cancelOptions: [{
                            title: "Dropout",
                            type: "CULT_CLASS_DROPOUT"
                        }, {
                            title: "Dropout and Cancel Snacks",
                            type: "CANCEL_CLASS_AND_MEAL",
                            meta: {
                                fulfilmentId: cafeFoodDetails.fulfilmentId,
                                date: TimeUtil.formatDateInTimeZone(tz, cafeFoodDetails.validity.start)
                            }
                        }],
                        action
                    }
                }
            }

            if (action.meta) {
                action.meta.shouldPopPageAfterCompletion = true
            }
            action.meta = {...action.meta, fromFlutterBookingpage: true}
            action.analyticsData = {classCredit: cultBooking?.creditCost}
            return {
                widgetType: "ACTION_CARD_WIDGET",
                displayText: displayText,
                icon: {
                    iconType: "DROPOUT"
                },
                action,
                isDisabled: !classDropoutOption.isEnabled,
            }
        }
    }

    private getLogScoreWidget(manageOptions: ManageOptionPayload[], meta: any): ActionCardWidget {
        const logScoreOption = _.find(manageOptions, manageOption => {
            return manageOption.type === "CANCEL_CULT_CLASS"
        })
        if (logScoreOption) {
            return {
                widgetType: "ACTION_CARD_WIDGET",
                displayText: `Log Score`,
                icon: {
                    iconType: "LOG"
                },
                action: {
                    title: "LOG",
                    actionType: logScoreOption.type,
                    meta: logScoreOption.meta
                }
            }
        }
    }

    private getPulseNoteWidget(bookingResponse: CultBookingResponse, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const reachCutOffTime = TimeUtil.getMomentForDateString(bookingResponse.booking.Class.startTime, tz, "hh:mm:ss")
            .subtract(CultUtil.getPulseClassReachEarlyCutoff(), "minutes").format("h:mm A")
        return {
            widgetType: "ACTION_CARD_WIDGET",
            displayText: reachCutOffTime ? `Reach by ${reachCutOffTime} to collect your PULSE kit` : `Reach ${CultUtil.getPulseClassReachEarlyCutoff()} minutes early`,
            icon: {
                iconType: "IMAGE_URL",
                url: "/image/pulse/clock_icon_4.png"
            },
        }
    }

    private getPulseDeviceInfoWidget(bookingResponse: CultBookingResponse, userContext: UserContext): ActionCardWidget | undefined {
        if (bookingResponse.booking && bookingResponse.booking.CultClass) {
            const cultUnderlyingClass = bookingResponse.booking.CultClass
            const isPulseEnabled = CultUtil.isClassAvailableForPulse(cultUnderlyingClass, AppUtil.isCultPulseFeatureSupported(userContext))

            if (isPulseEnabled && !_.isEmpty(bookingResponse.booking.pulseDeviceName)) {
                return {
                    widgetType: "ACTION_CARD_WIDGET",
                    displayText: CultUtil.pulsifyDeviceName(bookingResponse.booking, true),
                    icon: {
                        iconType: "PULSE_DEVICE"
                    }
                }
            }
        }
    }

    private getClassStatusWidget(bookingResponse: CultBookingResponse): ActionCardWidget {
        if (bookingResponse.booking && bookingResponse.booking.label === "Upcoming") {
            const isSportCategoryBooking = bookingResponse?.booking?.Class?.Workout?.isSportCategory

            return {
                widgetType: "ACTION_CARD_WIDGET",
                displayText: isSportCategoryBooking ? "Your session is confirmed" : "Your class is confirmed",
                icon: {
                    iconType: "CONFIRM",
                    gradientColors: ["#47ebc6", "#49f6be"]
                }
            }
        } else if (bookingResponse.waitlist && bookingResponse.waitlist.state === "PENDING") {
            return {
                widgetType: "ACTION_CARD_WIDGET",
                displayText: "You are Waitlisted",
                meta: {
                    number: bookingResponse.waitlist.waitlistNumber
                },
                action: {
                    actionType: "NAVIGATION",
                    title: "KNOW MORE",
                    url: "curefit://listpage?pageId=waitlisthiw"
                },
                icon: {
                    iconType: "NUMBER",
                    bgColor: "#5bdbb6",
                    textColor: "#ffffff"
                },
                analyticsData: {
                    eventType: "WAITLIST_KNOW_MORE_CLICK"
                }
            }
        }
    }

    private getJuniorOtpWidget(otp: number): WidgetView {
        const title = "Exit PIN"
        const otpString = `${otp}`
        const subTitle = "You need to enter this 4 digit PIN on center attendance tablet after your workout"
        const widget: WidgetView = new OTPInfoWidget(title, otpString, subTitle)
        widget.hasDividerBelow = false
        return widget
    }

    private isMockDropoutEnabled(cultBooking: CultBooking) {
        const classTimeInMs = (new Date("" + cultBooking.CultClass.startDateTimeUTC + " UTC")).valueOf()
        const currentTimeInMs = (new Date()).valueOf()
        const classTimeDiffInMin = CultUtil.convertMillisToMinutes(Math.abs(currentTimeInMs - classTimeInMs))
        this.logger.debug(`dropoutBooking `, {cultBooking, classTimeDiffInMin, classTimeInMs, currentTimeInMs, startDateTimeUTC: cultBooking.CultClass.startDateTimeUTC,
            cityID: cultBooking?.CultClass?.Center?.cityID})
        return (cultBooking?.CultClass?.Center?.cityID === 3 && classTimeDiffInMin > 30 && classTimeDiffInMin <= 60)
    }

    private getCallReminderWidget(ivrConfig: any, classStartTime: string, bookingNumber: string, isWaitlisted: boolean, tz: Timezone): CallReminderWidget {
        let selectedCallTimeBefore, selectedCallTime
        const ivrStatus = ivrConfig && ivrConfig.status
        selectedCallTimeBefore = ivrConfig.minutesBefore ? ivrConfig.minutesBefore.toString() : ""
        if (ivrStatus) {
            selectedCallTime = ivrConfig.minutesBefore ? CultUtil.getCallTime(ivrConfig.minutesBefore, classStartTime) : ""
        } else {
            selectedCallTime = ""
        }
        let ivrStatusMessage
        switch (ivrConfig.ivrState) {
            case "SUCCESS":
            case "LATE":
                ivrStatusMessage = `Reminder call was completed at ${TimeUtil.get12HRTimeFormat(ivrConfig.alarmProcessingDateTimeUTC, tz, true)}`
                break
            case "IVR_UNANSWERED":
                ivrStatusMessage = `We tried reaching out to you at ${TimeUtil.get12HRTimeFormat(ivrConfig.alarmProcessingDateTimeUTC, tz, true)} but the call was unanswered`
                break
            case "IVR_NETWORK_ERROR":
                ivrStatusMessage = "We tried reaching out to you but due to connectivity issue couldn’t get through"
                break
        }
        return {
            widgetType: "CALL_REMINDER_WIDGET",
            bookingNumber,
            title: "Call Reminder",
            info: {
                title: "KNOW MORE",
                actionType: "NAVIGATION",
                url: "curefit://listpage?pageId=callreminderhiw"
            },
            isEditable: ivrConfig.isIVREditable,
            isReminderSet: ivrStatus,
            isWaitlisted,
            selectedCallTime,
            selectedCallTimeBefore,
            slots: {
                checkBoxTitle: "Set same reminder for all classes",
                title: "Set time for call reminder",
                slotTimes: CultUtil.getSlotTimes(ivrConfig, true, "-", classStartTime)
            },
            notEditableAction: {
                type: "ALERT",
                title: "Call reminder time can not be changed",
                message: "Call reminder time can not be changed too close to the class start time",
                button: {
                    title: "Ok",
                    actionType: "HIDE_ALERT_MODAL"
                }
            },
            slotSelectedAction: CultUtil.getSlotSelectedAction(isWaitlisted, ivrStatus),
            ivrStatusMessage,
            fromFlutterBookingpage: true
        }
    }

    private getCenterInfoWidget(userContext: UserContext, cultCenter: CultCenter, productType: ProductType): CenterView & WidgetView {
        const centerView: CenterView = new CenterView(userContext, cultCenter, productType)
        const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
        return Object.assign(widgetView, centerView)
    }

    private getCenterViewForCultRun(userContext: UserContext,
                          cultCenter: CultCenter,
                          productType: ProductType): CenterView {
        const address: Address = {
            addressString: cultCenter?.metadata?.parkingLocation?.address,
            latLong: {
                lat: cultCenter?.Address?.latitude,
                long: cultCenter?.Address?.longitude
            },
            mapUrl: cultCenter?.metadata?.parkingLocation?.navigation,
            pincode: cultCenter?.metadata?.parkingLocation?.pincode,
        }
        const userAgent = userContext.sessionInfo.userAgent
        const seoParams: SeoUrlParams = {
            locality: cultCenter?.locality,
            productName: cultCenter?.name,
        }
        const  centerView: CenterView = {
            id: cultCenter?.id,
            name: cultCenter?.name,
            action:
                productType === "FITNESS"
                    ? ActionUtil.cultCenter(
                    cultCenter.id.toString(),
                    undefined,
                    userAgent,
                    seoParams
                    )
                    : ActionUtil.mindCenter(
                    cultCenter?.id?.toString(),
                    undefined,
                    userAgent,
                    seoParams
                    ),
            address: address,
            distance: "",
            mapUrl: cultCenter?.metadata?.parkingLocation?.navigation,
            showIcon: false,
        }
        return centerView
    }

    private getCenterInfoWidgetForCultRunParkingLocation(
        userContext: UserContext,
        cultCenter: CultCenter,
        productType: ProductType,
        title: string
    ): CenterView & WidgetView & Header {

        const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
        const header: Header = { title: title }
        const centerView = this.getCenterViewForCultRun(userContext, cultCenter, productType)
        return Object.assign(widgetView, centerView, header)
    }
    private getCenterInfoWidgetForCultRunMeetupLocation(userContext: UserContext, cultCenter: CultCenter, productType: ProductType, title: string): CenterView & WidgetView & Header {
        const centerView: CenterView = new CenterView(userContext, cultCenter, productType)
        const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
        const header: Header = {title: title}
        return Object.assign(widgetView, centerView, header)
    }

    private getDescriptionWidget(description: string): WidgetView {
        return new DescriptionWidgetV2("About", [description])
    }

    private async getPackInfoWidget(userContext: UserContext, membership: CultMembership): Promise<PackInfoWidget> {
        if (membership) {
            const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membership.id.toString(), this.membershipService)
            const action: ActionCard = {
                title: "SEE PACK",
                action: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
            }
            return new PackInfoWidget(membership.productID, membership.packName, action)
        }
        else {
            return undefined
        }
    }
    private async getCafeFoodWidget(cafeFoodDetails: FoodFulfilment, timezone: Timezone, userAgent: UserAgentType): Promise<WorkoutSnackWidget> {
        const widgetType: WidgetType = "WORKOUT_SNACK_WIDGET"
        const date: string = TimeUtil.formatDateInTimeZone(timezone, cafeFoodDetails.validity.start)
        const menuType: MenuType = "ALL"
        const images: {
            url: string,
            title: string,
            calories?: string | number,
            isVeg?: boolean,
        }[] = []
        const productIds: string[] = []
        const productQuantityMap: { [productId: string]: number } = {}
        for (const j in cafeFoodDetails.products) {
            productIds.push(cafeFoodDetails.products[j].productId)
            if (_.isNil(productQuantityMap[cafeFoodDetails.products[j].productId])) {
                productQuantityMap[cafeFoodDetails.products[j].productId] = 0
            }
            productQuantityMap[cafeFoodDetails.products[j].productId] += cafeFoodDetails.products[j].quantity
        }
        const productMap: { [productId: string]: Product } = await this.catalogueService.getProductMap(productIds)
        for (const productId in productMap) {
            const product: Product = productMap[productId]
            let parentProduct
            if (!_.isNil(product.parentProductId)) {
                parentProduct = await this.catalogueService.getProduct(product.parentProductId)
            }
            const nutritionInfo = EatUtil.computeNutritionalInfo(product, parentProduct, cafeFoodDetails.listingBrand)
            const url: string = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            const title: string = product.title
            const isVeg: boolean = product.attributes.isVeg ? true : false
            const calories: string | number = `${nutritionInfo.Calories["Total Calories"]} cal`
            let quantity: number = !_.isNil(productQuantityMap[productId]) ? productQuantityMap[productId] : 0
            while (quantity > 0) {
                images.push({ url, title, calories, isVeg })
                quantity--
            }
        }
        const seoParams: SeoUrlParams = {
            productName: productMap[cafeFoodDetails.products[0].productId].title
        }
        const action: Action = {
            actionType: "NAVIGATION",
            url: (cafeFoodDetails.products.length === 1) ? ActionUtil.foodSingle(cafeFoodDetails.products[0].productId, date, menuType, false, cafeFoodDetails.fulfilmentId, userAgent, seoParams) : ActionUtil.foodCart(cafeFoodDetails.fulfilmentId, <MealSlot>"ALL", date)
        }
        return {
            widgetType: widgetType,
            action: action,
            images: images
        }
    }
    private async getOfferWidget(kioskId: string, userContext: UserContext, booking: CultBooking): Promise<BulkOfferAPIResponse> {
        const date: string = booking.Class.date
        const userProfile = userContext.userProfile
        const sessionInfo = userContext.sessionInfo
        const requiredOfferTypes: BulkApiOfferType[] = ["EAT_CART"]
        const baseOfferRequestParam: BaseOfferRequestParams = {
            userId: userProfile.userId,
            cityId: userProfile.cityId,
            deviceId: sessionInfo.deviceId,
            source: AppUtil.callSourceFromContext(userContext)
        }
        const deliveryArea: DeliveryArea = kioskId ? await this.deliveryAreaService.findAreaForKiosk(kioskId) : undefined
        const bulkOfferAPIRequest: BulkOfferAPIRequest = Object.assign({}, baseOfferRequestParam, {
            areaId: deliveryArea ? deliveryArea.areaId : undefined,
            dateMealSlotMap: { [date]: [<MenuType>"ALL"] },
            requiredOfferTypes: requiredOfferTypes
        })
        return null
    }

    private async getShareActionWidget(userContext: UserContext, cultBooking: CultBooking, productType: ProductType, hasTopDivider?: boolean): Promise<ActionCardWidget> {
        if (CultUtil.isInviteBuddySupportedForWorkout(cultBooking.Class.Workout)) {
            const action = CultUtil.getLazyInviteLinkAction(userContext, productType, cultBooking)
            if (action) {
                return {
                    widgetType: "ACTION_CARD_WIDGET",
                    displayText: "Invite buddies for workout",
                    icon: {
                        iconType: "IMAGE_URL",
                        url: "/image/icons/cult/share_20px.png"
                    },
                    action: {
                        ...action,
                        title: "INVITE"
                    },
                    isDisabled: false
                }
            }
        }
    }

    private getPageActions(booking: CultBooking, userContext: UserContext): Action[] {
        const isSportCategoryBooking: boolean = booking?.Class?.Workout?.isSportCategory

        const noQRCodeMessageSuffix: string = isSportCategoryBooking ? "minutes before the session start time" : "minutes before the class start time"
        const noQRCodeMessage: string = `QR Code will appear here ${booking.qrCode.lowerLimitForQRCode}` + noQRCodeMessageSuffix
        if (userContext.sessionInfo.appVersion === 10.73) {
            return [
                {actionType: "NAVIGATION",
                url: "curefit://hometab",
                title: "MARK ATTENDANCE",
                }
            ]
        }
        if (!_.isEmpty(booking.qrCode) && _.isEmpty(booking.workoutCell)) {
            return [{
                actionType: "SHOW_QR_CODE_MODAL",
                title: "MARK ATTENDANCE",
                meta: {
                    closeIconUrl: "/image/icons/cult/cancel-modal.png",
                    title: "Mark Attendance",
                    qrCodeString: booking.qrCode.qrString,
                    noQRCodeMessage: noQRCodeMessage,
                    qrCodeContainerStyle: {
                        borderWidth: 8,
                        borderColor: "#676877"
                    },
                    refreshQRCode: {
                        title: "Refresh QR Code",
                        actionType: "REFRESH_QR_CODE",
                        meta: {
                            bookingId: booking.id,
                        }
                    },
                    footer: {
                        title: "",
                        subTitle: "Scan this QR Code at the attendance tab to mark your attendance"
                    }
                },
                analyticsData: {
                    "classCredit": booking?.creditCost
                }
            }]
        }
        return []
    }
}
export default BookingDetailViewBuilderV2
