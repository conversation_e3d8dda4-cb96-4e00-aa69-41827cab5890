import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CULT_CLIENT_TYPES, CultMembership, CultPack, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { ProductType } from "@curefit/product-common"
import {
    ActionPageWidgetView,
    AddTransferMemberDetails,
    CenterSelectionWidget,
    DisplayUserDetailWidget,
    InfoSeeMoreWidget,
    InviteUserWidget,
    SelectCenterAction,
    TitleDescriptionWidget,
    TransferMembershipTypeInfo,
    TransferMembershipWidget,
    UserSelectionWidget,
    WidgetView
} from "../common/views/WidgetView"
import { CacheHelper } from "../util/CacheHelper"
import { User } from "@curefit/user-common"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { City } from "@curefit/location-common"
import { Cult<PERSON>enter, CultTransferProduct, ValidateMembershipTransfer } from "@curefit/cult-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { TransferPackNameByTransferType } from "../util/CultUtil"
import AppUtil from "../util/AppUtil"
import { UserContext } from "@curefit/userinfo-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"


@injectable()
class TransferMembershipViewBuilder {
    constructor(@inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
    ) {
    }

    async buildView(membershipId: string, packId: number, fitnessTransferPacks: CultTransferProduct[], productType: ProductType, userContext: UserContext): Promise<{ widgets: WidgetView[] }> {
        const widgets: WidgetView[] = []
        widgets.push(await this.getTransferMembershipWidget(membershipId, packId, fitnessTransferPacks, productType, userContext))
        widgets.push(this.footerKnowMoreWidget())
        return { widgets }
    }

    async transferDetails(membershipId: string, productType: ProductType, userId: string, membershipTransferType: string, transferProductId: string, transferId: string, selectedUserId?: string, selectedCityId?: string, selectedCenterId?: string): Promise<ActionPageWidgetView> {
        const transferMembershipPage: any = {}
        const widgetPromises: Promise<WidgetView>[] = []
        const membership = productType === "FITNESS" ? await this.cultFitService.getMembershipById(parseInt(membershipId), userId) : await this.mindFitService.getMembershipById(parseInt(membershipId), userId)
        let selectedUser, selectedCity, selectedCenter
        if (!selectedUserId && membershipTransferType !== "ANOTHER_USER") {
            selectedUserId = userId
        }
        selectedUser = selectedUserId ? await this.userCache.getUser(selectedUserId) : null
        selectedCity = selectedCityId ? await this.cityService.getCityById(selectedCityId) : null
        selectedCenter = selectedCenterId ? productType === "FITNESS" ? await this.catalogueService.getCultCenter(selectedCenterId) : await this.catalogueService.getCultMindCenter(selectedCenterId) : null
        if (membershipTransferType === "ANOTHER_USER") {
            widgetPromises.push(this.buildUserSelectionWidget(selectedUser))
        }
        widgetPromises.push(this.buildCitySelectionWidget(selectedCity, membershipTransferType))
        if (selectedCity) {
            widgetPromises.push(this.buildCenterInfoWidget(membership, productType, selectedCenter, selectedCity.cityId, selectedCenterId, membershipTransferType))
        }

        transferMembershipPage.widgets = await Promise.all(widgetPromises)
        let title = "Proceed", action: any = { actionType: "TRANSFER_MEMBERSHIP" }, orderProduct: any = {}, meta: any = {
            selectedUserId,
            selectedCityId,
            selectedCenterId
        }
        if (!selectedUser && membershipTransferType === "ANOTHER_USER") {
            title = "Add member details"
            action = {
                actionType: "NAVIGATION",
                url: "curefit://addMemberDetails"
            }
            meta = {}
        } else if (!selectedCity) {
            title = "Pick a city"
            action = {
                actionType: "SHOW_SELECT_CITY"
            }
            meta = {
                selectedUserId
            }
        } else if (!selectedCenterId) {
            title = "Select preferred center"
            action = this.getPreferredCenterAction(membership, productType, selectedCityId)
            meta = {
                selectedUserId,
                selectedCityId,
                ...action.meta
            }
        } else {
            orderProduct = {
                productType: productType,
                productId: transferProductId,
                option: {
                    transferId: transferId,
                    membershipId,
                    isTransferringMembership: true,
                    membershipTransferType,
                    centerServiceCenterId: selectedCenter.centerServiceId,
                    ...meta
                }
            }
        }
        transferMembershipPage.actions = [{
            title: title,
            ...action,
            meta: meta,
            orderProduct
        }]
        return transferMembershipPage
    }

    private async getTransferMembershipWidget(membershipId: string, packId: number, fitnessTransferPacks: CultTransferProduct[], productType: ProductType, userContext: UserContext): Promise<TransferMembershipWidget> {
        let filteredFitnessTransferPacks = fitnessTransferPacks
        if (AppUtil.removeAnotherUserTransferMembership(userContext)) {
            filteredFitnessTransferPacks = fitnessTransferPacks.filter(pack => pack.transferType !== "ANOTHER_USER")
        }
        let originalMembership: CultMembership
        let alertWidgetAction: Action = null
        if (productType == "FITNESS") {
            originalMembership = await this.cultFitService.getMembershipById(Number(membershipId), userContext.userProfile.userId, "CUREFIT_APP")
            const isUserHavingFutureBookings: boolean = await AppUtil.isUserHavingActiveBooking(originalMembership.membershipId, userContext.userProfile.userId, TimeUtil.parseDate(originalMembership.endDate, "UTC"), this.cultFitService, this.rollbarService)
            if (isUserHavingFutureBookings) {
                alertWidgetAction = {
                    actionType: "SHOW_ALERT_MODAL",
                    title: "Transfer Membership",
                    meta: {
                        title: "Transfer Membership",
                        subTitle: "All of your existing class bookings will be cancelled automatically once you transfer the pack to another city",
                    }
                }
            }
        }
        const transferMembershipType: TransferMembershipTypeInfo[] = filteredFitnessTransferPacks.map((pack: CultTransferProduct) => {
            const transferDetailAction: Action = {
                title: "SELECT",
                actionType: "NAVIGATION",
                url: `curefit://enterTransferDetails?membershipTransferType=${pack.transferType}&membershipId=${membershipId}&packId=${packId}&transferProductId=${pack.productId}&productType=${productType}&transferId=${pack.id}`
            }
            let transferAction: Action = null
            if (!_.isNil(alertWidgetAction)) {
                transferDetailAction.title = "Ok"
                alertWidgetAction.meta.actions = [transferDetailAction]
                transferAction = alertWidgetAction
            } else {
                transferAction = transferDetailAction
            }
            return {
                title: TransferPackNameByTransferType[pack.transferType] || pack.transferType,
                description: pack.info,
                packTransferType: pack.transferType,
                action: transferAction
            }
        })

        return {
            widgetType: "TRANSFER_MEMBERSHIP_WIDGET",
            title: "Transfer my membership to",
            separatorText: "OR",
            hasDividerAbove: true,
            transferMembershipType: transferMembershipType
        }
    }

    private footerKnowMoreWidget(): InfoSeeMoreWidget {
        return {
            widgetType: "INFO_SEE_MORE_WIDGET",
            title: "Want to know more about transfers?",
            seemore: {
                title: "Know more",
                actionType: "NAVIGATION",
                url: "curefit://infopage?contentId=transferMembershipPolicy&pageType=LIST"
            }
        }
    }

    private async buildUserSelectionWidget(selectedUser?: User): Promise<UserSelectionWidget> {
        const userSelectionWidget: UserSelectionWidget = {
            header: "Who will you transfer to?",
            title: "Add member details",
            widgetType: "USER_PICKER_WIDGET",
            action: {
                actionType: "NAVIGATION",
                url: "curefit://addMemberDetails"
            },
            selectedUserName: selectedUser ? `${selectedUser.firstName} ${selectedUser.lastName}` : "",
        }
        return userSelectionWidget
    }

    private async buildCitySelectionWidget(selectedCity: City, membershipTransferType: string): Promise<CenterSelectionWidget> {
        const centerSelectionWidget: CenterSelectionWidget = {
            widgetType: "CENTER_PICKER_WIDGET",
            header: membershipTransferType === "ANOTHER_USER" ? "Which city would the member be in?" : "Which city are you moving to?",
            title: selectedCity ? selectedCity.name : "Pick a City",
            canChangeCenter: true,
            action: {
                actionType: "SHOW_SELECT_CITY"
            },
            showHighlightedText: selectedCity ? false : true
        }
        return centerSelectionWidget
    }

    private getPreferredCenterAction(membership: CultMembership, productType: ProductType, selectedCityId: string): SelectCenterAction {
        return {
            title: "Pick a preferred center",
            showHelp: true,
            showFavourite: false,
            actionType: "SELECT_CENTER",
            meta: {
                packId: CatalogueServiceUtilities.extractPackId(membership.productID), // PMS::TODO: Remove when migrating transfer to new flow
                productId: membership.productID,
                productType: productType,
                ageCategory: "ADULT",
                cityId: selectedCityId,
                useCenterServiceId: true
            },
            productType: productType,
            queryParams: {
                cityId: selectedCityId
            }
        }
    }

    private async buildCenterInfoWidget(membership: CultMembership, productType: ProductType, selectedCenter: CultCenter, selectedCityId: string, selectedCenterId: string, membershipTransferType: string): Promise<CenterSelectionWidget> {
        if (membership && membership.centerID) {
            const action: SelectCenterAction = this.getPreferredCenterAction(membership, productType, selectedCityId)
            const centerInfoWidget: CenterSelectionWidget = {
                title: "Select center",
                prefixText: "",
                header: membershipTransferType === "ANOTHER_USER" ? "Which center would be the member be in?" : "Which center would the membership be in?",
                canChangeCenter: true,
                preferredCenterId: selectedCenterId ? Number(selectedCenterId) : undefined,
                preferredCenterName: selectedCenter ? selectedCenter.name : undefined,
                widgetType: "CENTER_PICKER_WIDGET",
                action: action,
                showHighlightedText: selectedCenter ? false : true
            }
            return centerInfoWidget
        }
    }

    async addMemberDetails(): Promise<AddTransferMemberDetails> {
        return {
            data: {
                title: "Verify member details by their phone number",
                helpText: "Enter their phone number",
                info: "Membership can only be transferred to registered cure.fit members",
            },
            referrerAction: {
                actionType: "NAVIGATION",
                url: "curefit://referralpagev2",
                title: "Refer them to Cure.fit"
            },
            actions: [{
                title: "Send OTP",
                actionType: "SEND_OTP_MEMBER"
            }]
        }
    }

    async enterMemberOtpDetailsView(mobileNumber: string): Promise<any> {
        return {
            message: `Please enter the code we just sent to the member at ${mobileNumber} to proceed`,
            mobileNumber: mobileNumber
        }
    }

    async memberDetailsView(memberDetails: ValidateMembershipTransfer, mobileNumber: string): Promise<ActionPageWidgetView> {
        const widgets: WidgetView[] = []
        if (memberDetails.userId) {
            const userDetails: User = await this.userCache.getUser(memberDetails.userId.toString())
            widgets.push(this.displayUserDetails(userDetails))
            return {
                widgets,
                actions: [{
                    title: "Proceed",
                    actionType: "SELECT_TRANSFER_MEMBER",
                    meta: {
                        selectedUserId: memberDetails.userId
                    }
                }]
            }
        } else {
            widgets.push(this.titleDescriptionWidget(mobileNumber))
            widgets.push(this.inviteUserWidget(mobileNumber))
            return { widgets }
        }

    }

    private displayUserDetails(userDetails: User): DisplayUserDetailWidget {
        return {
            header: "Member verified",
            name: `${userDetails.firstName} ${userDetails.lastName}`,
            mobileNumber: userDetails.phone,
            profilePictureUrl: userDetails.profilePictureUrl,
            widgetType: "DISPLAY_USER_DETAILS",
            hasDividerAbove: true
        }
    }

    private titleDescriptionWidget(mobileNumber: string): TitleDescriptionWidget {
        return {
            title: mobileNumber,
            description: "This mobile number is not registered with cure.fit. Please check again.",
            widgetType: "TITLE_DESCRIPTION_WIDGET",
            hasDividerAbove: true
        }
    }

    private inviteUserWidget(mobileNumber: string): InviteUserWidget {
        return {
            widgetType: "INVITE_USER_WIDGET",
            hasDividerAbove: true,
            action: {
                actionType: "NAVIGATION",
                url: "curefit://referralpagev2",
                title: "Refer them to Cure.fit"
            }
        }
    }
}

export default TransferMembershipViewBuilder
