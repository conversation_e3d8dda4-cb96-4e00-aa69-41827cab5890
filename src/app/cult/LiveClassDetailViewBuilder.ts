import * as _ from "lodash"
import { UserContext, BaseWidget, TemplateWidget } from "@curefit/vm-models"
import {
    ProductDetailPage,
    Action,
    WidgetView,
    DescriptionWidget,
    IconDescriptionWidget,
    ProductListWidget,
    ShareActionWidget,
    ManageOptionsWidgetV2,
    CultBuddiesJoiningListLargeView
} from "../common/views/WidgetView"
import { PageWidget } from "../page/Page"
import { injectable, inject } from "inversify"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { DigitalCatalogueEntryV1, ImageData, SocialDataResponse, DIYRecipeIngredient } from "@curefit/diy-common"
import { BannerCarouselWidget, ProductGuranteeWidget, TitleWidget, BannerWidget } from "../page/PageWidgets"
import { UrlPathBuilder } from "@curefit/product-common"
import { User } from "@curefit/user-common"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import LiveUtil, { CULT_LIVE_CAMPAIGN_ID } from "../util/LiveUtil"
import { UserAgentType, HourMin } from "@curefit/base-common"
import AppUtil from "../util/AppUtil"
import { ICultBusiness, PreferenceDetail } from "./CultBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import { IWidgetType, PageTypes, ImageOverlayCardContainerWidget, InfoCard } from "@curefit/apps-common"
import { HeaderWidget } from "@curefit/apps-common/dist/src/widgets/page/interfaces"
import WodViewBuilder, { IWODDetail } from "./WodViewBuilder"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { SimpleWod } from "@curefit/fitness-common"
import { LiveWodWidget } from "../common/views/LiveWodWidget"
import { builVanillaLiveClassView } from "../digital/VanillaLiveClassDetailViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { UNAUTHORIZED_ERROR_CODE } from "@curefit/error-client"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { LivePackUtil } from "../util/LivePackUtil"

export class LiveClassDetailView extends ProductDetailPage {

    pageType: string
    isFullScreen: boolean
    changeBackgroundColorIndex: number
    bannerImages: ImageData
    isRetelecast?: boolean
    refreshPageEpoch: number

    constructor(widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[], actions: Action[], isFullScreen: boolean, bannerImages: ImageData, isRetelecast: boolean, refreshPageEpoch: number) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.bannerImages = bannerImages
        this.pageType = "liveSessionDetail"
        this.isFullScreen = isFullScreen
        this.isRetelecast = isRetelecast
        this.refreshPageEpoch = refreshPageEpoch
        if (!this.isFullScreen) {
            this.changeBackgroundColorIndex = 1
        }
    }
}

@injectable()
class LiveClassDetailViewBuilder {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) private wodViewBuilder: WodViewBuilder,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
    ) {
    }

    async buildView(userContext: UserContext, classId: string, isFullScreen: boolean, nodeRelationId?: number): Promise<LiveClassDetailView> {
        const userAgent: UserAgentType = _.get(userContext, "sessionInfo.userAgent", "APP")
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[] = []
        const actions: Action[] = []
        let classResponse: DigitalCatalogueEntryV1
        const countryId = AppUtil.getCountryId(userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)

        try {
            classResponse = await this.diyFulfilmentService.getLiveClass(classId, userContext.userProfile.userId, countryId)
        } catch (err) {
            if (err.statusCode && err.responseCode === UNAUTHORIZED_ERROR_CODE) {
                widgets.push(new BannerWidget([{
                    id: "nora_class_error_banner",
                    image: "/image/livefit/app/no_nora_app_banner.png",
                    desktopWebImage: "/image/livefit/app/no_nora_desktop_banner.png",
                    action: `curefit://liveclassdetail?bookingNumber=5e958a36c5392200af7f5e59&productType=LIVE_FITNESS`
                }], userAgent !== "DESKTOP" ? "1110:2430" : "2149:1259"))
                return new LiveClassDetailView(widgets, actions, isFullScreen, undefined, false, undefined)
            }
        }

        const subscribedVideos: string[] = await this.diyFulfilmentService.getSubscribedVideos(userContext.userProfile.userId)
        const cultPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
        const user: User = await userContext.userPromise
        const isSubscribed = _.includes(subscribedVideos, (<any>classResponse)._id)
        const bannerImages = classResponse.bannerImages

        if (LiveUtil.isVanillaLiveFitFormat(classResponse.format)) {
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            return await builVanillaLiveClassView(classResponse, subscribedVideos, cultPreference, userContext, classId, this.classInviteLinkCreator, this.serviceInterfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId)
        }

        const isDetailPageV2 = AppUtil.isLiveClassDetailPageV2(userContext.sessionInfo, user, userContext)

        const { supported, card } = await this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)

        if (!isDetailPageV2) {
            widgets.push({
                widgetType: "HEADER_WIDGET",
                widgetTitle: {
                    title: classResponse.title,
                    subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "llll") + " | AT HOME" // "Mon 6 May, 5:00 PM | AT HOME"
                },
                showDivider: false,
                hasDividerBelow: false
            })
        }

        if (isFullScreen) {
            if (userAgent !== "DESKTOP" && !isDetailPageV2) {
                widgets.push(new BannerCarouselWidget("345:202", [{
                    id: (<any>classResponse)._id,
                    image: UrlPathBuilder.prefixSlash(classResponse.bannerImages.mobileImage),
                    action: undefined
                }], {
                    bannerWidth: 345,
                    bannerHeight: 202,
                    noVerticalPadding: false,
                    backgroundColor: "white",
                    roundedCorners: true
                }, 1, false, false, false))
            }

            let socialData: SocialDataResponse

            if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
                this.logger.info(`Adding social data for userId: ${userContext.userProfile.userId}, classId: ${classId}, nodeRelationId: ${nodeRelationId}`)
                if (nodeRelationId) {
                    await this.diyFulfilmentService.addSocialNode(userContext.userProfile.userId, classId, nodeRelationId)
                }
                const socialDataEternalPromise = eternalPromise(this.diyFulfilmentService.getSocialDataForSession(userContext.userProfile.userId, classId, !AppUtil.isLiveLazyInviteLinkActionSupported(userContext), AppUtil.getTenantFromUserContext(userContext)))
                if (socialDataEternalPromise) {
                    socialData = (await socialDataEternalPromise).obj
                    if (socialData && !_.isEmpty(socialData.attendingUsers) && !isDetailPageV2) {
                        const buddiesJoiningSmallListWidget: WidgetView = {
                            widgetType: "CULT_BUDDIES_JOINING_LIST_LARGE_WIDGET" as IWidgetType,
                            ...(await LiveUtil.getBuddiesJoiningListLargeView(socialData.attendingUsers, this.userCache, PageTypes.LiveClassDetail))
                        }
                        widgets.push(buddiesJoiningSmallListWidget)
                    }
                }
            }


            if (isSubscribed && !isDetailPageV2) {
                widgets.push(await this.getShareActionWidget(userContext, classResponse, socialData, null, card && { ...card, source: "class-details-invite" }))
            }

            const isSocialLeaguesSupported = await AppUtil.isSocialLeaguesSupported(userContext, "LIVE")

            if (isDetailPageV2) {
                widgets.push(await this.getImageOverlayCardContainerWidget(user, classId, classResponse, userContext, isSubscribed, socialData, card && { ...card, source: "class-details-invite" }, isSocialLeaguesSupported))
            }
            const bookingPref = (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
            // const firestoreEnabled = await LiveUtil.isFirestoreEnabled(this.diyFulfilmentService, (<any>classResponse)._id)
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const pageAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, classResponse, subscribedVideos, userContext.sessionInfo, 2, "session_detail_page_cta", bookingPref,
                this.classInviteLinkCreator, true, this.serviceInterfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, socialData, null, card && { ...card, source: "class-details-invite" }))).obj
            if (pageAction) {
                actions.push(pageAction)
            }

            widgets.push(this.getDescriptionWidget(classResponse))

            widgets.push(this.getNoteWidget())
            if (!_.isEmpty(classResponse.wodId)) {
                const wodWidget = await this.getMovementsWidget(classResponse, userContext)
                if (!_.isEmpty(wodWidget)) {
                    widgets.push(wodWidget)
                }
            }
            if (isDetailPageV2 && !(classResponse.format === "YOGA" || classResponse.format === "MEDITATION")) {
                widgets.push(this.buildBenefitsWidget())
            } else {
                widgets.push(this.getFullScreenIconDescriptionWidget(actions, userAgent))
            }
            widgets.push(this.getHowItWorksWidget())
        } else {
            widgets.push(this.getModalIconDescriptionWidget())
            widgets.push(this.getBulletInfoWidget())
        }

        return new LiveClassDetailView(widgets, actions, isFullScreen, bannerImages, false, classResponse.scheduledTimeEpoch)
    }

    async getImageOverlayCardContainerWidget(user: User, classId: string, classResponse: DigitalCatalogueEntryV1, userContext: UserContext, isSubscribed: boolean, socialData: SocialDataResponse, card?: any, isSocialLeagueSupported?: boolean): Promise<ImageOverlayCardContainerWidget> {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: classResponse.bannerImages.mobileImage
        })
        imageOverlayContainerWidget.widgets.push(this.getManageOptionsWidget(user, userContext, classId, classResponse, isSubscribed))
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: classResponse.title,
                subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "llll")
            },
            headerStyle: {
                marginLeft: 0,
                fontSize: 24,
                color: "#000000"
            },
            subTitleStyle: {
                marginLeft: 0
            }
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)

        const title = "YOUR BUDDIES IN THIS CLASS"
        if ((socialData && !_.isEmpty(socialData.attendingUsers)) || isSubscribed) {
            const cultBuddiesJoiningListLargeView: CultBuddiesJoiningListLargeView = await LiveUtil.getBuddiesJoiningListLargeView(socialData.attendingUsers, this.userCache, PageTypes.LiveClassDetail)
            const buddies = _.isEmpty(cultBuddiesJoiningListLargeView) ? undefined : cultBuddiesJoiningListLargeView.buddies
            let buddiesJoiningListWidget
            if (isSocialLeagueSupported) {
                if (!_.isEmpty(buddies)) {
                    buddiesJoiningListWidget = LiveUtil.getBuddiesInviteJoiningListWidgetV2(buddies)
                }
            } else {
                buddiesJoiningListWidget = {
                    widgetType: "BUDDIES_INVITE_JOINING_LIST_WIDGET" as IWidgetType,
                    buddies,
                    title,
                    hasDividerAbove: true,
                    inviteAction: await LiveUtil.getShareAction(userContext, this.classInviteLinkCreator, classResponse, socialData, card),
                    // seeMoreAction: {
                    //   title: "VIEW ALL",
                    //   actionType: "SHOW_WORKOUT_MEMBER_HISTORY_LIST_MODAL",
                    //   meta: {
                    //       header: {
                    //           title,
                    //       },
                    //       memberList
                    //   }
                    // }
                } as WidgetView
            }
            if (buddiesJoiningListWidget) {
                imageOverlayContainerWidget.widgets.push(buddiesJoiningListWidget)
            }
        }
        return imageOverlayContainerWidget
    }

    getManageOptionsWidget(user: User, userContext: UserContext, classId: string, classResponse: DigitalCatalogueEntryV1, isSubscribed: boolean): ManageOptionsWidgetV2 {
        const actions: Action[] = []
        let moreAction: Action = undefined
        if (isSubscribed) {
            actions.push(LiveUtil.getVideoUnsubscribeAction(user, classResponse, userContext.sessionInfo, "live_session_page_three_dot_menu"))
            moreAction = {
                actionType: "ACTION_LIST",
                actions
            }
        }
        const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(classResponse.duration / 1000)
        let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
        if (durationHourMin.hour > 0) {
            formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
        }
        let title = `LIVE | PREMIERE | ${formattedTimeString}`
        if (classResponse.isRetelecast) {
            title = `LIVE | AT HOME | ${formattedTimeString}`
        } else if (classResponse.vipClass) {
            title = `LIVE | VIP | ${formattedTimeString}`
        }
        return {
            widgetType: "MANAGE_OPTIONS_WIDGET_V2",
            title,
            moreAction
        }
    }

    getHowItWorksWidget(): ProductListWidget {
        const hiwWidget: ProductListWidget = {
            widgetType: "PRODUCT_LIST_WIDGET",
            type: "SMALL",
            header: {
                title: "How it works"
            },
            hideSepratorLines: false,
            items: [
                {
                    "subTitle": "Sign up for a Live at-home session",
                    "icon": "/image/icons/howItWorks/hitButton_1.png"
                },
                {
                    "subTitle": "Pick a comfortable location at your home for the workout",
                    "icon": "/image/icons/howItWorks/cast.png"
                },
                {
                    "subTitle": "Open app/web and join the session at least 2 minutes prior to the start time",
                    "icon": "/image/icons/howItWorks/time.png"
                },
                {
                    "subTitle": "Follow instructions to set up your mobile/laptop device",
                    "icon": "/image/icons/howItWorks/info.png"
                },
                {
                    "subTitle": "Voila! Now enjoy the awesome HOME experience",
                    "icon": "/image/icons/howItWorks/<EMAIL>"
                }
            ]
        }
        return hiwWidget
    }

    getDescriptionWidget(classResponse: DigitalCatalogueEntryV1): DescriptionWidget {
        const descriptionWidget: DescriptionWidget = {
            widgetType: "DESCRIPTION_WIDGET",
            showDivider: false,
            hasDividerBelow: false,
            descriptions: [{
                title: "About",
                subTitle: classResponse.description
                // moreIndex: 168
            }],
            containerStyle: {
                paddingTop: 0,
            },
        }
        return descriptionWidget
    }

    getNoteWidget(): DescriptionWidget {
        const infoCard: InfoCard = {
            title: "Attendance for this class",
            subTitle: "Try completing the session to mark attendance for this class. Only attended classes are added to your weekly reports and levels."
        }
        const descriptionWidget = new DescriptionWidget([infoCard])
        return descriptionWidget
    }

    private async getMovementsWidget(classResponse: DigitalCatalogueEntryV1, userContext: UserContext): Promise<LiveWodWidget> {
        let wod: SimpleWod = null
        if (classResponse.wodId) {
            wod = await this.herculesService.getSimpleWodById(classResponse.wodId)
        }
        let wods: IWODDetail
        if (!_.isNil(wod)) {
            wods = this.wodViewBuilder.getView(wod)
        }
        if (!_.isEmpty(wods)) {
            return new LiveWodWidget(wods, userContext, classResponse.duration, classResponse.calorieAttributes, false)
        }
    }

    private buildBenefitsWidget(): ProductGuranteeWidget {
        try {
            const guranteeInfo = [{
                description: "Class led by star fitness trainers",
                icon: "/image/livefit/app/fitness.png",
            },
            {
                description: "Track your performance with Energy Meter",
                icon: "/image/livefit/app/bolt.png",
            },
            {
                description: "Compete with friends in real-time",
                icon: "/image/livefit/app/group.png",
            },
            {
                description: "Get a detailed report at the end of class",
                icon: "/image/livefit/app/metrics.png",
            }]
            const guranteeWidget: ProductGuranteeWidget = {
                widgetType: "PRODUCT_GURANTEE_WIDGET",
                productType: "GYMFIT_PROMISE",
                layoutType: "GRID",
                header: {
                    title: "Benefits",
                },
                data: guranteeInfo
            }
            return guranteeWidget
        } catch (e) {
            return undefined
        }
    }

    getBulletInfoWidget(): WidgetView {
        return {
            widgetType: "BULLET_DESCRIPTION_WIDGET",
            data: [
                {
                    text: "Workout LIVE from the comfort of your home",
                },
                {
                    text: "Watch it on app, web or cast to your TV"
                }
            ]
        }
    }

    getModalIconDescriptionWidget(): IconDescriptionWidget {
        const iconDescriptionWidget: IconDescriptionWidget = {
            widgetType: "ICON_DESCRIPTION_WIDGET",
            data: [
                {
                    iconId: "LIVE_MULTICOLOR",
                    description: "Live"
                },
                {
                    iconId: "HOME_MULTICOLOR",
                    description: "At Home"
                },
                {
                    iconId: "NO_NOSHOW",
                    description: "No Equipment"
                },
                {
                    iconId: "RUN_MULTICOLOR",
                    description: "Best Trainers"
                }
            ],
            paddingTop: true
        }
        return iconDescriptionWidget
    }

    getFullScreenIconDescriptionWidget(actions: Action[], userAgent: UserAgentType): IconDescriptionWidget {

        const iconDescriptionWidget: IconDescriptionWidget = {
            widgetType: "ICON_DESCRIPTION_WIDGET",
            actions: userAgent === "DESKTOP" && actions || [],
            data: [
                {
                    iconId: "LIVE",
                    description: "Live"
                },
                {
                    iconId: "HOME",
                    description: "At Home"
                },
                {
                    iconId: "RUN",
                    description: "Best Trainers"
                },
                {
                    iconId: "GROUP",
                    description: "With Friends"
                }
            ],
            dividerType: "LARGE",
            showDivider: true
        }
        return iconDescriptionWidget
    }

    private async getShareActionWidget(userContext: UserContext, liveSession: DigitalCatalogueEntryV1, socialDataResponse: SocialDataResponse, hasTopDivider?: boolean, card?: any): Promise<ShareActionWidget> {

        const action = await LiveUtil.getShareAction(userContext, this.classInviteLinkCreator, liveSession, socialDataResponse, card)
        if (action) {
            return {
                title: "Invite buddies on WhatsApp",
                subTitle: "Have your friends join you for workout",
                action: action,
                widgetType: "SHARE_ACTION_WIDGET",
                iconUrl: "/image/icons/referral/whatsapp.png",
                hasTopDivider
            }
        }
    }
}

export default LiveClassDetailViewBuilder
