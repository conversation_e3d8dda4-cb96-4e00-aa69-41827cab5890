import * as _ from "lodash"
import { BaseWidget, TemplateWidget, UserContext } from "@curefit/vm-models"
import {
    Action,
    CultBuddiesJoiningListLargeView,
    DescriptionWidget,
    IconDescriptionWidget,
    ManageOptionsWidgetV2,
    ProductDetailPage,
    ShareActionWidget,
    WidgetView
} from "../common/views/WidgetView"
import { PAGE_ID, PageWidget } from "../page/Page"
import { inject, injectable } from "inversify"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import { DigitalCatalogueEntryV1, ImageData, LiveClass, LiveClassSlot, SocialDataResponse, } from "@curefit/diy-common"
import { BannerCarouselWidget, BannerWidget } from "../page/PageWidgets"
import { Tenant, User } from "@curefit/user-common"
import { eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import LiveUtil, { CULT_LIVE_CAMPAIGN_ID } from "../util/LiveUtil"
import { HourMin, UserAgentType } from "@curefit/base-common"
import AppUtil from "../util/AppUtil"
import { ICultBusiness, PreferenceDetail } from "./CultBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import {
    DateWiseLiveSlots,
    ExpandableSlotSelectionWidget,
    ImageOverlayCardContainerWidget,
    IWidgetType,
    LiveSlotSelectionWidget,
    PageTypes,
    SlotsInfo,
    TLClassDetailsWidget,
    TLClassSlotSelectorWidget,
    TLDescriptionWidget,
    TLImageCarouselWidget
} from "@curefit/apps-common"
import { HeaderWidget } from "@curefit/apps-common/dist/src/widgets/page/interfaces"
import WodViewBuilder, { IWODDetail } from "./WodViewBuilder"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { SimpleWod } from "@curefit/fitness-common"
import { LiveWodWidget } from "../common/views/LiveWodWidget"
import { LiveClassSlotCreator } from "./liveClassSlot/LiveClassSlotCreator"
import { UNAUTHORIZED_ERROR_CODE } from "@curefit/error-client"
import { builVanillaLiveClassViewV2 } from "../digital/VanillaLiveClassDetailViewBuilder"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { IFeedback } from "@curefit/vm-common"
import { IAppFeedback } from "../page/vm/services/AppFeedbackService"
import { ISocialService, SOCIAL_CLIENT_TYPES } from "@curefit/social-client"
import { LivePackUtil } from "../util/LivePackUtil"
import ActionUtil from "../util/ActionUtil"
import { TrialCardWidget } from "../digital/TrialCardWidget"
import { WidgetWithMetric } from "@curefit/vm-models/dist/src/models/widgets/BaseWidget"
import { ENTERPISE_CLIENT_TYPES, IEnterpriseClientService } from "@curefit/enterprise-client"
import { Corporate } from "@curefit/enterprise-common"

export class LiveClassDetailViewV2 extends ProductDetailPage {

    pageType: string
    isFullScreen: boolean
    changeBackgroundColorIndex: number
    bannerImages: ImageData
    isRetelecast?: boolean
    refreshPageEpoch: number
    classDuration: number
    isLocked: boolean
    feedback: IFeedback[]
    liveClassId: string

    constructor(widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | TrialCardWidget)[], actions: Action[], isFullScreen: boolean, bannerImages: ImageData, isRetelecast: boolean, refreshPageEpoch: number, classDuration: number, isLocked: boolean, liveClassId: string, feedback?: IFeedback[]) {
        super()
        this.widgets = widgets
        this.actions = actions
        this.bannerImages = bannerImages
        this.pageType = "liveSessionDetail"
        this.isFullScreen = isFullScreen
        this.isRetelecast = isRetelecast
        this.refreshPageEpoch = refreshPageEpoch
        this.classDuration = classDuration
        if (!this.isFullScreen) {
            this.changeBackgroundColorIndex = 1
        }
        this.isLocked = isLocked
        this.feedback = feedback
        this.liveClassId = liveClassId
    }
}

@injectable()
class LiveClassDetailViewBuilderV2 {

    constructor(
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService,
        @inject(CUREFIT_API_TYPES.WodViewBuilder) private wodViewBuilder: WodViewBuilder,
        @inject(CUREFIT_API_TYPES.LiveClassSlotCreator) protected liveClassSlotCreator: LiveClassSlotCreator,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(CUREFIT_API_TYPES.AppFeedbackService) protected userResearchAppFeedbackService: IAppFeedback,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService,
        @inject(ENTERPISE_CLIENT_TYPES.IEnterpriseClientService) private enterpriseClient: IEnterpriseClientService
    ) {

    }


    async getClassDetailWidget(liveClass: LiveClass, classId: string): Promise<TLClassDetailsWidget> {
        const { tlpHeaderImage, title, duration, intensityLevel, calorieAttributes, derivedInfo, videoCallMeta } = liveClass
        const trainers = [...(derivedInfo?.primaryTrainersInfo || []),
        ...(derivedInfo?.secondaryTrainersInfo || [])].map(trainerInfo => ({
            id: trainerInfo.instructorId,
            name: trainerInfo.name,
            image: trainerInfo.profileImageUrl,
            action: ActionUtil.trainerDetailAction(trainerInfo.instructorId)
        }))
        return {
            widgetType: "TL_CLASS_DETAILS_WIDGET",
            title,
            banner: tlpHeaderImage,
            bannerType: "IMAGE",
            trainerLinks: trainers,
            duration: LiveUtil.getFormattedTimeString(duration),
            intensity: intensityLevel,
            calories: calorieAttributes?.averageCaloriesBurnt?.toString(),
            shareAction: LiveUtil.getInviteBuddyLazyLoadAction(classId, { source: "livedetailpageshare" }),
            tags: videoCallMeta?.uiTags,
            videoProvider: videoCallMeta?.serviceProvider
        }
    }

    getClassSlotsWidget(classResponse: LiveClass, tz: Timezone, classId: string, isNoSlotSelected?: boolean): TLClassSlotSelectorWidget {
        return {
            widgetType: "TL_CLASS_SLOT_SELECTOR_WIDGET",
            slots: getDatewiseSlotInfo(classResponse, tz, classId, isNoSlotSelected, "ddd, MMM DD")
        }
    }

    getEquipment({ derivedInfo: { equipmentDetails = [] } = {} }: LiveClass): TLImageCarouselWidget {
        if (equipmentDetails.length === 0) {
            return null
        }
        return {
            widgetType: "TL_IMAGE_CAROUSEL_WIDGET",
            title: "Equipment",
            items: equipmentDetails.map(equipment => ({
                title: equipment.title,
                image: equipment.media.find(media => media.type === "IMAGE")?.url,
            }))
        }
    }

    getTargetArea({ derivedInfo: { targetAreas = [] } = {} }: LiveClass): TLImageCarouselWidget {
        if (targetAreas.length === 0) {
            return null
        }
        return {
            widgetType: "TL_IMAGE_CAROUSEL_WIDGET",
            title: "Target Area",
            items: targetAreas.map(area => ({
                title: area.title,
                image: area.media.find(media => media.type === "IMAGE")?.url,
            }))
        }
    }

    getClassDetails({ description }: LiveClass): TLDescriptionWidget {
        return {
            widgetType: "TL_DESCRIPTION_WIDGET",
            title: "Class Details",
            content: description
        }
    }


    async getTrainerLedWidgets(userContext: UserContext, classResponse: LiveClass, classId: string, socialData: SocialDataResponse, card: any): Promise<(WidgetView)[]> {
        const tz = userContext.userProfile.timezone
        const widgets: (WidgetView)[] = []
        widgets.push(await this.getClassDetailWidget(classResponse, classId))
        widgets.push(this.getClassSlotsWidget(classResponse, tz, classId))
        widgets.push(this.getClassDetails(classResponse))
        widgets.push(this.getEquipment(classResponse))
        widgets.push(this.getTargetArea(classResponse))
        return widgets
    }

    async getDetailWidgets(userContext: UserContext, user: User, classId: string, inviteOfferSupported: boolean, selectedClassResponse: DigitalCatalogueEntryV1, isDetailPageV2: boolean, isSubscribed: boolean, isLocked: boolean, isNoSlotSelected: boolean, socialData: SocialDataResponse, classResponse: LiveClass, card: any): Promise<(BaseWidget | WidgetView | PageWidget | TemplateWidget)[]> {
        const tz = userContext.userProfile.timezone
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | WidgetWithMetric)[] = []
        if (isDetailPageV2) {
            widgets.push(await this.getImageOverlayCardContainerWidget(user, classId, classResponse, selectedClassResponse, userContext, isSubscribed, socialData, tz, isLocked, card && { ...card, source: "class-details-invite" }, isNoSlotSelected))
        }
        if (!AppUtil.isInternationalApp(userContext) && !AppUtil.isMWeb(userContext)) {
            const bannerWidgets = await this.serviceInterfaces.widgetBuilder.buildWidgets(["cf4a54fd-73b6-4dd3-9a52-4c47d090bf1e32"], this.serviceInterfaces, userContext, undefined, undefined)
            if (!_.isEmpty(bannerWidgets) && !_.isEmpty(bannerWidgets.widgets)) {
                widgets.push(...bannerWidgets.widgets)
            }
        }
        widgets.push(this.getDescriptionWidget(classResponse))
        return widgets
    }


    async getDescriptionWidgets(userContext: UserContext, isDetailPageV2: boolean, selectedTimeSlot: LiveClassSlot, actions: Action[], isUserEligibleForMonetisation: boolean, classResponse: LiveClass): Promise<(BaseWidget | WidgetView | PageWidget | TemplateWidget)[]> {
        const isWeb = AppUtil.isWeb(userContext)
        const userAgent: UserAgentType = _.get(userContext, "sessionInfo.userAgent", "APP")
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget)[] = []
        // Show become a member widget if the session is locked
        if (isUserEligibleForMonetisation) {
            let showBenefitsWidget: boolean = false
            if (selectedTimeSlot && selectedTimeSlot.locked) {
                showBenefitsWidget = true
            }
            else {
                const segments: string[] = await this.serviceInterfaces.clsUtil.getPlatformSegments()
                if (!_.isEmpty(segments) && (segments.includes("cflive-free-trial-started") || segments.includes("cflive-subscription-to-expire-15-days"))) {
                    showBenefitsWidget = true
                }
            }
            if (showBenefitsWidget && !AppUtil.isInternationalApp(userContext)) {
                const packBenefitsWidget = this.getLivePackBenefitsWidget(isWeb)
                widgets.push(packBenefitsWidget)
            }
        }

        if (!_.isEmpty(classResponse.wodId)) {
            const wodWidget = await this.getMovementsWidget(classResponse, userContext)
            if (!_.isEmpty(wodWidget)) {
                widgets.push(wodWidget)
            }
        }
        if (!isDetailPageV2 || classResponse.format === "YOGA" || classResponse.format === "MEDITATION") {
            widgets.push(AppUtil.isSugarFitOrUltraFitApp(userContext) ? this.getFullScreenSFIconDescriptionWidget(actions, userAgent, classResponse) : this.getFullScreenIconDescriptionWidget(actions, userAgent))
        }

        return widgets
    }

    async getTrainerFormatLinkWidget(liveClassId: string, classResponse: LiveClass, userContext: UserContext) {
        const advancedCatalogue = await this.diyFulfilmentService.getAdvancedDigitalCatalogue(liveClassId)
        const widgets: (TrialCardWidget)[] = []
        const primaryTrainers = advancedCatalogue.primaryTrainers
        if (!_.isEmpty(primaryTrainers)) {
            for (let i = 0; i < primaryTrainers.length; i++) {
                const trainerId = primaryTrainers[i]
                const trainer = await this.diyFulfilmentService.getTrainerById(trainerId)
                const trainerTitle = "More from " + trainer.name.split(" ")[0]
                const trainerSubtitle = LiveUtil.getFormatText(classResponse.format) + " Trainer"
                if (trainer.otherDetails && trainer.otherDetails.isPopular) {
                    const widgetObj: TrialCardWidget = {
                        widgetType: "TRIAL_CARD_WIDGET",
                        title: trainerTitle,
                        subtitle: trainerSubtitle,
                        image: trainer.profileImageUrl,
                        action: ActionUtil.getTrainerPageNavigationAction(trainerId),
                        titleStyle: {
                            paddingTop: 5,
                        },
                    }
                    if (AppUtil.isEnterpriseCLPSupported(userContext)) {
                        const corp: Corporate = await this.enterpriseClient.getCorporateByUserId(userContext.userProfile.userId)
                        if (corp) {
                            widgetObj.companyLogoUrl = corp.logoUrl
                            widgetObj.sponsoredByCaption = "sponsored by"
                            widgetObj.companyName = corp.name
                        }
                    }
                    widgets.push(widgetObj)
                }
            }
        }
        if (!_.isEmpty(classResponse.format) && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            const format = await this.diyFulfilmentService.getFormatById(classResponse.format)
            const formatTitle = "More in " + LiveUtil.getFormatText(classResponse.format)
            const widgetObj: TrialCardWidget = {
                widgetType: "TRIAL_CARD_WIDGET",
                title: formatTitle,
                subtitle: "By All Trainers",
                image: format.imageUrl,
                action: ActionUtil.getFormatPageNavigationAction(classResponse.format.toString()),
                titleStyle: {
                    paddingTop: 5,
                }
            }
            if (AppUtil.isEnterpriseCLPSupported(userContext)) {
                const corp: Corporate = await this.enterpriseClient.getCorporateByUserId(userContext.userProfile.userId)
                if (corp) {
                    widgetObj.companyLogoUrl = corp.logoUrl
                    widgetObj.sponsoredByCaption = "sponsored by"
                    widgetObj.companyName = corp.name
                }
            }
            widgets.push(widgetObj)
        }
        return widgets
    }


    async buildView(userContext: UserContext, category: string, liveClassId: string, selectedClassId: string, isFullScreen: boolean, blockIfInternationalUser: boolean, nodeRelationId?: number, isSuccessiveClassCall?: boolean, isNoSlotSelected?: boolean): Promise<LiveClassDetailViewV2> {
        const userAgent: UserAgentType = _.get(userContext, "sessionInfo.userAgent", "APP")
        const widgets: (BaseWidget | WidgetView | PageWidget | TemplateWidget | TrialCardWidget)[] = []
        const actions: Action[] = []
        const userId = userContext.userProfile.userId
        const countryId = AppUtil.getCountryId(userContext)
        let classResponse: LiveClass = null
        if (isSuccessiveClassCall) {
            classResponse = await this.liveClassSlotCreator.getLiveCLassTimeSlot(liveClassId, userId)
        }
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
        if (!classResponse) {
            try {
                if (liveClassId === "undefined" && AppUtil.isWeb(userContext)) {
                    classResponse = await this.diyFulfilmentService.getStaticClassForCategory(category, userId, tenant, countryId)
                } else {
                    classResponse = await this.diyFulfilmentService.getLiveClass(liveClassId, userId, tenant, countryId)
                }
                await this.liveClassSlotCreator.createLiveCLassTimeSlot(classResponse.liveClassId, classResponse, userId)
            } catch (err) {
                if (err.statusCode && err.statusCode === UNAUTHORIZED_ERROR_CODE) {
                    widgets.push(new BannerWidget([{
                        id: "nora_class_error_banner",
                        image: "/image/livefit/app/no_nora_app_banner.png",
                        desktopWebImage: "/image/livefit/app/no_nora_desktop_banner.png",
                        action: `curefit://liveclassdetail?bookingNumber=5e958a36c5392200af7f5e59&productType=LIVE_FITNESS`
                    }], userAgent !== "DESKTOP" ? "1110:2430" : "2149:1259"))
                    return new LiveClassDetailViewV2(widgets, actions, isFullScreen, undefined, false, undefined, undefined, false, "5e958a36c5392200af7f5e59")
                }
            }
        }
        const classId = (selectedClassId !== "undefined" && !_.isEmpty(selectedClassId)) ? selectedClassId : (classResponse && classResponse.slots && classResponse.slots.length > 0) ? classResponse.slots[0].classId : undefined
        let selectedTimeSlot = undefined
        if (classResponse && classResponse.slots && classResponse.slots.length > 0) {
            selectedTimeSlot = _.find(classResponse.slots, slot => slot.classId === classId)
        }

        let selectedClassResponse: DigitalCatalogueEntryV1 = null
        if (!isNoSlotSelected && !_.isEmpty(classId)) {
            selectedClassResponse = await this.diyFulfilmentService.getDigitalCatalogueEntry(classId)
        }
        const subscribedVideos: string[] = await this.diyFulfilmentService.getSubscribedVideos(userContext.userProfile.userId)
        const cultPreference: PreferenceDetail = (await eternalPromise(this.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
        const user: User = await userContext.userPromise
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
        const isSubscribed = selectedClassResponse ? _.includes(subscribedVideos, (<any>selectedClassResponse)._id) : false
        if (_.isEmpty(classResponse)) {
            widgets.push(new BannerWidget([{
                id: "nora_class_error_banner",
                image: "/image/livefit/app/no_nora_app_banner.png",
                desktopWebImage: "/image/livefit/app/no_nora_desktop_banner.png",
                action: `curefit://liveclassdetail?bookingNumber=5e958a36c5392200af7f5e59&productType=LIVE_FITNESS`
            }], userAgent !== "DESKTOP" ? "1110:2430" : "2149:1259"))
            return new LiveClassDetailViewV2(widgets, actions, isFullScreen, undefined, false, undefined, undefined, false, "5e958a36c5392200af7f5e59")
        }
        const bannerImages = classResponse.bannerImages
        const isDetailPageV2 = AppUtil.isLiveClassDetailPageV2(userContext.sessionInfo, user, userContext, true)
        const isLocked = selectedTimeSlot ? AppUtil.isLiveContentLocked(userContext, selectedTimeSlot.locked, isUserEligibleForMonetisation, isUserEligibleForTrial) : false

        const { supported, card } = await this.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)

        // Deprecated
        if (LiveUtil.isVanillaLiveFitFormat(classResponse.format)) {
            return await builVanillaLiveClassViewV2(classResponse, subscribedVideos, cultPreference, userContext, classId, this.classInviteLinkCreator, this.serviceInterfaces, selectedClassResponse, isNoSlotSelected, isLocked, isUserEligibleForMonetisation)
        }

        if (!isDetailPageV2) {
            widgets.push({
                widgetType: "HEADER_WIDGET",
                widgetTitle: {
                    title: classResponse.title,
                    subTitle: TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "EEE, LLL d, h:mm aaa") + " | AT HOME" // "Mon 6 May, 5:00 PM | AT HOME"
                },
                showDivider: false,
                hasDividerBelow: false
            })
        }

        if (isFullScreen) {
            let socialData: SocialDataResponse

            if (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) {
                this.logger.info(`Adding social data for userId: ${userContext.userProfile.userId}, classId: ${classId}, nodeRelationId: ${nodeRelationId}`)
                if (nodeRelationId) {
                    await this.diyFulfilmentService.addSocialNode(userContext.userProfile.userId, classId, nodeRelationId)
                }
                const socialDataEternalPromise = eternalPromise(this.diyFulfilmentService.getSocialDataForSession(userContext.userProfile.userId, classId, !AppUtil.isLiveLazyInviteLinkActionSupported(userContext), tenant))
                if (socialDataEternalPromise) {
                    socialData = (await socialDataEternalPromise).obj
                    // if (socialData && !_.isEmpty(socialData.attendingUsers) && !isDetailPageV2) {
                    //     const buddiesJoiningSmallListWidget: WidgetView = {
                    //         widgetType: "CULT_BUDDIES_JOINING_LIST_LARGE_WIDGET" as IWidgetType,
                    //         ...(await LiveUtil.getBuddiesJoiningListLargeView(socialData.attendingUsers, this.userCache, PageTypes.LiveClassDetail))
                    //     }
                    //     widgets.push(buddiesJoiningSmallListWidget)
                    // }
                }
            }
            // slotsInfo = this.getTimeSlotsInfo(classResponse, tz)
            // if (isSubscribed && !isDetailPageV2) {
            //     widgets.push(await this.getShareActionWidget(userContext, classResponse, socialData, null, card && { ...card, source: "class-details-invite" }))
            // }

            const bookingPref = (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
            let pageAction: Action = null
            if (isNoSlotSelected) {
                pageAction = {
                    title: "PICK A SLOT",
                    actionType: "NAVIGATION",
                    disabled: true
                }
            } else if (classResponse.slots.length > 0) {
                pageAction = (await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, selectedClassResponse, subscribedVideos, userContext.sessionInfo, 2, "session_detail_page_cta",
                    bookingPref, this.classInviteLinkCreator, isLocked, this.serviceInterfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, socialData, null,
                    card && { ...card, source: "class-details-invite" }, isSubscribed, false, this.announcementBusiness))).obj
            } else {
                pageAction = { actionType: "POP_ACTION", title: "EXPLORE MORE" }
            }

            if (pageAction) {
                const isZoomClass = LiveUtil.isInteractiveSession(selectedClassResponse?.preferredStreamType) && selectedClassResponse?.videoCallMeta?.serviceProvider === "ZOOM"
                if (AppUtil.isInternationalApp(userContext) && isSubscribed && isZoomClass && !LiveUtil.isLive(userContext, selectedClassResponse)) {
                    const zoomSendLinkAction = LiveUtil.getSendZoomLinkAction(classResponse, "SEND ZOOM LINK", "livedetailpageshare")
                    zoomSendLinkAction.style = { flex: 1, marginRight: 10, minWidth: 0 }
                    pageAction.style = { flex: 0, minWidth: 0 }
                    actions.push(zoomSendLinkAction)
                }
                actions.push(pageAction)
            }

            if (AppUtil.isInternationalTLApp(userContext)) {
                widgets.push(...(await this.getTrainerLedWidgets(userContext, classResponse, classId, socialData, card)))
            } else {
                widgets.push(...(await this.getDetailWidgets(userContext, user, classId, supported, selectedClassResponse, isDetailPageV2, isSubscribed, isLocked, isNoSlotSelected, socialData, classResponse, card)))
                if (classResponse.format === "MIND_PODCAST") {
                    let playerStartTimeEpoch
                    if (selectedClassResponse) {
                        playerStartTimeEpoch = selectedClassResponse.playerStartTimeEpoch
                    }
                    const liveClassDetailViewV2 = new LiveClassDetailViewV2(widgets, actions, isFullScreen, bannerImages, false, playerStartTimeEpoch, classResponse.duration, isLocked, classResponse.liveClassId)
                    return liveClassDetailViewV2
                }
                widgets.push(...(await this.getDescriptionWidgets(userContext, isDetailPageV2, selectedTimeSlot, actions, isUserEligibleForMonetisation, classResponse)))
                if (AppUtil.isTrialCardWidgetSupported(userContext)) {
                    widgets.push(...(await this.getTrainerFormatLinkWidget(liveClassId, classResponse, userContext)))
                }
            }
        } else {
            widgets.push(this.getModalIconDescriptionWidget())
            widgets.push(this.getBulletInfoWidget())
        }

        let playerStartTimeEpoch
        if (selectedClassResponse) {
            playerStartTimeEpoch = selectedClassResponse.playerStartTimeEpoch
        }
        const feedbackConfig: IFeedback[] = await this.userResearchAppFeedbackService.getAppFeedbackForPage(userContext, PAGE_ID.LIVE_CLASS_DETAIL_VIEW_V2)
        const liveClassDetailViewV2 = new LiveClassDetailViewV2(widgets, actions, isFullScreen, bannerImages, false, playerStartTimeEpoch, classResponse.duration, isLocked, classResponse.liveClassId, feedbackConfig)
        return liveClassDetailViewV2
    }

    async getImageOverlayCardContainerWidget(user: User, classId: string, classResponse: LiveClass, selectedClassResponse: DigitalCatalogueEntryV1, userContext: UserContext, isSubscribed: boolean, socialData: SocialDataResponse, tz: Timezone, isLocked: boolean, card?: any, isNoSlotSelected?: boolean): Promise<ImageOverlayCardContainerWidget> {
        const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
        imageOverlayContainerWidget.assets.push({
            assetType: "IMAGE",
            assetUrl: classResponse.bannerImages?.mobileImage
        })
        imageOverlayContainerWidget.widgets.push(this.getManageOptionsWidget(user, userContext, classId, selectedClassResponse, isSubscribed, classResponse, isLocked, isNoSlotSelected))
        const subTitle = selectedClassResponse ? TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, selectedClassResponse.scheduledTimeEpoch, "llll") : TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, classResponse.scheduledTimeEpoch, "ddd, MMM Do, YYYY")
        let intensity = ``
        if (!_.isEmpty(classResponse.intensityLevel)) {
            intensity = classResponse.intensityLevel.charAt(0).toUpperCase() + classResponse.intensityLevel.slice(1).toLowerCase()
        }
        const headerWidget: HeaderWidget = {
            widgetType: "HEADER_WIDGET",
            widgetTitle: {
                title: classResponse.title,
                subTitle: subTitle
            },
            headerStyle: {
                marginLeft: 0,
                fontSize: 22,
                color: "#000000",
                width: classResponse.isMasterClass ? "90%" : "100%"
            },
            subTitleStyle: {
                marginLeft: 0,
                fontSize: 14
            },
            subHeader: {
                text: classResponse.isMasterClass ? `${intensity}` : (_.isEmpty(intensity) ? `${classResponse.trainerName}` : AppUtil.isSugarFitOrUltraFitApp(userContext) ? AppUtil.getSugarfitEventTitle(classResponse) : `${classResponse.trainerName} • ${intensity}`)
            },
            tagIcon: classResponse.isMasterClass ? "MASTERCLASS_TAG" : undefined
        }
        imageOverlayContainerWidget.widgets.push(headerWidget)
           if (!AppUtil.isSugarFitOrUltraFitApp(userContext)) {
               const buddiesJoiningListWidget = await getBuddiesInfo(this.cultBusiness, userContext, selectedClassResponse, classId, socialData, isSubscribed, this.userCache, this.logger, this.classInviteLinkCreator)
               if (!_.isEmpty(buddiesJoiningListWidget)) {
                   imageOverlayContainerWidget.widgets.push(buddiesJoiningListWidget)
               }
           }
        const slotsInfo = this.getTimeSlotsInfo(classResponse, tz, classId, isNoSlotSelected)
        if (AppUtil.isDateWiseLiveSlotWidgetSupported(userContext)) {
            const liveSlotSelectionWidget: LiveSlotSelectionWidget = {
                widgetType: "LIVE_SLOT_SELECTION_WIDGET",
                datesAvailable: getDatewiseSlotInfo(classResponse, tz, classId, isNoSlotSelected)
            }
            imageOverlayContainerWidget.widgets.push(liveSlotSelectionWidget)
        } else {
            const expandableSlotSelectionWidget: ExpandableSlotSelectionWidget = {
                widgetType: "EXPANDABLE_SLOT_SELECTION_WIDGET",
                title: slotsInfo.length > 0 ? "SLOTS AVAILABLE" : "NO SLOTS AVAILABLE",
                slotsInfo: slotsInfo
            }
            imageOverlayContainerWidget.widgets.push(expandableSlotSelectionWidget)
        }

        return imageOverlayContainerWidget
    }

    getTimeSlotsInfo(classResponse: LiveClass, tz: Timezone, classId: string, isNoSlotSelected?: boolean): SlotsInfo[] {
        return classResponse.slots.map(slot => {
            const isSelected = isNoSlotSelected ? false : slot.classId === classId
            const isScheduled = slot.subscriptionStatus === "SUBSCRIBED"
            return {
                time: TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch, "hh:mm A"),
                classId: slot.classId,
                status: slot.status,
                subscriptionStatus: slot.subscriptionStatus,
                isSelected: isSelected,
                isScheduled: isScheduled,
                isLive: slot.isPremier ? false : slot.status === "LIVE",
                icon: slot.isPremier ? (isScheduled || isSelected) ? "/image/livefit/app/star_white.png" : "/image/livefit/app/star_blue.png" : undefined
            }
        })
    }

    getManageOptionsWidget(user: User, userContext: UserContext, classId: string, selectedClassResponse: DigitalCatalogueEntryV1, isSubscribed: boolean, classResponse: LiveClass, isLocked: boolean, isNoSlotSelected?: boolean): ManageOptionsWidgetV2 {
        const actions: Action[] = []
        let moreAction: Action = undefined
        let shouldShowManagedOptions = true
        if (AppUtil.isSugarFitApp(userContext) && !_.isEmpty(classResponse.tags)) {
            const masterClassTagIndex = _.findIndex(classResponse.tags, tag => tag.includes("SF-MASTER-CLASS"))
            if (masterClassTagIndex > -1) {
                shouldShowManagedOptions = false
            }
        }
        if (isSubscribed && selectedClassResponse && shouldShowManagedOptions) {
            actions.push(LiveUtil.getVideoUnsubscribeAction(user, selectedClassResponse, userContext.sessionInfo, "live_session_page_three_dot_menu"))
            moreAction = {
                actionType: "ACTION_LIST",
                actions
            }
        }

        const durationHourMin: HourMin = TimeUtil.convertDurationSecondsToHourMin(classResponse.duration / 1000)
        let formattedTimeString: string = durationHourMin.min > 0 ? durationHourMin.min + " Min" : ""
        if (durationHourMin.hour > 0) {
            formattedTimeString = durationHourMin.hour + " Hr " + formattedTimeString
        }
        const isRetelecast = (selectedClassResponse && selectedClassResponse.isRetelecast) ? true : false
        const isPremiere = !isNoSlotSelected && !isRetelecast && classId
        let title, style, icon
        if (LiveUtil.isConsideredInteractiveSessionForPresentation(selectedClassResponse?.preferredStreamType)) {
            title = `LIVE • INTERACTIVE • ${formattedTimeString}`
            style = { backgroundColor: "#4ab74a" }
        } else if (isPremiere) {
            title = `LIVE • PREMIERE • ${formattedTimeString}`
            style = { backgroundColor: "#3888ff" }
            icon = "/image/livefit/app/star_white.png"
        } else if (selectedClassResponse?.vipClass && !isRetelecast) {
            title = `LIVE • VIP • ${formattedTimeString}`
        } else {
            title = `LIVE • AT HOME • ${formattedTimeString}`
        }
        const isSugarFitOrUltraFitApp = AppUtil.isSugarFitOrUltraFitApp(userContext)
        return {
            widgetType: "MANAGE_OPTIONS_WIDGET_V2",
            title,
            moreAction,
            style,
            icon,
            headerAction: !isSugarFitOrUltraFitApp ? LiveUtil.getInviteBuddyLazyLoadAction(classId, { source: "livedetailpageshare" }) : undefined,
            headerActionIcon: !isSugarFitOrUltraFitApp ? "SHARE_ICON" : undefined,
            isLocked,
            tags: selectedClassResponse?.tags
        }
    }

    getDescriptionWidget(classResponse: DigitalCatalogueEntryV1): DescriptionWidget {
        const descriptionWidget: DescriptionWidget = {
            widgetType: "DESCRIPTION_WIDGET",
            showDivider: false,
            hasDividerBelow: false,
            descriptions: [{
                title: "About",
                subTitle: classResponse.description
                // moreIndex: 168
            }],
            containerStyle: {
                paddingTop: 0,
            },
        }
        return descriptionWidget
    }

    private async getMovementsWidget(classResponse: DigitalCatalogueEntryV1, userContext: UserContext): Promise<LiveWodWidget> {
        let wod: SimpleWod = null
        if (classResponse.wodId) {
            wod = await this.herculesService.getSimpleWodById(classResponse.wodId)
        }
        let wods: IWODDetail
        if (!_.isNil(wod)) {
            wods = this.wodViewBuilder.getView(wod)
        }
        if (!_.isEmpty(wods)) {
            return new LiveWodWidget(wods, userContext, classResponse.duration, classResponse.calorieAttributes, false)
        }
    }

    getBulletInfoWidget(): WidgetView {
        return {
            widgetType: "BULLET_DESCRIPTION_WIDGET",
            data: [
                {
                    text: "Workout LIVE from the comfort of your home",
                },
                {
                    text: "Watch it on app, web or cast to your TV"
                }
            ]
        }
    }

    getModalIconDescriptionWidget(): IconDescriptionWidget {
        const iconDescriptionWidget: IconDescriptionWidget = {
            widgetType: "ICON_DESCRIPTION_WIDGET",
            data: [
                {
                    iconId: "LIVE_MULTICOLOR",
                    description: "Live"
                },
                {
                    iconId: "HOME_MULTICOLOR",
                    description: "At Home"
                },
                {
                    iconId: "NO_NOSHOW",
                    description: "No Equipment"
                },
                {
                    iconId: "RUN_MULTICOLOR",
                    description: "Best Trainers"
                }
            ],
            paddingTop: true
        }
        return iconDescriptionWidget
    }

    getFullScreenIconDescriptionWidget(actions: Action[], userAgent: UserAgentType): IconDescriptionWidget {

        const iconDescriptionWidget: IconDescriptionWidget = {
            widgetType: "ICON_DESCRIPTION_WIDGET",
            actions: [],
            data: [
                {
                    iconId: "LIVE",
                    description: "Live"
                },
                {
                    iconId: "HOME",
                    description: "At Home"
                },
                {
                    iconId: "RUN",
                    description: "Best Trainers"
                },
                {
                    iconId: "GROUP",
                    description: "With Friends"
                }
            ],
            dividerType: "LARGE",
            showDivider: true
        }
        return iconDescriptionWidget
    }

    getFullScreenSFIconDescriptionWidget(actions: Action[], userAgent: UserAgentType, classResponse: LiveClass): IconDescriptionWidget {

        let data = [
            {
                iconId: "LIVE",
                description: "Live"
            },
            {
                iconId: "HOME",
                description: "At Home"
            },
            {
                iconId: "RUN",
                description: "Best Trainers"
            },
            {
                iconId: "GROUP",
                description: "With Friends"
            }
        ]

        if (classResponse && classResponse?.tags?.includes("WEBINAR")) {
            data = data = [
                {
                    iconId: "WEBINAR_LIVE",
                    description: "Live"
                },
                {
                    iconId: "APP_DEMO",
                    description: "App Demo"
                },
                {
                    iconId: "AT_HOME",
                    description: "At Home"
                },
                {
                    iconId: "DOCTOR_SPEAK",
                    description: "Dr. Speak"
                }
            ]
        }
        else if (classResponse && classResponse?.tags?.includes("EVENTS&TALK")) {
            data = [
                {
                    iconId: "WEBINAR_LIVE",
                    description: "Live"
                },
                {
                    iconId: "APP_DEMO",
                    description: "App Demo"
                },
                {
                    iconId: "AT_HOME",
                    description: "At Home"
                },
                {
                    iconId: "USER_SPEAK",
                    description: "User Speak"
                }
            ]
        }

        const iconDescriptionWidget: IconDescriptionWidget = {
            widgetType: "ICON_DESCRIPTION_WIDGET",
            actions: [],
            data: data,
            dividerType: "LARGE",
            showDivider: true
        }
        return iconDescriptionWidget
    }
    private getLivePackBenefitsWidget(isWeb: boolean) {
        const action: Action = {
            actionType: "NAVIGATION",
            title: "BECOME A MEMBER",
            subtitle: "STARTS at 249/MONTH",
            url: "curefit://livefitnessbrowsepage",
            analyticsData: {
                eventKey: "widget_click",
                eventData: {
                    widgetName: "Become A Member",
                    pageFrom: "liveSessionDetail"
                }
            }
        }
        const widget = new BannerCarouselWidget("375:340", [
            {
                id: "benefit_1",
                image: isWeb ? "/image/livefit/app/pack_benefits_1.png" : "/image/livefit/app/live_pack_benefit_1_v4.png",
                action: action
            },
            {
                id: "benefit_2",
                image: isWeb ? "/image/livefit/app/live_pack_benefits_web_2.png" : "/image/livefit/app/live_pack_benefit_2_v4.png",
                action: action
            },
            {
                id: "benefit_3",
                image: isWeb ? "/image/livefit/app/live_pack_benefits_web_3.png" : "/image/livefit/app/live_pack_benefit_3_v4.png",
                action: action
            },
        ], {
            showPagination: true,
            v2: true,
            alignment: "center",
            backgroundColor: "",
            autoScroll: false,
            enableSnap: false,
            useShadow: false,
            roundedCorners: true,
            noVerticalPadding: true,
            edgeToEdge: true,
            interContentSpacing: 0,
            bannerOriginalWidth: 505,
            bannerOriginalHeight: 405,
            bannerWidth: 375,
            bannerHeight: 340,
        }, 3, true, false, false, undefined, action)
        widget.widgetType = "LIVE_BENEFITS_CAROUSEL_WIDGET"
        widget.isFullWidthDesktop = false
        return widget
    }

    private async getShareActionWidget(userContext: UserContext, liveSession: DigitalCatalogueEntryV1, socialDataResponse: SocialDataResponse, hasTopDivider?: boolean, card?: any): Promise<ShareActionWidget> {

        const action = await LiveUtil.getShareAction(userContext, this.classInviteLinkCreator, liveSession, socialDataResponse, card)
        if (action) {
            return {
                title: "Invite buddies on WhatsApp",
                subTitle: "Have your friends join you for workout",
                action: action,
                widgetType: "SHARE_ACTION_WIDGET",
                iconUrl: "/image/icons/referral/whatsapp.png",
                hasTopDivider
            }
        }
    }
}

export default LiveClassDetailViewBuilderV2

export function getDatewiseSlotInfo(classResponse: LiveClass, tz: Timezone, classId: string, isNoSlotSelected?: boolean, dateFormat?: string): DateWiseLiveSlots[] {
    const dateWiseLiveSlots: DateWiseLiveSlots[] = []
    const dateWiseSlotMap: { [id: string]: SlotsInfo[] } = {}

    classResponse.slots.map(slot => {
        const isSelected = isNoSlotSelected ? false : slot.classId === classId
        const isScheduled = slot.subscriptionStatus === "SUBSCRIBED"
        const slotInfo: SlotsInfo = {
            time: TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch, "hh:mm A"),
            classId: slot.classId,
            status: slot.status,
            subscriptionStatus: slot.subscriptionStatus,
            isSelected: isSelected,
            isScheduled: isScheduled,
            isLive: slot.isPremier ? false : slot.status === "LIVE",
            icon: slot.isPremier ? (isScheduled || isSelected) ? "/image/livefit/app/star_white.png" : "/image/livefit/app/star_blue.png" : undefined
        }
        const slotDate = TimeUtil.formatEpochInTimeZone(tz, slot.scheduledTimeEpoch, dateFormat)
        if (dateWiseSlotMap[slotDate] === undefined) {
            dateWiseSlotMap[slotDate] = []
        }
        dateWiseSlotMap[slotDate].push(slotInfo)
    })

    Object.keys(dateWiseSlotMap).forEach(date => {
        dateWiseLiveSlots.push({
            date,
            timeSlots: dateWiseSlotMap[date],
            noSlotsAvailable: dateWiseSlotMap[date].length === 0,
            noSlotText: "Sorry, no slots available today!",
        })
    })

    return dateWiseLiveSlots
}

export async function getBuddiesInfo(cultBusiness: ICultBusiness, userContext: UserContext, selectedClassResponse: DigitalCatalogueEntryV1, classId: string, socialData: SocialDataResponse, isSubscribed: boolean, userCache: CacheHelper, logger: Logger, classInviteLinkCreator: ClassInviteLinkCreator): Promise<any> {
    const { card } = await cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)
    const title = "YOUR BUDDIES IN THIS CLASS"
    if (selectedClassResponse && ((socialData && !_.isEmpty(socialData.attendingUsers)) || isSubscribed)) {
        const cultBuddiesJoiningListLargeView: CultBuddiesJoiningListLargeView = await LiveUtil.getBuddiesJoiningListLargeView(socialData.attendingUsers, userCache, PageTypes.LiveClassDetail)
        const buddies = _.isEmpty(cultBuddiesJoiningListLargeView) ? undefined : cultBuddiesJoiningListLargeView.buddies
        let buddiesJoiningListWidget
        if (await AppUtil.isSocialLeaguesSupported(userContext, "LIVE")) {
            if (!_.isEmpty(buddies)) {
                buddiesJoiningListWidget = LiveUtil.getBuddiesInviteJoiningListWidgetV2(buddies)
            }
        } else {
            buddiesJoiningListWidget = {
                widgetType: "BUDDIES_INVITE_JOINING_LIST_WIDGET" as IWidgetType,
                buddies,
                title,
                hasDividerAbove: true,
                inviteAction: await LiveUtil.getShareAction(userContext, classInviteLinkCreator, selectedClassResponse, socialData, card),
            } as WidgetView
        }
        return buddiesJoiningListWidget
    }

    return null
}
