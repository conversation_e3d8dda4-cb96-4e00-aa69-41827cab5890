import { <PERSON><PERSON>B<PERSON>ing, CultCenter, CultMembership } from "@curefit/cult-common"
import { CustomerIssueType, IssueCategory } from "@curefit/issue-common"
import { ProductType } from "@curefit/product-common"
import { Feedback } from "@curefit/feedback-common"
import {
    ActionCard,
    CalloutWidget,
    DescriptionWidget,
    InfoCard,
    ManageOptionPayload,
    ManageOptions,
    ManageOptionsWidget,
    PackInfoWidget,
    ProductDetailPage,
    ProductFeedbackWidget,
    WidgetView,
    ActionCardWidget,
    QRCodeWidget
} from "../common/views/WidgetView"
import CenterView from "./CenterView"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import FeedbackPageConfigV2Cache from "../ugc/FeedbackPageConfigV2Cache"
import { inject, injectable } from "inversify"
import IProductBusiness from "../product/IProductBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import CultUtil, { CULTSCORE_WORKOUT_IDS } from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import { ActionUtil } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import CatalogueServiceUtilities from "../util/CatalogueServiceUtilities"

@injectable()
class BookingDetailViewBuilder {

    constructor(
        @inject(CUREFIT_API_TYPES.ProductBusiness) private productBusiness: IProductBusiness,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) protected membershipService: IMembershipService) {
    }

    async getView(userContext: UserContext, productType: ProductType, booking: CultBooking, issuesMap: Map<string, CustomerIssueType[]>,
        feedback?: Feedback, feedbackPageConfigV2Cache?: FeedbackPageConfigV2Cache): Promise<ProductDetailPage> {
        const productDetailPage = new ProductDetailPage()
        const date = TimeUtil.parseDateTime(booking.Class.date + " " + booking.Class.startTime, userContext.userProfile.timezone)
        const classStartTimeInEpoch = date.getTime()
        productDetailPage.refreshTimeEpoch = classStartTimeInEpoch - (booking.qrCode.lowerLimitForQRCode * 60 * 1000)
        productDetailPage.widgets.push(await this.getSummaryWidget(userContext, productType, booking, issuesMap))
        if (!_.isEmpty(booking.workoutCell)) {
            productDetailPage.widgets.push(this.getWorkoutStationWidget(booking.workoutCell))
        }
        if (booking.qrCode.qrString) {
            productDetailPage.widgets.push(this.getQRCodeWidget(booking))
        }
        const cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, booking, issuesMap, true, false)
        const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled
        })
        const tz = userContext.userProfile.timezone


        const startTime = TimeUtil.getMomentForDateString(booking.Class.date + " " + booking.Class.startTime, tz, "YYYY-MM-DD hh:mm:ss")
        const isClassStarted = TimeUtil.getMomentNow(tz).isAfter(startTime)

        if (!booking.isBookingCancellable && !isClassStarted) {
            productDetailPage.widgets.push(new CalloutWidget(`Cancel class 60 mins before start`))
        }

        if (!isClassStarted) {
            const cancelClassWidget = this.getManageOptionsWidget(cultManageOptions.manageOptions, cultManageOptions.meta)
            cancelClassWidget.isDisabled = enabledOptions.length > 0 ? false : true
            productDetailPage.widgets.push(cancelClassWidget)
        }
        else if (CULTSCORE_WORKOUT_IDS.indexOf(booking.Class.Workout.id) > -1) {
            const logCultScoreWidget = this.getManageOptionsWidget(cultManageOptions.manageOptions, cultManageOptions.meta)
            logCultScoreWidget.isDisabled = enabledOptions.length > 0 ? false : true
            productDetailPage.widgets.push(logCultScoreWidget)
        }

        if (!_.isEmpty(booking.CultClass.Workout.preWorkoutGears)) {
            const workoutKitWidget = CultUtil.getWorkoutKitWidget(booking.CultClass.Workout.preWorkoutGears, null, booking.CultClass.date, booking.Center.id)
            if (!_.isNil(workoutKitWidget)) {
                productDetailPage.widgets.push(workoutKitWidget)
            }
        }
        productDetailPage.widgets.push(CultUtil.getImportantUpdatesWidget(userContext, booking.Class.Workout.isSportCategory, undefined, undefined, undefined, undefined, undefined, CultUtil.isCultRunWorkout(booking.CultClass.workoutID)))

        productDetailPage.widgets.push(this.getCenterInfoWidget(userContext, booking.Center, productType))

        if (feedback && (feedback.rating === "NOT_RATED" || feedback.rating === "DISMISSED")) {
            productDetailPage.widgets.push(new ProductFeedbackWidget(await feedbackPageConfigV2Cache.getQuestionV2(feedback), feedback.feedbackId))
        }
        productDetailPage.widgets.push(this.getDescriptionWidget(booking.Class.Workout.info))
        if (booking.Membership)
            productDetailPage.widgets.push(await this.getPackInfoWidget(userContext, booking.Membership))
        productDetailPage.pageAction = {
            title: "Done",
            action: "POP_ACTION"
        }
        productDetailPage.actions = [{
            title: "Done",
            actionType: "POP_ACTION"
        }]
        productDetailPage.creditCost = booking.creditCost
        return productDetailPage
    }

    private async getSummaryWidget(userContext: UserContext, productType: ProductType, booking: CultBooking, issuesMap: Map<string, CustomerIssueType[]>): Promise<WidgetView> {
        const tz = userContext.userProfile.timezone
        const startTime = TimeUtil.getMomentForDateString(booking.Class.startTime, tz, "hh:mm:ss")
        const endTime = TimeUtil.getMomentForDateString(booking.Class.endTime, tz, "hh:mm:ss")
        const duration = momentTz.duration(endTime.diff(startTime)).asMinutes() + " minutes"
        const document = _.find(booking.Class.Workout.documents, document => {
            return document.tagID === 11
        })
        const summaryWidget: WidgetView & {
            title: string,
            bookingNumber: string,
            subTitle: string,
            date: string,
            image: string,
            startTime: string,
            endTime: string,
            meta?: any,
            manageOptions?: { displayText: string, options: ManageOptionPayload[] }
        } = {
            widgetType: "CULT_BOOKING_SUMMARY",
            bookingNumber: booking.bookingNumber,
            title: booking.Class.Workout.name,
            subTitle: duration,
            date: booking.Class.date,
            startTime: booking.Class.startTime,
            endTime: booking.Class.endTime,
            image: document ? "/" + document.URL : undefined
        }

        const cultManageOptions = await this.productBusiness.getCultManageOptions(userContext, productType, booking, issuesMap, false, true)
        const isUpcomingClass: boolean = booking.label === "Upcoming"
        summaryWidget.manageOptions = cultManageOptions.manageOptions
        summaryWidget.meta = cultManageOptions.meta
        return summaryWidget
    }

    private getManageOptionsWidget(manageOptions: ManageOptions, meta: any) {
        return new ManageOptionsWidget(manageOptions, meta)
    }

    private getCenterInfoWidget(userContext: UserContext, cultCenter: CultCenter, productType: ProductType): CenterView & WidgetView {
        const centerView: CenterView = new CenterView(userContext, cultCenter, productType)
        const widgetView: WidgetView = { widgetType: "CENTER_INFO_WIDGET" }
        return Object.assign(widgetView, centerView)
    }

    private getDescriptionWidget(description: string): WidgetView {
        const descriptions: InfoCard[] = [{
            subTitle: description,
        }]
        return new DescriptionWidget(descriptions)
    }

    private getIssueList(issuesMap: Map<string, CustomerIssueType[]>, isUpcomingClass: boolean, membership: CultMembership): { code: string, title: string, confirmation: string }[] {
        const issueList: { code: string, title: string, confirmation: string }[] = []
        let issueKey: IssueCategory = undefined
        if (membership) {
            if (isUpcomingClass)
                issueKey = "CultPackSessionBooked"
            else
                issueKey = "CultPackSessionAttended"
        } else {
            if (isUpcomingClass)
                issueKey = "CultSingleSessionBooked"
            else
                issueKey = "CultPackSessionBooked"
        }
        issuesMap.get(issueKey).forEach(customerIssueType => {
            issueList.push({
                code: customerIssueType.code,
                title: customerIssueType.subject,
                confirmation: customerIssueType.confirmation
            })
        })
        return issueList
    }


    private async getPackInfoWidget(userContext: UserContext, membership: CultMembership): Promise<Promise<PackInfoWidget>> {
        if (membership) {
            const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membership.id.toString(), this.membershipService)
            const action: ActionCard = {
                title: "SEE PACK",
                action: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
            }
            return new PackInfoWidget(membership.productID, membership.packName, action)
        }
        else {
            return undefined
        }
    }
    private getWorkoutStationWidget(workoutCell: string): ActionCardWidget {
        return {
            widgetType: "ACTION_CARD_WIDGET_V2",
            displayText: `Station Number : ${workoutCell}`,
            icon: {
                iconType: "IMAGE_URL",
                url: "/image/icons/cult/cult_spot_number.png"
            }
        }
    }

    private getQRCodeWidget(booking: CultBooking): QRCodeWidget {
        return {
            widgetType: "QR_CODE_WIDGET",
            header: {
                title: "Mark Attendance"
            },
            qrCodeString: booking?.qrCode?.qrString || "dummy qr code",
            title: "Show QR code",
            subTitle: "Show this QR Code to our crew at the centre and pick up your purchases",
        }
    }

}
export default BookingDetailViewBuilder
