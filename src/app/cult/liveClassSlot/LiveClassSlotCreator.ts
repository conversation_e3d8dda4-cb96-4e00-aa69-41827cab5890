import { inject, injectable } from "inversify"
import { IRedisDao } from "@curefit/redis-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { LiveClass } from "@curefit/diy-common"
import { LiveClassSlotKey } from "./LiveClassSlot"

export const CLASS_TIME_SLOTS_EXPIRE_IN_SECS = 2 * 60

export type LINK_TYPE = "PRODUCT_PAGE"

@injectable()
export class LiveClassSlotCreator {

    constructor(
        @inject(CUREFIT_API_TYPES.LiveClassSlotRedisDao) private redisDao: IRedisDao<LiveClassSlotKey, LiveClass>, ) {
    }

    public async createLiveCLassTimeSlot(liveClassId: string, liveClassInfo: LiveClass, userId: string): Promise<boolean> {
        const isCreated = await this.redisDao.upsertWithExpiry(LiveClassSlotKey.from(liveClassId, userId), liveClassInfo, CLASS_TIME_SLOTS_EXPIRE_IN_SECS)
        return isCreated
    }

    public async getLiveCLassTimeSlot(liveClassId: string, userId: string): Promise<LiveClass> {
        const classInviteLink = await this.redisDao.read(LiveClassSlotKey.from(liveClassId, userId))
        return classInviteLink
    }

}
