import { Key } from "@curefit/redis-utils"

const NAMESPACE = "cf-app:"

export class LiveClassSlot<PERSON>ey implements Key {
    key: string

    constructor(liveClassId: string, userId: string) {
        this.key = `${NAMESPACE}${userId}:${liveClassId}`
    }

    lookupKey(): string {
        return this.key
    }

    static from(liveClassId: string, userId: string) {
        return new LiveClassSlotKey(liveClassId, userId)
    }
}
