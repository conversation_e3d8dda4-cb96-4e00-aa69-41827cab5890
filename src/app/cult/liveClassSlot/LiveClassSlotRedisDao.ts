import { inject } from "inversify"
import { BaseRedisDaoImpl, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { LiveClassSlotKey } from "./LiveClassSlot"
import { LiveClass } from "@curefit/diy-common"

export class LiveClassSlotRedisDao extends BaseRedisDaoImpl<LiveClassSlotKey, LiveClass> {
  constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) multiCrudKeyValueDao: IMultiCrudKeyValue) {
    super(multiCrudKeyValueDao)
  }
}
