
import { injectable, inject } from "inversify"
import { <PERSON><PERSON><PERSON>el<PERSON> } from "../util/CacheHelper"
import { UserContext } from "@curefit/userinfo-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CultWorkout, CultDocument, NuxClassRecommendations, PostWorkoutTips } from "@curefit/cult-common"
import * as _ from "lodash"


import {
  ClassInfoCardWidget,
  CultClassRecommendation,
  HeaderWidget,
  InfoCard,
  ProductListWidget
} from "../common/views/WidgetView"
import { ActionCard, ProductRowListWidget } from "../page/PageWidgets"
import { CardVideoWidget, CardVideoItem } from "@curefit/vm-models"
import { AnnouncementDetails } from "../announcement/AnnouncementViewBuilder"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { UrlPathBuilder } from "@curefit/product-common"
import { FIRST_CLASS_PRE_WORKOUT_VIDEO, FIRST_CLASS_POST_WORKOUT_VIDEO, FIRST_CLASS_PRE_WORKOUT_THUMBNAIL, FIRST_CLASS_POST_WORKOUT_THUMBNAIL } from "../util/CultUtil"
import { CdnUtil } from "@curefit/util-common"

export interface WorkoutTips {
  heading: string,
  subHeading: string,
  identifier: string
}

export interface PreworkoutTipsPage {
  widgets: Array<ProductListWidget>
}




@injectable()
class NuxViewBuilder {

  constructor(@inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
    @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness) {

  }

  async getRecommendationSteps(userContext: UserContext): Promise<any> {
    const announcementDetails = <AnnouncementDetails>await this.announcementBusiness.createAndGetAnnouncementDetails(userContext, `NUX_LANDING_PAGE`)
    return {
      title: "Bringing it together",
      steps: [
        {
          title: "Taking into account your fitness",
          time: 1000
        },
        {
          title: "Keeping in mind the activities you enjoy",
          time: 1000
        },
        {
          title: "Considering your fitness goal",
          time: 1000
        },
        {
          title: "Generating your Recommendation",
          time: 3000
        }
      ],
      apiUrl: "/cult/classRecommendationStatus",
      action: {
        actionType: "NAVIGATION",
        pageFrom: "classrecommendationgenerator",
        url: "curefit://classrecommendation"
      },
      dismissAction: {
        actionType: "REST_API",
        title: "DISMISS",
        meta: {
          method: "POST",
          url: `user/announcement/${announcementDetails.announcementId}`,
          body: {
            state: "DISMISSED"
          }
        }
      },
    }
  }

  async getLandingPage(userContext: UserContext): Promise<any> {
    const userData = await this.userCache.getUser(userContext.userProfile.userId)
    const announcementDetails = <AnnouncementDetails>await this.announcementBusiness.createAndGetAnnouncementDetails(userContext, `NUX_LANDING_PAGE`)
    return {
      title: `Welcome to Cult, ${userData.firstName}!`,
      subtitle: "We realise starting a new fitness journey can be intimidating. We’ve got your back!\n\nJust answer 4 questions and we will help you get started.",
      pageAction: {
        title: "Let's Begin",
        actionType: "NAVIGATION",
        url: "curefit://userform?formId=NUX_FORM"
      },
      skipAction: {
        title: "SKIP",
        actionType: "NAVIGATION",
        "url": "curefit://classbookingv2?productType=FITNESS&pageFrom=addActivity&filterApplied=false",
      },
      dismissAction: {
        actionType: "REST_API",
        title: "DISMISS",
        meta: {
          method: "POST",
          url: `user/announcement/${announcementDetails.announcementId}`,
          body: {
            state: "DISMISSED"
          }
        }
      },
      backgroundImageUrlOne: CdnUtil.getCdnUrl("curefit-content/image/icons/cultOnboarding/woman_2.png"),
      backgroundImageUrlTwo: ""
    }
  }

  async generateRecommendations() {
    return {
      title: "Bringing it together",
      taskList: ["Taking into account your fitness", "Keeping in mind the activities you enjoy", "Considering your fitness goal", "Generating your Recommendation"]
    }
  }

  async getPostWorkoutClassTips(postWorkoutTips: PostWorkoutTips, userId: string) {
    const widgets = []
    const userDetails = await this.userCache.getUser(userId)
    const videoWidgetItem: CardVideoItem = {
      image: FIRST_CLASS_POST_WORKOUT_THUMBNAIL,
      videoUri: FIRST_CLASS_POST_WORKOUT_VIDEO,
      thumbnailVideoUri: "",
      displayPlayIcon: true
    }
    widgets.push(new CardVideoWidget(videoWidgetItem))
    if (postWorkoutTips && postWorkoutTips.tips && postWorkoutTips.tips.recoveryTips) {
      const header = {
        title: "RECOVERY TIPS",
        subTitle: `${userDetails.firstName}, you might experience some muscle soreness.Here are a few tips to help you recover better.`
      }
      const infoCards: ActionCard[] = postWorkoutTips.tips.recoveryTips.map((tips: any) => {
        return {
          title: tips.heading,
          subTitle: tips.subHeading,
          icon: UrlPathBuilder.getCultOnboardingImages(tips.identifier)
        }
      })
      widgets.push(new ProductListWidget("SMALL", header, infoCards, undefined, undefined, undefined, true))
    }
    if (postWorkoutTips && postWorkoutTips.tips && postWorkoutTips.tips.recoveryMeal) {
      const header = {
        title: "RECOVERY MEAL",
        subTitle: `Some tips from our experienced sports nutritionists to help you recover.`
      }
      const infoCards: ActionCard[] = postWorkoutTips.tips.recoveryMeal.map((tips: any) => {
        return {
          title: tips.heading,
          subTitle: tips.subHeading,
          image: UrlPathBuilder.getCultOnboardingImages(tips.identifier)
        }
      })
      widgets.push(new ProductRowListWidget("MEDIUM", header, infoCards))
    }
    return { widgets }
  }

  async getRecommendationsViewForFirstClass(classRecommendation: NuxClassRecommendations) {
    const widgets: Array<HeaderWidget | ClassInfoCardWidget> = []
    widgets.push({
      widgetType: "HEADER_WIDGET",
      widgetTitle: {
        title: "What we suggest",
        subTitle: `We think you’ll enjoy these ${classRecommendation.workouts.length} workout formats based on your choices.`
      },
      titleStyle: {
        fontFamily: "BrandonText-Bold",
        fontSize: 24,
        letterSpacing: -0.4,
        color: "#000000"
      },
      subtitleStyle: {
        fontFamily: "BrandonText-Regular",
        fontSize: 14,
        color: "#000000"
      },
      style: {
        marginLeft: 5,
        marginRight: 30
      }
    })
    const recommendationItems: any = []
    const filtersApplied: Set<number> = new Set()
    classRecommendation.workouts.forEach((cultWorkout: CultWorkout, index: number) => {
      const document: CultDocument = _.find(cultWorkout.documents, doc => doc.tagID === 11)
      const imageUrl: string = !_.isNil(document) ? "/" + document.URL : undefined
      if (!filtersApplied.has(cultWorkout.workoutCategoryID)) {
        filtersApplied.add(cultWorkout.workoutCategoryID)
      }
      recommendationItems.push({
        title: cultWorkout.name,
        description: cultWorkout.info,
        benefits: cultWorkout.benefits,
        image: imageUrl,
        workoutCategoryId: cultWorkout.workoutCategoryID
      })
    })
    widgets.push({
      widgetType: "CLASS_INFO_CARD_WIDGET",
      items: recommendationItems
    })
    let actionUrl = ""
    if (classRecommendation.workouts && classRecommendation.workouts.length > 0) {
      actionUrl = `curefit://classbookingv2?productType=FITNESS&pageFrom=classRecommendation&filterApplied=true&workoutIds=${Array.from(filtersApplied).join(",")}`
    } else {
      actionUrl = `curefit://classbookingv2?productType=FITNESS&pageFrom=classRecommendation`
    }

    const actions = [{
      title: "Book First FREE Session",
      actionType: "NAVIGATION",
      url: actionUrl,
    }]
    return {
      widgets,
      actions,
      analyticsData: {
        workoutId: Array.from(filtersApplied).join(",")
      }
    }
  }

  async getPreWorkoutTips(preWorkoutTipsInfo: WorkoutTips[], center: any) {
    const centerFacilities: string[] = []
    const videoWidgetItem: CardVideoItem = {
      image: FIRST_CLASS_PRE_WORKOUT_THUMBNAIL,
      videoUri: FIRST_CLASS_PRE_WORKOUT_VIDEO,
      thumbnailVideoUri: "",
      displayPlayIcon: true
    }
    const widgets = []
    widgets.push({ ...(new CardVideoWidget(videoWidgetItem)), removePadding: true, header: "PREPARE FOR YOUR FIRST CLASS" })
    for (let i = 0; i < center.facilities.length; i++) {
      if (center.facilities[i].available) centerFacilities.push(center.facilities[i].name)
    }
    const centerInfoHeader = {
      "title": "ABOUT YOUR CENTRE",
    }
    const centerInfoCards = [
      {
        icon: UrlPathBuilder.getCultOnboardingImages("location"),
        subTitle: center.Address.addressLine1,
        cardAction: {
          actionType: "NAVIGATION",
          url: `curefit://internaldeeplink?uri=${center.placeUrl}`,
          title: "NAVIGATE"
        },
      },
      {
        icon: UrlPathBuilder.getCultOnboardingImages("center"),
        subTitle: `Center facilities:\n${centerFacilities.join(" | ")}`
      }
    ]
    const centerInfo = {
      ...new ProductListWidget("SMALL", centerInfoHeader, centerInfoCards, undefined, undefined, undefined, true),
      noTopPadding: true,
      hasDividerAbove: true
    }
    widgets.push(centerInfo)
    console.log("center.CenterManager", center.CenterManager)
    if (center && center.CenterManager) {
      const centerManagerInfo = {
        name: `${center.CenterManager}`,
        designation: "Centre Manager ",
        profilePictureUrl: "",
        address: center.name,
        widgetType: "DISPLAY_USER_DETAILS",
        hasDividerAbove: false,
        showElevatedCard: true
      }
      widgets.push(centerManagerInfo)
    }
    const preWorkoutTipsHeader = {
      "title": "WHAT TO DO",
    }
    if (preWorkoutTipsInfo) {
      const preWorkoutTipsInfoCards = preWorkoutTipsInfo.map((preWorkoutTips) => ({
        title: preWorkoutTips.heading,
        subTitle: preWorkoutTips.subHeading,
        icon: UrlPathBuilder.getCultOnboardingImages(preWorkoutTips.identifier),
      }))
      const preWorkoutTips = {
        ...new ProductListWidget("SMALL", preWorkoutTipsHeader, preWorkoutTipsInfoCards, null, null, null, true),
        noTopPadding: true,
        hasDividerAbove: true
      }
      widgets.push(preWorkoutTips)
    }
    return { widgets }
  }
}



export default NuxViewBuilder
