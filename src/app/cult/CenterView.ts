import { CultCenter } from "@curefit/cult-common"
import { Address } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { TimeUtil } from "@curefit/util-common"
import CultUtil from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import LocationUtil from "../util/LocationUtil"

const IOS_CENTRE_PAGE_VERSION = 5.6
const ANDROID_CENTERE_PAGE_VERSION = 5.7

class CenterView {
    constructor(userContext: UserContext, center: CultCenter, productType: ProductType, cityName?: string, showWorkouts?: boolean) {
        this.id = center.id
        this.name = center.name

        const tz = userContext.userProfile.timezone
        const userAgent = userContext.sessionInfo.userAgent
        if (center.launchDate) {
            const numDays = TimeUtil.diffInDaysReal(tz, center.launchDate, TimeUtil.todaysDate(tz, "YYYY-MM-DD"))
            if (numDays > 0) {
                const launchDateString = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDate(center.launchDate, tz), "Do, MMM")
                this.name = `${center.name} - Opening on ${launchDateString}`
            }
        }
        const seoParams: SeoUrlParams = {
            city: cityName ? cityName : undefined,
            locality: center.locality,
            productName: center.name
        }
        if (productType === "FITNESS") {
            this.action = ActionUtil.cultCenter(center.id.toString(), undefined, userAgent, seoParams)
        } else {
            this.action = ActionUtil.mindCenter(center.id.toString(), undefined, userAgent, seoParams)
        }
        this.distance = LocationUtil.getDistanceFromLatLonInKm(
            center.Address.latitude,
            center.Address.longitude,
            userContext.sessionInfo.lat,
            userContext.sessionInfo.lon
        ).toFixed(2) + " Km"
        this.address = {
            addressString: CultUtil.getCultCenterAddress(center),
            locality: center.Address.locality,
            latLong: {
                lat: center.Address.latitude,
                long: center.Address.longitude
            },
            mapUrl: center.placeUrl,
            pincode: center.Address.pinCode ? center.Address.pinCode.toString() : "",
        }
        this.alertMeta = CultUtil.getEmployeeOnlyModalMetaForCenter(center.id.toString())
        this.mapUrl = center.placeUrl
        this.images.push(`/image/centers/CULTCENTER${center.id}/7.jpg`)
        if (showWorkouts && !_.isEmpty(center.workouts)) {
            this.tagView = {
                tagTitle: `Workouts available at this centre`,
                tags: _.map(center.workouts, workout => workout.name)
            }
        }
    }
    public id: number
    public name: string
    public action: string
    public address: Address
    public mapUrl: string
    public distance: string
    public images?: string[] = []
    public alertMeta?: {
        title: string
        message: string
    }
    public showIcon: boolean = false
    public tagView?: {
        tagTitle: string
        tags: string[]
    }
}

export default CenterView
