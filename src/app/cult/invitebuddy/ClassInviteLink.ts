import { Key } from "@curefit/redis-utils"
import { ProductType } from "@curefit/product-common"
import { Tenant } from "@curefit/user-common"
import { LINK_TYPE } from "./ClassInviteLinkCreator"

export class ClassInviteLinkKey implements Key {
    classId: string
    key: string

    constructor(tenant: Tenant, classId: string, linkType?: LINK_TYPE, source?: string) {
        this.classId = classId
        if (linkType === "PRODUCT_PAGE") {
            this.key = `CFAPP:${tenant}:page:${classId}:source:${source}`
        } else {
            this.key = `CFAPP:${tenant}:invite:${classId}:source:${source}`
        }
    }

    lookupKey(): string {
        return this.key
    }

    static from(tenant: Tenant, classId: string, linkType?: LINK_TYPE, source?: string) {
        return new ClassInviteLinkKey(tenant, classId, linkType, source)
    }
}

export interface ClassInviteLink {
    productType: ProductType
    classId: string
    centerId?: string,
    sharableUrl: string
    expiryInSecs: number
}
