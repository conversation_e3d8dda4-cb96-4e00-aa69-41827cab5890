import { inject, injectable } from "inversify"
import { IRedisDao } from "@curefit/redis-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import { eternalPromise } from "@curefit/util-common"
import { UserContext } from "@curefit/userinfo-common"
import { Tenant as AppTenant } from "@curefit/user-common"
import { ClassInviteLink, ClassInviteLinkKey } from "./ClassInviteLink"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import {
    DigitalCatalogueEntryV1,
    DIYPack,
    DIYProduct,
    DIYSocialNodeType,
    LiveFitWorkoutFormat
} from "@curefit/diy-common"
import LiveUtil from "../../util/LiveUtil"
import { IRIS_CLIENT_TYPES, IDeeplinkService } from "@curefit/iris-client"
import { SOCIAL_CLIENT_TYPES, ISocialService } from "@curefit/social-client"
import { NodeRelationEntry, EntityType, Tenant } from "@curefit/social-common"
import { LinkPayload, DeeplinkServices } from "@curefit/iris-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "../../util/AppUtil"
import AtlasUtil from "../../util/AtlasUtil"

export const CLASS_INVITE_LINK_EXPIRY_IN_SECS = 30 * 24 * 60 * 60
export const CLASS_REFERRAL_INVITE_LINK_EXPIRY_IN_SECS = 4 * 24 * 60 * 60

export type LINK_TYPE = "PRODUCT_PAGE"

const SOCIAL_EVENT_NODE_SYSTEM_CREATOR_ID = 1
const SOCIAL_EVENT_SUBSCRIBED_ATTRIBUTE_ID = 2

const DIY_SOCIAL_HAS_RECOMMENDED_RELATION = "HAS_RECOMMENDED"

const HOME_PAGE_URL = "https://cure.fit"
const SUGARFIT_PAGE_URL = "https://www.sugarfit.com"

@injectable()
export class ClassInviteLinkCreator {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkRedisDao) private redisDao: IRedisDao<ClassInviteLinkKey, ClassInviteLink>,
        @inject(IRIS_CLIENT_TYPES.DeepLinkService) protected deepLinkService: IDeeplinkService,
        @inject(SOCIAL_CLIENT_TYPES.SocialService) private socialService: ISocialService) {
    }

    private async createUniversalDeepLinkUrl(tenant: AppTenant, requestBody: LinkPayload) {
        const preferredService = tenant === AppTenant.LIVEFIT_APP ? DeeplinkServices.BRANCH : undefined
        return await this.deepLinkService.createUniversalDeepLinkUrl(tenant, requestBody, undefined, undefined, preferredService)
    }

    public async createInviteLink(tenant: AppTenant, classInviteLink: ClassInviteLink, linkType?: LINK_TYPE, expiry?: number, source?: string): Promise<boolean> {
        const key = ClassInviteLinkKey.from(tenant, classInviteLink.classId, linkType, source)
        const isCreated = await this.redisDao.upsertWithExpiry(key, classInviteLink, expiry || CLASS_INVITE_LINK_EXPIRY_IN_SECS)
        this.logger.info(`class invite link created for classId ${classInviteLink.classId}`)
        return isCreated
    }

    public async getCultClassInviteLink(userContext: UserContext, classId: string, productType: ProductType, centerId: string): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let classInviteLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, classId))
        if (!classInviteLink) {
            const deeplinkPath = `curefit://classbookingv2?productType=${productType}&classId=${classId}&centerId=${centerId}`
            // TODO: need to update image and website url based on tenants
            const requestBodyForBranchApi = {
                type: 2,
                campaign: "Invite Buddy",
                channel: "app",
                applink: `${deeplinkPath}&deeplinkCampaignType=FITNESS_CLASS_INVITE`,
                marketingTitle: "Cult Class Invite",
                canonicalUrl: HOME_PAGE_URL,
                ...(!AppUtil.isInternationalApp(userContext) && {
                    weblink: HOME_PAGE_URL,
                }),
                feature: "referral",
                socialMetaTagInfo: {
                    socialTitle: "Let's workout together!",
                    socialImageLink: "https://lh3.googleusercontent.com/rwwxKYAjXIHp6GaAWKOs0MiJNezFV3w4wUZbWQIkcKFobNwTXpjWTtjnVBbBw4eHmQ",
                    socialType: "website"
                }
            }
            const response = (await eternalPromise(this.createUniversalDeepLinkUrl(tenant, requestBodyForBranchApi))).obj
            if (!response) {
                this.logger.error("class invite Link not generated for class id: " + classId)
                return Promise.resolve(undefined)
            } else {
                classInviteLink = {
                    classId: classId,
                    productType: productType,
                    expiryInSecs: CLASS_INVITE_LINK_EXPIRY_IN_SECS,
                    centerId: centerId,
                    sharableUrl: response.url
                }
                await this.createInviteLink(tenant, classInviteLink)
            }
        }
        return classInviteLink.sharableUrl
    }

    public async getDiyClassInviteLink(userContext: UserContext, diyPack: DIYPack, productType: ProductType, packName: string, nodeType: DIYSocialNodeType, image: string, productId?: string, productName?: string): Promise<string | undefined> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const packId = diyPack.productId
        try {
            const linkSource = "diyInvite"
            const source = `${userContext.userProfile.userId}-${linkSource}`
            let classDetailLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, productId ? productId : packId, null, source))

            if (classDetailLink) {
                return classDetailLink.sharableUrl
            } else {
                const sessionName = productName ? (productName + " - " + packName) : packName
                const nodeRelation = await this.getOrCreateNodeAndRelationForDiyClass(userContext.userProfile.userId, sessionName, "", productId ? productId : packId, nodeType)
                const nodeRelationID = `&nodeRelationID=${nodeRelation.id.toString()}`
                const classFormat = `&format=${productType}`
                const isFitnessProduct = (productType === "DIY_FITNESS" || productType === "DIY_FITNESS_PACK")
                const route = isFitnessProduct ? "cultdiypack" : "mindpack"
                const desktopPath = ActionUtilV1.diyPackProductPage(diyPack, "DESKTOP")

                let deeplinkPath = `curefit://${route}?packId=${packId}${nodeRelationID}${classFormat}${productId ? "&productId=" + productId : ""}`

                if (AppUtil.isWeb(userContext)) {
                   deeplinkPath = ActionUtilV1.diyPackProductPage(diyPack, userContext.sessionInfo.userAgent)
                }
                let applink
                try {
                    const requestBodyForBranchApi = {
                        type: 2,
                        campaign: linkSource,
                        feature: "referral",
                        channel: "app",
                        applink: deeplinkPath,
                        marketingTitle: "DIY Class Invite",
                        canonicalUrl: HOME_PAGE_URL,
                        ...(!AppUtil.isInternationalApp(userContext) && {
                            weblink: `${HOME_PAGE_URL}${desktopPath}`,
                        }),
                        socialMetaTagInfo: {
                            socialTitle: isFitnessProduct ? "Try this home workout" : "Try this meditation",
                            socialDescription: packName,
                            socialImageLink: `https://cdn-images.cure.fit/www-curefit-com/image/upload/${image}`,
                            socialType: "website",
                        }
                    }
                    applink = (await eternalPromise(this.createUniversalDeepLinkUrl(AppUtil.getTenantFromUserContext(userContext), requestBodyForBranchApi))).obj
                } catch (e) {
                    this.logger.error(`Error while creating branch link for diy referral userId: ${userContext.userProfile.userId} error: ${JSON.stringify(e)}`)
                }

                if (!applink) {
                    return Promise.resolve(undefined)
                } else {
                    classDetailLink = {
                        classId: productId ? productId : packId,
                        productType,
                        expiryInSecs: CLASS_REFERRAL_INVITE_LINK_EXPIRY_IN_SECS,
                        sharableUrl: applink.url
                    }
                    await this.createInviteLink(tenant, classDetailLink, null, CLASS_REFERRAL_INVITE_LINK_EXPIRY_IN_SECS, source)
                    return applink.url
                }
            }
        } catch (e) {
            this.logger.error(`Error while creating deep-link for referral userId: ${userContext.userProfile.userId} error: ${JSON.stringify(e)}`)
            return Promise.resolve(undefined)
        }
    }

    private async getOrCreateNodeAndRelationForDiyClass(userID: string, sessionName: string, sessionDescription: string, sessionId: string, nodeType: DIYSocialNodeType): Promise<NodeRelationEntry> {
        let relation: NodeRelationEntry
        try {
            relation = await this.socialService.createUserEventRelation({
                userNodeEntry: {
                    entityId: userID,
                    entityType: EntityType.USER
                },
                eventEntry: {
                    name: sessionName,
                    description: sessionDescription || "",
                    tenant: Tenant.DIY,
                    context: nodeType,
                    externalId: sessionId,
                    creatorId: SOCIAL_EVENT_NODE_SYSTEM_CREATOR_ID
                },
                nodeRelationEntry: {
                    attributeCode: DIY_SOCIAL_HAS_RECOMMENDED_RELATION,
                    context: "recommended",
                }
            })
        } catch (e) {
            this.logger.error("error while creating diy referral link ", e)
        }
        return relation
    }

    public async getLiveClassInviteLink(userContext: UserContext, classId: string, className: string, liveClassId: string): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let classInviteLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, classId))
        if (!classInviteLink) {
            const deeplinkPath = `curefit://liveclassdetail?bookingNumber=${encodeURIComponent(classId)}&liveClassId=${encodeURIComponent(liveClassId)}&productType=LIVE_FITNESS`
            const requestBodyForBranchApi = {
                type: 2,
                campaign: "Invite Buddy",
                feature: "referral",
                channel: "app",
                marketingTitle: "Live Class Invite",
                applink: `${deeplinkPath}&deeplinkCampaignType=LIVE_CLASS_INVITE`,
                canonical_url: AppUtil.isSugarFitOrUltraFitApp(userContext) ? SUGARFIT_PAGE_URL : HOME_PAGE_URL,
                ...(!AppUtil.isInternationalApp(userContext) && {
                    weblink: AppUtil.isSugarFitOrUltraFitApp(userContext) ? SUGARFIT_PAGE_URL : HOME_PAGE_URL,
                }),
                socialMetaTagInfo: {
                    socialTitle: "Join me for a LIVE class from home!",
                    socialDescription: `${className}`,
                    socialImageLink:  AppUtil.isSugarFitOrUltraFitApp(userContext) ? "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/chroniccare/logos/sugarfit_deeplink_logo.png" : "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/cf_logo_black_bg_2x.png",
                    socialType: "website"
                },
            }
            const response = (await eternalPromise(this.createUniversalDeepLinkUrl(AppUtil.getTenantFromUserContext(userContext), requestBodyForBranchApi))).obj
            if (!response) {
                this.logger.error("class invite Link not generated for class id: " + classId)
                return Promise.resolve(undefined)
            } else {
                classInviteLink = {
                    classId: classId,
                    productType: "LIVE_FITNESS",
                    sharableUrl: response.url,
                    expiryInSecs: CLASS_INVITE_LINK_EXPIRY_IN_SECS
                }
                await this.createInviteLink(tenant, classInviteLink)
            }
        }
        return classInviteLink.sharableUrl
    }

    private async getOrCreateNodeAndRelationForClass(userID: string, sessionName: string, sessionDescription: string, sessionId: string): Promise<NodeRelationEntry> {
        let relation: NodeRelationEntry
        try {
            relation = await this.socialService.createUserEventRelation({
                userNodeEntry: {
                    entityId: userID,
                    entityType: EntityType.USER
                },
                eventEntry: {
                    name: sessionName,
                    description: sessionDescription || "",
                    tenant: Tenant.LIVE,
                    context: "session",
                    externalId: sessionId,
                    creatorId: SOCIAL_EVENT_NODE_SYSTEM_CREATOR_ID
                },
                nodeRelationEntry: {
                    attributeId: SOCIAL_EVENT_SUBSCRIBED_ATTRIBUTE_ID,
                    context: "subscribed",
                }
            })
        } catch (e) {
            this.logger.error("error while create not for referral link ", e)
        }
        return relation
    }

    public async getLiveClassReferralLink(userContext: UserContext, classId: string, className: string, sessionDescription: string, format: LiveFitWorkoutFormat, card?: any, newLiveSessionId?: string): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        try {
            const linkSource = card.source || "cultlivereferral"
            const source = `${userContext.userProfile.userId}-${linkSource}`
            const processedClassId = card.oneTimeLink ? "" : classId
            let classDetailLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, processedClassId, null, source))

            if (classDetailLink) {
                return classDetailLink.sharableUrl
            } else {

                const nodeRelation = await this.getOrCreateNodeAndRelationForClass(userContext.userProfile.userId, className, sessionDescription, classId)
                const nodeRelationID = `&nodeRelationID=${nodeRelation.id.toString()}`
                const newliveClassID = `&liveClassId=${newLiveSessionId}`
                const classFormat = `&format=${format}`
                let deeplinkPath = `curefit://liveclassdetail?bookingNumber=${encodeURIComponent(classId)}&productType=LIVE_FITNESS${nodeRelationID}${classFormat}${encodeURIComponent(newLiveSessionId ? newliveClassID : "")}`
                const appDeepLink = LiveUtil.getDeepLinkFromCardDetails(card && {
                    ...card,
                    redirectionUrl: card.redirectionUrl || deeplinkPath
                })
                if (appDeepLink) {
                    deeplinkPath = appDeepLink
                }

                let applink
                if (card && !applink) {
                    const requestBodyForBranchApi = {
                        type: 2,
                        campaign: linkSource,
                        feature: "referral",
                        channel: "app",
                        marketingTitle: "Live Class Invite",
                        applink: deeplinkPath,
                        canonical_url: HOME_PAGE_URL,
                        ...(!AppUtil.isInternationalApp(userContext) && {
                            weblink: HOME_PAGE_URL,
                        }),
                        socialMetaTagInfo: {
                          socialTitle: "Join me for a LIVE class from home!",
                          socialDescription: `${className}`,
                          socialImageLink: "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/cf_logo_black_bg_2x.png",
                          socialType: "website",
                        }
                    }
                    applink = (await eternalPromise(this.createUniversalDeepLinkUrl(AppUtil.getTenantFromUserContext(userContext), requestBodyForBranchApi))).obj
                }

                if (!applink) {
                    return Promise.resolve(undefined)
                } else {
                    classDetailLink = {
                        classId: processedClassId,
                        productType: "LIVE_FITNESS",
                        expiryInSecs: card.oneTimeLink ? CLASS_INVITE_LINK_EXPIRY_IN_SECS : CLASS_REFERRAL_INVITE_LINK_EXPIRY_IN_SECS,
                        sharableUrl: applink.url
                    }
                    await this.createInviteLink(tenant, classDetailLink, null, CLASS_REFERRAL_INVITE_LINK_EXPIRY_IN_SECS, source)
                    return applink.url
                }
            }
        } catch (e) {
            this.logger.error(`Error while creating deep-link for referral userId: ${userContext.userProfile.userId} error: ${JSON.stringify(e)}`)
            return Promise.resolve(undefined)
        }
    }

    public async getDIYClassLink(userContext: UserContext, videoResponse: DIYProduct): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)

        let classDetailLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, videoResponse.productId, "PRODUCT_PAGE"))
        if (!classDetailLink) {
            const content = AtlasUtil.getContentDetailV2(videoResponse)
            const contentMeta = AtlasUtil.getContentMetaV2(videoResponse)
            const deeplinkPath = ActionUtil.diyVideoUrl(videoResponse, content.URL)
            const requestBodyForBranchApi = {
                type: 2,
                campaign: "DIY_ADD_TO_CALENDAR",
                feature: "referral",
                channel: "app",
                marketingTitle: "Add DIY class to calendar",
                applink: `${deeplinkPath}&deeplinkCampaignType=DIY_CLASS_ADDTOCALENDAR`,
                canonicalUrl: AppUtil.isSugarFitOrUltraFitApp(userContext) ? SUGARFIT_PAGE_URL : HOME_PAGE_URL,
                ...(!AppUtil.isInternationalApp(userContext) && {
                    weblink: AppUtil.isSugarFitOrUltraFitApp(userContext) ? SUGARFIT_PAGE_URL : HOME_PAGE_URL,
                }),
                socialMetaTagInfo: {
                    socialTitle: "Add DIY class to calendar!",
                    socialImageLink: "https://lh3.googleusercontent.com/rwwxKYAjXIHp6GaAWKOs0MiJNezFV3w4wUZbWQIkcKFobNwTXpjWTtjnVBbBw4eHmQ",
                    socialType: "website",
                }
            }
            const response = (await eternalPromise(this.createUniversalDeepLinkUrl(AppUtil.getTenantFromUserContext(userContext), requestBodyForBranchApi))).obj
            if (!response) {
                this.logger.error("class detail Link not generated for class id: " + videoResponse.productId)
                return Promise.resolve(undefined)
            } else {
                classDetailLink = {
                    classId: videoResponse.productId,
                    productType: videoResponse.productType,
                    expiryInSecs: CLASS_INVITE_LINK_EXPIRY_IN_SECS,
                    sharableUrl: response.url
                }
                await this.createInviteLink(tenant, classDetailLink, "PRODUCT_PAGE")
            }
        }
        return classDetailLink.sharableUrl
    }

    public async getLiveClassLink(userContext: UserContext, videoResponse: DigitalCatalogueEntryV1): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let classDetailLink = await this.redisDao.read(ClassInviteLinkKey.from(tenant, (<any>videoResponse)._id, "PRODUCT_PAGE"))
        if (!classDetailLink) {
            const deeplinkPath = `curefit://liveclassdetail?bookingNumber=${encodeURIComponent((<any>videoResponse)._id)}&productType=LIVE_FITNESS`
            const requestBodyForBranchApi = {
                type: 2,
                campaign: "LIVE_ADD_TO_CALENDAR",
                feature: "referral",
                channel: "app",
                marketingTitle: "Add LIVE class to calendar",
                applink: `${deeplinkPath}&deeplinkCampaignType=LIVE_CLASS_ADDTOCALENDAR`,
                canonicalUrl: HOME_PAGE_URL,
                ...(!AppUtil.isInternationalApp(userContext) && {
                    weblink: HOME_PAGE_URL,
                }),
                socialMetaTagInfo: {
                    socialTitle: "Add LIVE class to calendar!",
                    socialImageLink: "https://lh3.googleusercontent.com/rwwxKYAjXIHp6GaAWKOs0MiJNezFV3w4wUZbWQIkcKFobNwTXpjWTtjnVBbBw4eHmQ",
                    socialType: "website",
                }
            }
            const response = (await eternalPromise(this.createUniversalDeepLinkUrl(AppUtil.getTenantFromUserContext(userContext), requestBodyForBranchApi))).obj
            if (!response) {
                this.logger.error("class detail Link not generated for class id: " + (<any>videoResponse)._id)
                return Promise.resolve(undefined)
            } else {
                classDetailLink = {
                    classId: (<any>videoResponse)._id,
                    productType: "LIVE_FITNESS",
                    expiryInSecs: CLASS_INVITE_LINK_EXPIRY_IN_SECS,
                    sharableUrl: response.url
                }
                await this.createInviteLink(tenant, classDetailLink, "PRODUCT_PAGE")
            }
        }
        return classDetailLink.sharableUrl
    }
}
