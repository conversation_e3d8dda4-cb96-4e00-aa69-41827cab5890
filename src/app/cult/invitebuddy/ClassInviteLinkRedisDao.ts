import { inject } from "inversify"
import { BaseRedisDaoImpl, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ClassInviteLink, ClassInviteLinkKey } from "./ClassInviteLink"

export class ClassInviteLinkRedisDao extends BaseRedisDaoImpl<ClassInviteLinkKey, ClassInviteLink> {
    constructor(@inject(REDIS_TYPES.MultiCrudKeyValueDao) multiCrudKeyValueDao: IMultiCrudKeyValue) {
        super(multiCrudKeyValueDao)
    }
}
