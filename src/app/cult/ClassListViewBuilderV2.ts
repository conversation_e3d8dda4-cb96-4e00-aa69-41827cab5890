import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { inject, injectable } from "inversify"
import CultU<PERSON>, {
    CENTER_MAINTENANCE_WARNING,
    CENTER_SHUTDOWN,
    SWIMMING_WORKOUT_IDS,
    SWIMMING_SEGMENT_ID,
    CULT_NAS_PRICE_HIKE_BANNER_ID,
    MIND_NAS_PRICE_HIKE_BANNER_ID,
    CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_PROD,
    CLASS_SCHEDULE_PAGE_BANNER_WIDGET_ID_STAGE,
    getCenterReopenBanner,
    CULT_SPORT_CATEGORY_STAGE_WORKOUT_CATEGORY,
    CULT_SPORT_CATEGORY_PROD_WORKOUT_CATEGORY,
    workoutAbbreviationMap,
    BOOTCAMP_STAGE_WORKOUT_ID,
    BOOT<PERSON>MP_PROD_WORKOUT_ID,
    BO<PERSON><PERSON><PERSON>_CATEGORY_STAGE_WORKOUT_CATEGORY,
    B<PERSON><PERSON><PERSON><PERSON>_CATEGORY_PROD_WORKOUT_CATEGORY,
    DANCE_FITNESS_REVAMP_WORKOUT_ID,
    STRENGTH_REVAMP_WORKOUT_ID,
    WORKOUT_RECOMMENDATION_MAP,
    HRX_USER_D30
} from "../util/CultUtil"
import {
    CenterScheduleDetailCard,
    CultCenter,
    CultClassesResponseV2,
    CultWorkout,
    CultWorkoutCategory,
    CultBookingResponse,
    CultClass,
    CultSummary,
    ScheduleNoShowDetails
} from "@curefit/cult-common"
import {
    CULT_CLIENT_TYPES,
    ICultServiceOld as ICultService,
    SquadClassBooking,
    SquadClassBookingLite
} from "@curefit/cult-client"
import { FilterNamespace, LiveClassSlot, Trainer } from "@curefit/diy-common"
import { Filter } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import { CdnUtil, eternalPromise, TimeUtil } from "@curefit/util-common"
import { CenterNotSelectedError } from "../common/errors/CenterNotSelectedError"
import { Action } from "../common/views/WidgetView"
import ClassViewV2 from "./ClassViewV2"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import { AnnouncementView } from "../announcement/AnnouncementViewBuilder"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { ICultBusiness } from "./CultBusiness"
import * as momentTz from "moment-timezone"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService, IIntlClientService } from "@curefit/diy-client"
import { CacheHelper } from "../util/CacheHelper"
import { FitclubBusiness } from "../fitclub/FitclubBusiness"
import { BannerCarouselWidget } from "../page/PageWidgets"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { CFLiveProduct, DigitalCatalogueSearchRequest, LiveClass, LiveFitWorkoutFormat } from "@curefit/diy-common"
import {
    CultSchedulePageNotificationParams,
    NotificationWidget
} from "../common/views/NotificationWidget"
import { User } from "@curefit/user-common"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import LiveUtil, {
    LiveFitMasterClassFormat,
    masterClassFormat,
    allFormats,
    INTERACTIVE_ICON_URL, liveInteractiveClassFormat, LIVE_DISCOVERY_TABS_WIDGET_WEB_PROD
} from "../util/LiveUtil"
import {
    ClassByCenter,
    ClassByDateV2,
    ClassByTimeV2,
    CultClassRecommendationWidgetView, Label,
    PageTypes, WidgetView
} from "@curefit/apps-common"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ClassInviteLinkCreator } from "./invitebuddy/ClassInviteLinkCreator"
import { ErrorFactory, HTTP_CODE } from "@curefit/error-client"
import { DaysRemainingWidgetView } from "../digital/DaysRemainingWidgetView"
import { CFServiceInterfaces } from "../page/vm/ServiceInterfaces"
import { DaysRemainingWidget, ISegmentService } from "@curefit/vm-models"
import { ErrorCodes } from "../error/ErrorCodes"
import { OrderSource } from "@curefit/base-common"
import { LivePackUtil } from "../util/LivePackUtil"
import { PromiseCache } from "../util/VMUtil"
import WidgetBuilder from "../page/vm/WidgetBuilder"
import UserUtil, { LocationDataKey, LocationPreferenceResponseEntity } from "../util/UserUtil"
import IUserBusiness from "../user/IUserBusiness"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { LatLong } from "@curefit/location-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import LocationUtil from "../util/LocationUtil"
import { FeatureStateCache } from "../page/vm/services/FeatureStateCache"
import { isNull } from "lodash"
import { TransformUtil } from "../util/TransformUtil"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import AuthUtil from "../util/AuthUtil"
import PlayUtil from "../util/PlayUtil"
import { NoshowBookingBlockedWidget } from "../page/vm/widgets/NoshowBookingBlockedWidget"
import { AttributeKeyType, Membership } from "@curefit/membership-commons"
import gymfitUtil from "../util/GymfitUtil"
import { ProgressBar } from "../common/views/ProfileWidgetView"
import { IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import { interfaces } from "inversify-express-utils"
import { IFindQuery, SortOrder } from "@curefit/mongo-utils"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"

export const CULT_AREA_TOOL_TIP = "cult_area_tool_tip"

const DEFAULT_MAX_EXPANDED_CENTERS = 3
const HOME_CENTER_ID = -5
const HOME_CENTER_ID_MIND = -6
const HOME_CENTER_ID_EAT = -7
const LIVE_MIND_FORMATS = new Set(["YOGA", "MEDITATION", "MIND_PODCAST"])
const LIVE_EAT_FORMATS = new Set(["EAT"])
const MAX_SORT_PREFERENCE = 1000

const FILTERS_PRIORITY_MAP = new Map<number, number>()
// Order :: Dance, Strength, Burn, Yoga, Boxing
FILTERS_PRIORITY_MAP.set(56, 1.0) // Dance
FILTERS_PRIORITY_MAP.set(69, 2.0) // Strength
FILTERS_PRIORITY_MAP.set(37, 2.0) // HRX
FILTERS_PRIORITY_MAP.set(9, 3.0) // s & c
FILTERS_PRIORITY_MAP.set(66, 4.0) // Burn
FILTERS_PRIORITY_MAP.set(5, 5.0) // Yoga
FILTERS_PRIORITY_MAP.set(8, 6.0) // Boxing
FILTERS_PRIORITY_MAP.set(0, 100.0) // Default

const CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP = new Map<number, string>()
CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.set(1, "18469940-e571-4fff-b168-a2e63aa2065a_l1" )
CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.set(2, "18469940-e571-4fff-b168-a2e63aa2065a_l2")
export const ONBOARDING_RECOMMENDED_LEVEL_ATTRIBUTE = "onboarding_recommended_level"

export interface ClassScheduleResponse {
    title?: string
    header?: SchedulePageHeader
    message?: string
    classByDateMap: { [id: string]: ClassByDateV2 }
    centerInfoMap?: { [id: string]: CenterInfo }
    timeSlotOptions: TimeSlotOption[]
    trainers?: any
    workoutCategoryFilters: { id: number, name: string, displayText: string }[]
    liveClassFormatFilter?: Array<LiveFitWorkoutFormat | LiveFitMasterClassFormat>
    days: { id: string }[]
    announcementView?: AnnouncementView
    // TODO - remove this after killing 7.75
    shutdownScheduleDetails?: {
        description: string,
        icon: string,
        action: Action,
        dismissAction: Action
    }
    waitListAnnouncementAction?: Action
    waitListOnboardingAction?: any
    wlOnboardingTooltip?: string
    wlOnboardingAnimation?: string
    wlOnboardingImage?: string
    showCoachmark?: boolean
    showCreditOnboarding?: boolean
    wlOnboardingTitle?: string
    wlOnboardingSubtitle?: string
    wlShowBottomSheet?: boolean
    bannerWidget?: BannerCarouselWidget
    notificationWidget?: NotificationWidget
    emptyClassesMessage?: string
    userType?: "TRIAL_USER" | "MEMBER"
    rescheduleClassInfo?: {
        title: string,
        className: string,
        classDetails: string
    }
    daysRemainingWidget?: DaysRemainingWidget
    setBookingPageDateAction?: Action
    bookmarkedTrainers?: Trainer[]
    filters?: Filter[]
    infoWidget?: any
    tabsWidget?: any
    nudgeDetails?: NudgeDetail[]
    recommendedWorkouts?: {
        workoutDetail: WorkoutDetail[],
        action: Action,
        dismissAction: Action
    },
    oneDaySchedule?: boolean,
    sessionsRemainingWidget?: BasicProgressCard,
    showSneakPeak?: boolean,
    sneakPeakCollapsedWidgets?: any,
    sneakPeakExpandedWidgets?: any,
    creditsDetail?: CreditDetail,
    localitySelectionAction?: Action
}

export interface CreditDetail {
    title: string,
    creditsLeft: number,
    action: Action
}
export interface WorkoutDetail {
    name: string,
    description?: string,
    timestamp: string
}

export interface BasicProgressCard extends WidgetView {
    widgetType: "BASIC_PROGRESS_CARD",
    text: string,
    bgColor: string,
    textColor: string,
    progressBar: ProgressBar,
    trialType: string,
}

export interface NudgeDetail {
    title: string,
    titleColor: string,
    description?: string,
    action: Action,
}

interface CenterInfo {
    centerName: string,
    verticalName: string
    isNew: boolean
}

export interface TimeSlotOption {
    slot: string
    icon: string
    displayText: string
    startTime: string

}

interface SchedulePageHeader {
    title: string
    toolTip?: {
        text: string
        closeAction: Action
    }
}

@injectable()
class ClassListViewBuilderV2 {

    constructor(
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private mindFitService: ICultService,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected diyFulfilmentService: IDIYFulfilmentService,
        @inject(DIY_CLIENT_TYPES.IntlClientService) protected diyIntlClientService: IIntlClientService,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) private userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.FitclubBusiness) protected fitclubBusiness: FitclubBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) public logger: Logger,
        @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) private classInviteLinkCreator: ClassInviteLinkCreator,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(LOCATION_SERVICE_TYPES.LocationService) private locationService: ILocationService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CUREFIT_API_TYPES.FeatureStateCache) public featureStateCache: FeatureStateCache,
        @inject(REDIS_TYPES.RedisDao) private crudDao: ICrudKeyValue,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
        @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) private maxmindService: IMaxmindService,
    ) {
    }


    private getAvailableWorkoutDays(userContext: UserContext, classDates: string[], classByDateMap: { [id: string]: ClassByDateV2 }): { id: string }[] {
        // to process for live classes response
        let dates
        if (_.isEmpty(classDates) && !_.isEmpty(classByDateMap)) {
            dates = TimeUtil.getDays(userContext.userProfile.timezone, 4, false, "YYYY-MM-DD")
        } else {
            dates = classDates
        }
        const days: { id: string }[] = dates.map(classDate => {
            return {
                "id": classDate
            }
        })
        return days.filter(day => !_.isEmpty(classByDateMap[day.id]) && !_.isEmpty(classByDateMap[day.id].classByTimeList))
    }

    private getAvailableWorkoutCategoriesFilter(
        workoutCategories: CultWorkoutCategory[],
        isCultSportCategorySupported?: boolean,
        isBootcampCategorySupported?: boolean
    ): { id: number, name: string, displayText: string }[] {
        let workoutFilters: {
            id: number
            name: string
            displayText: string
        }[] = _.map(workoutCategories, workoutCategory => {
            return {
                id: workoutCategory.id,
                name: workoutCategory.name,
                displayText: workoutCategory.name
            }
        })

        if (!isCultSportCategorySupported) {
            const sportCategoryIds: number[] = process.env.ENVIRONMENT === "STAGE"
                ? CULT_SPORT_CATEGORY_STAGE_WORKOUT_CATEGORY : CULT_SPORT_CATEGORY_PROD_WORKOUT_CATEGORY

            workoutFilters = _.filter(workoutFilters, (workoutCategory) => {
                const workoutPresent: number = _.find(sportCategoryIds, (categoryId: number) => {
                    if (categoryId === workoutCategory.id) {
                        return true
                    }
                    return false
                })
                if (workoutPresent) {
                    return false
                }
                return true
            })
        }

        if (!isBootcampCategorySupported) {
            const bootcampCategoryIds: number[] = process.env.ENVIRONMENT === "STAGE"
                ? BOOTCAMP_CATEGORY_STAGE_WORKOUT_CATEGORY : BOOTCAMP_CATEGORY_PROD_WORKOUT_CATEGORY
            workoutFilters = _.filter(workoutFilters, (workoutCategory) => {
                const workoutPresent: number = _.find(bootcampCategoryIds, (categoryId: number) => {
                    return categoryId === workoutCategory.id
                })
                return !workoutPresent
            })
        }

        workoutFilters = workoutFilters.sort((a, b) => {
            // priority ordering
            const priorityForA = FILTERS_PRIORITY_MAP.get(a.id) || FILTERS_PRIORITY_MAP.get(0)
            const priorityForB = FILTERS_PRIORITY_MAP.get(b.id) || FILTERS_PRIORITY_MAP.get(0)

            return priorityForA - priorityForB
        })

        // shift 'Others' filter to last
        workoutFilters.push(workoutFilters.splice(workoutFilters.findIndex(workout => workout.name == "Others"), 1)[0])
        workoutFilters = workoutFilters.filter(workout => !!workout)

        return workoutFilters
    }

    private getWorkoutMap(workouts: CultWorkout[]): { [id: string]: CultWorkout } {
        const workoutMap: { [id: string]: CultWorkout } = {}
        workouts.forEach(workout => {
            workoutMap[workout.id] = workout
        })
        return workoutMap
    }


    private getTimeSlotOptions(): TimeSlotOption[] {
        return [
            {
                "slot": "morning",
                "icon": "morning",
                "displayText": "Morning",
                "startTime": "06:00:00"
            },
            {
                "slot": "evening",
                "icon": "evening",
                "displayText": "Evening",
                "startTime": "16:00:00"
            }
        ]
    }

    private async getCenterInfoMap(userContext: UserContext, centers: CultCenter[], productType: ProductType): Promise<{ [id: string]: CenterInfo }> {
        const tz = userContext.userProfile.timezone
        const centerInfoMap: { [id: string]: CenterInfo } = {}
        for (const center of centers) {
            centerInfoMap[center.id] = {
                centerName: center.name,
                verticalName: undefined,
                isNew: TimeUtil.diffInDaysReal(tz, TimeUtil.todaysDate(tz), center.launchDate) < 15
            }
        }
        centerInfoMap[HOME_CENTER_ID] = {
            centerName: "At Home",
            verticalName: LiveUtil.classFormatTitle(userContext, productType),
            isNew: false
        }
        centerInfoMap[HOME_CENTER_ID_MIND] = {
            centerName: "At Home",
            verticalName: "mind.live",
            isNew: false
        }
        centerInfoMap[HOME_CENTER_ID_EAT] = {
            centerName: "At Home",
            verticalName: "eat.live",
            isNew: false
        }
        return centerInfoMap
    }

    private async settingsToSchedulePageHeader(productType: ProductType, settings: { key: string, value: any }[], showCenterShutdownWarningIcon: boolean, centerShutdownDetailCards: CenterScheduleDetailCard[], centerName?: string, filterApplied?: string, locationPreference?: LocationPreferenceResponseEntity) {
        let title: string = undefined
        let toolTip: {
            text: string
            title?: string
            closeAction?: Action
        } = undefined
        if (!_.isEmpty(centerName)) {
            return {
                title: centerName
            }
        }
        if (_.isEmpty(settings)) {
            throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
        } else {
            if (locationPreference && !_.isEmpty(locationPreference)) {
                // get user locality preference from rashi and favorites from cult
                const settingMap = _.keyBy(settings, setting => setting.key)
                if (settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"]) {
                    title = `Favourites (${settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"].value.length})`
                } else {
                    const locationType = locationPreference.prefLocationType
                    if (!locationType) {
                        throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
                    }
                    switch (locationType) {
                        case LocationDataKey.LOCALITY:
                            title = locationPreference.locality
                            break
                        case LocationDataKey.COORDINATES:
                            const coordinatesLatLong: LatLong = locationPreference.coordinates
                            if (_.isNil(coordinatesLatLong) || !UserUtil.isValidCoordinates(coordinatesLatLong.lat, coordinatesLatLong.long) || _.isNil(locationPreference.coordinatesName)) {
                                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
                            }
                            title = locationPreference.coordinatesName
                            break
                        case LocationDataKey.CURRENT_LOC:
                            const latLong: LatLong = locationPreference.currentLocation
                            if (_.isNil(latLong) || !UserUtil.isValidCoordinates(latLong.lat, latLong.long)) {
                                throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
                            }
                            title = "Near You"
                            break
                        default:
                            throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
                    }
                }

            } else {
                const settingMap = _.keyBy(settings, setting => setting.key)
                if (settingMap["USER_BOOKING_V2_SETTINGS_STATE"] && settingMap["USER_BOOKING_V2_SETTINGS_STATE"].value === "system_generated"
                    && settingMap["USER_BOOKING_V2_LOCALITY"]) {
                    toolTip = {
                        text: "We have selected an area for your convenience. You can always edit it here.",
                        closeAction: {
                            actionType: "REST_API",
                            meta: {
                                method: "POST",
                                url: `/user/inAppNotification/${CULT_AREA_TOOL_TIP}`, // TODO: Fix this once we move to proper modelling of this
                                body: {"state": "CLOSED", productType: productType}
                            }
                        }
                    }
                }
                if (settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"]) {
                    title = `Favourites (${settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"].value.length})`
                } else if (settingMap["USER_BOOKING_V2_LAT_LONG"]) {
                    title = settingMap["USER_BOOKING_V2_LAT_LONG"].value.name
                } else if (settingMap["USER_BOOKING_V2_LOCALITY"]) {
                    title = settingMap["USER_BOOKING_V2_LOCALITY"].value // Lookup area name here
                    const locality = productType === "FITNESS" ? await this.cultFitService.localityById(settingMap["USER_BOOKING_V2_LOCALITY"].value, undefined, "CUREFIT-API")
                        : await this.mindFitService.localityById(settingMap["USER_BOOKING_V2_LOCALITY"].value, undefined, "CUREFIT-API")
                    title = `${locality.name}`
                } else {
                    throw new CenterNotSelectedError().throwError(this.errorFactory, "Please select a preference")
                }
            }

        }

        if (filterApplied === "true") {
            toolTip = {
                title: "FILTER APPLIED",
                text: "Showing workouts based on the recommendation. You can change it from here.",
            }
        }

        const header: any = {
            title: title,
            toolTip: toolTip
        }

        if (showCenterShutdownWarningIcon) {
            header["action"] = {
                title: "KNOW MORE",
                icon: CENTER_MAINTENANCE_WARNING,
                actionType: "SHOW_CAROUSEL_LIST",
                meta: {
                    type: "CENTER_SHUTDOWN",
                    bundleProducts: centerShutdownDetailCards
                }
            }
        }
        return header
    }

    getVideoWorkoutId(tags: string[]): number {
        let workoutId = -1
        _.forEach(tags, tag => {
            if (tag.startsWith("workoutId")) {
                workoutId = Number(tag.split("_")[1])
            }
        })
        return workoutId
    }

    isFreshClassLabel(classResponse: LiveClass, slotResponse: LiveClassSlot, userContext: UserContext) {
        if (slotResponse.isPremier === true) {
            return true
        }
        if (!classResponse.firstTelecastTimeEpoch) {
            return false
        }
        const dayDiff = TimeUtil.absDiffInDaysFromEpoch(slotResponse.scheduledTimeEpoch, classResponse.firstTelecastTimeEpoch)
        return dayDiff < 1
    }

    getLabels(classResponse: LiveClass, slotResponse: LiveClassSlot, userContext: UserContext, doesUserBelongToSchedulePageLabelExperiment: boolean) {
        const labels: Label[] = []
        if (this.isSpecialClass(classResponse)) {
            labels.push({
                icon: "masterclass",
                title: "New Program",
                textColor: "white",
                backgroundColor: "statusBlue"
            })
        } else if (LiveUtil.isConsideredInteractiveSessionForPresentation(classResponse.preferredStreamType) && classResponse.format === "AMA") {
            labels.push({
                icon: "chat",
                title: "AMA",
                textColor: "white",
                backgroundColor: "statusGreen"
            })
        } else if (LiveUtil.isConsideredInteractiveSessionForPresentation(classResponse.preferredStreamType)) {
            const isClassSlotIconUrlSupported = AppUtil.isClassSlotIconUrlSupported(userContext)
            labels.push({
                icon: !isClassSlotIconUrlSupported ? "live-interactive" : undefined,
                title: "INTERACTIVE",
                textColor: "white",
                backgroundColor: "statusGreen",
                iconUrl: isClassSlotIconUrlSupported ? INTERACTIVE_ICON_URL : undefined
            })
        } else if (doesUserBelongToSchedulePageLabelExperiment && LiveUtil.isVodSession(classResponse.preferredStreamType) && this.isFreshClassLabel(classResponse, slotResponse, userContext)) {
            labels.push({
                icon: null,
                title: "NEW",
                textColor: "white",
                backgroundColor: "statusBlue"
            })
        }
        return labels
    }

    getSortPreference(classResponse: LiveClass) {
        if (this.isSpecialClass(classResponse)) {
            return MAX_SORT_PREFERENCE - 1
        }
        return undefined
    }

    isSpecialClass(classResponse: LiveClass) {
        return classResponse.title.toLowerCase().includes("dance-a-thon")
    }

    async getButtonType(classResponse: LiveClass, slotResponse: LiveClassSlot, userContext: UserContext, doesUserBelongToSchedulePageLabelExperiment: boolean): Promise<string> {
        if (LiveUtil.isVodSession(classResponse.preferredStreamType) && this.isFreshClassLabel(classResponse, slotResponse, userContext)) {
            if (doesUserBelongToSchedulePageLabelExperiment) {
                return "LABEL_NEW_TEST"
            } else {
                return "LABEL_NEW_CONTROL"
            }
        }
        const labels = this.getLabels(classResponse, slotResponse, userContext, doesUserBelongToSchedulePageLabelExperiment)
        const buttonType = labels.map((l) => ("LABEL_" + l.title?.toUpperCase().replace(" ", "_"))).join()
        return buttonType
    }

    async getLiveClassScheduleResponse(userContext: UserContext, user: User, centerPositionByIdMap: { [id: number]: number }, classByDateMap: { [id: string]: ClassByDateV2 }, classByDateTimeMap: { [id: string]: ClassByTimeV2 }, classByDateTimeCenterMap: { [id: string]: ClassByCenter }, productType: ProductType, liveClassWorkCategoryFilters: any[], liveClassFormatFilter: any[], isUserEligibleForMonetisation: boolean, isUserEligibleForLiveTrialPack: boolean, orderSource: OrderSource, bucketId: string, blockInternationalUser: boolean, queryParams?: any): Promise<boolean> {
        let liveClassAvailable: boolean = false
        let isMasterClassPresentInAnyClass: boolean = false
        let isInteractiveClassPresentInAnyClass: boolean = false
        const tz = userContext.userProfile.timezone
        const countryId = AppUtil.getCountryId(userContext)
        const formats = LiveUtil.getFormatsBasedOnProductType(productType)
        const formatFilterList: any[] = []
        formats.forEach(format => formatFilterList.push(format))
        const filters: any = {formats}
        if (!_.isNil(queryParams)) {
            if (!_.isNil(queryParams.trainerId))
                filters.trainerIds = [queryParams.trainerId]
            if (!_.isNil(queryParams.formatId))
                filters.formats = [queryParams.formatId]
        }
        // const expId = process.env.ENVIRONMENT === "STAGE" ? "415" : "855"
        // const hamletExperimentMap = await this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [expId]))
        // const userAssignment = hamletExperimentMap ? hamletExperimentMap.assignmentsMap[expId] : undefined
        const doesUserBelongToSchedulePageLabelExperiment = true

        const allLiveClassesResponse: LiveClass[] = await this.diyFulfilmentService.getUpcomingLiveClassesV2(user.id, AppUtil.getTenantFromUserContext(userContext), filters, countryId)       // const subscribedVideos: string[] = await this.diyFulfilmentService.getSubscribedVideos(user.id)
        const isLiveQnASupported = await AppUtil.isLiveQnASupported(userContext)
        const liveClassesResponse = LiveUtil.filterInteractiveSessionOrRealLiveWhenNotSupported(allLiveClassesResponse, isLiveQnASupported, userContext)
        let liveClasses: ClassViewV2[] = []
        const masterClassWorkoutCategoriesMap: Map<string, { id: number, name: string, displayText: string }> = new Map<string, { id: number, name: string, displayText: string }>()
        for (let i = 0; i < liveClassesResponse.length; i++) {
            const videoResponse = liveClassesResponse[i]
            const format = videoResponse.format
            if (_.includes(videoResponse.tags, "TEST_SESSION") && !user.isInternalUser) {
                continue
            }
            const videoSlots: ClassViewV2[] = []
            for (let i = 0; i < videoResponse.slots.length; i++) {
                if (!_.includes(formatFilterList, videoResponse.format)) {
                    formatFilterList.push(videoResponse.format)
                }
                const slot = videoResponse.slots[i]
                let slotAction = null
                const buttonType = await this.getButtonType(videoResponse, slot, userContext, doesUserBelongToSchedulePageLabelExperiment)
                const buttonAction = LiveUtil.getLiveClassSlotButtonAction(userContext, videoResponse, slot, "class_booking_slot", isUserEligibleForMonetisation, isUserEligibleForLiveTrialPack, user, bucketId, blockInternationalUser, buttonType)
                const isBooked = slot.subscriptionStatus === "SUBSCRIBED"
                slotAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(videoResponse.liveClassId, slot.classId, "class_booking_slot", videoResponse.contentCategory, userContext).url
                slotAction = (!isBooked && AppUtil.isTVAppWithOrderSource(orderSource)) ? `curefit://bookClass?liveClassId=${videoResponse.liveClassId}` : slotAction
                const formats = LiveUtil.getFormatsArrayForLiveClass(videoResponse)
                const intensity = videoResponse.intensityLevel ? videoResponse.intensityLevel.charAt(0).toUpperCase() + videoResponse.intensityLevel.slice(1).toLowerCase() : ``
                const classResponse: ClassViewV2 = {
                    id: slot.classId,
                    productType: "LIVE_FITNESS",
                    date: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch, "yyyy-MM-dd"),
                    startTime: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch, "HH:mm:ss"), // "06:00:00",
                    endTime: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch + videoResponse.duration, "HH:mm:ss"), // "06:50:00",
                    workoutId: <any>slot.classId,
                    workoutCategoryId: this.getVideoWorkoutId(videoResponse.tags),
                    centerID: 0, // Same centerID for correct freemium sorting
                    availableSeats: 2,
                    workoutName: videoResponse.title,
                    state: isBooked ? "BOOKED" : "AVAILABLE",
                    isBooked,
                    action: slotAction,
                    buttonAction: buttonAction,
                    shareAction: LiveUtil.isVanillaLiveFitFormat(formats[0]) ? undefined :
                        LiveUtil.getInviteBuddyLazyLoadAction(slot.classId, {source: "classbookingpageshare"}),
                    trainerName: _.isEmpty(intensity) ? `${videoResponse.trainerName}` : `${videoResponse.trainerName} |  ${intensity}`,
                    formattedTimeString: LiveUtil.getFormattedTimeString(videoResponse.duration),
                    format: formats,
                    tags: videoResponse.videoCallMeta?.uiTags,
                    labels: this.getLabels(videoResponse, slot, userContext, doesUserBelongToSchedulePageLabelExperiment),
                    tagColor: LiveUtil.getTagColorForFormat(videoResponse.format),
                    isLocked: AppUtil.isLiveContentLocked(userContext, slot.locked, isUserEligibleForMonetisation, isUserEligibleForLiveTrialPack),
                    sortPreference: this.getSortPreference(videoResponse)
                }
                if (LiveUtil.isConsideredInteractiveSessionForPresentation(videoResponse.preferredStreamType)) {
                    isInteractiveClassPresentInAnyClass = true
                }
                if (videoResponse.isMasterClass) {
                    if (!isMasterClassPresentInAnyClass) {
                        isMasterClassPresentInAnyClass = true
                        formatFilterList.unshift(masterClassFormat)
                    }
                    const workoutId = videoResponse.trainerName ? videoResponse.trainerName.replace(/\s/g, "_").toLowerCase() : videoResponse.liveClassId
                    classResponse.workoutCategoryId = <any>workoutId
                    if (!masterClassWorkoutCategoriesMap.has(workoutId)) {
                        const workOutFilter = {
                            id: <any>workoutId,
                            name: "Masterclass with " + videoResponse.trainerName,
                            displayText: "Masterclass with " + videoResponse.trainerName
                        }
                        masterClassWorkoutCategoriesMap.set(workoutId, workOutFilter)
                    }
                }
                videoSlots.push(classResponse)
            }

            liveClasses = [...liveClasses, ...videoSlots]
            if (liveClasses.length > 0) {
                liveClassAvailable = true
            }
        }
        if (AppUtil.isInteractiveFilterSupported(userContext) && isInteractiveClassPresentInAnyClass) {
            formatFilterList.unshift(liveInteractiveClassFormat)
        }
        // changing the response for sending formats to {id, name} because id can be an non-UX friendly text
        if (AppUtil.formatFilterNameSupport(userContext) || AppUtil.isWeb(userContext) || AppUtil.isTVApp(userContext)) {
            formatFilterList.unshift(allFormats)
            formatFilterList.forEach(format => {
                liveClassFormatFilter.push({
                    id: format,
                    name: LiveUtil.getFormatName(format),
                    icon: LiveUtil.getFormatIcon(format)
                })
            })
        } else {
            formatFilterList.forEach(format => {
                liveClassFormatFilter.push(format)
            })
        }

        if (liveClasses.length > 0) {
            liveClassAvailable = true
        }

        if (liveClassAvailable && _.isEmpty(classByDateMap)) {
            const dates = TimeUtil.getDays(userContext.userProfile.timezone, 4, false, "YYYY-MM-DD")
            dates.forEach(date => {
                const classByDateV2: ClassByDateV2 = {
                    id: date,
                    classByTimeList: [],
                    timeSlots: [],
                    formats: [allFormats]
                }
                classByDateMap[date] = classByDateV2
            })
            Array.from(masterClassWorkoutCategoriesMap.keys()).forEach((key) => {
                liveClassWorkCategoryFilters.push(masterClassWorkoutCategoriesMap.get(key))
            })
            // this.logger.info("live class workout filters: " + JSON.stringify(liveClassWorkCategoryFilters))
        }

        for (const liveClassDetail of liveClasses) {
            const classByDate = classByDateMap[liveClassDetail.date]
            if (classByDate) {  // if a class exists on that given date i.e., if it is a valid date
                classByDate.formats = _.isEmpty(classByDate.formats) ? [allFormats] : classByDate.formats
                classByDate.formats = _.isEmpty(liveClassDetail.format) ? classByDate.formats : classByDate.formats.concat(liveClassDetail.format)
                let classByTime: ClassByTimeV2 = classByDateTimeMap[liveClassDetail.date + "_" + liveClassDetail.startTime] // checking if time slot of this session exists
                if (!classByTime) {
                    classByTime = {
                        id: liveClassDetail.startTime,
                        bookedCenterPosition: -1,
                        uniqueFavouriteCenters: new Set<number>(),
                        maxExpandedCenters: DEFAULT_MAX_EXPANDED_CENTERS,
                        centerWiseClasses: []
                    }
                    classByDateTimeMap[liveClassDetail.date + "_" + liveClassDetail.startTime] = classByTime    // creating a slot for that time so that we can add our class now
                    classByDate.classByTimeList.push(classByTime)
                }
                const centerId = Number(liveClassDetail.centerID)   // now checking center for this class
                let classByCenter = classByDateTimeCenterMap[liveClassDetail.date + "_" + liveClassDetail.startTime + "_" + centerId] // checking if class exists for this date time and center
                if (!classByCenter) {   // if not, create an entry by center, we will push our class to this
                    classByCenter = {
                        centerId: centerId,
                        classes: []
                    }
                    classByDateTimeCenterMap[liveClassDetail.date + "_" + liveClassDetail.startTime + "_" + centerId] = classByCenter
                    classByTime.centerWiseClasses.push(classByCenter)
                    // apply sorting in order of center rank as coming from backend
                    classByTime.centerWiseClasses.sort((a: ClassByCenter, b: ClassByCenter) => {
                        return centerPositionByIdMap[a.centerId] < centerPositionByIdMap[b.centerId] ? -1 : 1
                    })
                }
                classByCenter.classes.push(liveClassDetail)

                if (liveClassDetail.isBooked) {
                    classByTime.bookedCenterPosition = classByTime.centerWiseClasses.length
                }
                /**** Logic for calculating maxExpandedCenters
                 // Case 1: Area/Near Me view -> always show DEFAULT_MAX_EXPANDED_CENTERS in expanded state
                 // Case 2: Favourites View -> Always show all centers as expanded in favourites view
                 // Note: booked class slot should always be in expanded state ****/

                // // Get the booked class slot center position in center ranking map, so that we can expand the slots upto booked center position
                // if (!_.isNil(liveClassDetail.bookingNumber)) {
                //     classByTime.bookedCenterPosition = centerPositionByIdMap[centerId]
                // }

                // // To show booked class center in expanded state, expand the timeslot upto bookedCenterPosition if bookedCenterPosition is greater than maxExpandedCenters
                classByTime.maxExpandedCenters = Math.max(classByTime.uniqueFavouriteCenters.size, classByTime.maxExpandedCenters, classByTime.bookedCenterPosition + 1)
            }
        }
        return liveClassAvailable
    }

    async getIntlLiveClassScheduleResponse(
        userContext: UserContext,
        user: User,
        classByDateMap: { [id: string]: ClassByDateV2 },
        classByDateTimeMap: { [id: string]: ClassByTimeV2 },
        classByDateTimeCenterMap: { [id: string]: ClassByCenter },
        productType: ProductType,
        liveClassWorkCategoryFilters: any[],
        isUserEligibleForMonetisation: boolean,
        isUserEligibleForLiveTrialPack: boolean,
        classes?: LiveClass[]
    ): Promise<boolean> {
        let liveClassAvailable: boolean = false
        const tz = userContext.userProfile.timezone
        const countryId = AppUtil.getCountryId(userContext)
        const formats = LiveUtil.getFormatsBasedOnProductType(productType)
        const formatFilterList: any[] = []
        formats.forEach(format => formatFilterList.push(format))
        const allLiveClasses: LiveClass[] = classes || (
            await this.diyFulfilmentService.getUpcomingLiveClasses(
                user.id,
                AppUtil.getTenantFromUserContext(userContext),
                formats,
                countryId
            )
        )
        const isLiveQnASupported = await AppUtil.isLiveQnASupported(userContext)
        const liveClassesResponse = LiveUtil.filterInteractiveSessionOrRealLiveWhenNotSupported(allLiveClasses, isLiveQnASupported, userContext)
        let liveClasses: ClassViewV2[] = []
        for (let i = 0; i < liveClassesResponse.length; i++) {
            const videoResponse = liveClassesResponse[i]
            const format = videoResponse.format
            if (_.includes(videoResponse.tags, "TEST_SESSION") && !user.isInternalUser) {
                continue
            }
            const videoSlots: ClassViewV2[] = []
            for (let i = 0; i < videoResponse.slots.length; i++) {
                if (!_.includes(formatFilterList, videoResponse.format)) {
                    formatFilterList.push(videoResponse.format)
                }
                const slot = videoResponse.slots[i]
                let slotAction = null
                const buttonAction = LiveUtil.getLiveClassSlotButtonAction(userContext, videoResponse, slot, "tl_live_class", isUserEligibleForMonetisation, isUserEligibleForLiveTrialPack, user, "", false)
                const isLocked = AppUtil.isLiveContentLocked(userContext, slot.locked, isUserEligibleForMonetisation, isUserEligibleForLiveTrialPack)
                const isBooked = slot.subscriptionStatus === "SUBSCRIBED"
                slotAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(videoResponse.liveClassId, slot.classId, "tl_live_class", videoResponse.contentCategory, userContext)
                const trainers = videoResponse.derivedInfo?.primaryTrainersInfo?.map?.(trainer => {
                    return ({
                        name: trainer.name,
                        image: trainer.profileImageUrl,
                        id: trainer.instructorId
                    })
                })
                const isLive: boolean = slot.status === "LIVE"

                if (isBooked && !isLive) {
                    buttonAction.title = "BOOKED"
                }

                const classResponse: ClassViewV2 = {
                    id: slot.classId,
                    productType: "LIVE_FITNESS",
                    date: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch, "yyyy-MM-dd"),
                    startTime: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch, "HH:mm:ss"),
                    endTime: TimeUtil.formatEpochInTimeZoneDateFns(tz, slot.scheduledTimeEpoch + videoResponse.duration, "HH:mm:ss"),
                    workoutId: <any>slot.classId,
                    workoutCategoryId: this.getVideoWorkoutId(videoResponse.tags),
                    centerID: LIVE_MIND_FORMATS.has(format) ? HOME_CENTER_ID_MIND : LIVE_EAT_FORMATS.has(format) ? HOME_CENTER_ID_EAT : HOME_CENTER_ID,
                    availableSeats: 2,
                    workoutName: videoResponse.title,
                    state: isBooked ? "BOOKED" : "AVAILABLE",
                    isBooked,
                    action: slotAction.url,
                    cardAction: slotAction,
                    buttonAction: buttonAction,
                    trainerName: videoResponse.trainerName,
                    formattedTimeString: LiveUtil.getFormattedTimeString(videoResponse.duration),
                    isLocked,
                    trainers,
                    workoutFormat: videoResponse.format,
                    intensityLevel: videoResponse.intensityLevel,
                    isLive,
                    tags: videoResponse.videoCallMeta?.uiTags,
                    image: LiveUtil.isInteractiveSession(videoResponse.preferredStreamType) && videoResponse.tlpHeaderImage,
                    imageAspectRatio: 2.08
                }
                videoSlots.push(classResponse)
            }

            liveClasses = [...liveClasses, ...videoSlots]
        }

        if (liveClasses.length > 0) {
            liveClassAvailable = true
        }

        if (liveClassAvailable && _.isEmpty(classByDateMap)) {
            const dates = TimeUtil.getDays(userContext.userProfile.timezone, 4, false, "YYYY-MM-DD")
            dates.forEach(date => {
                const classByDateV2: ClassByDateV2 = {
                    id: date,
                    classByTimeList: [],
                    formats: [allFormats]
                }
                classByDateMap[date] = classByDateV2
            })
            // this.logger.info("live class workout filters: " + JSON.stringify(liveClassWorkCategoryFilters))
        }

        for (const liveClassDetail of liveClasses) {
            const classByDate = classByDateMap[liveClassDetail.date]
            if (classByDate) {  // if a class exists on that given date i.e., if it is a valid date
                classByDate.formats = _.isEmpty(classByDate.formats) ? [allFormats] : classByDate.formats
                classByDate.formats = _.isEmpty(liveClassDetail.workoutFormat) ? classByDate.formats : classByDate.formats.concat(liveClassDetail.workoutFormat)
                let classByTime: ClassByTimeV2 = classByDateTimeMap[liveClassDetail.date + "_" + liveClassDetail.startTime] // checking if time slot of this session exists
                if (!classByTime) {
                    classByTime = {
                        id: liveClassDetail.startTime,
                        centerWiseClasses: []
                    }
                    classByDateTimeMap[liveClassDetail.date + "_" + liveClassDetail.startTime] = classByTime    // creating a slot for that time so that we can add our class now
                    classByDate.classByTimeList.push(classByTime)
                }
                const centerId = Number(liveClassDetail.centerID)   // now checking center for this class
                let classByCenter = classByDateTimeCenterMap[liveClassDetail.date + "_" + liveClassDetail.startTime + "_" + centerId] // checking if class exists for this date time and center
                if (!classByCenter) {   // if not, create an entry by center, we will push our class to this
                    classByCenter = {
                        centerId: centerId,
                        classes: []
                    }
                    classByDateTimeCenterMap[liveClassDetail.date + "_" + liveClassDetail.startTime + "_" + centerId] = classByCenter
                    classByTime.centerWiseClasses.push(classByCenter)
                }
                classByCenter.classes.push(liveClassDetail)
            }

            for (const classByDate of Object.values(classByDateMap)) {
                classByDateMap[classByDate.id].classByTimeList = classByDate.classByTimeList.sort((a: ClassByTimeV2, b: ClassByTimeV2) => {
                    return a.id < b.id ? -1 : 1
                })
            }
        }
        return liveClassAvailable
    }


    private addBookingScreenNudge(date: string, currentDate: string, bookingPageDateAction: Action, bookingScreenNudges: any, classByDateMap: { [id: string]: ClassByDateV2 }, isNuxFlow?: boolean): Action {
        if (bookingScreenNudges && bookingScreenNudges.widgetType !== "CULT_CLASS_RECOMMENDATION_WIDGET_V1") {
            if (["NUX_FILTER", "FILTER"].includes(bookingScreenNudges.widgetType)) {
                if (date === currentDate) {
                    const {widgetData, widgetType, position} = bookingScreenNudges
                    const classByTimeListLength = classByDateMap[date].classByTimeList.length
                    if (classByTimeListLength > 0) {
                        const widgetPosition = Math.ceil(classByTimeListLength * position * 0.01)
                        classByDateMap[date].classByTimeList.splice(widgetPosition, 0, {
                            id: widgetType,
                            widgetType,
                            widgetData
                        })
                    }
                }
            } else {
                const {widgetData, widgetType, position} = bookingScreenNudges
                const classByTimeListLength = classByDateMap[date].classByTimeList.length
                if (classByTimeListLength > 0) {
                    const widgetPosition = Math.ceil(classByTimeListLength * position * 0.01)
                    classByDateMap[date].classByTimeList.splice(widgetPosition, 0, {
                        id: widgetType,
                        widgetType,
                        widgetData
                    })
                }
            }
        } else if (bookingScreenNudges && bookingScreenNudges.widgetType === "CULT_CLASS_RECOMMENDATION_WIDGET_V1") {
            const {widgetData, widgetType, position} = bookingScreenNudges
            if (widgetData.classDetails[date]) {
                const classByTimeListLength = classByDateMap[date].classByTimeList.length
                if (classByTimeListLength > 0) {
                    const updateWidgetData = {
                        ...widgetData,
                        classDetails: widgetData.classDetails[date]
                    }
                    const widgetPosition = Math.ceil(classByTimeListLength * position * 0.01)
                    classByDateMap[date].classByTimeList.splice(widgetPosition, 0, {
                        id: widgetType,
                        widgetType,
                        widgetData: updateWidgetData
                    })
                    if (!_.isEmpty(bookingPageDateAction) && _.isEmpty(bookingPageDateAction.meta)) {
                        bookingPageDateAction.meta = {
                            selectedDate: date,
                            toastMessage: "Changing dates to find the best suitable class"
                        }
                    }
                }
            } else if (isNuxFlow && date === currentDate && _.isEmpty(widgetData.classDetails[date])) {
                bookingPageDateAction = {
                    actionType: "SET_BOOKING_PAGE_DATE",
                    meta: {}
                }
            }
        }
        return bookingPageDateAction
    }

    async buildView(userContext: UserContext,
                    productType: ProductType,
                    classesResponse: CultClassesResponseV2,
                    centerViewOnly?: boolean,
                    filterApplied?: string,
                    isLiveBookingPage?: boolean,
                    rescheduleClassBookingResponse?: CultBookingResponse, orderSource?: OrderSource,
                    bookingScreenNudgeEternalPromise?: Promise<{ obj: any, err?: any }>,
                    isNuxFlow?: boolean,
                    queryParams?: any, squadData?: SquadClassBookingLite[], oneDaySchedule?: boolean, cultCityName?: string, widgetResponse?: any,
                    filterWorkoutId?: number, isCultUnbound?: boolean): Promise<ClassScheduleResponse> {
        const user = await userContext.userPromise
        if (isLiveBookingPage) {
            classesResponse.classes = []
        }
        let favouriteCenters: number[] = []
        let settingMap
        if (!_.isEmpty(classesResponse.userSettings)) {
            settingMap = _.keyBy(classesResponse.userSettings, setting => setting.key)
            if (settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"]) {
                favouriteCenters = settingMap["USER_BOOKING_V2_FAVOURITE_CENTER"].value
            }
        }
        const classByDateMap: { [id: string]: ClassByDateV2 } = {}
        const classByDateTimeMap: { [id: string]: ClassByTimeV2 } = {}
        const classByDateTimeCenterMap: { [id: string]: ClassByCenter } = {}
        const centerPositionByIdMap: { [id: number]: number } = {}
        const centerInfoByIdMap: { [id: number]: any } = {}
        let dayWiseWidget: NoshowBookingBlockedWidget = null
        if (classesResponse.noShowDetails) {
            dayWiseWidget = this.getNoShowBookingBlockedWidget(classesResponse.noShowDetails, userContext)
        }

        const classDates = AppUtil.isInternationalApp(userContext)
            ? TimeUtil.getDays(userContext.userProfile.timezone, 4, false, "YYYY-MM-DD")
            : classesResponse.dates
        classDates.forEach(date => {
            const classByDateV2: ClassByDateV2 = {
                id: date,
                classByTimeList: [],
                dayWiseWidget: classesResponse?.noShowDetails?.bookingBlockedDates.includes(date) ? dayWiseWidget : undefined
            }
            classByDateMap[date] = classByDateV2
        })

        // build a map of all center ids with their order in list
        _.forEach(classesResponse.centers, (center, index) => {
            centerPositionByIdMap[center.id] = index
            centerInfoByIdMap[center.id] = center
        })
        const isWaitlistConfirmationProbabilitySupported = await AppUtil.isWaitlistConfirmationSupported(userContext, this.hamletBusiness)
        const isCultSportCategorySupported: boolean = await CultUtil.isCultSportsCategorySupported(userContext, this.hamletBusiness)
        const isBaddyFitSupporeted: boolean = await CultUtil.isBaddyFitSupported(userContext, this.hamletBusiness)
        const isCultRunSupported: boolean = await CultUtil.isCultRunSupported(userContext, this.hamletBusiness)
        let isBootcampSupported: boolean = await CultUtil.iBootcampBookingSupported(userContext, this.serviceInterfaces)
        const isNewBookingFlow: boolean = true
        let showSportCategoryInfoWidget: boolean = false

        const cityId = userContext?.userProfile?.cityId ?? cultCityName
        const locationPreference: LocationPreferenceResponseEntity = await this.userBusiness.getLocationPreference(userContext, userContext.userProfile.userId, cityId)

        let blockInternationalUser = false
        try {
            blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, this.maxmindService, this.logger)
        } catch {
            this.logger.error("IP address location detection failed for user: " + userContext.userProfile.userId)
        }

        const cultAndMindSummary = await this.userCache.getCultSummary(userContext.userProfile.userId)

        const workoutMap = this.getWorkoutMap(classesResponse.workouts)

        const squadsDataByClass = await this.processSquadData(userContext, squadData, 5)

        const [isBootcampBookingExperimentSupported, isBootcampImpressionCompleted, showBootcampToolTip]: any = await Promise.all([
            AppUtil.isBootcampBookingExperimentSupported(userContext, this.hamletBusiness),
            TransformUtil.isBootcampImpressionCompleted(userContext.userProfile.userId, this.crudDao),
            TransformUtil.isBootcampToolTipVisible(userContext.userProfile.userId, this.crudDao)
        ])

        let workoutLevelToolTip = false
        let recommendationLevel: number = null
        if (await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, this.segmentService)) {
            recommendationLevel = await CultUtil.getUserRecommendationLevel(user.id, this.userAttributeClient)
        }
        this.logger.debug(`recomendationTest`, { recommendationLevel })
        for (const classDetail of classesResponse.classes) {
            // Don't add class if sport category is not supported
            if (!isCultSportCategorySupported) {
                const sportsCategoryCenters = await CultUtil.getSportCategoryCenterIds()
                const isSportsCenter: number = _.find(sportsCategoryCenters, (centerId) => Number(classDetail.centerID) === centerId)
                if (isSportsCenter) {
                    continue
                }

                const sportsCategoryWorkout = await CultUtil.getSportCategoryWorkoutIds()
                const isSportsWorkout: number = _.find(sportsCategoryWorkout, (workoutId) => Number(classDetail.workoutID) === workoutId)
                if (isSportsWorkout) {
                    continue
                }
            }

            if (!isBaddyFitSupporeted) {
                const baddyFitWorkout = await CultUtil.getBaddyFitWorkoutIds()
                const isBaddyFitWorkout: number = _.find(baddyFitWorkout, (workoutId) => Number(classDetail.workoutID) === workoutId)
                if (isBaddyFitWorkout) {
                    continue
                }
            }

            if (!isCultRunSupported) {
                const cultRunWorkout = await CultUtil.getCultRunWorkoutIds()
                const isCultRunWorkout: number = _.find(cultRunWorkout, (workoutId) => Number(classDetail.workoutID) === workoutId)
                if (isCultRunWorkout) {
                    continue
                }
            }

            const bootcampWorkout = await CultUtil.getBootcampWorkoutIds()
            const isBootcampWorkout: number = _.find(bootcampWorkout, (workoutId) => Number(classDetail.workoutID) === workoutId)
            if (!isBootcampSupported) {
                if (isBootcampWorkout) {
                    continue
                }
            }
            const showBootcampClasses: boolean = !_.isNil(isBootcampWorkout) && isBootcampSupported
            if (showBootcampClasses) {
                if (isBootcampBookingExperimentSupported) {
                    if (isBootcampImpressionCompleted) {
                        isBootcampSupported = false
                        continue
                    }
                } else {
                    isBootcampSupported = false
                    continue
                }
            }

            const classByDate = classByDateMap[classDetail.date]
            if (classByDate) {
                let classByTime: ClassByTimeV2 = classByDateTimeMap[classDetail.date + "_" + classDetail.startTime]
                if (!classByTime) {
                    classByTime = {
                        id: classDetail.startTime,
                        bookedCenterPosition: -1,
                        uniqueFavouriteCenters: new Set<number>(),
                        maxExpandedCenters: DEFAULT_MAX_EXPANDED_CENTERS,
                        centerWiseClasses: [],
                        isLive: false
                    }
                    classByDateTimeMap[classDetail.date + "_" + classDetail.startTime] = classByTime
                    classByDate.classByTimeList.push(classByTime)
                }
                const centerId = Number(classDetail.centerID)
                // for a center which is favourite center, add it to unique favourite list of that time slot to calculate maxExpandedCenters
                if (!_.isEmpty(favouriteCenters) && favouriteCenters.indexOf(centerId) > -1) {
                    classByTime.uniqueFavouriteCenters.add(centerId)
                }
                let classByCenter = classByDateTimeCenterMap[classDetail.date + "_" + classDetail.startTime + "_" + centerId]
                if (!classByCenter) {
                    classByCenter = {
                        centerId: centerId,
                        classes: [],
                        distance: null
                    }
                    classByDateTimeCenterMap[classDetail.date + "_" + classDetail.startTime + "_" + centerId] = classByCenter
                    classByTime.centerWiseClasses.push(classByCenter)

                    if (locationPreference) {
                        const locationType = locationPreference.prefLocationType
                        if (locationType && (locationType == LocationDataKey.COORDINATES || locationType == LocationDataKey.CURRENT_LOC)) {
                            const currentCenter = centerInfoByIdMap[centerId]
                            if (currentCenter.distance) {
                                classByCenter.distance = `${currentCenter.distance.toFixed(1)} km`
                            }
                        }
                    }
                    if (classDetail.creditCost) {
                        classByCenter.creditCost = classDetail.creditCost
                    }

                    // apply sorting in order of center rank as coming from backend
                    classByTime.centerWiseClasses.sort((a: ClassByCenter, b: ClassByCenter) => {
                        return centerPositionByIdMap[a.centerId] < centerPositionByIdMap[b.centerId] ? -1 : 1
                    })
                }

                let showWorkoutLevelToolTip = false
                try {
                    if ((classDetail.workoutID == DANCE_FITNESS_REVAMP_WORKOUT_ID
                        || classDetail.workoutID == STRENGTH_REVAMP_WORKOUT_ID) &&
                        !workoutLevelToolTip) {
                        workoutLevelToolTip = true
                        showWorkoutLevelToolTip = await CultUtil.isCultFormatImpressionCompleted(userContext.userProfile.userId, classDetail.workoutID , this.crudDao)
                            && await CultUtil.checkStateOfCultFormatImpressionCount(userContext.userProfile.userId, classDetail.workoutID, this.crudDao)
                        if (showWorkoutLevelToolTip) {
                            const response = await CultUtil.increaseCultFormatImpressionCount(userContext.userProfile.userId, classDetail.workoutID, this.crudDao)
                        }
                    }
                } catch (error) {
                    this.logger.error("Error while forming new dance fitness tool tip", error)
                    this.serviceInterfaces.rollbarService.sendError(error)
                }

                const classWorkoutLevel = CultUtil.getWorkoutRecommendationLevel(workoutMap[classDetail.workoutID].name)
                let isClassRecommended = null
                if (recommendationLevel) {
                    isClassRecommended = (recommendationLevel === classWorkoutLevel)
                }
                this.logger.debug(`recomendationTest:`, {recommendationLevel, isClassRecommended, classWorkoutLevel, workoutId: classDetail.workoutID, workoutMap})

                // const segments: string[] = await this.serviceInterfaces.clsUtil.getPlatformSegments()
                // const isMiniGoalModalShown = await this.serviceInterfaces.featureStateCache.match(userContext.userProfile.userId, "mini_goal_info_key", "shown")
                // const isUserEligibleForMiniGoal = await CultUtil.isUserEligibleForMiniGoal(userContext, this.serviceInterfaces, classDetail.workoutID)
                //     && !_.isEmpty(segments) && (segments.includes(HRX_USER_D30))
                // const isMiniGoalModalNotShownForEligibleUser = isUserEligibleForMiniGoal && !isMiniGoalModalShown
                const getwaitlistCnfProbabilityColor = await AppUtil.isAppNewWaitlistColorCodingSupported(this.serviceInterfaces.segmentService, userContext)
                this.logger.debug("classDetails", classDetail)
                classByCenter.classes.push(new ClassViewV2(classDetail, workoutMap, getwaitlistCnfProbabilityColor, false, isWaitlistConfirmationProbabilitySupported, isNewBookingFlow, squadsDataByClass, this.userCache, showBootcampToolTip, showWorkoutLevelToolTip, null, isClassRecommended, null, userContext, this.serviceInterfaces))
                if (classDetail.wlConfirmationThreshold !== undefined && classDetail.isWaitlistAvailable && isWaitlistConfirmationProbabilitySupported) {
                    classByDate.wlOnboarding = true
                }

                if (!showSportCategoryInfoWidget && classByCenter.classes[classByCenter.classes.length - 1].isNewClass) {
                    showSportCategoryInfoWidget = true
                }

                /**** Logic for calculating maxExpandedCenters
                 // Case 1: Area/Near Me view -> always show DEFAULT_MAX_EXPANDED_CENTERS in expanded state
                 // Case 2: Favourites View -> Always show all centers as expanded in favourites view
                 // Note: booked class slot should always be in expanded state ****/

                // Get the booked class slot center position in center ranking map, so that we can expand the slots upto booked center position
                if (!_.isNil(classDetail.bookingNumber)) {
                    classByTime.bookedCenterPosition = centerPositionByIdMap[centerId]
                }

                // To show booked class center in expanded state, expand the timeslot upto bookedCenterPosition if bookedCenterPosition is greater than maxExpandedCenters
                classByTime.maxExpandedCenters = Math.max(classByTime.uniqueFavouriteCenters.size, classByTime.maxExpandedCenters, classByTime.bookedCenterPosition + 1)
            }
        }

        let liveClassAvailable = false
        const liveClassWorkCategoryFilters: any[] = []
        const liveClassFormatFilter: any[] = []
        if (isLiveBookingPage) {
            const {
                isUserEligibleForMonetisation,
                isUserEligibleForTrial
            } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, this.hamletBusiness)
            liveClassAvailable = await this.getLiveClassScheduleResponse(userContext, user, centerPositionByIdMap, classByDateMap, classByDateTimeMap, classByDateTimeCenterMap, productType, liveClassWorkCategoryFilters, liveClassFormatFilter, isUserEligibleForMonetisation, isUserEligibleForTrial, orderSource, bucketId, blockInternationalUser, queryParams)
        }

        let isWaitlistOnboardingEnabled = 0
        // tslint:disable-next-line:prefer-const
        let setBookingPageDateAction: Action
        const datesArray = Object.keys(classByDateMap)
        const tz = userContext.userProfile.timezone
        const moment = momentTz.tz(tz)
        const currentDate = moment.format("YYYY-MM-DD")
        const currentHour = moment.format("HH")
        const currentMin = moment.format("mm")
        let bookingScreenNudges
        if (bookingScreenNudgeEternalPromise) {
            bookingScreenNudges = (await bookingScreenNudgeEternalPromise).obj
        }
        for (const date of datesArray) {
            const classByDate = classByDateMap[date]
            const classesByTime: ClassByTimeV2[] = classByDate.classByTimeList
            const formats = classByDate.formats
            if (_.isEmpty(classByDate.timeSlots)) {
                classByDate.timeSlots = []
            }
            const timeSlots = classByDate.timeSlots
            classesByTime.sort((a: ClassByTimeV2, b: ClassByTimeV2) => {
                return a.id < b.id ? -1 : 1
            })
            // remove duplicate formats
            if (!_.isEmpty(classByDate.formats)) {
                classByDate.formats = _.uniq(classByDate.formats)
            }

            if (!_.isEmpty(classesByTime) && classesByTime[0]) {
                // set time slots
                const classStartTime = classesByTime[0].id.split(":")[0] as unknown as number
                const isHalfAnHourSlot: boolean = classStartTime < 13 ? false : true
                for (let i = classStartTime; i < 24; i++) {
                    timeSlots.push(("0" + i).slice(-2) + ":00:00")
                    isHalfAnHourSlot && timeSlots.push(("0" + i).slice(-2) + ":30:00")
                }
                // set is Live
                if (date === currentDate) {
                    for (const classPerSlot of classesByTime) {
                        const classTime = classPerSlot.id.split(":")
                        if (classTime[0] < currentHour || (classTime[0] === currentHour && classTime[1] <= currentMin)) {
                            classPerSlot.isLive = true
                        }
                    }
                }
            }
            if (classByDateMap[date].wlOnboarding) {
                isWaitlistOnboardingEnabled = 1
                classByDateMap[date].wlOnboardingText = "Swipe left to see how likely you are to get a confirmed waitlist"
                classByDateMap[date].wlOnboardingIcon = "/image/icons/cult/pointed-finger.png"
            }
        }

        let centerName
        if (centerViewOnly && !_.isEmpty(classesResponse.centers)) {
            centerName = classesResponse.centers[0].name + (classesResponse.centers.length > 1 ? `+${classesResponse.centers.length - 1} more` : "")
        }

        const shutdownScheduleDetailsPromise = this.cultBusiness.getCenterUnderMaintenance(userContext, user,
            classesResponse.shutdownSchedule, true, this.announcementBusiness)
        const {
            shutdownScheduleDetails,
            showCenterShutdownCard,
            showCenterShutdownWarningIcon,
            centerShutdownDetailCards,
            centerShutdownDetailV2
        } = await shutdownScheduleDetailsPromise

        const scheduleResponse: ClassScheduleResponse = {
            header: AppUtil.isTVApp(userContext)
                ? { title: "Class Schedule" }
                : ((isLiveBookingPage && !centerName) || (AuthUtil.isGuestUser(userContext) && (_.isEmpty(classesResponse.userSettings) || isNull(locationPreference?.prefLocationType)))
                    ? { title: await gymfitUtil.getDefaultLocality(this.serviceInterfaces.gymfitService, cityId)}
                    : await this.settingsToSchedulePageHeader(productType, classesResponse.userSettings, showCenterShutdownWarningIcon, centerShutdownDetailCards, centerName, filterApplied, locationPreference)),
            classByDateMap: classByDateMap,
            centerInfoMap: await this.getCenterInfoMap(userContext, classesResponse.centers, productType),
            days: this.getAvailableWorkoutDays(userContext, classDates, classByDateMap),
            timeSlotOptions: this.getTimeSlotOptions(),
            workoutCategoryFilters: [...this.getAvailableWorkoutCategoriesFilter(classesResponse.workoutCategories, isCultSportCategorySupported, isBootcampSupported), ...liveClassWorkCategoryFilters],
            liveClassFormatFilter: productType === "HOBBY" ? [] : liveClassFormatFilter,
            setBookingPageDateAction: setBookingPageDateAction,
            nudgeDetails: []
        }

        // TODO: remove these once we kill 7.75 app version
        if (showCenterShutdownCard) {
            scheduleResponse.shutdownScheduleDetails = shutdownScheduleDetails
            scheduleResponse.nudgeDetails = scheduleResponse.nudgeDetails.concat(centerShutdownDetailV2)
        }

        // build notification widget
        if (AppUtil.isNotificationsSupportedInClassBookingPage(userContext)) {
            const builderParams: CultSchedulePageNotificationParams = {
                announcementBusiness: this.announcementBusiness,
                cultService: this.cultFitService,
                shutdownNotificationData: showCenterShutdownCard ? shutdownScheduleDetails : undefined,
                fitClubNotificationData: undefined,
                userContext: userContext,
                centerNotificationResponse: classesResponse.centerNotificationResponse
            }
            const notificationWidgetPromise = eternalPromise(new NotificationWidget().buildViewForCultBookingPage(builderParams))
            scheduleResponse.notificationWidget = (await notificationWidgetPromise).obj
        }
        if (!liveClassAvailable && (_.isEmpty(scheduleResponse.days) || _.isEmpty(classesResponse.classes))) {
            if (classesResponse.shutdownSchedule && classesResponse.shutdownSchedule.length > 0 && AppUtil.isCenterShutdownSupported(userContext, user.isInternalUser)) {
                scheduleResponse.message = "Centres in your selection are temporarily not operational. Please select another centre."
            } else {
                scheduleResponse.message = "Classes aren't yet scheduled for this date. Do stay tuned."
            }
        }
        scheduleResponse.emptyClassesMessage = "Classes aren't yet scheduled for this date. Do stay tuned."

        if (rescheduleClassBookingResponse) {
            const rescheduleClassInfo: CultClass = rescheduleClassBookingResponse.booking.CultClass
            const classStartTime = rescheduleClassInfo.formattedStartTime
            const classEndTime = rescheduleClassInfo.formattedEndTime

            scheduleResponse.rescheduleClassInfo = {
                title: "RESCHEDULING FOR",
                className: rescheduleClassInfo.Workout.name,
                classDetails: `${TimeUtil.formatDateStringInTimeZone(rescheduleClassInfo.date, tz, "ddd D MMM")}, ${classStartTime} - ${classEndTime}, ${rescheduleClassInfo.Center.name}`
            }
        }
        if (productType === "FITNESS") {
            const isCultTrialUser = cultAndMindSummary.trialEligibility && cultAndMindSummary.trialEligibility.cult
            if (isCultTrialUser) {
                scheduleResponse.userType = "TRIAL_USER"
            }
        } else if (productType === "MIND") {
            const isMindTrialUser = cultAndMindSummary.trialEligibility && cultAndMindSummary.trialEligibility.mind
            if (isMindTrialUser) {
                scheduleResponse.userType = "TRIAL_USER"
            }
        }
        if (!isLiveBookingPage) {
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
                const widget: any = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
                if (!_.isEmpty(widget)) {
                    widget.data = widget?.data.map((banner: any) => {
                        if (banner.action?.actionType === "SHOW_CULT_CENTER_REOPEN_MODAL") {
                            const allCentersShutdown: boolean = classesResponse.centers.every(center => center.status !== "OPEN")
                            const noClassPresent: boolean = classesResponse.classes.length === 0
                            if (allCentersShutdown && noClassPresent) {
                                return getCenterReopenBanner(banner, classesResponse.centers)
                            } else {
                                return undefined
                            }
                        }
                        return banner
                    })

                    widget.data = widget?.data.filter((banner: any) => banner)
                }

                if (!_.isEmpty(widget.data)) {
                    scheduleResponse.bannerWidget = widget as unknown as BannerCarouselWidget
                }
            }

            const creditBasedMembership = await CultUtil.getActiveCreditBasedMembership(userContext, this.membershipService)
            const creditbenefit = !_.isEmpty(creditBasedMembership) ? creditBasedMembership.benefits.find(benefit => benefit.name == "ACCESS_CREDITS") : null
            if (!_.isEmpty(creditbenefit)) {
                const userCreditsLeft: number = creditbenefit.maxTickets - creditbenefit.ticketsUsed
                scheduleResponse.creditsDetail = {
                    title: "CREDIT" + ((userCreditsLeft === 1) ? "" : "S"),
                    creditsLeft: userCreditsLeft,
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://credit_history_page?membershipId=" + creditBasedMembership?.id,
                        analyticsData: {
                            creditsLeft: userCreditsLeft,
                            membershipId: creditBasedMembership?.id
                        },
                        viaDeepLink: true,
                        meta: {
                            viaDeeplink: true,
                        }
                    }
                }
            }

            const selectMembership = await CultUtil.getActiveCultSelectMembership(userContext, this.membershipService)
            let isCultSelectPack = !_.isEmpty(selectMembership)
            if (selectMembership?.metadata?.jpmcMembership) {
                isCultSelectPack = false
            }
            const activePROMembership = await CultUtil.getActiveProMembership(userContext, this.membershipService)
            if (isCultSelectPack && selectMembership.metadata.cityId == userContext.userProfile.cityId) {
                const selectCenters: string[] = []
                selectMembership.attributes.forEach(a => {
                    if (a.attrKey == AttributeKeyType.ACCESS_CENTER) {
                        selectCenters.push(a.attrValue)
                    }
                })
                const scheduleCentersList: any[] = []
                classesResponse.centers.forEach(center => {
                    scheduleCentersList.push(center.centerServiceId)
                })
                const isOnlySelectCenterPresent = scheduleCentersList.length == 1 && scheduleCentersList.filter((value) => selectCenters.includes(value)).length == 1
                if (!isOnlySelectCenterPresent) {
                    try {
                        const ticketBanner = await CultUtil.getTicketBanner(selectMembership, userContext, true)
                        if (ticketBanner) {
                            scheduleResponse.bannerWidget = ticketBanner as unknown as BannerCarouselWidget
                        }
                    } catch (err) {
                        this.logger.error("Error while showing Select remaining  ticket banner", err)
                    }
                }
            } else if (activePROMembership && !_.isEmpty(activePROMembership)) {
                try {
                    const ticketBanner = await CultUtil.getTicketBanner(activePROMembership, userContext, false)
                    if (ticketBanner) {
                        scheduleResponse.bannerWidget = ticketBanner as unknown as BannerCarouselWidget
                    }
                } catch (err) {
                    this.logger.error("Error while showing ticket banner", err)
                }
            }
            const limitedEliteMembership = await CultUtil.getActiveLimitedEliteMembership(userContext, this.membershipService)
            const isLimitedElitePack = !_.isEmpty(limitedEliteMembership)
            if (isLimitedElitePack) {
                const isAwayCity = limitedEliteMembership.metadata.cityId !== userContext.userProfile.cityId
                const cultBenefit = limitedEliteMembership.benefits.find(a => a.name === (isAwayCity ? "CULT_AWAY" : "CULT"))
                const progressVal = (cultBenefit.ticketsUsed ?? 0) / (cultBenefit.maxTickets ?? 1)
                const color = CultUtil.getProgressColor(progressVal, false)
                scheduleResponse.sessionsRemainingWidget = {
                    widgetType: "BASIC_PROGRESS_CARD",
                    text: `${cultBenefit.maxTickets - cultBenefit.ticketsUsed} of ${cultBenefit.maxTickets}${isAwayCity ? " away" : ""} session${cultBenefit.maxTickets > 1 ? "s" : ""} left`,
                    bgColor: color,
                    textColor: "",
                    trialType: cultBenefit.maxTickets > 6 ? "duration" : "session",
                    progressBar: {
                        completed: cultBenefit.ticketsUsed,
                        total: cultBenefit.maxTickets,
                        progressBarColor: cultBenefit.maxTickets > 6 ? color : CultUtil.getProgressColor(progressVal, true),
                        progressBarBackgroundColor: "transparent"
                    }
                }
            }
        }

        if (isNewBookingFlow && AppUtil.isNewWaitListProbabilitySypported(userContext)) {
            const entity = "booking_v3"
            const state = "coachmark"
            const isShown = await this.featureStateCache.match(userContext.userProfile.userId, entity, state)
            if (!isShown) {
                scheduleResponse.showCoachmark = true
                this.featureStateCache.set(userContext.userProfile.userId, entity, state)
            }
        }

        if (await AppUtil.isAppCLPv2Supported(userContext, this.segmentService)) {
            const creditBasedMembership = await CultUtil.getActiveCreditBasedMembership(userContext, this.membershipService)
            if (!_.isEmpty(creditBasedMembership)) {
                const entity = "credit_onboarding"
                const state = "onboarded"
                const isShown = await this.featureStateCache.match(userContext.userProfile.userId, entity, state)
                if (!isShown) {
                    scheduleResponse.showCreditOnboarding = true
                    this.featureStateCache.set(userContext.userProfile.userId, entity, state)
                }
            }
        }
        // hardcoding to remove WL Onboarding
        isWaitlistOnboardingEnabled = 0
        // Logic for onboarding announcement.
        if (isWaitlistOnboardingEnabled) {
            const announcementId = "waitlist_onboarding_1"
            const waitlistOnboardingAnnouncement = await this.announcementBusiness.createAndGetAnnouncementDetails(
                userContext, announcementId)
            if (waitlistOnboardingAnnouncement && waitlistOnboardingAnnouncement.state === "CREATED") {
                const dismissAction: Action = {
                    actionType: "REST_API",
                    title: "Got it",
                    meta: {
                        method: "POST",
                        url: `/user/announcement/${waitlistOnboardingAnnouncement.announcementId}`,
                        body: {"state": "DISMISSED"}
                    }
                }
                scheduleResponse.waitListAnnouncementAction = dismissAction
                scheduleResponse.wlOnboardingTitle = "Jump up the queue whenever someone cancels their slot"
                scheduleResponse.wlOnboardingSubtitle = "Filled slots have a waitlist queue."
                scheduleResponse.wlOnboardingTooltip = "What is waitlist?"
                scheduleResponse.wlOnboardingAnimation = AppUtil.isNewWaitListProbabilitySypported(userContext) ? null : "/image/cultpass/waitlist_onboarding_6.json"
                scheduleResponse.wlOnboardingImage = "/image/cultpass/waitlist_onboarding_6.png"
            } else {
                const existingUserAnnouncementId = "waitlist_onboarding_2"
                const existingUserAnnouncement = await this.announcementBusiness.createAndGetAnnouncementDetails(
                    userContext, existingUserAnnouncementId)
                if (existingUserAnnouncement && existingUserAnnouncement.state === "CREATED") {
                    const dismissAction: Action = {
                        actionType: "REST_API",
                        title: "Got it",
                        meta: {
                            method: "POST",
                            url: `/user/announcement/${existingUserAnnouncement.announcementId}`,
                            body: {"state": "DISMISSED"}
                        }
                    }
                    scheduleResponse.waitListAnnouncementAction = dismissAction
                    scheduleResponse.wlOnboardingTooltip = "What is waitlist?"
                    scheduleResponse.wlOnboardingTitle = "Jump up the queue whenever someone cancels their slot"
                    scheduleResponse.wlOnboardingSubtitle = "Filled slots have a waitlist queue."
                    scheduleResponse.wlOnboardingAnimation = AppUtil.isNewWaitListProbabilitySypported(userContext) ? null : "/image/cultpass/waitlist_onboarding_6.json"
                    scheduleResponse.wlOnboardingImage = "/image/cultpass/waitlist_onboarding_6.png"
                    scheduleResponse.wlShowBottomSheet = true
                }
            }
        }

        if (await AppUtil.isAppNewWaitlistColorCodingSupported(this.serviceInterfaces.segmentService, userContext)) {
            const entity = "waitlist_onboarding_v2"
            const state = "onboarded"
            const isShown = await this.featureStateCache.match(userContext.userProfile.userId, entity, state)
            if (!isShown) {
                const findQuery: IFindQuery = {
                    condition: {
                        userId: userContext.userProfile.userId,
                        score: {"$gte": 1},
                        activityType: "CULT_CLASS",
                        date: {$lte: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)}
                    },
                    sortField: "date",
                    count: 1,
                    sortOrder: SortOrder.DESC
                }

                const activitiesPromise = this.serviceInterfaces.activityStoreDao.find(findQuery)
                const activities = await activitiesPromise
                const lastAttendedSession = !_.isEmpty(activities) ? activities[0] : undefined
                let title = "Important update"
                let description = "Your waitlist experience just got better"
                if (_.isNil(lastAttendedSession)) {
                    title = null
                    description = "Jump up the queue whenever someone cancels their slot"
                }
                // fire and forget
                this.featureStateCache.set(userContext.userProfile.userId, entity, state)
                scheduleResponse.waitListOnboardingAction = {
                    "actionType": "SHOW_WAITLIST_INFO_ONBOARDING_MODAL",
                    "meta": {
                        "title": title,
                        "description": description,
                        "lowWaitlistImage": "/image/mem-exp/white_waitlist_onboarding.png",
                        "lowWaitlistDescription": "Indicates more than 90% chance of class confirmation",
                        "lowWaitlistTitle": "White",
                        "lowWaitlistColor": "#FFFFFF",
                        "highWaitlistImage": "/image/mem-exp/orange_waitlist_onboarding.png",
                        "highWaitlistDescription": "Indicates 50% chance of confirmation",
                        "highWaitlistTitle": "Orange",
                        "highWaitlistColor": "#FFB876",
                        "primaryActionText": "GOT IT",
                    }
                }
            }
        }

        // Logic for showing new class widget.
        const infoWidget = this.getInfoWidget(classesResponse.workouts, showSportCategoryInfoWidget && isCultSportCategorySupported)
        if (infoWidget) {
            scheduleResponse.infoWidget = infoWidget
        }

        // For Live Discovery Schedule Page in web.
        if (isLiveBookingPage && await AppUtil.isAtHomeCLPSupported(userContext, this.hamletBusiness)) {
            const widgetId = LIVE_DISCOVERY_TABS_WIDGET_WEB_PROD
            const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
                scheduleResponse.tabsWidget = widgetResponse.widgets[0]
            }
        }
        if (oneDaySchedule) scheduleResponse.oneDaySchedule = oneDaySchedule
        if (recommendationLevel) {
            const widgetId = CLASS_SCHEDULE_LEVEL_WIDGET_ID_MAP.get(recommendationLevel)
            const widgetResponse = await this.widgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, userContext, undefined, undefined)
            this.logger.debug(`recomendationTest:`, {widgetId, widgetResponse, recommendationLevel})
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
                const widget: any = widgetResponse.widgets[0] ? widgetResponse.widgets[0] : undefined
                scheduleResponse.bannerWidget = widget
            }
        }
        if (await AppUtil.doesUserBelongToM1RecommendationSegment(userContext, this.segmentService)) {
            scheduleResponse.sneakPeakExpandedWidgets = CultUtil.getSneakPeakExpandedWidgets()
            scheduleResponse.showSneakPeak = true
            scheduleResponse.sneakPeakCollapsedWidgets = CultUtil.getSneakPeakCollapsedWidgets()
        }

        if (filterWorkoutId) {
            scheduleResponse.localitySelectionAction = {
                actionType: "NAVIGATION",
                url: `curefit://localityselector?pageFromType=CLASS_BOOKING&filterWorkoutId=${filterWorkoutId}`
            }
        } else if (isCultUnbound) {
            scheduleResponse.localitySelectionAction = {
                actionType: "NAVIGATION",
                url: `curefit://localityselector?pageFromType=CLASS_BOOKING&isCultUnbound=${isCultUnbound}`
            }
        }

        return scheduleResponse
    }

    async processSquadData(userContext: UserContext, cultPeersFutureClasses: SquadClassBookingLite[], squadSize: Number): Promise<Map<string, User[]>> {
        try {
            const userEntryGroupByClassId = new Map<string, User[]>()
            const userCountByClassId = new Map<string, number>()

            if (cultPeersFutureClasses && cultPeersFutureClasses.length > 0) {
                const userIds: string[] = []

                // Get the first squadSize unique users for each class
                for (const booking of cultPeersFutureClasses) {
                    const classId = booking.classId.toString()
                    const bookingUserId = booking.userId.toString()
                    const currentCount = userCountByClassId.get(classId) || 0

                    if (currentCount < squadSize) {
                        userCountByClassId.set(classId, currentCount + 1)
                        userIds.push(bookingUserId)
                    }
                }

                // Get the user details for the first squadSize unique users for each class
                const uniqueUserIds: string[] = Array.from(new Set(userIds))
                const usersMap: {[userId: string]: User} = await this.userCache.getUsers(uniqueUserIds)

                for (const booking of cultPeersFutureClasses) {
                    const classId = booking.classId.toString()
                    const bookingUserId = booking.userId.toString()

                    if (!userEntryGroupByClassId.has(classId)) {
                        userEntryGroupByClassId.set(classId, [])
                    }

                    const userEntry: User = usersMap[bookingUserId]
                    if (userEntry) {
                        userEntryGroupByClassId.get(classId)!.push(userEntry)
                    }
                }
                return userEntryGroupByClassId
            }
        } catch (e) {
            this.logger.error(`processSquadData failure for user: ${userContext.userProfile.userId}`)
        }
        return new Map<string, User[]>()
    }

    getInfoWidget(workouts: CultWorkout[], isSportCategorySupported: boolean) {
        let isNewWorkoutPresent: boolean = false
        workouts.map(workout => {
            if (workout.isNewWorkout) {
                isNewWorkoutPresent = true
            }
        })

        if (isNewWorkoutPresent && isSportCategorySupported) {
            return {
                widgetType: "CULT_SCHEDULE_INFO_WIDGET",
                title: "We have added new workouts to the schedule",
                tagName: "NEW"
            }
        }
        return null
    }

    getNoShowBookingBlockedWidget(noShowDetails: ScheduleNoShowDetails, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        const dateTime = new Date(noShowDetails.lastNoShowCLass.classDate + " " + noShowDetails.lastNoShowCLass.classStartTime)
        const bookingDate = TimeUtil.formatDateInTimeZoneDateFns(tz, dateTime, "MMM dd")
        const bookingTime = TimeUtil.formatDateInTimeZoneDateFns(tz, dateTime, "hh:mm a")
        const blockedDates: string[] = []
        let blockedDatesString = ""
        for (let i = 0; i < noShowDetails.bookingBlockedDates.length; i++) {
            blockedDates.push(TimeUtil.formatDateInTimeZoneDateFns(tz, new Date(noShowDetails.bookingBlockedDates[i]), "MMM dd"))
        }
        for (let i = 0 ; i < blockedDates.length; i++) {
            if (i == blockedDates.length - 1 && i > 0) {
                blockedDatesString += " and "
            } else if (i > 0) {
                blockedDatesString += ", "
            }
            blockedDatesString += blockedDates[i]
        }
        const title = "Uh oh! " + PlayUtil.getNumberOrdinal(noShowDetails.noShowCount) + " no-show. Bookings will be blocked on " + blockedDatesString
        const subtitle = "We missed you at " + noShowDetails.lastNoShowCLass.workoutName + " class at " + bookingTime + " on " + bookingDate
        const dayWiseWidget: NoshowBookingBlockedWidget = new NoshowBookingBlockedWidget()
        dayWiseWidget.widgetType = "NOSHOW_BOOKING_BLOCKED_WIDGET"
        dayWiseWidget.title = title
        dayWiseWidget.subtitle = subtitle
        dayWiseWidget.icon = "lock"
        dayWiseWidget.action = {
            actionType: "NAVIGATION",
            title: "KNOW MORE",
            url: "curefit://webview?uri=https://support.cult.fit/support/solutions/articles/25000030016-what-is-the-new-no-show-policy-and-how-does-the-policy-work-"
        }
     return dayWiseWidget
    }

    async buildIntlView(userContext: UserContext, productType: ProductType, liveClasses?: LiveClass[]): Promise<ClassScheduleResponse> {
        const classByDateMap: { [id: string]: ClassByDateV2 } = {}
        const classByDateTimeMap: { [id: string]: ClassByTimeV2 } = {}
        const classByDateTimeCenterMap: { [id: string]: ClassByCenter } = {}
        const user = await userContext.userPromise

        const classDates = TimeUtil.getDays(userContext.userProfile.timezone, 4, false, "YYYY-MM-DD")

        classDates.forEach(date => {
            const classByDateV2: ClassByDateV2 = {
                id: date,
                classByTimeList: []
            }
            classByDateMap[date] = classByDateV2
        })

        const liveClassWorkCategoryFilters: any[] = []
        const {
            isUserEligibleForMonetisation,
            isUserEligibleForTrial
        } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(this.cultBusiness, this.diyFulfilmentService, userContext)

        const bookmarkedTrainers = await this.diyIntlClientService.getBookmarkedTrainers(userContext.userProfile.userId)
        const filters = await this.diyIntlClientService.getAvailableFilters(true, FilterNamespace.COLLECTION)

        await this.getIntlLiveClassScheduleResponse(
            userContext,
            user,
            classByDateMap,
            classByDateTimeMap,
            classByDateTimeCenterMap,
            productType,
            liveClassWorkCategoryFilters,
            isUserEligibleForMonetisation,
            isUserEligibleForTrial,
            liveClasses
        )

        const liveClassFormatFilter: any[] = []
        const formats = LiveUtil.getFormatsBasedOnProductType(productType)
        const formatFilterList: any[] = []
        formats.forEach((format) => formatFilterList.push(format))

        formatFilterList.unshift(allFormats)
        formatFilterList.forEach((format) => {
            liveClassFormatFilter.push({
                id: format,
                name: LiveUtil.getFormatName(format),
                icon: LiveUtil.getFormatIcon(format),
            })
        })


        const scheduleResponse: ClassScheduleResponse = {
            header: AppUtil.isTVApp(userContext) && {title: "Workout"},
            classByDateMap: classByDateMap,
            days: this.getAvailableWorkoutDays(userContext, classDates, classByDateMap),
            trainers: bookmarkedTrainers.map(trainer => {
                return ({
                    id: trainer.instructorId,
                    image: trainer.profileImageUrl,
                })
            }),
            filters,
            workoutCategoryFilters: [],
            timeSlotOptions: this.getTimeSlotOptions(),
            emptyClassesMessage: "Classes aren't yet scheduled for this date. Do stay tuned.",
            liveClassFormatFilter: liveClassFormatFilter
        }

        return scheduleResponse
    }
}

export default ClassListViewBuilderV2
