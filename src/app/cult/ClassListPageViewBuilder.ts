import * as _ from "lodash"
import { Action } from "../common/views/WidgetView"
import { CultBooking } from "@curefit/cult-common"
import { PageWidget } from "../page/Page"
import { inject, injectable } from "inversify"
import IssueBusiness from "../crm/IssueBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Status, SupportActionableCardWidget, SupportEmptyListingWidget } from "../page/PageWidgets"
import { ActionUtil } from "@curefit/base-utils"
import { ActionUtil as AppActionUtil } from "../util/ActionUtil"
import { TimeUtil } from "@curefit/util-common"
import { SupportListPageView } from "../crm/SupportListPageView"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import CultUtil from "../util/CultUtil"
import { User } from "@curefit/user-common"
import { CacheHelper } from "../util/CacheHelper"
import * as momentTz from "moment-timezone"
import { ISegmentService } from "@curefit/vm-models"

export const CultBookingStatus: { [key: string]: any; } = {
    WaitListed: {
        text: "WAITLISTED",
        colour: "#fec62e"
    },
    Upcoming: {
        text: "CONFIRMED",
        colour: "#000000"
    },
    Completed: {
        text: "COMPLETED",
        colour: "#50d166"
    },
    Ongoing: {
        text: "ONGOING",
        colour: "#00B4df"
    },
    Cancelled: {
        text: "CANCELLED",
        colour: "#e05343"
    },
    "No Show": {
        text: "MISSED",
        colour: "#d1d1d1"
    },
    "Dropped out": {
        text: "DROPPED OUT",
        colour: "#e05343"
    }
}

@injectable()
class ClassListPageViewBuilder {
    constructor(
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
    ) {
    }

    async buildClassListPageView(userContext: UserContext, cultBookings: CultBooking[], waitlistings: CultBooking[], pageNumber: number): Promise<SupportListPageView> {
        const widgets: PageWidget[] = []
        let actions: Action[]

        const classBookings: CultBooking[] = []
        cultBookings.map(booking => {
            classBookings.push(booking)
        })
        waitlistings.map(booking => {
            classBookings.push(booking)
        })
        const timezone = userContext.userProfile.timezone
        classBookings.sort((a, b) => {
            if (a.Class.date === b.Class.date) {
                return momentTz(a.Class.startTime, "hh:mm:ss", timezone).isBefore(momentTz(b.Class.startTime, "hh:mm:ss", timezone)) ? 1 : -1
            }
            return momentTz.utc(a.Class.date).isBefore(momentTz.utc(b.Class.date)) ? 1 : -1
        })

        if (_.isEmpty(classBookings)) {
            if (pageNumber === 1 && !(await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext))) {
                const userPromise = this.userCache.getUser(userContext.userProfile.userId)
                const user: User = await userPromise
                const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
                widgets.push(new SupportEmptyListingWidget("CULT", "No Classes Yet!", "Book a cult.fit or mind.fit class and get your fitness journey going. It's fitness made fun and easy."))
                actions = [{
                    actionType: "NAVIGATION",
                    url: ActionUtil.getBookCultClassUrl("FITNESS", isNewClassBookingSuppoted, "support"),
                    title: "Book a Class"
                }]
            }
        } else {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(classBookings, classBooking => {
                return this.getActionableCardWidget(classBooking, userContext)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            cardWidgets.map(cardWidget => {
                widgets.push(cardWidget)
            })
        }
        return new SupportListPageView(widgets, actions)
    }

    async buildClassListWidgets(userContext: UserContext, cultBookings: CultBooking[], waitlistings: CultBooking[]): Promise<SupportActionableCardWidget[]> {
        const classBookings: CultBooking[] = []
        cultBookings.map(booking => {
            classBookings.push(booking)
        })
        waitlistings.map(booking => {
            classBookings.push(booking)
        })
        const timezone = userContext.userProfile.timezone
        classBookings.sort((a, b) => {
            if (a.Class.date === b.Class.date) {
                return momentTz(a.Class.startTime, "hh:mm:ss", timezone).isBefore(momentTz(b.Class.startTime, "hh:mm:ss", timezone)) ? 1 : -1
            }
            return momentTz.utc(a.Class.date).isBefore(momentTz.utc(b.Class.date)) ? 1 : -1
        })
        if (!_.isEmpty(classBookings)) {
            const cardWidgetPromises: Promise<SupportActionableCardWidget>[] = _.map(classBookings, classBooking => {
                return this.getActionableCardWidget(classBooking, userContext)
            })
            const cardWidgets: SupportActionableCardWidget[] = await Promise.all(cardWidgetPromises)
            return cardWidgets
        }
        return []
    }

    async getActionableCardWidget(classBooking: CultBooking, userContext: UserContext): Promise<SupportActionableCardWidget> {
        const timezone = userContext.userProfile.timezone
        const reportIssueParams = this.issueBusiness.getCultOrMindCenterBookingIssueParams(classBooking, classBooking.Center.tenantID === 1 ? "FITNESS" : "MIND")
        const hour: string = TimeUtil.getMomentForDateString(classBooking.Class.startTime, timezone, "HH:mm:ss").format("HH")
        const minute: string = TimeUtil.getMomentForDateString(classBooking.Class.startTime, timezone, "HH:mm:ss").format("mm")
        const startTime: string = TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(timezone, new Date()), +hour, +minute, true, timezone)
        const document = _.find(classBooking.Class.Workout.documents, doc => {
            return doc.tagID === 11
        })
        return {
            widgetType: "SUPPORT_ACTIONABLE_CARD_WIDGET",
            title: CultUtil.pulsifyClassName(classBooking.CultClass, userContext),
            subTitle: classBooking.Center.name,
            footer: AppUtil.isWeb(userContext) ? undefined : [{
                text: `${TimeUtil.formatDateStringInTimeZone(classBooking.Class.date, timezone, "ddd, D MMM")}, ${startTime}`,
            }],

            cardAction: {
                actionType: "NAVIGATION",
                url: AppActionUtil.getIssuesUrl(),
                meta: (await AppUtil.isNewSupportInFlutterSupported(this.segmentService, userContext)) ? {
                    title: CultUtil.pulsifyClassName(classBooking.CultClass, userContext),
                    subTitle: "What is your primary concern with the class"
                } : null,
            },
            imageUrl: document ? "/" + document.URL : "image/<EMAIL>",
            status: this.getCultBookingStatus(classBooking),
            time: AppUtil.isWeb(userContext) ? `${TimeUtil.formatDateStringInTimeZone(classBooking.Class.date, timezone, "ddd, D MMM")}, ${startTime}` : undefined,
            timestamp: momentTz(classBooking.Class.startDateTimeUTC, "YYYY-MM-DD HH:mm:ss").valueOf(),
            timezone: timezone
        }
    }

    private getCultBookingStatus(classBooking: CultBooking) {
        if (!_.isEmpty(classBooking.wlBookingNumber)) {
            return <Status>CultBookingStatus.WaitListed
        } else {
            return (<Status>CultBookingStatus[classBooking.label])
        }
    }
}

export default ClassListPageViewBuilder
