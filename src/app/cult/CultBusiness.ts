import {
    CultCenter,
    CultMembership,
    CultSummary,
    CultUserPreference,
    NearByCenter,
    ShutdownSchedule,
    ITrialClases,
    UserTrialEligibility,
} from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService, ICultService as ICultServiceNew } from "@curefit/cult-client"
import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as _ from "lodash"
import CultUtil, {
    CENTER_MAINTENANCE_WARNING,
    CENTER_SHUTDOWN,
    CENTER_SHUTDOWN_ICON,
    FITCLUB_ICON,
    transformCultSummary,
    BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_ID,
    isActiveOrFuturePassAccessPresent, getFindCenterLocationArray
} from "../util/CultUtil"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { CATALOG_CLIENT_TYPES, CatalogueServiceV2Utilities, ICatalogueService } from "@curefit/catalog-client"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { BASE_TYPES, Logger, NotFoundError } from "@curefit/base"
import { UserContext, Session, SessionInfo } from "@curefit/userinfo-common"
import { CultPackUserType, Order } from "@curefit/order-common"
import {
    OrderCreate, IOrderService, OMS_API_CLIENT_TYPES
} from "@curefit/oms-api-client"
import { CdnUtil, eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import * as momentTz from "moment-timezone"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { Action, CallReminderSlotTime, CultClassRecommendation } from "../common/views/WidgetView"
import CenterView from "./CenterView"
import CultPackPageConfig from "../pack/CultPackPageConfig"
import AppUtil, {
    FREEMIUM_APP_SUPPORT_VERSION,
    LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_PROD,
    LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_STAGE
} from "../util/AppUtil"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { User, Tenant } from "@curefit/user-common"
import { AnnouncementBusiness } from "../announcement/AnnouncementBusiness"
import { AnnouncementDetails, AnnouncementView } from "../announcement/AnnouncementViewBuilder"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import { AlertError } from "../common/errors/AlertError"
import { ISessionBusiness as ISessionService, SeoUrlParams } from "@curefit/base-utils"
import {
    ClassByCenter,
    ClassByDateV2,
    ClassByTimeV2,
    CultClassRecommendationWidgetView,
    IClassView,
    MAX_RECOMMENDATION_TO_BE_SHOWN,
    WidgetView
} from "@curefit/apps-common"
import {
    CultProductDetails,
    IRecommendationService,
    RECOMMENDATION_CLIENT_TYPES,
    RecommendationEngine, RecommendationRequest, RecommendationResponse, RecommendationSubmodule
} from "@curefit/recommendation-client"
import { ActionUtil } from "@curefit/base-utils"
import { ContentMetric } from "@curefit/apps-common"
import { CONSTELLO_CLIENT_TYPES, IGiftCardService } from "@curefit/constello-client"
import { REDIS_TYPES, ICrudKeyValue } from "@curefit/redis-utils"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { ALBUS_CLIENT_TYPES, IHealthfaceService, Patient } from "@curefit/albus-client"
import AuthUtil from "../util/AuthUtil"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import {
    convertMapStyleToStatic,
    CUSTOM_MAP_CULT_FIND_CENTER_STYLE_COLORED,
    MAX_DISTANCE_CENTER_ALLOWED_IN_METRES
} from "../page/vm/widgets/CultFindCenterWidgetView"
import { UserAllocation } from "@curefit/hamlet-common"
import CenterViewV2 from "./CenterViewV2"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { Membership } from "@curefit/membership-commons"
import { CenterResponse, CenterSearchRequest, CenterStatus, CenterType, SortBy } from "@curefit/center-service-common"
import { CENTER_SERVICE_CLIENT_TYPES, ICenterService } from "@curefit/center-service-client"
import UserUtil, { LocationDataKey, LocationPreferenceRequestEntity, LocationPreferenceResponseEntity, UpdateStatusResponseEntity, UserPreferenceV2 } from "../util/UserUtil"
import { RASHI_CLIENT_TYPES, UserAttributeClient } from "@curefit/rashi-client"
import LocationUtil from "../util/LocationUtil"
import { NudgeDetail } from "../cult/ClassListViewBuilderV2"
import { UserCity } from "../user/IUserBusiness"
import { City } from "@curefit/location-common"

export type PreferenceSelection = "favourites" | "userLocation" | "area"
export const MAX_FAVOURITES_ALLOWED = 5

export interface PreferenceDetail {
    favourites?: {
        maxFavourites: number
        selectedFavourites:
        {
            centerId: number
            centerName: string
        }[]

    }
    userLocation?: {
        selectedLocation: {
            latitude: number
            longitude: number
            name: string
        }
    }
    area?: {
        selectedAreaId: number
        allAreas:
        {
            areaId: number
            name: string
            numCenters: number
        }[]
    }
    currentSelection?: PreferenceSelection,
    bookingEmailPreference?: boolean,
    shutdownScheduleDetails?: {
        description: string,
        icon: string,
        action: Action
    },
    ivrCallPreference?: boolean,
    ivrCallTime?: IvrCallTime
}

export interface IvrCallTime {
    selectedMinutesBefore: number,
    slots: CallReminderSlot
}

export interface CallReminderSlot {
    minMinutesBefore: number,
    maxMinutesBefore: number,
    granularityMinutes: number,
    ivrStatus?: boolean,
    minutesBefore?: number
}

export interface CenterDetails {
    centers: CenterView[] | CenterViewV2[],
    footer?: {
        title: string,
        gradientColor: [string, string]
    }
}

export interface ClassType {
    cult?: boolean
    mind?: boolean
}

export const PreferenceMap = new Map<string, PreferenceSelection>()
PreferenceMap.set("USER_BOOKING_V2_FAVOURITE_CENTER", "favourites")
PreferenceMap.set("USER_BOOKING_V2_LAT_LONG", "userLocation")
PreferenceMap.set("USER_BOOKING_V2_LOCALITY", "area")

export const PreferenceReverseMap = new Map<PreferenceSelection, string>()
PreferenceReverseMap.set("favourites", "USER_BOOKING_V2_FAVOURITE_CENTER")
PreferenceReverseMap.set("userLocation", "USER_BOOKING_V2_LAT_LONG")
PreferenceReverseMap.set("area", "USER_BOOKING_V2_LOCALITY")

export interface Preference {
    productType: ProductType
    selectedFavourites: string[]
    selectedLocation: {
        latitude: number
        longitude: number
    }
    selectedAreaId: number
    currentSelection: PreferenceSelection
}


export interface MoneyBackOfferDetail {
    isOfferApplied: boolean
    isOfferExpired: boolean
    cancellationWindow: { startDate: string, endDate: string }
}

export interface CenterLocation {
    lat: number
    lon: number
    title: string
    distance: number
    action: Action
    address: string
    contentMetric?: ContentMetric
    centerStatus?: string
    isCenterStatusAlertEnabled?: boolean
    alertAction?: Action
    categoryAction?: Action
    categoryTitle?: string
    showBigSeparator?: boolean,
}

export interface NearByCentersInfo {
    centers: CenterLocation[],
    isCenterAwayFromCity: boolean,
    nearByCenterIndex: number,
}

export interface ICultBusiness {
    getAllCentersCached(): Promise<{ [id: number]: CultCenter }>


    getCultCityId(userContext: UserContext, cityId: string, userId: string, chooseOtherCity?: boolean): Promise<number>

    getMindCityId(userContext: UserContext, cityId: string, userId: string): Promise<number>

    getClassSchedulePreference(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<PreferenceDetail>

    getClassRemindersPreference(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<PreferenceDetail>

    getClassCalendarPreference(userContext: UserContext, userId: string, productType: ProductType): Promise<PreferenceDetail>

    getPreferenceByKey(userContext: UserContext, cityId: string, userId: string, productType: ProductType, key: string): Promise<PreferenceDetail>

    savePreference(userContext: UserContext, cityId: string, userId: string, preference: Preference): Promise<{ status: boolean }>

    getAllCenters(userContext: UserContext, cityId: string, isCultUnbound?: boolean): Promise<CenterResponse[]>

    updateUserAcknowledged(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<{ status: boolean }>

    getClientMetadataForOrder(userContext: UserContext, createOrder: OrderCreate, product: Product): Promise<any>

    getPlayClientMetadataForOrder(userContext: UserContext): Promise<any>

    checkMoneyBackOfferOnMembership(membership: Membership, userContext: UserContext): Promise<MoneyBackOfferDetail>

    saveBookingConfirmationPreference(userContext: UserContext, cityId: string, userId: string, preference: CultUserPreference): Promise<{ status: boolean }>

    getCenterUnderMaintenance(userContext: UserContext, user: User, shutdownSchedule: ShutdownSchedule[], createAnnouncement?: boolean, announcementBusiness?: AnnouncementBusiness): Promise<any>

    checkForParQ(ex: any, userContext: UserContext, pageId: string): Promise<any>

    getCultClassRecommendationData(userContext: UserContext, productType: ProductType, pageFrom: string, scheduleCenters: CultCenter[], centerIds?: string[]): Promise<{ [date: string]: CultClassRecommendationWidgetView }>

    isBookFreeCultClassSupported(userContext: UserContext): Promise<ClassType>

    getNearbyCenters(userId: string, cultCityId: number, latitude: number, longitude: number, productType: ProductType, userContext: UserContext, showPreferredCenters?: boolean): Promise<NearByCentersInfo>

    isUserEligibleForInviteOffer(userContext: UserContext, campaignId: string): Promise<{ supported: boolean, card?: any }>

    checkIfUserIsEligibleForLivePackMonetisation(userContext: UserContext, user?: User): Promise<boolean>

    getBookingCancellationInfo(userContext: UserContext, bookingNumber: string): Promise<any>

    getBookingScreenNudges(userContext: UserContext, productType: ProductType): Promise<any>

    /**
     *  This will check if the user is part of first experiment or other.
     *  The experiments are conducted for curefit live trial
     */
    checkIfUserPartOfLiveTrialExperiment1(userContext: UserContext): Promise<boolean>

    getLivePTCrossGenderMatchingDetails(userContext: UserContext, patientId: number): Promise<{
        isCrossGenderEnabled: boolean
        genderText: string
        patient: Patient
    }>

}

@injectable()
export class CultBusiness implements ICultBusiness {

    constructor(
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
        @inject(LOCATION_SERVICE_TYPES.LocationService) private locationService: ILocationService,
        @inject(OMS_API_CLIENT_TYPES.OrderService) private omsApiClient: IOrderService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(CUREFIT_API_TYPES.CultPackPageConfig) protected cultPackPageConfig: CultPackPageConfig,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) public recommendationService: IRecommendationService,
        @inject(CONSTELLO_CLIENT_TYPES.GiftCardService) public giftCardService: IGiftCardService,
        @inject(REDIS_TYPES.RedisDao) public redisDao: ICrudKeyValue,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyFulfilmentService: IDIYFulfilmentService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
        @inject(CENTER_SERVICE_CLIENT_TYPES.CenterService) private centerService: ICenterService,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: UserAttributeClient,
    ) {
    }

    /**
     *  This will check if the user is part of first experiment or other.
     *  The experiments are conducted for curefit live trial
     */
    async checkIfUserPartOfLiveTrialExperiment1(userContext: UserContext): Promise<boolean> {
        const experimentID = process.env.ENVIRONMENT === "STAGE" ? LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_STAGE : LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_PROD // owner "<EMAIL>"
        const userAssigmentExperiments: UserAllocation = {assignmentsMap: {}}
        const isUserABEnabled = userAssigmentExperiments && userAssigmentExperiments?.assignmentsMap && !_.isEmpty(userAssigmentExperiments.assignmentsMap[experimentID]) && !_.isEmpty(userAssigmentExperiments.assignmentsMap[experimentID]?.bucket)
        if (isUserABEnabled) {
            const bucket = userAssigmentExperiments.assignmentsMap[experimentID].bucket
            if (bucket.bucketId === "1") {
                return true
            } else {
                return false
            }
        }
        return false
    }

    /**
     *  This will check if the user is part of third experiment or other.
     *  The experiments are conducted for curefit live trial
     */
    async checkIfUserPartOfLiveTrialExperiment3(userContext: UserContext): Promise<boolean> {
        const experimentID = process.env.ENVIRONMENT === "STAGE" ? LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_STAGE : LIVE_PACK_TRIAL_HAMLET_EXPERIMENT_PROD // owner "<EMAIL>"
        const userAssigmentExperiments: UserAllocation = {assignmentsMap: {}}
        const isUserABEnabled = userAssigmentExperiments && userAssigmentExperiments.assignmentsMap && !_.isEmpty(userAssigmentExperiments.assignmentsMap[experimentID]) && !_.isEmpty(userAssigmentExperiments.assignmentsMap[experimentID].bucket)
        if (isUserABEnabled) {
            const bucket = userAssigmentExperiments.assignmentsMap[experimentID].bucket
            if (bucket.bucketId === "3" || bucket.bucketId === "4") {
                return true
            } else {
                return false
            }
        }
        return false
    }


    async getCultCityId(userContext: UserContext, cityId: string, userId: string, chooseOtherCity: boolean = false): Promise<number> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const userCity: UserCity = LocationUtil.getUserCityFromUserContext(userContext, this.cityService, this.logger, cityId, null)
        const city: City = userCity.city
        if (city && this.cityService.checkIfCityIsOtherCity(city.cityId) && !chooseOtherCity) {
            const activeMembershipData = await this.cultFitService.activeMembership(userId)
            const membership = CultUtil.cultPackProgress(userContext, activeMembershipData.memberships)
            if (membership)
                return membership.currentMembership.cityID
        }
        return city.cultCityId
    }

    async updateUserAcknowledged(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<{ status: boolean }> {
        const cultCityId = productType === "FITNESS" ? await this.getCultCityId(userContext, cityId, userId) : await this.getMindCityId(userContext, cityId, userId)

        const cultUserPreference: CultUserPreference = {
            settings: [{
                "key": "USER_BOOKING_V2_SETTINGS_STATE",
                "value": "user_acknowledged"
            }],
            shutdownSchedule: []
        }
        const result = productType === "FITNESS" ? this.cultFitService.savePreference(cultCityId, userId, cultUserPreference, "CUREFIT_API", userContext.userProfile.subUserId) :
            this.mindFitService.savePreference(cultCityId, userId, cultUserPreference, "CUREFIT_API", userContext.userProfile.subUserId)
        return result
    }

    async getMindCityId(userContext: UserContext, cityId: string, userId: string): Promise<number> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const city = cityId ? await this.cityService.getCityById(cityId) : await this.cityService.getDefaultCity(tenant)
        if (this.cityService.checkIfCityIsOtherCity(city.cityId)) {
            const activeMembershipData = await this.mindFitService.activeMembership(userId)
            const membership = CultUtil.cultPackProgress(userContext, activeMembershipData.memberships)
            if (membership)
                return membership.currentMembership.cityID
        }
        return city.cultCityId
    }

    async getAllCenters(userContext: UserContext, cityId: string, isCultUnbound?: boolean): Promise<CenterResponse[]> {
        const latitude = userContext.sessionInfo.lat
        const longitude = userContext.sessionInfo.lon
        const centerSearchRequest: CenterSearchRequest = {
            includeDetails: true,
            offset: 0,
            limit: isCultUnbound ? 1000 : 200,
            city: isCultUnbound ? null : cityId,
            type: CenterType.CULT_CENTER,
            statuses: [CenterStatus.ACTIVE],
            latitude: latitude,
            longitude: longitude,
            sortBy: (latitude && longitude) ? SortBy.DISTANCE : SortBy.RELEVANCE,
        }
        return this.centerService.searchCenters(centerSearchRequest)
    }

    async getAllCentersCached(): Promise<{ [id: number]: CultCenter }> {
        const cultCenters = await this.catalogueService.getCultCenters()
        const mindCenters = await this.catalogueService.getCultMindCenters()
        let centers: CultCenter[] = []
        centers = centers.concat(...cultCenters)
        centers = centers.concat(...mindCenters)
        const centersMap = _.keyBy(centers, "id")
        return centersMap
    }

    async getPreferenceDetails(userContext: UserContext, preference: CultUserPreference): Promise<PreferenceDetail> {
        const preferenceDetail: PreferenceDetail = {
            area: {
                allAreas: [],
                selectedAreaId: undefined
            },
            currentSelection: undefined,
            favourites: {
                maxFavourites: MAX_FAVOURITES_ALLOWED,
                selectedFavourites: undefined
            },
            userLocation: {
                selectedLocation: undefined
            },
            bookingEmailPreference: true,
            ivrCallPreference: false
        }
        const cultCenterMapPromise = this.catalogueService.getCultCentersMap()
        const mindCenterMapPromise = this.catalogueService.getCultMindCentersMap()
        const cultCenterMap = await cultCenterMapPromise
        const mindCenterMap = await mindCenterMapPromise
        let slotTimes: CallReminderSlot
        try {
            slotTimes = await this.cultFitService.getCallReminderAllowedMinsBefore({
                userId: userContext.userProfile.userId,
                appName: "CUREFIT_APP"
            })
        } catch (e) {
            this.logger.error(`Error on getCallReminderAllowedMinsBefore ${e} for userId: ${userContext.userProfile.userId}`)
        }
        preference.settings.forEach(setting => {
            if (setting.key === "USER_BOOKING_V2_LAT_LONG") {
                preferenceDetail.userLocation.selectedLocation = {
                    latitude: setting.value.latitude,
                    longitude: setting.value.longitude,
                    name: setting.value.name
                }
            } else if (setting.key === "USER_BOOKING_V2_LOCALITY") {
                preferenceDetail.area.selectedAreaId = setting.value
            } else if (setting.key === "USER_BOOKING_V2_FAVOURITE_CENTER") {
                const favouritesIds: number[] = setting.value
                preferenceDetail.favourites.selectedFavourites =
                    _.map(favouritesIds, val => {
                        let centerName
                        if (cultCenterMap.get(CatalogueServiceV2Utilities.getCultCenterProductId(val))) {
                            centerName = cultCenterMap.get(CatalogueServiceV2Utilities.getCultCenterProductId(val)).name
                        } else {
                            centerName = mindCenterMap.get(CatalogueServiceV2Utilities.getMindCenterProductId(val)).name
                        }
                        return {
                            centerId: val,
                            centerName: centerName
                        }
                    })
            } else if (setting.key === "USER_BOOKING_V2_ACTIVE_SETTING") {
                preferenceDetail.currentSelection = PreferenceMap.get(setting.value)
            } else if (setting.key === "USER_SEND_BOOKING_CONFIRMATION_EMAIL") {
                preferenceDetail.bookingEmailPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_STATUS") {
                preferenceDetail.ivrCallPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_MINUTES_BEFORE") {
                preferenceDetail.ivrCallTime = {
                    selectedMinutesBefore: setting.value,
                    slots: slotTimes
                }
            }
        })
        return preferenceDetail
    }

    async getPreferenceByKey(userContext: UserContext, cityId: string, userId: string, productType: ProductType, key: string): Promise<PreferenceDetail> {
        const cultCityId = await this.getCultCityId(userContext, cityId, userId)
        const preference = await this.cultFitService.getPreferenceByKey(cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId, key)
        return await this.getPreferenceDetails(userContext, preference)
    }

    async getClassSchedulePreference(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<PreferenceDetail> {
        const preferenceDetail: PreferenceDetail = {
            area: {
                allAreas: [],
                selectedAreaId: undefined
            },
            currentSelection: undefined,
            favourites: {
                maxFavourites: MAX_FAVOURITES_ALLOWED,
                selectedFavourites: undefined
            },
            userLocation: {
                selectedLocation: undefined
            },
            shutdownScheduleDetails: undefined,
            bookingEmailPreference: true,
            ivrCallPreference: true
        }

        const cultCityId = await this.getCultCityId(userContext, cityId, userId, true)
        const preferencePromise = productType === "FITNESS" ? this.cultFitService.getPreference(cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId) :
            this.mindFitService.getPreference(cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId)
        const localitiesPromise = productType === "FITNESS" ? this.cultFitService.browseLocality(cultCityId, "CUREFIT_API", userContext.userProfile.subUserId || userContext.userProfile.userId) :
            this.mindFitService.browseLocality(cultCityId, "CUREFIT_API", userContext.userProfile.subUserId || userContext.userProfile.userId)
        const cultCenterMapPromise = this.catalogueService.getCultCentersMap()
        const mindCenterMapPromise = this.catalogueService.getCultMindCentersMap()

        const user = await userContext.userPromise
        let preference: CultUserPreference
        const sessionData = _.get(userContext, "sessionInfo.sessionData", {})
        if (AuthUtil.isFixedGuestUser(userContext)) {
            if (productType === "FITNESS" && sessionData.cultPreference) {
                preference = sessionData.cultPreference
            } else if (productType === "MIND") {
                preference = sessionData.mindPreference
            }
        } else {
            preference = await preferencePromise
        }
        const localities = await localitiesPromise
        const cultCenterMap = await cultCenterMapPromise
        const mindCenterMap = await mindCenterMapPromise


        // Populate all area details
        preferenceDetail.area.allAreas = _.map(localities, locality => {
            return {
                areaId: locality.id,
                name: locality.name,
                numCenters: locality.centersCount
            }
        })

        let slotTimes: CallReminderSlot
        try {
            slotTimes = await this.cultFitService.getCallReminderAllowedMinsBefore({ userId: userContext.userProfile.userId, appName: "CUREFIT_APP" })
        } catch (e) {
            this.logger.error(`Error on getCallReminderAllowedMinsBefore ${e} for userId: ${userContext.userProfile.userId}`)
        }

        const locationPreference = await this.getLocationPreference(userContext, userId, cityId)
        let localityId: any
        if (locationPreference.locality) {
            const localities = await this.cultFitService.browseLocality(cultCityId, "CUREFIT_API", userId)
            localities.map(locality => {
                if (locality.name == locationPreference.locality) {
                    localityId = locality.id
                }
            })
        }
        // read the  save settings and translate to app specific payload
        preference.settings && preference.settings.forEach((setting) => {
            if (setting.key === "USER_BOOKING_V2_LAT_LONG") {
                preferenceDetail.userLocation.selectedLocation = {
                    latitude: setting.value.latitude,
                    longitude: setting.value.longitude,
                    name: setting.value.name
                }
            } else if (setting.key === "USER_BOOKING_V2_LOCALITY") {
                preferenceDetail.area.selectedAreaId = setting.value
                if (localityId) {
                    preferenceDetail.area.selectedAreaId = localityId
                }
            } else if (setting.key === "USER_BOOKING_V2_FAVOURITE_CENTER") {
                const favouritesIds: number[] = setting.value
                preferenceDetail.favourites.selectedFavourites =
                    _.map(favouritesIds, val => {
                        let centerName
                        if (cultCenterMap.get(CatalogueServiceV2Utilities.getCultCenterProductId(val))) {
                            centerName = cultCenterMap.get(CatalogueServiceV2Utilities.getCultCenterProductId(val)).name
                        } else {
                            centerName = mindCenterMap.get(CatalogueServiceV2Utilities.getMindCenterProductId(val)).name
                        }
                        let isCenterUnderMaintenance
                        if (AppUtil.isCenterShutdownSupported(userContext, user.isInternalUser)) {
                            isCenterUnderMaintenance = preference.shutdownSchedule.find((center) => {
                                return center.centerID === val
                            })
                        }
                        return {
                            centerId: val,
                            centerName: centerName,
                            isCenterUnderMaintenance: isCenterUnderMaintenance ? true : false,
                            centerMaintenanceIcon: CENTER_MAINTENANCE_WARNING
                        }
                    })
            } else if (setting.key === "USER_BOOKING_V2_ACTIVE_SETTING") {
                preferenceDetail.currentSelection = PreferenceMap.get(setting.value)
            } else if (setting.key === "USER_SEND_BOOKING_CONFIRMATION_EMAIL") {
                preferenceDetail.bookingEmailPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_STATUS") {
                preferenceDetail.ivrCallPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_MINUTES_BEFORE") {
                preferenceDetail.ivrCallTime = {
                    selectedMinutesBefore: setting.value,
                    slots: slotTimes
                }
            }
        })
        const shutdownScheduleDetailsPromise = this.getCenterUnderMaintenance(userContext, user, preference.shutdownSchedule)
        const { shutdownScheduleDetails } = await shutdownScheduleDetailsPromise
        if (shutdownScheduleDetails) {
            preferenceDetail.shutdownScheduleDetails = shutdownScheduleDetails
        }
        return preferenceDetail
    }

    async getLocationPreference(userContext: UserContext, userId: string, cityId: string): Promise<LocationPreferenceResponseEntity> {
        const response: LocationPreferenceResponseEntity = {
            prefLocationType: undefined
        }
        if (!userContext.sessionInfo.isUserLoggedIn) {
            UserUtil.getLocationPreferenceResponseEntityFromSessionData(userContext.sessionInfo.sessionData, cityId, response)
        } else {
            await UserUtil.getLocationPreference(userId, cityId, this.userAttributeClient, response)
        }
        return response
    }

    async getClassRemindersPreference(userContext: UserContext, cityId: string, userId: string, productType: ProductType): Promise<PreferenceDetail> {
        const preferenceDetail: PreferenceDetail = {
            area: {
                allAreas: [],
                selectedAreaId: undefined
            },
            currentSelection: undefined,
            favourites: {
                maxFavourites: MAX_FAVOURITES_ALLOWED,
                selectedFavourites: undefined
            },
            userLocation: {
                selectedLocation: undefined
            },
            shutdownScheduleDetails: undefined,
            bookingEmailPreference: true,
            ivrCallPreference: true
        }

        const cultCityId = await this.getCultCityId(userContext, cityId, userId)
        const preferencePromise = productType === "FITNESS" ? this.cultFitService.getPreference(cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId) :
            this.mindFitService.getPreference(cultCityId, userId, "CUREFIT_API", userContext.userProfile.subUserId)

        const user = await userContext.userPromise
        const preference = await preferencePromise

        let slotTimes: CallReminderSlot
        try {
            slotTimes = await this.cultFitService.getCallReminderAllowedMinsBefore({ userId: userContext.userProfile.userId, appName: "CUREFIT_APP" })
        } catch (e) {
            this.logger.error(`Error on getCallReminderAllowedMinsBefore ${e} for userId: ${userContext.userProfile.userId}`)
        }

        // read the  save settings and translate to app specific payload
        preference.settings.forEach(setting => {
            if (setting.key === "USER_SEND_BOOKING_CONFIRMATION_EMAIL") {
                preferenceDetail.bookingEmailPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_STATUS") {
                preferenceDetail.ivrCallPreference = setting.value
            } else if (setting.key === "USER_BOOKING_IVR_MINUTES_BEFORE") {
                preferenceDetail.ivrCallTime = {
                    selectedMinutesBefore: setting.value,
                    slots: slotTimes
                }
            }
        })
        return preferenceDetail
    }

    async getClassCalendarPreference(userContext: UserContext, userId: string, productType: ProductType): Promise<PreferenceDetail> {
        const preferenceDetail: PreferenceDetail = {
            bookingEmailPreference: true
        }
        // hardcode city id to 1 for this preference
        const preference = await this.cultFitService.getPreferenceByKey(1, userId, "CUREFIT_API", userContext.userProfile.subUserId, "USER_SEND_BOOKING_CONFIRMATION_EMAIL")
        if (preference && !_.isEmpty(preference.settings) && preference.settings[0].value != null) {
            preferenceDetail.bookingEmailPreference = preference.settings[0].value
        }
        return preferenceDetail
    }

    async savePreference(userContext: UserContext, cityId: string, userId: string, preference: Preference): Promise<{ status: boolean, message?: string }> {
        const cultCityId = await this.getCultCityId(userContext, cityId, userId)
        let message
        const cultUserPreference: CultUserPreference = {
            settings: [],
            shutdownSchedule: []
        }
        if (preference.hasOwnProperty("currentSelection")) {
            cultUserPreference.settings.push({
                "key": "USER_BOOKING_V2_ACTIVE_SETTING",
                "value": PreferenceReverseMap.get(preference.currentSelection) ? PreferenceReverseMap.get(preference.currentSelection) : null
            })

            try {
                let locality
                if (!_.isNil(preference.selectedAreaId)) {
                    locality = await this.cultFitService.localityById(preference.selectedAreaId, undefined, "CUREFIT-API")
                }
                const userLocationPreference = await CultUtil.getUserLocationPreference(preference, locality)
                await this.setLocationPreference(userContext, userId, cityId, userLocationPreference)
            } catch (err) {
                this.logger.error(`savePreference: error in updating user location preference while updating cult locality preference for userID - ${userId} and cityID - ${cityId}, error -  ${JSON.stringify(err)}`)
            }
        }

        if (preference.selectedFavourites) {
            // check if client is sending more than max favourites but backend throws error so splice the array before sending
            if (!_.isEmpty(preference.selectedFavourites) && preference.selectedFavourites.length > MAX_FAVOURITES_ALLOWED) {
                preference.selectedFavourites.splice(MAX_FAVOURITES_ALLOWED, preference.selectedFavourites.length - MAX_FAVOURITES_ALLOWED)
            }
            cultUserPreference.settings.push({
                "key": "USER_BOOKING_V2_FAVOURITE_CENTER",
                "value": preference.selectedFavourites
            })
            message = "Locating group classes at"
        }

        if (preference.currentSelection == "favourites") {
            message = "Locating group classes at"
        }

        if (!_.isNil(preference.selectedAreaId)) {
            cultUserPreference.settings.push({
                "key": "USER_BOOKING_V2_LOCALITY",
                "value": preference.selectedAreaId
            })
        }

        if (!_.isEmpty(preference.selectedLocation)) {
            if (userContext.sessionInfo.appVersion >= 8.49) {
                const cityAndCountry = await this.cityService.getCityAndCountry(AppUtil.getTenantFromUserContext(userContext), preference.selectedLocation.latitude, preference.selectedLocation.longitude)
                const detectedCityId = cityAndCountry && cityAndCountry.city ? cityAndCountry.city.cityId : undefined
                if (detectedCityId !== userContext.userProfile.cityId) {
                    throw this.errorFactory.withCode(ErrorCodes.CULT_LOCATION_PREFERENCE_UPDATE_ERR, 400).withMeta({ detectedCity: detectedCityId, currentCity: userContext.userProfile.cityId }).withDebugMessage(`Please change city to ${detectedCityId}`).build()
                }
            }
            // lat long is stored reversed in mongo as [long, lat]
            const placeData =  await LocationUtil.getLocationData(this.locationService, preference.selectedLocation.latitude, preference.selectedLocation.longitude)

            cultUserPreference.settings.push({
                "key": "USER_BOOKING_V2_LAT_LONG",
                "value": {
                    "latitude": preference.selectedLocation.latitude,
                    "longitude": preference.selectedLocation.longitude,
                    "name": placeData && placeData.placeId != "NOT_FOUND" ? placeData.name : null
                }
            })
        }
        const sessionInfo: SessionInfo = _.get(userContext, "sessionInfo")
        const { at } = sessionInfo
        const { sessionData } = sessionInfo
        const productType = preference.productType
        if (AuthUtil.isFixedGuestUser(userContext)) {
            if (productType === "FITNESS") {
                sessionData.cultPreference = cultUserPreference
            } else if (productType === "MIND") {
                sessionData.mindPreference = cultUserPreference
            }
            await this.sessionBusiness.updateSessionData(at, sessionData)
            return { status: true, message }
        } else {
            const result = productType === "FITNESS" ? this.cultFitService.savePreference(cultCityId, userId, cultUserPreference, "CUREFIT_API", userContext.userProfile.subUserId) :
                this.mindFitService.savePreference(cultCityId, userId, cultUserPreference, "CUREFIT_API", userContext.userProfile.subUserId)
            return {status: true, message }
        }
    }

    async setLocationPreference(userContext: UserContext, userId: string, cityId: string, preference: LocationPreferenceRequestEntity): Promise<UpdateStatusResponseEntity> {
        if (preference.prefLocationType == LocationDataKey.CURRENT_LOC) {
            if (!UserUtil.isValidCoordinates(userContext.sessionInfo.lat, userContext.sessionInfo.lon)) {
                return { success: false, message: `Please provide location permission`}
            }
            const checkValue = await UserUtil.isCoordinateInCity(userContext, cityId, userContext.sessionInfo.lat, userContext.sessionInfo.lon, this.cityService)
            if (!checkValue.ok) {
                return { success: false, message: `Selected City is ${checkValue.currentCityName} but detected city is ${checkValue.detectedCity.name}`, currentCityName: checkValue.currentCityName, detectedCity: checkValue.detectedCity }
            }
        }

        if (preference.prefLocationType == LocationDataKey.COORDINATES) {
            if (_.isNil(preference.coordinates) || _.isNil(preference.coordinatesName)) {
                return { success: false, message: `Map Location not found`}
            }
            const checkValue = await UserUtil.isCoordinateInCity(userContext, cityId, preference.coordinates.lat, preference.coordinates.long, this.cityService)
            if (!checkValue.ok) {
                return { success: false, message: `Selected City is ${checkValue.currentCityName} but detected city is ${checkValue.detectedCity.name}`, currentCityName: checkValue.currentCityName, detectedCity: checkValue.detectedCity }
            }
        }

        const userPreference: UserPreferenceV2 = {
            settings: []
        }

        if (!_.isNil(preference.prefLocationType)) {
            await UserUtil.updateLocationData(userContext, userId, cityId, LocationDataKey.LOC_PREF_TYPE, preference.prefLocationType, this.userAttributeClient, userPreference)
            let value: string
            switch (preference.prefLocationType) {
                case LocationDataKey.COORDINATES:
                    if (!_.isNil(preference.coordinates)) {
                        value = `${preference.coordinates.lat},${preference.coordinates.long}`
                        await UserUtil.updateLocationData(userContext, userId, cityId, LocationDataKey.COORDINATES_NAME, preference.coordinatesName, this.userAttributeClient, userPreference)
                    }
                    break
                case LocationDataKey.LOCALITY:
                    if (!_.isNil(preference.locality)) {
                        value = preference.locality
                    }
                    break
                case LocationDataKey.CURRENT_LOC:
                    value = `${userContext.sessionInfo.lat},${userContext.sessionInfo.lon}`
                    break
            }

            if (!_.isNil(value)) {
                await UserUtil.updateLocationData(userContext, userId, cityId, preference.prefLocationType, value, this.userAttributeClient, userPreference)
                if (!userContext.sessionInfo.isUserLoggedIn) {
                    const sessionData = userContext.sessionInfo.sessionData
                    sessionData.fitnessLocationPreference = userPreference
                    await this.sessionBusiness.updateSessionData(userContext.sessionInfo.at, sessionData)
                }
                return { success: true}
            }

            // Can change error message accordingly
            return { success: false, message: `Value for prefered location type ${preference.prefLocationType} is not provided` }
        }
        return { success: false, message: "Value for prefLocationType is not provided" }
    }

    async saveBookingConfirmationPreference(userContext: UserContext, cityId: string, userId: string, bookingConfirmationPreference: CultUserPreference) {
        const cultCityId = await this.getCultCityId(userContext, cityId, userId)
        return this.cultFitService.savePreference(cultCityId, userId, bookingConfirmationPreference, "CUREFIT_API", userContext.userProfile.subUserId)
    }


    async getClientMetadataForOrder(userContext: UserContext, createOrder: OrderCreate, product: Product): Promise<any> {
        const userId = userContext.userProfile.userId
        const membershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PURCHASED", "PAUSED"]))).obj
        if (membershipList) {
            const isExistingMember = _.some(membershipList, (membership) => !CultUtil.isMembershipExpired(userContext, membership))
            const cultPackUserType: CultPackUserType = isExistingMember ? "existing_member" : "new_member"
            return { cultPackUserType }
        }
        return undefined
    }

    async getPlayClientMetadataForOrder(userContext: UserContext): Promise<any> {
        const userId = userContext.userProfile.userId
        const membershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["PLAY"], ["PURCHASED", "PAUSED"]))).obj
        if (membershipList) {
            const isExistingMember = _.some(membershipList, (membership) => !CultUtil.isMembershipExpired(userContext, membership))
            const playPackUserType: CultPackUserType = isExistingMember ? "existing_member" : "new_member"
            return { playPackUserType }
        }
        return undefined
    }

    async checkMoneyBackOfferOnMembership(membership: Membership, userContext: UserContext): Promise<MoneyBackOfferDetail> {
        let isOfferApplied = false
        let isOfferExpired = false
        const tz = userContext.userProfile.timezone
        if (membership && membership.orderId && membership.orderId !== "null") {
            let order: Order
            try {
                 order = await this.omsApiClient.getOrder(membership.orderId)
            }
            catch (err) {
                this.logger.error("Error while fetching membership order", {orderId: membership.orderId, err})
            }
            if (!_.isEmpty(order)) {
                // this.logger.info(`Moneyback offer -> Checking for money back offer for orderId: ${membership.cfOrderId}`)
                const offerConfig = this.cultPackPageConfig.moneybackOfferInfo
                // this.logger.info(`Moneyback offer -> Offer Config: ${JSON.stringify(offerConfig)}`)
                if (!_.isEmpty(order.offersApplied) && !_.isEmpty(_.intersection(order.offersApplied, offerConfig.offerIds))) {
                    this.logger.info(`User ${userContext.userProfile.userId} is eligible for money back offer`)
                    isOfferApplied = true
                    // const limitInDays: number = offerConfig.limitInDays
                    // const packBuyDate: string = TimeUtil.formatDateInTimeZone(tz, order.createdDate)
                    // this.logger.info(`Moneyback offer -> packBuyDate: ${packBuyDate}`)
                    // const endDate: string = TimeUtil.addDays(tz, packBuyDate, limitInDays - 1) // doing -1 because of not including the last date
                    const startDate = TimeUtil.formatEpochInTimeZone(tz, membership.start)
                    const cancellationWindow = { startDate: startDate, endDate: offerConfig.cancellationEndDate }
                    const currentMillis = TimeUtil.getCurrentEpoch()
                    if (currentMillis > offerConfig.offerRedemptionTill) {
                        isOfferExpired = true
                    }
                    // this.logger.info(`Moneyback offer -> Final offer details for User ${userContext.userProfile.userId} is isOfferApplied: ${isOfferApplied} isOfferExpired: ${isOfferExpired} cancellationWindow: ${JSON.stringify(cancellationWindow)}`)
                    return { isOfferApplied, isOfferExpired, cancellationWindow }
                }
            }
        }
    }

    async checkIfUserIsEligibleForLivePackMonetisation(userContext: UserContext, user?: User): Promise<boolean> {
        if (AppUtil.isInternationalApp(userContext)) {
            return true
        }
        else if (AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            return false
        }
        return AppUtil.isLivePackSupported(userContext)
        /* {
            return false
        }

        const { clientVersion } = userContext.sessionInfo
        if (clientVersion < FREEMIUM_APP_SUPPORT_VERSION) {
            return true
        }

        const isUserPartOfExperiment1 = await this.checkIfUserPartOfLiveTrialExperiment1(userContext)
        if (!isUserPartOfExperiment1) {
            return true
        }

        const diyProductFulfilment: {
            productType: ProductType
            contentConsumedCount: number
        }[] = await this.diyFulfilmentService.getDIYProductFulfilmentInfo(userContext.userProfile.userId)
        if (!_.isEmpty(diyProductFulfilment)) {
            const isConsumed = diyProductFulfilment.some(productFulfilment => {
                if (productFulfilment.contentConsumedCount > 0) {
                    return true
                }
            })
            if (isConsumed) {
                return true
            }
        }

        const segments: string[] = await this.segmentationCacheClient.getUserSegments(userContext.userProfile.userId)
        return (!_.isEmpty(segments) && segments.includes("1 LIVE Class"))*/
    }

    async getCenterUnderMaintenance(userContext: UserContext, user: User, shutdownSchedule: ShutdownSchedule[], createAnnouncement?: boolean, announcementBusiness?: AnnouncementBusiness): Promise<any> {
        const tz = userContext.userProfile.timezone, announcementIdsForCenterShutdown = []
        let showCenterShutdownCard = false, centerShutdownDetailCards, centerShutdownDetailV2, shutdownScheduleDetails: any = undefined,
            showCenterShutdownWarningIcon = shutdownSchedule && AppUtil.isCenterShutdownSupported(userContext, user?.isInternalUser) && shutdownSchedule.length > 0
        if (AppUtil.isCenterShutdownSupported(userContext, user?.isInternalUser) && shutdownSchedule && shutdownSchedule.length > 0) {
            let description = ""
            if (shutdownSchedule.length > 1) {
                description = createAnnouncement ? "Some centres are scheduled for maintenance" : "View all centres scheduled for maintenance"
            } else {
                description = `${shutdownSchedule[0].center.name} is scheduled for maintenance`
            }
            const centerShutdownDetailCardsPromise = []
            const centerShutdownDetailPromiseV2 = []
            for (const shutdownCenter of shutdownSchedule) {
                const reopensTime = !shutdownCenter.partialDayShutdown ? `${momentTz.tz(shutdownCenter.endDate, tz).add("days", 1).format("DD MMM")} (${TimeUtil.diffInDays(userContext.userProfile.timezone, shutdownCenter.endDate, TimeUtil.todaysDate(userContext.userProfile.timezone)) + 1}days left)` : null
                const startTime = shutdownCenter.partialDayShutdown ? `${momentTz.tz(shutdownCenter.startDate, tz).format("DD MMMM")}` : `${momentTz.tz(shutdownCenter.startDate, tz).format("DD MMM")}, ${momentTz.tz(shutdownCenter.maintenanceStartTime, "HH:mm:ss", tz).format("hh:mm A")}`
                const maintenanceEndDate = shutdownCenter.partialDayShutdown ? `${momentTz.tz(shutdownCenter.endDate, tz).format("DD MMMM")}` : null
                const maintenanceTime = shutdownCenter.partialDayShutdown ? `${momentTz.tz(shutdownCenter.maintenanceStartTime, "HH:mm:ss", tz).format("hh:mm A")} to ${momentTz.tz(shutdownCenter.maintenanceEndTime, "HH:mm:ss", tz).format("hh:mm A")}` : "Whole day"
                if (createAnnouncement && announcementBusiness) {
                    const announcementDetails = <AnnouncementDetails>await announcementBusiness.createAndGetAnnouncementDetails(userContext, `CENTER_SHUTDOWN-${shutdownCenter.id} `)
                    if (announcementDetails.state === "CREATED") {
                        showCenterShutdownCard = true
                        showCenterShutdownWarningIcon = false
                    }
                    announcementIdsForCenterShutdown.push({
                        announcementId: announcementDetails.announcementId,
                        state: "DISMISSED"
                    })
                }
                const centerShutdownItems = [{
                    icon: "TIME",
                    title: `Maintenance start date: ${startTime} `
                },
                {
                    icon: "TIME",
                    title: shutdownCenter.partialDayShutdown ? `Maintenance end date: ${maintenanceEndDate}` : `Center reopens on: ${reopensTime}`
                }, {
                    icon: "TIME",
                    title: `Maintenance time: ${maintenanceTime} `
                }, {
                    icon: "LOCATION",
                    title: shutdownCenter.center.Address.addressLine1
                }]

                centerShutdownDetailCardsPromise.push({
                    centerName: shutdownCenter.center.name,
                    icon: CENTER_SHUTDOWN,
                    imageUrl: CENTER_SHUTDOWN_ICON,
                    status: shutdownCenter.reason.toUpperCase(),
                    items: centerShutdownItems,
                    note: shutdownCenter.note,
                })
                const holidayStartDate = `${momentTz.tz(shutdownCenter.startDate, tz).format("DD MMMM")}`
                const holidayEndDate = `${momentTz.tz(shutdownCenter.endDate, tz).format("DD MMMM")}`
                const shutdownStartTime = `${momentTz.tz(shutdownCenter.maintenanceStartTime, "HH:mm:ss", tz).format("h:mm A")}`
                const shutdownEndTime = `${momentTz.tz(shutdownCenter.maintenanceEndTime, "HH:mm:ss", tz).format("h:mm A")}`
                const maintenanceStartDateAndTime = `${holidayStartDate} ${shutdownStartTime}`
                const maintenanceEndDateAndTime = `${holidayEndDate} ${shutdownEndTime}`
                const shutdownReason: string = `Center will be closed due to ${shutdownCenter.reason}`
                let shutdownTitle: string
                if (holidayStartDate === holidayEndDate) {
                    shutdownTitle = `${shutdownCenter.center.name} will be closed on ${holidayStartDate} from ${shutdownStartTime} to ${shutdownEndTime}`
                } else {
                    shutdownTitle = `${shutdownCenter.center.name} will be closed from ${holidayStartDate} ${shutdownStartTime} to ${holidayEndDate} ${shutdownEndTime}`
                }
                const centerShutdownDetailV2: NudgeDetail = {
                    title: shutdownTitle,
                    titleColor: "#F7C744",
                    description: shutdownReason,
                    action: {
                        title: "KNOW MORE",
                        actionType: "OPEN_SHUTDOWN_MODAL",
                        meta: {
                            type: "CENTER_SHUTDOWN",
                            centerName: shutdownCenter.center.name,
                            status: shutdownReason.toUpperCase(),
                            startTitle: "Maintenance starts: ",
                            endTitle: "Maintenance ends: ",
                            startValue: maintenanceStartDateAndTime,
                            endValue: maintenanceEndDateAndTime,
                            note: shutdownReason,
                        }
                    }
                }
                centerShutdownDetailPromiseV2.push(centerShutdownDetailV2)


            }
            centerShutdownDetailCards = await Promise.all(centerShutdownDetailCardsPromise)
            centerShutdownDetailV2 = await Promise.all(centerShutdownDetailPromiseV2)

            shutdownScheduleDetails = {
                description: description,
                icon: CENTER_MAINTENANCE_WARNING,
                action: {
                    title: "KNOW MORE",
                    actionType: "SHOW_CAROUSEL_LIST",
                    meta: {
                        type: "CENTER_SHUTDOWN",
                        bundleProducts: centerShutdownDetailCards
                    }
                }
            }
            if (createAnnouncement) {
                shutdownScheduleDetails["dismissAction"] = {
                    actionType: "REST_API",
                    title: "DISMISS",
                    meta: {
                        method: "POST",
                        url: `/user/updateBulkAnnouncement`,
                        body: announcementIdsForCenterShutdown
                    }
                }
            }
        }
        return {
            shutdownScheduleDetails,
            showCenterShutdownCard,
            showCenterShutdownWarningIcon,
            centerShutdownDetailCards,
            centerShutdownDetailV2
        }
    }

    async checkForParQ(ex: any, userContext: UserContext, pageId: string) {

        if (ex.statusCode !== 412) {
            throw ex
        }

        const formId = pageId === "gymfit" ? (ex.meta.status === "USER_DETAILS_INCOMPLETE" ? "USER_DETAILS_PARQ_form" : "GYMFIT_PARQ_form") : "PARQ_form"
        const isPhoneBasedLoginSupported = AppUtil.isNewSigninFlowSupported(userContext)
        const user: User = await userContext.userPromise
        const actions: Action[] = [{
            actionType: "NAVIGATION",
            url: "curefit://userform?formId=" + formId,
            title: "CONTINUE"
        }]
        const dismissAction: Action[] = [{
            actionType: "HIDE_ALERT_MODAL",
            title: "DISMISS"
        }]
        const verifyEmailActions: Action[] = [{
            actionType: "NAVIGATION",
            url: "curefit://updateemail",
            title: "VERIFY EMAIL"
        }]
        const resendInstructionsAction: Action[] = [{
            actionType: "RESEND_PARQ_EMAIL",
            title: "RESEND MAIL"
        }]
        const parqActions = isPhoneBasedLoginSupported ? resendInstructionsAction : dismissAction
        let issues: IssueDetailView[]
        if (ex.meta.status === "UNDER_18" || ex.meta.status === "FAILED") {
            issues = await this.issueBusiness.getCultParQIssue(ex.meta.status, userContext)
            const issueManageOption = this.issueBusiness.toManageOptionPayload(issues, true)
            parqActions.unshift({
                title: "HELP",
                icon: "REPORT_ISSUE",
                actionType: "REPORT_ISSUE",
                actionId: "REPORT_ISSUE",
                meta: { issues: issues }
            })
            verifyEmailActions.unshift({
                title: "HELP",
                icon: "REPORT_ISSUE",
                actionType: "REPORT_ISSUE",
                actionId: "REPORT_ISSUE",
                meta: { issues: issues }
            })
        }


        switch (ex.meta.status) {
            case "UNDER_18":
                if (isPhoneBasedLoginSupported && (!user.email || !user.isEmailVerified)) {
                    throw new AlertError("Age proof pending", "You had indicated that you are below 18 years of age.\n\nWe have put your membership on hold pending submission of your age proof.\n\nPlease update and verify your email and we will send instructions on your email", verifyEmailActions, ex, {
                        modalWidth: 350,
                        modalHeight: 400
                    })
                } else {
                    throw new AlertError("Age proof pending", "You had indicated that you are below 18 years of age.\n\nWe have put your membership on hold pending submission of your age proof.\n\nPlease refer to the detailed instructions <NAME_EMAIL>.", parqActions, ex, {
                        modalWidth: 350,
                        modalHeight: 350
                    })
                }
            case "NOT_PROVIDED":
                throw new AlertError("LET'S GET STARTED!", "We love that you’re here! As part of our onboarding process, we request you to fill the PAR-Q i.e. Physical Activity Readiness Questionnaire.\n\nAll questions are designed to help assess any potential health risks associated with physical activity based on your health history.", actions, ex, {
                    modalWidth: 350,
                    modalHeight: 400
                })
            case "FAILED":
                if (isPhoneBasedLoginSupported && (!user.email || !user.isEmailVerified)) {
                    throw new AlertError("DOCTOR APPROVAL PENDING", "Your responses indicated that rigorous physical activity might not be safe for you.\n\nWe have put your membership on hold pending approval from your doctor.\n\nPlease update and verify your email and then refer to the detailed instructions <NAME_EMAIL>.\n\nWe will resume your membership within 48 hours of receiving a valid doctor’s approval.\n\nYour membership will be extended by the number of days it was on hold for.", verifyEmailActions, ex, {
                        modalWidth: 350,
                        modalHeight: 400
                    })
                } else {
                    throw new AlertError("DOCTOR APPROVAL PENDING", "Your responses indicated that rigorous physical activity might not be safe for you.\n\nWe have put your membership on hold pending approval from your doctor.\n\nPlease refer to the detailed instructions <NAME_EMAIL>.\n\nWe will resume your membership within 48 hours of receiving a valid doctor’s approval.\n\nYour membership will be extended by the number of days it was on hold for.", parqActions, ex, {
                        modalWidth: 350,
                        modalHeight: 370
                    })
                }
            case "USER_DETAILS_INCOMPLETE":
                throw new AlertError("LET'S GET STARTED", "We love that you’re here! But before you get going, we need some basic information about you.", actions, ex, {
                    modalWidth: 350,
                    modalHeight: 370
                })
        }
    }


    async getCultClassRecommendationData(userContext: UserContext, productType: ProductType, pageFrom: string, scheduleCenters: CultCenter[], centerIds?: string[]): Promise<{ [date: string]: CultClassRecommendationWidgetView }> {
        const recommendationEngine: RecommendationEngine = RecommendationEngine.CULT_RECOMMENDATION_ENGINE
        const recommendationRequest: RecommendationRequest = {
            timezone: userContext.userProfile.timezone,
            tenantID: productType === "FITNESS" ? 1 : 2,
            userInfo: {
                userId: userContext.userProfile.userId,
                location: {
                    cityId: userContext.userProfile.city.cultCityId.toString(),
                    latLong: {
                        lat: null,
                        long: null
                    }
                }
            },
            count: 20,
            filters: undefined,
            fromTimeEpoch: undefined
        }
        if (!_.isEmpty(centerIds)) {
            recommendationRequest.userInfo.location.favouriteCenters = _.join(centerIds, ",")
        }
        const recommendationsResponse = await this.recommendationService.getRecommendations(recommendationEngine, recommendationRequest, RecommendationSubmodule.CULT_PN_RECOMMENDATION)
        const scheduleCenterIds = _.map(scheduleCenters, center => center.id.toString())
        const recommendationCenterIds: string[] = []
        _.map(recommendationsResponse.recommendations, recommendation => {
            const centerId = (recommendation.productDetails as CultProductDetails).centerId.toString()
            if (!recommendationCenterIds.includes(centerId)) {
                recommendationCenterIds.push(centerId)
            }
        })
        const intersectionCenterIds = _.intersection(scheduleCenterIds, recommendationCenterIds)
        // if (intersectionCenterIds.length != recommendationCenterIds.length) {
        //     this.logger.info("~~Recommendation center Ids different: " + JSON.stringify(recommendationCenterIds))
        //     this.logger.info("~~Schedule center Ids: " + JSON.stringify(scheduleCenterIds))
        //     this.logger.info("~~Recommendation request: " + JSON.stringify(recommendationRequest))
        // }
        recommendationsResponse.recommendations = _.filter(recommendationsResponse.recommendations, recommendation => scheduleCenterIds.includes((recommendation.productDetails as CultProductDetails).centerId.toString()))
        const cultRecommendationByDateMap: { [date: string]: CultClassRecommendationWidgetView } = {}
        const classByDateTimeMap: { [id: string]: ClassByTimeV2 } = {}
        const classByDateTimeCenterMap: { [id: string]: ClassByCenter } = {}
        _.map(recommendationsResponse.recommendations, recommendation => {
            const cultProductDetails = recommendation.productDetails as CultProductDetails
            const recommendationDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, cultProductDetails.classDate, "YYYY-MM-DD")
            if (!cultRecommendationByDateMap[recommendationDate]) {
                cultRecommendationByDateMap[recommendationDate] = new CultClassRecommendationWidgetView()
                if (pageFrom === "addActivity") {
                    cultRecommendationByDateMap[recommendationDate].expanded = true
                }
            }
            const classByTimeListForDate = cultRecommendationByDateMap[recommendationDate].classByTimeList
            if (classByTimeListForDate.length < MAX_RECOMMENDATION_TO_BE_SHOWN) {
                const classStartTime = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, cultProductDetails.classDate, "HH:mm:ss")
                let classByTime: ClassByTimeV2 = classByDateTimeMap[recommendationDate + "_" + classStartTime]
                if (!classByTime) {
                    classByTime = {
                        id: classStartTime,
                        bookedCenterPosition: -1,
                        uniqueFavouriteCenters: new Set<number>(),
                        maxExpandedCenters: 1,
                        centerWiseClasses: []
                    }
                    classByDateTimeMap[recommendationDate + "_" + classStartTime] = classByTime
                    cultRecommendationByDateMap[recommendationDate].classByTimeList.push(classByTime)
                }
                const centerId = Number(cultProductDetails.centerId)
                let classByCenter = classByDateTimeCenterMap[recommendationDate + "_" + classStartTime + "_" + centerId]
                if (!classByCenter) {
                    classByCenter = {
                        centerId: centerId,
                        classes: []
                    }
                    classByDateTimeCenterMap[recommendationDate + "_" + classStartTime + "_" + centerId] = classByCenter
                    classByTime.centerWiseClasses.push(classByCenter)
                }
                const classView: IClassView = {
                    id: cultProductDetails.classId,
                    date: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, cultProductDetails.classDate, "YYYY-MM-DD"),
                    centerID: centerId,
                    endTime: undefined,
                    productType: productType,
                    startTime: classStartTime,
                    state: cultProductDetails.availability.cultAppAvailableSeats <= 0 && cultProductDetails.availability.waitlistAvailableSeats > 0 ? "WAITLIST_AVAILABLE" : "AVAILABLE",
                    workoutId: Number(cultProductDetails.workoutId),
                    workoutCategoryId: Number(cultProductDetails.workoutCategoryId),
                    workoutName: cultProductDetails.workoutName.toUpperCase(),
                    availableSeats: cultProductDetails.availability.cultAppAvailableSeats,
                    waitlistInfo: {
                        waitlistedUserCount: cultProductDetails.availability.waitlistedUserCount
                    }
                }
                classByCenter.classes.push(classView)
            }
        })
        if (!_.isEmpty(cultRecommendationByDateMap)) {
            const datesArray = Object.keys(cultRecommendationByDateMap)
            for (const date of datesArray) {
                if (cultRecommendationByDateMap[date]) {
                    cultRecommendationByDateMap[date].classByTimeList.sort((a: ClassByTimeV2, b: ClassByTimeV2) => {
                        return a.id < b.id ? -1 : 1
                    })
                    const classesCount = cultRecommendationByDateMap[date].classByTimeList.length
                    cultRecommendationByDateMap[date].header.title = `RECOMMENDED WORKOUT${classesCount > 1 ? "S" : ""} (${classesCount})`
                }
            }
        }
        return cultRecommendationByDateMap
    }

    async isBookFreeCultClassSupported(userContext: UserContext): Promise<ClassType> {
        try {
            let isCultTrialBookingSupported = false
            let isMindTrialBookingSupported = false
            const experimentId = BOOK_FREE_CLASS_TEXT_UPDATE_HAMLET_ID // owner "<EMAIL>"
            const isUserSupported = false

            if (isUserSupported) {
                const { membershipSummary: { current: { cult, mind } } }: CultSummary = await this.cacheHelper.getCultSummary(userContext.userProfile.userId)
                if (!cult && !mind) {
                    const cultTrialClass: UserTrialEligibility = await this.cultFitService.getUserTrialEligibility(userContext.userProfile.userId, "CUREFIT_APP")
                    const mindTrialClass: UserTrialEligibility = await this.mindFitService.getUserTrialEligibility(userContext.userProfile.userId, "CUREFIT_APP")

                    if (cultTrialClass.trialEligibility.isEligible) {
                        isCultTrialBookingSupported = true
                    }
                    if (mindTrialClass.trialEligibility.isEligible) {
                        isMindTrialBookingSupported = true
                    }
                }
            }
            return {
                cult: isCultTrialBookingSupported,
                mind: isMindTrialBookingSupported
            }
        } catch (e) {
            this.logger.error(`Error on checking if user is eligigle for trial class ${e} for userId: ${userContext.userProfile.userId}`)
            return {
                cult: false,
                mind: false
            }
        }
    }

    public async getNearbyCenters(
        userId: string,
        cultCityId: number,
        latitude: number,
        longitude: number,
        productType: ProductType,
        userContext: UserContext,
        showPreferredCenters?: boolean
    ): Promise<NearByCentersInfo> {
        const baseService = productType === "MIND" ? this.mindFitService : this.cultFitService
        let centers: NearByCenter[] = (await baseService.getCentersNearby(cultCityId, userId, "CUREFIT_API", latitude, longitude)).centers

        // Changes done for dogfooding sports category. Remove Sport Centers if it's not in dogfooding.
        const isCultSportsCategorySupported: boolean = await CultUtil.isCultSportsCategorySupported(userContext,
            this.hamletBusiness)
        if (!isCultSportsCategorySupported) {
            const sportCategoryCenters: number[] = await CultUtil.getSportCategoryCenterIds()

            centers = _.filter(centers, (workout) => {
                const centerPresent: number = _.find(sportCategoryCenters, (centerId: number) => {
                    if (centerId === workout.id) {
                        return true
                    }
                    return  false
                })
                if (centerPresent) {
                    return false
                }
                return true
            })
        }

        const preferredCenters: NearByCenter[] = _.filter(centers, center => {
            return center.type === "PREFERRED"
        })

        const normalCenters: NearByCenter[] = _.filter(centers, center => {
            return center.type === "NEARBY" || !center.type
        })

        const convenientCenters: NearByCenter[] = _.filter(centers, center => {
            return center.type === "CONVENIENT"
        })

        const isCenterAwayFromCity: boolean = _.filter(centers, center => {
            return (center.type === "CONVENIENT" || center.type === "NEARBY") && (center.distance * 1000) >= MAX_DISTANCE_CENTER_ALLOWED_IN_METRES
        }).length !== 0

        const convenientCenterList = getFindCenterLocationArray(convenientCenters, userContext, "CONVENIENT", _.isEmpty(preferredCenters) ? true : false)
        const preferredCenterList = getFindCenterLocationArray(preferredCenters, userContext, _.isEmpty(convenientCenterList) ? "PREFERRED" : undefined, _.isEmpty(convenientCenterList) ? true : false)
        const centerList = getFindCenterLocationArray(normalCenters, userContext, (!_.isEmpty(convenientCenterList) || !_.isEmpty(preferredCenterList)) ? "NEARBY" : undefined)
        const nearByCenterIndex = _.isEmpty(convenientCenterList) ? preferredCenterList.length : 0

        return {
            nearByCenterIndex: nearByCenterIndex,
            isCenterAwayFromCity: isCenterAwayFromCity,
            centers: [...convenientCenterList, ...preferredCenterList, ...centerList]
        }
    }

    public async getLivePTCrossGenderMatchingDetails(userContext: UserContext, patientId: number): Promise<{
        isCrossGenderEnabled: boolean
        genderText: string
        patient: Patient
    }> {
        const patient = await this.healthfaceService.getPatientDetails(patientId)
        const maleFemaleText = patient.gender
        const isCrossGenderEnabled = await this.healthfaceService.isPatientLivePTTrainerCrossGenderMatchEnabled(Number(patientId), "CULTFIT")
        return {
            isCrossGenderEnabled,
            genderText: maleFemaleText,
            patient
        }
    }

    public async getBookingCancellationInfo(userContext: UserContext, bookingNumber: string): Promise<any> {
        const userId = userContext.userProfile.userId
        const bookingCancellationInfo = await this.cultFitService.getCancellationNudgeDetailsByBookingNumber(bookingNumber, userId, "CUREFIT_API")
        return bookingCancellationInfo
    }
    public async isUserEligibleForInviteOffer(userContext: UserContext, campaignId: string): Promise<{ supported: boolean, card?: any }> {
        try {
            const isUserLoggedIn = userContext.sessionInfo.isUserLoggedIn
            // TODO: Remove intl check, once this check has been added at referral service
            if (isUserLoggedIn && !AppUtil.isInternationalApp(userContext) && AppUtil.isUserSupportedForInviteOffer(userContext) &&
                await this.redisDao.read("CULT_LIVE_REFERRAL_FEATURE") === "true") {
                const gitCard = await this.giftCardService.getOrCreateGiftCardsWithPolicy(
                    userContext.userProfile.userId,
                    campaignId,
                    userContext.userProfile.city.countryId
                )
                const [card] = _.get(gitCard, "data.summary.cards", [])
                const maxConsumptions = _.get(gitCard, "data.policyEntity.policy.cardConfig.maxConsumptions")
                if (card && card.consumedCount < maxConsumptions) {
                    return {
                        supported: true,
                        card: { ...card, campaignId }
                    }
                }
            }
            this.logger.info(`User not eligible for referral userId: ${userContext.userProfile.userId} campaignId: ${campaignId}`)
            return {
                supported: false
            }
        } catch (e) {
            this.logger.error(`Error while checking user eligibility for referral userId: ${userContext.userProfile.userId}  campaignId: ${campaignId}`, e)
            return {
                supported: false
            }
        }
    }


    public async getBookingScreenNudges(userContext: UserContext, productType: ProductType): Promise<any> {
        const bookingScreenNudge = await this.cultFitService.getBookingScreenNudgeForUser(userContext.userProfile.userId, "CUREFIT_API", userContext.userProfile.city.cultCityId)
        const user = await userContext.userPromise
        let widgetType, widgetData: any, position
        if (bookingScreenNudge) {
            const isBookingScreenFilterNudgesSupported = AppUtil.isBookingScreenFilterNudgesSupported(userContext)
            switch (bookingScreenNudge.type) {
                case "NUX_FILTER":
                case "FILTER": {
                    if (isBookingScreenFilterNudgesSupported) {
                        widgetType = "QUESTIONNAIRE_FORM_WIDGET"
                        widgetData = {
                            widgetType: widgetType,
                            isFilterNudge: true,
                            toolTipInfo: {
                                text: "You can disable the filters from here",
                            },
                            nudgeAction: {
                                actionType: "REST_API",
                                meta: {
                                    method: "POST",
                                    url: `/cult/bookingNudges/submitAction`,
                                    body: { nudgeType: bookingScreenNudge.type }
                                }
                            },
                            questionnaire: [{
                                question: {
                                    key: "q1",
                                    value: bookingScreenNudge.message
                                },
                                icon: "/image/icons/cult/arms.png",
                                answers: bookingScreenNudge.workouts,
                                submitText: "APPLY FILTER"
                            }],
                            analyticsData: {
                                widgetMetric: {
                                    nudgeType: bookingScreenNudge.type,
                                    widgetType: "QUESTIONNAIRE_FORM_WIDGET",
                                    widgetName: bookingScreenNudge.type
                                }
                            }
                        }
                        position = bookingScreenNudge.position
                    }
                    break
                }
                case "RENEW": {
                    widgetType = "IMAGE_LIST_WIDGET"
                    widgetData = {
                        widgetType: widgetType,
                        data: [{ image: "/image/icons/cult/sanitize.png", action: {} }, { image: "/image/icons/cult/antimicrobial_coating.png", action: {} }, { image: "/image/icons/cult/social_distance.png", action: {} }],
                        layoutProps: {
                            bannerWidth: 116,
                            bannerHeight: 128,
                            aspectRatio: "116:128",
                        },
                        listContainerStyle: { paddingLeft: 0 },
                        containerStyle: { backgroundColor: "rgba(56, 136, 255, 0.08)", flex: undefined, paddingBottom: 30, paddingTop: 15, paddingLeft: 30 },
                        header: {
                            title: "Membership Expired",
                            subTitle: bookingScreenNudge.message
                        },
                        headerContentStyle: {
                            style: {
                                backgroundColor: "rgba(56, 136, 255, 0.08)",
                                flex: undefined,
                                paddingTop: 15,
                                paddingLeft: 30,
                                paddingRight: 20,

                            },
                            titleProps: {
                                style: { color: "#383838", fontSize: 16 }
                            },
                            subTitleProps: {
                                style: { color: "#383838", fontSize: 14 }
                            },
                            seemore: {
                                actionType: "NAVIGATION",
                                title: "RENEW",
                                url: CultUtil.getCultMindClpBrowsePackAction("FITNESS"),
                                analyticsData: {
                                    nudgeType: bookingScreenNudge.type,
                                    widgetName: bookingScreenNudge.type
                                }
                            }

                        }
                    }
                    position = bookingScreenNudge.position
                    break
                }

                case "NEW_CENTER": {
                    widgetType = "MAP_INFO_WIDGET"
                    const coordinate = {
                        latitude: bookingScreenNudge.address.latitude,
                        longitude: bookingScreenNudge.address.longitude
                    }
                    const mapImageUrl = await this.locationService.signStaticMapUrl(CultUtil.getStaticImageUrlForMap(coordinate))
                    widgetData = {
                        widgetType: widgetType,
                        title: bookingScreenNudge.message,
                        iconUrl: "/image/icons/cult/info-new.png",
                        description: `${bookingScreenNudge.centerName}, ${bookingScreenNudge.address.addressLine1 ? bookingScreenNudge.address.addressLine1 : ""} ${bookingScreenNudge.address.addressLine2 ? bookingScreenNudge.address.addressLine2 : ""}`,
                        mapImageUrl: mapImageUrl,
                        action: {
                            title: "ADD TO FAVORITES",
                            actionType: "ADD_TO_FAVORITES",
                            analyticsData: {
                                eventKey: "widget_click",
                                eventData: {
                                    nudgeType: bookingScreenNudge.type,
                                    widgetName: bookingScreenNudge.type
                                }
                            },
                            meta: {
                                productType: "FITNESS",
                                centerIds: [bookingScreenNudge.centerID]
                            }
                        },
                        widgetMetric: {
                            centerId: bookingScreenNudge.centerID,
                        }
                    }
                    position = bookingScreenNudge.position
                    break
                }

                case "INFO": {
                    widgetType = "INFO_WIDGET_V3"
                    widgetData = {
                        widgetType: widgetType,
                        icon: "/image/icons/cult/info-new.png",
                        descriptionStyle: { flex: 1, color: "#787878", fontFamily: "BrandonText-Medium", fontSize: 14, textAlign: "left", marginLeft: 12 },
                        containerStyle: { backgroundColor: "#fafafa", paddingTop: 16, paddingLeft: 20, paddingRight: 20, paddingBottom: 25, flexDirection: "row", paddingHorizontal: 0, paddingVertical: 0 },
                        description: bookingScreenNudge.message
                    }
                    if (bookingScreenNudge.centerID) {
                        widgetData.widgetMetric = {
                            centerId: bookingScreenNudge.centerID,
                        }
                    }
                    widgetType = widgetType
                    break
                }

                case "NUX_RECOMMENDATION": {
                    if (isBookingScreenFilterNudgesSupported) {
                    widgetType = "CULT_CLASS_RECOMMENDATION_WIDGET_V1"
                    if (!_.isEmpty(bookingScreenNudge.recommendedClasses)) {
                        const classByDateMap: any = {}
                        bookingScreenNudge.recommendedClasses.map((recoClass: any) => {
                            if (classByDateMap[recoClass.date]) {
                                if (classByDateMap[recoClass.date][recoClass.Center.id]) {
                                    classByDateMap[recoClass.date] = { ...classByDateMap[recoClass.date], [recoClass.Center.id]: [...classByDateMap[recoClass.date][recoClass.Center.id], recoClass] }
                                }
                                else {
                                    classByDateMap[recoClass.date] = {...classByDateMap[recoClass.date], [recoClass.Center.id]: [recoClass] }
                                }
                            }
                            else {
                                classByDateMap[recoClass.date] = { [recoClass.Center.id]: [recoClass] }
                            }
                        })

                        const classByDateMapWidget: any = {}
                        for (const dates in classByDateMap) {
                            const recoClassesByCenterArray = []
                            for (const recoClassesByCenter in classByDateMap[dates]) {
                                const centerWiseClassMap = classByDateMap[dates][recoClassesByCenter].map((recoClass: any) => {
                                    return {
                                        cultClass: recoClass,
                                        slot: {
                                            id: recoClass.id.toString(),
                                            productType: productType,
                                            startTime: recoClass.startTime,
                                            workoutName: recoClass.Workout.name,
                                            classTime: recoClass.startTime,
                                            state: "AVAILABLE",
                                            nudgeType: bookingScreenNudge.type,
                                            widgetName: bookingScreenNudge.type
                                        },
                                        className: recoClass.Workout.name,
                                        time: recoClass.formattedStartTime
                                    }
                                })
                                recoClassesByCenterArray.push({
                                    icon: "/image/icons/cult/location_gray.png",
                                    tenantName: productType === "FITNESS" ? "cult.fit | " : "mind.fit | ",
                                    address: classByDateMap[dates][recoClassesByCenter][0].Center.name,
                                    classes: centerWiseClassMap
                                })

                            }
                            classByDateMapWidget[dates] = recoClassesByCenterArray
                        }
                        widgetData = {
                            widgetType: widgetType,
                            title: bookingScreenNudge.message,
                            classDetails: classByDateMapWidget,
                            isExpanded: true,
                            analyticsData: {
                                widgetMetric: {
                                    nudgeType: bookingScreenNudge.type,
                                    widgetType: "QUESTIONNAIRE_FORM_WIDGET",
                                    widgetName: bookingScreenNudge.type
                                }
                            }
                        }
                        position = bookingScreenNudge.position
                    }
                    break
                }
                }

                case "QA": {
                    const { isUserLoggedIn } = userContext.sessionInfo
                    widgetType = "QUESTIONNAIRE_FORM_WIDGET"
                    widgetData = {
                        widgetType: widgetType,
                        questionnaire: bookingScreenNudge.questionnaire.map((questions: any, index: number) => {
                            if (index === 0) {
                                questions.question.value = isUserLoggedIn ? `Hey ${user.firstName}, ${questions.question.value}` : questions.question.value
                            }
                            return {
                                question: questions.question,
                                icon: "/image/icons/cult/arms.png",
                                answers: questions.answers,
                                hintText: questions.isSingleSelect ? "(Select one)" : "",
                                isSingleSelect: questions.isSingleSelect,
                            }
                        }),
                        analyticsData: {
                            widgetMetric: {
                                nudgeType: bookingScreenNudge.type,
                                widgetType: "QUESTIONNAIRE_FORM_WIDGET",
                                widgetName: bookingScreenNudge.type
                            }
                        }
                    }
                    if (!isUserLoggedIn) {
                        widgetData.loginAction = {
                            actionType: "SHOW_ALERT_MODAL",
                            meta: {
                                title: "Login Required!",
                                subTitle: "Please login to continue",
                                actions: [{ actionType: "LOGOUT", title: "Login" }]
                            }
                        }
                    }
                    position = bookingScreenNudge.position
                    break
                }

                case "UNPAUSE": {
                    widgetType = "IMAGE_LIST_WIDGET"
                    const packUnpauseAction = CultUtil.getMembershipUnpauseAction(
                        bookingScreenNudge.membership,
                        productType,
                        userContext,
                        user.isInternalUser
                    )
                    widgetData = {
                        widgetType: widgetType,
                        data: [{ image: "/image/icons/cult/sanitize.png", action: {} }, { image: "/image/icons/cult/antimicrobial_coating.png", action: {} }, { image: "/image/icons/cult/social_distance.png", action: {} }],
                        layoutProps: {
                            bannerWidth: 116,
                            bannerHeight: 128,
                            aspectRatio: "116:128",
                        },
                        listContainerStyle: { paddingLeft: 0 },
                        containerStyle: { backgroundColor: "rgba(56, 136, 255, 0.08)", flex: undefined, paddingBottom: 30, paddingTop: 15, paddingLeft: 30 },
                        header: {
                            title: "Membership Paused",
                            subTitle: bookingScreenNudge.message
                        },
                        headerContentStyle: {
                            style: {
                                backgroundColor: "rgba(56, 136, 255, 0.08)",
                                flex: undefined,
                                paddingTop: 15,
                                paddingLeft: 30,
                                paddingRight: 20,

                            },
                            titleProps: {
                                style: { color: "#383838", fontSize: 16 }
                            },
                            subTitleProps: {
                                style: { color: "#383838", fontSize: 14 }
                            },
                            seemore: {
                                ...packUnpauseAction,
                                title: "UNPAUSE",
                            }

                        }
                    }
                    position = bookingScreenNudge.position
                    break
                }

                case "RECOMMENDATION": {
                    if (isBookingScreenFilterNudgesSupported) {
                    widgetType = "CULT_CLASS_RECOMMENDATION_WIDGET_V1"
                    if (bookingScreenNudge.recommendedClasses.recommendations && bookingScreenNudge.recommendedClasses.recommendations.length > 0) {
                        const classByDateMap: any = {}
                            bookingScreenNudge.recommendedClasses.recommendations.map(({ productDetails: recoClass }: any) => {
                            const recommendationDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, recoClass.classDate, "YYYY-MM-DD")
                            if (classByDateMap[recommendationDate]) {
                                if (classByDateMap[recommendationDate][recoClass.centerId]) {
                                    classByDateMap[recommendationDate] = { ...classByDateMap[recommendationDate], [recoClass.centerId]: [...classByDateMap[recommendationDate][recoClass.centerId], recoClass] }
                                }
                                else {
                                    classByDateMap[recommendationDate] = {...classByDateMap[recommendationDate], [recoClass.centerId]: [recoClass] }
                                }
                            }
                            else {
                                classByDateMap[recommendationDate] = { [recoClass.centerId]: [recoClass] }
                            }
                        })

                        const classByDateMapWidget: any = {}
                        for (const dates in classByDateMap) {
                            const recoClassesByCenterArray = []
                            for (const recoClassesByCenter in classByDateMap[dates]) {
                                const centerWiseClassMap = classByDateMap[dates][recoClassesByCenter].map((recoClass: any) => {
                                    const classStartTime = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, recoClass.classDate, "HH:mm:ss")
                                    const formattedStartTime = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, recoClass.classDate, "hh:mm A")
                                    return {
                                        cultClass: recoClass,
                                        slot: {
                                            id: recoClass.classId.toString(),
                                            productType: productType,
                                            startTime: classStartTime,
                                            workoutName: recoClass.workoutName,
                                            classTime: classStartTime,
                                            state: "AVAILABLE",
                                            nudgeType: bookingScreenNudge.type,
                                            widgetName: bookingScreenNudge.type
                                        },
                                        className: recoClass.workoutName,
                                        time: formattedStartTime
                                    }
                                })
                                recoClassesByCenterArray.push({
                                    icon: "/image/icons/cult/location_gray.png",
                                    tenantName: productType === "FITNESS" ? "cult.fit | " : "mind.fit | ",
                                    address: classByDateMap[dates][recoClassesByCenter][0].centerName,
                                    classes: centerWiseClassMap
                                })

                            }
                            classByDateMapWidget[dates] = recoClassesByCenterArray
                        }
                        widgetData = {
                            widgetType: widgetType,
                            title: `Hey ${user.firstName}, ${bookingScreenNudge.message}`,
                            icon: bookingScreenNudge.isActive ? null : "/image/icons/cult/cult_class_reco_emoji.png",
                            classDetails: classByDateMapWidget,
                            isExpanded: true
                        }
                        position = bookingScreenNudge.position
                    }
                    break
                }
            }
            }
        }
        if (widgetData && widgetType) {
            if (widgetData.widgetMetric) {
                widgetData.widgetMetric = {
                    ...widgetData.widgetMetric,
                    nudgeType: bookingScreenNudge.type
                }
            } else {
                widgetData.widgetMetric = {
                    nudgeType: bookingScreenNudge.type
                }
            }
            return { widgetData: widgetData, widgetType: widgetType, position: position }
        }
    }
}
