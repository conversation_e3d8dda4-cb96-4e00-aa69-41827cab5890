import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/vm-models"
import { DescriptionWidget, InfoCard, ProductDetailPage, SGTOneStepBookingPage } from "../common/views/WidgetView"
import { ALBUS_CLIENT_TYPES, BookingDetail, IHealthfaceService, TwilioChatChannelDetails } from "@curefit/albus-client"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { Duration, SimpleWod } from "@curefit/fitness-common"
import * as _ from "lodash"
import {
    Action,
    CultWorkoutSessionBreakupWidget, HeaderWidget, ImageOverlayCardContainerWidget,
    NoteListWidget, NoteWidget,
    WorkoutDetail,
    WorkoutSelectionListWidget
} from "@curefit/apps-common"
import { TimeUtil, Timezone } from "@curefit/util-common"
import CultUtil from "../util/CultUtil"
import { capitalizeFirstLetter } from "@curefit/base"
import { ConsultationProduct } from "@curefit/care-common"
import CareUtil from "../util/CareUtil"
import IssueBusiness, { IssueDetailView } from "../crm/IssueBusiness"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { WodDetailsResponse } from "@curefit/cult-common"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
@injectable()
export default class SGTPreBookingDetailPageViewBuilder extends ProductDetailPage {

    constructor(
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(HERCULES_CLIENT_TYPES.IHerculesService) private herculesService: IHerculesService,
        @inject(CUREFIT_API_TYPES.IssueBusiness) private issueBusiness: IssueBusiness,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService
    ) {
        super()
    }

    async buildView(userContext: UserContext, startTime: string, productCode: string, patientId: number, parentBookingId: number, focusAreaId: string, wodId: string, endTime?: string): Promise<ProductDetailPage> {
        const productDetailPage = new ProductDetailPage()
        const userId = userContext.userProfile.userId
        if (_.isNil(patientId)) {
            const patientsList = await this.healthfaceService.getAllPatients(userId)
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            patientId = selfPatient.id
        }
        const slotDetails = await this.healthfaceService.getSlotDetails(
            userId,
            patientId,
            parentBookingId,
            startTime,
            productCode,
            "CULTFIT",
            Number(focusAreaId)
        )
        const wod: WodDetailsResponse = await this.cultFitService.getWodDetails(userContext.userProfile.userId, wodId, true, 1)
        const wodSchedule = slotDetails.wodSchedule.find((wod: any) => {
            return wod.sgtWodSchedule.focusAreaId === Number(focusAreaId)
        })
        productDetailPage.widgets.push(this.getSummaryWidget(userContext, slotDetails.consultationProduct, startTime, wod, wodSchedule))
        productDetailPage.widgets.push( await this.getDescriptionWidget(userContext, slotDetails, wodSchedule, wod, wodId))
        productDetailPage.widgets.push(this.getNoteListWidget(slotDetails, userContext))
        if (slotDetails.bookedByPatient && !_.isNull(slotDetails.sgtConsultationInventoryBookingDetails)) {
            if (slotDetails.sgtconsultationInventoryBookingDetails.focusAreaId !== Number(focusAreaId)) {
                productDetailPage.actions.push({
                    title: "CONFIRM & BOOK",
                    actionType: "SHOW_ALERT_MODAL",
                    meta: {
                        title: "Overlapped Booking",
                        subTitle: "You have another class booked at this time slot.",
                        actions: [{actionType: "HIDE_ALERT_MODAL", title: "Ok"}]
                    }
                })
            }
        } else {
            productDetailPage.actions.push({
                actionType: "BOOK_SGT_CLASS",
                title: "CONFIRM & BOOK",
                meta: {
                    patientId,
                    startTime,
                    parentBookingId,
                    endTime,
                    productId: productCode,
                    focusAreaId,
                    isFromDetailPage: true,
                }
            })
        }
        return productDetailPage
    }
    private getSummaryWidget(userContext: UserContext, product: any, startTime: string, wod: WodDetailsResponse, wodSchedule: any): ImageOverlayCardContainerWidget {
            const imageOverlayContainerWidget = new ImageOverlayCardContainerWidget()
            imageOverlayContainerWidget.assets.push({
                assetType: "IMAGE",
                assetUrl: product.heroImageUrl
            })
            const isLiveSGTProduct = CareUtil.isLiveSGTProduct(product)
            const isLivePTProduct =  CareUtil.isLivePTProduct(product)
            const subTitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, Number(startTime), "ddd, MMM Do, hh:mm A")
            const actions: Action[] = []
            let moreAction: Action
            if (!_.isEmpty(actions)) {
                moreAction = {
                    actionType: "ACTION_LIST",
                    actions
                }
            }
            const title = wodSchedule ? CareUtil.getWorkoutName(wodSchedule?.sgtWodSchedule?.workoutName, wodSchedule?.sgtWodSchedule?.focusAreaName) : product.name
            const duration = product?.productSpecs?.actualClassDuration ? product?.productSpecs?.actualClassDuration : Math.floor(product.duration / 60000)
            const calorieCount = wodSchedule ? wodSchedule.calorieCount + " kcal" : wod.calorieValue
            const headerWidget: HeaderWidget = {
                widgetType: "HEADER_WIDGET",
                widgetTitle: {
                    title: title,
                    subTitle: subTitle
                },
                headerStyle: {
                    marginLeft: 0,
                    fontSize: 22,
                    color: "#000000",
                    width: "90%"
                },
                subTitleStyle: {
                    marginLeft: 0,
                    fontSize: 14
                },
                subHeader: { text: `${duration} min · ${calorieCount}`}
            }
            imageOverlayContainerWidget.widgets.push(headerWidget)
            return imageOverlayContainerWidget
    }

    private async getDescriptionWidget(userContext: UserContext, slotDetails: any, wodSchedule: any, wod: WodDetailsResponse , wodId: string): Promise<NoteWidget> {


        // let wod: SimpleWod = null
        if (wodId) {
            wod = await this.herculesService.getSimpleWodById(wodId)
        }
        const mainWorkoutsString: string = CultUtil.getWodMovements(wod)
        let mainWorkouts: string[] = []
        if (mainWorkoutsString) mainWorkouts = mainWorkoutsString.split(", ")
        const subTitle = wodSchedule?.sgtWodSchedule?.description ? wodSchedule?.sgtWodSchedule?.description : slotDetails?.consultationProduct?.description
        const descriptionWidget = new NoteWidget()
        descriptionWidget.widgetType = "NOTE_WIDGET"
        descriptionWidget.title = "About"
        descriptionWidget.notes = mainWorkouts
        descriptionWidget.subTitle = subTitle
        descriptionWidget.containerStyle = {
            backgroundColor: "#ffffff"
        }
        descriptionWidget.headerStyle = {
            fontSize: 18,
            color: "#000000"
        }
        return descriptionWidget
    }

    getNoteListWidget(slotDetails: any, userContext: UserContext): NoteListWidget {
        const userType: string = slotDetails.userType
        const widget: NoteListWidget = {
            widgetType: "NOTE_LIST_WIDGET",
            note: {
                title: "NOTE",
                color: "#000000",
            },
            data: [
                {
                    icon: "/image/icons/livept/video_30.png",
                    info: "The class will be held on a video call. Video link will be shared 5 mins before the session.",
                },
                {
                    icon: "/image/icons/cult/tshirt.png",
                    info: "Wear comfortable clothes and shoes. Keep a water bottle and towel close by.",
                },
                {
                    icon: "/image/icons/livept/cancel_30.png",
                    info: "Cancel the class upto 1 hour before the class.",
                    description: `Cut-off: ${TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, slotDetails.startTime - (slotDetails.consultationProduct.cancellationWindowInMins * 60000), "Do MMM, hh:mm a")}`
                },
                {
                    icon: "/image/icons/livept/Laptop_big.png",
                    info: "We recommend joining the session from the website/laptop for the best experience.",
                },
            ]
        }
        return widget
    }
}