import { Cult<PERSON>enter } from "@curefit/cult-common"
import { Address } from "@curefit/eat-common"
import { ProductType } from "@curefit/product-common"
import { User } from "@curefit/user-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import {
    Action,
    ActionCard,
    CenterAddressWidget,
    CenterOtherDetailsItem,
    CenterOtherDetailsWidget,
    Header,
    InfoCard,
    ProductDetailPage,
    ProductGridWidget,
    ProductSummaryWidget
} from "../common/views/WidgetView"
import { SeoUrlParams, ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import CultUtil from "../util/CultUtil"
import { UserContext } from "@curefit/userinfo-common"
import AppUtil from "../util/AppUtil"
import { TimeUtil } from "@curefit/util-common"

class CenterDetailView extends ProductDetailPage {
    constructor(center: CultCenter, productType: ProductType, userContext: User<PERSON>ontext, user: User) {
        super()
        const userAgent: UserAgent = userContext.sessionInfo.userAgent
        this.meta = {
            status: center?.state
        }
        this.widgets.push(this.getCenterSummayWidget(center))
        this.widgets.push(this.getCenterAddressWidget(center, userContext))
        if (center.facilities)
            this.widgets.push(this.getFacilitiesWidget(center))
        if (!_.isEmpty(center.workouts))
            this.widgets.push(this.getWorkoutsWidget(center, productType, userContext.sessionInfo.userAgent))
        if (!_.isEmpty(center.trainers))
            this.widgets.push(this.getTrainerWidget(center, productType, userContext.sessionInfo.userAgent))
        if (userAgent === "MBROWSER" || userAgent === "DESKTOP") {
            this.widgets.push(this.getOtherDetails(userContext, center))
        }
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
        const action: Action = {
            actionType: "NAVIGATION",
            title: "Book Class",
            url: ActionUtil.getBookCultClassUrl(productType, isNewClassBookingSupported, "centerPage", undefined, center.id.toString())
        }
        this.actions = [action]
    }

    private getCenterSummayWidget(center: CultCenter): ProductSummaryWidget {
        // const document = center.documents.find((document) => (document.tagName  === "TITLE"))
        const document = _.find(center.documents, { tagName: "PRODUCT_BNR" })
        const productSummaryWidget: ProductSummaryWidget = {
            widgetType: "PRODUCT_SUMMARY_WIDGET",
            title: center.name,
            subTitle: "",
            image: document ? "/" + document.URL : undefined
        }
        return productSummaryWidget
    }

    private getCenterAddressWidget(center: CultCenter, userContext?: UserContext): CenterAddressWidget {
        const addressString = CultUtil.getCultCenterAddress(center, userContext)
        const centerAddressWidget: CenterAddressWidget = {
            widgetType: "CENTER_ADDRESS_WIDGET",
            header: {
                title: "Address",
            },
            mapUrl: center.placeUrl,
            addressText: addressString,
            latLong: {
                lat: center.Address.latitude,
                long: center.Address.longitude
            }
        }
        return centerAddressWidget
    }

    private getFacilitiesWidget(center: CultCenter): ProductGridWidget {
        const header: Header = {
            title: "Facilities at this centre"
        }
        const facilities: InfoCard[] = []
        const availableFacilities = center.facilities.filter(facility => { return facility.available })
        availableFacilities.forEach(facility => {
            facilities.push({
                title: facility.name,
                image: "/image/icons/centerFacility/" + facility.type.toLowerCase() + "/2.png"
            })
        })
        const facilitiesWidget: ProductGridWidget = new ProductGridWidget("ICON", header, facilities)
        return facilitiesWidget
    }

    private getWorkoutsWidget(center: CultCenter, productType: string, userAgent: UserAgent): ProductGridWidget {
        const header: Header = {
            title: "Available workouts"
        }
        const workouts: ActionCard[] = []
        center.workouts.forEach(workout => {
            const seoParams: SeoUrlParams = {
                productName: workout.name
            }
            const workoutAction = ActionUtil.cultWorkoutV2(workout.id.toString(), undefined, userAgent, seoParams, productType)
            let thumbDoc = undefined
            if (userAgent === "APP") {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "M_CARD")
            } else {
                thumbDoc = _.find(workout.documents, doc => doc.tagName === "D_CARD")
            }
            workouts.push({
                title: workout.name,
                image: thumbDoc ? "/" + thumbDoc.URL : undefined, // TODO: Finalize and use tagged document image from cult
                action: workoutAction
            })
        })
        const workoutsWidget: ProductGridWidget = new ProductGridWidget("SQUARE", header, workouts)
        return workoutsWidget
    }


    private getTrainerWidget(center: CultCenter, productType: string, userAgent: UserAgent): ProductGridWidget {
        const header: Header = {
            title: "Trainers"
        }
        const trainers: ActionCard[] = []
        center.trainers.forEach(trainer => {
            let thumbDoc = undefined
            if (userAgent === "APP") {
                thumbDoc = _.find(trainer.documents, doc => doc.tagName === "M_CARD")
            } else {
                thumbDoc = _.find(trainer.documents, doc => doc.tagName === "D_CARD")
            }
            const image = thumbDoc ? "/" + thumbDoc.URL : undefined
            const description = CultUtil.getCultTrainerDescription(trainer.description)
            trainers.push({
                title: trainer.name,
                image: image,
                action: ActionUtil.trainerInfoCard(trainer.name, "", description, image)
            })
        })
        const trainersWidget: ProductGridWidget = new ProductGridWidget("SQUARE", header, trainers)
        return trainersWidget
    }

    private getOtherDetails(userContext: UserContext, center: CultCenter): CenterOtherDetailsWidget {
        let startTime = center.businessStartTime
        let endTime = center.businessEndTime
        const header = "Other Details"
        const existingTimeFormat = "HH:mm:ss"
        const requiredFormat = "hh:mm"
        const data: CenterOtherDetailsItem[] = [{
            type: "EMAIL",
            title: "EMAIL",
            subTitle: "<EMAIL>"
        }]
        if (startTime && endTime) {
            startTime = TimeUtil.convertDateInFormatToAnotherFormat(userContext.userProfile.timezone, startTime, existingTimeFormat, requiredFormat)
            endTime = TimeUtil.convertDateInFormatToAnotherFormat(userContext.userProfile.timezone, endTime, existingTimeFormat, requiredFormat)
            const timingSubTitle = `Morning ${startTime} - Night ${endTime}`
            data.unshift({ type: "TIME", title: "PREFERRED TIMING", subTitle: timingSubTitle })
        }
        const centerOtherDetailsWidget: CenterOtherDetailsWidget = new CenterOtherDetailsWidget(data, header)
        return centerOtherDetailsWidget
    }
}

export default CenterDetailView
