import DeliveryAreaView from "../user/DeliveryAreaView"
import UserAddressView from "../user/UserAddressView"
import { PlaceData } from "@curefit/location-common"
import { CartOffer } from "@curefit/offer-common"
import { ProductMenu } from "@curefit/eat-common"
import { Action, WidgetView } from "../common/views/WidgetView"
import { AnnouncementView } from "../announcement/AnnouncementViewBuilder"
import { PageSelectorType } from "@curefit/vm-common"
import { TooltipAnnouncement } from "@curefit/apps-common"
import { SupportActionableCardWidget } from "./PageWidgets"

export interface CityView {
    isSelected: boolean
    cityId: string
    name: string
}
export interface Page {
    userLocation: string
    cityName?: string
    widgets?: PageWidget[]
}

export interface EatClpPage extends Page {
    area?: DeliveryAreaView
    address?: UserAddressView
    placeData?: PlaceData
}

export interface EatClpPageV2 extends EatClpPage {
    header?: PageWidget
    sections?: EatFitMealSlot[]
    lists?: { [key: string]: PageWidget[] }
    selectedTabIndex?: number
    footer?: PageWidget
    cartOffers?: CartOffer[]
}

export interface Section {
    id: string
    name: string
}

export interface CultClpPageV2 extends Page {
    header: PageWidget
    sections: Section[]
    lists: { [key: string]: (PageWidget | WidgetView)[] }
    selectedTabIndex?: number
    footer: PageWidget
}

export interface CareDateSelectorPage extends Page {
    key: string
    sections: Section[]
    lists: { [key: string]: PageWidget[] }
    selectedTabIndex?: number
}

export type DATE_PICKER_HEADER_TYPE = "CENTER_SELECTOR" | "STATIC"

export interface CareDatePickerView {
    key: string
    userLocation?: string
    header: {
        title: string
        type?: DATE_PICKER_HEADER_TYPE,
        action?: Action
    }
    widgets: PageWidget[],
    footerWidget?: PageWidget[]
    announcementView?: AnnouncementView
    tooltipAnnouncement?: TooltipAnnouncement
    noSlotsInfo?: NoSlotsInfo
}

export interface NoSlotsInfo {
    title: string
    subtitle: string
    imageUrl: string
}

export interface EatFitMealSlot {
    id: string
    name: string
    mealName: string
}

export interface MealAvailability {
    day: string
    inventoryResult: { [campaignType: string]: { [packId: string]: { left: number, total: number } } }
    menus: ProductMenu[]
}

export type PageWidgetType = "BANNER_WIDGET" |
    "CATEGORY_WIDGET" |
    "SUB_CATEGORY_TYPE_1_WIDGET" |
    "SUB_CATEGORY_TYPE_2_WIDGET" |
    "SUB_CATEGORY_TYPE_3_WIDGET" |
    "CATEGORY_TILE_WIDGET" |
    "CARD_SCROLL_WIDGET" |
    "BANNER_DESC_CARD_WIDGET" |
    "CATEGORY_ACTION_CARD_WIDGET" |
    "ACTION_CARD_LIST_WIDGET" |
    "MAGAZINE_GRID_WIDGET" |
    "INTEREST_WIDGET" |
    "CULT_BUY_PACK_WIDGET" |
    "CULT_BOOK_CLASS_WIDGET" |
    "CULT_CENTER_GRID_WIDGET" |
    "CULT_CENTER_AREA_WIDGET" |
    "EAT_BUY_MEAL_WIDGET" |
    "MAGAZINE_LIST_WIDGET" |
    "GRID_WIDGET" |
    "HEADER_WIDGET" |
    "PACK_PROGRESS_WIDGET" |
    "OFFER_WIDGET" |
    "IMAGE_WIDGET" |
    "PRODUCT_LIST_WIDGET" |
    "PRODUCT_OFFER_WIDGET" |
    "IMAGE_GRID_WIDGET" |
    "MEAL_LIST_ITEM_WIDGET" |
    "TITLE_WIDGET" |
    "MEAL_GRID_WIDGET" |
    "CLP_FOOTER_WIDGET" |
    "PROFILE_LIST_WIDGET" |
    "EXPERIENCE_WIDGET" |
    "HOW_IT_WORKS_WIDGET" |
    "CLP_IMAGE_WIDGET" |
    "VIDEO_CAROUSEL_WIDGET" |
    "GROUPED_MAGAZINE_LIST_WIDGET" |
    "PACK_PROGRESS_LIST_WIDGET" |
    "OFFER_INFO_WIDGET" |
    "OGM_WIDGET" |
    "DATES_AVAILABLE_WIDGET" |
    "EVENT_GRID_WIDGET" |
    "MAGAZINE_ROW_WIDGET" |
    "FOOD_SINGLES_RECOMMENDATION_WIDGET" |
    "PACK_RECOMMENDATION_WIDGET" |
    "CALLOUT_PAGE_WIDGET" |
    "PREFERED_DOCTOR_LIST_WIDGET" |
    "DIAGNOSTICS_TEST_LIST_WIDGET" |
    "CARE_CONSULTATION_WIDGET" |
    "DOCTOR_LIST_WIDGET" |
    "CARE_PRODUCT_STATE_WIDGET" |
    "DIAGNOSTICS_TEST_REPORT_SUMMARY_WIDGET" |
    "DIAGNOSTICS_TEST_DETAILED_SUMMARY_WIDGET" |
    "DIAGNOSTICS_TEST_REPORT_DETAIL_WIDGET" |
    "LISTING_ACTIONABLE_CARD_WIDGET" |
    "SUPPORT_ACTIONABLE_CARD_WIDGET" |
    "CARE_ME_EMPTY_LISTING_WIDGET" |
    "SUPPORT_EMPTY_LISTING_WIDGET" |
    "CHAT_LISTING_ACTIONABLE_CARD_WIDGET" |
    "TYPOGRAPHY_WIDGET" |
    "QUICK_LINKS_WIDGET"

export type CellType = "PACK" | "RECIPE" | "MEAL" | DiagnosticTestDetailCellType

export type DiagnosticTestDetailCellType = "NUMERICAL" | "WORDS"


export interface PageWidget extends WidgetView {
    hasTopPadding?: boolean,
    hasBottomPadding?: boolean,
    cellType?: CellType,
    titleWidget?: PageWidgetTitle // Deprecate this
    widgetTitle?: PageWidgetTitle
    moreWidget?: PageWidgetMore,
    analyticsData?: any,
    showDivider?: boolean
    isHeaderHeightDynamic?: boolean
}

export interface PageWidgetWithSource {
    widget: SupportActionableCardWidget,
    source: string
}

export interface PageWidgetTitle {
    icon?: string
    title: string
    subTitle?: string
    info?: string
    headerTopInfo?: string
}

export interface PageTitle {
    title: string
    subTitle?: string
    description?: string
}

export interface PageWidgetMore {
    action: string,
    text: string
    title?: string,
    productId?: string
}

export interface PageWidgetFooter {
    action: string,
    title: string,
    subTitle?: string,
    type?: string
}

export interface PageProduct {
    productId: string,
    image: string,
    action: string
}

export interface Offer {
    showOffer: boolean,
    icon: string
}

export class CampaignOfferDescription {
    constructor(public campaignType: String, expiryDate: string, offerId: string, offerDurationDays: string) {
        this.gradient = []
        this.gradient.push("#5c7fe4")
        this.gradient.push("#35e4ca")
        this.subtitle = "See the doctor in " + offerDurationDays + " days"
        this.action = "curefit://carefittc?id=TELECONSULTATION&offerId=" + offerId

        if (campaignType === "CARE_FREE_FOLLOWUP_TC") {
            this.title = "Book a Follow up"
            this.text = "Free follow-up till" + expiryDate
        }
        else if (campaignType === "CARE_FREE_TC_ON_HCU") {
            this.title = "Book a consultation for Health Checkup"
            this.text = "Free consult till " + expiryDate
        }
        else if (campaignType === "CARE_FREE_HCU") {
            this.title = "Book a health Checkup"
            this.subtitle = "First booking free "
            this.text = ""
            this.action = "curefit://carefithcu?id=GENERAL_HEALTHCHECKUP&offerId=" + offerId
        }

    }

    public title: string
    public subtitle: string
    public text: string
    public action: string
    public gradient: string[]
}

export { PageSelector, AreaPageSelector, CityPageSelector } from "@curefit/apps-common"

export const PAGE_ID = {
    LIVE_CLASS_DETAIL_VIEW_V2: "live_class_detail_view_v2",
    GIFTCARD_USER_REFERRAL: "giftcard_user_referral",
    CULT_BOOKING_RECO_VIEW: "cult_booking_reco_view",
}
