import {
    DailyMealWidget,
    GuranteeInfo,
    HorizontalActionableCardListType, IBaseWidget,
    LAYOUT_TYPE, UserContext,
    VideoCardItem
} from "@curefit/vm-models"
import {
    CellType,
    DiagnosticTestDetailCellType,
    PageWidget,
    PageWidgetFooter,
    PageWidgetMore,
    PageWidgetTitle,
    PageWidgetType
} from "./Page"
import {
    Action, GradientCard, GradientCarouselCard,
    Header,
    Icon,
    InfoCard, InfoItem,
    ListSubType,
    ManageOptionIcon,
    WidgetType,
    WidgetView
} from "../common/views/WidgetView"
import CenterView from "../cult/CenterView"
import { DiyContentDetail, DiyContentMeta } from "../util/AtlasUtil"
import { HowItWorksItem, InstructionItem, ProductPrice, ProductType } from "@curefit/product-common"
import { ListingBrandIdType, MealSlot } from "@curefit/eat-common"
import { MeasurementUnit, NutritionTag } from "@curefit/food-common"
import { Doctor } from "@curefit/care-common"
import {
    ActionV2,
    DividerType,
    ImageInfoProductType,
    ImageType,
    Orientation,
    ProductGuranteeWidgetProductType
} from "@curefit/vm-common"
import {
    CultWaitlistSlotInfo,
    IDiagnosticsCartAddonListWidget, IDiagnosticsCartAddonListWidgetAction,
    IDiagnosticsTestListWidget,
    IDiagnosticTestListDetailWidget,
    IWidgetType,
    TooltipAnnouncement, ViewStyle,
    WidgetType as AppWidgetType,
    TextStyle
} from "@curefit/apps-common"
import { OrderDetail } from "../order/OrderViewBuilder"
import { FMMealNutritionProfile } from "../eat/marketplace/FMMealNutritionProfile"
import { Media } from "@curefit/gymfit-common"
import { MediaType } from "aws-sdk/clients/ecr"
import { integer } from "aws-sdk/clients/cloudfront"

export interface Banner {
    id: string
    image: string
    desktopWebImage?: string
    mobileWebImage?: string
    action: string | Action
    callToAction?: string
    expiryDate?: Date
    cityId?: string,
    contentMetric?: any
}

export interface Video extends Banner {
    title: string
}


export interface Category {
    title: string,
    action: string,
    icon?: string,
    image?: string,
    gridImage?: string
    cityId: string
}

export interface Pack {
    title: string,
    price?: ProductPrice,
    action: string,
    image: string,
    subTitle: string
}

export type PackProductType = ProductType | "FITNESS_SELECT_CENTRE" | "MIND_SELECT_CENTRE"

export interface ActionCard {
    title?: string,
    price?: ProductPrice,
    action?: string,
    image?: string,
    subTitle?: string,
    metaText?: string
    description?: string,
    tryAction?: ActionCard,
    isOfferApplied?: boolean,
    colors?: string[],
    analyticsData?: any,
    actionObj?: Action
}

export interface PackActionCard extends ActionCard {
    productType?: PackProductType
}

export interface ConsultationActionCard {
    colors: string[]
    title: string
    icon: string
    action: Action
    productId: string
    price: ProductPrice
    type: "INCENTRE_VISIT" | "VIDEO_CALL"
}

export interface RecipeActionCard extends ActionCard {
    skipPrice: boolean,
    isVeg: boolean,
    duration: string,
    type: string
}

export interface ImageCard {
    image: string,
    action: string
}

export interface PackProgressActionCard extends ActionCard {
    productType: ProductType
    state: string
    total: number
    completed: number
    expiryMessage?: string
    footerAction?: ActionCard
    packId?: string
    fulfilmentId?: string
    mealSlot?: MealSlot,
    resumeDate?: string,
    resumeSlotId?: string,
    cta?: string,
    icon?: ManageOptionIcon
}

export interface DiyPlayAction extends Action {
    content: DiyContentDetail
    meta: DiyContentMeta
    image: string
}

export type PackProgressItemViewType = "PROGRESS" | "EXPIRY"

export interface PackProgress {
    title: string
    productType: ProductType
    subTitle: string
    action: Action
    state: string
    image: string
    total?: number
    completed?: number
    awayCityMessage?: string
    expiryMessage?: string
    widgetActions: (Action | DiyPlayAction)[]
    primaryAction?: Action
    subAction?: Action
    packId?: string
    fulfilmentId?: string
    mealSlot?: MealSlot
    clientState?: string,
    highlightedExpiryMessage?: string,
    itemViewType?: PackProgressItemViewType
}

export interface PackProgressListWidget extends PageWidget {
    items: PackProgress[]
}

export interface MealActionCard extends ActionCard {
    isAvailable: boolean
    calories: string
}

export interface FooterText {
    fontType: string
    fontSize: string
    desktopWebFontSize?: string
    mobileWebFontSize?: string
    text: string
}

export interface ClpFooterWidget extends PageWidget {
    texts: FooterText[]
    image?: string
}

export interface OgmWidget extends PageWidget {
    title: string
    subTitle: string
    actionTitle: string
    icon: string
    action: Action
    widgetType: WidgetType
}

export interface MealItem extends FMMealItem {
    title: string
    titleWithoutUnits?: string
    productId: string
    stock: number
    isInventorySet: boolean
    calories: string | number
    categoryId?: string
    foodCategoryId?: string
    offerIds?: string[]
    price: ProductPrice
    date: string
    actions?: Action[]
    image: string
    imageThumbnail?: string
    isVeg: boolean
    difference?: number
    overlayImage?: string,
    nutritiontags?: NutritionTag[],
    mealSlot?: {
        id: MealSlot
        name: string
    },
    salesTag?: string,
    shipmentWeight?: number,
    isAllDayItem?: boolean,
    qty?: number,
    variants?: string[],
    parentProductId?: string,
    unit?: MeasurementUnit,
    displayUnitQty?: string,
    variantTitle?: string,
    listingBrand?: ListingBrandIdType
}

export interface FMMealItem {
    nutritionProfileInfo?: FMMealNutritionProfile
    description?: string
    customisableText?: string
}

export interface DateWiseSlots {
    date: string
    timeZones: TimeSlotCategory[] | DigitalTimeSlotCategory[]
    dateType: DateType
    icon?: string,
    imageUrl?: string
    noSlotsAvailable?: boolean
    noSlotText?: string
    nextAvailableSlotInfo?: NextAvailableSlotInfo
    otherAvailableAgentSlotPageAction?: Action
}

export interface NextAvailableSlotInfo {
    title: string
    subtitle?: string,
    icon?: string,
    nextDate?: string
}

export interface MultiConsultationItem {
    title: string,
    subtitle: string,
    state: "PAST" | "PRESENT" | "FUTURE"
}

export interface PreferedDoctor {
    color: string
    name: string
    id: string,
    type: string
}

export interface TimeSlot {
    availableType: TimeSlotState
    startTime?: number
    endTime?: number
    timeSlot?: string
    classId?: number
    classProductCode?: string
    text: string
    subText?: string
    preferedDoctorIdList?: number[]
    eligibleDoctorIds?: number[]
    doctorIdList?: number[],
    meta?: any,
    listingCode?: string
    showSelected?: boolean,
    bookedForSameAddress?: boolean,
    bookedByPatient?: boolean,
    slotClubbingBulletColor?: string
    detailedTimeText?: string
    action?: Action
}

export interface TimeSlotCategory {
    title: string,
    timeSlots: TimeSlot[]
}

export interface DigitalTimeSlotCategory {
    title: string,
    videoCard: VideoCardItem
}

export type TimeSlotState = "AVAILABLE" | "UNAVAILABLE"

export type DateType = "WEEKEND" | "WEEKDAY"

export interface ProfileListWidget extends PageWidget {
    actioncards: ActionCard[]
}

export interface CenterAreaActionCard extends ActionCard {
    centers: CenterView[]
}

export interface CenterAreaWidget extends PageWidget {
    actioncards: CenterAreaActionCard[]
}

export interface VideoCarouselWidget extends PageWidget {
    items: Video[]
}

export interface ExperienceWidget extends PageWidget {
    actioncards: Banner[]
}

export interface ClpImageWidget extends PageWidget {
    title: string
    action: Action
}


export interface HowItWorksWidget extends PageWidget {
    items: HowItWorksItem[]
}

export interface MealListItemWidget extends PageWidget, MealItem {
}

export interface ChangeMealItemWidget extends WidgetView, MealItem {
}

export interface MealGridWidget extends PageWidget {
    items: MealItem[]
}

export interface TitleWidget extends PageWidget {
    title?: string
    titleFontWeight?: string
    subTitle?: string
    action?: Action
}

export abstract class BasePageWidget {
    public hasTopPadding?: boolean = true
    public hasBottomPadding?: boolean = false
    public hasDividerBelow?: boolean = true
    public analyticsData?: any

    constructor() {

    }
}

export interface AvailableProductTags {
    productId: string,
    title: string,
    selected: boolean
    icon?: string
    iconType?: string
}

export class SfConsultAgentWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType
    public agentName: string
    public picUrl: string
    public designation: string
}

export class DatesAvailableWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DATES_AVAILABLE_WIDGET"
    public action: string
    public actionObj: Action
    public consultAgentWidgetPresent?: boolean
    public actionButtonPadding?: number
    public footerPresent: boolean
    public productId?: string
    public doctorType?: string
    public consultationProductType?: string
    public datesAvailable: DateWiseSlots[]
    public preferredDoctorMap?: { [key: string]: PreferedDoctor }
    public preferredDoctorChangeMessage?: string
    public isMultiConsultation?: boolean
    public isDigitalCalendar?: boolean
    public availableProductTags?: AvailableProductTags[]
    public showPreferredDoctorBullets: boolean = true
    public backgroundColor?: string
    public tooltipAnnouncement?: TooltipAnnouncement
    public bannerWidget?: BannerCarouselWidget
    public therapistChangeConsentMessage?: string
    public showBanner: boolean

    constructor(dates: DateWiseSlots[],
        action: string,
        actionObj: Action,
        preferredDoctorMap?: { [key: string]: PreferedDoctor },
        consultationProductType?: string,
        footerPresent?: boolean,
        preferredDoctorChangeMessage?: string,
        isMultiConsultation?: boolean,
        isDigitalCalendar?: boolean,
        availableProductTags?: AvailableProductTags[],
        therapistChangeConsentMessage?: string,
        showBanner?: any
    ) {
        super()
        this.action = action
        this.consultationProductType = consultationProductType
        this.datesAvailable = dates
        this.footerPresent = footerPresent
        this.preferredDoctorMap = preferredDoctorMap
        this.actionObj = actionObj
        this.preferredDoctorChangeMessage = preferredDoctorChangeMessage
        this.isMultiConsultation = isMultiConsultation
        this.isDigitalCalendar = isDigitalCalendar
        this.availableProductTags = availableProductTags
        this.therapistChangeConsentMessage = therapistChangeConsentMessage
        this.showBanner = showBanner
    }
}

export class MultiConsultationDetailWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "MULTI_CONSULTATION_DETAIL_WIDGET"
    public items: MultiConsultationItem[]

    constructor(items: MultiConsultationItem[]) {
        super()
        this.items = items
    }
}


export class PreferedDoctorListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "PREFERED_DOCTOR_LIST_WIDGET"
    public preferredDoctorList: PreferedDoctor[]
    public action?: Action

    constructor(preferredDoctorList: PreferedDoctor[], action?: Action) {
        super()
        this.preferredDoctorList = preferredDoctorList
        this.action = action
    }
}

export class CalloutPageWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CALLOUT_PAGE_WIDGET"
    public icon: string
    public title: string
    public subtitle: string
    public actionString: string
    public action: string
    public subTitleStyle?: any

    constructor(icon: string, subtitle: string, title?: string, actionString?: string, action?: string) {
        super()
        this.icon = icon
        this.title = title
        this.subtitle = subtitle
        this.actionString = actionString
        this.action = action
    }
}

export class CareFooterInfoWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CARE_FOOTER_INFO_WIDGET"
    public title: string
    public subTitle: string
    public icon?: string
    public bullet?: boolean
    public bulletBackgroundColor?: string
    public containerStyling?: any
    public titleStyling?: any
    public subTitleStyling?: any
    public imageStyling?: any

    constructor(title: string, subtitle: string, icon?: string, bullet?: boolean, bulletBackgroundColor?: string) {
        super()
        this.icon = icon,
            this.title = title
        this.subTitle = subtitle
        this.bullet = bullet
        this.bulletBackgroundColor = bulletBackgroundColor
        this.containerStyling = {backgroundColor: "#eceff9"}
    }
}


export interface EventItem {
    eventName: string,
    eventDate: string,
    eventLocation: string,
    eventPrice: ProductPrice,
    stock: number,
    actions: Action[],
    image: string
}

export interface EventGridWidget extends PageWidget {
    items: EventItem[]
}

export class ProductListWidget extends BasePageWidget {
    public widgetType: WidgetType = "PRODUCT_LIST_WIDGET"

    constructor(public type: ListSubType, public header: Header,
                public items: ActionCard[], footer?: ActionCard) {
        super()
    }
}

export class ProductRowListWidget extends BasePageWidget {
    public widgetType: WidgetType = "PRODUCT_ROW_LIST_WIDGET"

    constructor(public type: ListSubType, public header: Header,
                public items: ActionCard[], footer?: ActionCard) {
        super()
    }
}

export class CareConsultationWidget extends BasePageWidget {
    public widgetType: WidgetType = "CARE_CONSULTATION_WIDGET"

    // TODO remove price after app version support
    constructor(public title: string, public subtitle: string,
                public items: ConsultationActionCard[], public price?: ProductPrice) {
        super()
    }
}

export class ListingActionableCardWidget extends BasePageWidget {
    public widgetType: WidgetType = "LISTING_ACTIONABLE_CARD_WIDGET"
    public tag?: string
    public title: string
    public subTitle: string
    public imageUrl?: string
    public icon?: string
    public footer?: {
        text: string,
        icon: string
    }[]
    public actions?: Action[]
    public cardAction: Action
    public showDivider?: boolean
}

export interface Status {
    text: string
    colour: string
}

export class SupportActionableCardWidget extends BasePageWidget {
    public widgetType: WidgetType = "SUPPORT_ACTIONABLE_CARD_WIDGET"
    public tag?: string
    public title: string
    public titleStyling?: {
        color: string
    }
    public subTitle: string
    public imageUrl?: string
    public icon?: string
    public vegIcon?: string
    public footer?: {
        text: string,
        icon?: string,
        backgroundColor?: string,
        status?: Status // added only for care items as one test can have multiple sub tests (atHome or inCenter)
    }[]
    public cardAction: Action
    public showDivider?: boolean
    public imageShape?: string
    public status?: Status
    public time?: string
    public timestamp?: number
    public timezone?: string
    public isNotCard?: boolean
    public ticketId?: number
}

export class ChatListingActionableCardWidget extends ListingActionableCardWidget {
    public widgetType: WidgetType = "CHAT_LISTING_ACTIONABLE_CARD_WIDGET"
    public unReadMessageCount: number
    public lastReadTimeText: string
    public specialityInfo?: string[]
}

export class TicketDetailsWidget extends BasePageWidget {
    public widgetType: WidgetType = "TICKET_DETAILS_WIDGET"
    public title: string
    public status: Status
    public ticketId: string
    public createdOn: string
    public footer?: {
        text: string,
        value: {
            text: string,
            color?: string
        }
    }[]
    public orderDetailWidget?: SupportActionableCardWidget
}

export type TicketDescriptionWidgetType = "DESCRIPTION" | "ATTACHMENT"

export class TicketDescriptionWidget extends BasePageWidget {
    public widgetType: WidgetType = "TICKET_DESCRIPTION_WIDGET"
    public type: TicketDescriptionWidgetType
    public title: string
    public description?: string
    public urls?: string[]
    public dividerType?: string

}

export type TicketChatWidgetType = "USER_CHAT" | "USER_ATTACHMENT" | "CF_CHAT" | "CF_ATTACHMENT"

export class TicketChatWidget extends BasePageWidget {
    public widgetType: WidgetType = "TICKET_CHAT_WIDGET"
    public type: TicketChatWidgetType
    public title?: string
    public description?: string
    public urls?: string[]
    public time?: string
}

export class TicketResolutionWidget extends BasePageWidget {
    public widgetType: WidgetType = "TICKET_RESOLUTION_WIDGET"
    public title?: string
    public actions?: Action[]
}

export class TicketCSATFooterWidget extends BasePageWidget {
    public widgetType: WidgetType = "TICKET_CSAT_FOOTER_WIDGET"
    public questionId: string
    public questionTitle: string
    public tags: Action[]
}

export class HorizontalImageListWidget extends BasePageWidget {
    public widgetType: WidgetType = "HORIZONTAL_IMAGE_LIST_WIDGET"
    public header: Header
    public listData: HorizontalList[]
}

export class HorizontalList {
    public header: Header
    public action: Action
    public items: HorizontalListItem[]
}

export class HorizontalListItem {
    public title: string
    public image: string
    public itemAction: Action
}

export class UserPlanDescriptionWidget extends BasePageWidget {
    public widgetType: WidgetType = "USER_PLAN_DESCRIPTION_WIDGET"
    public header?: {
        title: string
        subtitle?: string
    }
    public optionId: string
    public title: string
    public details: string[]
    public action: Action
    public gradientColor: string[]
    public actionColor: string
}


export class MagazineRowWidget extends BasePageWidget {
    public widgetType: WidgetType = "MAGAZINE_ROW_WIDGET"

    constructor(public header: Header,
                public items: ActionCard[], footer?: ActionCard) {
        super()
    }
}


export class OfferWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "OFFER_WIDGET"

    constructor(public banner: Banner) {
        super()
    }
}

export class OfferInfoWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "OFFER_INFO_WIDGET"

    constructor(public title: string, public subTitle: string, public icon: string, public action: Action, public productType: ProductType) {
        super()
    }
}

export class ProductOfferWidget extends BasePageWidget implements PageWidget {
    public widgetType: (WidgetType | PageWidgetType) = "PRODUCT_OFFER_WIDGET"

    constructor(public icon: Icon, public title: string, public subtitle: string, public text: string, public action: string, public actionText: string, public gradient: string[]) {
        super()
    }
}

export class BannerWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "BANNER_WIDGET"

    constructor(public banners: Banner[], public bannerRatio: string) {
        super()
    }
}

export class DoctorListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DOCTOR_LIST_WIDGET"

    constructor(public title: string, public doctors: Doctor[], public action: Action) {
        super()
    }
}

export class CategoryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CATEGORY_WIDGET"

    constructor(public categories: Category[]) {
        super()
    }
}

export class SubCategoryWidgetType1 extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "SUB_CATEGORY_TYPE_1_WIDGET"

    constructor(
        public tryMealAction: ActionCard,
        public packs: Pack[],
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
        this.seemore = seemore
        this.widgetTitle = widgetTitle
    }
}

export class SubCategoryWidgetType2 extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "SUB_CATEGORY_TYPE_2_WIDGET"

    constructor(
        public cultPacks: ActionCard[],
        public classBookAction: ActionCard & {
            offer?: {
                title: string
                subTitle: string
            }
        },
        public categories: ActionCard[],
        public cultDIYCategoriesTitle: PageWidgetTitle,
        public cultDIYCategoriesSeeMore: PageWidgetMore,
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
    }
}

export class SubCategoryWidgetType3 extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "SUB_CATEGORY_TYPE_3_WIDGET"

    constructor(
        public categories: Category[],
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
        this.seemore = seemore
        this.widgetTitle = widgetTitle
    }
}

export class InterestWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "INTEREST_WIDGET"

    constructor(
        public numberOfPeopleNeeded: number,
        public numberOfPeopleInterested: number,
        public url: string,
        public title: string,
        public message: string,
        public subject: string
    ) {
        super()
    }
}

export class CategoryTileWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CATEGORY_TILE_WIDGET"

    constructor(
        public actioncards: (ActionCard & { expanded?: boolean })[],
        public widgetTitle: PageWidgetTitle) {
        super()
    }
}

export class ScrollCardWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CARD_SCROLL_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
    }
}

export class CultCenterGridWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CULT_CENTER_GRID_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
    }
}

export class BannerDescriptionCardWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "BANNER_DESC_CARD_WIDGET"

    constructor(
        public actioncard: ActionCard,
        public widgetTitle: PageWidgetTitle,
        public seemore?: PageWidgetMore) {
        super()
    }
}

export class CategoryActionCardWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CATEGORY_ACTION_CARD_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public seemore: PageWidgetMore) {
        super()
    }
}

export class ActionCardListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "ACTION_CARD_LIST_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public seemore?: PageWidgetMore) {
        super()
    }
}

export class MagazineListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "MAGAZINE_LIST_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public isOfferAvailable?: boolean,
        public seemore?: PageWidgetMore) {
        super()
    }
}

export class DiagnosticCardAddOnListWidget implements IDiagnosticsCartAddonListWidget {
    public widgetType: "DIAGNOSTICS_CART_ADDON_LIST_WIDGET"

    constructor(
        public infoCards: InfoCard[],
        public title: string,
        public action: IDiagnosticsCartAddonListWidgetAction,
        public orientation: Orientation,
        public displayAddOn: boolean = true,
        public subTitle?: string,
        public containerStyle?: ViewStyle
    ) {
        this.infoCards = infoCards
        this.title = title
        this.action = action
        this.subTitle = subTitle
        this.widgetType = "DIAGNOSTICS_CART_ADDON_LIST_WIDGET"
        this.displayAddOn = displayAddOn
        this.subTitle = subTitle
        this.containerStyle = containerStyle
    }
}

export class DiagnosticsTestListWidget extends IDiagnosticsTestListWidget {
    public widgetType: AppWidgetType = "DIAGNOSTICS_TEST_LIST_WIDGET"

    constructor(
        public gridSize: number,
        public infocards: InfoCard[],
        public widgetTitle: PageWidgetTitle,
        public action: Action,
        public isOfferAvailable?: boolean,
        public seemore?: PageWidgetMore,
        public orientation?: Orientation) {
        super(gridSize, infocards, widgetTitle, action, isOfferAvailable, seemore, orientation)
    }
}

export type STEP_STATE = "NOT_STARTED" | "STARTED" | "COMPLETED"
export type StepStateViewType =
    "ACTIONABLE"
    | "TEXT_ACTION"
    | "ACTION_ICON"
    | "MULTI_ACTIONS"
    | "CONSULTATION_CARDS"
    | "INSTRUCTION_CARD"
    | "CONSULTATION_CARDS_V2"
    | "ACTIONABLE_V2"

export interface StateView {
    title?: string,
    action?: Action,
    subTitle?: string,
    cardAction?: Action,
    text?: string,
    textStyling?: boolean,
    image?: string,
    imageUrl?: string,
    footer?: {
        text: string,
        icon: string
    }[],
    bullets?: string[]
    actions?: Action[],
    progressPercent?: number
    meta?: {
        instructions?: InstructionItem[],
        style?: any,
        expandObj?: {
            isExpandable: boolean,
            isExpanded: boolean,
        },
        actions?: {
            icon?: string,
            action?: Action,
            bgColor?: string[],
            color?: string,
            isHighLight?: boolean,
            title?: string,
            actionType?: string
        }[],
        title?: string,
        subtitle?: string
    }
}

export interface StepStateCard {
    state: STEP_STATE,
    title?: string,
    viewType: StepStateViewType,
    isStateCollapsible?: boolean
    views: StateView[]
}

export interface ProductStateItem {
    header?: string,
    title: string,
    subtitle?: string,
    gradientColors?: string[],
    dividerGradient?: string[]
    icon?: string,
    action?: Action
    states: StepStateCard[],
    isExpanded?: boolean,
    withoutLine?: boolean,
    useCustomStyling?: boolean

}

export class CareProductStateWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CARE_PRODUCT_STATE_WIDGET"

    constructor(
        public items: ProductStateItem[],
        public expandedItemIndices: number[],
        public isCard?: boolean,
        public gradientContent?: any,
        public disableClick?: boolean,
        public widgetTitle?: PageWidgetTitle
    ) {
        super()
    }
}

export class CareMeEmptyListingWidget extends BasePageWidget implements PageWidget {
    public widgetType: PageWidgetType = "CARE_ME_EMPTY_LISTING_WIDGET"

    constructor(public icon: string, public title: string, public subTitle?: string) {
        super()
    }
}

export class SupportEmptyListingWidget extends BasePageWidget implements PageWidget {
    public widgetType: PageWidgetType = "SUPPORT_EMPTY_LISTING_WIDGET"

    constructor(public icon: string, public title: string, public subTitle?: string) {
        super()
    }
}

export interface TestReportDetail {
    lineStartValue?: number,
    lineEndValue?: number,
    testCount?: string
    testState?: string,
    stateColor: string,
    lineWidthRatio: number
}

export interface DiagnosticTestReportItem {
    header?: {
        title?: string,
        subtitle?: string,
        image?: string
        testState?: string
        testStateColor?: string
        action?: Action
    },
    markerIndex?: number,
    testResultValue?: number
    testInfos?: TestReportDetail[]
}

export class DiagnosticsTestReportSummaryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DIAGNOSTICS_TEST_REPORT_SUMMARY_WIDGET"

    constructor(
        public reportItems: DiagnosticTestReportItem[],
        public showLine: boolean = true,
        public widgetTitle?: PageWidgetTitle,
        public noteText?: string) {
        super()
    }
}

export class CareTeamWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CARE_TEAM_WIDGET"
    productType?: ProductType
    public hasDividerBelow?: boolean

    constructor(public careTeamItems: CareTeamItem[], public title?: string, public contentContainerStyle?: any, public isNotCard?: boolean, hasDividerBelow?: boolean) {
        super()
    }
}

export interface SupportCardItem {
    title: string
    subTitle?: string
    imageUrl?: string
    footer?: {
        text: string,
        icon?: string,
    }[]
    cardAction: Action
    status: Status
    ticketId?: number
}

export interface ActionableCardItem {
    tag?: string
    title: string
    subTitle: string
    imageUrl?: string
    icon?: string
    footer?: {
        text: string,
        icon: string,
        status?: Status // added only for care items as one test can have multiple sub tests (atHome or inCenter)
    }[]
    actions?: Action[]
    cardAction: Action
    imageShape?: string
}

export class HorizontalActionableCardListingWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET"
    public title: string
    public titleStyling?: {
        color: string,
        fontSize: number,
        paddingLeft: number,
        paddingBottom: number,
    }
    public type: HorizontalActionableCardListType
    public cardItems: ActionableCardItem[] | DiagnosticTestReportItem[] | SupportCardItem[]
    public footer?: {
        title: string,
        action: Action
    }
    public header?: {
        action: Action
    }
    public orientation?: Orientation
    public backgroundColor?: string
    public dividerType?: string
    public useNewWidgetInWeb?: boolean
}

export class HorizontalActionProductListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "HORIZONTAL_ACTION_CARD_PRODUCT_LIST_WIDGET"
    public header: Header
    public cardItems: ActionableCardItem[] | OrderDetail[]
    public items:
        | InfoCard[]
        | ActionCard[]
        | GradientCard[]
        | GradientCarouselCard[]
        | DailyMealWidget[]
        | InfoItem[]
    public analyticsData?: any
    public hideSepratorLines?: boolean
    public backgroundColor?: string
    public dividerType?: string
}

export class ProductGuranteeWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "PRODUCT_GURANTEE_WIDGET"
    public productType: ProductGuranteeWidgetProductType
    public header: Header
    public containerStyle?: any
    public layoutType: LAYOUT_TYPE
    public data: GuranteeInfo[]
}

export class ImageInfoWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "IMAGE_INFO_WIDGET"
    public productType: ImageInfoProductType
    public header: Header
    public cardType: ImageType
    public cards: ImageInfoCard[]
}

export class ImageInfoCard {
    public title: string
    public subTitle?: string
    public subTitleIcon?: string
    public imageUrl: string
    public logoUrl?: string
    public action: Action
    public rightInfo?: {
        title: string
        action?: Action
    }
}

export type PACK_SELECTION_PRODUCT_TYPE = "GYMFIT"

export interface PackItem {
    title: string
    subTitle?: string
    price: ProductPrice
    packId: string
    selected: boolean
    meta?: string
    action?: Action
}

export class PackSelectionWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "PACK_SELECTION_WIDGET"
    public header: Header
    public dividerType: DividerType = "SMALL"
    public showDivider: boolean = true
    public packSelectionProductType: PACK_SELECTION_PRODUCT_TYPE
    public items: PackItem[]
}

export class GymfitCodeWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "GYMFIT_CODE_WIDGET"
    public title: string
    public titleIcon: string
    public subTitle: string
    public description: string
    public iconType: string
    public code?: string
    public footer?: {
        text: string
    }
}

export interface CareTeamItem {
    imageUrl: string
    title: string
    subTitle: string
    messageAction?: Action
    actions: Action[]
}

export class DiagnosticsTestDetailedSummaryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DIAGNOSTICS_TEST_DETAILED_SUMMARY_WIDGET"

    constructor(
        public reportItem: DiagnosticTestReportItem,
        public showLine: boolean = true,
        public summaryTitle?: string,
        public summaryText?: string) {
        super()
    }
}

export class DiagnosticsEmailTestReportSummaryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DIAGNOSTICS_EMAIL_TEST_REPORT_SUMMARY_WIDGET"

    constructor(
        public title: string,
        public subTitle: string,
        public description: string,
        public action: Action
    ) {
        super()
    }
}

export class DiagnosticsEmailTestReportDetailedSummaryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DIAGNOSTICS_EMAIL_TEST_REPORT_DETAILED_WIDGET"

    constructor(
        public title: string,
        public action: Action,
        public items: DiagnosticEmailTestReportItem[],
        public showLine: boolean = true
    ) {
        super()
    }
}

export interface DiagnosticEmailTestReportItem {
    title: string,
    description: string,
    highlightedText?: string,
    action?: Action
}

export interface DiagnosticTestReportCell {
    measuredValueTitle?: string,
    normalValueTitle?: string,
    measuredValue?: string,
    normalValue?: string,
}

export class DiagnosticsTestReportDetailWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "DIAGNOSTICS_TEST_REPORT_DETAIL_WIDGET"

    constructor(
        public reportItem: DiagnosticTestReportItem,
        public cellType: DiagnosticTestDetailCellType,
        public cellData: DiagnosticTestReportCell) {
        super()
    }
}

export interface CultBuyPackWidget extends PageWidget, ActionCard {
}

export interface TryMealWidget extends PageWidget, ActionCard {
}

export interface DiySeries {
    title: string
    actionCards: ActionCard[]
}

export interface DiyWidget extends PageWidget {
    items: DiySeries[]
}

export interface CultBookClassWidget extends PageWidget, ActionCard {
    offer?: {
        title: string
        subTitle: string
    }
}

export class MagazineGridWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "MAGAZINE_GRID_WIDGET"

    constructor(
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public seemore?: PageWidgetMore) {
        super()
    }
}

export class GridWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "GRID_WIDGET"

    constructor(
        public cellType: CellType,
        public actioncards: ActionCard[],
        public widgetTitle: PageWidgetTitle,
        public isOfferAvailable?: boolean,
        public seemore?: PageWidgetMore,
        public pageWidgetFooter?: PageWidgetFooter) {
        super()
    }
}

export class ImageWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "IMAGE_WIDGET"

    constructor(
        public action: string,
        public image: string) {
        super()
    }
}

export class ImageGridWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "IMAGE_GRID_WIDGET"

    // image cards count should be even
    constructor(
        public imageCards: ImageCard[]) {
        super()
    }
}

export class HeaderWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "HEADER_WIDGET"
    style?: any
    headerStyle?: any
    titleStyle?: any
    subTitleStyle?: any
    headerTopInfoStyle?: any
    headerTopInfoViewStyle?: any

    constructor(
        public widgetTitle: PageWidgetTitle, public showDivider?: boolean) {
        super()
    }

}

export class FoodSinglesRecommendationWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "FOOD_SINGLES_RECOMMENDATION_WIDGET"

    constructor(
        public widgetTitle: PageWidgetTitle,
        public mealCards: MealItem[],
        public footer?: PageWidgetFooter) {
        super()
    }
}

export class PackRecommendationWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "PACK_RECOMMENDATION_WIDGET"

    constructor(
        public widgetTitle: PageWidgetTitle,
        public actionCards: ActionCard[],
        public footer?: PageWidgetFooter) {
        super()
    }
}

export interface ImageBannerWithAction {
    imageUrl: string
    action?: Action
}

export interface HeaderWithBanner {
    title?: string
    subTitle?: string
    seemore?: Action
    banner?: ImageBannerWithAction
}

export interface GearCollection {
    name: string
    title: string
    subTitle?: string
    bannerImageUrl?: string
}

export class ProductBrowseWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "PRODUCT_BROWSE_WIDGET"

    constructor(
        public data: any,
        public header?: HeaderWithBanner) {
        super()
    }
}

export class CategoryBannerWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "CATEGORY_BANNER_WIDGET"

    constructor(
        public data: any,
        public header?: Header) {
        super()
    }
}

export class TimerWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "TIMER_WIDGET"

    constructor(
        public timerEndTime: number,
        public action?: Action,
        public title?: string
    ) {
        super()
    }
}

export class BannerCarouselWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "BANNER_CAROUSEL_WIDGET"
    public isFullWidthDesktop?: boolean

    constructor(
        public bannerRatio: string,
        public data: Banner[],
        public layoutProps?: any,
        public maxNumBanners?: number,
        public edgeToEdge?: boolean,
        public showDivider?: boolean,
        public isRetelecast?: boolean,
        public header?: Header,
        public action?: Action,
    ) {
        super()
    }
}

export class MerchantryWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "MERCHANTRY_WIDGET"

    constructor(
        public layoutProps?: any,
        public data?: any,
        public header?: Header,
        public action?: Action,
    ) {
        super()
    }
}

export class JourneyWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "JOURNEY_WIDGET"

    constructor(
        public gridSize: number,
        public infocards: InfoCard[],
        public widgetTitle: PageWidgetTitle,
        public action: Action) {
        super()
    }
}

export class SpecialistSelectionListWidget extends BasePageWidget implements PageWidget {
    public widgetType: WidgetType = "SPECIALIST_SELECTION_LIST_WIDGET"

    constructor(
        public gridSize: number,
        public dataList: SpecialityItem[],
        public widgetTitle: PageWidgetTitle) {
        super()
    }
}

export interface SpecialityItem {
    title: string,
    imageUrl?: string
    subTitle?: string,
    description?: string,
    priceItems?: any,
    action: Action
}

export class TypographyWidget implements WidgetView {
    widgetType: "TYPOGRAPHY_WIDGET"
    tagColor: string
    marginTop?: number
    marginBottom?: number
    backgroundColor?: string
    data: {
        text: string,
        fontColor: string,
        fontSize: number
        fontFamily?: string
        fontWeight?: string

    }[]
    dividerType?: string
}

export class QuickLinkWidget implements WidgetView {
    widgetType: "QUICK_LINKS_WIDGET"
    data: [{
        action?: Action,
        title: string
    }]
}

export class ReportStatusWidget extends BasePageWidget {
    widgetType: WidgetType = "REPORTS_STATUS_WIDGET"
    inProgressTests?: {
        action: Action,
        cta: string,
        count: number,
        title: string,
        subtitle: string
    }
    pendingTests?: {
        action: Action,
        cta: string,
        count: number,
        title: string,
        subtitle: string
    }
    total: number
    count: number
    title: string
    titleStyle: string

    constructor(total: number, count: number, title: string, titleStyle: string, inProgressTests?: any, pendingTests?: any) {
        super()
        this.total = total
        this.count = count
        this.title = title
        this.titleStyle = titleStyle
        this.inProgressTests = inProgressTests
        this.pendingTests = pendingTests
    }
}

export interface WorkoutMediaData {
    media: Media
    action?: Action
}

export interface CollapsibleProperties {
    isCollapsible: boolean
    isCollapsed: boolean
}

export interface CardItemSubTitle {
    text: string
    textColor?: string
}

export interface CreditPillData {
    credit?: string
    height?: number
    backgroundColor?: string
    typescaleValue?: string
}

export class FormatSummaryWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "FORMAT_SUMMARY_WIDGET"
    intensityLevel: string
    subTitleList: CardItemSubTitle[]
    mediaDetails: WorkoutMediaData[]
    centerName: string
    navigateAction: Action
    timingDetails: string
    isV2Widget: boolean
    creditPillData?: CreditPillData

    constructor(
        public title: string,
        public description: string,
        centerName: string,
        navigateAction: Action,
        timingDetails: string,
        isV2Widget: boolean,
        intensityLevel: string,
        subTitleList: CardItemSubTitle[],
        mediaDetails: WorkoutMediaData[],
        public layoutProps?: any,
        creditPillData?: CreditPillData,
    ) {
        super()
        this.intensityLevel = intensityLevel
        this.subTitleList = subTitleList
        this.mediaDetails = mediaDetails
        this.centerName = centerName
        this.navigateAction = navigateAction
        this.timingDetails = timingDetails
        this.isV2Widget = isV2Widget
        this.creditPillData = creditPillData
    }
}

export class MapWidgetV2 extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "CENTER_MAP_WIDGET_V2"
    address: string
    action: Action

    constructor(
        address: string,
        action: Action,
        public title: string,
        public layoutProps?: any
    ) {
        super()
        this.address = address
        this.action = action
    }
}

export class CFMediaDataWidget  {
    mediaUrl: string
    type: string
    height: number
    width?: number
    topPadding?: number
    bottomPadding?: number
    leftPadding?: number
    rightPadding?: number

    constructor(
        mediaUrl: string,
        type: string,
        height: number,
        width?: number,
        topPadding?: number,
        bottomPadding?: number,
        leftPadding?: number,
        rightPadding?: number
    ) {
        this.mediaUrl = mediaUrl
        this.type = type
        this.height = height
        this.width = width
        this.topPadding = topPadding
        this.bottomPadding = bottomPadding,
        this.leftPadding = leftPadding,
        this.rightPadding = rightPadding
    }
}

export class CFMediaWidget extends  BasePageWidget implements  PageWidget {
    widgetType: WidgetType = "CF_MEDIA_WIDGET"
    mediaData: CFMediaDataWidget = null
    constructor(mediaWidget: CFMediaDataWidget) {
        super()
        this.mediaData = mediaWidget
    }
}

export class CFBottomSheetActionWidget extends  BasePageWidget implements  PageWidget {
    widgetType: WidgetType = "ACTION_LIST_WIDGET"
    actionList: [Action] = null
    layputProps: Object = null
    constructor(action: [Action], object: Object) {
        super()
        this.actionList = action
    }
}

export class CenterTimingWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "CENTER_TIMING_WIDGET"
    title: string

    constructor(
        title: string,
        public layoutProps?: any
    ) {
        super()
        this.title = title
    }
}

export interface InstructionMedia {
    url: string,
    type: MediaType,
    videoUrl: string,
    action: Action,
    fullScreenIcon: string
}

export interface InstructionsWithMediaV2 {
    text: string,
    media: InstructionMedia
}

export class ProductGridWidgetV2 extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "PRODUCT_GRID_WIDGET_V2"
    footer: string
    instructions: InstructionsWithMediaV2[]
    showBackground?: boolean
    customIcon?: {url: string, height: string, width: string}
    disableCollapse?: boolean
    showTopDivider?: boolean
    constructor(
        public header: Header,
        public items: InfoCard[],
        footer: string,
        instructions: InstructionsWithMediaV2[],
        public collapsibleProperties?: CollapsibleProperties,
        public layoutProps?: any
    ) {
        super()
        this.instructions = instructions
        this.footer = footer
    }
}

export interface Facility {
    name: string
    imageUrl: string
}

export class FacilitiesWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "FACILITIES_WIDGET"
    facilitiesList: Facility[]

    constructor(
        public title: string,
        public subTitle: string,
        facilitesList: Facility[],
        public collapsibleProperties?: CollapsibleProperties,
        public layoutProps?: any
    ) {
        super()
        this.facilitiesList = facilitesList
    }
}

export interface AssetView {
    url: String
    type: String
    repeat: boolean
}

export class WorkoutDetailWidget extends BasePageWidget implements  PageWidget {
    widgetType: WidgetType = "FORMAT_INFO_WIDGET"
    data: AssetView[]

    constructor(
        public title: string,
        public subTitle: string,
        public isV2Widget: boolean,
        data: AssetView[],
        public layoutProps?: any,
        public isPaddingDivider?: boolean,
        public initiallyExpanded?: boolean
    ) {
        super()
        this.title = title
        this.subTitle = subTitle
        this.isV2Widget = isV2Widget
        this.data = data
        this.layoutProps = layoutProps
        this.isPaddingDivider = isPaddingDivider
        this.initiallyExpanded = initiallyExpanded
    }

}

export interface WorkoutMovementInfo {
    title: string
    subtitle?: string
    note?: string
    goal?: string
    strategy?: string
    subPartType?: string
    showGoalView?: boolean
    workoutComponents?: WorkoutComponent[]
}

export interface WorkoutComponent {
    title: string
    description?: string
    secondaryTitle?: string
    secondaryTitleStyle?: SecondaryTextStyle
    action?: Action
    image?: string
}

export interface SecondaryTextStyle {
    fontSize: number
    fontWeight: string
    color: string
}

export class WODInfoWidgetV2 extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "WOD_INFO_WIDGET_V2"
    focus: string
    mainWorkoutInfo: WorkoutMovementInfo[]
    preWorkoutInfo: WorkoutMovementInfo
    postWorkoutInfo: WorkoutMovementInfo

    constructor(
        public title: string,
        focus: string,
        mainWorkoutInfo: WorkoutMovementInfo[],
        preWorkoutInfo: WorkoutMovementInfo,
        postWorkoutInfo: WorkoutMovementInfo,
        public collapsibleProperties?: CollapsibleProperties,
        public layoutProps?: any
    ) {
        super()
        this.focus = focus
        this.mainWorkoutInfo = mainWorkoutInfo
        this.preWorkoutInfo = preWorkoutInfo
        this.postWorkoutInfo = postWorkoutInfo
    }
}

export interface WaitlistInfo {
    waitlistCount: number
    waitlistCnfProbability?: string
    title: string
    infoAction: Action
    waitlistPrediction?: string
    waitlistColor?: string
}

export interface WaitlistConfirmationTimePicker {
    title: string
    cultClassId: number
    description: string
    collapsedDescription: string
    classTimeInEpochs: number
    timeSlots: WorkoutWaitlistTimingSlot[]
    collapsibleProperties?: CollapsibleProperties
}

export interface WorkoutWaitlistTimingSlot {
    title: string
    time: number
    isSelected: boolean
    isRecommended: boolean
}

export class WaitlistWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "WAITLIST_WIDGET"
    waitlistInfo: WaitlistInfo
    timePicker: WaitlistConfirmationTimePicker
    headerText: string
    waitlistCount: number
    wlBookingNumber: string
    fromFlutterBookingpage?: boolean

    constructor(
        headerText: string,
        waitlistCount: number,
        waitlistInfo: WaitlistInfo,
        timePicker: WaitlistConfirmationTimePicker,
        wlBookingNumber: string,
        public layoutProps?: any,
        fromFlutterBookingpage?: boolean
    ) {
        super()
        this.headerText = headerText
        this.waitlistCount = waitlistCount
        this.waitlistInfo = waitlistInfo
        this.timePicker = timePicker
        this.wlBookingNumber = wlBookingNumber
        this.fromFlutterBookingpage = fromFlutterBookingpage
    }
}

export class DescriptionWidgetV2 extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "DESCRIPTION_WIDGET"
    title: string
    items: string[]

    constructor(
        title: string,
        items: string[],
        public layoutProps?: any,
        public dividerWidth?: number
    ) {
        super()
        this.title = title
        this.items = items
    }
}

export class HorizontalListSrcollWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "HORIZONTAL_LIST_SCROLL_WIDGET"
    items: string[]

    constructor(
        items: string[],
        public layoutProps?: any
    ) {
        super()
        this.items = items
    }
}

export class ActionWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "ACTION_WIDGET"

    constructor(
        public title: string,
        public style: any,
        public actions: Action[],
        public layoutProps?: any
    ) {
        super()
    }
}

export class OrderConfimationCarouselWidget extends BasePageWidget {
    widgetType: WidgetType = "CIRCULAR_IMAGE_CAROUSEL_WIDGET"
    items: any[]

    constructor(
        items: any[],
        public layoutProps: any
    ) {
        super()
        this.items = items
    }
}

export class BannerCardWidgetView extends BasePageWidget {
    widgetType: WidgetType = "BANNER_CARD_WIDGET"
    layout: {
        spacing: {
            bottom: 100,
        },
    }
    title?: string
    subTitle?: string
    description?: string
    imageUrl?: string
    lottieUrl?: string
    bgImageUrl?: string
    campaignId?: string
    action?: Action

    constructor(title: string, subTitle: string, bgImageUrl: string, imageUrl: string, action: Action) {
        super()
        this.title = title
        this.subTitle = subTitle
        this.imageUrl = imageUrl
        this.bgImageUrl = bgImageUrl
        this.action = action
    }
}

export interface CardContainerCellWidget extends WidgetView {
    title: string
    subTitle?: string
    prefixIcon?: string
    prefixImage?: string
    suffixIcon?: string
    suffixImage?: string
}

export class MiniGoalWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "MINI_GOAL_WIDGET"
    data: {
        title: string
        subtitle: string
        numberOfCircles: number
        numberOfTicks: number
        isCollapsible: boolean
        isCollapsed: boolean
        action: Action,
        infoImgUrl?: string
    }

    constructor(
        title: string,
        subtitle: string,
        numberOfCircles: number,
        numberOfTicks: number,
        isCollapsible: boolean,
        isCollapsed: boolean,
        action: Action,
        infoImgUrl?: string,
        public layoutProps?: any,
    ) {
        super()
        this.data = {
            title,
            subtitle,
            numberOfCircles,
            numberOfTicks,
            isCollapsible,
            isCollapsed,
            action,
            infoImgUrl
        }
    }
}

export class MiniGoalBannerWidget extends BasePageWidget implements PageWidget {
    widgetType: WidgetType = "MINI_GOAL_BANNER_WIDGET"
    data: {
        title: string
        subtitle: string
        description: string
        mediaUrl: string
    }

    constructor(
        title: string,
        subtitle: string,
        description: string,
        mediaUrl: string,
        public layoutProps?: any

    ) {
        super()
        this.data = {
            title,
            subtitle,
            description,
            mediaUrl
        }
    }
}