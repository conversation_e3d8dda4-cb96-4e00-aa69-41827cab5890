import { <PERSON><PERSON><PERSON>, Banner, Category } from "./PageWidgets"
import { inject, injectable } from "inversify"
import { PageWidgetMore, PageWidgetTitle } from "./Page"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class HomePageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 31 * 60)
        this.load("HomePageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "HomePageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.eatCategory = data.eatCategory
            this.cultCategory = data.cultCategory
            this.mindCategory = data.mindCategory
            this.eatCategoryOutsideBlr = data.eatCategoryOutsideBlr
            this.cultCategoryOutsideBlr = data.cultCategoryOutsideBlr
            this.mindCategoryOutsideBlr = data.mindCategoryOutsideBlr
            this.banners = data.banners
            this.categories = data.categories
            this.eatTitle = data.eatTitle
            this.eatfitPackIds = data.eatfitPackIds
            this.eatSeeMore = data.eatSeeMore
            this.cultTitle = data.cultTitle
            this.cultDIYCategoriesTitle = data.cultDIYCategoriesTitle
            this.classBookAction = data.classBookAction
            this.tryMealAction = data.tryMealAction
            this.packBuyAction = data.packBuyAction
            this.cultDIYPackIds = data.cultDIYPackIds
            this.cultSeeMore = data.cultSeeMore
            this.cultDIYCategoriesSeeMore = data.cultDIYCategoriesSeeMore
            this.mindTitle = data.mindTitle
            this.mindDIYPackIds = data.mindDIYPackIds
            this.mindSeeMore = data.mindSeeMore
            this.careCategory = data.careCategory
            this.carefitUserIds = data.carefitUserIds
            this.exploreCategories = data.exploreCategories
            return data
        })
    }

    public eatCategoryOutsideBlr: Category
    public cultCategoryOutsideBlr: Category
    public mindCategoryOutsideBlr: Category
    public eatCategory: Category
    public cultCategory: Category
    public mindCategory: Category
    public careCategory: Category
    public exploreCategories: Category[]
    public banners: Banner[]
    public categories: Category[]
    public eatfitPackIds: string[]
    public eatTitle: PageWidgetTitle
    public eatSeeMore: PageWidgetMore
    public packBuyAction: ActionCard
    public classBookAction: ActionCard
    public tryMealAction: ActionCard
    public cultDIYPackIds: string[]
    public cultDIYCategoriesTitle: PageWidgetTitle
    public cultSeeMore: PageWidgetMore
    public cultTitle: PageWidgetTitle
    public cultDIYCategoriesSeeMore: PageWidgetMore
    public mindDIYPackIds: string[]
    public mindTitle: PageWidgetTitle
    public mindSeeMore: PageWidgetMore
    public carefitUserIds: string[]
}

export default HomePageConfig
