import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { ISegmentService, UserContext } from "@curefit/vm-models"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { FeatureStateCache } from "./vm/services/FeatureStateCache"

const clone = require("clone")

export interface CoachMark {
    actionType: string
    meta: CoachMarkMeta
}

export interface CoachMarkMeta {
    flag: string
    mark: string
    pageId: string
    items: CoachMarkItem[]
}

export interface  CoachMarkItem {
    image: string
    subPageId?: string
    cyclopsSegmentIds?: string[]
}

@injectable()
export class CoachMarkConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) protected rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.FeatureStateCache) protected featureStateCache: FeatureStateCache,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
    ) {
        super(logger, 35 * 60)
        this.load("CoachMarkConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "CoachMarkConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.data = data
            return data
        })
    }

    public data: CoachMark[]


    async getCoachMark(userContext: UserContext, pageId: string, subPageIds?: string[]): Promise<CoachMark> {
        const userId = userContext.userProfile.userId
        const clonedData = clone(this.data) as CoachMark[]
        const coachMarks = clonedData
            .filter(coachMark => !!coachMark.meta)
            .filter(coachMark => coachMark.meta.pageId === pageId)

        let ret: CoachMark = null
        try {
            for (const coachMark of coachMarks) {
                const items = []
                for (const coachMarkItem of coachMark.meta.items) {
                    if (!!coachMarkItem.subPageId) {
                        if (!subPageIds || !subPageIds.includes(coachMarkItem.subPageId)) {
                            continue
                        }
                    }
                    if (!!coachMarkItem.cyclopsSegmentIds) {
                        const segment = await this.segmentService.doesUserBelongToAnySegment(coachMarkItem.cyclopsSegmentIds, userContext)
                        if (!segment) {
                            continue
                        }
                    }
                    items.push(coachMarkItem)
                }
                if ((items?.length ?? 0) === 0) {
                    continue
                }
                coachMark.meta.items = items
                const isShown = await this.featureStateCache.match(userId, coachMark.meta.flag, coachMark.meta.mark)
                if (!isShown) {
                    ret = coachMark
                }
            }
        } catch (err) {
            this.rollbarService.sendError(err)
        }
        if (ret) {
            this.featureStateCache.set(userId, ret.meta.flag, ret.meta.mark)
        }
        return ret
    }
}
