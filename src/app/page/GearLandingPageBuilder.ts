import * as _ from "lodash"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { inject, injectable } from "inversify"
import { ActionUtil } from "@curefit/base-utils"
import { CatalogueProduct, Annotation } from "@curefit/gear-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { Action } from "../common/views/WidgetView"
import { PageWidget } from "./Page"
import {
    BannerCarouselWidget,
    CategoryBannerWidget,
    GearCollection,
    HeaderWithBanner,
    ProductBrowseWidget,
    TimerWidget
} from "./PageWidgets"
import { GearUtil } from "@curefit/gearvault-client"
import { UserContext } from "@curefit/userinfo-common"
import GearLandingPageConfig, { GearCarouselBanner } from "./GearLandingPageConfig"
import { TimeUtil } from "@curefit/util-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import AppUtil from "../util/AppUtil"
import GearCatalogueLandingPageService from "./GearCatalogueLandingPageService"
import { IPageService, Page, IWidgetBuilder } from "@curefit/vm-models"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CFServiceInterfaces } from "./vm/ServiceInterfaces"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import GearProductViewBuilder from "../product/GearProductViewBuilder"
import { OrderSource } from "@curefit/order-common"
import { UserInfo } from "@curefit/user-common"
import { GearProductPricesResponse } from "@curefit/offer-common"

export class GearCategoryPage {
    public category: string
    public widgets: GearLandingPageWidget[]

    constructor(category: string, widgets: GearLandingPageWidget[]) {
        this.category = category,
            this.widgets = widgets
    }
}

export interface IGearLandingPageWidgetBuildParams {
    widgetType: string
    maxNumBanners: number
    edgeToEdge: boolean
    templateId: string
    bannerRatio: string
    backgroundColor: string
    data: GearLandingPageProduct[]
    layoutProps: LayoutProps
}

export class GearLandingPageWidget {
    public widgetType: string
    public maxNumBanners: number
    public edgeToEdge: boolean
    public templateId: string
    public bannerRatio: string
    public backgroundColor: string
    public data: GearLandingPageProduct[]
    public layoutProps: LayoutProps

    constructor(attributes: IGearLandingPageWidgetBuildParams) {
        this.widgetType = attributes.widgetType,
            this.maxNumBanners = attributes.maxNumBanners,
            this.edgeToEdge = attributes.edgeToEdge,
            this.templateId = attributes.templateId,
            this.bannerRatio = attributes.bannerRatio,
            this.backgroundColor = attributes.backgroundColor,
            this.data = attributes.data,
            this.layoutProps = attributes.layoutProps
    }
}

export interface ILayoutPropsBuildParams {
    interContentSpacing: number
    autoScroll: boolean
    roundedCorners: boolean
    edgeToEdge: boolean
    backgroundColor: string
    bannerHeight: number
    bannerWidth: number
    bannerOriginalHeight: number
    bannerOriginalWidth: number
    alignment: string
    showPagination: boolean
}

export class LayoutProps {
    public interContentSpacing: number
    public autoScroll: boolean
    public roundedCorners: boolean
    public edgeToEdge: boolean
    public backgroundColor: string
    public bannerHeight: number
    public bannerWidth: number
    public bannerOriginalHeight: number
    public bannerOriginalWidth: number
    public alignment: string
    public showPagination: boolean

    constructor(attributes: ILayoutPropsBuildParams) {
        this.interContentSpacing = attributes.interContentSpacing,
            this.autoScroll = attributes.autoScroll,
            this.roundedCorners = attributes.roundedCorners,
            this.edgeToEdge = attributes.edgeToEdge,
            this.backgroundColor = attributes.backgroundColor,
            this.bannerHeight = attributes.bannerHeight,
            this.bannerWidth = attributes.bannerWidth,
            this.bannerOriginalHeight = attributes.bannerOriginalHeight,
            this.bannerOriginalWidth = attributes.bannerOriginalWidth
        this.alignment = attributes.alignment,
            this.showPagination = attributes.showPagination
    }
}

export interface IGearLandingPageProductBuildParams {
    id: number
    brand: string
    segmentIds?: number[]
    contentMetric?: ContentMetric
    action?: Action
    image: string
    name: string
    price: string
    primaryPrice: string
    discountPercent: number
    secondaryPrice: string
    annotations: Annotation[]
}

export class GearLandingPageProduct {
    public id: number
    public brand: string
    public segmentIds?: number[]
    public contentMetric?: ContentMetric
    public action?: Action
    public image: string
    public name: string
    public price: string
    public display_price: string
    public discount_percent: number
    public secondary_price: string
    public annotations: Annotation[]

    constructor(attributes: IGearLandingPageProductBuildParams) {
        this.id = attributes.id
        this.brand = attributes.brand
        this.segmentIds = attributes.segmentIds
        this.contentMetric = attributes.contentMetric
        this.action = attributes.action
        this.image = attributes.image
        this.name = attributes.name
        this.price = attributes.price
        this.display_price = attributes.primaryPrice
        this.discount_percent = attributes.discountPercent
        this.secondary_price = attributes.secondaryPrice
        this.annotations = attributes.annotations
    }
}

export interface ContentMetric {
    contentId: string,
    contentSegmentId: string,
    contentSegmentName: string
}

export interface IProductCollectionWidgetBuilderParams {
    collection: GearCollection
    banner?: string
    data: any
}

@injectable()
export default class GearLandingPageBuilder {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.GearLandingPageConfig) protected landingPageConfig: GearLandingPageConfig,
        @inject(CUREFIT_API_TYPES.GearCatalogueLandingPageService) private gearCLPService: GearCatalogueLandingPageService,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) protected widgetBuilder: IWidgetBuilder,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
    ) { }

    async build(userContext: UserContext): Promise<PageWidget[]> {
        const widgets: PageWidget[] = []

        if (!_.isEmpty(this.landingPageConfig.categories)) {
            widgets.push(this.getCategoryBannerWidget())
        }

        if (!_.isEmpty(this.landingPageConfig.collections)) {
            const collectionData = await Promise.all(
                _.map(this.landingPageConfig.collections, c => this.getCollectionData(c.name, userContext))
            )
            _.map(this.landingPageConfig.collections, (c, index) => {
                widgets.push(this.getProductCollectionWidget({
                    collection: c,
                    banner: c.bannerImageUrl,
                    data: collectionData[index]
                }))
            })
        }

        return widgets
    }

    async buildPreBuzz(userContext: UserContext): Promise<PageWidget[]> {
        const widgets: PageWidget[] = []

        if (!_.isEmpty(this.landingPageConfig.prebuzzConfig)) {

            if (this.landingPageConfig.prebuzzConfig.headerBanner) {
                widgets.push(
                    this.getBannerWidget(
                        this.landingPageConfig.prebuzzConfig.headerBanner,
                        userContext.sessionInfo.userAgent
                    )
                )
            }

            // if timer is configured
            if (this.landingPageConfig.prebuzzConfig.timer) {
                widgets.push(this.getTimerWidget())
            }

            if (!_.isEmpty(this.landingPageConfig.prebuzzConfig.banners)) {
                _.map(this.landingPageConfig.prebuzzConfig.banners, banner => {
                    widgets.push(this.getBannerWidget(banner, userContext.sessionInfo.userAgent))
                })
            }
        }

        return widgets
    }

    async buildCategoryPage(response: { title: string, products: CatalogueProduct[], slug?: string }, userContext: UserContext): Promise<GearCategoryPage> {
        const data: GearLandingPageProduct[] = []
        const PRIMARY_IMAGE_TAG = "BOGO_IMAGE"
        for (const product of response.products) {
            const id: number = product.id
            const segmentIds: number[] = []
            const contentMetric: ContentMetric = {
                contentId: "",
                contentSegmentId: "",
                contentSegmentName: ""
            }
            const action: Action = {
                url: ActionUtil.gearProduct(String(product.id), undefined, AppUtil.isCultSportWebApp(userContext) ? product.slug : undefined),
                actionType: "NAVIGATION"
            }
            const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

            let productName = product.name

            if (!AppUtil.isCultSportWebApp(userContext) && AppUtil.shouldTruncateGearBrandName(userContext) && productName.length > 23) {
                productName = productName.substring(0, 20) + "..."
            }
            const image: string = product.master.images.length > 0 ? (_.isNil(_.find(product.master.images, i => _.includes(i.tags, PRIMARY_IMAGE_TAG))) ? product.master.images[0].mini_url : _.find(product.master.images, i => _.includes(i.tags, PRIMARY_IMAGE_TAG)).mini_url) : ""
            const name: string = isGearBrandNameSupported || product.brand == null ? productName : `${product.brand.toUpperCase()} ${productName}`
            const price: string = product.price
            const brand: string = product.brand
            const primaryPrice: string = product.display_price
            const secondaryPrice: string = product.discount_percent ? `${RUPEE_SYMBOL} ${product.mrp}` : undefined
            const discountPercent: number = product.discount_percent || undefined
            const annotations = _.map(product.annotations, (annotation: string) => GearProductViewBuilder.getStyledAnnotation(annotation))
            const gearLandingPageProduct: GearLandingPageProduct = new GearLandingPageProduct({
                id, segmentIds, contentMetric, action, image, name, price, primaryPrice, discountPercent, secondaryPrice, brand, annotations
            })
            data.push(gearLandingPageProduct)
        }

        const layoutProps: LayoutProps = new LayoutProps({
            interContentSpacing: 15,
            autoScroll: false,
            roundedCorners: true,
            edgeToEdge: false,
            backgroundColor: "#ffffff",
            bannerHeight: 225,
            bannerWidth: 164,
            bannerOriginalHeight: 225,
            bannerOriginalWidth: 164,
            alignment: "left",
            showPagination: false
        })

        const gearLandingPageWidget: GearLandingPageWidget = new GearLandingPageWidget({
            widgetType: "LIST_GRID_WIDGET",
            maxNumBanners: 3,
            edgeToEdge: false,
            templateId: "",
            bannerRatio: "164:225",
            backgroundColor: "#c8e8ec",
            data,
            layoutProps
        })

        // Adding VM widgets to the top of collection screen.
        // adding version check to send no vm widgets for older versions
        let widgets: any[] = [gearLandingPageWidget]

        if (!_.isEmpty(response.slug) && AppUtil.isWidgetsSupportedInGearCollection(userContext)) {
            const pageId = `gear-clp-${response.slug}`
            try {
                const page: Page = await this.pageService.getPage(pageId, AppUtil.getTenantFromUserContext(userContext))
                const widgetData = await this.widgetBuilder.buildWidgets(page.body, this.serviceInterfaces, userContext, null, null)
                widgets = widgetData.widgets.concat(widgets)
            }
            catch (ex) {
                this.logger.error(`pageId ${pageId} errored out in pageService - ${ex}`)
            }
        }

        const gearLandingPage: GearCategoryPage = new GearCategoryPage(response.title, widgets)
        return Promise.resolve(gearLandingPage)
    }

    private async getCollectionData(collectionName: string, userContext: UserContext): Promise<GearLandingPageProduct[]> {
        const response: { products: CatalogueProduct[] } = await this.gearCLPService.getCataloguePageWith(collectionName)

        const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)

        const userInfo: UserInfo = {userId: userContext.userProfile.userId, deviceId: userContext.sessionInfo.deviceId}
        const productOffers: GearProductPricesResponse = await this.offerServiceV3.getGearProductPrices(_.map(response.products, p => `${p.id}`), userInfo, orderSource)
        const products = GearUtil.addDiscountInformationToProducts(
            _.map(response.products, p => GearUtil.convertProductVariantsToGearSKUs(p)),
            productOffers.priceMap
        )

        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

        return _.map(products, (p: CatalogueProduct) => new GearLandingPageProduct({
            id: p.id,
            brand: p.brand,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.gearProduct(`${p.id}`, undefined, AppUtil.isCultSportWebApp(userContext) ? p.slug : undefined)
            } as Action,
            image: p.master.images[0].product_url,
            name: isGearBrandNameSupported || p.brand == null ? p.name : `${p.brand.toUpperCase()} ${p.name}`,
            price: p.price,
            primaryPrice: p.display_price,
            secondaryPrice: p.discount_percent ? `${RUPEE_SYMBOL} ${p.mrp}` : undefined,
            discountPercent: p.discount_percent || undefined,
            annotations: _.map(p.annotations, (annotation: string) => GearProductViewBuilder.getStyledAnnotation(annotation))
        }))
    }

    private getCategoryBannerWidget() {
        return new CategoryBannerWidget(
            _.map(this.landingPageConfig.categories, c => ({
                action: {
                    url: ActionUtil.gearCollection(c.action),
                    actionType: "NAVIGATION"
                },
                image: c.image,
                title: c.title
            })),
            {
                title: "Browse Categories",
                subTitle: "Choose from a range of products"
            }
        )
    }

    private getProductCollectionWidget(attributes: IProductCollectionWidgetBuilderParams) {
        const seeMoreAction: Action = {
            url: ActionUtil.gearCollection(attributes.collection.name),
            actionType: "NAVIGATION"
        }
        const header: HeaderWithBanner = {
            seemore: seeMoreAction,
            title: attributes.collection.title,
            subTitle: attributes.collection.subTitle,
        }
        if (attributes.banner) {
            header.banner = {
                imageUrl: attributes.banner,
                action: seeMoreAction
            }
        }

        return new ProductBrowseWidget(attributes.data, header)
    }

    private getTimerWidget() {
        const timerConfig = this.landingPageConfig.prebuzzConfig.timer
        return new TimerWidget(
            TimeUtil.getEpochFromDate(timerConfig.timerEndTime),
            timerConfig.action,
            timerConfig.title
        )
    }

    private getBannerWidget(bannerConfig: GearCarouselBanner, userAgent: UserAgent) {
        const data = _.map(bannerConfig.data, d => ({
            id: d.id,
            image: userAgent === "DESKTOP" ? d.desktopWebImage : d.mobileWebImage,
            action: d.action || ""
        }))

        return new BannerCarouselWidget(
            userAgent === "DESKTOP" ? bannerConfig.desktopBannerRatio : bannerConfig.mobileBannerRatio,
            data,
            undefined,
            bannerConfig.maxNumBanners || 1,
            bannerConfig.edgeToEdge || true
        )
    }

}
