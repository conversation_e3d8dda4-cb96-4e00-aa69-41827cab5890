import { ListWidgetV3 } from "@curefit/vm-models"
import { IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { IPopLiveOnDemandVideoResponse, OnDemandVideoCategory } from "@curefit/diy-common"
import { Action, PageTypes } from "@curefit/apps-common"
import { LivePremiereWidget } from "./LivePremiereWidget"
import {
    IEatOnDemandVideoResponse,
    LiveFitWorkoutFormat,
    OnDemandVideoResponseBase
} from "@curefit/diy-common"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE } from "../../util/LiveUtil"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { ProductType } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import AppUtil from "../../util/AppUtil"
import { LivePackUtil } from "../../util/LivePackUtil"

export class LivePremiereListWidgetBuilder extends ListWidgetV3 {
    category: OnDemandVideoCategory
    slidesToShow: number

    constructor() {
        super()
        this.widgets = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filter: string]: string }): Promise<IBaseWidget> {

        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly", false)) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"

        if (AppUtil.isWeb(userContext)) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }

        let category: OnDemandVideoCategory = _.get(queryParams, "category", undefined) as OnDemandVideoCategory
        const pageFrom = _.get(queryParams, "selectedTab") ? _.get(queryParams, "selectedTab") : _.get(queryParams, "pageId", undefined)
        const userId = userContext.userProfile.userId
        let cfInterfaces: CFServiceInterfaces
        try {
            cfInterfaces = interfaces as CFServiceInterfaces
        } catch {
            interfaces.logger.error("Problem in typecasting to CFServiceInterfaces")
            return undefined
        }
        const user = await cfInterfaces.userCache.getUser(userId)
        const isPhone = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent)
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        this.slidesToShow = isPhone ? undefined : 2.5

        if (bookmarkedOnly) {
            return undefined
        }
        if (!_.isNil(this.category)) {
            interfaces.logger.info(`Overridden category with ${this.category}`)
            category = this.category
        }
        if (_.isNil(category)) {
            interfaces.logger.error("Category was not passed")
            return undefined
        }

        const result = await interfaces.diyService.getAllLivePremiereVideos(category, userId, AppUtil.getTenantFromUserContext(userContext), vegOnlyFilter)
        const widgetPromise: Promise<IBaseWidget>[] = []
        if (!_.isEmpty((result))) {
            await Promise.all(
                _.map(result, async (premierItem: OnDemandVideoResponseBase) => {
                    const videoUrlFromBackend = premierItem.media[isPhone ? "APP_VIDEO_URL" : "WEB_VIDEO_URL"]
                    const isPremiereLiveNow = !_.isEmpty(videoUrlFromBackend)
                    const title = premierItem.title
                    const imageUrl = isPhone ? premierItem.media["APP_BANNER_URL"] : premierItem.media["WEB_BANNER_URL"]
                    const startDate = _.get(premierItem, "premiereContentConfig.goLiveTime", undefined)
                    let startDateFormatted: string
                    if (startDate) {
                        const tz = userContext.userProfile.timezone
                        startDateFormatted = TimeUtil.formatDateStringInTimeZone(startDate, tz, "ddd MMM DD  h:mm A").toUpperCase()
                    }
                    const premierTitle = isPremiereLiveNow ? "PREMIERE LIVE" : "PREMIERE"
                    const isLocked = premierItem.isLocked
                    let id: string
                    let instructorType: string
                    let instructorName: string
                    let instantAction: Action
                    let detailsAction: Action
                    let productType: ProductType | LiveFitWorkoutFormat
                    let sessionSource: LIVE_SESSION_ACTION_SOURCE
                    switch (category) {
                        case "EAT":
                            const eatItem = premierItem as IEatOnDemandVideoResponse
                            id = eatItem.onDemandVideoId
                            instructorType = "chef"
                            instructorName = _.get(eatItem, "instructorInfo.name", undefined)
                            productType = "EAT"
                            sessionSource = "on_demand_eat_session"
                            break
                        case "POP_LIVE":
                            const popItem = premierItem as IPopLiveOnDemandVideoResponse
                            id = popItem.popLiveOnDemandVideoId
                            instructorType = ""
                            instructorName = _.get(popItem, "meta.instructorInfo.name", undefined)
                            productType = "HOBBY"
                            sessionSource = "on_demand_pop_session"
                            break
                        default:
                            break
                    }
                    const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
                    const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, cfInterfaces.hamletBusiness)
                    instantAction = {
                        actionType: "NAVIGATION",
                        title: isPremiereLiveNow ? "PLAY NOW" : `${startDateFormatted}`,
                        url: isPremiereLiveNow ? await LiveUtil.generateOnDemandContentVideoNavigationUrl(
                            videoUrlFromBackend, id, category,
                            userContext, cfInterfaces
                        ) : `curefit://${PageTypes.LivePremiereDetailsPage}?id=${id}&category=${category}` + (pageFrom ? `&pageFrom=${pageFrom}` : ``),
                    }
                    detailsAction = {
                        actionType: "NAVIGATION",
                        title: "VIEW DETAILS",
                        url: `curefit://${PageTypes.LivePremiereDetailsPage}?id=${id}&category=${category}` + (pageFrom ? `&pageFrom=${pageFrom}` : ``),
                    }
                    const diyInstantAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, instantAction, isUserEligibleForTrial, productType, sessionSource, bucketId, blockInternationalUser)
                    widgetPromise.push(new LivePremiereWidget(id, title, premierTitle, instructorType, instructorName, imageUrl, startDateFormatted, diyInstantAction, detailsAction, _.get(premierItem, "premiereContentConfig.goLiveTime", undefined)).buildView(interfaces, userContext, queryParams))
                })
            )
        } else {
            interfaces.logger.error("No data retrieved from backend")
            return undefined
        }
        let widgets = await Promise.all(widgetPromise)

        widgets = widgets.filter(v => !_.isNil(v) && !_.isNull(v)).sort((a: LivePremiereWidget, b: LivePremiereWidget) => {
            if (a.goLiveTime <= b.goLiveTime) {
                return -1
            } else return 1
        })
        this.widgets.push(...widgets)
        return this
    }
}
