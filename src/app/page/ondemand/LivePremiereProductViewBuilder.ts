import { inject, injectable } from "inversify"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import {
    IConvertedOnDemandVideoResponse,
    IEatOnDemandVideoResponse,
    IPopLiveOnDemandVideoResponse,
    OnDemandVideoCategory,
    DIYRecipeView
} from "@curefit/diy-common"
import { IBaseWidget, IServiceInterfaces, ListWidgetV3 } from "@curefit/vm-models"

import { ILivePremiereDetailsParams, ILivePremiereDetailsResponse, getShareMesssage } from "./OnDemandCommon"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { CardHeader, HeaderCardWidget } from "../../common/views/HeaderCardWidget"
import { LivePremiereSummaryWidget } from "./LivePremiereSummaryWidget"
import { TimeUtil } from "@curefit/util-common"
import { ProductType } from "@curefit/product-common"
import { LiveFitWorkoutFormat } from "@curefit/diy-common"
import LiveUtil, { LIVE_SESSION_ACTION_SOURCE, RECIPE_PRODUCT_TYPE } from "../../util/LiveUtil"
import { WidgetView } from "../../common/views/WidgetView"
import { Action } from "@curefit/apps-common"
import { InstructorInfoWidget } from "./InstructorInfoWidget"
import AppUtil from "../../util/AppUtil"
import { RecipePackWidget } from "../vm/widgets/RecipePackWidget"
import { LivePackUtil } from "../../util/LivePackUtil"

@injectable()
export class LivePremiereProductViewBuilder {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, params: ILivePremiereDetailsParams): Promise<ILivePremiereDetailsResponse> {
        const id = params.id
        const category = params.category
        const userId = userContext.userProfile.userId
        const isSearchResult = params.isSearchResult

        const product = await interfaces.diyService.getPremiereVideoById(category, id, userId, AppUtil.getTenantFromUserContext(userContext))
        if (_.isNil(product)) {
            interfaces.logger.error(`Product with id ${id} not found`)
            return undefined
        }

        let body = await this.getWidgets(interfaces, userContext, product, category, isSearchResult)
        body = body.filter(v => !_.isNil(v) && !_.isNull(v))
        return {
            body,
        }
    }

    async getWidgets(interfaces: CFServiceInterfaces, userContext: UserContext, product: IConvertedOnDemandVideoResponse, category: OnDemandVideoCategory, isSearchResult: boolean = false): Promise<IBaseWidget[] & WidgetView[]> {
        const title = product.title
        const isPhone = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent)
        const videoUrlFromBackend = product.media[isPhone ? "APP_VIDEO_URL" : "WEB_VIDEO_URL"]
        const isPremiereLiveNow = !_.isEmpty(videoUrlFromBackend)
        // to support search video design
        const premierTitle = isSearchResult ? undefined : "PREMIERE"
        const startDate = _.get(product, "premiereContentConfig.goLiveTime")
        let startDateFormatted: string
        if (startDate) {
            const tz = userContext.userProfile.timezone
            startDateFormatted = TimeUtil.formatDateStringInTimeZone(startDate, tz, "ddd MMM DD  h:mm A").toUpperCase()
            startDateFormatted = isPremiereLiveNow ? "LIVE NOW" : startDateFormatted
        }
        const imageUrl = isPhone ? product.media["APP_BANNER_URL"] : product.media["WEB_BANNER_URL"]
        const productDescription = product.description
        const isLocked = product.isLocked
        let productTags: string[]
        let id: string
        let instructorType: string
        let instructorName: string
        let instructorImage: string
        let instructorDesignation: string
        let instructorExperience: string
        let instructorDescription: string
        let durationInSecs: number
        let duration: string
        let productType: ProductType | LiveFitWorkoutFormat
        let sessionSource: LIVE_SESSION_ACTION_SOURCE
        let relatedRecipes: DIYRecipeView[]

        switch (category) {
            case "EAT":
                const eatItem = product as IEatOnDemandVideoResponse
                id = eatItem.onDemandVideoId
                instructorType = "Chef"
                instructorName = _.get(eatItem, "instructorInfo.name")
                instructorImage = _.get(eatItem, "instructorInfo.profileImageUrl")
                instructorDesignation = _.get(eatItem, "instructorInfo.otherDetails.jobHighlight")
                instructorExperience = _.get(eatItem, "instructorInfo.otherDetails.totalExp")
                instructorDescription = _.get(eatItem, "instructorInfo.profileInfo")
                durationInSecs = _.get(eatItem, "meta.durationInSecs")
                const diet = _.get(eatItem, "meta.diet")
                const cuisine = _.get(eatItem, "meta.cuisine")
                productTags = [diet, cuisine]
                productTags = productTags.filter(v => !_.isNil(v) && !_.isNull(v))
                relatedRecipes = eatItem.relatedRecipes
                productType = "EAT"
                sessionSource = "on_demand_eat_session"
                break
            case "POP_LIVE":
                const popItem = product as IPopLiveOnDemandVideoResponse
                id = popItem.popLiveOnDemandVideoId
                instructorType = "Speaker"
                instructorName = _.get(popItem, "meta.instructorInfo.name")
                instructorImage = _.get(popItem, "meta.instructorInfo.profileImageUrl")
                instructorDesignation = _.get(popItem, "meta.instructorInfo.otherDetails.jobHighlight")
                instructorExperience = _.get(popItem, "meta.instructorInfo.otherDetails.totalExp")
                instructorDescription = _.get(popItem, "meta.instructorInfo.profileInfo")
                durationInSecs = _.get(popItem, "meta.durationInSec")
                productType = "HOBBY"
                sessionSource = "on_demand_pop_session"
                break
            default:
                break
        }
        if (durationInSecs) {
            if (durationInSecs < 60) {
                duration = `${durationInSecs} SECS`
            } else {
                const durationMinutes = Math.round(durationInSecs / 60)
                duration = durationMinutes === 1 ? `1 MIN` : `${durationMinutes} MINS`
            }
        }
        const shareAction = await this.getDeeplinkShareAction(interfaces, userContext, category, title, productDescription, imageUrl, id)
        const summaryWidget = await new LivePremiereSummaryWidget(id, title, isPremiereLiveNow, premierTitle, productDescription, category === "POP_LIVE" ? "" : instructorType, instructorName, duration, startDateFormatted, productTags, shareAction).buildView(interfaces, userContext, {})
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        const action: Action = {
            actionType: "NAVIGATION",
            url: await LiveUtil.generateOnDemandContentVideoNavigationUrl(
                videoUrlFromBackend, id, category,
                userContext, interfaces
            )
        }
        const diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, action, isUserEligibleForTrial, productType, sessionSource, bucketId, blockInternationalUser)
        const cardHeader: CardHeader = {
            imageUrl: imageUrl,
            isVideo: isPremiereLiveNow,
            action: isPremiereLiveNow ? diyAction : undefined,
        }
        const cardWidgets = [summaryWidget]
        const headerCardWidget = await new HeaderCardWidget(cardWidgets, cardHeader).buildView(interfaces, userContext, {})
        const bodyWidgets: (IBaseWidget[] & WidgetView[]) = []
        bodyWidgets.push(headerCardWidget)
        if (!_.isEmpty(instructorName)) {
            const instructorInfoWidget = await new InstructorInfoWidget(`About ${instructorType}`, instructorName, instructorImage, instructorDesignation, instructorExperience, instructorDescription).buildView(interfaces, userContext, {})
            bodyWidgets.push(instructorInfoWidget)
        }
        if (relatedRecipes && AppUtil.isRelatedRecipesSupportedInPremiere(userContext)) {
            const relatedRecipesWidget = await this.getRelatedRecipesWidget(interfaces, userContext, isUserEligibleForMonetisation, isUserEligibleForTrial, relatedRecipes)
            bodyWidgets.push(relatedRecipesWidget)
        }
        return bodyWidgets
    }

    async getRelatedRecipesWidget(interfaces: CFServiceInterfaces, userContext: UserContext, isUserEligibleForMonetisation: boolean, isUserEligibleForLivePackTrial: boolean, recipes: DIYRecipeView[]): Promise<IBaseWidget> {
        if (!_.isEmpty(recipes)) {
            const relatedRecipesList = await new RelatedRecipesList().buildView(interfaces, userContext, {})
            const isPhone = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent)
            relatedRecipesList.slidesToShow = isPhone ? undefined : 2
            const recipeWidgetPromises: Promise<IBaseWidget>[] = []
            recipes.forEach(recipe => {
                const isRecipeBookmarked = _.get(recipe, "userMeta.isSubscribed", false)
                const recipeItemAction = LiveUtil.getDIYRecipeAction(recipe, userContext.sessionInfo.userAgent)
                const isLocked = recipe.locked && isUserEligibleForMonetisation
                const diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, recipeItemAction, isUserEligibleForLivePackTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
                const recipePackWidget = new RecipePackWidget(recipe.imageDetails.thumbnailImage, recipe.title, recipe.isVeg, diyAction,
                    recipe.preparationTime, recipe.id, isRecipeBookmarked, isLocked).buildView(interfaces, userContext, {})
                recipeWidgetPromises.push(recipePackWidget)
            })
            relatedRecipesList.title = "Related recipes"
            relatedRecipesList.widgets = await Promise.all(recipeWidgetPromises)
            return relatedRecipesList
        } else {
            return undefined
        }
    }

    async getDeeplinkShareAction(interfaces: CFServiceInterfaces, userContext: UserContext, onDemandVideoCategory: OnDemandVideoCategory, title: string, description: string, thumbnailUrl: string, videoId: string): Promise<Action | undefined> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const deeplink = await interfaces.deeplinkService.getOnDemandPremiereShareLink(tenant, onDemandVideoCategory, title, description, thumbnailUrl, videoId)
        const shareMessage = deeplink ? getShareMesssage(deeplink, onDemandVideoCategory) : undefined
        const shareAction: Action = deeplink ? {
            actionType: "SHARE_ACTION",
            title: "Share",
            meta: {
                isShareSingle: true,
                shareOptions: {
                    message: shareMessage
                }
            }
        } : undefined
        return shareAction
    }
}

class RelatedRecipesList extends ListWidgetV3 {
    slidesToShow: number

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<RelatedRecipesList> {
        return this
    }
}
