import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"

export class LivePremiereWidget extends BaseWidget {
    id: string
    title: string
    imageUrl?: string
    startDate?: string
    premiereTitle: string
    instructorType?: string
    instructorName?: string
    instantAction?: Action
    detailsAction?: Action
    goLiveTime?: Date

    constructor(id: string, title: string, premiereTitle: string, instructorType: string, instructorName: string, imageUrl?: string, startDate?: string, instantAction?: Action, detailsAction?: Action, goLiveTime?: Date) {
        super("LIVE_PREMIERE_WIDGET")
        this.id = id
        this.title = title
        this.imageUrl = imageUrl
        this.startDate = startDate
        this.premiereTitle = premiereTitle
        this.instructorType = instructorType
        this.instructorName = instructorName
        this.instantAction = instantAction
        this.detailsAction = detailsAction
        this.goLiveTime = goLiveTime
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<LivePremiereWidget> {
        return this
    }
}