import { OnDemandVideoCategory } from "@curefit/diy-common"
import { IBaseWidget } from "@curefit/vm-models"
import { IRecipePagenatedResponse } from "../../recipe/RecipeCommon"

export interface IOnDemandCollectionParams {
    pageNumber: number
    pageSize: number
    collectionType: OnDemandVideoCategory
    collectionId: string
    vegOnly?: boolean
}

export const getShareMesssage = (deeplink: string, onDemandVideoCategory: OnDemandVideoCategory) => {
    switch (onDemandVideoCategory) {
        case "EAT":
            return `Hey, check out this eat.live show on the cult.fit app. Watch a series of cookery shows on eat.live and get cooking now! * ${deeplink} *`
        default:
            return `Hey, check out this pop.live show on the cult.fit app! Watch a series of shows on hobbies, lifestyle, media and more on pop.live. * ${deeplink} *`
    }
}

export interface IOnDemandAutoPlayResponse {
    videos: IOnDemandAutoPlayVideo[]
}

export interface IOnDemandAutoPlayParams {
    presentVideoId: string
    category: OnDemandVideoCategory
}


export interface IOnDemandAutoPlayVideo {
    title: string
    contentId: string
    activityId: string
    absoluteVideoUrl: string
    videoUrl: string
    imageUrl: string
    onDemandCategory: OnDemandVideoCategory
}

export interface ILivePremiereDetailsResponse {
    body: IBaseWidget[]
}

export interface ILivePremiereDetailsParams {
    id: string
    category: OnDemandVideoCategory
    isSearchResult?: boolean
}

export interface IBreadCrumb {
    link?: string
    pathname?: string
    title: string
}

export interface IOnDemandPageResponse extends IRecipePagenatedResponse {
    filters: any
}
