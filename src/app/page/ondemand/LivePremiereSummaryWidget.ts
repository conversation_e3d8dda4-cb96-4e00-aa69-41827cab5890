import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"

export class LivePremiereSummaryWidget extends BaseWidget {
    id: string
    title: string
    isPremiereLive: boolean
    premiereTitle: string
    startDate?: string
    instructorType?: string
    instructorName?: string
    summary: string
    shareAction?: Action
    duration?: string
    productTags?: string[]

    constructor(id: string, title: string, isPremiereLive= false, premiereTitle: string, summary: string, instructorType: string, instructorName: string, duration?: string, startDate?: string, productTags?: string[], shareAction?: Action) {
        super("LIVE_PREMIERE_SUMMARY_WIDGET")
        this.id = id
        this.title = title
        this.isPremiereLive = isPremiereLive
        this.startDate = startDate
        this.premiereTitle = premiereTitle
        this.summary = summary
        this.instructorType = instructorType
        this.instructorName = instructorName
        this.duration = duration
        this.productTags = productTags
        this.shareAction = shareAction
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<LivePremiereSummaryWidget> {
        return this
    }
}
