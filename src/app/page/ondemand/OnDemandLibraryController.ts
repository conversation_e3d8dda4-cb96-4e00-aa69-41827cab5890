import { controller, httpGet, httpPost, queryParam } from "inversify-express-utils"
import AuthMiddleware from "../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { Container, inject } from "inversify"
import * as express from "express"
import { UserContext } from "@curefit/vm-models"
import { OnDemandVideoCategory } from "@curefit/diy-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import * as _ from "lodash"
import {
    ILivePremiereDetailsParams,
    ILivePremiereDetailsResponse,
    IOnDemandAutoPlayParams,
    IOnDemandAutoPlayResponse,
    IOnDemandCollectionParams, IOnDemandPageResponse,
} from "./OnDemandCommon"
import { OnDemandProductCollectionViewBuilder } from "../vm/widgets/ondemand/OnDemandProductCollectionViewBuilder"
import { IRecipePagenatedResponse } from "../../recipe/RecipeCommon"
import { OnDemandAutoPlayBuilder } from "../vm/widgets/ondemand/OnDemandAutoPlayBuilder"
import { LivePremiereProductViewBuilder } from "./LivePremiereProductViewBuilder"
import { RecipeCollectionDetailViewBuilder } from "../../recipe/RecipeCollectionDetailViewBuilder"
import AppUtil from "../../util/AppUtil"

export function onDemandControllerFactory(kernel: Container) {

    @controller("/ondemand",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession
    )
    class OnDemandLibraryController {
        constructor(
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.OnDemandProductViewBuilder) private onDemandProductViewBuilder: OnDemandProductCollectionViewBuilder,
            @inject(CUREFIT_API_TYPES.LivePremiereProductViewBuilder) private livePremiereProductViewBuilder: LivePremiereProductViewBuilder,
            @inject(CUREFIT_API_TYPES.OnDemandAutoPlayBuilder) private onDemandAutoPlayBuilder: OnDemandAutoPlayBuilder,
            @inject(CUREFIT_API_TYPES.RecipeCollectionDetailViewBuilder) private recipeCollectionDetailPageBuilder: RecipeCollectionDetailViewBuilder
        ) {
        }

        @httpPost("/collection")
        public async getCollectionById(req: express.Request, res: express.Response): Promise<IOnDemandPageResponse> {
            const body = req.body
            const userContext = req.userContext as UserContext
            const isInternationalUser = AppUtil.isInternationalApp(userContext)

            const collectionType: OnDemandVideoCategory | "RECIPE" = _.get(body, "collectionCategory", undefined)
            const collectionId: string = _.get(body, "collectionId", undefined)
            const pageNumber: number = _.get(body, "pageNumber", 0)
            const pageSize: number = _.get(body, "pageSize", 10)
            const vegOnly: boolean = String(_.get(body, "vegOnly", false)) === "true"

            if (_.isNil(collectionId) || _.isNil(collectionType)) {
                throw new Error("Invalid data passed.")
            }

            // hack: using recipe collection inside on demand to use the same client page
            if (collectionType === "RECIPE") {
                if (!_.isNil(collectionId)) {
                    return await this.recipeCollectionDetailPageBuilder.buildView(this.serviceInterfaces, userContext, {
                        "collectionId": collectionId,
                        "vegOnly": vegOnly
                    }, isInternationalUser)
                }
            }

            const params: IOnDemandCollectionParams = {
                collectionId,
                collectionType: collectionType as OnDemandVideoCategory,
                pageNumber,
                pageSize,
                vegOnly
            }

            return this.onDemandProductViewBuilder.buildView(this.serviceInterfaces, userContext, params, isInternationalUser)
        }

        @httpGet("/autoplayoptions")
        public async getAutoPlayOptions(req: express.Request, res: express.Response): Promise<IOnDemandAutoPlayResponse> {
            const userContext = req.userContext as UserContext
            const presentVideoId: string = _.get(req, "query.currentVideoId", undefined)
            const category: OnDemandVideoCategory = _.get(req, "query.category", undefined)

            if (_.isNil(presentVideoId) || _.isNil(category)) {
                throw new Error("Few request params are missing.")
            }
            const params: IOnDemandAutoPlayParams = {
                presentVideoId,
                category
            }
            return this.onDemandAutoPlayBuilder.buildView(this.serviceInterfaces, userContext, params)
        }

        @httpPost("/premieredetails")
        public async getPremiereDetails(req: express.Request, res: express.Response): Promise<ILivePremiereDetailsResponse> {
            const reqBody = req.body
            const userContext = req.userContext as UserContext

            const category: OnDemandVideoCategory = _.get(reqBody, "category", undefined)
            const id: string = _.get(reqBody, "id", undefined)
            const isSearchResult: boolean = _.get(reqBody, "isSearchResult", false)

            if (_.isNil(id) || _.isNil(category)) {
                throw new Error("Invalid data passed.")
            }

            const params: ILivePremiereDetailsParams = {
                id,
                category,
                isSearchResult
            }

            return this.livePremiereProductViewBuilder.buildView(this.serviceInterfaces, userContext, params)
        }
    }
}
