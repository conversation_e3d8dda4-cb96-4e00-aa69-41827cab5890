import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"

export class InstructorInfoWidget extends BaseWidget {
    title: string
    instructorName: string
    instructorImage?: string
    instructorDesignation?: string
    instructorExperience?: string
    instructorDescription?: string

    constructor(title: string, instructorName: string, instructorImage?: string, instructorDesignation?: string, instructorExperience?: string, instructorDescription?: string) {
        super("INSTRUCTOR_INFO_WIDGET")
        this.title = title
        this.instructorName = instructorName
        this.instructorImage = instructorImage
        this.instructorDesignation = instructorDesignation
        this.instructorExperience = instructorExperience
        this.instructorDescription = instructorDescription
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<InstructorInfoWidget> {
        return this
    }
}