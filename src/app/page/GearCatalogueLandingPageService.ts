import { inject, injectable } from "inversify"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import * as _ from "lodash"
import { CataloguePage, CatalogueProduct } from "@curefit/gear-common"
import { GearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
const url = require("url")

interface GearCatalogueLandingPageCache {
    bySlugs: Map<string, { title: string, products: CatalogueProduct[] }>
}

@injectable()
class GearCatalogueLandingPageService extends InMemoryCacheService<GearCatalogueLandingPageCache> {
    private slugsWithOptionalPagination: string[] = []
    constructor(
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(GEARVAULT_CLIENT_TYPES.GearService) private gearService: GearService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 15 * 60)
        this.load("GearCatalogueLandingPageService")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    async loadData(): Promise<GearCatalogueLandingPageCache> {
        const bySlugs = new Map<string, { title: string, products: CatalogueProduct[] }>()
        try {
            for (const fetchKey of this.slugsWithOptionalPagination) {
                const { pathname: slug, query = {} } = url.parse(fetchKey, true)
                const { pageNumber, perPage } = query
                const productCollection: { title: string, products: CatalogueProduct[] } = await this.gearService.getProductCollection(slug, pageNumber, perPage)
                bySlugs.set(fetchKey, productCollection)
            }
        }
        catch (err) {
            this.logger.error(err)
        }
        return { bySlugs }
    }

    async getCataloguePageWith(slug: string, pageNumber?: number, perPage?: number): Promise<{ title: string, products: CatalogueProduct[] }> {
        try {
            const fetchKey = `${slug}${pageNumber && perPage ? `?pageNumber=${Number(pageNumber)}&perPage=${Number(perPage)}` : ""}`
            this.logger.info(`The corresponding fetch key is ${fetchKey}`)
            if (!this.cache.bySlugs.has(fetchKey)) {
                const slugPresent = this.slugsWithOptionalPagination.indexOf(fetchKey) > -1
                if (!slugPresent) {
                    this.slugsWithOptionalPagination.push(fetchKey)
                }
                this.logger.debug(`Fetching ${fetchKey} page from gearvault and serving`)
                return await this.gearService.getProductCollection(slug, pageNumber, perPage)
            }
            this.logger.debug(`Fetching ${fetchKey} page from cache and serving`)
            return this.cache.bySlugs.get(fetchKey)
        }
        catch (err) {
            this.logger.error("Failed to getCataloguePageWith " + err)
        }
    }
}

export default GearCatalogueLandingPageService
