import { inject, injectable } from "inversify"
import { Page, PageWidgetTitle } from "./Page"
import {
    <PERSON><PERSON>ard,
    Banner,
    BannerWidget,
    CareConsultationWidget,
    ClpFooterWidget,
    ConsultationActionCard,
    DoctorListWidget,
    ExperienceWidget,
    MagazineListWidget
} from "./PageWidgets"
import IPageBuilder from "./IPageBuilder"
import CareLandingPageConfig from "./CareLandingPageConfig"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { FoodProduct as Product } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { DiagnosticProductResponse } from "@curefit/care-common"
import { User } from "@curefit/user-common"
import { Session } from "@curefit/userinfo-common"
import LocationUtil from "../util/LocationUtil"
import IUserBusiness from "../user/IUserBusiness"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import * as _ from "lodash"
import { Doctor } from "@curefit/care-common"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { CareUtil } from "../util/CareUtil"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { ActionUtil } from "@curefit/base-utils"
import { PackOffersResponse } from "@curefit/offer-service-client"
import { OfferUtil } from "@curefit/base-utils"
import { UserContext } from "@curefit/userinfo-common"
import { CacheHelper } from "../util/CacheHelper"
import AppUtil from "../util/AppUtil"
import { CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "./vm/ServiceInterfaces"

const clone = require("clone")

@injectable()
class CareLandingPageBuilderV1 implements IPageBuilder {
    constructor(@inject(CUREFIT_API_TYPES.CareLandingPageConfig) private careLandingPageConfig: CareLandingPageConfig,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
    }

    async build(userContext: UserContext, session: Session, lat: number, lon: number, appVersion: number, osName: string, codepushVersion: number): Promise<Page> {
        const isWithinBangalore: boolean = LocationUtil.isWithinBangalore(lat, lon)
        const page: Page = {
            userLocation: isWithinBangalore ? "bangalore" : "others",
            widgets: []
        }
        const userId: string = session.userId
        const deviceId: string = session.deviceId
        const apiPromises: Promise<any>[] = []
        const productPromise1 = this.catalogueService.getProduct(this.careLandingPageConfig.consultationProducts[0].productId)
        const productPromise2 = this.catalogueService.getProduct(this.careLandingPageConfig.consultationProducts[1].productId)
        const hcuBundlePromise = this.healthfaceService.browseProducts("BUNDLE", "HCU")
        const doctorListPromise = this.healthfaceService.getResources("DOCTOR", 0, 100)
        const lifeStyleCoachesPromise = this.healthfaceService.getResources("LIFESTYLE_COACH", 0, 100)
        const userPromise = this.userCache.getUser(session.userId)

        apiPromises.push(productPromise1)
        apiPromises.push(productPromise2)
        apiPromises.push(hcuBundlePromise)
        apiPromises.push(doctorListPromise)

        await Promise.all(apiPromises)

        const product1: Product = await productPromise1
        const product2: Product = await productPromise2
        const hcuProducts: DiagnosticProductResponse[] = await hcuBundlePromise
        const doctors: Doctor[] = (await doctorListPromise).doctorList.content
        const coaches: Doctor[] = (await lifeStyleCoachesPromise).doctorList.content

        page.widgets.push(this.getBannerWidget(this.careLandingPageConfig.banners))

        const consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "CONSULTATION",
            [product1.productId, product2.productId],
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const hcuOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            hcuProducts.map(product => product.productCode),
            AppUtil.callSourceFromContext(userContext),
            this.serviceInterfaces,
            true
        )
        const user = await userPromise
        const isInternalUser = user.isInternalUser
        page.widgets.push(this.getConsultationWidget(product1, product2, consultationOffer, osName, appVersion, codepushVersion, isInternalUser))
        page.widgets.push(this.getBundleListWidget(user, hcuProducts, this.careLandingPageConfig.hcuTitle, hcuOffers))
        if (!_.isEmpty(doctors)) {
            page.widgets.push(this.getDoctorListWidget("MEET OUR DOCTORS", doctors, true))
        }
        if (!_.isEmpty(coaches)) {
            page.widgets.push(this.getDoctorListWidget("MEET OUR LIFESTYLE COACHES", coaches, true))
        }
        page.widgets.push(this.getExperienceWidget())
        const footerWidget: ClpFooterWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            texts: this.careLandingPageConfig.footerTexts,
            widgetType: "CLP_FOOTER_WIDGET"
        }
        page.widgets.push(footerWidget)
        return page
    }


    private getBundleListWidget(user: User, products: DiagnosticProductResponse[], widgetTitle: PageWidgetTitle, bundleOffer: PackOffersResponse): MagazineListWidget {
        const actioncards: ActionCard[] = []
        products.forEach(product => {
            const diagnosticsProduct = CatalogueServiceUtilities.toDiagnosticsProduct(product)
            const offerDetails = OfferUtil.getPackOfferAndPrice(diagnosticsProduct, bundleOffer)
            const price: ProductPrice = offerDetails.price
            if (!_.isEmpty(offerDetails.offers)) {
                price.discountText = offerDetails.offers[0].description
            }
            actioncards.push({
                // title: diagnosticsProduct.title,
                price: price,
                action: ActionUtil.carefitbundle(diagnosticsProduct.productId, diagnosticsProduct.subCategoryCode),
                image: diagnosticsProduct.imageUrl,
                // subTitle: diagnosticsProduct.subTitle
            })
        })
        return new MagazineListWidget(actioncards, widgetTitle)
    }

    private getBannerWidget(banners: Banner[]): BannerWidget {
        const widget = new BannerWidget(banners, "1242:743")
        widget.hasDividerBelow = false
        return widget
    }

    private getConsultationWidget(product1: Product, product2: Product, consultationOffer: PackOffersResponse, osName: string, appVersion: number, codepushVersion: number, isInternalUser: boolean): CareConsultationWidget {
        const title: {
            text: string
            title: string
        } = this.careLandingPageConfig.consultationTitle
        const actionCards: ConsultationActionCard[] = clone(this.careLandingPageConfig.consultationProducts)
        const offerDetails1 = OfferUtil.getPackOfferAndPrice(product1, consultationOffer)
        const offerDetails2 = OfferUtil.getPackOfferAndPrice(product2, consultationOffer)
        let price: ProductPrice
        if (!CareUtil.isSplitConsultationSupported(osName, appVersion, codepushVersion, isInternalUser)) {
            price = offerDetails2.price
        } else {
            actionCards[0].price = offerDetails1.price
            actionCards[1].price = offerDetails2.price
        }
        return new CareConsultationWidget(title.title, title.text, actionCards, price)
    }

    private getDoctorListWidget(title: string, doctors: Doctor[], hasDividerBelow: boolean): DoctorListWidget {
        const widget = new DoctorListWidget(title, doctors, {
            actionType: "SHOW_DOCTOR_DETAILS_MODAL"
        })
        widget.hasDividerBelow = hasDividerBelow
        return widget
    }

    private getExperienceWidget(): ExperienceWidget {
        const experienceWidget: ExperienceWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "EXPERIENCE_WIDGET",
            widgetTitle: this.careLandingPageConfig.experienceSpaceTitle,
            actioncards: this.careLandingPageConfig.experienceItems,
            moreWidget: this.careLandingPageConfig.experienceMoreAction
        }
        return experienceWidget
    }
}


export default CareLandingPageBuilderV1
