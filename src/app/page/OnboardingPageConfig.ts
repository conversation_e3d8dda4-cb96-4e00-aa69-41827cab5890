import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { IsDefined, IsOptional, MaxLength, validate, ValidateNested } from "class-validator"
import { plainToClass, Type } from "class-transformer"
import { GenericError } from "@curefit/error-client"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

export class BannerAction {
    actionType: string
    title: string
}

export class OnboardingBanner {

    title: string

    @IsOptional()
    subTitle: string

    image: string

    @IsDefined()
    imageRatio: string

    @ValidateNested()
    @Type(() => BannerAction)
    action?: BannerAction
}

export class OnboardingData {

    @ValidateNested()
    @Type(() => OnboardingBanner)
    banners: OnboardingBanner[]

    theme: OnboardingTheme

    @ValidateNested()
    @Type(() => BannerAction)
    pageAction?: BannerAction
}

export class OnboardingStory {
    description?: string
    image?: string
    videoUrl?: string
    duration?: number
}

export class LayoutPropsData {
    variant?: string
    aspectRatio?: string
    edgeToEdge?: boolean
}

export class StoriesLayoutProps {
    type?: string

    @ValidateNested()
    @Type(() => LayoutPropsData)
    data?: LayoutPropsData
}

export class OnboardingStoriesData {
    widgetType: string

    @ValidateNested()
    @Type(() => OnboardingStory)
    data: OnboardingStory[]

    @ValidateNested()
    @Type(() => StoriesLayoutProps)
    layoutProps: StoriesLayoutProps

    autoPlay?: boolean
}

export class LoginData {
    @ValidateNested()
    @Type(() => OnboardingStoriesData)
    stories: OnboardingStoriesData

    theme: OnboardingTheme
}

export type OnboardingType = "APP_LAUNCH"| "GOAL_PLAN" | "PULSE_ONBOARDING"
export type OnboardingTheme = "LIGHT"| "DARK"| "light"| "dark"

export class OnboardingDataByCity {

    @ValidateNested()
    @Type(() => OnboardingData)
    byCityId: Map<string, OnboardingData>

    @ValidateNested()
    @Type(() => LoginData)
    onboardingData?: LoginData
}

export class OnboardingDataByType {

    @ValidateNested()
    @Type(() => OnboardingDataByCity)
    byType: Map<string, OnboardingDataByCity>
}

@injectable()
class OnboardingPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 34 * 60)
        this.load("OnboardingPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao
            .findOne({ pageId: { $eq: "OnboardingPageConfig" } })
            .then(({data}) => plainToClass<OnboardingDataByType, OnboardingDataByType>(OnboardingDataByType, data as OnboardingDataByType))
            .then(async (data: OnboardingDataByType) => {
                const errors = await validate(data)
                if (errors.length > 0) {
                    const genericErr: GenericError = new GenericError({message: "validation failed. errors: " + errors})
                    genericErr.statusCode = 400
                    throw genericErr
                }
                this.data = data
                return data
            })
    }
    public data: OnboardingDataByType
}

export default OnboardingPageConfig
