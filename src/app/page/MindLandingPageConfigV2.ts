import { inject, injectable } from "inversify"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import BaseCultLandingPageConfig from "./BaseCultLandingPageConfig"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class MindLandingPageConfigV2 extends BaseCultLandingPageConfig {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) protected rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(pageConfigDao, rollbarService, logger)
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

     getPageConfigName(): string {
        return "MindLandingPageConfigV2"
     }
}

export default MindLandingPageConfigV2
