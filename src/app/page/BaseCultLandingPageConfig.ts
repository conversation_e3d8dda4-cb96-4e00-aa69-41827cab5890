import { <PERSON>, FooterText, Video } from "./PageWidgets"
import { inject, injectable } from "inversify"
import { PageWidgetMore, PageWidgetTitle } from "./Page"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { HowItWorksItem } from "@curefit/product-common"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"


@injectable()
abstract class BaseCultLandingPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) protected rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
    ) {
        super(logger, 35 * 60)
        this.load("BaseCultLandingPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    abstract getPageConfigName(): string
    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: this.getPageConfigName() } }).then(pageConfig => {
            const data = pageConfig.data
            this.banners = data.banners
            this.freeTrialOffer = data.freeTrialOffer
            this.freeTrialBanner = data.freeTrialBanner
            this.introVideo = data.introVideo
            this.diyIntroVideo = data.diyIntroVideo
            this.packsTitle = data.packsTitle
            this.singleCentrePacksTitle = data.singleCentrePacksTitle
            this.groupPacksTitle = data.groupPacksTitle
            this.packsMoreAction = data.packsMoreAction
            this.howItWorksTitle = data.howItWorksTitle
            this.howItWorksItems = data.howItWorksItems
            this.diyHowItWorksTitle = data.diyHowItWorksTitle
            this.diyHowItWorksItems = data.diyHowItWorksItems
            this.workoutTitle = data.workoutTitle
            this.workoutIds = data.workoutIds
            this.trainersTitle = data.trainersTitle
            this.trainersMoreAction = data.trainersMoreAction
            this.centerTitle = data.centerTitle
            this.centerMoreAction = data.centerMoreAction
            this.experienceSpaceTitle = data.experienceSpaceTitle
            this.experienceItems = data.experienceItems
            this.experienceMoreAction = data.experienceMoreAction
            this.DIYPacksTitle = data.DIYPacksTitle
            this.DIYPacksMoreAction = data.DIYPacksMoreAction
            this.footerTexts = data.footerTexts
            this.footerImage = data.footerImage
            this.becomeMemberItem = data.becomeMemberItem
            return data
        })
    }

    public banners: Banner[]
    public freeTrialOffer: Banner
    public freeTrialBanner: Banner
    public introVideo: Video
    public diyIntroVideo: Video
    public packsTitle: PageWidgetTitle
    public singleCentrePacksTitle: PageWidgetTitle
    public groupPacksTitle: PageWidgetTitle
    public packsMoreAction: PageWidgetMore
    public howItWorksTitle: PageWidgetTitle
    public howItWorksItems: HowItWorksItem[]
    public diyHowItWorksTitle: PageWidgetTitle
    public diyHowItWorksItems: HowItWorksItem[]
    public workoutTitle: PageWidgetTitle
    public workoutIds: number[]
    public trainersTitle: PageWidgetTitle
    public becomeMemberItem: PageWidgetTitle
    public trainersMoreAction: PageWidgetMore
    public centerTitle: PageWidgetTitle
    public centerMoreAction: PageWidgetMore
    public experienceSpaceTitle: PageWidgetTitle
    public experienceMoreAction: PageWidgetMore
    public experienceItems: Banner[]
    public DIYPacksTitle: PageWidgetTitle
    public DIYPacksMoreAction: PageWidgetMore
    public footerTexts: FooterText[]
    public footerImage: string

}

export default BaseCultLandingPageConfig
