import { <PERSON><PERSON><PERSON>, Banner, FooterText } from "./PageWidgets"
import { inject, injectable } from "inversify"
import { PageWidgetMore, PageWidgetTitle } from "./Page"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class EatLandingPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 32 * 60)
        this.load()
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "EatLandingPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.eatFitCategories = data.eatFitCategories
            this.eatFitTitle = data.eatFitTitle
            this.topPackIds = data.topPackIds
            this.topPacksTitle = data.topPacksTitle
            this.topPacksMoreAction = data.topPacksMoreAction
            this.recipeIds = data.recipeIds
            this.recipesTitle = data.recipesTitle
            this.recipesMoreAction = data.recipesMoreAction
            this.recipes = data.recipes
            this.tryMealAction = data.tryMealAction
            this.banners = data.banners
            this.offerTopBanners = data.offerTopBanners
            this.offerBanners = data.offerBanners
            this.freeTrialOffer = data.freeTrialOffer
            this.freeTrialSoldOut = data.freeTrialSoldOut
            this.freePackForCultUser = data.freePackForCultUser
            this.snacksHappyHours = data.snacksHappyHours
            this.breakfastHappyHours = data.breakfastHappyHours
            this.dinnerHappyHours = data.dinnerHappyHours
            this.lunchHappyHours = data.lunchHappyHours
            this.freeTrialYetToOpen = data.freeTrialYetToOpen
            this.footerTexts = data.footerTexts
            this.footerImage = data.footerImage
            this.salesTagsImages = data.salesTagsImages
            this.crewBusy = data.crewBusy
            this.badWeather = data.badWeather
            return data
        })
    }

    public banners: Banner[]
    public offerTopBanners: Banner[]
    public offerBanners: Banner[]
    public freeTrialOffer: Banner
    public freeTrialSoldOut: Banner
    public freePackForCultUser: Banner
    public snacksHappyHours: Banner
    public breakfastHappyHours: Banner
    public dinnerHappyHours: Banner
    public lunchHappyHours: Banner
    public freeTrialYetToOpen: Banner
    public eatFitCategories: ActionCard[]
    public eatFitTitle: PageWidgetTitle
    public topPackIds: string[]
    public topPacksTitle: PageWidgetTitle
    public topPacksMoreAction: PageWidgetMore
    public recipes: ActionCard[]
    public recipeIds: string[]
    public recipesTitle: PageWidgetTitle
    public recipesMoreAction: PageWidgetMore
    public tryMealAction: ActionCard
    public footerTexts: FooterText[]
    public footerImage: string
    public salesTagsImages: any
    public crewBusy: Banner
    public badWeather: Banner
}

export default EatLandingPageConfig
