import { inject, injectable } from "inversify"
import { EatClpPage, EatClpPageV2, EatFitMealSlot, MealAvailability, Page, PageWidget, PageWidgetTitle } from "./Page"
import {
    ActionCard,
    Banner,
    BannerWidget,
    MagazineListWidget,
    MealItem,
    MealListItemWidget,
    OfferWidget,
    PackProgress,
    PackProgressActionCard,
    PackProgressListWidget,
    TitleWidget
} from "./PageWidgets"
import { Action, ManageOptionIcon, MealAction, WidgetView } from "../common/views/WidgetView"
import IPageBuilder from "./IPageBuilder"
import EatLandingPageConfig from "./EatLandingPageConfig"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IPackService } from "@curefit/alfred-client"
import { IGateService } from "@curefit/delivery-client"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { IOfferServiceEther as IOfferService } from "@curefit/offer-service-client"
import { CartOffer, FoodPackOffersResponseV2 } from "@curefit/offer-common"
import { DelayMode, DelayModeReason } from "@curefit/eat-common"
import { DeliveryArea, FoodPack, FoodPackOption, MealSlot, FoodProduct as Product } from "@curefit/eat-common"
import { ProductMenu } from "@curefit/eat-common"
import { Session } from "@curefit/userinfo-common"
import { Vertical, UserAgentType as UserAgent } from "@curefit/base-common"
import { FoodPackBooking } from "@curefit/shipment-common"
import { IShipmentService, ALFRED_CLIENT_TYPES } from "@curefit/alfred-client"
import { UrlPathBuilder } from "@curefit/product-common"
import { capitalizeFirstLetter, Timezone } from "@curefit/util-common"
import LocationUtil from "../util/LocationUtil"
import * as _ from "lodash"
import { TimeUtil } from "@curefit/util-common"
import { SlotUtil } from "@curefit/eat-util"
import IProductBusiness from "../product/IProductBusiness"
import IUserBusiness from "../user/IUserBusiness"
import { PreferredLocation } from "@curefit/userinfo-common"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import * as momentTz from "moment-timezone"
import { ADDRESS_APP_VERSION } from "@curefit/base-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import { MAX_CART_SIZE, MealUtil as EtherMealUtil } from "@curefit/base-utils"
import MealUtil from "../util/MealUtil"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID, DEFAULT_TICKET_SIZE } from "../util/EatUtil"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import { EatLocationUtil } from "@curefit/eat"
import { OfferUtil } from "@curefit/base-utils"
import { IBannerService, BannerQueryCondition, VM_MODELS_TYPES } from "@curefit/vm-models"
import { ICerberusService, RecommendedMeals } from "@curefit/vm-models"
import { eternalPromise } from "@curefit/util-common"
import { EAT_BANNER_RATIO_MAP } from "../util/AppUtil"
import { EatOfferRequestParams, EatOfferRequestParamsV3, FoodPackOffersResponse, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import UserAddressView from "../user/UserAddressView"
import { OfferV2 } from "@curefit/offer-common"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { MASTERCHEF_MODELS_TYPES } from "@curefit/masterchef-models"
import { EatUserType, SEO } from "@curefit/vm-common"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { getEatUserType } from "@curefit/vm-models"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { IFulfilmentService } from "@curefit/alfred-client"
import { UserContext } from "@curefit/userinfo-common"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import { CacheHelper } from "../util/CacheHelper"
import { FoodCategory } from "@curefit/eat-common"
import { EAT_TYPES, IFoodCategoryService } from "@curefit/eat"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import AppUtil from "../util/AppUtil"
import { FoodFulfilment } from "@curefit/order-common"

export const PLACEHOLDER_PRICE = 4000

@injectable()
class EatLandingPageBuilderV3 implements IPageBuilder {
    constructor(@inject(CUREFIT_API_TYPES.EatLandingPageConfig) protected eatLandingPageConfig: EatLandingPageConfig,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CAESAR_CLIENT_TYPES.MenuService) protected menuService: IMenuService,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) protected deliveryAreaService: IDeliveryAreaService,
        @inject(CUREFIT_API_TYPES.CerberusServiceV2) protected cerberusService: ICerberusService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceEther) protected offerService: IOfferService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.UserBusiness) protected userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.SessionService) protected sessionBusiness: ISessionService,
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) protected capacityService: ICapacityService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(DELIVERY_CLIENT_TYPES.GateService) protected gateService: IGateService,
        @inject(CUREFIT_API_TYPES.BannerService) protected bannerService: IBannerService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) public activityStoreDao: IActivityStoreReadonlyDao,
        @inject(EAT_TYPES.FoodCategoryService) protected foodCategoryService: IFoodCategoryService,
    ) {

    }

    async build(userContext: UserContext, session: Session, lat: number, lon: number, appVersion?: number, os?: string, codePushVersion?: number, selectedTab?: number, isOnlySingles?: boolean): Promise<Page> {
        const isWithinBangalore: boolean = LocationUtil.isWithinBangalore(lat, lon)
        const userId: string = session.userId
        const deviceId: string = session.deviceId
        const userAgent: UserAgent = session.userAgent
        const preferredLocation = await this.userBusiness.getPreferredLocation(userContext, session.userId, session.sessionData, lon, lat, undefined, true)
        const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId

        const deliveryArea: DeliveryArea = await this.deliveryAreaService.getDeliveryArea(areaId)
        const areaTz = this.deliveryAreaService.getTimeZoneForArea(deliveryArea)
        const preferredLocationEatNow = await this.userBusiness.getPreferredLocation(userContext, session.userId, session.sessionData, lon, lat, SlotUtil.getCurrentMealSlotExact(areaTz), false)
        let packOffersPromise
        if (this.showPackInfo()) {
            packOffersPromise = eternalPromise(this.getPackOffers(userId, userContext, areaId, deviceId))
        }
        const singleOffersPromise = eternalPromise(this.getSingleOffers(userContext, userId, areaId, deviceId, TimeUtil.getDays(areaTz, 3)))

        // Auto detecting and storing areas we are already delivering.
        if (!session.sessionData.locationPreferenceData && appVersion >= ADDRESS_APP_VERSION) {
            if (preferredLocation.placeData) {
                session.sessionData.locationPreferenceData = {}
                session.sessionData.locationPreferenceData.placeId = preferredLocation.placeData.placeId
                session.sessionData.locationPreferenceData.placeName = preferredLocation.placeData.name
                session.sessionData.locationPreferenceData.placeAddress = preferredLocation.placeData.address
                session.sessionData.locationPreferenceData.latLong = preferredLocation.latLong
                // Fire and Forget
                this.sessionBusiness.updateSessionData(session.at, session.sessionData)
            }
        }

        // initialise section and list to be sent
        const availableMealSlotsPromise = this.deliveryAreaService.getMealSlotsForArea(areaId)
        let availableMealSlots = await availableMealSlotsPromise
        let mealSlotArray: EatFitMealSlot[] = this.getMealSlotArrays(availableMealSlots)
        let selectedSlotIndex: number = selectedTab >= 0 ? selectedTab : EatUtil.getSelectedMealSlotIndex(availableMealSlots, preferredLocation.deliveryChannel, areaTz)
        const currentMealSlot = availableMealSlots[selectedSlotIndex]
        if (deliveryArea.scheduledCartDisabled) {
            availableMealSlots = availableMealSlots.filter((mealSlot) => {
                return mealSlot === currentMealSlot
            })
            mealSlotArray = mealSlotArray.filter((mealSlotObj) => {
                return mealSlotObj.id === currentMealSlot
            })
            selectedSlotIndex = 0
        }
        const widgetMap: { [key: string]: PageWidget[] } = this.getMapOfWidgets(mealSlotArray)
        // Get the city details
        const city = session.sessionData.cityId ? await this.cityService.getCityById(session.sessionData.cityId) : undefined
        const cityId = city ? city.cityId : undefined

        // Promise 2: Active pack promises
        const activePackPromises: Promise<PackProgressActionCard & PageWidget>[] = []
        const breakfastWidgetPromises: Promise<PageWidget>[] = []
        const lunchWidgetPromises: Promise<PageWidget>[] = []
        const snacksWidgetPromises: Promise<PageWidget>[] = []
        const dinnerWidgetPromises: Promise<PageWidget>[] = []

        // Promise 1: Building  Active packs widget promises
        if (this.showPackInfo()) {
            const activePacks: any[] = []
            const hasActivePacks: boolean = !_.isEmpty(activePacks) ? true : false
            if (hasActivePacks) {
                if (this.isNewPackProgressWidget()) {
                    const packsbyMealSlot = _.groupBy(activePacks, activePack => {
                        const deliverySlot = SlotUtil.getMealSlotForSlotId(activePack.packSlot.slotId, areaTz)
                        return deliverySlot
                    })
                    Object.keys(packsbyMealSlot).forEach(mealSlot => {
                        if (availableMealSlots.indexOf("BREAKFAST") >= 0 && mealSlot === "BREAKFAST") {
                            breakfastWidgetPromises.push(this.packProgressListWidget(userContext, userAgent, packsbyMealSlot[mealSlot]))
                        } else if (availableMealSlots.indexOf("LUNCH") >= 0 && mealSlot === "LUNCH") {
                            lunchWidgetPromises.push(this.packProgressListWidget(userContext, userAgent, packsbyMealSlot[mealSlot]))
                        } else if (availableMealSlots.indexOf("SNACKS") >= 0 && mealSlot === "SNACKS") {
                            snacksWidgetPromises.push(this.packProgressListWidget(userContext, userAgent, packsbyMealSlot[mealSlot]))
                        } else if (availableMealSlots.indexOf("DINNER") >= 0 && mealSlot === "DINNER") {
                            dinnerWidgetPromises.push(this.packProgressListWidget(userContext, userAgent, packsbyMealSlot[mealSlot]))
                        }
                    })
                } else {
                    activePacks.forEach(activePack => {
                        activePackPromises.push(this.getPackProgressActionCard(userAgent, activePack, userContext))
                    })
                }
            }
        }

        // Promise 2: Building packs widget , rain mode , free delivery call out
        const packOffersResult = await packOffersPromise
        let packOffersResponse: FoodPackOffersResponseV2
        if (this.showPackInfo()) {
            if (packOffersResult && packOffersResult.obj) {
                packOffersResponse = packOffersResult.obj
            } else {
                this.logger.error("Error while fetching pack offers " + packOffersResult.err)
            }
        }

        const isKiosk = preferredLocation.deliveryChannel === "KIOSK" || preferredLocation.deliveryChannel === "CAFE"
        //  BREAKFAST tab promises
        if (availableMealSlots.indexOf("BREAKFAST") >= 0) {
            const delayMode = this.getDelayMode("BREAKFAST", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            if (stopOrders) {
                breakfastWidgetPromises.push(Promise.resolve(new OfferWidget(delayMode.reason.startsWith("OPS") ? this.eatLandingPageConfig.crewBusy : this.eatLandingPageConfig.badWeather)))
            } else if (this.eatLandingPageConfig.breakfastHappyHours && !isKiosk)
                breakfastWidgetPromises.push(Promise.resolve(new OfferWidget(this.eatLandingPageConfig.breakfastHappyHours)))
            if (this.showPackInfo())
                breakfastWidgetPromises.push(this.getEatFitPacksWidgetWithMealSlot(userContext, "BREAKFAST", preferredLocation, packOffersResponse))
        }

        // LUNCH tab promises
        if (availableMealSlots.indexOf("LUNCH") >= 0) {
            const delayMode = this.getDelayMode("LUNCH", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            if (stopOrders) {
                lunchWidgetPromises.push(Promise.resolve(new OfferWidget(delayMode.reason.startsWith("OPS") ? this.eatLandingPageConfig.crewBusy : this.eatLandingPageConfig.badWeather)))
            } else if (this.eatLandingPageConfig.lunchHappyHours && !isKiosk)
                lunchWidgetPromises.push(Promise.resolve(new OfferWidget(this.eatLandingPageConfig.lunchHappyHours)))
            if (this.showPackInfo())
                lunchWidgetPromises.push(this.getEatFitPacksWidgetWithMealSlot(userContext, "LUNCH", preferredLocation, packOffersResponse))
        }

        // SNACKS tab promises
        if (availableMealSlots.indexOf("SNACKS") >= 0) {
            const delayMode = this.getDelayMode("SNACKS", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            if (stopOrders) {
                snacksWidgetPromises.push(Promise.resolve(new OfferWidget(delayMode.reason.startsWith("OPS") ? this.eatLandingPageConfig.crewBusy : this.eatLandingPageConfig.badWeather)))
            } else if (this.eatLandingPageConfig.snacksHappyHours && !isKiosk)
                snacksWidgetPromises.push(Promise.resolve(new OfferWidget(this.eatLandingPageConfig.snacksHappyHours)))
            if (this.showPackInfo())
                snacksWidgetPromises.push(this.getEatFitPacksWidgetWithMealSlot(userContext, "SNACKS", preferredLocation, packOffersResponse))
        }

        // DINNER tab promises
        if (availableMealSlots.indexOf("DINNER") >= 0) {
            const delayMode = this.getDelayMode("DINNER", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            if (stopOrders) {
                dinnerWidgetPromises.push(Promise.resolve(new OfferWidget(delayMode.reason.startsWith("OPS") ? this.eatLandingPageConfig.crewBusy : this.eatLandingPageConfig.badWeather)))
            } else if (this.eatLandingPageConfig.dinnerHappyHours && !isKiosk)
                dinnerWidgetPromises.push(Promise.resolve(new OfferWidget(this.eatLandingPageConfig.dinnerHappyHours)))
            if (this.showPackInfo())
                dinnerWidgetPromises.push(this.getEatFitPacksWidgetWithMealSlot(userContext, "DINNER", preferredLocation, packOffersResponse))
        }



        // get singles availability for meals and building single meal widgets
        const curMealSlot = SlotUtil.getCurrentMealSlotExact(areaTz)
        const breakfastAvailabilityPromise = availableMealSlots.indexOf("BREAKFAST") >= 0 ? this.productBusiness.getNextAvailableMenu(userContext, "BREAKFAST", curMealSlot === "BREAKFAST" ? preferredLocationEatNow : preferredLocation, true) : undefined
        const lunchAvailabilityPromise = availableMealSlots.indexOf("LUNCH") >= 0 ? this.productBusiness.getNextAvailableMenu(userContext, "LUNCH", curMealSlot === "LUNCH" ? preferredLocationEatNow : preferredLocation, true) : undefined
        const snackAvailabilityPromise = availableMealSlots.indexOf("SNACKS") >= 0 ? this.productBusiness.getNextAvailableMenu(userContext, "SNACKS", curMealSlot === "SNACKS" ? preferredLocationEatNow : preferredLocation, true) : undefined
        const dinnerAvailabilityPromise = availableMealSlots.indexOf("DINNER") >= 0 ? this.productBusiness.getNextAvailableMenu(userContext, "DINNER", curMealSlot === "DINNER" ? preferredLocationEatNow : preferredLocation, true) : undefined

        const breakfastAvailability: MealAvailability = await breakfastAvailabilityPromise
        const lunchAvailability: MealAvailability = await lunchAvailabilityPromise
        const snackAvailability: MealAvailability = await snackAvailabilityPromise
        const dinnerAvailability: MealAvailability = await dinnerAvailabilityPromise

        const mealSlotToAvailability: Map<string, MealAvailability> = new Map<string, MealAvailability>()
        mealSlotToAvailability.set("BREAKFAST", breakfastAvailability)
        mealSlotToAvailability.set("LUNCH", lunchAvailability)
        mealSlotToAvailability.set("SNACKS", snackAvailability)
        mealSlotToAvailability.set("DINNER", dinnerAvailability)

        let todaysRecommendedMealMap
        let tomRecommendedMealMap
        const mealDays: string[] = []
        availableMealSlots.forEach(mealSlot => {
            const availability = mealSlotToAvailability.get(mealSlot)
            mealDays.push(availability.day)
        })
        const days: string[] = _.uniq(mealDays).sort()

        const todaysRecommendedMealsPromise = eternalPromise(this.cerberusService.getRecommendedMeals(session.userId, days[0]))
        let tomRecommendedMealsPromise
        if (days.length > 1) {
            tomRecommendedMealsPromise = eternalPromise(this.cerberusService.getRecommendedMeals(session.userId, days[1]))
        }

        const todaysRecommendedMealsResult = await todaysRecommendedMealsPromise
        todaysRecommendedMealMap = EatUtil.getRecommendedMealMap(todaysRecommendedMealsResult)
        if (!todaysRecommendedMealMap) {
            this.logger.error("Error contacting receommendation service for todaysRecommendedMeals ")
        }

        const tomRecommendedMealsResult = await tomRecommendedMealsPromise
        tomRecommendedMealMap = EatUtil.getRecommendedMealMap(tomRecommendedMealsResult)
        if (!tomRecommendedMealMap) {
            this.logger.error("Error contacting receommendation service for tomRecommendedMeals ")
        }

        const singleOffersResult = await singleOffersPromise
        let singleOffersResponse: FoodSinglePriceOfferResponse
        if (singleOffersResult && singleOffersResult.obj) {
            singleOffersResponse = singleOffersResult.obj
        } else {
            this.logger.error("Error while fetching single offers " + singleOffersResult.err)
        }
        const cartOffers: OfferV2[] = []
        const offerIds = this.getOfferIds(packOffersResponse, singleOffersResponse, cartOffers)
        // const isUpForRenewal = this.isUpForRenewal(activePacks)
        const bannerPromise = this.getBannerWidgetV2(userContext, userAgent, userId, cityId, areaId, areaTz, offerIds, undefined, undefined)

        // BREAKFAST single meal widget promise
        if (availableMealSlots.indexOf("BREAKFAST") >= 0) {
            const delayMode = this.getDelayMode("BREAKFAST", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            await this.addEatFitMealsWidget(userContext, preferredLocation, breakfastWidgetPromises, "BREAKFAST", breakfastAvailability, singleOffersResponse,
                breakfastAvailability.day === days[0] ? todaysRecommendedMealMap : tomRecommendedMealMap, session, areaTz, stopOrders)
        }

        // LUNCH single meal widget promise
        if (availableMealSlots.indexOf("LUNCH") >= 0) {
            const delayMode = this.getDelayMode("LUNCH", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            await this.addEatFitMealsWidget(userContext, preferredLocation, lunchWidgetPromises, "LUNCH", lunchAvailability, singleOffersResponse,
                lunchAvailability.day === days[0] ? todaysRecommendedMealMap : tomRecommendedMealMap, session, areaTz, stopOrders)
        }

        // SNACKS single meal widget promise
        if (availableMealSlots.indexOf("SNACKS") >= 0) {
            const delayMode = this.getDelayMode("SNACKS", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            await this.addEatFitMealsWidget(userContext, preferredLocation, snacksWidgetPromises, "SNACKS", snackAvailability, singleOffersResponse,
                snackAvailability.day === days[0] ? todaysRecommendedMealMap : tomRecommendedMealMap, session, areaTz, stopOrders)
        }

        // DINNER single meal widget promise
        if (availableMealSlots.indexOf("DINNER") >= 0) {
            const delayMode = this.getDelayMode("DINNER", deliveryArea)
            const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
            await this.addEatFitMealsWidget(userContext, preferredLocation, dinnerWidgetPromises, "DINNER", dinnerAvailability, singleOffersResponse,
                dinnerAvailability.day === days[0] ? todaysRecommendedMealMap : tomRecommendedMealMap, session, areaTz, stopOrders)
        }

        // Promise 7: recipe tab promises
        // const recipeWidgetPromises: Promise<PageWidget>[] = []
        // recipeWidgetPromises.push(this.getRecipesGridWidget())

        // Await Promise 1
        const banner: BannerWidget = await bannerPromise

        // Await Promise 2
        if (!this.isNewPackProgressWidget() && this.showPackInfo()) {
            const packProgressWidgets: (PackProgressActionCard & PageWidget)[] = await Promise.all(activePackPromises)
            this.addPackProgressAccordingToMealSlot(widgetMap, packProgressWidgets, availableMealSlots)
        }

        // Await all tab promises
        const breakfastWidgets: PageWidget[] = await Promise.all(breakfastWidgetPromises)
        const lunchWidgets: PageWidget[] = await Promise.all(lunchWidgetPromises)
        const snacksWidgets: PageWidget[] = await Promise.all(snacksWidgetPromises)
        const dinnerWidgets: PageWidget[] = await Promise.all(dinnerWidgetPromises)
        // const recipeWidgets: PageWidget[] = await Promise.all(recipeWidgetPromises)
        if (availableMealSlots.indexOf("BREAKFAST") >= 0)
            this.addWidgetsToWidgetMap(widgetMap, breakfastWidgets, "BREAKFAST", isOnlySingles)
        if (availableMealSlots.indexOf("LUNCH") >= 0)
            this.addWidgetsToWidgetMap(widgetMap, lunchWidgets, "LUNCH", isOnlySingles)
        if (availableMealSlots.indexOf("SNACKS") >= 0)
            this.addWidgetsToWidgetMap(widgetMap, snacksWidgets, "SNACKS", isOnlySingles)
        if (availableMealSlots.indexOf("DINNER") >= 0)
            this.addWidgetsToWidgetMap(widgetMap, dinnerWidgets, "DINNER", isOnlySingles)
        // if (this.isRecipeWidgetNeeded())
        //     this.addWidgetsToWidgetMap(widgetMap, recipeWidgets, "RECIPES")
        const cartOfferResponse: CartOffer[] = EatUtil.cartOffersForUpsell(cartOffers)
        const page: EatClpPageV2 = {
            userLocation: isWithinBangalore ? "bangalore" : "others",
            cityName: city ? city.name : undefined,
            header: isOnlySingles ? undefined : banner,
            sections: mealSlotArray,
            lists: widgetMap,
            selectedTabIndex: selectedSlotIndex,
            footer: this.getFooterWidget(),
            cartOffers: cartOfferResponse
        }

        if (appVersion >= ADDRESS_APP_VERSION)
            this.addPreferredLocation(preferredLocation, page)
        return page
    }

    pruneWidgetsForSinglesPage(widgets: WidgetView[]) {
        return widgets.filter((widget) => {
            return widget.widgetType !== "PACK_PROGRESS_WIDGET" && widget.widgetType !== "MAGAZINE_LIST_WIDGET"
        })
    }

    getDelayMode(mealSlot: MealSlot, deliveryArea: DeliveryArea): { mealSlot: MealSlot, mode: DelayMode, reason: DelayModeReason, revertDrivingScaleFactor?: number } {
        if (deliveryArea.delayMode) {
            return deliveryArea.delayMode.find((delayMode) => {
                return delayMode.mealSlot === mealSlot
            })
        }
    }

    async getPackOffers(userId: string, userContext: UserContext, areaId: string, deviceId: string): Promise<FoodPackOffersResponseV2> {
        return {} as FoodPackOffersResponseV2
    }

    async getSingleOffers(userContext: UserContext, userId: string, areaId: string, deviceId: string, dates: string[]): Promise<FoodSinglePriceOfferResponse> {
        const eatOfferRequestParams: EatOfferRequestParamsV3 = {
            areaId: areaId,
            userId: userId,
            cityId: userContext.userProfile.cityId,
            deviceId: deviceId,
            dateMealSlotMap: await this.menuService.getDateMealSlotsMap(areaId),
            source: AppUtil.callSourceFromContext(userContext)
        }
        return {} as FoodSinglePriceOfferResponse
    }

    addPreferredLocation(preferredLocation: PreferredLocation, page: EatClpPage) {
        if (preferredLocation.area) {
            // page.area = new DeliveryAreaView(preferredLocation.area)
            page.placeData = {
                placeId: "DUMMY",
                name: preferredLocation.area.subArea,
                address: ""
            }
        }
        if (preferredLocation.address) {
            page.address = new UserAddressView(preferredLocation.address)
        }
        if (preferredLocation.placeData) {
            page.placeData = preferredLocation.placeData
        }
        if (preferredLocation.city) {
            page.placeData = {
                placeId: "DUMMY",
                name: preferredLocation.city.name,
                address: ""
            }
        }
    }

    async getPackProgressActionCard(userAgent: UserAgent, foodPackBooking: FoodPackBooking, userContext: UserContext): Promise<PackProgressActionCard & PageWidget> {
        const foodPack = await this.catalogueService.getFoodPack(foodPackBooking.packId)
        let expiryMessage: string = undefined
        const bookingTz = foodPackBooking.timezone
        if (EtherMealUtil.isRenewAllowed(foodPackBooking)) {
            if (foodPackBooking.subscriptionType === undefined) {
                expiryMessage = foodPackBooking.schedule.ticketsLeft < 1 ? "Pack expiring today!" : "Pack expiring soon!"
            } else if (!foodPackBooking.autoRenewSupported && foodPackBooking.packState === "ACTIVE") {
                expiryMessage = "Subscription ends " + momentTz.tz(foodPackBooking.packEndDate, bookingTz).calendar(null, {
                    sameDay: "[today!]",
                    nextDay: "[tomorrow!]",
                    nextWeek: "[soon!]",
                    sameElse: "[soon!]"
                })
            }
        }
        foodPackBooking.packState = foodPackBooking.packState === "PAUSED" || foodPackBooking.pauseWindow && foodPackBooking.pauseWindow.start.getMilliseconds > new Date().getMilliseconds ? "PAUSED" : foodPackBooking.packState
        let icon: ManageOptionIcon = "RESUME"
        let cta = "Details"
        if (foodPackBooking.packState === "PAUSED") {
            if (foodPackBooking.subscriptionType === undefined) {
                icon = "PAUSE"
                cta = "Resume"
            } else {
                icon = "CANCEL"
                cta = "Undo Cancel"
            }
        } else if (expiryMessage) {
            if (foodPackBooking.packState === "CANCELLED") {
                cta = "Restart"
            } else if (!foodPackBooking.autoRenewSupported) {
                cta = "Renew"
            }
        } else if (foodPackBooking.packState === "HALTED") {
            icon = "PAUSE"
        } else if (foodPackBooking.packState === "PENDING") {
            icon = "PAUSE"
        }
        let subTitle = undefined
        if (expiryMessage) {
        } else if (foodPackBooking.subscriptionType === undefined) {
            subTitle = `${foodPackBooking.schedule.ticketsTotal - foodPackBooking.schedule.ticketsLeft} of ${foodPackBooking.schedule.ticketsTotal} meals served`
        } else if (foodPackBooking.packState === "HALTED") {
            subTitle = "Subscription Halted"
        } else if (foodPackBooking.packState === "PENDING") {
            subTitle = "Payment Pending"
        } else if (foodPackBooking.packState === "PAUSED") {
            subTitle = TimeUtil.formatDateInTimeZone(bookingTz, foodPackBooking.pauseWindow.start, "Do MMM") + " - " + TimeUtil.formatDateInTimeZone(bookingTz, foodPackBooking.pauseWindow.end, "Do MMM")
        } else {
            subTitle = (foodPackBooking.autoRenewSupported ? "Renews " : "Ends ") + TimeUtil.getDayText(foodPackBooking.packEndDate, bookingTz, {
                sameDay: "[today]",
                nextDay: "[tomorrow]",
                nextWeek: "[on] Do MMM",
                sameElse: "[on] Do MMM"
            })
        }
        let progressComplete: number = foodPackBooking.schedule.ticketsTotal - foodPackBooking.schedule.ticketsLeft
        if (foodPackBooking.subscriptionType !== undefined && momentTz.tz(foodPackBooking.packStartDate, bookingTz).isSameOrBefore(TimeUtil.todaysDateWithTimezone(bookingTz))) {
            progressComplete = foodPackBooking.weekendsEnabled ? TimeUtil.getMomentNow(bookingTz).diff(momentTz.tz(foodPackBooking.packStartDate, bookingTz), "days") : TimeUtil.weekDayDiffInDays(bookingTz, foodPackBooking.packStartDate, TimeUtil.todaysDateWithTimezone(bookingTz))
            progressComplete += SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDateWithTimezone(bookingTz), bookingTz, foodPackBooking.packSlot.slotId) ? 1 : 0
        }
        const seoParams: SeoUrlParams = {
            productName: foodPack.title
        }
        const packProgressWidget: PackProgressActionCard & PageWidget = {
            productType: "FOOD",
            widgetType: "PACK_PROGRESS_WIDGET",
            hasTopPadding: true,
            hasBottomPadding: false,
            title: foodPack.title,
            subTitle: subTitle,
            action: ActionUtil.foodPack(foodPackBooking.packId, foodPackBooking.fulfilmentId, undefined, undefined, userAgent, seoParams),
            packId: foodPackBooking.packId,
            fulfilmentId: foodPackBooking.fulfilmentId,
            state: foodPackBooking.packState,
            resumeDate: TimeUtil.todaysDate(bookingTz),
            resumeSlotId: foodPackBooking.packSlot.slotId,
            image: UrlPathBuilder.getPackImagePath(foodPackBooking.packId, "FOOD", "MAGAZINE", foodPack.imageVersion, userAgent),
            total: foodPackBooking.schedule.ticketsTotal,
            completed: progressComplete,
            mealSlot: foodPack.mealSlot,
            expiryMessage: expiryMessage,
            icon: icon,
            cta: cta
        }

        return packProgressWidget
    }


    protected getFooterWidget(): PageWidget {
        return undefined
    }
    protected isRecipeWidgetNeeded(): boolean {
        return false
    }

    protected isNewPackProgressWidget(): boolean {
        return false
    }

    private getOfferIds(packOffersResponse: FoodPackOffersResponseV2, singleOfferResponse: FoodSinglePriceOfferResponse, cartOffers: OfferV2[]): string[] {
        const allEatofferIds: string[] = packOffersResponse ? Object.keys(packOffersResponse.offers) : []
        if (!_.isEmpty(cartOffers)) {
            cartOffers.forEach(cartOffer => {
                allEatofferIds.push(cartOffer.offerId)
            })
        }
        if (singleOfferResponse) {
            Object.keys(singleOfferResponse.prices).forEach(date => {
                const offerOnDate = singleOfferResponse.prices[date]
                Object.keys(offerOnDate).forEach(mealSlot => {
                    const offerOnMealSlot = offerOnDate[mealSlot]
                    Object.keys(offerOnMealSlot).forEach(productId => {
                        const offerOnProdut = offerOnMealSlot[productId]
                        _.forEach(offerOnProdut.offerIds, offerId => {
                            allEatofferIds.push(offerId)
                        })
                        _.forEach(offerOnProdut.preBuzzOfferIds, offerId => {
                            allEatofferIds.push(offerId)
                        })
                    })
                })

            })
        }
        return _.uniq(allEatofferIds)
    }

    private isUpForRenewal(activePacks: FoodPackBooking[]) {
        if (_.isEmpty(activePacks)) {
            return false
        }

        const renewalEligiblePacks = activePacks.filter(activePack => {
            if (activePack.schedule.ticketsLeft <= 2)
                return true
            else
                return false
        })

        // This method is not even used by any Class. So Added IST_TIMEZONE. If it used in future add user timezone
        const futurePacks = activePacks.filter(activePack => {
            const packStartDate = TimeUtil.getMomentForDateString(activePack.packStartDate, TimeUtil.IST_TIMEZONE)
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(TimeUtil.IST_TIMEZONE), TimeUtil.IST_TIMEZONE)
            if (packStartDate.isAfter(today)) {
                return true
            }
            return false
        })

        return !_.isEmpty(renewalEligiblePacks) && _.isEmpty(futurePacks) ? true : false
    }

    private async getBannerWidgetV2(userContext: UserContext, userAgent: UserAgent, userId: string, cityId: string, areaId: string, areaTz: Timezone, offerIds: string[], isUpForRenewal: boolean, hasActivePacks: boolean): Promise<BannerWidget> {
        // let eatUserType: EatUserType = "ALL"
        // if (isUpForRenewal) {
        //     eatUserType = "UP_FOR_RENEWAL"
        // } else if (hasActivePacks) {
        //     eatUserType = "WITH_PACK"
        // } else {
        //     eatUserType = "WITHOUT_PACK"
        // }

        const activitiesPromise = this.activityStoreDao.find({
            condition: { "activityType": "EATFIT_MEAL", "userId": userId, "show": true, "score": 1 }
        })
        const activities = await activitiesPromise
        const eatFulfilments: FoodFulfilment[] = []
        const eatUserType: EatUserType = getEatUserType(userContext, activities, eatFulfilments, areaTz)
        const vertical: Vertical = this.getVerticalOverride() || "EAT_FIT"
        const bannerCondition: BannerQueryCondition = {
            bannerVersion: 1,
            status: "LIVE",
            timeInstanceWithTz: { date: new Date(), timezone: TimeUtil.IST_TIMEZONE },
            cityId, areaId, eatUserType, userAgent, offerIds, vertical
        }

        const bannersBasedOnCondition = this.bannerService.getBanners(bannerCondition)
        const banners: Promise<Banner>[] = _.map(bannersBasedOnCondition, async (bannerBasedOnCondition) => {
            let bannerImage
            if (userAgent === "DESKTOP")
                bannerImage = "/" + bannerBasedOnCondition.webImage
            else if (userAgent === "MBROWSER")
                bannerImage = "/" + bannerBasedOnCondition.mWebImage
            else
                bannerImage = "/" + bannerBasedOnCondition.appImage
            const banner: Banner = {
                id: bannerBasedOnCondition.bannerId,
                action: await this.bannerService.getBannerAction(bannerBasedOnCondition, userAgent),
                image: bannerImage
            }
            return banner
        })
        const bannerRatio = EAT_BANNER_RATIO_MAP.get(userAgent)
        const widget: BannerWidget = new BannerWidget((await Promise.all(banners)).slice(0, 5), bannerRatio)
        return Promise.resolve(widget)
    }
    protected getVerticalOverride(): Vertical {
        return undefined
    }

    private async packProgressListWidget(userContext: UserContext, userAgent: UserAgent, activePacks: FoodPackBooking[]): Promise<PackProgressListWidget> {
        const packProgressPromises = _.map(activePacks, async (activePack) => {
            const foodPack = await this.catalogueService.getFoodPack(activePack.packId)
            const packTz = activePack.timezone
            let expiryMessage: string = undefined
            if (EtherMealUtil.isRenewAllowed(activePack)) {
                if (activePack.subscriptionType === undefined) {
                    expiryMessage = activePack.schedule.ticketsLeft < 1 ? "Pack expiring today!" : "Pack expiring soon!"
                } else if (!activePack.autoRenewSupported && activePack.packState === "ACTIVE") {
                    expiryMessage = "Subscription ends " + momentTz.tz(activePack.packEndDate, packTz).calendar(null, {
                        sameDay: "[today!]",
                        nextDay: "[tomorrow!]",
                        nextWeek: "[soon!]",
                        sameElse: "[soon!]"
                    })
                }
            }
            activePack.packState = activePack.packState === "PAUSED" || activePack.pauseWindow && activePack.pauseWindow.start.getMilliseconds > new Date().getMilliseconds ? "PAUSED" : activePack.packState
            let subTitle = undefined
            if (expiryMessage) {
            } else if (activePack.subscriptionType === undefined) {
                subTitle = `${activePack.schedule.ticketsTotal - activePack.schedule.ticketsLeft} of ${activePack.schedule.ticketsTotal} meals served`
            } else if (activePack.packState === "HALTED") {
                subTitle = "Subscription Halted"
            } else if (activePack.packState === "PENDING") {
                subTitle = "Payment Pending"
            } else if (activePack.packState === "PAUSED") {
                subTitle = TimeUtil.formatDateInTimeZone(packTz, activePack.pauseWindow.start, "Do MMM") + " - " + TimeUtil.formatDateInTimeZone(packTz, activePack.pauseWindow.end, "Do MMM")
            } else {
                subTitle = (activePack.autoRenewSupported ? "Renews " : "Ends ") + TimeUtil.getDayText(activePack.packEndDate, packTz, {
                    sameDay: "[today]",
                    nextDay: "[tomorrow]",
                    nextWeek: "[on] Do MMM",
                    sameElse: "[on] Do MMM"
                })
            }
            let progressComplete: number = activePack.schedule.ticketsTotal - activePack.schedule.ticketsLeft
            if (activePack.subscriptionType !== undefined && momentTz.tz(activePack.packStartDate, packTz).isSameOrBefore(TimeUtil.todaysDateWithTimezone(packTz))) {
                progressComplete = activePack.weekendsEnabled ? TimeUtil.getMomentNow(packTz).diff(momentTz.tz(activePack.packStartDate, packTz), "days") : TimeUtil.weekDayDiffInDays(packTz, activePack.packStartDate, TimeUtil.todaysDateWithTimezone(packTz))
                progressComplete += SlotUtil.isCancelCutOffPassed(TimeUtil.todaysDateWithTimezone(packTz), packTz, activePack.packSlot.slotId) ? 1 : 0
            }
            const seoParams: SeoUrlParams = {
                productName: foodPack.title
            }
            const packProgressView: PackProgress = {
                productType: "FOOD",
                title: foodPack.title,
                subTitle: subTitle,
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.foodPack(activePack.packId, activePack.fulfilmentId, undefined, undefined, userAgent, seoParams)
                },
                state: activePack.packState,
                image: UrlPathBuilder.getPackImagePath(activePack.packId, "FOOD", "MAGAZINE", foodPack.imageVersion,
                    userAgent),
                widgetActions: [],
                total: activePack.schedule.ticketsTotal,
                completed: progressComplete,
                packId: activePack.packId,
                fulfilmentId: activePack.fulfilmentId,
                mealSlot: foodPack.mealSlot,
                expiryMessage: expiryMessage
            }

            if (activePack.packState === "HALTED") {
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.foodPack(activePack.packId, activePack.fulfilmentId, undefined, undefined, userAgent, seoParams),
                    title: "Restart Subscription",
                    viewType: "SECONDARY_BUTTON"
                }
                packProgressView.widgetActions.push(action)
            } else if (EtherMealUtil.isRenewAllowed(activePack) && !activePack.autoRenewSupported) {
                const action: Action = {
                    actionType: "RENEW_MEAL_PACK_V2",
                    title: activePack.subscriptionType === undefined ? "Renew Pack" : "Renew Subscription",
                    viewType: "SECONDARY_BUTTON",
                    url: "curefit://payment",
                    meta: {
                        orderId: activePack.orderId,
                        fulfilmentId: activePack.fulfilmentId,
                        productId: activePack.packId
                    }
                }
                packProgressView.widgetActions.push(action)
            } else if (activePack.packState === "PAUSED") {
                const action: Action = {
                    actionType: "RESUME_MEAL_PACK",
                    title: activePack.subscriptionType === undefined ? "Resume pack" : "Undo cancel",
                    viewType: "SECONDARY_BUTTON"
                }
                packProgressView.widgetActions.push(action)
            } else {
                if (activePack.subscriptionType === undefined) {
                    const action: Action = {
                        actionType: "PAUSE_MEAL_PACK",
                        title: "Pause pack",
                        viewType: "SECONDARY_BUTTON"
                    }
                    packProgressView.widgetActions.push(action)
                } else {
                    let nextShipment = activePack.activeShipment
                    if (nextShipment) {
                        if (SlotUtil.isCancelCutOffPassed(nextShipment.deliveryDate, packTz, nextShipment.deliverySlot.slotId))
                            nextShipment = undefined
                    }
                    if (nextShipment === undefined && !_.isEmpty(activePack.futureShipment)) {
                        nextShipment = activePack.futureShipment[0]
                    }
                    if (nextShipment) {
                        const minDate = nextShipment.deliveryDate
                        const maxDate = activePack.subscriptionType === undefined ? momentTz.max(momentTz.tz(activePack.packEndDate, packTz).subtract(activePack.schedule.ticketsLeft, "days"), momentTz.tz(minDate, packTz)) : momentTz.tz(new Date(), packTz).add(activePack.schedule.ticketsTotal, "days")
                        const action: Action = {
                            actionType: "SKIP_MEALS",
                            title: "Cancel Meals",
                            viewType: "SECONDARY_BUTTON",
                            meta: {
                                nextAvailableDate: nextShipment.deliveryDate,
                                difference: await this.productBusiness.getCancelDifference(nextShipment),
                                defaultDate: minDate,
                                defaultStartDate: minDate,
                                deliveryDate: nextShipment.deliveryDate,
                                maximumDate: maxDate.format("YYYY-MM-DD"),
                                minimumDate: minDate
                            }
                        }
                        packProgressView.widgetActions.push(action)
                    }
                }
            }
             // disabling action for deprecating eat fit
            if (packProgressView?.widgetActions) {
                packProgressView.widgetActions = []
            }

            return packProgressView
        })
        const packProgress: PackProgress[] = await Promise.all(packProgressPromises)
        const packProgressWidget: PackProgressListWidget = {
            widgetType: "PACK_PROGRESS_LIST_WIDGET",
            items: packProgress,
            hasBottomPadding: false,
            hasTopPadding: false
        }
        return packProgressWidget
    }

    private addWidgetsToWidgetMap(widgetMap: { [key: string]: PageWidget[] }, widgets: PageWidget[], tabType: string, isOnlySingles: boolean) {
        if (widgets.length > 0) {
            widgets.forEach(widget => {
                widgetMap[tabType].push(widget)
            })
        }
        if (isOnlySingles)
            widgetMap[tabType] = this.pruneWidgetsForSinglesPage(widgetMap[tabType])
    }

    private getMealSlotArrays(availableMealSlots: MealSlot[]) {
        const mealSlots: EatFitMealSlot[] = []
        availableMealSlots.forEach(availableMealSlot => {
            mealSlots.push({
                id: availableMealSlot, mealName: capitalizeFirstLetter(availableMealSlot.toLowerCase()),
                name: capitalizeFirstLetter(availableMealSlot.toLowerCase())
            })
        })

        if (this.isRecipeWidgetNeeded())
            mealSlots.push({ id: "RECIPES", mealName: "Recipes", name: "Recipes" })

        return mealSlots
    }

    private getMapOfWidgets(mealSlotArray: EatFitMealSlot[]) {
        const widgetMap: { [key: string]: PageWidget[] } = {}

        mealSlotArray.forEach(mealSlot => {
            widgetMap[mealSlot.id] = []
        })

        return widgetMap
    }


    async getEatFitPacksWidgetWithMealSlot(
        userContext: UserContext,
        mealSlot: MealSlot,
        preferredLocation: PreferredLocation,
        packOffersResponse: FoodPackOffersResponseV2
    ): Promise<MagazineListWidget> {
        const mealPacksPromise: Promise<FoodPack[]> = this.catalogueService.getFoodPacksByMealSlot(mealSlot, preferredLocation.deliveryChannel, "PRIMARY")
        const preferredLocationAttributes = EatLocationUtil.preferredLocationAttributes(preferredLocation)
        const inventoryResult = await this.capacityService.getInventoryForPacksBasedOnMenuAlone(preferredLocationAttributes.areaId, mealSlot)
        const packAvailabilityMap = new Map<string, boolean>()
        Object.keys(inventoryResult).forEach(day => {
            const inventoryForDay = inventoryResult[day]
            Object.keys(inventoryForDay).forEach(packId => {
                const inventoryForPack = inventoryForDay[packId]
                if (inventoryForPack.total > 0) {
                    packAvailabilityMap.set(packId, true)
                }
            })
        })
        const city = await this.cityService.getCityById(userContext.userProfile.cityId)
        return mealPacksPromise.then(mealPacks => {
            mealPacks.sort((a, b) => {
                return a.ordering < b.ordering ? -1 : 1
            })
            let isOfferAvailable = false
            const topPickActionCards: ActionCard[] = []
            mealPacks.forEach(mealPack => {
                const foodPackOption: FoodPackOption = EtherMealUtil.findDefaultSubscriptionOption(mealPack, packOffersResponse)
                const result = OfferUtil.getFoodPackOfferAndPrice(mealPack, foodPackOption, packOffersResponse)
                if (mealPack && mealPack.status === "LIVE") {
                    if (result.price.listingPrice < result.price.mrp) {
                        isOfferAvailable = true
                    }
                    if (foodPackOption.subscriptionType !== undefined) {
                        result.price.listingPrice = Math.round(result.price.listingPrice / foodPackOption.numTickets)
                        result.price.mrp = Math.round(result.price.mrp / foodPackOption.numTickets)
                    }
                    const seoParams: SeoUrlParams = {
                        productName: mealPack.title,
                        city: city ? city.name : undefined
                    }
                    if (packAvailabilityMap.get(mealPack.productId)) {
                        topPickActionCards.push({
                            title: mealPack.title,
                            price: result.price,
                            subTitle: foodPackOption.subscriptionType === undefined ? `${DEFAULT_TICKET_SIZE} meals at` : "Starting from",
                            isOfferApplied: !_.isEmpty(result.offers),
                            image: UrlPathBuilder.getPackImagePath(mealPack.productId, "FOOD", "MAGAZINE", mealPack.imageVersion, userContext.sessionInfo.userAgent),
                            action: ActionUtil.foodPack(mealPack.productId, undefined, undefined, true, userContext.sessionInfo.userAgent, seoParams),
                        })
                    }
                }
            })
            // const pageWidgetMore: PageWidgetMore = {
            //     text: "See more",
            //     action: "curefit://foodbrowsepage"
            // }
            const pageTitle = this.getEatFitPackWidgetTitle(preferredLocation, mealSlot)

            const widget: MagazineListWidget = new MagazineListWidget(
                topPickActionCards,
                pageTitle,
                isOfferAvailable
            )
            widget.seemore = MealUtil.mealSeeMore(userContext, true, preferredLocation.address ? preferredLocation.address.addressType === "KIOSK" : false)

            return Promise.resolve(widget)
        })
    }

    protected showPackInfo(): boolean {
        return true
    }
    protected async addEatFitMealsWidget(
        userContext: UserContext,
        preferredLocation: PreferredLocation,
        widgetPromises: Promise<PageWidget>[],
        mealSlot: MealSlot,
        menuAvailabilityDetails: {
            day: string, inventoryResult: { [campaignType: string]: { [packId: string]: { left: number, total: number } } }, menus: ProductMenu[]
        },
        singleOfferResponse: FoodSinglePriceOfferResponse,
        recommendedMealMap: _.Dictionary<RecommendedMeals>,
        session: Session,
        areaTz: Timezone,
        disablePurchase?: boolean
    ) {
        const productIds = _.map(menuAvailabilityDetails.menus, menu => menu.productId)
        const productMap = await this.catalogueService.getProductMap(productIds)
        const mealItemPromises = _.map(menuAvailabilityDetails.menus, async (menu) => {
            const product = productMap[menu.productId]
            const offerDetails = OfferUtil.getSingleOfferAndPrice(product, menuAvailabilityDetails.day, singleOfferResponse, mealSlot)
            const availability = OfferUtil.getAvailabilityV1(product, menuAvailabilityDetails.inventoryResult)
            const actions: (Action | MealAction)[] = []
            const city = await this.cityService.getCityById(session.sessionData.cityId)
            const seoParams: SeoUrlParams = {
                productName: product.title
            }
            const actionUrl = ActionUtil.foodSingle(product.productId, menuAvailabilityDetails.day, mealSlot, !disablePurchase, undefined, userContext.sessionInfo.userAgent, seoParams)
            actions.push({
                url: actionUrl,
                actionType: "WIDGET_NAVIGATION"
            })
            const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })
            if (!session.isNotLoggedIn || userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER") {
                if (preferredLocation.isInServicableArea) {
                    actions.push({
                        actionType: "BUY_MEAL",
                        url: "curefit://cartcheckout?listingBrand=EAT_FIT",
                        title: "BUY",
                        listingBrand: "EAT_FIT",
                        date: menuAvailabilityDetails.day,
                        productId: product.productId,
                        productName: product.title,
                        offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                        offerIds: offerIds,
                        price: offerDetails.price,
                        maxCartSize: MAX_CART_SIZE,
                        stock: Math.min(MAX_CART_SIZE, availability.left),
                        mealSlot: {
                            id: mealSlot,
                            name: capitalizeFirstLetter(mealSlot.toLowerCase())
                        }
                    })
                    actions.push({
                        actionType: "ADD_TO_CART",
                        title: "ADD",
                        productName: product.title,
                        date: menuAvailabilityDetails.day,
                        productId: product.productId,
                        offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                        offerIds: offerIds,
                        price: offerDetails.price,
                        maxCartSize: MAX_CART_SIZE,
                        stock: Math.min(MAX_CART_SIZE, availability.left),
                        mealSlot: {
                            id: mealSlot,
                            name: capitalizeFirstLetter(mealSlot.toLowerCase())
                        }
                    })
                } else {
                    actions.push({
                        actionType: "SHOW_ALERT_MODAL",
                        title: "ADD",
                        meta: {
                            title: "Unserviceable Location",
                            subTitle: "We currently do not deliver to this area. Please update your location at the top of the page",
                            actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                        }
                    })
                }
            } else {
                actions.push({
                    actionType: "SHOW_ALERT_MODAL",
                    title: "BUY",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                })
                actions.push({
                    actionType: "SHOW_ALERT_MODAL",
                    title: "ADD",
                    meta: {
                        title: "Login Required!",
                        subTitle: "Please login to continue",
                        actions: [{ actionType: "LOGOUT", title: "Login" }]
                    }
                })
            }
            if (disablePurchase) {
                actions.splice(1, 2)
                actions.push({
                    actionType: "SHOW_ALERT_MODAL",
                    title: "CLOSED",
                    meta: {
                        title: "Ordering closed",
                        subTitle: "We're currently closed for orders. Please check back with us in sometime",
                        actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                    }
                })
            }
            let image
            if (userContext.sessionInfo.userAgent === "DESKTOP") {
                image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "HERO", product.imageVersion)
            } else {
                image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            }
            let parentProduct
            if (!_.isNil(product.parentProductId)) {
                parentProduct = await this.catalogueService.getProduct(product.parentProductId)
            }
            const nutritionInfo = EatUtil.computeNutritionalInfo(product, parentProduct, "WHOLE_FIT")
            const salesTagImage = this.eatLandingPageConfig.salesTagsImages[menu.salesTag]
            image = (salesTagImage ? "/l_" + salesTagImage : "") + image
            const mealItem: MealItem = {
                title: product.title,
                calories: `${nutritionInfo.Calories["Total Calories"]} cal`,
                price: offerDetails.price,
                date: menuAvailabilityDetails.day,
                image: image,
                shipmentWeight: product.shipmentWeight,
                isInventorySet: availability.total > 0,
                stock: Math.min(MAX_CART_SIZE, availability.left),
                actions: actions,
                foodCategoryId: product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID,
                productId: product.productId,
                isVeg: product.attributes["isVeg"] === "TRUE"
            }
            return mealItem
        })

        const mealItems = await Promise.all(mealItemPromises)
        const mealItemsWithInventory = _.filter(mealItems, mealItem => { return mealItem.isInventorySet })
        const categoryIdArray = _.uniq(mealItemsWithInventory.map((mealItem) => {
            return mealItem.foodCategoryId
        }))
        const clpCategories: { [id: string]: FoodCategory } = this.foodCategoryService.getCLPCategories(categoryIdArray)
        const channel = EatUtil.getDeliveryChannelFromLocation(preferredLocation)
        const sortedMealItems = EatUtil.sortMealItems(mealItemsWithInventory, recommendedMealMap, mealSlot, productMap, clpCategories, channel)
        const pageWidgetTitle: PageWidgetTitle = this.getEatFitMealsWidgetTitle(userContext, mealSlot, preferredLocation, menuAvailabilityDetails.day, areaTz)
        this.addMealWidgetPromise(userContext, sortedMealItems, widgetPromises, pageWidgetTitle)
    }

    getEatFitPackWidgetTitle(preferredLocation: PreferredLocation, mealSlot?: MealSlot): PageWidgetTitle {
        let title, subTitle
        const mealName = this.getMealName(mealSlot)
        if (preferredLocation.isWithinServicableCity) {
            if (preferredLocation.isInServicableArea) {
                title = `Subscribe to ${mealName.toLowerCase()}`
                subTitle = `Meal boxes, zero delivery charges`
                if (preferredLocation.address && preferredLocation.address.kioskId) {
                    subTitle = `Meal boxes at your eat.fit kiosk`
                }
            } else {
                title = `${capitalizeFirstLetter(mealName)} subscription`
                subTitle = "Currently not servicing this locality"
            }
        } else {
            title = `${capitalizeFirstLetter(mealName.toLowerCase())} subscription`
            subTitle = "Available only in Bangalore & Delhi NCR"
        }
        return {
            title: title,
            subTitle: subTitle
        }
    }

    protected getEatFitMealsWidgetTitle(userContext: UserContext, mealSlot: MealSlot, preferredLocation: PreferredLocation, mealDate: string, areaTz: Timezone): PageWidgetTitle {
        const meal = this.getMealName(mealSlot).toLowerCase()
        const isKiosk = preferredLocation.deliveryChannel === "KIOSK" || preferredLocation.deliveryChannel === "CAFE"
        let title = `${isKiosk ? "Kiosk" : "Order"} ${meal}`
        let subTitle = "Choose from a selection of delicious meals"
        if (preferredLocation.isWithinServicableCity) {
            if (preferredLocation.isInServicableArea) {
                if (mealDate !== TimeUtil.todaysDateWithTimezone(areaTz)) {
                    const mealText = isKiosk ? "kiosk " + meal : meal
                    const dayText = TimeUtil.getDayText(mealDate, areaTz, {
                        sameDay: `[Today's ${mealText}]`,
                        nextDay: `[Tomorrow's ${mealText}]`,
                        nextWeek: `dddd ['s ${mealText}]`,
                        lastDay: "[Yesterday]",
                        lastWeek: "[Last] dddd",
                        sameElse: "[On] ddd, DD MMM"
                    })
                    title = dayText
                }
            } else {
                subTitle = "Currently not servicing this locality"
            }
        } else {
            subTitle = "Available only in Bangalore & Delhi NCR"
        }
        return {
            title: title,
            subTitle: subTitle
        }
    }

    getMealName(mealslot: MealSlot) {
        switch (mealslot) {
            case "LUNCH":
                return "Lunch"
            case "SNACKS":
                return "Snacks"
            case "DINNER":
                return "Dinner"
            case "BREAKFAST":
                return "Breakfast"
        }
        return "Meal"
    }

    addMealWidgetPromise(userContext: UserContext, mealItems: MealItem[], widgetPromises: Promise<PageWidget>[], pageWidgetTitle: PageWidgetTitle) {
        const mealItemListWidgets: MealListItemWidget[] = []
        mealItems.map((mealItem: MealItem) => {
            mealItemListWidgets.push({
                widgetType: "MEAL_LIST_ITEM_WIDGET",
                hasBottomPadding: false,
                hasTopPadding: false,
                title: mealItem.title,
                calories: mealItem.calories,
                price: mealItem.price,
                date: mealItem.date,
                shipmentWeight: mealItem.shipmentWeight,
                image: mealItem.image,
                stock: mealItem.stock,
                isInventorySet: mealItem.isInventorySet,
                actions: mealItem.actions,
                productId: mealItem.productId,
                isVeg: mealItem.isVeg
            })
        })


        const titleWidget: TitleWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "TITLE_WIDGET",
            title: pageWidgetTitle.title,
            subTitle: pageWidgetTitle.subTitle,
        }
        const action = "curefit://infopage?contentId=whyeatfit&pageType=LIST"
        titleWidget.action = {
            actionType: "NAVIGATION",
            title: "Why eat.fit",
            url: action
        }

        widgetPromises.push(Promise.resolve(titleWidget))
        mealItemListWidgets.forEach((mealListItemWidget: MealListItemWidget) => {
            widgetPromises.push(Promise.resolve(mealListItemWidget))
        })
    }

    addPackProgressAccordingToMealSlot(widgetMap: { [key: string]: PageWidget[] }, packProgressWidgets: (PackProgressActionCard & PageWidget)[], availableMealSlots: MealSlot[]) {
        packProgressWidgets.forEach(packProgressWidget => {
            switch (packProgressWidget.mealSlot) {
                case "BREAKFAST":
                    if (availableMealSlots.indexOf("BREAKFAST") >= 0)
                        widgetMap["BREAKFAST"].push(packProgressWidget)
                    break

                case "SNACKS":
                    if (availableMealSlots.indexOf("SNACKS") >= 0)
                        widgetMap["SNACKS"].push(packProgressWidget)
                    break

                case "LUNCH":
                    if (availableMealSlots.indexOf("LUNCH") >= 0)
                        widgetMap["LUNCH"].push(packProgressWidget)
                    break

                case "DINNER":
                    if (availableMealSlots.indexOf("DINNER") >= 0)
                        widgetMap["DINNER"].push(packProgressWidget)
                    break
            }
        })
    }
}



export default EatLandingPageBuilderV3
