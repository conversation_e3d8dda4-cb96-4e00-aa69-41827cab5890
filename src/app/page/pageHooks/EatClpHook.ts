import { PageHook } from "./PageHook"
import { UserContext } from "@curefit/userinfo-common"
import { Page, TabPage } from "../vm/VMPageBuilder"
import EatUtil, { ATTACH_CATEGORIES } from "../../util/EatUtil"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import { CartOffer, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { SlotUtil } from "@curefit/eat-util"
import { Page as VMPage } from "@curefit/vm-common"
import { injectable, inject } from "inversify"
import * as _ from "lodash"
import { OfferUtil } from "@curefit/base-utils"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { CFUserProfile } from "../vm/CFUserProfile"
import { OfferV2 } from "@curefit/offer-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { TimeUtil } from "@curefit/util-common"
import { WalletBalance } from "@curefit/fitcash-common"
import { GenericError } from "@curefit/error-client"
import { BASE_TYPES, Logger } from "@curefit/base"
import { MenuType } from "@curefit/eat-common"
import { ClientCartOffer } from "../../offer/OfferCommon"
import OffersUtil from "../../util/OffersUtil"
import AppUtil from "../../util/AppUtil"
import { AreaPageSelector, CityPageSelector } from "@curefit/apps-common"
import { OfferConstraint } from "@curefit/offer-common/src/Offer"

@injectable()
export class EatClpHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "eatclp"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        const [deliveryArea, mealSlots]  = await Promise.all([
            userContext.userProfile.deliveryAreaPromise,
            userContext.userProfile.availableMealSlotsPromise
        ])
        const timezone = userContext.userProfile.timezone
        if (!deliveryArea) {
            this.logger.error(`Found undefined delivery area ${userContext.userProfile.areaId}`)
        }
        if (deliveryArea && deliveryArea.kioskType === "CAFE") {
            page = { ...page }
            page.tabs = page.tabs.filter(tab => {
                if (tab.subPageId === "eatordernow") {
                    page.name = "cult.fit cafe"
                    page.icon = undefined
                    return true
                } else if ((_.isNil(queryParams.date) || queryParams.date === TimeUtil.todaysDate(timezone, "YYYY-MM-DD")) && SlotUtil.isServiceable(mealSlots, deliveryArea, timezone).isServiceable) {
                    if (tab.subPageId === "eatnow") {
                        page.name = "cult.fit cafe"
                        page.icon = undefined
                        return true
                    }
                } else if (tab.subPageId === "eatlater") {
                    page.name = "cult.fit cafe"
                    page.icon = undefined
                    return true
                }
                return false
            })
        }
        if (deliveryArea && _.isNil(queryParams.selectedTab)
            && !SlotUtil.isServiceable(mealSlots, deliveryArea, timezone).isServiceable) {
            if (_.isArray(page.tabs)) {
                const hasEatLater = _.findIndex(page.tabs, { "subPageId": "eatlater" })
                if (hasEatLater >= 0) {
                    queryParams["selectedTab"] = "eatlater"
                }
            }
        }
        return Promise.resolve(page)
    }

    protected getOffers(singleOffersResult: FoodSinglePriceOfferResponse, sharedData: any, interfaces: CFServiceInterfaces) {
        const eatCategoryOffers = sharedData ? OfferUtil.getCategoryOffers(sharedData.date, singleOffersResult, sharedData.menuType) : []
        const categoryIds: string[] = _.uniq(_.concat([], ...eatCategoryOffers.map((categoryOffer) => {
            return categoryOffer.constraints.categoryIds
        })))
        const offerClpCategories = interfaces.foodCategoryService.getCLPCategories(categoryIds)
        const filteredCategoryOffers = eatCategoryOffers.filter((categoryOffer) => {
            if (offerClpCategories[categoryOffer.constraints.categoryIds[0]]) {
                const clpCategoryId = offerClpCategories[categoryOffer.constraints.categoryIds[0]].categoryId
                return ATTACH_CATEGORIES.includes(clpCategoryId)
            } else {
                interfaces.logger.error(`Cannot find category ID for offerCLPCategories ${JSON.stringify(offerClpCategories)} categoryOffer ${JSON.stringify(categoryOffer)}`)
                return false
            }
        })
        const eatAddonCategoryOffers = _.map(filteredCategoryOffers, (addonCategoryOffer) => { return { title: addonCategoryOffer.title, description: addonCategoryOffer.description, offerId: addonCategoryOffer.offerId, isCategoryOffer: true } })
        const eatComboOffers = sharedData ? OfferUtil.getComboOffers(sharedData.date, singleOffersResult, sharedData.menuType) : []
        const eatAddonComboOffers = _.map(eatComboOffers, (addonComboOffer) => { return { title: addonComboOffer.title, description: addonComboOffer.description, offerId: addonComboOffer.offerId, mainComboProductIds: addonComboOffer.constraints.comboMainProductIds, offerProducts: addonComboOffer.offerProducts } })
        const eatCategories = sharedData ? _.map(sharedData.clpCategoryArr, (clpCategory) => { return { title: clpCategory.name, categoryId: clpCategory.categoryId } }) : []
        const filteredEatCategories = eatCategories.filter((category) => { return ATTACH_CATEGORIES.includes(category.categoryId) })
        const orderedFilteredEatCategories: {
            title: string,
            categoryId: string
        }[] = []
        _.forEach(ATTACH_CATEGORIES, category => {
            const cat =  _.find(filteredEatCategories, cat => {
                return cat.categoryId === category
            })
            if (!_.isNil(cat)) {
                orderedFilteredEatCategories.push(cat)
            }
        })
        return [...eatAddonCategoryOffers, ...eatAddonComboOffers, ...orderedFilteredEatCategories]
    }


    async createCartOffers(userContext: UserContext, menuType: MenuType): Promise<{
        cartOffers: ClientCartOffer[],
        modalCartOffers: ClientCartOffer[]
    }> {
        let cartOffersResult = (await userContext.userProfile.eatCartOffersPromise) || []
        cartOffersResult = cartOffersResult.filter(
            offer => !menuType || !offer.constraints || _.isEmpty(offer.constraints.mealSlots) || offer.constraints.mealSlots.includes(menuType)
        )
        const userProfile = <CFUserProfile>userContext.userProfile
        const deliveryArea = await userProfile.deliveryAreaPromise
        const isCafe = deliveryArea?.channel === "CAFE"
        if (!isCafe) {
            let modalCartOffers: ClientCartOffer[] = []
            const cartOffers = _.sortBy(EatUtil.cartOffersForUpsell(cartOffersResult), ["constraints.minimumAmount", "priority"])
            const fitCashOffer: OfferV2 = _.maxBy(_.filter(cartOffersResult, (offer: OfferV2) => {
                return offer.addons[0] && offer.addons[0].addonType === "FITCASH"
            }), "priority")
            const freeDeliveryOffer: OfferV2 = _.maxBy(_.filter(cartOffersResult, (offer: OfferV2) => {
                return offer.noDeliveryCharge
            }), "priority")
            if (fitCashOffer || freeDeliveryOffer) {
                let deliveryText = "", fitcashText = "", separator = ""
                let shortText: string
                if (fitCashOffer && freeDeliveryOffer) {
                    deliveryText = "Free delivery"
                    fitcashText = `${fitCashOffer.addons[0].config.fitcashbackPercent}% fitcash`
                    separator = " & "
                    if (fitCashOffer.constraints.minimumAmount || freeDeliveryOffer.constraints.minimumAmount) {
                        if (fitCashOffer.constraints.minimumAmount == freeDeliveryOffer.constraints.minimumAmount) {
                            fitcashText += ` on orders above ${RUPEE_SYMBOL}${freeDeliveryOffer.constraints.minimumAmount}`
                        } else {
                            deliveryText += freeDeliveryOffer.constraints.minimumAmount ? ` above ${RUPEE_SYMBOL}${freeDeliveryOffer.constraints.minimumAmount}` : ""
                            fitcashText += fitCashOffer.constraints.minimumAmount ? ` above ${RUPEE_SYMBOL}${fitCashOffer.constraints.minimumAmount}` : ""
                        }
                    }
                    shortText = `Free delivery & ${fitCashOffer.addons[0].config.fitcashbackPercent}% fitcash`
                } else if (fitCashOffer) {
                    fitcashText = fitCashOffer.addons[0].config.fitcashbackPercent + "% fitcash"
                    fitcashText += fitCashOffer.constraints.minimumAmount ? ` on orders above ${RUPEE_SYMBOL}${fitCashOffer.constraints.minimumAmount}` : ""
                    shortText = `${fitCashOffer.addons[0].config.fitcashbackPercent}% fitcash`
                } else {
                    deliveryText = "Free delivery"
                    deliveryText += freeDeliveryOffer.constraints.minimumAmount ? ` on orders above ${RUPEE_SYMBOL}${freeDeliveryOffer.constraints.minimumAmount}` : ""
                    shortText = "Free delivery"
                }
                if (fitCashOffer) {
                    const predefinedText = OffersUtil.getOfferTextForPreDefinedOffers(fitCashOffer.offerId)
                    if (predefinedText) {
                        fitcashText = shortText = predefinedText
                    }
                }
                modalCartOffers = [
                    fitCashOffer ?
                        {
                            title: fitcashText + separator + deliveryText,
                            constraints: fitCashOffer.constraints,
                            mealSlots: fitCashOffer.constraints.mealSlots ? fitCashOffer.constraints.mealSlots : ALL_MEAL_SLOTS,
                            priority: fitCashOffer.priority,
                            offerId: fitCashOffer.offerId,
                            shortTitle: shortText,
                            isFitClubOffer: true,
                            description: fitcashText + separator + deliveryText,
                        } :
                        {
                            title: fitcashText + separator + deliveryText,
                            constraints: freeDeliveryOffer.constraints,
                            mealSlots: freeDeliveryOffer.constraints.mealSlots ? freeDeliveryOffer.constraints.mealSlots : ALL_MEAL_SLOTS,
                            priority: freeDeliveryOffer.priority,
                            offerId: freeDeliveryOffer.offerId,
                            shortTitle: shortText,
                            isFitClubOffer: true,
                            description: fitcashText + separator + deliveryText,
                        }
                ]
            }
            return {
                cartOffers: cartOffers,
                modalCartOffers: modalCartOffers
            }
        } else {
            return {
                cartOffers: EatUtil.cartOffersForUpsell(cartOffersResult).filter((cartOffer) => {
                    return _.isNil(cartOffer.constraints.paymentChannel) && cartOffer.constraints.minimumAmount > 0
                }),
                modalCartOffers: []
            }
        }
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        let page = <TabPage>await pageView
        const selectedTab = queryParams ? queryParams.selectedTab : undefined
        if (queryParams["outletId"]) {

            let cartOffers: ClientCartOffer[] = []
            try {
                const { cartOfferDetails } = await (userContext.userProfile as CFUserProfile).foodMarketplaceCartOffersPromise
                cartOfferDetails?.forEach( offer => {
                    const { uiLabels: { description, cartLabel, orderSummaryLabel, saleBannerLabel }, offerId, priority = 1, constraints = {} } = offer
                    cartOffers.push({
                        title: cartLabel,
                        shortTitle: cartLabel,
                        description,
                        mealSlots: ["ALL"],
                        offerId,
                        constraints, // maybe revisit
                        priority: priority as number // maybe revisit
                    })
                })
            } catch (e) {
                serviceInterfaces.logger.error("SUPPRESS: Error Fetching FOOMP Cart offers for outletId" + queryParams["outletId"], e)
                cartOffers = []
            }
            // 1. setting the outlet name for foodmp menu listing page
            page.pageData = {
                analyticsData: {
                    outletId: sharedData["outletId"],
                    outletName: sharedData["outletName"],
                    outOfStockCount: sharedData["outOfStockCount"],
                    inStockCount: sharedData["inStockCount"],
                    userId: userContext?.userProfile?.userId,
                    pageId: "menulistpage",
                    listingBrand: "FOOD_MARKETPLACE",
                    latLong: EatUtil.getLatLong(await userContext?.userProfile?.preferredLocationPromise, parseFloat(queryParams["lat"]), parseFloat(queryParams["lon"])),
                    city: userContext?.userProfile?.cityId
                },
                fitcashBalance: undefined,
                cartOffers,
                addonCategories: {
                    EAT_LATER: [],
                    EAT_NOW: [],
                    ORDER_NOW: [],
                    WHOLE_FIT: []
                },
                modalOffers: []
            }
            // 2. Removing other tabs from sections
            page?.sections.filter(s => s.id === "foodmpordernow")
            page = {
                ...page,
                name: sharedData["outletName"],
                icon: undefined,
                pageSelector: {
                    areaName: undefined // removing address display at outlet listing level
                } as AreaPageSelector
            }
        } else {
            let cartOffers: CartOffer[] = []
            let modalCartOffers: ClientCartOffer[] = []
            if (!_.isNil(selectedTab) && selectedTab !== "eatordernow") {
                // client passes only pageId and no selectedTab on the first page call for eat
                cartOffers = []
            } else {
                const menuType = sharedData && sharedData["ORDER_NOW"] && sharedData["ORDER_NOW"].menuType
                const cartOffersResponse = await this.createCartOffers(userContext, menuType)
                // these are the progressive offers that are being used for bottom cart action component
                cartOffers = cartOffersResponse.cartOffers
                // these are being used for showing offers (combined or not) on the eatclp addon modal
                modalCartOffers = cartOffersResponse.modalCartOffers
            }
            const eatNowData = sharedData["EAT_NOW"]
            const eatLaterData = sharedData["EAT_LATER"]
            const orderNowData = sharedData["ORDER_NOW"]
            // const wholeFitData = sharedData["WHOLE_FIT"]
            let eatNowAddonCategories = undefined
            const singleOffersResult = await userContext.userProfile.eatSingleOffersPromise
            if (eatNowData) {
                eatNowAddonCategories = this.getOffers(singleOffersResult, eatNowData, serviceInterfaces)
            }
            let eatLaterAddonCategories = undefined
            if (eatLaterData) {
                eatLaterAddonCategories = this.getOffers(singleOffersResult, eatLaterData, serviceInterfaces)
            }
            let orderNowAddonCategories = undefined
            if (orderNowData) {
                orderNowAddonCategories = this.getOffers(singleOffersResult, orderNowData, serviceInterfaces)
            }
            let fitcashBalance: number
            try {
                const fitcashBalanceResponse: WalletBalance = await serviceInterfaces.fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)
                fitcashBalance = fitcashBalanceResponse.balance / 100 // paise to rupee
            } catch (error) {
                const genericErr: GenericError = new GenericError({message: error.message})
                genericErr.statusCode = 500
                serviceInterfaces.rollbarService.handleError(genericErr)
            }

            // filtering food marketplace page for normal case
            page.sections.filter(s => s.id !== "foodmpordernow")
            page.pageData = {
                fitcashBalance,
                cartOffers: AppUtil.isProgressiveCartOffersSupported(userContext) ? cartOffers : modalCartOffers,
                addonCategories: {
                    EAT_LATER: eatLaterAddonCategories ? eatLaterAddonCategories : [],
                    EAT_NOW: eatNowAddonCategories ? eatNowAddonCategories : [],
                    ORDER_NOW: orderNowAddonCategories ? orderNowAddonCategories : [],
                    WHOLE_FIT: []
                },
                modalOffers: AppUtil.isProgressiveCartOffersSupported(userContext) ? modalCartOffers : undefined
            }
        }

        return page
    }

}
