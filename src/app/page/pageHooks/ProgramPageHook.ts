import { inject, injectable } from "inversify"
import { <PERSON>Hook } from "./PageHook"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Page as VMPage } from "@curefit/vm-common/dist/src/models/Page"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Page } from "../vm/VMPageBuilder"
import { DIYUserAttribute, DIYUserAttributeState } from "@curefit/diy-common"
import AppUtil from "../../util/AppUtil"
import { Action } from "@curefit/apps-common"


@injectable()
export class ProgramPageHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "programpage"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return Promise.resolve(page)
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {

        const userId: string = userContext.userProfile.userId
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const userAttribute: DIYUserAttribute = await serviceInterfaces.diyService.getUserAttribute(userId, "ONB_RECOMMENDED_PACK_ATTRIBUTE", tenant)
        const page = await pageView

        // @ts-ignore
        const diyPackId =  page.body.reduce((packId, widget) => {
            if (!packId) {
                if ( widget.widgetType === "PROGRAM_INFO_WIDGET"
                    || widget.widgetType === "PROGRAM_SCHEDULE_WIDGET"
                    || widget.widgetType === "PROGRAM_ICONS_WIDGET") {
                    return widget.packId
                }
            }
            return packId
        }, null)

        if (userAttribute?.attributeState === DIYUserAttributeState.ACTIVE) {
            return page
        }


        const action: Action = {
            actionType: "SHOW_SUCCESS_MODAL",
            title: "ENROLL NOW",
            // @ts-ignore
            text: "You have enrolled! You will get one class recommendation everyday as per your program",
            isEnroll: true,
            programEnrollmentMeta: {
                packId: diyPackId,
                isEnroll: true,
                completionAction: {
                    actionType: "NAVIGATION",
                    url: "curefit://fitnesshubclp?pageId=fitnesshub"
                }
            }
        }

        const skipAction: Action = {
            title: "SKIP",
            actionType: "NAVIGATION",
            url: "curefit://fitnesshubclp?pageId=fitnesshub",
            variant: "tertiary"
        }

        page.actions = [action]
        page.rightBarButton = skipAction
        page.searchBarButton = null
        page.actionLayout = "vertical"

        return page
    }
}
