import { inject, injectable } from "inversify"
import { PageHook } from "./PageHook"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Page as VMPage } from "@curefit/vm-common/dist/src/models/Page"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Page } from "../vm/VMPageBuilder"
import AppUtil from "../../util/AppUtil"
import { Action } from "@curefit/apps-common/dist/src/actions/actions"
import LiveUtil from "../../util/LiveUtil"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"


@injectable()
export class CoachProgramPageHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "coachprogram"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return Promise.resolve(page)
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {

        const userId: string = userContext.userProfile.userId
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const supportSuccessModalV2 = await AppUtil.doesUserSupportSuccessModalV2(userContext)

        const isTrialExpiredPromise = AppUtil.doesUserBelongToCoachTrialExpiredSegment(userContext, serviceInterfaces)

        const [isTrialExpired, page] = await Promise.all([isTrialExpiredPromise, pageView])

        // const page = await pageView

        // @ts-ignore
        const diyPackId =  page.body.reduce((packId, widget) => {
            if (!packId) {
                if ( widget.widgetType === "PROGRAM_INFO_WIDGET"
                    || widget.widgetType === "PROGRAM_SCHEDULE_WIDGET"
                    || widget.widgetType === "PROGRAM_ICONS_WIDGET") {
                    return widget.packId
                }
            }
            return packId
        }, null)

        const response  = await serviceInterfaces.diyService.getDIYCoachUserPlan(userId, tenant)

        if (response) {
            page.rightBarButton = {
                actionType: "SHOW_ACTION_LIST_MODAL",
                payload: {
                    actionList: [
                        {
                            action: this.getExitProgramAction(userContext, diyPackId, isTrialExpired)
                        },
                    ],
                    bottomAction: {
                        actionType: "EMPTY_ACTION",
                        title: "CANCEL",
                        variant: "secondary"
                    }
                },
                icon: "three-dots-vertical",
                variant: "icon",
            }
            page.searchBarButton = null
            return page
        }

        const modalAction: Action = {
            actionType: "SHOW_ACTION_LIST_MODAL",
            payload: {
                actionList: [
                    {
                        action: {
                            actionType: "NAVIGATION",
                            url: this.getEditFormUrl(userContext),
                            title: "Edit Goals",
                            icon: "edit",
                            iconTintColor: "white",
                            iconSize: 16,
                            textColor: "white",
                            variant: "tertiary",
                        },
                    },
                ],
                bottomAction : {
                    actionType: "EMPTY_ACTION",
                    title: "CANCEL",
                    variant: "secondary"
                }
            },
            icon: "three-dots-vertical",
            variant: "icon",
        }


        const enrollCompletionUrl = "curefit://tabpage?pageId=fitnesshub&selectedTab=cultpassLIVE-Members_NEW"

        const action: Action = await this.getPageAction(userContext, supportSuccessModalV2, enrollCompletionUrl, diyPackId, serviceInterfaces.hamletBusiness)

        const exploreAction: Action = {
            actionType: "NAVIGATION",
            title: "EXPLORE PROGRAMS",
            url: "curefit://coachexploreprogram?showCross=true",
            variant: "tertiary",
        }

        page.actions = [action]
        // page.actionLayout = "vertical"
        page.rightBarButton = modalAction
        page.searchBarButton = null

        return page
    }

    getExitProgramAction = (userContext: UserContext, packId: string, isTrialExpired: boolean) => {

        if (isTrialExpired) {
            return {
                actionType: "NAVIGATION",
                title: "Leave Program",
                icon: "logout",
                iconTintColor: "statusRed",
                iconSize: "18",
                variant: "tertiary",
                textColor: "statusRed",
                url: "curefit://livefitnessbrowsepage",
            }
        }


        let feedbackUrl = "curefit://userform?formId=Guidance_for_at_home_feedback"
        if (userContext.sessionInfo.appVersion >= 9.64) {
            feedbackUrl = "curefit://fl_form?formId=Guidance_for_at_home_feedback"
        }

        return {
            title: "Leave Program",
            actionType: "SHOW_ALERT_MODAL",
            icon: "logout",
            iconTintColor: "statusRed",
            iconSize: "18",
            variant: "tertiary",
            textColor: "statusRed",
            meta: {
                subTitle: "Your progress in this program will be lost.",
                meta: {
                    modalHeight: 250,
                    titleStyle: {
                        fontSize: 20
                    }
                },
                title: "Are you sure?",
                actions: [
                    {
                        title: "YES",
                        actionType: "SHOW_SUCCESS_MODAL",
                        payload : {
                            text: "Sad to see you leave. We encourage you to explore other goal-based programs that'll help you reach your fitness goal faster.",
                            coachProgramEnrollmentMeta: {
                                packId: packId,
                                isEnroll: false,
                            },
                            eventType: "USER_EXITED_PROGRAM_" + packId,
                        },
                        completionAction: {
                            url: feedbackUrl,
                            actionType: "NAVIGATION"
                        },
                    },
                    {
                        title: "NO",
                        actionType: "HIDE_ALERT_MODAL"
                    }
                ]
            },
        }
    }

    getEditFormAction(userContext: UserContext) {
        return {
            actionType: "NAVIGATION",
            url: this.getEditFormUrl(userContext),
            icon: "edit",
            iconTintColor: "white",
            iconSize: 16,
            textColor: "white",
            title: "Edit Goals",
            variant: "tertiary",
        }
    }

    getEditFormUrl(userContext: UserContext) {
        if (userContext.sessionInfo.appVersion >= 9.60) {
            return "curefit://fl_form?formId=Home_Guidance_Onboarding_v2&fillPreviousResponse=true&prefillFormId=Home_Guidance_Onboarding&enrollProgram=true"
        }
        return "curefit://userform?formId=Home_Guidance_Onboarding&fillPreviousResponse=true"
    }

    async getPageAction(userContext: UserContext, supportSuccessModalV2: boolean, enrollCompletionUrl: string, diyPackId: string, hamletBusiness: HamletBusiness): Promise<Action> {
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, hamletBusiness)

        if (userContext.sessionInfo.appVersion >= 9.67 && (bucketId === "2" || bucketId === "3")) {
            return {
                actionType: "NAVIGATION",
                title: "ENROLL NOW",
                url: `curefit://preprogrampage?enrollProgram=true&packId=${diyPackId}`
            }
        }
        return {
            actionType: "SHOW_SUCCESS_MODAL",
            title: "ENROLL NOW",
            payload: {
                text: !supportSuccessModalV2 ? "You have enrolled! You will get one class recommendation everyday as per your program" : null,
                title: "Enrolled Successfully!",
                subTitle: `"Commitment is what transforms a promise into a reality"`,
                coachProgramEnrollmentMeta: {
                    packId: diyPackId,
                    isEnroll: true,
                },
                timeout: 3000,
                eventType: "USER_ENROLLED_PROGRAM_" + diyPackId,
            },
            completionAction: {
                actionType: "NAVIGATION",
                url: enrollCompletionUrl
            }
        }
    }
}