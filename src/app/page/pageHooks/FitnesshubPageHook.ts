import { inject, injectable } from "inversify"
import { <PERSON><PERSON><PERSON> } from "./PageHook"
import { BASE_TYPES, Logger } from "@curefit/base"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Page as VMPage, Tab } from "@curefit/vm-common"
import { UserContext } from "@curefit/userinfo-common"
import { Page } from "../vm/VMPageBuilder"

const cultpassLiveSegment = process.env.ENVIRONMENT === "STAGE" ? "45f055e8-fdf3-4f03-905d-d57309e0c354" : "9fb8ddb9-5df9-4122-8ed1-195b0c7ef034"
const cultpassGoldSegment = process.env.ENVIRONMENT === "STAGE" ? "236d9e09-2dea-4695-a0e7-f054c9a4f90f" : "fe79a43c-7a66-4d51-ab90-1db0719c123b"
const cultpassBlackSegment = process.env.ENVIRONMENT === "STAGE" ? "bdc32d30-4dcd-48e6-8ede-40f07bfc1d0f" : "d4745a50-6023-4e2c-9f30-ec02ef95ea57"
const NEW_UNIFIED_CLP_MEMBERS_CONVERTED_AFTER_4_NOV_9_28 = "89e7b8a7-bb10-485e-8752-52acfd1f724b"
const UNIFIED_FITNESS_TAB_MEMBER_SEGMENT = process.env.ENVIRONMENT === "STAGE" ? "ec612e06-0e57-4e2d-b299-3b7bf0bd616a" : "4844702f-24bf-4c85-bc86-6bf69c4a5341"
const cultpassPlaySegment = process.env.ENVIRONMENT === "STAGE" ? "bdc32d30-4dcd-48e6-8ede-40f07bfc1d0f" : "f218c352-1067-421f-8b2a-691d6733553e"
const onlyCultpassPlaySegment = process.env.ENVIRONMENT === "STAGE" ? "bdc32d30-4dcd-48e6-8ede-40f07bfc1d0f" : "66f942f4-65da-4db8-8444-3c7925c00048"

@injectable()
export class FitnesshubPageHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "fitnesshub"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, serviceInterfaces: CFServiceInterfaces): Promise<VMPage> {
        return Promise.resolve(page)
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        const blackSegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(cultpassBlackSegment, userContext)
        const unifiedCLPMemberV1LaunchDarkThemeSegment = await serviceInterfaces.segmentService.doesUserBelongToAnySegment([NEW_UNIFIED_CLP_MEMBERS_CONVERTED_AFTER_4_NOV_9_28, UNIFIED_FITNESS_TAB_MEMBER_SEGMENT], userContext)
        const page = await pageView
        page.theme = unifiedCLPMemberV1LaunchDarkThemeSegment ? "DARK" : "LIGHT"
        if (blackSegment) {
            page.name = "cultpass ELITE"
            return Promise.resolve(page)
        }
        const goldSegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(cultpassGoldSegment, userContext)
        if (goldSegment) {
            page.name = "cultpass PRO"
            return Promise.resolve(page)
        }

        const playSegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(cultpassPlaySegment, userContext)
        if (playSegment) {
            page.name = "cultpass PLAY"
            return Promise.resolve(page)
        }

        const liveSegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(cultpassLiveSegment, userContext)
        const onlyPlaySegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(onlyCultpassPlaySegment, userContext)
        if (liveSegment && !onlyPlaySegment) {
            page.name = "cultpass HOME"
            return Promise.resolve(page)
        }
        return Promise.resolve(page)
    }
}
