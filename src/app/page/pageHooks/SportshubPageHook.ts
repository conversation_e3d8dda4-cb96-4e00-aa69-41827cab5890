import { inject, injectable } from "inversify"
import { <PERSON>Hook } from "./PageHook"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Page as VMPage } from "@curefit/vm-common/dist/src/models/Page"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Page } from "../vm/VMPageBuilder"


const cultpassPlaySegment = process.env.ENVIRONMENT === "STAGE" ? "bdc32d30-4dcd-48e6-8ede-40f07bfc1d0f" : "80b1aa33-837c-43e1-b6d2-84562b5ceff1"
@injectable()
export class SportshubPageHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "sportshub"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, serviceInterfaces: CFServiceInterfaces): Promise<VMPage> {
        return Promise.resolve(page)
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        const page = await pageView

        const playSegment = await serviceInterfaces.segmentService.doesUserBelongToSegment(cultpassPlaySegment, userContext)
        if (playSegment) {
            page.name = "cultpass PLAY"
            return Promise.resolve(page)
        }
        return Promise.resolve(page)
    }
}
