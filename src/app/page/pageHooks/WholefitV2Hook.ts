import { EatClpHook } from "./EatClpHook"
import { Page as VMPage } from "@curefit/vm-common"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Page, TabPage } from "../vm/VMPageBuilder"
import { injectable } from "inversify"

@injectable()
export class WholefitV2Hook extends EatClpHook {

    getPageId(): string {
        return "wholefitv2"
    }
    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return page
    }

    async callPageHook(interfaces: CFServiceInterfaces,  pageView: Promise<Page>, userContext: UserContext, queryParams?: { [filter: string]: any}, sharedData?: any): Promise<Page> {
        return pageView
    }
}