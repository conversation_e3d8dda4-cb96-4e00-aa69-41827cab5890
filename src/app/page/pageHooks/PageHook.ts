import { <PERSON>ageH<PERSON>, <PERSON>, pageIdHookMap } from "../vm/VMPageBuilder"
import { Page as VMPage } from "@curefit/vm-common"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { injectable } from "inversify"

@injectable()
export abstract class Page<PERSON><PERSON> implements IPageHook {

    pageId: string

    constructor() {
    }

    register(pageId: string) {
        this.pageId = pageId
        pageIdHookMap.set(pageId, this)
    }



    abstract preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, serviceInterfaces: CFServiceInterfaces): Promise<VMPage>

    abstract callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page>

}
