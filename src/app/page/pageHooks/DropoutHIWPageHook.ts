import { PageHook } from "./PageHook"
import { UserContext } from "@curefit/userinfo-common"
import { ListPage, Page } from "../vm/VMPageBuilder"
import { Page as VMPage } from "@curefit/vm-common"
import { injectable } from "inversify"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"

@injectable()
export class DropoutHIW<PERSON>ageHook extends PageHook {

    constructor() {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "dropouthiw"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return Promise.resolve(page)
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        const page = <ListPage>await pageView
        const fromDropoutAction = queryParams.fromDropoutAction === "true"
        if (fromDropoutAction) {
            page.actions = [{
                title: queryParams.actionTitle || "Continue",
                actionType: "CULT_CLASS_DROPOUT",
                meta: { ...queryParams, nextAction: { actionType: "POP_ACTION" }}
            }]
        } else {
            page.actions = [{
                title: "Got It",
                actionType: "POP_ACTION"
            }]
        }
        return page
    }

}
