import { UserContext } from "@curefit/userinfo-common"
import { Page } from "../vm/VMPageBuilder"
import { Page as VMPage } from "@curefit/vm-common"
import { inject, injectable } from "inversify"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { PageHook } from "./PageHook"
import { ErrorFactory } from "@curefit/error-client"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import { ErrorCodes } from "../../error/ErrorCodes"

@injectable()
export class FitclubMembershipV2Hook extends PageHook {

    constructor(
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "FitclubMembershipPageV2"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return page
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).build()
    }

}
