import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { UserContext } from "@curefit/userinfo-common"
import { Page, TabPage } from "../vm/VMPageBuilder"
import EatUtil from "../../util/EatUtil"
import { CartOffer } from "@curefit/offer-common"
import { Page as VMPage, Segment } from "@curefit/vm-common"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { PageHook } from "./PageHook"
import { BASE_TYPES, Logger } from "@curefit/base"
import AppUtil from "../../util/AppUtil"

@injectable()
export class WellnessClpHook extends PageHook {

    constructor(@inject(BASE_TYPES.ILogger) protected logger: Logger) {
        super()
        this.register(this.getPageId())
    }

    getPageId(): string {
        return "wellnessclp"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return page
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        return pageView
    }

}
