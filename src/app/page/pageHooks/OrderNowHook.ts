import { UserContext } from "@curefit/userinfo-common"
import { Page, TabPage } from "../vm/VMPageBuilder"
import EatUtil from "../../util/EatUtil"
import { CartOffer } from "@curefit/offer-common"
import { Page as VMPage } from "@curefit/vm-common"
import { injectable } from "inversify"
import * as _ from "lodash"
import { EatClpHook } from "./EatClpHook"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { CFUserProfile } from "../vm/CFUserProfile"
import AppUtil from "../../util/AppUtil"

@injectable()
export class OrderNowHook extends EatClpHook {

    getPageId(): string {
        return "eatordernow"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return page
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        const page = <TabPage>await pageView
        const menuType = sharedData && sharedData["ORDER_NOW"] && sharedData["ORDER_NOW"].menuType
        const cartOffersResponse = await this.createCartOffers(userContext, menuType)
        const orderNowData = sharedData["ORDER_NOW"]
        const singleOffersResult = await userContext.userProfile.eatSingleOffersPromise
        let orderNowAddonCategories
        if (orderNowData) {
            orderNowAddonCategories = this.getOffers(singleOffersResult, orderNowData, serviceInterfaces)
        }
        page.pageData = {
            cartOffers: AppUtil.isProgressiveCartOffersSupported(userContext) ? cartOffersResponse.cartOffers : cartOffersResponse.modalCartOffers ,
            addonCategories: { ORDER_NOW: orderNowAddonCategories ? orderNowAddonCategories : [] },
            modalOffers: AppUtil.isProgressiveCartOffersSupported(userContext) ? cartOffersResponse.modalCartOffers : undefined
        }
        return page
    }

}
