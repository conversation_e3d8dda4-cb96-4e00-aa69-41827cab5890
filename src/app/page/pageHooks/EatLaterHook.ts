import { UserContext } from "@curefit/userinfo-common"
import { Page, TabPage } from "../vm/VMPageBuilder"
import EatUtil from "../../util/EatUtil"
import { CartOffer } from "@curefit/offer-common"
import { Page as VMPage } from "@curefit/vm-common"
import { injectable } from "inversify"
import * as _ from "lodash"
import { EatClpHook } from "./EatClpHook"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"

@injectable()
export class EatLaterHook extends EatClpHook {

    getPageId(): string {
        return "eatlater"
    }

    async preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<VMPage> {
        return page
    }

    async callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page> {
        const page = <TabPage>await pageView
        const cartOffersResult = await userContext.userProfile.eatCartOffersPromise

        const cartOffers: CartOffer[] = EatUtil.cartOffersForUpsell(cartOffersResult).filter((cartOffer) => {
            return _.isNil(cartOffer.constraints.paymentChannel) && cartOffer.constraints.minimumAmount > 0
        })
        const eatLaterData = sharedData["EAT_LATER"]
        const singleOffersResult = await userContext.userProfile.eatSingleOffersPromise

        let eatLaterAddonCategories
        if (eatLaterData) {
            eatLaterAddonCategories = this.getOffers(singleOffersResult, eatLaterData, serviceInterfaces)
        }
        page.pageData = { cartOffers: cartOffers, addonCategories: { EAT_LATER: eatLaterAddonCategories ? eatLaterAddonCategories : [] } }
        return page
    }

}
