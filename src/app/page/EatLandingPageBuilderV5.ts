import { inject } from "inversify"
import { <PERSON>, PageWidget, PageWidgetTitle } from "./Page"
import { ClpFooterWidget, MealGridWidget, MealItem } from "./PageWidgets"
import IPageBuilder from "./IPageBuilder"
import EatLandingPageConfig from "./EatLandingPageConfig"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IGateService } from "@curefit/delivery-client"
import { ICatalogueService, CATALOG_CLIENT_TYPES } from "@curefit/catalog-client"
import { IOfferServiceEther as IOfferService } from "@curefit/offer-service-client"
import { Session } from "@curefit/userinfo-common"
import { Vertical } from "@curefit/base-common"
import MealUtil from "../util/MealUtil"
import IProductBusiness from "../product/IProductBusiness"
import IUserBusiness from "../user/IUserBusiness"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import { Logger, BASE_TYPES } from "@curefit/base"
import EatLandingPageBuilderV3 from "./EatLandingPageBuilderV3"
import { IBannerService } from "@curefit/vm-models"
import { ICerberusService } from "@curefit/vm-models"
import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import { ICapacityService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { MASTERCHEF_MODELS_TYPES } from "@curefit/masterchef-models"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { IFulfilmentService } from "@curefit/alfred-client"
import { UserContext } from "@curefit/userinfo-common"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import { CacheHelper } from "../util/CacheHelper"
import { EAT_TYPES, IFoodCategoryService } from "@curefit/eat"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"

class EatLandingPageBuilderV5 extends EatLandingPageBuilderV3 implements IPageBuilder {
    constructor(@inject(CUREFIT_API_TYPES.EatLandingPageConfig) protected eatLandingPageConfig: EatLandingPageConfig,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CAESAR_CLIENT_TYPES.MenuService) protected menuService: IMenuService,
        @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) protected deliveryAreaService: IDeliveryAreaService,
        @inject(CUREFIT_API_TYPES.CerberusServiceV2) protected cerberusService: ICerberusService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceEther) protected offerService: IOfferService,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerServiceV2: IOfferServiceV2,
        @inject(CUREFIT_API_TYPES.ProductBusiness) protected productBusiness: IProductBusiness,
        @inject(CUREFIT_API_TYPES.UserBusiness) protected userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.SessionService) protected sessionBusiness: ISessionService,
        @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) protected capacityService: ICapacityService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(DELIVERY_CLIENT_TYPES.GateService) protected gateService: IGateService,
        @inject(CUREFIT_API_TYPES.BannerService) protected bannerService: IBannerService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) public activityStoreDao: IActivityStoreReadonlyDao,
        @inject(EAT_TYPES.FoodCategoryService) protected foodCategoryService: IFoodCategoryService
    ) {
        super(eatLandingPageConfig, logger,
            menuService, deliveryAreaService, cerberusService, offerService, offerServiceV2, productBusiness, userBusiness, sessionBusiness,
            capacityService, catalogueService, cityService,
            gateService, bannerService, userCache, activityStoreDao, foodCategoryService)
    }

    build(userContext: UserContext, session: Session, lat: number, lon: number, appVersion?: number, os?: string): Promise<Page> {
        return super.build(userContext, session, lat, lon, appVersion, os)
    }

    protected isRecipeWidgetNeeded(): boolean {
        return false
    }

    protected showPackInfo(): boolean {
        return false
    }

    protected getVerticalOverride(): Vertical {
        return "PHONEPE_EAT"
    }

    protected getFooterWidget(): PageWidget {
        const footerWidget: ClpFooterWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            texts: this.eatLandingPageConfig.footerTexts,
            widgetType: "CLP_FOOTER_WIDGET",
            image: this.eatLandingPageConfig.footerImage
        }
        return footerWidget
    }

    addMealWidgetPromise(userContext: UserContext, mealItems: MealItem[], widgetPromises: Promise<PageWidget>[], pageWidgetTitle: PageWidgetTitle) {
        const mealGridWidget: MealGridWidget = {
            titleWidget: pageWidgetTitle,
            widgetType: "MEAL_GRID_WIDGET",
            items: mealItems,
            hasTopPadding: false,
            hasBottomPadding: false
        }
        mealGridWidget.moreWidget = MealUtil.mealSeeMore(userContext, false, false)
        widgetPromises.push(Promise.resolve(mealGridWidget))
    }
}



export default EatLandingPageBuilderV5
