import { Banner, ConsultationActionCard, FooterText } from "./PageWidgets"
import { inject, injectable } from "inversify"
import { Offer, PageWidgetMore, PageWidgetTitle } from "./Page"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class CareLandingPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 38 * 60)
        this.load("CareLandingPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: "CareLandingPageConfig" } }).then(pageConfig => {
            const data = pageConfig.data
            this.banners = data.banners
            this.consultationTitle = data.consultationTitle
            this.consultationProducts = data.consultationProducts
            this.offer = data.currentOffer
            this.footerTexts = data.footerTexts
            this.hcuTitle = data.hcuTitle
            this.kmhTitle = data.kmhTitle
            this.careHCUUserIds = data.careHCUUserIds
            this.experienceSpaceTitle = data.experienceSpaceTitle
            this.experienceItems = data.experienceItems
            this.experienceMoreAction = data.experienceMoreAction
            return data
        })
    }

    public banners: Banner[]
    public consultationTitle: {
        text: string
        title: string
    }
    public consultationProducts: ConsultationActionCard[]
    public offer: Offer
    public footerTexts: FooterText[]
    public hcuTitle: PageWidgetTitle
    public kmhTitle: PageWidgetTitle
    public careHCUUserIds: string[]
    public experienceSpaceTitle: PageWidgetTitle
    public experienceMoreAction: PageWidgetMore
    public experienceItems: Banner[]
}

export default CareLandingPageConfig
