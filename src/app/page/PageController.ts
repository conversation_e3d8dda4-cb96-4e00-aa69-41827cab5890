import { AuthUtil } from "./../util/AuthUtil"
import * as express from "express"
import * as _ from "lodash"
import { controller, httpGet, httpPut, httpDelete, httpPost, queryParam } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Page } from "./Page"
import { ProductDetailPage } from "../common/views/WidgetView"
import IPageBuilder, { Tab } from "./IPageBuilder"
import IInfoPageBuilder from "./InfoPageBuilder"
import AuthMiddleware from "../auth/AuthMiddleware"
import { Session } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { IFeedback, TemplatePage } from "@curefit/vm-common"
import VMPageBuilder, { WHOLE_FIT_SUPPORTED_PAGE_IDS } from "./vm/VMPageBuilder"
import { UserContext } from "@curefit/userinfo-common"
import IUserBusiness from "../user/IUserBusiness"
import { CFUserProfile } from "./vm/CFUserProfile"
import GearLandingPageBuilder, { GearCategoryPage } from "./GearLandingPageBuilder"
import { CatalogueProduct as CGCatalogueProduct } from "@curefit/gear-common"
import { GearUtil } from "@curefit/gearvault-client"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES } from "@curefit/delivery-client"
import { SlotUtil } from "@curefit/eat-util"
import WidgetBuilder from "./vm/WidgetBuilder"
import { CFServiceInterfaces } from "./vm/ServiceInterfaces"
import { BaseWidget } from "@curefit/vm-models"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import EatUtil from "../util/EatUtil"
import GearCatalogueLandingPageService from "./GearCatalogueLandingPageService"
import { PromUtil } from "../../util/PromUtil"
import { ListingBrandIdType } from "@curefit/eat-common"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import AppUtil from "../util/AppUtil"
import { OrderSource } from "@curefit/order-common"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { Tenant, UserInfo } from "@curefit/user-common"
import { HamletBusiness, HAMLET_TYPES } from "@curefit/hamlet-node-sdk"
import CFAPIJavaService from "../CFAPIJavaService"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { GearProductPricesResponse } from "@curefit/offer-common"
import { PageTypes } from "@curefit/apps-common"
import { IAppFeedback } from "./vm/services/AppFeedbackService"
import { FUSE_CLIENT_TYPES, IDiagnosticService } from "@curefit/fuse-node-client"
import { ALBUS_CLIENT_TYPES, IHealthfaceService } from "@curefit/albus-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import CareUtil from "../util/CareUtil"

function controllerFactory(kernel: Container) {
    @controller("/page", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class PageController {
        constructor(
            @inject(CUREFIT_API_TYPES.CultLandingPageBuilderV2) private cultPageBuilderV2: IPageBuilder,
            @inject(CUREFIT_API_TYPES.EatLandingPageBuilderV3) private eatPageBuilderV3: IPageBuilder,
            @inject(CUREFIT_API_TYPES.EatLandingPageBuilderV4) private eatPageBuilderV4: IPageBuilder,
            @inject(CUREFIT_API_TYPES.EatLandingPageBuilderV5) private eatPageBuilderV5: IPageBuilder,
            @inject(CUREFIT_API_TYPES.MindLandingPageBuilderV2) private mindLandingPageBuilderV2: IPageBuilder,
            @inject(CUREFIT_API_TYPES.InfoPageBuilder) private infoPageBuilder: IInfoPageBuilder,
            @inject(CUREFIT_API_TYPES.VMPageBuilder) private VMPageBuilder: VMPageBuilder,
            @inject(CUREFIT_API_TYPES.CareLandingPageBuilderV1) private careLandingPageBuilderV1: IPageBuilder,
            @inject(CUREFIT_API_TYPES.UserBusiness) protected userBusiness: IUserBusiness,
            @inject(CUREFIT_API_TYPES.GearLandingPageBuilder) protected gearLandingPageBuilder: GearLandingPageBuilder,
            @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) private deliveryAreaService: IDeliveryAreaService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(CUREFIT_API_TYPES.WidgetBuilder) private VMWidgetBuilder: WidgetBuilder,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
            @inject(CUREFIT_API_TYPES.GearCatalogueLandingPageService) private gearCLPService: GearCatalogueLandingPageService,
            @inject(CUREFIT_API_TYPES.PromUtil) private promUtil: PromUtil,
            @inject(PAGE_CONFIG_TYPES.ConfigService) private configService: ConfigService,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.CFAPIJavaService) private cFAPIJavaService: CFAPIJavaService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.AppFeedbackService) private userResearchAppFeedbackService: IAppFeedback,
            @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) protected diagnosticService: IDiagnosticService,
            @inject(ALBUS_CLIENT_TYPES.HealthfaceService) private healthfaceService: IHealthfaceService,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        ) {
        }

        @httpGet("/infopage")
        infoPage(req: express.Request): Promise<ProductDetailPage | TemplatePage> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const contentId: string = req.query.contentId
            const appVersion: number = Number(req.headers["appversion"])
            const osName: string = req.headers["osname"] as string
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.infoPageBuilder.build(contentId, req.userContext as UserContext)
        }

        @httpGet("/home/<USER>")
        async homePageV1(req: express.Request): Promise<Page> {
            const userAgent: UserAgent = req.session.userAgent
            // Just for web seo this api is used now
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                return Promise.resolve({ userLocation: "" })
            }
        }


        @httpGet("/cultLanding/v2")
        cultLandingV2(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const selectedTab: Tab = req.query.selectedTab
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.cultPageBuilderV2.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion, selectedTab)
        }

        @httpGet("/mindLanding/v2")
        mindLandingV2(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const selectedTab: Tab = req.query.selectedTab
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.mindLandingPageBuilderV2.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion, selectedTab)
        }

        @httpGet("/eatLanding/v3")
        eatLandingV3(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const selectedTabIndex: number = req.query.selectedTabIndex ? Number(req.query.selectedTabIndex) : -1
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const isOnlySingles: boolean = req.query.singlesonly
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.eatPageBuilderV3.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion.valueOf(), selectedTabIndex, isOnlySingles)
        }

        @httpGet("/eatLanding/v4")
        eatLandingV4(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.eatPageBuilderV4.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion)
        }

        @httpGet("/eatLanding/v5")
        eatLandingV5(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            return this.eatPageBuilderV5.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion)
        }

        @httpGet("/careLanding")
        careLandingV1(req: express.Request): Promise<Page> {
            const session: Session = req.session
            const lat: number = req.query.lat
            const lon: number = req.query.lon
            const osName: string = req.headers["osname"] as string
            const appVersion: number = Number(req.headers["appversion"])
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0

            return this.careLandingPageBuilderV1.build(req.userContext as UserContext, session, lat, lon, appVersion, osName, codePushVersion)
        }

        @httpGet("/gearLanding/:slug")
        async gearLandingV1(req: express.Request): Promise<GearCategoryPage> {
            const slug: string = req.params.slug
            const session: Session = req.session
            const userId = session.userId
            const deviceId: string = session.deviceId
            const pageNumber = req.query.pageNumber
            const perPage = req.query.perPage
            const orderSource: OrderSource = AppUtil.callSourceFromContext(req.userContext)

            const gearCategoryPage: { title: string, products: CGCatalogueProduct[] } = await this.gearCLPService.getCataloguePageWith(slug, pageNumber, perPage)
            const productCount = gearCategoryPage.products.length
            const isOutofStock = productCount === 0 ? _.isNil(pageNumber) || !_.isNil(pageNumber) && pageNumber <= 1 : false
            if (isOutofStock) {
                this.logger.error(`[ProductsFromGearvault] products received are empty`)
                throw this.errorFactory.withCode(ErrorCodes.GEAR_SOLD_OUT_ERR, 400).withDebugMessage("Collection sold out.").build()
            }
            const userInfo: UserInfo = {userId: userId, deviceId: deviceId}
            const productOffers: GearProductPricesResponse = await this.offerServiceV3.getGearProductPrices(_.map(gearCategoryPage.products, p => `${p.id}`), userInfo, orderSource)
            return await this.gearLandingPageBuilder.buildCategoryPage(
                _.extend(gearCategoryPage, { products: GearUtil.addDiscountInformationToProducts(gearCategoryPage.products, productOffers.priceMap) }),
                req.userContext
            )
        }

        @httpGet("/widget")
        async VMWidget(req: express.Request): Promise<BaseWidget> {
            // assuming only one widget will be returned and no up front promise required. Need to revist when we see more usecase
            // currently powering the paginated api of completed activities in hometab
            const widgetId = req.query.widgetId
            const widgets = (await this.VMWidgetBuilder.buildWidgets([widgetId], this.serviceInterfaces, req.userContext, req.query, {})).widgets
            return widgets[0]
        }

        @httpPost("/widgets")
        async VMWidgets(req: express.Request): Promise<BaseWidget[]> {
            // currently powering the node widget builder from java service
            const widgetIds = req.body.widgetIds ?? []
            return (await this.VMWidgetBuilder.buildWidgets(widgetIds, this.serviceInterfaces, req.userContext, req.query, {})).widgets
        }

        async getPage(userContext: UserContext, pageId: string, session: Session, listingBrand: ListingBrandIdType, userAgentType: UserAgent, query: any, body?: any, req?: express.Request): Promise<any> {
            const tz = userContext.userProfile.timezone
            const pageBuildStartTime = process.hrtime()
            const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
            let userPreferredLatLong
            if (this.VMPageBuilder.hasPageRequiresLocationAware(pageId)) {
                const preferredLocationComputeStartTime = process.hrtime()

               const kioskDisabledPageIds = ["wellnessclp", "wellnesslist", "foodmpordernow"]
                const shouldIgnoreKiosk = kioskDisabledPageIds.includes(pageId) || !_.isNil(query.outletId)
                if (query.kioskId && !_.isNil(query.kioskId) && query.kioskId !== "undefined") {
                    const address = await this.userBusiness.addKioskById(userContext.userProfile.userId, query.kioskId)
                    const updateBrowseLocation = await this.userBusiness.updateBrowseLocation(session, { addressId: address.addressId }, tenant)
                    if (updateBrowseLocation.city.cityId !== userContext.userProfile.cityId) {
                        userContext.userProfile.city = updateBrowseLocation.city
                        userContext.userProfile.cityId = updateBrowseLocation.city.cityId
                    }
                }
                const userProfile: CFUserProfile = userContext.userProfile
                let mealSlot = undefined
                let ignoreServiceableTimings = true
                // const userAgentType: UserAgent = AuthUtil.getUserAgent(req)
                // getting preferred location here to compute the lat long for the preferred location used below.
                // Cant use session lat long as it would be the person's own lat long and not preferred location.
                const preferredLocationForLatLong = await this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId, userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat, mealSlot, ignoreServiceableTimings, listingBrand, userAgentType, shouldIgnoreKiosk)
                // checking below if we are serviceable in any of the delivery areas for eat now and modifying parameters mealSlot and ignoreServiceableTimings,
                // which would be required in get preferred location for getting an idea about mealslot(for computing eat now/eat later) and ignoreServiceableTimings
                this.logger.info(`Pagecontroller: pageId: ${pageId} query: ${JSON.stringify(query)} userAgentType: ${JSON.stringify(userAgentType)}`)
                if (preferredLocationForLatLong.latLong && preferredLocationForLatLong.latLong.lat && preferredLocationForLatLong.latLong.long) {
                    if (pageId === "eatnow" || (pageId === "eatclp" && query.selectedTab === "eatnow") ||
                        (((pageId === "eatclp" && query.selectedTab === "eatordernow") || (pageId === "eatordernow"))
                            && (_.isNil(query.mealSlot)))) {
                        try {
                            const deliveryArea = await this.deliveryAreaService.findDeliveryArea({ lat: preferredLocationForLatLong.latLong.lat, long: preferredLocationForLatLong.latLong.long }, "FOOD", undefined, undefined, undefined, userContext.userProfile.cityId)
                            this.logger.info(`deliveryArea returned in clp case: ${deliveryArea.areaId}`)
                            const servicableDelAreas = this.deliveryAreaService.getDeliveryAreasServiceableRightNow([deliveryArea])
                            if (_.isEmpty(servicableDelAreas)) {
                                this.logger.info(`No serviceable areas right now. Hence eat later`)
                            }
                            else {
                                mealSlot = SlotUtil.getCurrentMealSlotExact(tz)
                                ignoreServiceableTimings = false
                            }
                        }
                        catch (error) {
                            this.logger.error(`Error in findDeliveryArea for ${JSON.stringify(preferredLocationForLatLong)}`, error)
                        }
                    }
                }
                // if condition is for web when lat long are not sent
                if (preferredLocationForLatLong && preferredLocationForLatLong.latLong && preferredLocationForLatLong.latLong.lat
                    && preferredLocationForLatLong.latLong.long) {
                    // getting all delivery areas for this location
                    userProfile.availableDeliveryAreasPromise = this.serviceInterfaces.deliveryAreaService.getAllDeliveryAreasRightNowWithPriority({ lat: preferredLocationForLatLong.latLong.lat, long: preferredLocationForLatLong.latLong.long })
                }
                // getting the preferred location given its eatnow/eatlater
                userProfile.preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId,
                    userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat,
                    mealSlot, ignoreServiceableTimings, listingBrand === "FOOD_MARKETPLACE" ? listingBrand : "EAT_FIT", userAgentType, shouldIgnoreKiosk)
                let preferredLocation = await userProfile.preferredLocationPromise

                if (WHOLE_FIT_SUPPORTED_PAGE_IDS.includes(pageId)) {
                    // getting it for whole_fit
                    userProfile.wholeFitPreferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId,
                        userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat,
                        mealSlot, ignoreServiceableTimings, "WHOLE_FIT", userAgentType)
                    if (_.isNil(preferredLocation.area) && !AppUtil.isWholeFitV2ReviewSupported(userContext)) {
                        // check when request came for whole fit but whole fit doesn't exist
                        userProfile.preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userContext.userProfile.userId,
                            userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat,
                            mealSlot, ignoreServiceableTimings, "EAT_FIT", userAgentType)
                        preferredLocation = await userProfile.preferredLocationPromise
                    }
                }
                const areaId = preferredLocation.area ? preferredLocation.area.areaId : preferredLocation.defaultArea.areaId
                this.promUtil.reportWidgetRenderMetrics("preferredLocation", pageId, "none", "none", process.hrtime(preferredLocationComputeStartTime)[1] + process.hrtime(preferredLocationComputeStartTime)[0] * 1e9)
                userProfile.areaId = areaId
            }
            let params = !_.isNil(query) ? query : {}
            params = !_.isNil(pageId) ? { ...params, pageId } : params
            params = !_.isNil(body) ? { ...params, ...body } : params

            // Passing preferred location data as query params to widget build view
            if (userContext?.userProfile?.preferredLocationPromise) {
                // Preference is given as per the order: client sent query param latlong > api header's latlong > preferred location latlong
                userPreferredLatLong = AuthUtil.getLatLongFromUserContextAndParams(userContext, params, await userContext.userProfile.preferredLocationPromise)
                params = { ...params, ...userPreferredLatLong }
            }

            const page = await this.VMPageBuilder.getPage(pageId, userContext, params, req)
            if (Math.random() < 0.1) {
                this.promUtil.reportPageRenderTime(pageId, process.hrtime(pageBuildStartTime)[1] + process.hrtime(pageBuildStartTime)[0] * 1e9)
            }
            if (userContext.sessionInfo.isSessionUpdateNeeded) {
                this.sessionBusiness.updateSessionData(userContext.sessionInfo.at, userContext.sessionInfo.sessionData)
            }
            if (pageId === "careclptab") {
                page.showToolTip = true
            }
            if ((query.selectedTab === "clphcu" || pageId === "clphcu") && !session.isNotLoggedIn) {
                const userId = Number(userContext.userProfile.userId)
                const cartResponse = await this.diagnosticService.getCart(userId)
                const patientDetailsPromise = cartResponse?.cartMetaData?.patientId ? this.healthfaceService.getPatientDetails(cartResponse?.cartMetaData?.patientId) : undefined
                const product = cartResponse?.diagnosticCartItems && cartResponse?.diagnosticCartItems[0]?.productCode ? await this.catalogueService.getProduct(cartResponse?.diagnosticCartItems[0]?.productCode) : undefined
                const diagnosticTestTitle = cartResponse?.diagnosticCartItems && cartResponse?.diagnosticCartItems[0]?.productCode ? product?.title || "Diagnostic Test" : undefined
                const cartForApp = CareUtil.processCart(cartResponse, await patientDetailsPromise, diagnosticTestTitle)
                page.diagnosticCart = cartForApp
            }

            // This is used in wellness search flow, will be removed once we migrate preferredLocationPromise api to cf-api java
            if (userPreferredLatLong?.lat && userPreferredLatLong?.long) {
                page.userPreferredLatLong = userPreferredLatLong
            }
            return page
        }

        @httpGet("/:id")
        @httpPost("/:id")
        async VMPage(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const pageId = req.params.id
            const session = req.session
            const userAgentType: UserAgent = req.session.userAgent
            if ((pageId === "hometab" || pageId === "home-intl") && await AppUtil.isJavaServiceEnabled(userContext, this.hamletBusiness)) {
                return await this.cFAPIJavaService.homePage(req)
            }
            if (this.isPageDisabledForOrderSource(userContext)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("call disabled for this order source").build()
            }
            return await this.getPage(userContext, pageId, session, EatUtil.getListingBrandForPageId(pageId, req), userAgentType, req.query, req.body, req)
        }


        private isPageDisabledForOrderSource(userContext: UserContext): boolean {
            try {
                const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
                const pageEnablementConfig = this.configService.getConfig("ORDER_SOURCE_CONFIG")
                if (_.isNil(pageEnablementConfig)) {
                    return false
                }
                const disabledOrderSources: string[] = _.get(pageEnablementConfig, "configs.disabledSources", [])
                if (disabledOrderSources.includes(orderSource)) {
                    return true
                }
                return false
            } catch (err) {
                return false
            }
        }

    }
    return PageController
}

export default controllerFactory
