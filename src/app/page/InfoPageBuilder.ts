import { inject, injectable } from "inversify"


import {
  DescriptionWidget,
  <PERSON>er,
  ImageWidget,
  InfoCard,
  ProductDetailPage,
  ProductListWidget
} from "../common/views/WidgetView"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Logger, BASE_TYPES } from "@curefit/base"
import * as _ from "lodash"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import CultPackPageConfig from "../pack/CultPackPageConfig"
import {
  FooterWidget1,
  HeaderWidget1,
  TemplatePage,
  TemplateWidget,
  TemplateWidget1,
  TemplateWidget2,
  TemplateWidget3,
  TemplateWidget5
} from "@curefit/vm-common"
import { ActionUtil } from "@curefit/base-utils"
import { MealUtil } from "@curefit/base-utils"
import { IFindQuery } from "@curefit/mongo-utils"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { UserContext } from "@curefit/userinfo-common"
import TherapyPageConfig from "../therapy/TherapyPageConfig"
import TeleconsultationDetailsPageConfig from "../care/TeleconsultationDetailsPageConfig"
import AppUtil from "../util/AppUtil"
import { ICultBusiness } from "../cult/CultBusiness"

const BACKGROUND_LIGHT = "#ffffff"
const TEXT_LIGHT = "#ffffff"
const BACKGROUND_DARK = "#454343"
const TEXT_DARK = "#090909"
const BACKGROUND_BLUE = "#f6f7fd"
const BACKGROUND_CREAM = "#FAFAFA"
const BACKGROUND_BLACK = "#20212b"
const BACKGROUND_GREY = "#363741"
export interface IInfoPageBuilder {
  build(
    contentId: string,
    userContext: UserContext
  ): Promise<ProductDetailPage>
}
@injectable()
class InfoPageBuilder implements IInfoPageBuilder {
  constructor(
    @inject(CUREFIT_API_TYPES.CultPackPageConfig) private cultPackPageConfig: CultPackPageConfig,
    @inject(CUREFIT_API_TYPES.TherapyPageConfig) private therapyPageConfig: TherapyPageConfig,
    @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) private consultationPageConfig: TeleconsultationDetailsPageConfig,
    @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) private activityDao: IActivityStoreReadonlyDao,
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness
  ) { }

  async build(
    contentId: string,
    userContext: UserContext
  ): Promise<ProductDetailPage | TemplatePage> {

    // const isWithinBangalore: boolean = LocationUtil.isWithinBangalore(lat, lon);
    const page = new ProductDetailPage()
    page.widgets = []
    if (contentId === "biometric") {

      page.widgets.push(new ImageWidget(undefined, this.cultPackPageConfig.biometricInfo.icon))

      this.cultPackPageConfig.biometricInfo.data.forEach((info) => {
        const descriptions: InfoCard[] = []
        descriptions.push({
          title: info.question,
          subTitle: info.answer
        })
        page.widgets.push(new DescriptionWidget(descriptions))
      })

      page.actions.push({
        actionType: "POP_ACTION",
        title: "Done"
      })
      return page
    } else if (contentId === "whyeatfit") {
      return this.getWhyEatFitPage(userContext.sessionInfo.userAgent)
    } else if (contentId === "whyeatfitpacks") {
      return this.getWhyEatFitPackPage(userContext)
    } else if (contentId === "levelup") {
      return this.getWhyOgmPage(userContext.sessionInfo.userAgent)
    } else if (contentId === "fitstart") {
      return this.getFitStartPage(userContext.sessionInfo.userAgent, userContext.userProfile.userId)
    } else if (contentId === "whyeatfitsubscription") {
      return this.getWhyEatSubscribe(userContext, false)
    } else if (contentId === "whyeatfitkiosksubscription") {
      return this.getWhyEatSubscribe(userContext, true)
    } else if (contentId === "noshowpolicy") {
      return await this.getCultNoShowPolicyPage(userContext)
    } else if (contentId === "therapypolicy") {
      return this.getTherapyPolicyPage(userContext)
    } else if (contentId === "ptpolicy") {
      return this.getPTPolicyPage(userContext)
    } else if (contentId === "transferMembershipPolicy") {
      return await this.getCultTransferMembershipPolicy(userContext)
    } else if (contentId === "cultpassblackpolicy") {
      return await this.getCultPassBlackPolicyPage(userContext)
    } else if (contentId === "cultpassplaypolicy") {
      return await this.getPlayPackPolicy(userContext, false, false)
    } else if (contentId === "playselectpolicy") {
      return await this.getPlayPackPolicy(userContext, true, false)
    } else if (contentId === "playlimitedslppolicy") {
      return await this.getPlayPackPolicy(userContext, false, true)
    }
    return undefined
  }

  private getTherapyPolicyPage(userContext: UserContext): ProductDetailPage {
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${this.therapyPageConfig.cancellationPolicyInfo.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))
    this.therapyPageConfig.cancellationPolicyInfo.data.forEach((info) => {
      const descriptions: InfoCard[] = []
      descriptions.push({
        title: info.question,
        subTitle: info.answer
      })
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private getPTPolicyPage(userContext: UserContext): ProductDetailPage {
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${this.consultationPageConfig.ptCancellationPolicyInfo.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))
    this.consultationPageConfig.ptCancellationPolicyInfo.data.forEach((info) => {
      const descriptions: InfoCard[] = []
      descriptions.push({
        title: info.question,
        subTitle: info.answer
      })
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private async getCultNoShowPolicyPage(userContext: UserContext): Promise<ProductDetailPage> {
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${this.cultPackPageConfig.noshowPolicyInfo.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))
    this.cultPackPageConfig.noshowPolicyInfo.data.forEach(async (info) => {
      const descriptions: InfoCard[] = [{
        title: info.question,
        subTitle: info.answer
      }]
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private async getCultPassBlackPolicyPage(userContext: UserContext): Promise<ProductDetailPage> {
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${this.cultPackPageConfig.cultPassBlackPolicy.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))
    const policyConfig = userContext.sessionInfo.osName.toLowerCase() === "ios" ? this.cultPackPageConfig.cultPassBlackPolicyIOS : this.cultPackPageConfig.cultPassBlackPolicy
    policyConfig.data.forEach(async (info) => {
      const descriptions: InfoCard[] = [{
        title: info.question,
        subTitle: info.answer
      }]
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private async getCultTransferMembershipPolicy(userContext: UserContext): Promise<ProductDetailPage> {
    const transferMembershipPolicyInfo = {
      infoTitle: "Membership transfer",
      data: [{
        question: "What is membership transfer?",
        answer: "You can use membership transfer to transfer your cultpass membership from your current city to another city where cultpass is present"
      },
        {
          question: "How does it work?",
          answer: "On transfer, a new pack is created and all remaining pause days and adjusted remaining pack days get transferred to new city. If you are transferring to a city where cultpass is sold at a higher price than your current city then you will be required to pay the balance amount for the remaining duration and your membership duration will remain same as today. \nTransferring membership to another city allows you to book any number of classes in the city you are moving to"
        },
        {
          question: "How many times can I transfer?",
          answer: "Pack once transferred cannot be transferred again."
        },
        {
          question: "Can I go back on the transfer once executed?",
          answer: "Transfer once done cannot be modified again. Please check all details before going ahead with the transfer"
        }]
    }
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${transferMembershipPolicyInfo.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))
    transferMembershipPolicyInfo.data.forEach(async (info) => {
      const descriptions: InfoCard[] = [{
        title: info.question,
        subTitle: info.answer
      }]
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private async getPlayPackPolicy(
    userContext: UserContext,
    isSelectPolicy: boolean,
    isPlayLimitedSlpPolicy: boolean
  ): Promise<ProductDetailPage> {
    const playMembershipPolicyInfoAndroid = {
      "data": [
        {
          "answer": "You can cancel your booked session upto 30 minutes before the session start time from the home page of the cult.fit app",
          "question": "Cancelling my class"
        },
        {
          "answer": "Everytime you attend a class at the center you need to mark your attendance using the Facial Recognition system. You need to register on the FR tool the first time you attend a sports session. Not marking your attendance for a booked class is termed as a no-show and attracts a penalty",
          "question": "Marking my attendance"
        },
      ],
      "infoTitle": "Sport sessions at cult centers"
    }

    if (!isPlayLimitedSlpPolicy) {
      playMembershipPolicyInfoAndroid.data.push({
        "answer": "Everytime you miss a booked session you incur a no-show penalty and your membership duration reduces by 1 day. Please attend all the classes you book or cancel atleast 60 minutes in advance so that all slots can be optimally utilised by the members.",
        "question": "No show penalty"
      })
    }

    if (!isSelectPolicy && !isPlayLimitedSlpPolicy) {
      playMembershipPolicyInfoAndroid.data.push({
        "question": "Group classes",
        "answer": "You can book a group class at any cult center in your city. To enter the center, you will need to check-in using the QR code on the cult.fit app. You can cancel your booked session upto 60 minutes before the session start time or drop-out until the last minute. In case you drop-out in the last 60 minutes before the class start time, we will waive the no-show penalty if another member uses your slot."
      })
      playMembershipPolicyInfoAndroid.data.push({
        "answer": "You can directly walk in to the gym to workout at a time that is convenient to you. No prior booking is needed. To enter the gym, you will need to check-in using the cult.fit app.",
        "question": "Gym sessions"
      })
      playMembershipPolicyInfoAndroid.data.push({
        "answer": "You have unlimited access to all the content on the cult.fit app. You can attend any session of your choice whenever it is convenient for you.",
        "question": "Live classes"
      })
    }
    const playMembershipPolicyInfoIOS = {
      "data" : [
        {
          "answer": "You can cancel your booked session upto 30 minutes before the session start time from the home page of the cult.fit app",
          "question": "Cancelling my class"
        },
        {
          "answer": "Everytime you attend a class at the center you need to mark your attendance using the Facial Recognition system. You need to register on the FR tool the first time you attend a sports session. Not marking your attendance for a booked class is termed as a no-show and attracts a penalty",
          "question": "Marking my attendance"
        },
      ],
      "infoTitle" : "Sport sessions at cult centers"
    }

    if (!isPlayLimitedSlpPolicy) {
      playMembershipPolicyInfoIOS.data.push({
        "answer": "Everytime you miss a booked session you incur a no-show penalty and your membership duration reduces by 1 day. Please attend all the classes you book or cancel atleast 60 minutes in advance so that all slots can be optimally utilised by the members.",
        "question": "No show penalty"
      })
    }

    if (!isSelectPolicy && !isPlayLimitedSlpPolicy) {
      playMembershipPolicyInfoIOS.data.push({
        "question": "Group classes",
        "answer": "You can book a group class at any cult center in your city. To enter the center, you will need to check-in using the QR code on the cult.fit app. You can cancel your booked session upto 60 minutes before the session start time or drop-out until the last minute. In case you drop-out in the last 60 minutes before the class start time, we will waive the no-show penalty if another member uses your slot."
      })
      playMembershipPolicyInfoIOS.data.push({
        "answer": "You can directly walk in to the gym to workout at a time that is convenient to you. No prior booking is needed. To enter the gym, you will need to check-in using the cult.fit app.",
        "question": "Gym sessions"
      })
    }

    const playMembershipPolicyInfo = userContext.sessionInfo.osName.toLowerCase() === "ios" ? playMembershipPolicyInfoIOS : playMembershipPolicyInfoAndroid
    const page = new ProductDetailPage()
    page.widgets = []
    const header: Header = {
      title: `\n\n\n${playMembershipPolicyInfo.infoTitle}`
    }
    page.widgets.push(new ProductListWidget("SMALL", header, []))


    playMembershipPolicyInfo.data.forEach(async (info) => {
      const descriptions: InfoCard[] = [{
        title: info.question,
        subTitle: info.answer
      }]
      page.widgets.push(new DescriptionWidget(descriptions))
    })

    page.actions.push({
      actionType: "POP_ACTION",
      title: "Got it"
    })
    return page
  }

  private getWhyEatFitPage(userAgent: UserAgent): TemplatePage {
    let header: HeaderWidget1
    const image = userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/banner_web.jpg" : "/image/pages/whyeatfitv2/banner_mobile.jpg"
    header = {
      widgetType: "HEADER_WIDGET_1",
      image: image,
      titleColor: "#113858",
      subTitleColor: "#1e1e1e",
      title: "Healthy & tasty food, everyday",
      subTitle: "Daily meals from eat.fit that will leave you feeling light and top of your game!"
    }

    const balancedMealWidget: TemplateWidget1 = {
      title: "Balanced Meals Every Day",
      description: "eat.fit meals give you a boost of energy by incorporating foods from all food groups. All our meals are engineered to be balanced in terms of macro (carbs, proteins, fats) and micro (vitamins, minerals etc.) nutrients.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/1_web.jpg" : "/image/pages/whyeatfitv2/1_mobile.jpg",
      secondaryImage: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/1.2.2.jpg" : "/image/pages/whyeatfitv2/1.2_mobile.jpg",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "LEFT",
      backgroundColor: BACKGROUND_LIGHT,
      textColor: TEXT_DARK
    }

    const calorieCountedWidget: TemplateWidget1 = {
      title: "Calorie-Counted For Nutrition",
      description: "It’s not just the calories, but the quality of the calories that matter. With a complete breakdown of quality proteins, good fats and high-fibre carbs in each dish, our nutritionists highlight how the functional ingredients and superfoods can improve your health.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/2_web.jpg" : "/image/pages/whyeatfitv2/2_mobile_2.jpg",
      secondaryImage: "/image/pages/whyeatfitv2/2.2.4.jpg",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "RIGHT",
      backgroundColor: BACKGROUND_BLUE,
      textColor: TEXT_DARK
    }

    const qualityWidget: TemplateWidget1 = {
      title: "Responsibly Sourced For Quality",
      description: "From buying the freshest vegetables and salad greens to sourcing seasonal fruits and lean, high protein chicken breasts, our multi-level quality checks ensure that only the finest produce reaches your table.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/3_web.jpg" : "/image/pages/whyeatfitv2/3_mobile.jpg",
      secondaryImage: "/image/pages/whyeatfitv2/3.2.jpg",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "LEFT",
      backgroundColor: BACKGROUND_DARK,
      textColor: TEXT_LIGHT
    }

    const freshWidget: TemplateWidget1 = {
      title: "Fresh & Unprocessed For Health",
      description: "eat.fit meals are truly freshly prepared. We don't use pre-packed and preserved gravies and nor do we reuse our gravies. Our gravies and marinades are made and consumed within the prescribed quality standard.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/4_web.jpg" : "/image/pages/whyeatfitv2/4_mobile.jpg",
      secondaryImage: "/image/pages/whyeatfitv2/4.2.jpg",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "RIGHT",
      backgroundColor: BACKGROUND_LIGHT,
      textColor: TEXT_DARK
    }

    const orderNowWidget: TemplateWidget2 = {
      title: "World-Class Practices For Hygiene",
      description: "We handle your food with extreme care. Our chefs are trained to observe the strictest hygiene protocols. From hair nets and face masks, to handling of food with gloves and hand sanitisation every hour, we take hygiene as seriously as you do.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/5_web.jpg" : "/image/pages/whyeatfitv2/5_mobile.jpg",
      secondaryImage: "",
      widgetType: "TEMPLATE_WIDGET_2",
      orientation: "LEFT",
      action: {
        actionType: "PLAY_VIDEO",
        url: ActionUtil.videoUrl("curefit-content/image/pages/whyeatfitv2/why-eatfit-video.mp4")
      },
      buttonAction: {
        title: "Order now",
        actionType: userAgent === "APP" ? "POP_ACTION" : "NAVIGATION",
        url: ActionUtil.eatFitClp()
      },
      backgroundColor: BACKGROUND_CREAM,
      textColor: TEXT_DARK
    }

    const footer: FooterWidget1 = {
      title: "TIME TO",
      subTitle: "EAT FIT",
      widgetType: "FOOTER_WIDGET_1"
    }

    const widgets: TemplateWidget[] = []
    widgets.push(balancedMealWidget)
    widgets.push(calorieCountedWidget)
    widgets.push(qualityWidget)
    widgets.push(freshWidget)
    widgets.push(orderNowWidget)

    return {
      header: header,
      widgets: widgets,
      footer: footer
    }
  }

  private getWhyEatFitPackPage(userContext: UserContext): TemplatePage {
    let header: HeaderWidget1
    const isPackRedesignSupported = MealUtil.isPackRedesignSupported(userContext)
    const image = userContext.sessionInfo.userAgent === "DESKTOP" ? "/image/pages/whyeatpack/banner_web.jpg" : "/image/pages/whyeatpack/banner_mobile.jpg"
    header = {
      widgetType: "HEADER_WIDGET_1",
      image: image,
      title: "Why Packs",
      titleColor: "#113858",
      subTitleColor: "#1e1e1e",
      subTitle: "Commit to eating fit through our meal packs"
    }

    const mealPacksWidget: TemplateWidget3 = {
      title: "Here's what our meal pack offers",
      widgetType: "TEMPLATE_WIDGET_3",
      orientation: "LEFT",
      image: userContext.sessionInfo.userAgent === "DESKTOP" ? "/image/pages/whyeatpack/6_web.jpg" : "/image/pages/whyeatpack/6_mobile_2.jpg",
      dataItems: [{
        icon: "BULLET",
        title: "Enjoy different dishes everyday"
      },
      {
        icon: "BULLET",
        title: "Meals delivered daily, no follow-ups!"
      },
      {
        icon: "BULLET",
        title: "Zero delivery charges & lower price per meal"
      },
      {
        icon: "BULLET",
        title: "Change your meal, delivery slot & address on any given day"
      },
      {
        icon: "BULLET",
        title: "Eat at your convenience, within validity period"
      }],
      backgroundColor: BACKGROUND_CREAM,
      textColor: TEXT_DARK
    }

    const signupWidget: TemplateWidget3 = {
      title: "How to Sign up",
      widgetType: "TEMPLATE_WIDGET_3",
      orientation: "RIGHT",
      image: userContext.sessionInfo.userAgent === "DESKTOP" ? "/image/pages/whyeatpack/7_web.jpg" : isPackRedesignSupported ? "/image/pages/whyeatpack/8_mobile.png" : "/image/pages/whyeatpack/7_mobile.jpg",
      dataItems: [{
        icon: "BULLET",
        title: "Select your cuisine, for breakfast / lunch / snacks / dinner"
      },
      {
        icon: "BULLET",
        title: isPackRedesignSupported ? "Sign up for daily or weekday only deliveries" : "Choose delivery address, slot & start date"
      },
      {
        icon: "BULLET",
        title: isPackRedesignSupported ? "Choose delivery address, slot & start date. Pay and you are set!" : "Complete payment, and you are all set!"
      }],
      buttonAction: {
        actionType: "NAVIGATION",
        title: "Subscribe now",
        url: ActionUtil.packBrowsePage()
      },
      backgroundColor: BACKGROUND_LIGHT,
      textColor: TEXT_DARK
    }

    const widgets: TemplateWidget[] = []
    widgets.push(mealPacksWidget)
    widgets.push(signupWidget)

    return {
      header: header,
      widgets: widgets
    }
  }


  private getWhyEatSubscribe(userContext: UserContext, isKiosk: boolean): TemplatePage {
    let header: HeaderWidget1
    const image = userContext.sessionInfo.userAgent === "DESKTOP" ? "/image/pages/whyeatfitv2/2_mobile_2.jpg" : "/image/pages/whyeatfitv2/2_mobile_2.jpg"
    header = {
      widgetType: "HEADER_WIDGET_1",
      image: image
    }

    const whySubscribeTemplate: TemplateWidget5 = {
      title: "Time to Eat.fit every day",
      widgetType: "TEMPLATE_WIDGET_5",
      dataItems: [{
        icon: "SAVINGS",
        title: "Minimum 30% off on monthly subscription with zero delivery charges"
      },
      {
        icon: "CONVENIENCE",
        title: "Flexible plans allow you to change / cancel meals any time"
      },
      {
        icon: "HEALTHY_MEALS",
        title: isKiosk ? "Healthy homely meals at your kiosk every day" : "Healthy homely meals delivered every day"
      },
      {
        icon: "CANCEL_SUBSCRIPTION",
        title: "Cancel your subscription anytime and get a full refund!"
      }],
      backgroundColor: BACKGROUND_LIGHT,
      textColor: TEXT_DARK
    }

    const whySubscribeTemplate2: TemplateWidget5 = {
      title: "More time and peace of mind",
      widgetType: "TEMPLATE_WIDGET_5",
      buttonAction: {
        actionType: "NAVIGATION",
        title: "Subscribe now",
        url: ActionUtil.packBrowsePage()
      },
      dataItems: [{
        icon: "BULLET",
        title: "No more scanning menus deciding what to order"
      },
      {
        icon: "BULLET",
        title: "No spending an hour in line at the grocery store"
      },
      {
        icon: "BULLET",
        title: "No having to cook after a long, tiring day at work"
      },
      {
        icon: "BULLET",
        title: "No hassle of washing the dishes the morning after"
      }
      ],
      backgroundColor: BACKGROUND_LIGHT,
      textColor: TEXT_DARK
    }

    const widgets: TemplateWidget[] = []
    if (userContext.sessionInfo.userAgent === "APP")
      widgets.push(header)
    widgets.push(whySubscribeTemplate)
    widgets.push(whySubscribeTemplate2)

    return {
      header: header,
      widgets: widgets
    }
  }

  private getWhyOgmPage(userAgent: UserAgent): TemplatePage {
    let header: HeaderWidget1
    const image = userAgent === "DESKTOP" ? "/image/pages/whyogm/web_top_banner.jpg" : "/image/pages/whyogm/mobile_top_banner_1.jpg"
    header = {
      widgetType: "HEADER_WIDGET_1",
      image: image,
      title: "Don't Just Workout. Have FUN.",
      titleColor: TEXT_LIGHT,
      subTitleColor: TEXT_LIGHT,
      subTitle: "Exchange your current gym membership for a Cult Pack."
    }

    const whatWidget: TemplateWidget2 = {
      title: "What’s Level Up?",
      description: "Give your current workout a new lease of life by upgrading to a whole new experience what we call the Cult.\n\nIf you are already a member of any gym but are looking to break the monotony, download the cult.fit app and upload your current gym membership details to get FREE months added to the purchase of any Cult Pack.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyogm/web_what_img_1.jpg" : "/image/pages/whyogm/mobile_what_img_1.jpg",
      secondaryImage: "",
      widgetType: "TEMPLATE_WIDGET_2",
      orientation: "LEFT",
      action: undefined,
      buttonAction: {
        title: "Download now",
        actionType: "NAVIGATION",
        url: `curefit://webview?uri=${encodeURIComponent("https://mpg7.app.link/hNOirQZOmK")}`,
      },
      backgroundColor: BACKGROUND_BLACK,
      textColor: TEXT_LIGHT
    }


    const whyWidget: TemplateWidget1 = {
      title: "Why Level Up?",
      description: "Because waiting for your turn to run on the treadmill till you lose your motivation is just not fun.\n\nAt Cult, you workout and have fun with highly motivated individuals who believe in mixing up their workout routine with a gamut of formats rather than just depending on boring, complex looking machines.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyogm/web_why_img_1.jpg" : "/image/pages/whyogm/mobile_why_img_1.jpg",
      secondaryImage: userAgent === "DESKTOP" ? "/image/pages/whyogm/web_why_img_2.jpg" : "/image/pages/whyogm/mobile_why_img_2.jpg",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "RIGHT",
      backgroundColor: BACKGROUND_GREY,
      textColor: TEXT_LIGHT
    }

    const howWidget: TemplateWidget1 = {
      title: "How to Level Up?",
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "LEFT",
      description: "It's easy to exchange your current gym membership with Cult. Just follow these simple steps and enjoy your new playground.",
      image: userAgent === "DESKTOP" ? "/image/pages/whyogm/web_how_img_1.jpg" : "/image/pages/whyogm/mobile_how_img_1.jpg",
      secondaryImage: userAgent === "DESKTOP" ? "/image/pages/whyogm/web_how_img_3.jpg" : "/image/pages/whyogm/web_how_img_3.jpg",
      backgroundColor: BACKGROUND_BLACK,
      textColor: TEXT_LIGHT
    }

    const widgets: TemplateWidget[] = []
    widgets.push(whatWidget)
    widgets.push(whyWidget)
    widgets.push(howWidget)

    return {
      header: header,
      widgets: widgets
    }
  }

  private async getFitStartPage(userAgent: UserAgent, userId: string): Promise<TemplatePage> {

    const findQuery: IFindQuery = {
      condition: { "date": { "$gte": "2018-01-02", "$lte": "2018-02-28" }, "userId": userId, "show": true, "activityType": { $in: ["CULT_CLASS", "MIND_CLASS"] } }
    }
    const activities = await this.activityDao.find(findQuery)
    const score = _.sumBy(activities, activity => { return activity.score })
    let header: HeaderWidget1
    const image = userAgent === "DESKTOP" ? "/image/pages/fitstart/web_top_banner.jpg" : "/image/pages/fitstart/mobile_top_banner.jpg"
    header = {
      widgetType: "HEADER_WIDGET_1",
      image: image
    }

    let whatImage
    if (userAgent === "DESKTOP") {
      whatImage = "/image/pages/fitstart/web_what_banner.jpg"
    } else if (userAgent === "MBROWSER") {
      whatImage = "/image/pages/fitstart/mobile_what_banner.jpg"
    } else {
      whatImage = "/image/pages/fitstart/app_what_banner.jpg"
    }

    let scoreText
    if (score < 20) {
      scoreText = `${20 - score} more classes and you could win a Cult Sipper!`
    } else if (score < 30) {
      scoreText = `${30 - score} more classes and you could win a Cult T-shirt!`
    } else if (score < 40) {
      scoreText = `${40 - score} more classes and you could win a Cult Gym Bag!`
    } else {
      scoreText = `Congratulations ! You have completed the challenge !`
    }

    const webScoreImage = `/l_text:Arial_90:${score},g_north_west,y_0.3,co_rgb:fb3879/w_0.9,c_scale,fl_relative,l_text:Arial_44:${encodeURIComponent(scoreText)},g_north_west,y_0.5,co_rgb:fb3879/image/pages/fitstart/score_banner_2.jpg`
    const appScoreImage = `/l_text:Arial_90:${score},g_north_west,x_0.065,y_0.3,co_rgb:fb3879/w_0.9,c_scale,fl_relative,l_text:Arial_44:${encodeURIComponent(scoreText)},g_north_west,x_0.065,y_0.5,co_rgb:fb3879/image/pages/fitstart/score_banner_1.jpg`
    const whatWidget: TemplateWidget1 = {
      title: "What's #MYFITSTART contest?",
      description: "Every year we make a resolution to stay fit and more often we end up forgetting about it. To get you to the start, we had the Fitstart Rewards program between Jan 1 to Feb 28. Here's where you stand.\n\n#BeBetterEveryday",
      image: whatImage,
      secondaryImage: userAgent == "APP" ? appScoreImage : webScoreImage,
      widgetType: "TEMPLATE_WIDGET_1",
      orientation: "LEFT",
      backgroundColor: "#131313",
      textColor: TEXT_LIGHT
    }

    const howWidget: TemplateWidget2 = {
      title: "Next Steps",
      description: "Athletes are requested to collect their goodies from the preferred Cult centre after 3rd March 2018 ",
      image: userAgent === "DESKTOP" ? "/image/pages/fitstart/web_how_banner.jpg" : "/image/pages/fitstart/mobile_how_banner.jpg",
      secondaryImage: "",
      widgetType: "TEMPLATE_WIDGET_2",
      orientation: "RIGHT",
      action: undefined,
      buttonAction: {
        title: "BOOK YOUR NEXT CULT CLASS",
        actionType: "NAVIGATION",
        url: ActionUtil.bookCultClass("packpage"),
      },
      backgroundColor: "#131313",
      textColor: TEXT_LIGHT
    }

    const widgets: TemplateWidget[] = []
    widgets.push(whatWidget)
    widgets.push(howWidget)

    return {
      header: header,
      widgets: widgets
    }
  }
}



export default InfoPageBuilder
