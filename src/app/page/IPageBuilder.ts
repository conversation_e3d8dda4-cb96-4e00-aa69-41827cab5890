import { Page } from "./Page"
import { Session } from "@curefit/userinfo-common"
import { UserContext } from "@curefit/userinfo-common"

export type Tab = "CENTER" | "DIY" | "EVENTS"

interface IPageBuilder {
    build: (userContext: UserContext, session: Session, lat: number, lon: number, appVersion?: number, os?: string, codePushVersion?: number, selectedTab?: (Tab | number), isOnlySingles?: boolean) => Promise<Page>
}

export default IPageBuilder
