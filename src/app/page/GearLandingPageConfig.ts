import { Banner, Category, GearCollection } from "./PageWidgets"
import { inject, injectable } from "inversify"
import { DEFAULT_CACHE_REFRESH_TIME_IN_SECONDS, InMemoryCacheService } from "@curefit/memory-cache"
import { IPageConfigReadOnlyDao, IPageConfigReadWriteDao, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { Action } from "../common/views/WidgetView"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
class GearLandingPageConfig extends InMemoryCacheService<any> {

    constructor(
        @inject(PAGE_CONFIG_TYPES.PageConfigReadOnlySecondaryDao) protected pageConfigDao: IPageConfigReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) protected logger: ILogger
    ) {
        super(logger, 33 * 60)
        this.load("GearLandingPageConfig")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    loadData(): Promise<any> {
        return this.pageConfigDao.findOne({ pageId: { $eq: this.getPageConfigName() } }).then(pageConfig => {
            const data = pageConfig.data
            this.categories = data.categories
            this.collections = data.collections
            this.prebuzzConfig = data.prebuzzConfig
            return data
        })
    }

    getPageConfigName(): string {
        return "GearLandingPageConfig"
    }

    public categories: Category[]
    public collections: GearCollection[]
    public prebuzzConfig: PrebuzzConfig

}

interface PrebuzzConfig {
    timer?: {
        timerEndTime: Date
        title?: string
        action?: Action
    }
    banners?: GearCarouselBanner[]
    headerBanner?: GearCarouselBanner
}

export interface GearCarouselBanner {
    data: Banner[]
    desktopBannerRatio?: string
    mobileBannerRatio?: string
    maxNumBanners?: number
    edgeToEdge?: boolean
}

export default GearLandingPageConfig
