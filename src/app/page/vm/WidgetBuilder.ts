import { BASE_TYPES, FetchUtilV2, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as express from "express"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    IBaseWidget,
    IPageService,
    ISegmentService,
    IServiceInterfaces,
    IWidgetBuilder,
    UserContext,
    WidgetInstance,
    WidgetWithMetric
} from "@curefit/vm-models"
import { PositionOverride, Segment, TechOwners, WidgetTemplate } from "@curefit/vm-common"
import { getWidgetByType } from "./WidgetMap"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { PromUtil } from "../../../util/PromUtil"
import { WidgetRankingBusiness } from "../../cron/widgetRanking/WidgetRankingBusiness"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { GenericError } from "@curefit/error-client"
import CFAPIJavaService from "../../CFAPIJavaService"
import AppUtil from "../../util/AppUtil"
import tracer from "dd-trace"
import { promiseWithTimeout } from "@curefit/util-common"
import { Severity } from "@sentry/types"
import { Tenant } from "@curefit/user-common"

const clone = require("clone")
const async = require("async")

@injectable()
class WidgetBuilder implements IWidgetBuilder {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.PromUtil) private promUtil: PromUtil,
        @inject(CUREFIT_API_TYPES.WidgetRankingBusiness) private widgetRankingBusiness: WidgetRankingBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.CFAPIJavaService) private cFAPIJavaService: CFAPIJavaService,
        @inject(BASE_TYPES.FetchUtilV2) protected fetchHelper: FetchUtilV2,
    ) { }

    async buildWidgets(widgetIds: string[], interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, widgetPositionOverrideMap: { [widgetId: string]: PositionOverride }, sharedData?: any, req?: express.Request): Promise<{ widgets: WidgetWithMetric[] }> {
        const widgetInstances: WidgetInstance[] = await this.pageService.getWidgets(widgetIds, AppUtil.getTenantFromUserContext(userContext))
        const appTenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const widgetPositionMap: { [widgetId: string]: Array<number> } = {}
        const javaWidgetInstances: WidgetInstance[] = []
        const nodeWidgetInstances: WidgetInstance[] = []
        widgetInstances.forEach((widgetInstance, index) => {
            if (widgetInstance && !_.isEmpty(widgetInstance.widgetData)) {
                if (!widgetPositionMap[widgetInstance.widgetId]) {
                    widgetPositionMap[widgetInstance.widgetId] = []
                }
                widgetPositionMap[widgetInstance.widgetId].push(index)
                if ((<any>widgetInstance.widgetData).isJavaWidget) {
                    javaWidgetInstances.push(widgetInstance)
                } else {
                    nodeWidgetInstances.push(widgetInstance)
                }
            }
        })
        let javaWidgetIds: string[] = []
        let javaWidgetResponsePromise
        if (!_.isEmpty(javaWidgetInstances)) {
            javaWidgetIds = javaWidgetInstances.map(widgetInstance => widgetInstance.widgetId)
            this.logger.debug("java widgetIds: ", {javaWidgetIds})
            try {
                javaWidgetResponsePromise = this.cFAPIJavaService.getWidgets(javaWidgetIds, queryParams, appTenant, req)
            } catch (error) {
                this.logger.error(`Error building java widget: `, {error})
                this.rollbarService.sendError(error)
            }
        }
        const widgetPromises = nodeWidgetInstances
            .map((widgetInstance, idx) => {
                return tracer.trace("widget.render", { resource: `/widget/${widgetInstance.widgetId}` }, () => {
                    return this.buildWidgetTimed(widgetInstance, idx, widgetInstances, interfaces, userContext, queryParams, widgetPositionOverrideMap, sharedData)
                })
            })

        const widgets: (WidgetWithMetric | WidgetWithMetric[])[] = (await Promise.all(widgetPromises))
        let javaWidgetResponse
        if (!_.isEmpty(javaWidgetIds)) {
            try {
                javaWidgetResponse = await javaWidgetResponsePromise
            } catch (error) {
                this.logger.error(`Error building java widget: `, {error})
                this.rollbarService.sendError(error)
            }

            if (javaWidgetResponse && !_.isEmpty(javaWidgetResponse.widgets)) {
                widgets.push(...javaWidgetResponse.widgets)
            }
        }

        const { flatWidgets, positionOverrideWidgets } = this.flattenWidgets(widgets, widgetPositionOverrideMap, javaWidgetResponse && !_.isEmpty(javaWidgetResponse.widgets) ? widgetPositionMap : {})
        const finalWidgets = this.orderWidgetsBasedOnPositionOverride(flatWidgets, positionOverrideWidgets, widgetPositionOverrideMap)
        return {
            widgets: finalWidgets
        }
    }

    async buildWidgetTimed(widgetInstance: WidgetInstance, idx: number, widgetInstances: WidgetInstance[], interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, widgetPositionOverrideMap: { [widgetId: string]: PositionOverride }, sharedData?: any): Promise<WidgetWithMetric | WidgetWithMetric[]> {
        try {
            const widgetRenderTimeoutMillis = widgetInstance?.sla?.timeoutMillis?.fatal ?? 3000
            const task = async () => {
                const failureMessage = `render timeout for widgetId: ${widgetInstance?.widgetId}`
                const widgetRenderPromise = this.buildWidget(widgetInstance, idx, widgetInstances, interfaces, userContext, queryParams, widgetPositionOverrideMap, sharedData)
                return await promiseWithTimeout(widgetRenderTimeoutMillis, widgetRenderPromise, failureMessage)
            }
            return await async.retry({
                times: 3,
                interval: 0,
                errorFilter: (err: any) => {
                    this.reportWidgetRenderError(userContext, widgetInstance, sharedData?.pageId, err)
                    return true
                }
            },
                task)
        } catch (error) {
            this.reportWidgetRenderError(userContext, widgetInstance, sharedData?.pageId, error)
        }
        return undefined
    }

    async reportWidgetRenderError(userContext: UserContext, widgetInstance: WidgetInstance, pageId: string, error: any) {
        const widgetRenderTimeoutMillis = widgetInstance?.sla?.timeoutMillis?.fatal ?? 3000
        const slaOwner = widgetInstance?.sla?.owner ?? TechOwners.APPINFRA
        this.logger.error(`render failed for widgetId: ${widgetInstance?.widgetId}`, error)
        this.rollbarService.sendError(error, {
            fingerprint: [`widgetId:${widgetInstance?.widgetId}`],
            extra: {
                widgetId: widgetInstance?.widgetId,
                widgetRenderTimeoutMillis,
                userId: userContext.userProfile.userId
            },
            tags: {
                "sla_owner": slaOwner,
                "widgetId": widgetInstance?.widgetId ?? "",
                "pageId": pageId ?? ""
            }
        })
    }

    async buildWidget(widgetInstance: WidgetInstance, idx: number, widgetInstances: WidgetInstance[], interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, widgetPositionOverrideMap: { [widgetId: string]: PositionOverride }, sharedData?: any): Promise<WidgetWithMetric | WidgetWithMetric[]> {
        const pageId: string = sharedData?.pageId ?? ""

        const isSampledWidgetImpressionRequired = Math.floor(Math.random() * 100) === 0

        const widgetBuildStartTime = process.hrtime()
        const widgetClass = getWidgetByType(widgetInstance.widgetType)
        let exceptionClass = "none"
        const slaOwner = widgetInstance?.sla?.owner ?? "none"
        try {
            const now = new Date()
            if (process.env.APP_ENV !== "ALPHA" && widgetInstance.status && widgetInstance.status !== "LIVE") {
                return undefined
            }
            if (process.env.APP_ENV === "ALPHA" && widgetInstance.status && widgetInstance.status !== "LIVE" && widgetInstance.status !== "DOGFOODING") {
                return undefined
            }
            if (widgetInstance.startTimeWithTz && now.getTime() < new Date(widgetInstance.startTimeWithTz.date).getTime()) {
                return undefined
            }
            if (widgetInstance.endTimeWithTz && now.getTime() > new Date(widgetInstance.endTimeWithTz.date).getTime()) {
                return undefined
            }
            const widgetPromise = this.pageService.getWidgetClass(widgetInstance, widgetClass)

            let segmentPromise: Promise<Segment>
            if (!_.isEmpty(widgetInstance.segmentIds)) {
                segmentPromise = this.segmentService.doesUserBelongToAnySegment(widgetInstance.segmentIds, userContext)
            }
            if (!_.isEmpty(queryParams) && queryParams.successiveFetch === "true" && idx < widgetInstances.length - 1) {
                return undefined
            }
            queryParams = _.isNil(queryParams) ? {} : { ...queryParams }
            const clonedQueryParams = clone(queryParams)
            clonedQueryParams["widgetId"] = widgetInstance.widgetId

            const segment = await segmentPromise
            // If user does not belong to any of the tagged segment, skip this widget
            if (segmentPromise && !segment) {
                return undefined
            }
            if (pageId === "gymfitList") {
                widgetInstance.pageType = "gymsclp"
            }
            const widget: IBaseWidget = await widgetPromise
            if (!widget) {
                const genericErr: GenericError = new GenericError({ message: "Error instantiating widget" })
                genericErr.statusCode = 500
                throw genericErr
            }
            const result = await widget.buildView(interfaces, userContext, clonedQueryParams, sharedData)

            if (!_.isNil(result) && _.isArray(result)) {
                return await Promise.all(_.map(result, async (widget, index) => {
                    if (!_.isNil(widget)) {
                        const widgetWithLayoutProps = await this.configureLayoutPropsForWidget(widget, userContext)
                        return this.decorateWidgetWithMetric(isSampledWidgetImpressionRequired, widgetInstance, widgetWithLayoutProps, segment, index)
                    } else {
                        return undefined
                    }
                }))
            } else if (!_.isNil(result)) {
                const widgetWithLayoutProps = await this.configureLayoutPropsForWidget(result, userContext)
                return this.decorateWidgetWithMetric(isSampledWidgetImpressionRequired, widgetInstance, widgetWithLayoutProps, segment)
            }
        } catch (error) {
            this.rollbarService.sendError(error, {
                fingerprint: [`widgetId:${widgetInstance?.widgetId}`],
                extra: {
                    widgetId: widgetInstance?.widgetId,
                    userId: userContext.userProfile.userId
                }
            })
            this.logger.error(`Error building widget ${widgetInstance.description} statusCode:${error.statusCode}, name:${error.name}, title:${error.title}, message:${error.message}, trace:${error.stack}`)
            exceptionClass = error.name
            return undefined
        } finally {
            this.promUtil.reportWidgetRenderMetrics(widgetInstance.widgetId, pageId, exceptionClass, slaOwner, process.hrtime(widgetBuildStartTime)[1] + process.hrtime(widgetBuildStartTime)[0] * 1e9)
        }
    }


    // async getWidgetRankingMap(userId: string): Promise<Map<string, number>> {
    //     const date = TimeUtil.todaysDateWithTimezone("UTC", "YYYY-MM-DD")
    //     const hourOfDay = TimeUtil.now("UTC").hour
    //     const widgetRankingPromise = eternalPromise(this.widgetRankingBusiness.getWidgetRanking(userId, date, hourOfDay))
    //     const widgetRankingResult = await widgetRankingPromise
    //     const widgetRankingMap: Map<string, number> = new Map<string, number>()
    //     if (widgetRankingResult.obj) {
    //         const rankingDetail = JSON.parse(widgetRankingResult.obj.rankingDetail)
    //         Object.keys(rankingDetail).forEach(widgetId => {
    //             widgetRankingMap.set(widgetId, rankingDetail[widgetId].rankOfWidget)
    //         })
    //     } else if (widgetRankingResult.err) {
    //         this.logger.error("Error getting widget ranking details " + widgetRankingResult.err)
    //         this.rollbarService.sendError(widgetRankingResult.err)
    //     }
    //     return widgetRankingMap
    // }

    // private async orderWidgetsBasedOnWidgetRank(userId: string, widgets: WidgetWithMetric[], widgetRankingMap: Map<string, number>, widgetInstanceMap: Map<string, WidgetInstance>) {
    //     widgets.sort((widgetA, widgetB) => {
    //         // Splitting with "/" to accomondate the array widgets where id is appended with array index.
    //         const widgetIdA = widgetA.widgetMetric.widgetId.split("/")[0]
    //         const widgetIdB = widgetB.widgetMetric.widgetId.split("/")[0]

    //         const widgetInstanceA = widgetInstanceMap.get(widgetIdA)
    //         const widgetInstanceB = widgetInstanceMap.get(widgetIdB)

    //         // If either of the widget are fixed position widget don't change ordering
    //         if (widgetInstanceA.isFixedPositionWidget || widgetInstanceB.isFixedPositionWidget) {
    //             return 0
    //         }

    //         const widgetRankingA = widgetRankingMap.get(widgetIdA)
    //         const widgetRankingB = widgetRankingMap.get(widgetIdB)
    //         // If both has ranking information use that for sorting or else retain the position as is
    //         if (widgetRankingA && widgetInstanceB) {
    //             const diff = widgetRankingA - widgetRankingB
    //             return diff > 0 ? 1 : diff < 0 ? -1 : 0
    //         } else {
    //             return 0
    //         }
    //     })
    //     return widgets
    // }


    private orderWidgetsBasedOnPositionOverride(flatWidgets: WidgetWithMetric[], positionOverrideWidgets: WidgetWithMetric[], widgetPositionOverrideMap: { [widgetId: string]: PositionOverride }) {
        if (!_.isEmpty(positionOverrideWidgets)) {
            positionOverrideWidgets.sort((widgetA, widgetB) => {
                const widgetAPos = widgetPositionOverrideMap[widgetA.widgetMetric.widgetId].position
                const widgetBPos = widgetPositionOverrideMap[widgetB.widgetMetric.widgetId].position
                const diff = widgetAPos - widgetBPos
                return diff > 0 ? 1 : diff < 0 ? -1 : 0
            })
            positionOverrideWidgets.forEach((overrideWidget) => {
                const positionOverride = widgetPositionOverrideMap[overrideWidget.widgetMetric.widgetId].position
                if (positionOverride < flatWidgets.length) {
                    flatWidgets.splice(positionOverride, 0, overrideWidget)
                } else {
                    flatWidgets.push(overrideWidget)
                }
            })
        }
        return flatWidgets
    }

    private flattenWidgets(widgets: (WidgetWithMetric | WidgetWithMetric[])[], widgetPositionOverrideMap: { [widgetId: string]: PositionOverride }, widgetPositionMap: { [widgetId: string]: Array<number> }) {
        const flatWidgets: WidgetWithMetric[] = []
        const subWidgets: WidgetWithMetric[] = []
        const positionOverrideWidgets: WidgetWithMetric[] = []
        _.forEach(widgets, (widgetOrArr) => {
            if (!_.isNil(widgetOrArr) && _.isArray(widgetOrArr)) {
                widgetOrArr.forEach((widget) => {
                    if (!_.isNil(widget)) {
                        if (widgetPositionOverrideMap && widgetPositionOverrideMap[widget.widgetMetric.widgetId])
                            positionOverrideWidgets.push(widget)
                        else if (widgetPositionMap[widget.widgetMetric.widgetId]?.length) {
                            flatWidgets[widgetPositionMap[widget.widgetMetric.widgetId][0]] = widget // Assuming two positions can never be same.
                            widgetPositionMap[widget.widgetMetric.widgetId].shift()
                        }
                        else
                            subWidgets.push(widget)
                    }
                })
            } else if (!_.isNil(widgetOrArr)) {
                if (widgetPositionOverrideMap && widgetPositionOverrideMap[widgetOrArr.widgetMetric.widgetId])
                    positionOverrideWidgets.push(widgetOrArr)
                else if (widgetPositionMap[widgetOrArr.widgetMetric.widgetId]?.length) {
                    flatWidgets[widgetPositionMap[widgetOrArr.widgetMetric.widgetId][0]] = widgetOrArr
                    widgetPositionMap[widgetOrArr.widgetMetric.widgetId].shift()
                }
                else
                    subWidgets.push(widgetOrArr)
            }
        })
        return { flatWidgets: [...flatWidgets.filter((widget) => (!!widget && !!widget.widgetMetric.widgetId)), ...subWidgets], positionOverrideWidgets: positionOverrideWidgets }
    }

    private decorateWidgetWithMetric(isSampledWidgetImpressionRequired: boolean, widgetInstance: WidgetInstance, widget: IBaseWidget, segment: Segment, index?: number): WidgetWithMetric {
        const decoratedWidget: WidgetWithMetric = <WidgetWithMetric>widget
        decoratedWidget.widgetMetric = {
            widgetId: widgetInstance.widgetId + (index ? "/" + index : ""),
            widgetName: widgetInstance.description,
            widgetType: widget.widgetType,
            widgetSegmentId: segment ? segment.segmentId : undefined,
            widgetSegmentName: segment ? segment.name : undefined,
            isWidgetImpressionRequired: widgetInstance.isWidgetImpressionRequired,
            pageType: widgetInstance?.pageType || undefined,
            productId: widget.eventData?.productId,
            isSampledWidgetImpressionRequired: isSampledWidgetImpressionRequired
        }
        return decoratedWidget
    }

    private async configureLayoutPropsForWidget(widget: IBaseWidget, userContext: UserContext) {
        // All Widgets will have template and so configure the layout props here

        const { sessionInfo } = userContext
        const widgetWithLayoutProps: WidgetWithMetric = <WidgetWithMetric>widget
        let template: WidgetTemplate

        if (widgetWithLayoutProps.layoutProps && widgetWithLayoutProps.layoutProps.spacing) {
            return widget
        }

        if (!_.isNil(widget.templateId)) {
            template = await this.pageService.getWidgetTemplate(widget.templateId)
        } else {
            // Set Default Template
            template = await this.pageService.getWidgetTemplate("BaseSpacingTemplate")
        }

        if (!widgetWithLayoutProps.layoutProps) {
            widgetWithLayoutProps.layoutProps = {}
        }

        if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
            // For templates which have still not set spacing, as app expects spacing
            if (template?.templateData?.app?.spacing) {
                widgetWithLayoutProps.layoutProps.spacing = template.templateData.app.spacing
            } else {
                widgetWithLayoutProps.layoutProps.spacing = { top: "0", bottom: "70" }
            }
        }

        // Added layout props for desktop only for cultsport, incase it is required for all desktop, we can remove cultsport app check
        if (sessionInfo.userAgent === "DESKTOP" && AppUtil.isCultSportWebApp(userContext)) {
            // For templates which have still not set spacing, as app expects spacing
            if (template?.templateData?.web?.spacing) {
                widgetWithLayoutProps.layoutProps.spacing = template.templateData.web.spacing
            } else {
                widgetWithLayoutProps.layoutProps.spacing = { top: "0", bottom: "70" }
            }
        }
        return widgetWithLayoutProps
    }

}

export default WidgetBuilder
