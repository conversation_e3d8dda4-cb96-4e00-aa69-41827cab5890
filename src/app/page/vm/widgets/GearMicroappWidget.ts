import * as _ from "lodash"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"

interface MicroappViewConfig {
  type: "SCREEN"
  key: string
}

interface MicroappConfig {}

interface MicroappWidgetConfig {
  initView?: MicroappViewConfig
  config?: MicroappConfig
}

export class GearMicroappWidget extends BaseWidget {
    initView: MicroappViewConfig
    config: MicroappConfig

    constructor(widgetConfig: MicroappWidgetConfig) {
      super("GEAR_MICROAPP_WIDGET")
      this.initView = widgetConfig?.initView || { type : "SCREEN", key : "HomeScreen" }
      this.config = widgetConfig?.config
    }

    async buildView(): Promise<IBaseWidget> {
      return this
    }
}
