import { IBaseWidget, ProductSelectionWidget, SelectionItem } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { GymfitListingChannels, GymfitListingType } from "@curefit/gymfit-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import AppUtil from "../../../../util/AppUtil"
import { Namespace, OfflineFitnessPack, ProductSubType, Visibility } from "@curefit/pack-management-service-common"
import CultUtil from "../../../../util/CultUtil"

export class ProductSelectionWidgetView extends ProductSelectionWidget {

    buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_PACK") {
            return new GymfitPackViewBuilder(this).buildView(interfaces, userContext, queryParams)
        }
    }
}

export class GymfitPackViewBuilder extends ProductSelectionWidget {

    constructor(productSelectionWidget: ProductSelectionWidget) {
        super()
        this.productType = productSelectionWidget.productType
        this.header = productSelectionWidget.header
        this.dividerType = productSelectionWidget.dividerType
        this.data = productSelectionWidget.data
        this.footer = productSelectionWidget.footer
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const gymfitPacks: OfflineFitnessPack[] = await interfaces.offlineFitnessPackService.searchCachedPacks({
            namespace: Namespace.OFFLINE_FITNESS,
            productTypes: ["GYMFIT_FITNESS_PRODUCT"],
            restrictions: {cities: [userContext.sessionInfo.sessionData.cityId]},
            productSubType: ProductSubType.GENERAL,
            status: "ACTIVE",
            saleEnabled: true,
            visibility: AppUtil.isWeb(userContext) ? Visibility.WEBSITE : Visibility.APP
        })
        if (_.isEmpty(gymfitPacks)) {
            return undefined
        }
        const productIds = gymfitPacks.map(product => {
            return product.productId
        })
        const offers = await interfaces.offerServiceV3.getGymFitProductPrices({
            userInfo: {
                userId: userContext.userProfile.userId,
                deviceId: userContext.sessionInfo.deviceId,
            },
            productIds: productIds,
            source: "CUREFIT_APP",
            cityId: userContext.sessionInfo.sessionData.cityId
        })
        this.data = gymfitPacks.map(pack => {
            const offerDetails = CultUtil.getOfferDetailsPMS(pack, offers)
            const packItem: SelectionItem = {
                title: Math.floor(pack.product.durationInDays / 30) + " months",
                price: {
                    listingPrice: offerDetails.price.listingPrice,
                    mrp: offerDetails.price.mrp === offerDetails.price.listingPrice ? undefined : offerDetails.price.mrp,
                    currency: offerDetails.price.currency
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.gymfitPack(pack.productId)
                },
                contentMetric: undefined
            }
            return packItem

        })
        this.footer = {
            title: "GET PACK",
            subTitle: undefined,
            seemore: {actionType: "NAVIGATION", url: ActionUtil.gymfitPack(gymfitPacks[0].productId)}
        }
        return this
    }

}
