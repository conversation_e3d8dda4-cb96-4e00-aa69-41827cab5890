import { ClpCalloutWidget, CultBuddiesJoiningListSmallView } from "./../../../../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { Action as VMAction } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { FulfilmentMetaInfo } from "@curefit/order-common"

export type AllAction = Action
export interface CardListItemLeftInfo {
    images?: string[]
    title?: string,
}

export interface CardListItemRightInfo {
    title?: string
    subTitle?: string
    icon?: "LOG"
    action: AllAction
    moreAction: AllAction
}
export interface CardListItem {
    header?: {
        title: string
    }
    title: string
    subTitle: string
    action: AllAction
    leftInfo: CardListItemLeftInfo
    rightInfo: CardListItemRightInfo
    viewType?: CardListItemViewType,
    note?: string
}

export interface CardListItemWithTimestamp extends CardListItem {
    date: string
    timestamp: number
}

export interface UpcomingItem extends CardListItemWithTimestamp {
    calloutInfo?: ClpCalloutWidget
    footer?: UpcomingItemFooter,
    footers?: UpcomingItemFooter[],
    meta?: FulfilmentMetaInfo
    buddiesJoining?: CultBuddiesJoiningListSmallView
    refreshCardEpoch?: number
}

export interface UpcomingItemFooter {
    title: string
    subTitle?: string
    icon?: string
    action?: Action
    tooltip?: UpcomingItemTooltip
    value?: string
    iconAction?: Action
}

export interface UpcomingItemTooltip {
    title: string,
    text: string
}

export type CardListContentType = "UPCOMING_ACTIVITIES" | "DIY_RECOMMENDATIONS" | "MEDICAL_REPORTS" |
    "FITNESS_REPORTS" | "TRACK_HABITS" | "CLASS_RECOMMENDATIONS" | "FOOD_RECOMMENDATIONS" | "CHALLENGES"
    | "COMPLETED_ACTIVITES" | "LIVE_ACTIVITIES" | "OFFER_CALLOUT_WIDGET" | "LIVE_RECOMMENDATIONS"

export type CardListItemViewType = "CHALLENGE_INVITE" | "DOCTOR_UNAVAILABLE"

export interface CardListWidgetFooter {
    contentType: "VIEW_MORE_FOOTER" | "MESSAGE_FOOTER"
    title: string
    action?: Action
}
export type CardListDataItemType = CardListItem | UpcomingItem
export const DEFAULT_PAGINATION_START = 0

export function getDefaultPaginationCount(appVersion: number) {
    if (appVersion < 7.80) {
        return 10
    } else {
        return 4
    }
}
export class CardListWidgetView extends BaseWidget {

    title: string
    contentType: CardListContentType
    data: CardListDataItemType[]
    calloutWidget: any
    maxItemsToDisplay: number
    footer?: CardListWidgetFooter
    nextUrl?: string // For pagination
    currentUrl?: string
    sceneStyle?: any
    constructor(title: string, contentType: CardListContentType, data: CardListDataItemType[], footer?: CardListWidgetFooter, nextUrl?: string, calloutWidget?: any, sceneStyle?: any) {
        super("CARD_LIST_WIDGET")
        this.title = title
        this.contentType = contentType
        this.calloutWidget = calloutWidget
        this.data = data
        const maxCount = contentType === "UPCOMING_ACTIVITIES" ? 2 : 3
        this.maxItemsToDisplay = maxCount
        this.footer = footer
        this.nextUrl = nextUrl
        this.sceneStyle = sceneStyle
        if (data.length > maxCount || nextUrl) {
            this.footer = {
                contentType: "VIEW_MORE_FOOTER",
                title: contentType === "COMPLETED_ACTIVITES" ? "More" : `+${(data.length - maxCount)} More`
            }
        }
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        this.currentUrl = `/page/widget?widgetId=${queryParams.widgetId}&start=${queryParams.start || DEFAULT_PAGINATION_START}&count=${queryParams.count || getDefaultPaginationCount(userContext.sessionInfo.appVersion)}`
        return this
    }

}
