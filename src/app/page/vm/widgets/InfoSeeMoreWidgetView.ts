import * as _ from "lodash"
import { InfoSeeMoreWidget } from "@curefit/vm-models"
import { IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/vm-models"
import { Center } from "@curefit/care-common"

const clone = require("clone")

export class InfoSeeMoreWidgetView extends InfoSeeMoreWidget {

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (this.dynamicInfoType && this.dynamicInfoType === "CENTERS_INFO" && this.productType === "PERSONAL_TRAINING") {
            return new PTCentersInfoSeeMoreWidget(this).buildView(interfaces, userContext, queryParams)
        }
        return this
    }
}

const CENTERS_TO_BE_SHOWN_UPFRONT = 2

export class PTCentersInfoSeeMoreWidget extends InfoSeeMoreWidgetView {
    title: string
    description: string
    seemore: Action

    constructor(widget: InfoSeeMoreWidgetView) {
        super()
        this.header = widget.header
        this.dividerType = widget.dividerType
        this.title = widget.title
        this.seemore = widget.seemore
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const cityCode = userContext.userProfile.cityId
        const centers = await interfaces.cultPTService.getAllCenters("CONSULTATION", "CF_INCENTRE_CONSULTATION", "CONS_CULT_PT", "CULTFIT", cityCode)
        if (!_.isEmpty(centers)) {
            this.description = "Currently available in "
            const totalCenters = centers.length
            if (totalCenters <= CENTERS_TO_BE_SHOWN_UPFRONT) {
                this.description += _.join(_.map(centers, center => center.name), ", ")
                this.seemore = undefined // dont send see more if centers are less than 3
            } else {
                const trimmedCenters = _.slice(clone(centers) as Center[], 0, CENTERS_TO_BE_SHOWN_UPFRONT)
                this.description += _.join(_.map(trimmedCenters, center => center.name), ", ") + "..."
                this.seemore.title = `${totalCenters - CENTERS_TO_BE_SHOWN_UPFRONT} More`
            }
            return this
        }
    }
}
