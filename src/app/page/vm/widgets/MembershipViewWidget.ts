import { ActiveConsultationResponse } from "@curefit/albus-client"
import { MembershipWidget, Action, PlayAction } from "@curefit/vm-models"
import { IBaseWidget, IServiceInterfaces, UserContext, UserProfile, CareWidgetUtil } from "@curefit/vm-models"
import { ActiveBundleOrderDetail } from "@curefit/albus-client"
import { TimeUtil, Timezone } from "@curefit/util-common"
import {
    ConsultationProduct,
    DiagnosticProduct,
    SKIN_HAIR_SUBCATEGORIES,
    CareMembershipType,
    LIVE_PT_DOCTOR_TYPES,
    CONSULTATION_PACK_SUBCATEGORIES,
    Patient
} from "@curefit/care-common"
import { ProductType } from "@curefit/product-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { OfferUtil } from "@curefit/base-utils"
import {
    ActionV2,
    BookingDetail,
    DOCTOR_TYPE,
    MPChildProduct,
    SUB_CATEGORY_CODE,
    ConsultationSellableProductItem,
    ConsultationSellableProductResponse
} from "@curefit/albus-client"
import { CareUtil, NEW_MIND_THERAPY_SPECIALITY_BOOKING_PAGE_SUPPORTED, DOCTOR_UNAVAILABLE_SHORTER_TEXT } from "../../../util/CareUtil"
import * as momentTz from "moment-timezone"
import * as moment from "moment"
import * as _ from "lodash"
import { IFindQuery, SortOrder } from "@curefit/mongo-utils"
import { ClientMembershipState } from "@curefit/cult-client"
import { CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import { pad } from "@curefit/util-common"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import CultUtil, { transformCultSummaryMap, UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD, CULT_CLASS_BOOK, CULT_FREE_CLASS_BOOK } from "../../../util/CultUtil"
import { ComplimentaryAccess, MembershipDetails, MembershipStates } from "@curefit/cult-common"
import { PackOffersResponse } from "@curefit/offer-common"
import { CFUserProfile } from "../CFUserProfile"
import AppUtil from "../../../util/AppUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { ActionUtil as AppActionUtil } from "../../../util/ActionUtil"
import { Action as WidgetAction, PauseInfo } from "../../../common/views/WidgetView"
import { NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED } from "@curefit/base-utils"
import { AnnouncementDetails } from "../../../announcement/AnnouncementViewBuilder"
import { GymfitMembership } from "@curefit/gymfit-common"
import LiveUtil from "../../../util/LiveUtil"
import AtlasUtil from "../../../util/AtlasUtil"
import { LivePackUtil } from "../../../util/LivePackUtil"
import GymfitUtil from "../../../util/GymfitUtil"
import { Membership } from "@curefit/membership-commons"
import CatalogueServiceUtilities from "../../../util/CatalogueServiceUtilities"
const LAB_TEST_PACKS_SUB_CATEGORY_CODE = ["HCU_PACK", "DIAG_PACK"]

export class MembershipViewWidget extends MembershipWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        // Backwards Compatibility
        if (this.showDivider === true && _.isUndefined(this.dividerType)) {
            this.dividerType = "LARGE"
        }

        if (this.productType === "FITNESS" || this.productType === "MIND") {
            return new CenterMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "CULT_COMPLEMENTARY_ACCESS" || this.productType === "MIND_COMPLEMENTARY_ACCESS") {
            return new ComplementaryAccessViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "DIY_FITNESS" || this.productType === "DIY_MEDITATION") {
            return new DIYMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "THERAPY") {
            return new TherapyMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "PERSONAL_TRAINING" || (this.productType === "BUNDLE" && this.subCategoryCode === "PHYSIOTHERAPY")) {
            return new PTMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "BUNDLE") {
            return new BundleMemebshipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "HCU_PACK" || this.productType === "PHYSIOTHERAPY_PACK" || this.productType === "CONSULTATION") {
            return new ActiveBundleAndConsultationMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "GYMFIT_FITNESS_PRODUCT") {
            return new GymfitMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        }
        return undefined
    }
}

export interface MembershipInfo {
    title: string
    subTitle?: string
    icon?: string
    meta?: string
    buttonAction?: Action | WidgetAction,
    memberNameString?: string,
    metaAction?: Action // to show action along with meta text
}
export interface MembershipData {
    membershipInfo: MembershipInfo
    membershipState: ClientMembershipState | MembershipStates | CareMembershipType
    lineItems: {
        title?: string
        subTitle?: string,
        icon?: string,
        iconBackgroundColor?: string,
        action?: Action,
        showDivider?: boolean
    }[]
    lineItemsAction?: WidgetAction | Action
    calloutText?: string
    action: Action
    actionUrl?: string
}

export class CenterMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const cultMindSummaryMapPromise = (userContext.userProfile as CFUserProfile).cultMindSummaryMap
        const findQuery: IFindQuery = {
            condition: {
                userId: userContext.userProfile.userId,
                score: { "$gte": 1 },
                activityType: this.productType === "FITNESS" ? "CULT_CLASS" : "MIND_CLASS",
                date: { $lte: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone) } // To force use the date + userid index as its not using the sort index properly
            },
            sortField: "date",
            count: 1,
            sortOrder: SortOrder.DESC
        }
        const activitiesPromise = interfaces.activityStoreDao.find(findQuery)
        const cultOrMindSummaryMap = transformCultSummaryMap(await cultMindSummaryMapPromise, (this.productType === "FITNESS" ? "CULT_FIT" : "MIND_FIT"))
        const activities = await activitiesPromise
        const lastAttendedClass = !_.isEmpty(activities) ? activities[0] : undefined

        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const todayDateString = TimeUtil.todaysDateWithTimezone(tz)
        const currentUser = await interfaces.userCache.getUser(userContext.userProfile.userId)
        if (_.isEmpty(cultOrMindSummaryMap)) {
            return undefined
        }
        const { cult, mind } = await interfaces.cultBusiness.isBookFreeCultClassSupported(userContext)
        const freeTrial = this.productType === "MIND" ? mind : cult
        const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(userContext, currentUser.isInternalUser)
        let primaryUserClassBookAction: Action = {
            actionType: "NAVIGATION",
            title: freeTrial ? CULT_FREE_CLASS_BOOK : CULT_CLASS_BOOK,
            url: ActionUtil.getBookCultClassUrl(<ProductType>this.productType, isNewClassBookingSuppoted, "cultCLP", undefined)
        }
        const cultAndMindSummary = await interfaces.userCache.getCultSummary(userContext.userProfile.userId)
        const isNuxSupported = await AppUtil.isNUXSupported(userContext, currentUser.isInternalUser, <ProductType>this.productType, interfaces.hamletBusiness)
        if (isNuxSupported && CultUtil.isCompletedClassCountZero(cultAndMindSummary)) {
            const announcementDetails = <AnnouncementDetails>await interfaces.announcementBusiness.createAndGetAnnouncementDetails(userContext, `NUX_LANDING_PAGE`)
            if (announcementDetails.state === "CREATED") {
                primaryUserClassBookAction = {
                    actionType: "NAVIGATION",
                    url: "curefit://nuxlandingpage",
                    title: "Book",
                }
            }
        }
        let allSubUserMembershipDataPromises: Promise<MembershipData>[] = []
        _.forEach(Object.keys(cultOrMindSummaryMap), userId => {
            // push memberships to be built
            const memberships: MembershipDetails[] = []
            const cultOrMindSummary = cultOrMindSummaryMap[userId]
            if (_.isNil(cultOrMindSummary.currentMembership) && _.isNil(cultOrMindSummary.upcomingMembership)
                && !_.isNil(cultOrMindSummary.previousMembership)) {
                // render past membership only if no current or future membership
                memberships.push(cultOrMindSummary.previousMembership)
            } else {
                if (!_.isNil(cultOrMindSummary.currentMembership)) {
                    memberships.push(cultOrMindSummary.currentMembership)
                }
                if (!_.isNil(cultOrMindSummary.upcomingMembership)) {
                    memberships.push(cultOrMindSummary.upcomingMembership)
                }
            }
            const membershipDataPromisesForUser: Promise<MembershipData>[] = _.map(memberships, async (membershipDetails) => {
                const isExpired = cultOrMindSummary.previousMembership === membershipDetails
                const isUpcoming = cultOrMindSummary.upcomingMembership === membershipDetails
                const isUpcomingPause = membershipDetails.state === "ACTIVE" && !_.isEmpty(membershipDetails.pauseDetails)

                const endDate = TimeUtil.getMomentForDateString(membershipDetails.endDate, tz)
                const daysRemainingInMembership = TimeUtil.diffInDays(tz, membershipDetails.endDate, todayDateString)

                const productId = membershipDetails.productId
                const pack = await interfaces.catalogueServicePMS.getProduct(productId)

                const packBrowseAction = ActionUtil.cultPackBrowse(userContext.sessionInfo.userAgent, false, "ADULT") // PMS::TODO Migrate to new pack browse action
                const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membershipDetails.id.toString(), interfaces.membershipService)
                const packPageAction: Action = {
                    actionType: "NAVIGATION",
                    url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext)
                }

                // Building sub title based on pack state and  pack start , pack end date
                let subTitle = membershipDetails.state === "PAUSED" ? "Paused" : isExpired ? "Expired" : isUpcoming ? "Upcoming" : "Active"
                let detailedMembership
                if (membershipDetails.isSubscription) {
                    detailedMembership = this.productType === "FITNESS" ? await interfaces.cultFitService.getMembershipById(membershipDetails.id, userProfile.userId) :
                        await interfaces.mindFitService.getMembershipById(membershipDetails.id, userProfile.userId)
                }
                if (membershipDetails.state === "ACTIVE") {
                    if (isUpcomingPause) {
                        const startDateText = TimeUtil.diffInDays(tz, membershipDetails.pauseDetails.startDate, todayDateString) > 1 ? `on ${momentTz.tz(membershipDetails.pauseDetails.startDate, tz).format("DD MMM")}` : "tonight"
                        subTitle = `Pause starts ${startDateText}`
                    } else if (isExpired) {
                        subTitle = `${subTitle}, Ended on ${TimeUtil.formatDateStringInTimeZone(membershipDetails.endDate, tz, "DD MMM")}`
                    } else if (daysRemainingInMembership < 10) {
                        subTitle = `${subTitle}, ${daysRemainingInMembership} days left`
                    } else {
                        subTitle = `${subTitle}, Ending on ${TimeUtil.formatDateStringInTimeZone(membershipDetails.endDate, tz, "DD MMM")}`
                    }

                    // check if subscription is active and change sub title accordingly
                    if (membershipDetails.isSubscription) {
                        if (detailedMembership.userSubscription && detailedMembership.userSubscription.state === "ACTIVE") {
                            // const renewalDate = detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate
                            const renewalDate = TimeUtil.getMomentForDateString(detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate, tz)
                            if (renewalDate.isSameOrAfter(today) && !isUpcomingPause) {
                                subTitle = `${subTitle}, Renews on ${renewalDate.format("DD MMM")}`
                            }
                        }
                    }
                } else if (membershipDetails.state === "PAUSED") {
                    const pauseDate: string = membershipDetails.pauseDetails && membershipDetails.pauseDetails.maxEndDate
                    subTitle = pauseDate ? `${subTitle} till ${TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDate(pauseDate, tz), "DD MMM")}` : subTitle
                } else if (isUpcoming) {
                    subTitle = `Starting from ${TimeUtil.formatDateStringInTimeZone(membershipDetails.startDate, tz, "DD MMM")}`
                }
                const userDetails = await interfaces.userCache.getUser(userId)

                const membershipData: MembershipData = {
                    membershipInfo: {
                        title: pack.title,
                        subTitle: subTitle,
                        icon: "PLAY",
                        memberNameString: undefined
                    },
                    membershipState: membershipDetails.state,
                    action: packPageAction,
                    actionUrl: packPageAction.url,
                    lineItems: []
                }

                let juniorClassBookAction: WidgetAction
                if (AppUtil.isChildUserBookingSupported(userContext)) {
                    juniorClassBookAction = AppActionUtil.getClassBookingAction(userContext, <ProductType>this.productType, userDetails, undefined, "cultCLP")
                } else {
                    juniorClassBookAction = AppActionUtil.getJuniorClassBookingAppUpdateAction(userContext)
                }

                // Show last attended class for current ongoing pack if not show book class prompt
                if (lastAttendedClass && membershipDetails.state !== "PAUSED") {
                    const booking = this.productType === "FITNESS" ? await interfaces.cultFitService.getBookingById(lastAttendedClass.meta.fulfilmentId, lastAttendedClass.userId)
                        : await interfaces.mindFitService.getBookingById(lastAttendedClass.meta.fulfilmentId, lastAttendedClass.userId)

                    if (!isExpired) {
                        const numDays = TimeUtil.diffInDays(tz, lastAttendedClass.date, todayDateString)
                        membershipData.lineItems.push({
                            title: "LAST CLASS",
                            subTitle: numDays === 0 ? `${booking.Class.Workout.name}, today` : `${booking.Class.Workout.name}, ${numDays} days ago`
                        })
                        membershipData.lineItemsAction = primaryUserClassBookAction
                    }
                } else if (membershipDetails.state !== "PAUSED" && !isExpired) {
                    membershipData.lineItems.push({
                        subTitle: lastAttendedClass ? undefined : (this.productType === "FITNESS" ? `Book your first cult class now` : `Book your first mind class now`),
                    })
                    membershipData.lineItemsAction = primaryUserClassBookAction
                }

                if (membershipDetails.state === "PAUSED" || (isUpcomingPause && AppUtil.isCultPauseCancelSupported(userContext))) {
                    detailedMembership = this.productType === "FITNESS" ? await interfaces.cultFitService.getMembershipById(membershipDetails.id, userProfile.userId) :
                        await interfaces.mindFitService.getMembershipById(membershipDetails.id, userProfile.userId)
                    const packProductType = this.productType === "FITNESS" ? "FITNESS" : "MIND"
                    membershipData.membershipInfo.buttonAction = CultUtil.getMembershipUnpauseAction(detailedMembership, packProductType, userContext, (await userContext.userPromise).isInternalUser)
                }

                // For advanced paid pre registration cases
                if (membershipDetails.isPreReg) {
                    membershipData.membershipInfo.buttonAction = {
                        title: "Upgrade",
                        actionType: packPageAction.actionType,
                        url: packPageAction.url
                    }
                }

                // For Expired or about to expire Pack and when there is no future membership
                // remove renew action from junior pack
                if (_.isNil(cultOrMindSummary.upcomingMembership) && (isExpired || daysRemainingInMembership <= 10) && membershipDetails.state !== "PAUSED") {
                    membershipData.membershipInfo.buttonAction = {
                        title: "RENEW",
                        actionType: "NAVIGATION",
                        url: packBrowseAction
                    }
                    if (endDate.isBefore(today)) {
                        membershipData.lineItems = [{
                            title: `Renew your membership`
                        }]
                    }
                }

                const city = await interfaces.cityService.getCityById(userProfile.cityId)
                if (city.cultCityId !== membershipDetails.cityId) {
                    if (_.isNil(detailedMembership)) {
                        detailedMembership = this.productType === "FITNESS" ? await interfaces.cultFitService.getMembershipById(membershipDetails.id, userProfile.userId) :
                            await interfaces.mindFitService.getMembershipById(membershipDetails.id, userProfile.userId)

                    }
                    const city = await interfaces.cityService.getCityById(userProfile.cityId)
                    if (detailedMembership.freeAwayClassesRemainingForMonth < UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD) {
                        membershipData.calloutText = `${city.name} | ${detailedMembership.freeAwayClassesRemainingForMonth} ${detailedMembership.freeAwayClassesRemainingForMonth > 1 ? "classes" : "class"} free`
                    }
                }

                return membershipData
            })
            allSubUserMembershipDataPromises = allSubUserMembershipDataPromises.concat(membershipDataPromisesForUser)
        })

        const membershipData: MembershipData[] = await Promise.all(allSubUserMembershipDataPromises)
        const membershipDataList = membershipData.filter(item => item)
        if (membershipDataList.length > 0) {
            this.data = membershipDataList
            return this
        }
        return undefined
    }

    private getPauseInfo(tz: Timezone, pausedUsed: number, packEndDate: string, pauseDaysLeft: number, shouldAddExtendsOn: boolean): PauseInfo[] {
        const endsOn = TimeUtil.formatDateInTimeZone(tz, new Date(packEndDate), "DD MMM YYYY")
        const pauseInfo = [
            { title: "Pause days used", value: AppUtil.appendDays(pausedUsed) }
        ]
        if (shouldAddExtendsOn) {
            pauseInfo.push({ title: "Membership will be extended by", value: AppUtil.appendDays(pausedUsed) })
            pauseInfo.push({ title: "Membership will now end on", value: endsOn })
        }
        pauseInfo.push({ title: "Pause days left", value: AppUtil.appendDays(pauseDaysLeft) })
        return pauseInfo
    }
}

export class DIYMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]
    showAdditionalMembershipWidget: boolean

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const userId = userProfile.userId
        const packFulfilments = this.productType === "DIY_FITNESS" ? await userProfile.cultDiyMembershipPromise : await userProfile.mindDiyMembershipPromise
        if (_.isEmpty(packFulfilments)) {
            return undefined
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        const membershipDataPromises = packFulfilments.map(async packFulfilment => {
            const packs = this.productType === "DIY_FITNESS" ? await interfaces.diyService.getDIYFitnessPacksForIds(userId, [packFulfilment.productId]) : await interfaces.diyService.getDIYMeditationPacksForIds(userId, [packFulfilment.productId])
            const pack = packs[0]
            if (!pack) {
                return undefined
            }
            const sessionId = pack.sessionIds[LiveUtil.getNextDIYSessionIdx(pack, packFulfilment)]
            const tenant = AppUtil.getTenantFromUserContext(userContext)
            const sessionPromise = this.productType === "DIY_FITNESS" ? interfaces.diyService.getDIYFitnessProductsByProductIds(userId, [sessionId], tenant) : interfaces.diyService.getDIYMeditationProductsByProductIds(userId, [sessionId], tenant)
            const findQuery: IFindQuery = {
                condition: {
                    userId: userProfile.userId,
                    activityType: this.productType,
                    "meta.packId": pack.productId,
                    date: { $lte: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone) } // To force use the date + userid index as its not using the sort index properly
                },
                sortField: "date",
                count: 1,
                sortOrder: SortOrder.DESC
            }
            const activitiesPromise = interfaces.activityStoreDao.find(findQuery)
            const activities = await activitiesPromise
            const sessions = await sessionPromise
            const session = sessions[0]
            const lastAttendedSession = !_.isEmpty(activities) ? activities[0] : undefined
            if (!session) {
                return undefined
            }
            let playAction: PlayAction = {
                title: "PLAY",
                icon: "PLAY",
                url: this.productType === "DIY_FITNESS" ? ActionUtil.cultDiySingle(pack.productId, session.productId, "DIY_FITNESS") : ActionUtil.mindDiySingle(pack.productId, session.productId, "DIY_MEDITATION"),
                actionType: "NAVIGATION",
            }
            if (AppUtil.isWeb(userContext)) {
                playAction = {
                    actionType: "PLAY_VIDEO",
                    title: "PLAY",
                    icon: "PLAY",
                    meta: {
                        content: AtlasUtil.getContentDetailV2(session),
                        queryParams: AtlasUtil.getContentMetaV2(session, pack),
                        title: session.title,
                        checkDownloadStatus: true
                    }
                }
            }
            const completedSessionCount = packFulfilment.completedProductIds ? packFulfilment.completedProductIds.length : 0
            const seoParams: SeoUrlParams = {
                productName: pack.title
            }
            const membershipData: MembershipData = {
                membershipInfo: {
                    title: pack.title,
                    icon: undefined,
                    meta: completedSessionCount === 0 ? "No Sessions Done" : `${pad(completedSessionCount, 2)} / ${pad(pack.sessionIds.length, 2)} Sessions`
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
                },
                actionUrl: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent),
                lineItems: [],
                membershipState: "ACTIVE"
            }

            if (completedSessionCount === 0) {
                membershipData.lineItems.push({
                    subTitle: "Start your first session now"
                })
            } else if (lastAttendedSession) {
                const todayDateString = TimeUtil.todaysDateWithTimezone(tz)
                const numDays = TimeUtil.diffInDays(tz, lastAttendedSession.date, todayDateString)
                membershipData.lineItems.push({
                    title: "LAST SESSION",
                    subTitle: numDays === 0 ? `Today` : `${numDays} days ago`
                })
            }
            membershipData.lineItemsAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, session.locked, playAction, isUserEligibleForTrial, session.productType, "diy_pack_widget_clp", bucketId, blockInternationalUser, session.format)
            return membershipData
        })
        const membershipData: MembershipData[] = await Promise.all(membershipDataPromises)
        this.data = membershipData.filter(p => p)
        this.showAdditionalMembershipWidget = AppUtil.isDesktop(userContext) && this.data.length === 1
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}

export class BundleMemebshipViewWidget extends MembershipWidget {
    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.subCategoryCode = membershipWidget.subCategoryCode ? membershipWidget.subCategoryCode : "MP"
        this.subCategoryCodes = membershipWidget.subCategoryCodes
            ? membershipWidget.subCategoryCodes
            : (membershipWidget.subCategoryCode ? [this.subCategoryCode] : ["MP", "MP_V2"])
        this.data = []
    }

    getManagedPlanMeta(booking: BookingDetail, userContext: UserContext): string {
        const tz = userContext.userProfile.timezone
        switch (booking.stepInfosV2[0].stepState) {
            case "SUBSCRIPTION_ACTIVE":
                return `Expires on ${TimeUtil.formatEpochInTimeZone(tz, booking.bundleOrderResponse.expiryTimeEpoch, "D MMM YYYY")}`
            case "SUBSCRIPTION_ACTIVE_WITH_AUTO_RENEW":
                return `Active, Auto Renew on ${TimeUtil.formatEpochInTimeZone(tz, booking.bundleOrderResponse.expiryTimeEpoch, "D MMM")}`
            case "EXPIRED":
                return `Expired on ${TimeUtil.formatEpochInTimeZone(tz, booking.bundleOrderResponse.expiryTimeEpoch, "D MMM YYYY")}`
        }
    }

    getManagedPlanButtonAction(userContext: UserContext, subsBooking: BookingDetail, renewAction: ActionV2, offers: PackOffersResponse,
        subscriptionStartDate: number, subCategoryCode: string): Action {
        return {
            title: "RENEW",
            actionType: "SHOW_RENEW_MP_SUBSCRIPTION_MODAL",
            meta: CareUtil.getRenewMPSubscriptionActionMeta(userContext, subsBooking, renewAction, offers, subscriptionStartDate, subCategoryCode)
        }
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const patientsList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userProfile)
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        if (!selfPatient) {
            return undefined
        }
        const membershipDataPromises = this.subCategoryCodes.map(async (subCategoryCode) => {
            let activePackBookings = await interfaces.healthfaceService.getActivePacksByBookingType(userProfile.userId, selfPatient.id, "CARE", "BUNDLE", subCategoryCode)
            if (_.isEmpty(activePackBookings)) {
                return undefined
            }
            activePackBookings = activePackBookings.filter(activeBooking => activeBooking.booking.status === "CONFIRMED")
            if (_.isEmpty(activePackBookings)) {
                return undefined
            }
            const membershipDataPromise = _.map(activePackBookings, async activePackBooking => {
                const subscriptionBooking = activePackBooking.childBookingInfos.find(bookingInfo => bookingInfo.booking.subCategoryCode === "MP_SUBS")
                let subsOffers
                const productPromise = interfaces.catalogueService.getProduct(activePackBooking.booking.productCode)
                const subscriptionStartDate = interfaces.healthfaceService.getStartDate(subCategoryCode, activePackBooking.booking.id)
                let renewAction
                if (!_.isEmpty(subscriptionBooking.stepInfosV2[0].actionV2s)) {
                    renewAction = subscriptionBooking.stepInfosV2[0].actionV2s.find(action => action.actionType === "RENEW_SUBSCRIPTION")
                    if (!_.isEmpty(renewAction)) {
                        subsOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                            userContext,
                            "BUNDLE",
                            renewAction.actionContext.subscriptionProducts.map((product: MPChildProduct) => product.baseSellableProduct.productCode),
                            AppUtil.callSourceFromContext(userContext),
                            interfaces,
                            true
                        )
                    }
                }
                const membershipData: MembershipData = {
                    membershipInfo: {
                        title: (await productPromise).title,
                        subTitle: `For ${activePackBooking.bundleOrderResponse.patient.name}`,
                        meta: this.getManagedPlanMeta(subscriptionBooking, userContext),
                        metaAction: !_.isEmpty(renewAction) ? this.getManagedPlanButtonAction(userContext, subscriptionBooking, renewAction, subsOffers, await subscriptionStartDate, subCategoryCode) : {
                            title: "MANAGE",
                            actionType: "NAVIGATION",
                            url: ActionUtil.carefitbundle(activePackBooking.booking.productCode, subCategoryCode, activePackBooking.booking.id.toString())
                        }
                    },
                    lineItems: [],
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(activePackBooking.booking.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
                    },
                    membershipState: "ACTIVE"
                }
                return membershipData
            })
            return await Promise.all(membershipDataPromise)
        })
        const membershipDatas: MembershipData[][] = await Promise.all(membershipDataPromises)
        this.data = _.flatMap(membershipDatas).filter(item => item)
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}

export class TherapyMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const isNewSpecialityBookingSupported = sessionInfo.appVersion >= NEW_MIND_THERAPY_SPECIALITY_BOOKING_PAGE_SUPPORTED
        const patientsList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userProfile)
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        if (!selfPatient) {
            return undefined
        }
        let activePackBookings = await interfaces.healthfaceService.getActivePacksByBookingType(userProfile.userId, selfPatient.id, "MIND", "BUNDLE", "MIND_THERAPY")
        if (_.isEmpty(activePackBookings)) {
            return undefined
        }
        activePackBookings = activePackBookings.filter(activeBooking => activeBooking.booking.status === "CONFIRMED")
        if (_.isEmpty(activePackBookings)) {
            return undefined
        }
        const membershipDataPromises = _.map(activePackBookings, async activePackBooking => {
            const aggregatedMembershipInfosPromise = interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.booking.id, selfPatient.id, "CONSULTATION")
            const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activePackBooking.bundleOrderResponse.expiryTimeEpoch))
            const today = TimeUtil.todaysDateWithTimezone(tz)
            const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
            const aggregatedMembershipInfo = (await aggregatedMembershipInfosPromise)[0]
            const subTitle = `${aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed} sessions, ${remainingDays} days left`
            let membershipData: MembershipData
            if (isNewSpecialityBookingSupported) {
                const childProductCode = aggregatedMembershipInfo.productMetas.find(item => item.productCode !== "CONS_MIND_PSY_ONLINE" && item.productCode !== "CONS_MIND_PSY_INCENTRE")
                const product = await interfaces.catalogueService.getProduct(childProductCode?.productCode) as ConsultationProduct
                membershipData = {
                    membershipInfo: {
                        title: CareUtil.isCoupleTherapist(product.doctorType) ? "Couple Therapy Pack" : "Therapy Pack",
                        subTitle: subTitle,
                        icon: "PLAY",
                        metaAction: product ? CareUtil.getSelectSpecialityAction(
                            userContext,
                            "BOOK",
                            product,
                            activePackBooking.booking.id,
                            activePackBooking.booking.patientId,
                            true
                        ) : undefined,
                        meta: " " // hack to get BOOK button to the right
                    },
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
                    },
                    lineItems: [],
                    membershipState: "ACTIVE"
                }
            } else {
                membershipData = {
                    membershipInfo: {
                        title: "Therapy Pack",
                        subTitle: subTitle,
                        icon: "PLAY"
                    },
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString())
                    },
                    lineItems: [],
                    membershipState: "ACTIVE"
                }
                const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                    productMeta.productDetail = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productMeta.productCode))
                    return productMeta
                })
                const consultationOfferPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "CONSULTATION",
                    aggregatedMembershipInfo.productMetas.map(productMeta => productMeta.productCode),
                    AppUtil.callSourceFromContext(userContext),
                    interfaces,
                    true
                )
                const productMetas = await Promise.all(membershipProductsPromises)
                const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                const offerDetails = OfferUtil.getPackOfferAndPrice(consultationProducts[0], await consultationOfferPromise)
                const offerIds = offerDetails.offers.map(offer => offer.offerId)
                const onlineConsultationProductCodesMap = new Map<DOCTOR_TYPE, string>()
                const incentreConsultationProductCodesMap = new Map<DOCTOR_TYPE, string>()
                consultationProducts.forEach(consultationProduct => {
                    if (consultationProduct.consultationMode === "ONLINE") {
                        onlineConsultationProductCodesMap.set(consultationProduct.doctorType, consultationProduct.productId)
                    } else {
                        incentreConsultationProductCodesMap.set(consultationProduct.doctorType, consultationProduct.productId)
                    }
                })
                const onlineBookingAction = CareUtil.getTherapistSelectionModalAction(onlineConsultationProductCodesMap, offerIds, patientsList, "VIDEO CALL", activePackBooking.booking.id, "membership_online")
                const inCentreBookingAction = CareUtil.getTherapistSelectionModalAction(incentreConsultationProductCodesMap, offerIds, patientsList, "VISIT CENTER", activePackBooking.booking.id, "membership_center")
                membershipData.lineItems.push({
                    action: onlineBookingAction,
                    showDivider: false
                })
                membershipData.lineItems.push({
                    action: inCentreBookingAction,
                    showDivider: false
                })
            }
            return membershipData
        })
        const membershipData: MembershipData[] = await Promise.all(membershipDataPromises)
        this.data = _.filter(membershipData, x => !!x)
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}

export class PTMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        const tz = userContext.userProfile.timezone
        const sessionInfo = userContext.sessionInfo
        const activePackBookings = await interfaces.cultPTService.getActiveBundleOrders(userProfile.userId, "BUNDLE", "PERSONAL_TRAINING")
        if (_.isEmpty(activePackBookings)) {
            return undefined
        }
        const membershipDataPromises = _.map(activePackBookings, async activePackBooking => {
            const productPromise = interfaces.catalogueService.getProduct(activePackBooking.productCode)
            const aggregatedMembershipInfosPromise = interfaces.cultPTService.getAggregatedMembershipsByRootBookingId(activePackBooking.bookingId, activePackBooking.patientId, "CONSULTATION")
            const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activePackBooking.endDate))
            const today = TimeUtil.todaysDateWithTimezone(tz)
            const remainingDays = TimeUtil.diffInDays(tz, endDate, today)
            const aggregatedMembershipInfo = (await aggregatedMembershipInfosPromise)[0]
            const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                productMeta.productDetail = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productMeta.productCode))
                return productMeta
            })
            const remainingSessions = aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed
            let subTitle = `Active, `
            if (remainingDays < 30) {
                subTitle += `${remainingDays} days left`
            } else {
                subTitle += `Ending on ${TimeUtil.formatDateStringInTimeZone(endDate, tz, "DD MMM")}`
            }
            const meta = `Total: ${aggregatedMembershipInfo.totalTickets} \u2022 Remaining: ${remainingSessions}`
            const membershipData: MembershipData = {
                membershipInfo: {
                    title: (await productPromise).title,
                    subTitle: subTitle,
                    meta: meta
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.carefitbundle(activePackBooking.productCode, activePackBooking.subCategoryCode, activePackBooking.bookingId.toString())
                },
                lineItems: [],
                membershipState: "ACTIVE"
            }
            if (aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed > 0) {
                const productMetas = await Promise.all(membershipProductsPromises)
                const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                let action
                const preferedDoctor = await interfaces.healthfaceService.getYourTeam(userContext.userProfile.userId, activePackBooking.patientId, undefined, CareUtil.getHealthfaceTenant(activePackBooking.subCategoryCode))
                if (userContext.sessionInfo.appVersion >= NEW_SPECIALITY_BOOKING_PAGE_SUPPORTED && _.isEmpty(preferedDoctor)) {
                    action = {
                        ...CareUtil.specialistListingAction(userContext, consultationProducts[0], true, activePackBooking.patientId, activePackBooking.bookingId, undefined, undefined, true, "#f1f4f7,#f1f4f7"),
                        title: "BOOK"
                    }
                }
                let url = ActionUtil.selectCareDateV1(consultationProducts[0].productId, undefined, activePackBooking.bookingId, userContext)
                url += `&patientId=${activePackBooking.patientId}`
                const sessionBookingAction: Action = action ? action : {
                    actionType: "NAVIGATION",
                    title: "BOOK",
                    url: url
                }
                membershipData.membershipInfo.metaAction = sessionBookingAction
            }
            return membershipData
        })
        const membershipData: MembershipData[] = await Promise.all(membershipDataPromises)
        this.data = _.filter(membershipData, x => !!x)
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}

export class ComplementaryAccessViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const cultMindSummaryMapPromise = (userContext.userProfile as CFUserProfile).cultMindSummaryMap
        const complimentaryAccessProductType: ProductType = this.productType === "CULT_COMPLEMENTARY_ACCESS" ? "FITNESS" : "MIND"
        const findQuery: IFindQuery = {
            condition: {
                userId: userContext.userProfile.userId,
                score: { "$gte": 1 },
                activityType: complimentaryAccessProductType === "FITNESS" ? "CULT_CLASS" : "MIND_CLASS",
                date: { $lte: TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone) } // To force use the date + userid index as its not using the sort index properly

            },
            count: 1,
            sortField: "date",
            sortOrder: SortOrder.DESC
        }
        const activitiesPromise = interfaces.activityStoreDao.find(findQuery)
        const cultOrMindSummaryMap = transformCultSummaryMap(await cultMindSummaryMapPromise, (complimentaryAccessProductType === "FITNESS" ? "CULT_FIT" : "MIND_FIT"))
        const activities = await activitiesPromise
        const lastAttendedClass = !_.isEmpty(activities) ? activities[0] : undefined

        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const todayDateString = TimeUtil.todaysDateWithTimezone(tz)
        const centerId = complimentaryAccessProductType === "FITNESS" ? userContext.userProfile.cultCenterId : userContext.userProfile.mindCenterId

        const currentUser = await interfaces.userCache.getUser(userContext.userProfile.userId)
        if (_.isEmpty(cultOrMindSummaryMap)) {
            return undefined
        }
        const isNewClassBookingSuppoted = AppUtil.isNewClassBookingSuppoted(userContext, currentUser.isInternalUser)
        const pageFrom = this.productType === "CULT_COMPLEMENTARY_ACCESS" ? "cultCLP" : "mindCLP"
        const primaryUserClassBookAction: Action = {
            actionType: "NAVIGATION",
            title: "Book",
            url: ActionUtil.getBookCultClassUrl(complimentaryAccessProductType, isNewClassBookingSuppoted, pageFrom, undefined)
        }
        const buyAction: Action = {
            actionType: "NAVIGATION",
            title: "Explore",
            url: complimentaryAccessProductType === "FITNESS" ? ActionUtil.cultPackBrowse() : ActionUtil.mindPackBrowse()
        }
        let allComplementaryAccessDataPromises: Promise<MembershipData>[] = []
        _.forEach(Object.keys(cultOrMindSummaryMap), userId => {
            // push memberships to be built
            let complementaryAccesses: ComplimentaryAccess[] = []
            const cultOrMindSummary = cultOrMindSummaryMap[userId]
            if (!_.isNil(cultOrMindSummary.complimentaryAccessList)) {
                // remove cca if it's expired for more than 15 days
                _.remove(cultOrMindSummary.complimentaryAccessList.access, (cca) => {
                    return TimeUtil.getMomentForDateString(cca.endDate, tz).isBefore(today) && TimeUtil.diffInDays(tz, cca.endDate, todayDateString) > 6
                })
                const activeAccesses = _.filter(cultOrMindSummary.complimentaryAccessList.access, (item) => {
                    return momentTz.tz(today, tz).isBetween(item.startDate, item.endDate, "day", "[]")
                })
                const upcomingAccesses = _.filter(cultOrMindSummary.complimentaryAccessList.access, (item) => {
                    return TimeUtil.getMomentForDateString(item.startDate, tz).isAfter(today)
                })

                const expiredAccesses = _.filter(cultOrMindSummary.complimentaryAccessList.access, (item) => {
                    return TimeUtil.getMomentForDateString(item.endDate, tz).isBefore(today)
                })
                complementaryAccesses = complementaryAccesses.concat(activeAccesses, upcomingAccesses, expiredAccesses)
            }
            const membershipDataPromisesForUser: Promise<MembershipData>[] = _.map(complementaryAccesses, async (complementaryAccess: ComplimentaryAccess) => {
                const endDate = TimeUtil.getMomentForDateString(complementaryAccess.endDate, tz)
                const startDate = TimeUtil.getMomentForDateString(complementaryAccess.startDate, tz)
                const daysRemainingInMembership = TimeUtil.diffInDays(tz, complementaryAccess.endDate, todayDateString)
                // const complementaryAccessDuration = TimeUtil.diffInDays(tz, complementaryAccess.endDate, complementaryAccess.startDate)
                const packTitle = this.productType === "CULT_COMPLEMENTARY_ACCESS" ? "Cult" : "Mind"

                // Building sub title based on pack state and  pack start , pack end date
                let subTitle = undefined
                const isPassActive: boolean = momentTz.tz(today, tz).isBetween(complementaryAccess.startDate, complementaryAccess.endDate, "day", "[]")
                const isPassUpcoming: boolean = startDate.isAfter(today)
                const isPassExpired: boolean = endDate.isBefore(today)
                if (isPassActive) {
                    if (daysRemainingInMembership < 10)
                        subTitle = `Active, ${daysRemainingInMembership} days left`
                    else
                        subTitle = `Active, Ending on ${TimeUtil.formatDateStringInTimeZone(complementaryAccess.endDate, tz, "DD MMM")}`
                } else if (isPassExpired) {
                    subTitle = `Expired, Ended on ${TimeUtil.formatDateStringInTimeZone(complementaryAccess.endDate, tz, "DD MMM")}`
                } else if (isPassUpcoming) {
                    subTitle = `Upcoming, Starting from ${TimeUtil.formatDateStringInTimeZone(complementaryAccess.startDate, tz, "DD MMM")}`
                }
                const membershipData: MembershipData = {
                    membershipInfo: {
                        title: `${packTitle} Access Pass`,
                        subTitle: subTitle,
                        icon: "PLAY",
                        memberNameString: undefined
                    },
                    membershipState: undefined,
                    action: isPassActive ? primaryUserClassBookAction : { actionType: "NAVIGATION", url: null },
                    actionUrl: "",
                    lineItems: []
                }

                // Show last attended class for current ongoing pack if not show book class prompt
                if (lastAttendedClass) {
                    const booking = this.productType === "CULT_COMPLEMENTARY_ACCESS" ? await interfaces.cultFitService.getBookingById(lastAttendedClass.meta.fulfilmentId, lastAttendedClass.userId)
                        : await interfaces.mindFitService.getBookingById(lastAttendedClass.meta.fulfilmentId, lastAttendedClass.userId)

                    if (isPassActive) {
                        const numDays = TimeUtil.diffInDays(tz, lastAttendedClass.date, todayDateString)
                        membershipData.lineItems.push({
                            title: "LAST CLASS",
                            subTitle: numDays === 0 ? `${booking.Class.Workout.name}, today` : `${booking.Class.Workout.name}, ${numDays} days ago`
                        })
                        membershipData.lineItemsAction = primaryUserClassBookAction
                    } else if (isPassExpired) {
                        const numDays = TimeUtil.diffInDays(tz, lastAttendedClass.date, todayDateString)
                        membershipData.lineItems.push({
                            subTitle: `Get a membership pack`,
                        })
                        membershipData.lineItemsAction = buyAction
                    }
                } else if (isPassActive) {
                    membershipData.lineItems.push({
                        subTitle: lastAttendedClass ? undefined : (this.productType === "CULT_COMPLEMENTARY_ACCESS" ? `Book your first cult class now` : `Book your first mind class now`),
                    })
                    membershipData.lineItemsAction = primaryUserClassBookAction
                } else if (isPassExpired) {
                    membershipData.lineItems.push({
                        subTitle: `Get a membership pack`,
                    })
                    membershipData.lineItemsAction = buyAction
                }
                return membershipData
            })
            allComplementaryAccessDataPromises = allComplementaryAccessDataPromises.concat(membershipDataPromisesForUser)
        })

        const membershipData: MembershipData[] = await Promise.all(allComplementaryAccessDataPromises)
        const membershipDataList = membershipData.filter(item => item)
        if (membershipDataList.length > 0) {
            this.data = membershipDataList
            return this
        }
        return undefined
    }
}

export class ActiveBundleAndConsultationMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.subCategoryCodes = membershipWidget.subCategoryCodes
            ? membershipWidget.subCategoryCodes
            : (membershipWidget.subCategoryCode ? [membershipWidget.subCategoryCode] : [])
        this.doctorTypes = !_.isEmpty(membershipWidget.doctorTypes) ? membershipWidget.doctorTypes : []
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return undefined
        }
        const scheduledConsultationState = ["BOOKED", "SCHEDULED", "RESCHEDULED", "VITALS_COLLECTED", "STARTED", "CONSULTATION_STARTED"]
        const patientList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userProfile)
        if (_.isEmpty(patientList)) {
            return undefined
        }
        let activePackBookingsPromise: any[] = []
        let activeDiagnosticBookingDetails: BookingDetail[] = []
        let activeDiagnosticBookings: any[] = []
        if (!_.isEmpty(this.subCategoryCodes)) {
            activePackBookingsPromise = this.subCategoryCodes.map(async item => {
                if (item === "DIAGNOSTIC_TEST") {
                    return undefined
                }
                return interfaces.healthfaceService.getActiveBundleOrders(userProfile.userId, "BUNDLE", item)
            })
            if (this.subCategoryCodes.includes("DIAGNOSTIC_TEST")) {
                activeDiagnosticBookingDetails = await interfaces.healthfaceService.getPrescriptionLedDiagnostics(userProfile.userId, "DIAGNOSTIC_TEST", 0, 5)
            }
        }
        if (!_.isEmpty(activeDiagnosticBookingDetails)) {
            const activeDiagnosticBookingsPromise = this.getActiveDiagnosticBookings(interfaces, activeDiagnosticBookingDetails)
            const initialDiagnosticBookings = await Promise.all(activeDiagnosticBookingsPromise)
            activeDiagnosticBookings = _.filter(initialDiagnosticBookings, function (item) { return !!item })
        }
        if (!_.isEmpty(this.doctorTypes)) {
            if (this.ifLivePtWidget()) {
                activePackBookingsPromise.push(interfaces.cultPTService.getActiveConsultations(Number(userProfile.userId), LIVE_PT_DOCTOR_TYPES, "CULTFIT"))
            } else {
                activePackBookingsPromise.push(interfaces.healthfaceService.getActiveConsultations(Number(userProfile.userId), this.doctorTypes))
            }
        }
        const results = await Promise.all(activePackBookingsPromise.map(p => p.catch((e: any) => e)))
        const mergedBookings = results.concat(activeDiagnosticBookings)
        const uniqueBookings = _.filter(mergedBookings, function (item) { return !!item })
        let activePackBookings = uniqueBookings.filter(result => !(result instanceof Error))
        if (_.isEmpty(activePackBookings)) {
            return undefined
        } else {
            activePackBookings = _.flatMap(activePackBookings)
            const covidConsultationBooking: ActiveConsultationResponse =  activePackBookings.find((item: ActiveConsultationResponse) => _.get(item?.consultationProduct?.productSpecs, "isCovidConsWithFollowups", false))
            if (!_.isEmpty(covidConsultationBooking)) {
                // This change is to show all scheduled consultation upfront followed by covid consultation and other consultation and bundle pack.

                const activeScheduledConsultation = activePackBookings.filter((item: any ) => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const completedCovidConsultationBookings = activePackBookings.filter(item => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && _.get(item?.consultationProduct?.productSpecs, "isCovidConsWithFollowups", false) && !scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const completedCovidConsultationBookingIds = completedCovidConsultationBookings.map(item => item.bookingId)

                const activeOtherConsultation = activePackBookings.filter((item: any ) => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && !completedCovidConsultationBookingIds.includes(item?.bookingId) && !scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const activeBundleBookings = activePackBookings.filter((item: ActiveBundleOrderDetail) => ["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && item?.subCategoryCode).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                activePackBookings = activeScheduledConsultation.concat(completedCovidConsultationBookings, activeOtherConsultation, activeBundleBookings).filter(Boolean)
            } else {
                activePackBookings = activePackBookings.sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })
            }
            const productCodes = activePackBookings.map(activePackBooking => activePackBooking.productCode)
            const allProductsPromise = interfaces.catalogueService.getProducts(productCodes) as Promise<DiagnosticProduct[]>
            const membershipDataPromises = _.map(activePackBookings, async activePackBooking => {
                if (activePackBooking.categoryCode === "DIAGNOSTICS" && activePackBooking.subCategoryCode === "DIAGNOSTIC_TEST") {
                    return this.getDiagnosticMemberDetails(userContext, allProductsPromise, activePackBooking, interfaces, patientList)
                }
                return this.getMemberDetails(userContext, allProductsPromise, activePackBooking, interfaces, patientList)
            })
            const membershipData: MembershipData[] = await Promise.all(membershipDataPromises)
            this.data = _.filter(membershipData, x => !!x)
        }
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }

    ifLivePtWidget(): boolean {
        return !_.isEmpty(_.intersection(this.doctorTypes, LIVE_PT_DOCTOR_TYPES))
    }

    getActiveDiagnosticBookings(interfaces: IServiceInterfaces, activeDiagnosticBookingDetails: BookingDetail[]): Promise<any>[] {
        const activeDiagnosticBookings = activeDiagnosticBookingDetails.map(async (item) => {
            const rootBookingDetail = await interfaces.healthfaceService.getBookingDetail(Number(item.booking.rootBookingId))
            if (item?.diagnosticsTestOrderResponse[0]?.status === "REPORT_GENERATED") {
                const currentTime = moment().valueOf()
                const currentStateTransitionTime = moment(item?.diagnosticsTestOrderResponse[0]?.currentStateTransitionTime).valueOf()
                const differenceInDays = (currentTime - currentStateTransitionTime) / (86400 * 1000)
                if (differenceInDays > 7) {
                    return undefined
                }
            }
            if (rootBookingDetail.booking.categoryCode === "BUNDLE") {
                return undefined
            }
            const diagnosticTestTitle = (await interfaces.catalogueService.getProduct(item.diagnosticsTestOrderResponse[0].productCodes[0])).title
            const extraTestsTitle = (item.diagnosticsTestOrderResponse[0].productCodes.length >= 2) ? " + " + (item.diagnosticsTestOrderResponse[0].productCodes.length - 1) + (item.diagnosticsTestOrderResponse[0].productCodes.length >= 3 ? " Tests" : " Test") : ""
            return {
                bookingId: item.booking.id,
                userId: item.booking.userId,
                patientId: item.booking.patientId,
                subCategoryCode: item.booking.subCategoryCode,
                categoryCode: item.booking.categoryCode,
                startDate: item.booking.createdAt,
                productCode: item.booking.productCode,
                title: diagnosticTestTitle + extraTestsTitle,
            }
        })
        return activeDiagnosticBookings
    }

    async getDiagnosticMemberDetails(userAgent: UserContext, allProductsPromise: Promise<DiagnosticProduct[]>, activePackBooking: ActiveBundleOrderDetail, interfaces: IServiceInterfaces, patientList: Patient[]): Promise<MembershipData> {
        const patient = !_.isEmpty(patientList)
            ? patientList.find(patient => patient.id == activePackBooking.patientId)
            : await interfaces.healthfaceService.getPatientDetails(activePackBooking.patientId)
        const products = await allProductsPromise as DiagnosticProduct[]
        const product = products.find(product => product.productId === activePackBooking.productCode)
        const actionUrl = ActionUtil.diagnostics(activePackBooking.subCategoryCode, activePackBooking.bookingId.toString())
        const metaAction: Action = {
            actionType: "NAVIGATION",
            title: "MANAGE",
            url: actionUrl
        }
        const membershipData: MembershipData = {
            membershipInfo: {
                title: activePackBooking.title,
                subTitle: `For ${patient.name}`,
                metaAction: metaAction,
            },
            action: {
                actionType: "NAVIGATION",
                url: actionUrl
            },
            actionUrl: actionUrl,
            lineItems: [],
            membershipState: "ACTIVE"
        }
        return membershipData
    }

    async getMemberDetails(userContext: UserContext, allProductsPromise: Promise<DiagnosticProduct[]>, activePackBooking: ActiveConsultationResponse | ActiveBundleOrderDetail, interfaces: IServiceInterfaces, patientList: Patient[]): Promise<MembershipData> {
        if (!_.isEmpty(activePackBooking)) {
            const products = await allProductsPromise as DiagnosticProduct[]
            const product = products.find(product => product.productId === activePackBooking.productCode)
            if (product && product.subCategoryCode && this.subCategoryCodes.indexOf(product.subCategoryCode) !== -1) {
                if ((
                    CareUtil.isPartOfConsultationCovidPackProducts(product.productId) ||
                    CareUtil.isPartOfConsultationPackAyurvedaProducts(product.setCode) ||
                    product.subCategoryCode === "PHYSIOTHERAPY"
                ) &&
                    userContext.sessionInfo.userAgent !== "APP"
                ) {
                    return undefined
                }
                return this.getBundleDetails(userContext, product, interfaces, activePackBooking as ActiveBundleOrderDetail, patientList)
            } else {
                return this.getConsultationDetails(product, userContext, interfaces, activePackBooking as ActiveConsultationResponse, patientList)
            }
        }
    }

    async getBundleDetails(userContext: UserContext, product: DiagnosticProduct, interfaces: IServiceInterfaces, activePackBooking: ActiveBundleOrderDetail, patientList: Patient[]): Promise<MembershipData> {
        const patient = !_.isEmpty(patientList)
            ? patientList.find(patient => patient.id == activePackBooking.patientId)
            : await interfaces.healthfaceService.getPatientDetails(activePackBooking.patientId)
        const actionUrl = ActionUtil.carefitbundle(activePackBooking.productCode, activePackBooking.subCategoryCode, activePackBooking.bookingId.toString())
        const { meta, metaAction } = await this.getMetaDataAndAction(userContext, activePackBooking, interfaces)
        const membershipData: MembershipData = {
            membershipInfo: {
                title: product.title,
                subTitle: `For ${patient.name}`,
                meta: meta,
                metaAction: metaAction ? metaAction : {
                    actionType: "NAVIGATION",
                    title: "MANAGE",
                    url: actionUrl
                }
            },
            action: {
                actionType: "NAVIGATION",
                url: actionUrl
            },
            actionUrl: ActionUtilV1.carefitbundle(activePackBooking.productCode, activePackBooking.subCategoryCode, activePackBooking.bookingId.toString()),
            lineItems: [],
            membershipState: "ACTIVE"
        }
        return membershipData
    }

    async getMetaDataAndAction(userContext: UserContext, activePackBooking: ActiveBundleOrderDetail, interfaces: IServiceInterfaces): Promise<{ meta: string, metaAction: Action }> {
        let aggregatedMembershipInfo, meta = " ", metaAction: Action
        const islabTestBooking = LAB_TEST_PACKS_SUB_CATEGORY_CODE.includes(activePackBooking.subCategoryCode)
        if (["PHYSIOTHERAPY", "NUTRITIONIST", ...SKIN_HAIR_SUBCATEGORIES].indexOf(activePackBooking.subCategoryCode) !== -1 || islabTestBooking) {
            const aggregatedMembershipInfos = activePackBooking.subCategoryCode === "DIAG_PACK"
                ? await interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.bookingId, activePackBooking.patientId, "DIAGNOSTICS")
                : await interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.bookingId, activePackBooking.patientId, "CONSULTATION")
            if (!_.isEmpty(aggregatedMembershipInfos)) {
                if (activePackBooking.subCategoryCode === "PHYSIOTHERAPY") {
                    aggregatedMembershipInfo = aggregatedMembershipInfos[0]
                    const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                        productMeta.productDetail = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productMeta.productCode))
                        return productMeta
                    })
                    const productMetas = await Promise.all(membershipProductsPromises)
                    const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                    metaAction = {
                        ...CareUtil.specialistListingAction(userContext, consultationProducts[0], false, activePackBooking.patientId, activePackBooking.bookingId, undefined, undefined, false, undefined, undefined, undefined, true, "Select a Physiotherapist"),
                        actionType: "NAVIGATION",
                        title: "BOOK"
                    }
                } else if (activePackBooking.subCategoryCode === "NUTRITIONIST" || islabTestBooking) {
                    aggregatedMembershipInfo = aggregatedMembershipInfos[0]
                } else {
                    aggregatedMembershipInfo = aggregatedMembershipInfos.find(aggregatedMembership => aggregatedMembership.aggregatedBenefitType === "PRIMARY")
                }
                if (!_.isEmpty(aggregatedMembershipInfo)) {
                    if (aggregatedMembershipInfo?.startEndEpoch) {
                        const { endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, false)
                        meta = endDateFormatted
                    } else {
                        const remainingSessions = aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed
                        meta = `${remainingSessions}/${aggregatedMembershipInfo.totalTickets} session left`
                    }
                }
            }
        } else if (CONSULTATION_PACK_SUBCATEGORIES.includes(activePackBooking.subCategoryCode)) {
            const aggregatedMembershipInfos = await interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.bookingId, activePackBooking.patientId, "CONSULTATION")
            aggregatedMembershipInfo = aggregatedMembershipInfos[0]
            if (aggregatedMembershipInfo?.startEndEpoch) {
                const { endDateFormatted } = CareUtil.getConsultationPackProgressInfo(userContext, aggregatedMembershipInfo, false)
                meta = endDateFormatted
            }
        }
        return { meta: meta, metaAction: metaAction }
    }

    async getConsultationDetails(product: DiagnosticProduct, userContext: UserContext, interfaces: IServiceInterfaces, activeConsultation: ActiveConsultationResponse, patientList: Patient[] = []): Promise<MembershipData> {
        let patientPromise: Promise<Patient>
        if (_.isEmpty(patientList)) {
            patientPromise = interfaces.healthfaceService.getPatientDetails(Number(activeConsultation.patientId))
        }
        const doctorPromise = this.ifLivePtWidget()
            ? interfaces.healthfaceService.getDoctorDetails(Number(activeConsultation.doctorId))
            : interfaces.ollivanderService.getDoctorDetails(Number(activeConsultation.doctorId))
        const consultationProduct = <ConsultationProduct>await interfaces.catalogueService.getProduct(activeConsultation.productCode)
        const vertical = CareUtil.getVerticalForConsultation(consultationProduct?.doctorType)
        const actionUrl = ActionUtil.teleconsultationSingle(userContext, activeConsultation.productCode, consultationProduct.urlPath, activeConsultation.bookingId.toString(), undefined, vertical)
        let subtitle = "For "
        const tz = userContext.userProfile.timezone
        const patient = patientPromise ? await patientPromise : patientList.find(patient => patient.id == activeConsultation.patientId)
        if (patient.name) {
            if (patient.name.length > 20) {
                subtitle += patient.name.substring(0, 20) + "..."
            } else {
                subtitle += `${patient.name}`
            }
        }
        subtitle += ` | ${TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activeConsultation.startDate), "ddd, D MMM, hh:mm A")}`
        const membershipInfo: MembershipInfo = {
            title: product.title,
            subTitle: userContext.sessionInfo.userAgent !== "APP" ? `${subtitle} With ${(await doctorPromise).name}` : `With ${(await doctorPromise).name}`,
            memberNameString: subtitle,
            meta: " ", // hack to get MANAGE button to the right
        }
        const metaAction: Action = {
            title: "MANAGE",
            actionType: "NAVIGATION",
            url: actionUrl
        }
        if (CareUtil.isDoctorUnavailabilitySupported(userContext) && CareUtil.isDoctorNonAvailable(activeConsultation.status)) {
            return {
                membershipInfo: membershipInfo,
                action: metaAction,
                actionUrl,
                lineItems: [
                    {
                        title: DOCTOR_UNAVAILABLE_SHORTER_TEXT,
                        icon: "IMPORTANT",
                        iconBackgroundColor: "rgba(255,255,255, 0.3)",
                        action: metaAction,
                    }
                ],
                membershipState: "DOCTOR_UNAVAILABLE"
            }
        } else {
            return {
                membershipInfo: {
                    ...membershipInfo,
                    metaAction
                },
                action: metaAction,
                actionUrl,
                lineItems: [],
                membershipState: "ACTIVE"
            }
        }
    }
}

export class GymfitMembershipViewWidget extends MembershipWidget {

    data: MembershipData[]

    constructor(membershipWidget: MembershipWidget) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const membershipSummary = (await userContext.userProfile.gymfitMembershipPromise).membershipSummary
        const activeMembership: GymfitMembership = membershipSummary.activeMembership
        if (_.isEmpty(activeMembership) && _.isEmpty(membershipSummary.upcomingMemberships)) {
            return undefined
        }
        if (activeMembership) {
            const membershipData: MembershipData = {
                membershipInfo: {
                    title: "cultpass PRO",
                    memberNameString: "",
                    meta: "Active, Ending on " + TimeUtil.formatDateStringInTimeZone(activeMembership.endDate, userContext.userProfile.timezone, "DD MMM YYYY"),
                },
                action: {
                    actionType: "NAVIGATION",
                    url: await GymfitUtil.getMembershipPackDetailsUrl(activeMembership, userContext),
                },
                lineItems: [],
                membershipState: "ACTIVE"
            }
            this.data.push(membershipData)
        }
        if (membershipSummary.upcomingMemberships && membershipSummary.upcomingMemberships.length) {
            for (const upcomingMembership of membershipSummary.upcomingMemberships) {
                const membershipData: MembershipData = {
                    membershipInfo: {
                        title: "cultpass PRO",
                        memberNameString: "",
                        meta: `Starting from ${TimeUtil.formatDateStringInTimeZone(upcomingMembership.startDate, userContext.userProfile.timezone, "DD MMM")}`
                    },
                    action: {
                        actionType: "NAVIGATION",
                        url: await GymfitUtil.getMembershipPackDetailsUrl(upcomingMembership, userContext),
                    },
                    lineItems: [],
                    membershipState: "FUTURE"
                }
                this.data.push(membershipData)
            }
        }
        return this
    }
}
