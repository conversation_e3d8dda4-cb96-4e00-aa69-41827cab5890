import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { PromoBannerWidget } from "@curefit/vm-models"

export class PromoBannerViewWidget extends PromoBannerWidget {
    constructor() {
        super()
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        return this
    }

}
