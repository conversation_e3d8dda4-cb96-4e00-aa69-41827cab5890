import { <PERSON><PERSON><PERSON>, IBaseWidget, OLD_TEMPLATE_ID_PARAM_NAME, PMS_TEMPLATE_ID_PARAM_NAME, UserContext, VerticalOfferWidget, WidgetTemplate } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { User } from "@curefit/user-common"
import { UserOfferEligibilityResponse } from "@curefit/offer-common"
import * as _ from "lodash"
import { pluralizeStringIfRequired } from "@curefit/util-common"
import { ActionUtil } from "../../../util/ActionUtil"

export class VerticalOfferWidgetView extends VerticalOfferWidget {
    layoutProps: any

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        // 1. Take lowest time from all banners and add to base banner
        // 2. Add layout props from template

        const timers: number[] = []
        const now = new Date()
        const { sessionInfo } = userContext
        const userProfile = userContext.userProfile
        const user: User = await userContext.userPromise

        if (this.baseBanner.timer && this.baseBanner.timer.timerEndTimeWithTz) {
            const timerEndTime = new Date(this.baseBanner.timer.timerEndTimeWithTz.date.toString()).getTime()
            if (timerEndTime > now.getTime()) {
                timers.push(new Date(this.baseBanner.timer.timerEndTimeWithTz.date.toString()).getTime())
            }
        }
        if (this.baseBanner.timer && this.baseBanner.timer.privateOfferId && user.isPhoneVerified && user.phone) {
            const eligibility: UserOfferEligibilityResponse = await interfaces.offerService.getOfferEligibilityForUser(this.baseBanner.timer.privateOfferId, user.phone, "PHONE")
            if (eligibility && eligibility.status === "OK") {
                const privateOfferEndTime: number = new Date(eligibility.userOfferEligibility.endDate.toString()).getTime()
                if (privateOfferEndTime > new Date().getTime()) {
                    timers.push(privateOfferEndTime)
                }
            }

        }
        if (!_.isEmpty(this.baseBanner.actionV2)) {
            const vmAction = await interfaces.userActionMappingBusiness.getVMActionFromActionV2(this.baseBanner.actionV2, interfaces, userContext)
            if (!_.isEmpty(vmAction)) {
                this.baseBanner.action = vmAction
            }
        }
        // Check for this.baseBanner action segments, deleting action object, when segment doesnot match
        if (this.baseBanner.action && !_.isEmpty(this.baseBanner.action.segmentIds)) {
            const segment = await interfaces.segmentService.doesUserBelongToAnySegment(this.baseBanner.action.segmentIds, userContext)
            if (!segment) {
                delete this.baseBanner.action
            }
        }
        if (!_.isEmpty(this.baseBanner.action) && this.baseBanner.action.url &&
                (this.baseBanner.action.url.includes(PMS_TEMPLATE_ID_PARAM_NAME) || this.baseBanner.action.url.includes(OLD_TEMPLATE_ID_PARAM_NAME))) {
            const action: any = await this.getPackIdUrlFromTemplate(interfaces, userContext, this.baseBanner.action)
            // It's just not be configured for certain cities hence with this check we are preventing it from failing
            if (action) {
                this.baseBanner.action = action
            } else {
                return undefined
            }
        }
        const finalChildBanners: BannerItem[] = []
        for (const banner of this.data) {
            if (banner.timer.privateOfferId && user.isPhoneVerified && user.phone) {
                const eligibility: UserOfferEligibilityResponse = await interfaces.offerService.getOfferEligibilityForUser(banner.timer.privateOfferId, user.phone, "PHONE")
                if (eligibility && eligibility.status === "OK") {
                    const privateOfferEndTime: number = new Date(eligibility.userOfferEligibility.endDate.toString()).getTime()
                    if (privateOfferEndTime > new Date().getTime()) {
                        if (!_.isEmpty(banner.action) && banner.action.url &&
                            (this.baseBanner.action.url.includes(PMS_TEMPLATE_ID_PARAM_NAME) || this.baseBanner.action.url.includes(OLD_TEMPLATE_ID_PARAM_NAME))) {
                            const action: any = await this.getPackIdUrlFromTemplate(interfaces, userContext, banner.action)
                            // It's just not be configured for certain cities hence with this check we are preventing it from failing
                            if (action) {
                                banner.action = action
                                timers.push(privateOfferEndTime)
                                finalChildBanners.push(banner)
                            }
                        } else {
                            timers.push(privateOfferEndTime)
                            finalChildBanners.push(banner)
                        }
                    }
                }
            }
        }
        timers.sort((a, b) => {
            return a < b ? -1 : 1
        })

        this.data = finalChildBanners

        if (!_.isEmpty(timers)) {
            this.baseBanner.timer.timerEndTime = timers[0]
            const diffInMillis = this.baseBanner.timer.timerEndTime - new Date().getTime()
            const days = Math.floor(diffInMillis / (1000 * 60 * 60 * 24))
            const hours = Math.floor(diffInMillis / (1000 * 60 * 60)) % 24
            if (days > 0) {
                delete this.baseBanner.timer.timerEndTime
                this.baseBanner.timer.timerTimeText = days + pluralizeStringIfRequired(" day", days) + " " + hours + " hours"
            }
        } else {
            delete this.baseBanner.timer
        }
        _.forEach(this.data, bannerItem => {
            delete bannerItem.timer.timerEndTime
        })

        if (!_.isNil(this.templateId)) {
            const widgetTemplate: WidgetTemplate = await interfaces.pageService.getWidgetTemplate(this.templateId)
            if (_.isNil(widgetTemplate)) {
                return undefined
            }
            if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
                this.layoutProps = widgetTemplate.templateData.app
            } else {
                this.layoutProps = widgetTemplate.templateData.web
            }

            if (!this.layoutProps) {
                return undefined
            }
        }

        return this
    }

}
