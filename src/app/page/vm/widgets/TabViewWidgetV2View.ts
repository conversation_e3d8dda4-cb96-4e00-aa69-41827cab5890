import { IBaseWidget, TabViewWidgetV2, User<PERSON>ontext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { Action } from "@curefit/apps-common"
import * as _ from "lodash"

interface TabViewV2Item {
    text: string
    key: string
    action: Action
}

export class TabViewWidgetV2View extends TabViewWidgetV2 {
    data: TabViewV2Item[] = []

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        _.forEach(this.items, item => {
            const tabItem: TabViewV2Item = {
                text: item.title,
                key: item.widgetId,
                action: {
                    actionType: "SCROLL_TO_WIDGET",
                    meta: {
                        widgetId: item.widgetId,
                        animate: item.animate
                    }
                }
            }
            this.data.push(tabItem)
        })
        return this
    }

}
