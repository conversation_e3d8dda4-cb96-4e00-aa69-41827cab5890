import * as _ from "lodash"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { ListWidgetView } from "@curefit/vm-models"
import { RecipePackWidget } from "./RecipePackWidget"
import { SortOrder } from "@curefit/mongo-utils"
import LiveUtil, { RECIPE_PRODUCT_TYPE } from "../../../util/LiveUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import AppUtil from "../../../util/AppUtil"
import { DIYRecipeView } from "@curefit/diy-common"
import { LivePackUtil } from "../../../util/LivePackUtil"

export class RecipeListWidget extends ListWidgetView {

    constructor() {
        super()
        this.isHorizontal = false
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        const categoryId = queryParams.diyCategory
        const vegOnly = (queryParams.vegOnly === "true") ? true : false
        const start = +queryParams.start
        const count = +queryParams.count
        const countryId = AppUtil.getCountryId(userContext)
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let items: DIYRecipeView[]
        if (categoryId === "recipes_all") {
            items = await interfaces.diyService.getAllRecipes(vegOnly, tenant, start, count, "productId", SortOrder.DESC, countryId)
        } else {
            items = await interfaces.diyService.getRecipesForCategory(categoryId, vegOnly, tenant, start, count, "productId", SortOrder.DESC, countryId)
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        this.widgets = _.map(items, item => {
            const isLocked = item.locked && isUserEligibleForMonetisation
            const action = LiveUtil.getDIYRecipeAction(item, userContext.sessionInfo.userAgent)
            const membershipAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, action, isUserEligibleForTrial, RECIPE_PRODUCT_TYPE, "recipe_list_widget", "", false)
            return new RecipePackWidget(item.imageDetails.thumbnailImage, item.title, item.isVeg, membershipAction, item.preparationTime, item.id, false, isLocked)
        })
        return this
    }

}
