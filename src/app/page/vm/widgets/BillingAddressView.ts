import { IBaseWidget, UserContext, IServiceInterfaces, BillingAddressWidget } from "@curefit/vm-models"

export class BillingAddressView extends BillingAddressWidget {
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        return new BillingAddressWidget().buildView(interfaces, userContext, queryParams)
    }
}
