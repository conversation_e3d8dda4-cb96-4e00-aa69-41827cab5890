import {
    <PERSON>I<PERSON>,
    GymCategoryListWidget,
    GymPack,
    GymPackHeaderWidget,
    GymPacksWidget,
    IBaseWidget,
    PackBrowseWidgetV2,
} from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import {
    GymfitBrand,
    GymfitCenterCategory,
    GymfitListingChannels,
    GymfitListingType
} from "@curefit/gymfit-common"
import { ActionUtil } from "@curefit/base-utils"
import { Action } from "@curefit/apps-common"
import AppUtil from "../../../util/AppUtil"
import { Namespace, OfflineFitnessPack, ProductSubType, Visibility } from "@curefit/pack-management-service-common"
import CultUtil from "../../../util/CultUtil"
import CatalogueServiceUtilities from "../../../util/CatalogueServiceUtilities"
import * as _ from "lodash"

export class PackBrowseWidgetV2View extends PackBrowseWidgetV2 {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_MEMBERSHIP") {
            return new GymfitMembershipView(this).buildView(interfaces, userContext, queryParams)
        }
    }
}

class GymfitMembershipView extends PackBrowseWidgetV2 {

    constructor(packBrowseWidgetV2: PackBrowseWidgetV2) {
        super()
        this.header = packBrowseWidgetV2.header
        this.widgets = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const cityId = userContext.sessionInfo.sessionData.cityId
        const userId = userContext.userProfile.userId
        const gymfitPacks: OfflineFitnessPack[] = await interfaces.offlineFitnessPackService.searchCachedPacks({
            namespace: Namespace.OFFLINE_FITNESS,
            productTypes: ["GYMFIT_FITNESS_PRODUCT"],
            restrictions: {cities: [cityId]},
            productSubType: ProductSubType.GENERAL,
            status: "ACTIVE",
            saleEnabled: true,
            visibility: AppUtil.isWeb(userContext) ? Visibility.WEBSITE : Visibility.APP
        })
        const gymFitBrands: GymfitBrand[] = await interfaces.gymfitService.getBrandsByCategories([GymfitCenterCategory.GX, GymfitCenterCategory.GA])
        const presentableBrands = gymFitBrands.map((gymFitBrand) => gymFitBrand.imageUrls ? gymFitBrand.imageUrls.brandImageUrl : "").filter(brand => brand)
        const offers = await interfaces.offerServiceV3.getGymFitProductPrices({
            userInfo: {
                userId,
                deviceId: userContext.sessionInfo.deviceId,
            },
            productIds: gymfitPacks.map(pack => pack.id),
            source: "CUREFIT_APP",
            cityId
        })
        const presentableGymPacks: GymPack[] = await Promise.all(gymfitPacks.map(async (gymfitPack) => {
            const price = CultUtil.getOfferDetailsPMS(gymfitPack, offers).price
            const cardAction: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.gymfitPack(gymfitPack.productId)
            }
            const extraAction: Action = {
                title: "BUY NOW",
                actionType: "NAVIGATION",
                url: ActionUtil.gymfitPack(gymfitPack.productId)
            }
            const offerTitles = !_.isEmpty(offers.priceMap[gymfitPack.id]?.offerIds)
                ? offers.priceMap[gymfitPack.id].offerIds.map(offerId => offers.offerMap[offerId].description)
                : []

            const packPoints: any = CatalogueServiceUtilities.getGymfitProductPoints(gymfitPack, userContext.sessionInfo?.osName)
            return {
                title: gymfitPack.title,
                price: {
                    mrp: price.mrp,
                    listingPrice: price.listingPrice,
                    showPriceCut: price.listingPrice < price.mrp,
                    currency: price.currency
                },
                backgroundColors: ["#67b1fc", "#7472f3"],
                cardAction,
                extraAction,
                packPoints,
                offers: offerTitles,
            }
        }))
        const gymPackHeaderWidget = new GymPackHeaderWidget()
        gymPackHeaderWidget.header = "WHAT YOU GET"
        gymPackHeaderWidget.title = "Unlimited Gym Access"
        gymPackHeaderWidget.subTitle = "Workout at ELITE gyms anywhere, anytime"
        gymPackHeaderWidget.image = "/image/gymfit/gym_pack_trainer_image.png"
        const firstBrandSlice = presentableBrands.length > 1 ? presentableBrands.slice(0, presentableBrands.length / 2) : presentableBrands
        const secondBrandSlice = presentableBrands.length > 1 ? presentableBrands.slice(presentableBrands.length / 2, presentableBrands.length) : []
        gymPackHeaderWidget.gymLogos = [firstBrandSlice, secondBrandSlice]
        gymPackHeaderWidget.packBenefits = [
            {
                title: "Unlimited Access to Gyms",
                image: "image/gymfit/runner.png"
            },
            {
                title: "Top Quality Gyms",
                image: "image/gymfit/location.png"
            },
            {
                title: "Covid Safety Measures",
                image: "image/gymfit/covid_safety.png"
            },
            {
                title: "Best-In-Class Equipment",
                image: "image/gymfit/equipment.png"
            },
            {
                title: "Access to Cult & Gym Group Classes",
                image: "image/gymfit/gym_access.png"
            },
            {
                title: "Certified Trainers",
                image: "image/gymfit/certified_trainer.png"
            }
        ]
        this.widgets.push(await gymPackHeaderWidget.buildView(interfaces, userContext, queryParams))
        const gymPacksWidget = new GymPacksWidget()
        gymPacksWidget.title = "AVAILABLE PACKS"
        gymPacksWidget.gymPacks = presentableGymPacks
        this.widgets.push(await gymPacksWidget.buildView(interfaces, userContext, queryParams))
        return this
    }
}
