import { injectable } from "inversify"
import { ClassCountProgressWidget as IClassCountProgressWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { GymfitCenterCategory, GymfitMembership, GymfitMembershipState } from "@curefit/gymfit-common"
import * as _ from "lodash"
import { times } from "lodash"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { PackProgressWidget, ProgressPills } from "@curefit/apps-common"
import { TimeUtil } from "@curefit/util-common"
import GymfitUtil from "../../../util/GymfitUtil"


export declare enum ProgressType {
    LINE = "LINE",
    PILL = "PILL",
}

@injectable()
export class ClassCountProgressWidget extends IClassCountProgressWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const currentTime = Date.now()
        const memberships = await interfaces.membershipService.getMembershipsForUser(userContext.userProfile.userId, "curefit", ["GYMFIT_GA", "GYMFIT_GX"], [], currentTime, currentTime)
        if (!_.isEmpty(memberships)) {
            const activeMembership = memberships.find(membership => membership.status === "PURCHASED")
            if (!_.isEmpty(activeMembership)) {
                const cultBenefit = GymfitUtil.getCultBenefit(activeMembership)
                if (!_.isEmpty(cultBenefit)) {
                    return null
                }

                // const membershipDetail = await  interfaces.gymfitService.getMembershipDetailsById(activeMembership.id, userContext.userProfile.userId)
                // const filterCultCenterCategories = membershipDetail.restrictions?.filter(restriction =>
                //     (restriction.centerCategories.find(category => category === GymfitCenterCategory.CULT))
                // )
                // const endDate = TimeUtil.getMomentForDateString(membershipDetail.endDate, tz)
                // const startDate = TimeUtil.getMomentForDateString(membershipDetail.startDate, tz)
                // const gymMembershipDurationInMonths = Math.ceil(endDate.diff(startDate, "days") / 30)
                // if (filterCultCenterCategories.length) {
                //     const filterCultCenterCategory = filterCultCenterCategories[0]
                //     const { maxCount, usedCount } = filterCultCenterCategory
                //     const totalMaxCount = maxCount
                //     const progress = {
                //         leftText: "Available Sessions",
                //         rightText: `${totalMaxCount - usedCount} out of ${totalMaxCount}`,
                //         total: totalMaxCount,
                //         completed: usedCount,
                //         type: "LINE",
                //         progressData: {},
                //     }
                //     if (totalMaxCount <= 6) {
                //         progress.type = "PILL"
                //         progress.progressData = {
                //             progressData: [
                //                 ...times(usedCount, (count) => ({
                //                         backgroundColor: "#212223",
                //                     })
                //                 )
                //                 , ...times(totalMaxCount - usedCount, () => ({
                //                         backgroundColor: "#ededed",
                //                     })
                //                 )
                //             ],
                //             numPills: totalMaxCount
                //         } as ProgressPills
                //     } else {
                //         progress.progressData = {
                //             total: totalMaxCount,
                //             completed: usedCount,
                //             noPadding: true,
                //             progressBarColor: "#212223",
                //         } as PackProgressWidget
                //     }

                const { maxTickets, ticketsUsed } = cultBenefit
                const progress = {
                    leftText: "Available Sessions",
                    rightText: `${maxTickets - ticketsUsed} out of ${maxTickets}`,
                    total: maxTickets,
                    completed: ticketsUsed,
                    type: "LINE",
                    progressData: {},
                }
                if (maxTickets <= 6) {
                    progress.type = "PILL"
                    progress.progressData = {
                        progressData: [
                            ...times(ticketsUsed, (count) => ({
                                    backgroundColor: "#212223",
                                })
                            )
                            , ...times(maxTickets - ticketsUsed, () => ({
                                    backgroundColor: "#ededed",
                                })
                            )
                        ],
                        numPills: maxTickets
                    } as ProgressPills
                } else {
                    progress.progressData = {
                        total: maxTickets,
                        completed: ticketsUsed,
                        noPadding: true,
                        progressBarColor: "#212223",
                    } as PackProgressWidget
                }
                this.title = "Free Cult Classes"
                this.action = {
                    actionType: "NAVIGATION",
                    title: "BOOK",
                    url: "curefit://classbookingv2?pageFrom=addActivity&productType=FITNESS",
                }
                this.progress = progress as IClassCountProgressWidget["progress"]
                return this
            }
        }
        return null
    }
}
