import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { SlotUtil } from "@curefit/eat-util"
import { TimeUtil } from "@curefit/util-common"
import { MealSlot } from "@curefit/eat-common"
import { capitalizeFirstLetter } from "@curefit/util-common"
import { MealUtil } from "@curefit/base-utils"
import { Action } from "@curefit/vm-models"
import { CFUserProfile } from "../CFUserProfile"

export class MealSlotSelectorWidget extends BaseWidget {

    title: string
    subTitle: string
    day: string
    nextSlot: MealSlot
    currentSlot: MealSlot
    action: Action
    allSlots: MealSlot[]

    constructor(currentSlot: MealSlot, nextSlot: MealSlot, day: string, allSlots: MealSlot[]) {
        super("MEAL_SLOT_SELECTOR_WIDGET")
        this.day = day
        this.nextSlot = nextSlot
        this.allSlots = allSlots
        this.action = {
            actionType: "REFRESH_PAGE",
            meta: {
                options: []
            }
        }
        this.currentSlot = currentSlot
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        const deliveryArea = await userProfile.deliveryAreaPromise
        const areaTz = await interfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId)
        this.title = capitalizeFirstLetter(TimeUtil.getDayText(this.day, areaTz, {
            sameDay: "[Today's]",
            nextDay: "[Tomorrow's]",
            nextWeek: "dddd['s]",
            sameElse: "dddd['s]"
        })) + " " + capitalizeFirstLetter(this.nextSlot)
        this.title = this.title.toUpperCase()
        this.allSlots.forEach((mealSlot) => {
            let menuDay = TimeUtil.todaysDate(userContext.userProfile.timezone)
            const isOpen = MealUtil.isAnySlotHardCutOffNotPassed(mealSlot, deliveryArea.channel, menuDay, areaTz) && mealSlot !== this.currentSlot
            if (!isOpen) {
                menuDay = MealUtil.getNextAvailableMenuDate(deliveryArea.channel, areaTz)
            }
            const option: { title: string, payload: any } = {
                title: capitalizeFirstLetter(TimeUtil.getDayText(menuDay, areaTz, {
                    sameDay: "[Today's]",
                    nextDay: "[Tomorrow's]",
                    nextWeek: "dddd['s]",
                    sameElse: "dddd['s]"
                })) + " " + capitalizeFirstLetter(mealSlot),
                payload: {
                    date: menuDay,
                    mealSlot: mealSlot,
                    selectedTab: "eatlater"
                }
            }
            this.action.meta.options.push(option)
        })
        this.action.meta.options.sort((option1: any, option2: any) => {
            const slotTime1 = SlotUtil.getMealSlotTimes(option1.payload.mealSlot)
            const slotTime2 = SlotUtil.getMealSlotTimes(option2.payload.mealSlot)
            return option1.payload.date === option2.payload.date ? SlotUtil.compare(slotTime1.start, slotTime2.start) : option1.payload.date.localeCompare(option2.payload.date)
        })
        return this
    }
}
