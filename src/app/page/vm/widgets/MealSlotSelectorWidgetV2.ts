import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { SlotUtil } from "@curefit/eat-util"
import { TimeUtil } from "@curefit/util-common"
import * as momentTz from "moment-timezone"
import { DeliveryArea, MealSlot, MenuType } from "@curefit/eat-common"
import { capitalizeFirstLetter } from "@curefit/util-common"
import { MealUtil } from "@curefit/base-utils"
import { Action } from "@curefit/vm-models"
import { CFUserProfile } from "../CFUserProfile"
import * as _ from "lodash"

export class MealSlotSelectorWidgetV2 extends BaseWidget {

    title: string
    subTitle: string
    day: string
    selectedMealSlot: MenuType
    currentSlot: MealSlot
    action: Action
    allSlots: MealSlot[]
    allDeliveryAreas: DeliveryArea[]

    constructor(currentSlot: MealSlot, selectedMealSlot: MenuType, day: string, allSlots: MealSlot[], allDeliveryAreas: DeliveryArea[]) {
        super("MEAL_SLOT_SELECTOR_WIDGET_V2")
        this.day = day
        this.selectedMealSlot = selectedMealSlot
        this.allSlots = allSlots
        this.action = {
            actionType: "REFRESH_PAGE",
            meta: {
                options: []
            }
        }
        this.currentSlot = currentSlot
        this.allDeliveryAreas = allDeliveryAreas ? allDeliveryAreas : []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        let deliveryArea = await userProfile.deliveryAreaPromise
        const tz = await interfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId)
        if (this.day === TimeUtil.todaysDate(tz) && (this.selectedMealSlot === this.currentSlot || this.selectedMealSlot === "ALL")) {
            this.title = "EAT NOW"
        } else {
            this.title = capitalizeFirstLetter(momentTz.tz(this.day, "YYYY-MM-DD", tz).calendar(null, {
                sameDay: "[Today's]",
                nextDay: "[Tomorrow's]",
                nextWeek: "dddd['s]",
                sameElse: "dddd['s]"
            })) + " " + capitalizeFirstLetter(this.selectedMealSlot)
            this.title = this.title.toUpperCase()
        }
        if (!_.isEmpty(this.allDeliveryAreas)) {
            // top one is the one with highest priority
            deliveryArea = this.allDeliveryAreas[0]
            this.allSlots = await interfaces.deliveryAreaService.getMealSlotsForArea(deliveryArea.areaId)
        }
        if (!deliveryArea.scheduledCartDisabled) {
            this.allSlots.forEach((mealSlot) => {
                let menuDay = TimeUtil.todaysDate(tz)
                const isOpen = MealUtil.isAnySlotHardCutOffNotPassed(mealSlot, deliveryArea.channel, menuDay, tz) && mealSlot !== this.currentSlot
                if (!isOpen) {
                    menuDay = MealUtil.getNextAvailableMenuDate(deliveryArea.channel, tz)
                }
                const option: { title: string, payload: any } = {
                    title: capitalizeFirstLetter(momentTz.tz(menuDay, "YYYY-MM-DD", tz).calendar(null, {
                        sameDay: "[Today's]",
                        nextDay: "[Tomorrow's]",
                        nextWeek: "dddd['s]",
                        sameElse: "dddd['s]"
                    })) + " " + capitalizeFirstLetter(mealSlot),
                    payload: {
                        date: menuDay,
                        mealSlot: mealSlot,
                        selectedTab: "eatordernow"
                    }
                }
                this.action.meta.options.push(option)
            })
        }
        // check if any of the delivery area is serviceable right now for the location
        for (const area of this.allDeliveryAreas) {
            const allMealSlots = await interfaces.deliveryAreaService.getMealSlotsForArea(area.areaId)
            if (SlotUtil.isServiceable(allMealSlots, area, tz).isServiceable) {
                this.action.meta.options.push({
                    title: "Eat Now",
                    payload: {
                        date: TimeUtil.todaysDate(tz),
                        // TODO - temp fix for multi point fulfilment order now condition
                        // mealSlot: this.currentSlot,
                        selectedTab: "eatordernow"
                    }
                })
                break
            }
        }
        this.action.meta.options.sort((option1: any, option2: any) => {
            const slotTime1 = SlotUtil.getMealSlotTimes(option1.payload.mealSlot)
            const slotTime2 = SlotUtil.getMealSlotTimes(option2.payload.mealSlot)
            return option1.payload.date === option2.payload.date ? SlotUtil.compare(slotTime1.start, slotTime2.start) : option1.payload.date.localeCompare(option2.payload.date)
        })
        return this
    }
}
