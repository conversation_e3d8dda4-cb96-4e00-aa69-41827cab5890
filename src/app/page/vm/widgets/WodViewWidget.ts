import { Action, IBaseWidget, SessionInfo, UserProfile, CenterSelectionAction } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { UrlPathBuilder } from "@curefit/product-common"
import * as _ from "lodash"
import { ActionUtil } from "@curefit/base-utils"
import { ICatalogueService, CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import { TimeUtil } from "@curefit/util-common"
import LocationUtil from "../../../util/LocationUtil"
import CultUtil, {
    BOOTCAMP_PROD_WORKOUT_ID,
    BOOTCAMP_STAGE_WORKOUT_ID,
    transformCultSummary
} from "../../../util/CultUtil"
import { SimpleWod } from "@curefit/fitness-common"
import { ProductType } from "@curefit/product-common"
import { Header } from "@curefit/vm-models"
import { CultCenter, CultClass, CultWorkout } from "@curefit/cult-common"
import { CFUserProfile } from "../CFUserProfile"
import { WodWidget } from "@curefit/vm-models"
import ClassViewV2, { ClassState } from "../../../cult/ClassViewV2"
import AppUtil from "../../../util/AppUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { User } from "@curefit/user-common"
import { TransformUtil } from "../../../util/TransformUtil"

export const WORKOUT_PRIORITY_MAP = new Map<number, number>()
WORKOUT_PRIORITY_MAP.set(340, 1.0) // s & c enhanced with screen
WORKOUT_PRIORITY_MAP.set(341, 2.0) // HRX enhanced with screen
WORKOUT_PRIORITY_MAP.set(5, 3.0) // s & c
WORKOUT_PRIORITY_MAP.set(22, 4.0) // HRX Workout
WORKOUT_PRIORITY_MAP.set(1, 5.0) // Yoga
WORKOUT_PRIORITY_MAP.set(3, 6.0) // Zumba
WORKOUT_PRIORITY_MAP.set(4, 7.0) // Boxing
WORKOUT_PRIORITY_MAP.set(57, 8.0) // Prowl
WORKOUT_PRIORITY_MAP.set(23, 9.0) // Football
WORKOUT_PRIORITY_MAP.set(39, 10.0) // Tag Rugby
WORKOUT_PRIORITY_MAP.set(40, 11.0) // Sports Conditioning

export const CLASS_STATE_MAP = new Map<ClassState, number>()
CLASS_STATE_MAP.set("BOOKED", 1.0)
CLASS_STATE_MAP.set("AVAILABLE", 2.0)
CLASS_STATE_MAP.set("SEAT_NOT_AVAILABLE", 3.0)


export const WOD_PRODUCT_TYPES = ["FITNESS", "MIND"]

enum Intensity {
    lowIntensity = 1,
    mediumIntensity,
    highIntensity
}

const INTENSITY_MAP = {
    [Intensity.lowIntensity]: "Low Intensity",
    [Intensity.mediumIntensity]: "Medium Intensity",
    [Intensity.highIntensity]: "High Intensity",
}

interface WodItem {
    workoutId: string
    workoutName: string
    imageUrl?: string,
    videoUrl?: string,
    action?: Action
    timeSlots: {
        timeText: string
        classDetail: ClassViewV2
        action: Action
    }[]
    moreTimeSlot?: {
        text: string
        action: Action
    }
    wodDetails?: {title: string, subTitle?: string}[] | {title: string, label: string, subLabel: string}
}

export class WodViewWidget extends WodWidget {

    sections: {
        tabId: string
        id: string
        value: string
        name: string
    }[] = []

    lists?: { [id: string]: WodItem[] } = {}

    header: Header

    centerSelectionHeader?: {
        title: string,
        seemore: Action
    }

    isNewWodViewWidget: boolean

    theme: "LIGHT" | "DARK"

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (userContext.userProfile.cityId === "Dubai") {
            return undefined
        }
        this.showDivider = false
        const baseService = this.productType === "FITNESS" ? interfaces.cultFitService : interfaces.mindFitService
            this.isNewWodViewWidget = await AppUtil.isNewWodViewWidgetSupported(userContext)
        const isAuroraThemeWodViewWidgetSupported = AppUtil.isAuroraThemeWodViewWidgetSupported(userContext)
        const isAuroraThemeWodViewWidgetSeeMoreActionSupported = AppUtil.isAuroraThemeWodViewWidgetSeeMoreActionSupported(userContext)
        const workoutsPromise = baseService.browseFitnessWorkout("CUREFIT_API", false, "ADULT", userContext.userProfile.city.cultCityId)
        const preferredCenterAndTitle = await this.getPreferredCenterAndTitle(userContext, userContext.userProfile, userContext.sessionInfo, interfaces)
        const center = preferredCenterAndTitle.center
        // center can be null if no center is live for new city launches
        if (_.isEmpty(center)) {
            interfaces.logger.error("Wod widget building error: center not found for current city")
            return undefined
        }
        const centerId = center.id.toString()
        const classesPromise = baseService.browseFitnessClass(centerId, userContext.userProfile.userId, "CF-APP", true, false)
        const productCountsPromise = interfaces.questService.getProductCounts(userContext.userProfile.userId, this.productType, 30)
        const classesResponse = await classesPromise
        // classesResponse.classes = classesResponse.classes.filter(cultClass => {
        //     return cultClass.workoutID !== (process.env.ENVIRONMENT === "STAGE" ? BOOTCAMP_STAGE_WORKOUT_ID : BOOTCAMP_PROD_WORKOUT_ID)
        // })
        const action: CenterSelectionAction = {
            title: isAuroraThemeWodViewWidgetSupported ? center.name : this.isNewWodViewWidget ? "CHANGE" : "ALL CENTERS",
            actionType: "SELECT_CENTER",
            showFavourite: false,
            productType: this.productType
        }
        if (isAuroraThemeWodViewWidgetSupported) {
            this.widgetType = "WOD_WIDGET_V2"
            this.header = !isAuroraThemeWodViewWidgetSeeMoreActionSupported ? {
                title: "Classes at ",
                seemore: action,
            } : {
                title: "Classes at ",
                titleProps: {
                    action
                },
                seemore: this.header && this.header.seemore,
            }
        }
        else if (this.isNewWodViewWidget) {
            if (_.isEmpty(this.header)) {
                this.header = {
                    title: "Workout of the day"
                }
            }
            this.centerSelectionHeader = {
                title: center.name,
                seemore: action
            }
        } else {
            this.header = {
                title: preferredCenterAndTitle.headerTitle,
                subTitle: center.name,
                seemore: action
            }
        }

        const set = new Set<string>()
        classesResponse.classes.forEach(classs => {
            set.add(classs.date)
        })
        const dates = Array.from<string>(set).sort((a, b) => { return a < b ? -1 : 1 })
        // There is no upcoming classes and hence stop processing
        if (_.isEmpty(dates)) {
            interfaces.logger.error("No upcoming class for " + centerId)
            return undefined
        }
        // Building section list based on available class dates
        this.buildSections(dates, userContext)

        const lastDate = this.sections[this.sections.length - 1].id
        const dateWorkoutPairToWodItem: Map<string, { item: WodItem, date: string }> = await this.dateWorkoutPairToWodItem(workoutsPromise, classesResponse.classes, classesResponse.workouts, centerId,
            this.productType, lastDate, userContext, interfaces, this.isNewWodViewWidget, isAuroraThemeWodViewWidgetSupported)
        await this.buildWodListForEachDate(centerId, dateWorkoutPairToWodItem, userContext, interfaces, isAuroraThemeWodViewWidgetSupported)
        await this.sortWodItems(productCountsPromise)
        this.lists = _.omitBy(this.lists, _.isEmpty)
        if (_.isEmpty(this.lists)) {
            return undefined
        }
        this.sections = this.sections.filter(section => { return !_.isEmpty(this.lists[section.id]) })
        if (_.isEmpty(this.sections)) {
            return undefined
        }
        this.theme = this.productType === "FITNESS" ? "DARK" : "LIGHT"
        return this
    }

    private buildSections(sortedDates: string[], userContext: UserContext) {
        const dates = sortedDates.slice(0, this.isNewWodViewWidget ? 4 : 3)
        let i = 1
        dates.forEach(date => {
            this.sections.push({
                id: date,
                value: date,
                tabId: "tab_" + i,
                name: TimeUtil.getDayText(date, userContext.userProfile.timezone)
            })
            i++
            this.lists[date] = []
        })
    }

    private async buildWodListForEachDate(centerId: string, dateWorkoutPairToWodItem: Map<string, { item: WodItem, date: string }>, userContext: UserContext, interfaces: CFServiceInterfaces, isAuroraThemeWodViewWidgetSupported: boolean) {
        const dateWorkoutToWodItemValues = dateWorkoutPairToWodItem.values()
        let wodItemForADate = dateWorkoutToWodItemValues.next().value
        const currentUser: User = await userContext.userPromise
        // const nuxLandingPageUrl = await CultUtil.getNuxLandingPageUrl(userContext, currentUser, interfaces.hamletBusiness, interfaces.userCache, interfaces.announcementBusiness)
        while (wodItemForADate) {
            if (wodItemForADate.item) {
                const date = wodItemForADate.date
                const workoutCategoryId = wodItemForADate.item.timeSlots.length > 0 && wodItemForADate.item.timeSlots[0].classDetail.workoutId
                    ? wodItemForADate.item.timeSlots[0].classDetail.workoutId.toString()
                    : undefined
                const classBookingUrl = ActionUtil.getBookCultClassUrl(this.productType, true, "wodWidget", workoutCategoryId, centerId, undefined, date)
                // Sort the time slot in ascending order of time.
                wodItemForADate.item.timeSlots.sort((a: any, b: any) => {
                    const stateBasedPriorityForA = CLASS_STATE_MAP.get(a.classDetail.state)
                    const stateBasedPriorityForB = CLASS_STATE_MAP.get(a.classDetail.state)

                    if (stateBasedPriorityForA === stateBasedPriorityForB) {
                        return a.classDetail.startTime < b.classDetail.startTime ? -1 : 1
                    } else {
                        return stateBasedPriorityForA < stateBasedPriorityForB ? -1 : 1
                    }
                })

                // Show only 2 timelsots
                if (wodItemForADate.item.timeSlots.length > 2) {
                    wodItemForADate.item.timeSlots = wodItemForADate.item.timeSlots.slice(0, 2)
                    // if waitList class is shown, UI renders More item outside of the card because of design, so show only 1 item if any of the two classes are waitlisted
                    const waitListClassPresent = wodItemForADate.item.timeSlots[0].classDetail.state === "WAITLIST_AVAILABLE" || wodItemForADate.item.timeSlots[1].classDetail.state === "WAITLIST_AVAILABLE"
                    if (!isAuroraThemeWodViewWidgetSupported && waitListClassPresent) {
                        wodItemForADate.item.timeSlots = wodItemForADate.item.timeSlots.slice(0, 1)
                    }
                    wodItemForADate.item.moreTimeSlot = {
                        text: "MORE",
                        action: {
                            actionType: "NAVIGATION",
                            url: classBookingUrl
                        }
                    }
                }
                if (isAuroraThemeWodViewWidgetSupported) {
                    wodItemForADate.item.action = {
                        actionType: "NAVIGATION",
                        url: classBookingUrl
                    }
                }

                const bootcampWorkout = await CultUtil.getBootcampWorkoutIds()
                const isBootcampWorkout: number = _.find(bootcampWorkout, (workoutId) => Number(wodItemForADate.item.workoutId) === workoutId)
                const showBootcampClasses: boolean = !_.isNil(isBootcampWorkout)
                if (showBootcampClasses) {
                    wodItemForADate.item.action = TransformUtil.getBootcampModalAction()
                }

                this.lists[date].push(wodItemForADate.item)
            }
            wodItemForADate = dateWorkoutToWodItemValues.next().value
        }
    }

    private async sortWodItems(productCountsPromise: Promise<{ productId: string, count: number }[]>) {
        const productCounts = await productCountsPromise
        const workoutAttendeMap: { [key: string]: number } = {}
        productCounts.forEach(productCount => {
            workoutAttendeMap[productCount.productId] = productCount.count
        })

        Object.keys(this.lists).forEach(date => {
            const wodItems = this.lists[date]
            // Sort the wod item based on the number of classes attended.
            wodItems.sort((a, b) => {
                const productIdForA = this.productType === "FITNESS" ? CatalogueServiceV2Utilities.getCultWorkoutProductId(Number.parseInt(a.workoutId)) :
                    CatalogueServiceV2Utilities.getMindWorkoutProductId(Number.parseInt(a.workoutId))
                const productIdForB = this.productType === "FITNESS" ? CatalogueServiceV2Utilities.getCultWorkoutProductId(Number.parseInt(b.workoutId)) :
                    CatalogueServiceV2Utilities.getMindWorkoutProductId(Number.parseInt(b.workoutId))
                const numAWorkout = workoutAttendeMap[productIdForA]
                const numBWorkout = workoutAttendeMap[productIdForB]
                // To show experience widget as first
                if (a.workoutId === "experience") {
                    return -1
                }
                if (b.workoutId === "experience") {
                    return 1
                }

                // if user have not attended both workout pick up based on priority ordering
                if (!numAWorkout && !numBWorkout) {
                    const priorityForA = WORKOUT_PRIORITY_MAP.get(Number.parseInt(a.workoutId))
                    const priorityForB = WORKOUT_PRIORITY_MAP.get(Number.parseInt(b.workoutId))
                    if (!priorityForA) {
                        return 1
                    }
                    if (!priorityForB) {
                        return -1
                    }

                    return priorityForA < priorityForB ? -1 : 1
                }

                // if user have  attended some workout pick up based on num class attended
                if (!numAWorkout) {
                    return 1
                }
                if (!numBWorkout) {
                    return -1
                }

                return numAWorkout > numBWorkout ? -1 : 1
            })
        })
    }

    private async getPreferredCenterAndTitle(userContext: UserContext, userProfile: UserProfile, sessionInfo: SessionInfo, interfaces: CFServiceInterfaces): Promise<{ center: CultCenter, headerTitle: string }> {
        let headerTitle
        let center
        const tz = userContext.userProfile.timezone
        if (this.productType === "FITNESS") {
            if (userProfile.cultCenterId) {
                // interfaces.logger.info("WOD Widget Debugging -> cultCenterId in session data: " + userProfile.cultCenterId)
                headerTitle = "Preferred Center"
                center = await interfaces.catalogueService.getCultCenter(userProfile.cultCenterId)
                if (!_.isEmpty(center) && center.state === "INACTIVE") {
                    center = null
                }
            }

            const city = await interfaces.cityService.getCityById(userProfile.cityId)
            if (_.isEmpty(center)) {
                headerTitle = "Preferred Center"
                const preference = await interfaces.cultFitService.getPreferenceByKey(city.cultCityId, userProfile.userId, "CUREFIT_API", userContext.userProfile.subUserId, "USER_BOOKING_V2_LAST_BOOKED_CENTER")
                if (preference && !_.isEmpty(preference.settings) && preference.settings[0].value) {
                    center = await interfaces.catalogueService.getCultCenter(preference.settings[0].value)
                }
            }

            if (_.isEmpty(center)) {
                // interfaces.logger.info("WOD Widget Debugging -> session data center is empty")
                if (!_.isNil(sessionInfo.lat) && !_.isNil(sessionInfo.lon) && !_.isNaN(sessionInfo.lat) && !_.isNaN(sessionInfo.lon)) {
                    const centers = await interfaces.cultFitService.browseFitnessCenter(tz, "CUREFIT_API", true, city.cultCityId, true,
                        undefined, false, userProfile.subUserId || userProfile.userId)
                    if (!_.isEmpty(centers)) {
                        center = this.getClosestCenter(sessionInfo.lat, sessionInfo.lon, centers)
                        // interfaces.logger.info("WOD Widget Debugging -> closest center: " + center.name)
                        headerTitle = "Nearest Center"
                    }
                } else {
                    const centerId = userProfile.city.defaultCultCenter
                    center = await interfaces.catalogueService.getCultCenter(centerId)
                    headerTitle = "Preferred Center"
                }
            }
        } else if (this.productType === "MIND") {
            if (userProfile.mindCenterId) {
                headerTitle = "Preferred Center"
                center = await interfaces.catalogueService.getCultMindCenter(userProfile.mindCenterId)
                if (!_.isEmpty(center) && center.state === "INACTIVE") {
                    center = null
                }
            }
            if (_.isEmpty(center)) {
                const city = await interfaces.cityService.getCityById(userProfile.cityId)
                if (!_.isNil(sessionInfo.lat) && !_.isNil(sessionInfo.lon)) {
                    const centers = await interfaces.mindFitService.browseFitnessCenter(tz, "CUREFIT_API", true, city.cultCityId, true,
                        undefined, false, userProfile.subUserId || userProfile.userId)
                    if (!_.isEmpty(centers)) {
                        center = this.getClosestCenter(sessionInfo.lat, sessionInfo.lon, centers)
                        headerTitle = "Nearest Center"
                    }
                } else {
                    const centerId = userProfile.city.defaultMindCenter
                    center = await interfaces.catalogueService.getCultMindCenter(centerId)
                    headerTitle = "Preferred Center"
                }
            }
        }
        return { center: center, headerTitle: headerTitle }
    }

    private getClosestCenter(lat: number, lon: number, centers: CultCenter[]): CultCenter {
        const closestCenter = centers.reduce((previous: CultCenter, next: CultCenter) => {
            if (_.isNil(previous)) {
                return next
            }
            if (_.isNil(next)) {
                return previous
            }

            const lat1: number = previous.Address.latitude
            const lon1: number = previous.Address.longitude
            const lat2: number = next.Address.latitude
            const lon2: number = next.Address.longitude
            if (!_.isNumber(lat1) || !_.isNumber(lon1))
                return next
            if (!_.isNumber(lat2) || !_.isNumber(lon2))
                return previous
            const distance1: number = LocationUtil.getDistanceFromLatLonInKm(lat1, lon1, lat, lon)
            const distance2: number = LocationUtil.getDistanceFromLatLonInKm(lat2, lon2, lat, lon)
            return distance1 < distance2 ? previous : next
        }, undefined)
        return closestCenter
    }
    private async dateWorkoutPairToWodItem(workoutsPromise: Promise<CultWorkout[]>, classes: CultClass[], workouts: CultWorkout[], centerId: string, productType: ProductType, lastDate: string, userContext: UserContext, interfaces: CFServiceInterfaces, isNewWodViewWidget: boolean, isAuroraThemeWodViewWidgetSupported: boolean): Promise<Map<string, { item: WodItem, date: string }>> {
        const workoutsMap: { [id: string]: CultWorkout } = {}
        const classWorkoutsMap: { [id: string]: CultWorkout } = {}
        const workoutsData = []
        workoutsData.push(... await workoutsPromise)
        workouts.forEach(workout => {
            classWorkoutsMap[workout.id] = workout
            classWorkoutsMap[workout.id].tenantID = productType === "FITNESS" ? 1 : 2
        })
        workoutsData.forEach(workout => {
            workoutsMap[workout.id] = workout
        })

        const wodMap: { [id: string]: SimpleWod } = {}
        const wodMapBuildingPromise = _.map(classes, async classs => {
            let wod: SimpleWod = null
            if (classs.wodId) {
                // interfaces.logger.info("WOD Widget Debugging -> getting wod for wodid: ", classs.wodId)
                if (_.isEmpty(wodMap[classs.wodId])) {
                    // interfaces.logger.info("WOD Widget Debugging -> wod not present in wodmap for wodid: ", classs.wodId)
                    wod = await interfaces.herculeService.getSimpleWodById(classs.wodId)
                    wodMap[wod._id] = wod
                }
            }
        })
        const isNewBookingFlow: boolean = true
        await Promise.all(wodMapBuildingPromise)
        const dateWorkoutPairToWodItem: Map<string, { item: WodItem, date: string }> = new Map<string, { item: WodItem, date: string }>()
        // const currentUser: User = await userContext.userPromise
        // const nuxLandingPageUrl = await CultUtil.getNuxLandingPageUrl(userContext, currentUser, interfaces.hamletBusiness, interfaces.userCache, interfaces.announcementBusiness)

        const [isBootcampSupported, isBootcampBookingExperimentSupported, isBootcampImpressionCompleted]: any = await Promise.all([
            CultUtil.iBootcampBookingSupported(userContext, interfaces),
            AppUtil.isBootcampBookingExperimentSupported(userContext, interfaces.hamletBusiness),
            TransformUtil.isBootcampImpressionCompleted(userContext.userProfile.userId, interfaces.redisDao)
        ])
        const bootcampWorkout = await CultUtil.getBootcampWorkoutIds()
        const bootcampAction = TransformUtil.getBootcampModalAction()

        for (const classs of classes) {
            // To limit only for 3  days class and to show only classes from current center and exlcuding nearby center class
            if (classs.wodId && classs.date <= lastDate && parseInt(centerId) === parseInt(classs.centerID)) {
                const key = classs.date + "_" + classs.workoutID
                if (workoutsMap[classs.workoutID] === undefined) {
                    interfaces.logger.warn("Workout data improper for class ", {classs})
                    continue
                }
                const wodClasses: { item: WodItem, date: string } = dateWorkoutPairToWodItem.get(key) || {
                    item: {
                        workoutName: workoutsMap[classs.workoutID].name,
                        workoutId: classs.workoutID.toString(),
                        timeSlots: [],
                        wodDetails: this.isNewWodViewWidget ? this.getWodDetails(wodMap[classs.wodId], workoutsMap[classs.workoutID], isAuroraThemeWodViewWidgetSupported) : undefined
                    },
                    date: classs.date
                }

                const wodItem = wodClasses.item
                const document = _.find(workoutsMap[classs.workoutID].documents, document => {
                    return isAuroraThemeWodViewWidgetSupported ? document.tagID === 102 : document.tagID === 11
                })
                if (document && !wodItem.imageUrl) {
                    wodItem.imageUrl = UrlPathBuilder.prefixSlash(document.URL)
                }
                const getwaitlistCnfProbabilityColor = await AppUtil.isAppNewWaitlistColorCodingSupported(interfaces.segmentService, userContext)
                const classDetail = new ClassViewV2(classs, classWorkoutsMap, getwaitlistCnfProbabilityColor, false, null, isNewBookingFlow, null, null, null, null, null, null, null, userContext, interfaces)
                if (classDetail.state == "WAITLIST_FULL" || classDetail.state == "SEAT_NOT_AVAILABLE") {
                    continue
                }

                const isBootcampWorkout: number = _.find(bootcampWorkout, (workoutId) => Number(classs.workoutID) === workoutId)
                if (!isBootcampSupported) {
                    if (isBootcampWorkout) {
                        continue
                    }
                }
                const showBootcampClasses: boolean = !_.isNil(isBootcampWorkout) && isBootcampSupported
                if (showBootcampClasses) {
                    if (isBootcampBookingExperimentSupported) {
                        if (isBootcampImpressionCompleted) {
                            continue
                        }
                    } else {
                        continue
                    }
                }

                let timeSlotAction: Action
                if (isNewBookingFlow) {
                    let url
                    if (classs.bookingNumber) {
                        url = `curefit://cultclass?bookingNumber=${classs.bookingNumber}`
                    } else if (classs.wlBookingNumber) {
                        url = `curefit://cultclass?bookingNumber=${classs.wlBookingNumber}&isWaitlistBooking=true`
                    } else {
                        url = `curefit://prebookclass?classId=${classs.id}`
                    }
                    timeSlotAction = {
                        actionType: "NAVIGATION",
                        url: url
                    }
                } else {
                    timeSlotAction = { actionType: "CULT_CLASS_BOOKING" }
                }
                // if (nuxLandingPageUrl) {
                //     timeSlotAction = {
                //         actionType: "NAVIGATION",
                //         url: nuxLandingPageUrl
                //     }
                // }
                if (showBootcampClasses) {
                    timeSlotAction = bootcampAction
                }
                wodItem.timeSlots.push({
                    classDetail: classDetail,
                    timeText: TimeUtil.getMomentForDateString(classs.startTime, userContext.userProfile.timezone, "hh:mm:ss").format("LT"),
                    action: timeSlotAction
                })
                // Add the wod video details if not populated by previous class population
                if (!wodItem.action && classs.wodId && !isAuroraThemeWodViewWidgetSupported) {
                    const wod = wodMap[classs.wodId]
                    if (wod) { // safety check if wod is not in cache
                        const video = _.find(wod.media, { type: "VIDEO" })
                        let thumb
                        if (isNewWodViewWidget) {
                            thumb = _.find(wod.media, item => item.type === "IMAGE" && item.url.includes("_V2."))
                        }
                        if (!thumb) {
                            thumb = _.find(wod.media, { type: "IMAGE" })
                        }
                        wodItem.action = {
                            actionType: AppUtil.isWeb(userContext) ? "PLAY_VIDEO" : "NAVIGATION",
                            url: video ? ActionUtil.videoUrl(video.url) : undefined,
                            meta: {isVideo: true}
                        }
                        if (thumb) {
                            wodItem.imageUrl = UrlPathBuilder.prefixSlash(thumb.url)
                        }
                    }
                }
                if (showBootcampClasses) {
                    wodItem.action = bootcampAction
                    wodItem.imageUrl = "image/transform/bootcamp_wod_image1.png"
                }
                dateWorkoutPairToWodItem.set(key, wodClasses)
            }
        }

        return dateWorkoutPairToWodItem
    }

    private getWodDetails(wod: SimpleWod, workout: CultWorkout, isAuroraThemeWodViewWidgetSupported: boolean): {title: string, subTitle?: string}[] | {title: string, label: string, subLabel: string} {
        if (isAuroraThemeWodViewWidgetSupported) {
            const {name = "", otherAttributes: { intensity: intensityKey = "", averageCaloriesBurnt = ""} = {}} = workout || {}
            const {focus = ""} = wod || {}
            // @ts-ignore
            const intensity = intensityKey && INTENSITY_MAP[Number(intensityKey)] || ""
            return {
                title: focus ? name : "",
                label: focus ? focus : name,
                subLabel: intensity && averageCaloriesBurnt ? `${intensity} • ${averageCaloriesBurnt} kcal` : intensity ? intensity
                    : averageCaloriesBurnt ? `${averageCaloriesBurnt} kcal` : "",
            }
        }
        const wodDetails = []
        wodDetails.push({
            title: workout.name
        })

        if (_.get(workout, ["otherAttributes", "minCaloriesBurnt"]) && _.get(workout, ["otherAttributes", "maxCaloriesBurnt"])) {
            wodDetails.push({
                title: "Cal",
                subTitle: `${workout.otherAttributes.minCaloriesBurnt}-${workout.otherAttributes.maxCaloriesBurnt} KCal`
            })
        }

        if (wod && wod.focus) {
            wodDetails.push({
                title: "Focus",
                subTitle: wod.focus
            })
        }

        if (_.get(wod, ["mainMovement", "title"])) {
            wodDetails.push({
                title: "Main Movement",
                subTitle: wod.mainMovement.title
            })
        }

        return wodDetails
    }

}
