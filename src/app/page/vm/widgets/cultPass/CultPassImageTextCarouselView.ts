import { injectable } from "inversify"
import * as _ from "lodash"
import { IBaseWidget, CultPassImageTextCarousel, UserContext, CultPassImageTextCarouselItem } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import GymfitUtil from "../../../../util/GymfitUtil"
import { GymfitCenter } from "@curefit/gymfit-common"

@injectable()
export class CultPassImageTextCarouselView extends CultPassImageTextCarousel {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {

        if (this.itemType === "GYM_HOLIDAY") {
            const gymfitCenters = await interfaces.gymfitService.getAllGymfitCentersVisitedByUserInLastXDays(userContext.userProfile.userId, {x: 14})
            const centersMap: { [id: string]: GymfitCenter} = {}
            gymfitCenters.forEach((center: GymfitCenter) => centersMap[center.id] = center)
            const holidaysList: CultPassImageTextCarouselItem[] = gymfitCenters.flatMap(center => center.effectiveHolidays).sort((a, b) => a.startTime < b.startTime ? -1 : 1).map(holiday => {
                return ({
                    title: GymfitUtil.getGymHolidayMessage(userContext, centersMap[holiday.centerId], holiday),
                    imageUrl: "/image/gymfit/info.png",
                    imageWidth: 40,
                    imageAspectRatio: 1,
                })
            })

            if (!_.isEmpty(holidaysList)) {
                const centerHolidayWidget = new CultPassImageTextCarousel()
                centerHolidayWidget.items = holidaysList
                centerHolidayWidget.itemWidth = 335
                return centerHolidayWidget
            }
        }
        return null
    }
}