import { IBaseWidget, ProductListWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { GymfitAmenity } from "@curefit/gymfit-common"

export class ProductListWidgetView extends ProductListWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_AMENETIES") {
            return new GymfitAmenitiesView(this).buildView(interfaces, userContext, queryParams)
        }
        return this
    }
}


class GymfitAmenitiesView extends ProductListWidgetView {
    constructor(data: ProductListWidget) {
        super()
        this.alignment = "HORIZONTAL"
        this.data = []
        this.header = data.header
        this.footer = data.footer
        this.showDivider = false
        this.layoutProps = data.layoutProps
        this.layoutType = "IMAGE"
        this.productType = data.productType
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const cityId = userContext.sessionInfo.sessionData.cityId
        const amentities: GymfitAmenity[] = await interfaces.gymfitService.getAllAmenities(cityId)
        this.data = amentities.map(amenity => {
            return {
                title: amenity.name,
                subTitle: undefined,
                icon: amenity.imageUrls["clpImageUrl"],
                gradientColors: "",
                number: undefined,
                knowMore: {title: "", actionType: "NAVIGATION", url: "curefit://allgyms?amenities=" + amenity.name}
            }
        })
        return this
    }
}
