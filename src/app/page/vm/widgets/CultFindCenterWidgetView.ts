import { CultFindCenterWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { CenterLocation, NearByCentersInfo } from "../../../cult/CultBusiness"
import * as _ from "lodash"
import { FindACenterLocation } from "@curefit/apps-common"
import { Constants } from "@curefit/base-utils"
import { CdnUtil } from "@curefit/util-common"
import LocationUtil from "../../../util/LocationUtil"

export const MAX_DISTANCE_CENTER_ALLOWED_IN_METRES = 100 * 1000

const centerMarkerIconUrlMedium = CdnUtil.getCdnUrl("curefit-content/image/icons/cult/location-pin-1.png")
const userLocationIconUrlLarge = CdnUtil.getCdnUrl("curefit-content/image/icons/cult/user-location%403x.png")
const STATIC_MAP_HEIGHT = 300

export class CultFindCenterWidgetView extends CultFindCenterWidget {
    centerList: CenterLocation[]
    lat: number
    lon: number
    staticImageUrl: string
    maxCenterDetailsToShow: number = 10
    showStaticMap: boolean
    staticMapHeight: number
    nearByCenterIndex: number

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const {userProfile, sessionInfo} = userContext
        const cityId = userProfile.cityId
        const userId = userProfile.userId
        const cultCityId = await interfaces.cultBusiness.getCultCityId(userContext, cityId, userId)

        const lat = !_.isNil(queryParams.centerSearchLat) ? parseFloat(queryParams.centerSearchLat) : (sessionInfo.lat ? sessionInfo.lat : userProfile.city.representativeLatLong.lat)
        const lon = !_.isNil(queryParams.centerSearchLon) ? parseFloat(queryParams.centerSearchLon) : (sessionInfo.lon ? sessionInfo.lon : userProfile.city.representativeLatLong.long)
        this.lat = lat
        this.lon = lon
        const nearbyCenterInfo: NearByCentersInfo = await interfaces.cultBusiness.getNearbyCenters(userId, cultCityId, lat, lon, this.productType, userContext, true)
        this.centerList = nearbyCenterInfo.centers

        const isCenterAwayFromCity = nearbyCenterInfo.isCenterAwayFromCity
        const nearByCenterIndex = nearbyCenterInfo.nearByCenterIndex
        if (!isCenterAwayFromCity) {
            this.errorMessage = undefined
        }

        if (queryParams["resolveLocationName"] && queryParams["resolveLocationName"] === "true") {
            const place = await LocationUtil.getLocationData(interfaces.locationService, lat, lon)
            if (place && !_.isEmpty(place.name)) {
                this.searchHeader.title = place.name
            }
        }
        this.staticMapHeight = userContext.sessionInfo.appVersion >= 8.51 ? STATIC_MAP_HEIGHT : 365
        this.staticImageUrl = interfaces.locationService.signStaticMapUrl(this.getStaticImageUrl(this))
        this.nearByCenterIndex = nearByCenterIndex
        this.showStaticMap = true
        return this
    }

    private getStaticImageUrl(cultFindCenterWidgetView: CultFindCenterWidgetView): string {
        const scale = 2
        const mapWidth = 345
        const mapHeight = cultFindCenterWidgetView.staticMapHeight
        const centerList: FindACenterLocation[] = []
        cultFindCenterWidgetView.centerList.slice(0, cultFindCenterWidgetView.maxCenterDetailsToShow).forEach(centers => {
            centerList.push({lat: centers.lat, lon: centers.lon})
        })
        centerList.push({lat: cultFindCenterWidgetView.lat, lon: cultFindCenterWidgetView.lon})
        const {north, south, east, west} = this.getLimits(centerList)
        const verticalPadding = cultFindCenterWidgetView.errorMessage ? 150 : 90
        const zoom = this.getBoundsZoomLevel(
            { ne: { lat: north, lon: east }, sw: { lat: south, lon: west } },
            { height: mapHeight - 2 * verticalPadding, width: mapWidth }
        )
        const latitude = cultFindCenterWidgetView.lat
        const longitude = cultFindCenterWidgetView.lon
        const markers = this.createMarkersFromCenterList(cultFindCenterWidgetView.centerList.slice(0, cultFindCenterWidgetView.maxCenterDetailsToShow))
        const centerMarkers = `anchor:bottom|scale:2|icon:${centerMarkerIconUrlMedium}|${markers}`
        const userMarker = `anchor:bottom|scale:4|icon:${userLocationIconUrlLarge}|${latitude},${longitude}`
        const style = convertMapStyleToStatic(
            CUSTOM_MAP_CULT_FIND_CENTER_STYLE_COLORED
        )
        return `https://maps.googleapis.com/maps/api/staticmap?size=${mapWidth}x${mapHeight}&markers=${centerMarkers}&markers=${userMarker}&scale=${scale}&zoom=${zoom}&style=${style}&key=${Constants.getGoogleApiKey()}`
    }

    private getLimits(centerList: Array<FindACenterLocation>): { north: number; south: number; east: number; west: number } {
        let north = -Infinity
        let south = Infinity
        let east = -Infinity
        let west = Infinity
        centerList.forEach(item => {
            north = Math.max(north, item.lat)
            south = Math.min(south, item.lat)
            east = Math.max(east, item.lon)
            west = Math.min(west, item.lon)
        })
        return {
            north,
            south,
            east,
            west,
        }
    }

    private getBoundsZoomLevel(bounds: { ne: FindACenterLocation; sw: FindACenterLocation}, mapDim: { height: number; width: number }): number {
        const WORLD_DIM = { height: 256, width: 256 }
        const ZOOM_MAX = 21

        function latRad(lat: number): number {
            const sin = Math.sin((lat * Math.PI) / 180)
            const radX2 = Math.log((1 + sin) / (1 - sin)) / 2
            return Math.max(Math.min(radX2, Math.PI), -Math.PI) / 2
        }

        function zoom(mapPx: number, worldPx: number, fraction: number): number {
            return Math.floor(Math.log(mapPx / worldPx / fraction) / Math.LN2)
        }

        const { ne, sw } = bounds
        const latFraction = (latRad(ne.lat) - latRad(sw.lat)) / Math.PI

        const lonDiff = ne.lon - sw.lon
        const lonFraction = (lonDiff < 0 ? lonDiff + 360 : lonDiff) / 360

        const latZoom = zoom(mapDim.height, WORLD_DIM.height, latFraction)
        const lonZoom = zoom(mapDim.width, WORLD_DIM.width, lonFraction)

        return Math.floor(Math.min(latZoom, lonZoom, ZOOM_MAX))
    }

    private createMarkersFromCenterList(centerList: Array<CenterLocation>): string {
        let markersString = ""
        for (let i = 0; i < centerList.length; i++) {
            markersString += `${i > 0 ? "|" : ""}${centerList[i].lat},${centerList[i].lon}`
        }
        return markersString
    }

}

export const convertMapStyleToStatic = (styles: any): string => {
    const result: any = []
    styles.forEach(function(v: any, i: any, a: any) {
        let style = ""
        if (v.stylers) {
            // only if there is a styler object
            if (v.stylers.length > 0) {
                // Needs to have a style rule to be valid.
                style += `${
                    v.hasOwnProperty("featureType")
                        ? `feature:${v.featureType}`
                        : "feature:all"
                    }|`
                style += `${
                    v.hasOwnProperty("elementType")
                        ? `element:${v.elementType}`
                        : "element:all"
                    }|`
                v.stylers.forEach(function(val: any, i: any, a: any) {
                    const propertyname = Object.keys(val)[0]
                    const propertyval = val[propertyname].toString().replace("#", "0x")
                    style += `${propertyname}:${propertyval}|`
                })
            }
        }
        result.push(`style=${encodeURIComponent(style)}`)
    })
    return result.join("&")
}

export const CUSTOM_MAP_CULT_FIND_CENTER_STYLE_COLORED = [
    {
        featureType: "administrative.land_parcel",
        elementType: "labels",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "administrative.locality",
        stylers: [
            {
                visibility: "simplified",
            },
        ],
    },
    {
        featureType: "poi",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.attraction",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.business",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.medical",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.medical",
        elementType: "geometry",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.park",
        elementType: "labels",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.place_of_worship",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.place_of_worship",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.school",
        elementType: "labels",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.school",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.sports_complex",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "poi.sports_complex",
        elementType: "labels.text",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "road",
        elementType: "labels",
        stylers: [
            {
                visibility: "simplified",
            },
        ],
    },
    {
        featureType: "road",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "road.arterial",
        elementType: "labels",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "road.local",
        stylers: [
            {
                visibility: "simplified",
            },
        ],
    },
    {
        featureType: "transit",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "transit",
        elementType: "labels.icon",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
    {
        featureType: "water",
        elementType: "labels",
        stylers: [
            {
                visibility: "off",
            },
        ],
    },
]
