import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { UrlPathBuilder } from "@curefit/product-common"

export class CollectionsListItemWidget extends BaseWidget {
    imageUrl?: string
    title ?: string
    duration: string
    instructor?: string
    videoId?: string
    isLocked?: boolean
    action?: Action
    shareAction?: Action

    constructor(title?: string, duration?: string, instructor?: string, imageUrl?: string, videoId?: string, isLocked?: boolean, action?: Action, shareMessage?: string) {
        super("COLLECTIONS_LIST_ITEM_WIDGET")
        this.title = title
        this.duration = duration
        this.instructor = instructor
        this.imageUrl = imageUrl
        this.videoId = videoId
        this.isLocked = isLocked
        this.action = action
        this.shareAction = shareMessage ? {
            actionType: "SHARE_ACTION",
            title: "Share",
            meta: {
                // videoUrl: UrlPathBuilder.getVideoAbsolutePath(recipe.contentId, recipe.contentFormat),
                isShareSingle: true,
                shareOptions: {
                    message: shareMessage
                }
            }
        } : undefined
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<CollectionsListItemWidget> {
        return this
    }
}