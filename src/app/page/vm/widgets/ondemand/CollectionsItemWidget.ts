import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"

export class CollectionsItemWidget extends BaseWidget {
    title?: string
    sessions?: string
    imageUrl?: string
    action?: Action
    collectionId?: string

    constructor(title?: string, sessions?: string, imageUrl?: string, collectionId?: string, action?: Action) {
        super("COLLECTIONS_ITEM_WIDGET")
        this.title = title
        this.sessions = sessions
        this.imageUrl = imageUrl
        this.collectionId = collectionId
        this.action = action
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }, shareData?: any): Promise<CollectionsItemWidget> {
        return this
    }
}