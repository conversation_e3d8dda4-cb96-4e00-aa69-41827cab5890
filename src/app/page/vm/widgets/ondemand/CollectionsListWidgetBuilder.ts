import { ListWidgetV3 } from "@curefit/vm-models"
import { IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { OnDemandVideoCategory } from "@curefit/diy-common"
import { Action } from "@curefit/apps-common"
import { CollectionsItemWidget } from "./CollectionsItemWidget"
import AppUtil from "../../../../util/AppUtil"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"

export class CollectionsListWidgetBuilder extends ListWidgetV3 {
    category: OnDemandVideoCategory
    start: number
    count: number

    constructor() {
        super()
        this.widgets = []
    }
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filter: string]: string }): Promise<IBaseWidget> {

        const pageFrom = _.get(queryParams, "selectedTab") ? _.get(queryParams, "selectedTab") : _.get(queryParams, "pageId", undefined)
        const widgetPromise: Promise<IBaseWidget>[] = []
        // hack for cultpass LIVE Past classes as the on demand category doesn't support it
        if (pageFrom === "CultAtHome" || pageFrom === "live") {
            const countryId = AppUtil.getCountryId(userContext)
            this.subTitle = "Live Workout Library"
            this.start = 0
            this.count = 10
            this.numRows = undefined
            this.isHorizontal = false
            this.numCols = 2
            const liveSeries = await interfaces.diyService.getDIYPacksForLiveSeries("DIY_FITNESS", countryId)
            liveSeries.map((series, index) => {
                const seoParams: SeoUrlParams = {
                  productName: series.title
                }
                const imageUrl = series.imageDetails.clpImage
                const packId = series.productId
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(series, userContext.sessionInfo.userAgent)
                }
                const title = series.title
                const sessionIds = series.sessionIds
                const sessionText = "Class"
                const sessions = !_.isEmpty(series.sessionIds) ? (`${sessionIds.length} ${sessionText}` + (sessionIds.length === 1 ? `` : `es`)) : null
                widgetPromise.push(new CollectionsItemWidget(title, sessions, imageUrl, packId, action).buildView(interfaces, userContext, queryParams))
            })
            if (_.isEmpty(widgetPromise)) {
                return undefined
            }
            let widgets = await Promise.all(widgetPromise)
            widgets = widgets.filter(v => !_.isNil(v) && !_.isNull(v))
            this.widgets.push(...widgets)
            return this
        }

        let bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly", false)) === "true"
        let vegOnlyFilter = String(_.get(queryParams, "filters.vegOnly", false)) === "true"
        const isWeb = AppUtil.isWeb(userContext)

        interfaces.logger.info(`bookmarkedonly -> eatlivewidget: ${bookmarkedOnly}`)
        if (isWeb) {
            bookmarkedOnly = String(_.get(queryParams, "bookmarkedOnly", false)) === "true"
            vegOnlyFilter = String(_.get(queryParams, "vegOnly", false)) === "true"
        }
        if (bookmarkedOnly) {
            // not showing this widget in case bookmark is enabled
            return undefined
        }

        const userId = userContext.userProfile.userId
        let category: OnDemandVideoCategory = _.get(queryParams, "category", undefined) as OnDemandVideoCategory
        let start = 0
        let count = 10
        if (!_.isNil(this.category)) {
            interfaces.logger.info(`Overridden category with ${this.category}`)
            category = this.category
        }
        if (_.isNil(category)) {
            interfaces.logger.error("Category was not passed")
            return undefined
        }
        if (!_.isNil(this.start) && this.start > 0) {
            start = this.start
        }
        if (!_.isNil(this.count) && this.count > 0) {
            count = this.count
        }
        if (_.isNil(this.subTitle)) {
            this.subTitle = category === "EAT" ? "Recipe Past Collections" : "Pop.fit archives"
        }
        if (AppUtil.isShowTruncatedListSupported(userContext)) {
            if (category === "EAT") {
                this.numRows = 2
                this.showLessText = "LESS"
                this.showMoreText = "MORE"
                this.isHorizontal = false
                this.numCols = 2
            } else {
                // explicitely made indefined as it comes as 2 by default
                this.numRows = undefined
                this.isHorizontal = false
                this.numCols = 2
            }
        }
        const result = await interfaces.diyService.getOnDemandVideoCollectionsForCategory(userId, category, "LIVE", start, count)
        if (!_.isEmpty((result))) {
            _.map(result, onDemandItem => {
                const title = onDemandItem.title
                const sessionText = category === "EAT" ? "Episode" : "Session"
                let totalVideos = _.get(onDemandItem, "totalVideos", 0)
                if (vegOnlyFilter) {
                    totalVideos = _.get(onDemandItem, "vegItemCount", undefined) || totalVideos
                }
                const sessions = totalVideos ? (`${totalVideos} ${sessionText}` + (totalVideos === 1 ? `` : `s`)) : null
                const imageUrl = onDemandItem.imageUrlMap["MAGAZINE"]
                const seoParams: SeoUrlParams = {
                    productName: onDemandItem.title
                }
                const userAgent = userContext.sessionInfo.userAgent
                let collectionPageUrl
                if (category === "EAT") {
                    collectionPageUrl = ActionUtil.recipeShowsPage(onDemandItem.onDemandVideoCollectionId, seoParams, category, pageFrom, userAgent)
                } else {
                    // considered as a talk show
                    collectionPageUrl = ActionUtil.talkShowsPage(onDemandItem.onDemandVideoCollectionId, seoParams, category, pageFrom, userAgent)
                }
                const action: Action = {
                    actionType: "NAVIGATION",
                    url: collectionPageUrl,
                }
                widgetPromise.push(new CollectionsItemWidget(title, sessions, imageUrl, onDemandItem.onDemandVideoCollectionId, action).buildView(interfaces, userContext, queryParams))
            })
        } else {
            interfaces.logger.error("No data retrieved from backend")
            return undefined
        }
        let widgets = await Promise.all(widgetPromise)
        widgets = widgets.filter(v => !_.isNil(v) && !_.isNull(v))
        this.widgets.push(...widgets)
        return this
    }
}
