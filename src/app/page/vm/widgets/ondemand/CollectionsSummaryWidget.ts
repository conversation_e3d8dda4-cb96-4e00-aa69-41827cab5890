import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { IBreadCrumb } from "../../../ondemand/OnDemandCommon"

export class CollectionsSummaryWidget extends BaseWidget {
    title: string
    subTitle: string
    imageUrl: string
    breadCrumbs: IBreadCrumb[]
    constructor() {
        super("COLLECTIONS_SUMMARY_WIDGET")
    }

    async buildView(): Promise<IBaseWidget> {
        return this
    }
}