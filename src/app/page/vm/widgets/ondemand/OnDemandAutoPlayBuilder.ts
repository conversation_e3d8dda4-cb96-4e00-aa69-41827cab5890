import { injectable } from "inversify"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import {
    IOnDemandAutoPlayParams,
    IOnDemandAutoPlayResponse,
    IOnDemandAutoPlayVideo
} from "../../../ondemand/OnDemandCommon"
import * as _ from "lodash"
import { IEatOnDemandVideoResponse, IPopLiveOnDemandVideoResponse } from "@curefit/diy-common"
import AppUtil from "../../../../util/AppUtil"

@injectable()
export class OnDemandAutoPlayBuilder {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, params: IOnDemandAutoPlayParams): Promise<IOnDemandAutoPlayResponse> {
        const presentVideoId = params.presentVideoId
        const onDemandCategory = params.category
        const userId = userContext.userProfile.userId
        const isDevicePhone = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent)
        const result = await interfaces.diyService.getAutoplaySuggestions(presentVideoId, userId, AppUtil.getTenantFromUserContext(userContext), onDemandCategory)
        const videos: IOnDemandAutoPlayVideo[] = []
        if (!_.isNil(result) && !_.isNil(result.videos)) {
            _.map(result.videos, videoItem => {
                let title: string
                let contentId: string
                let imageUrl: string
                let absoluteVideoUrl: string
                let videoUrl: string
                let video: IOnDemandAutoPlayVideo
                switch (onDemandCategory) {
                    case "EAT":
                        const eatLiveVideo = videoItem as IEatOnDemandVideoResponse
                        title = eatLiveVideo.title
                        contentId = eatLiveVideo.onDemandVideoId
                        const eatBackendVideoUrl = isDevicePhone ? eatLiveVideo.media["APP_VIDEO_URL"] : eatLiveVideo.media["WEB_VIDEO_URL"]
                        imageUrl = isDevicePhone ? eatLiveVideo.media["APP_VIDEO_THUMBNAIL_URL"] : eatLiveVideo.media["WEB_VIDEO_THUMBNAIL_URL"]
                        absoluteVideoUrl = `https://cdn-media.cure.fit/${eatBackendVideoUrl}`
                        videoUrl = `curefit-content/${eatBackendVideoUrl}`
                        video = {
                            title,
                            contentId,
                            activityId: contentId,
                            imageUrl,
                            videoUrl,
                            absoluteVideoUrl,
                            onDemandCategory
                        }
                        break
                    case "POP_LIVE":
                        const popLiveVideo = videoItem as IPopLiveOnDemandVideoResponse
                        title = popLiveVideo.title
                        contentId = popLiveVideo.popLiveOnDemandVideoId
                        imageUrl = isDevicePhone ? popLiveVideo.media["APP_VIDEO_THUMBNAIL_URL"] : popLiveVideo.media["WEB_VIDEO_THUMBNAIL_URL"]
                        const popBackendVideoUrl = isDevicePhone ? popLiveVideo.media["APP_VIDEO_URL"] : popLiveVideo.media["WEB_VIDEO_URL"]
                        absoluteVideoUrl = `https://cdn-media.cure.fit/${popBackendVideoUrl}`
                        videoUrl = `curefit-content/${popBackendVideoUrl}`
                        video = {
                            title,
                            contentId,
                            activityId: contentId,
                            imageUrl,
                            videoUrl,
                            absoluteVideoUrl,
                            onDemandCategory
                        }
                        break
                    default: break
                }
                videos.push(video)
            })
        } else {
            interfaces.logger.error("Error in data retrieved from backend")
        }
        return { videos }
    }
}