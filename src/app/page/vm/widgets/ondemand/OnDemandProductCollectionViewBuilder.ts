import { injectable } from "inversify"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { getShareMesssage, IBreadCrumb, IOnDemandCollectionParams, IOnDemandPageResponse } from "../../../ondemand/OnDemandCommon"
import * as _ from "lodash"
import {
    IConvertedOnDemandVideoResponse,
    IEatOnDemandVideoResponse,
    IOnDemandVideoCollectionResponse,
    IPopLiveOnDemandVideoResponse, OnDemandVideoCategory
} from "@curefit/diy-common"
import { IBaseWidget, WebGridListWidget } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { CollectionsSummaryWidget } from "./CollectionsSummaryWidget"
import { CollectionsListItemWidget } from "./CollectionsListItemWidget"
import LiveUtil from "../../../../util/LiveUtil"
import AppUtil from "../../../../util/AppUtil"
import { LivePackUtil } from "../../../../util/LivePackUtil"
import { getBreadCrumbsData } from "../../../../recipe/RecipeCommon"

@injectable()
export class OnDemandProductCollectionViewBuilder {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, params: IOnDemandCollectionParams, isInternationalUser: boolean): Promise<IOnDemandPageResponse> {
        const collectionId = params.collectionId
        const collectionType = params.collectionType
        const pageNumber = params.pageNumber
        const pageSize = params.pageSize
        const vegOnly = params.vegOnly
        const start = pageNumber * pageSize
        let hasNextPage = false
        const userId = userContext.userProfile.userId
        const tenant = AppUtil.getTenantFromUserContext(userContext)

        const collectionsPromise = interfaces.diyService.getOnDemandVideoCollectionByIdForCategory(vegOnly, userId, tenant, collectionType, collectionId, start, pageSize + 1)
        const collections = await collectionsPromise

        if (collections && collections.videos && collections.videos.length > pageSize) {
            collections.videos = collections.videos.splice(0, pageSize)
            hasNextPage = true
        }

        let collectionBannerPromise
        if (pageNumber === 0) {
            collectionBannerPromise = this.getCollectionBannerPromise(collections, userContext, vegOnly, isInternationalUser)
        }
        const collectionCardsPromise = this.getCollectionProductCards(userId, collections, interfaces, userContext)

        let body: IBaseWidget[] = []
        const collectionBanner = await collectionBannerPromise
        const collectionCards = await collectionCardsPromise
        if (_.isEmpty(collectionCards)) {
            hasNextPage = false
            body = [collectionBanner, { message: "Oops! No items to show.", widgetType: "NO_DATA_MESSAGE_WIDGET" } as unknown as IBaseWidget]
        } else {
            if (AppUtil.isWeb(userContext)) {
                const gridWidget = new WebGridListWidget(undefined, collectionCards)
                body = [collectionBanner, gridWidget]
            } else {
                body = [collectionBanner, ...collectionCards]
            }
        }

        body = body.filter(v => !_.isNil(v) && !_.isNull(v))

        return {
            body,
            hasNextPage,
            // not showing recipe header component for pop live
            filters: collectionType === "POP_LIVE" ? undefined : {
                widgetType: "RECIPE_FILTER_WIDGET_V2",
                filters: [
                    {
                        type: "VEG",
                        meta: {
                            isEnabled: vegOnly,
                            title: "VEG"
                        }
                    }]
            }
        }
    }

    async getCollectionBannerPromise(collection: IOnDemandVideoCollectionResponse, userContext: UserContext, vegOnly: boolean = false, isInternationalUser = false): Promise<IBaseWidget> {
        const widget = new CollectionsSummaryWidget()
        const sessionText = collection.onDemandVideoCategory === "EAT" ? "Episode" : "Session"
        let totalVideos = collection.totalVideos
        if (vegOnly) {
            totalVideos = _.get(collection, "vegItemCount", undefined)
        }
        widget.title = collection.title
        widget.subTitle = totalVideos ? (`${totalVideos} ${sessionText}` + (totalVideos === 1 ? `` : `s`)) : null
        widget.imageUrl = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent) ? collection.imageUrlMap["HERO"] : collection.imageUrlMap["HERO_WEB"]
        const isWeb = AppUtil.isWeb(userContext)
        if ( isWeb ) {
            widget.breadCrumbs = getBreadCrumbsData(collection.title, collection.onDemandVideoCategory, isInternationalUser)
        }

        return widget.buildView()
    }

    async getCollectionProductCards(userId: string, collection: IOnDemandVideoCollectionResponse, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget[]> {
        if (_.isNil(collection.videos)) {
            interfaces.rollbarService.sendError(new Error("On Demand Video Collection Broken"), {
                extra: {
                    collectionId: collection.onDemandVideoCollectionId, title: collection.title, category: collection.onDemandVideoCategory
                }
            })
            interfaces.logger.error(`On Demand Video Collection Broken. ID: ${collection.onDemandVideoCollectionId}`)
            return []
        }

        const widgetPromises: Promise<IBaseWidget>[] = []

        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        // todo: change interface of video
        for (let i = 0; i < collection.videos.length; i = i + 1) {
            let video = collection.videos[i]
            if (!_.isNil(video) && !_.isNull(video) && collection.status === "LIVE") {
                let title: string
                let instructor: string
                let duration: string
                let imageUrl: string
                let videoId: string
                let diyAction: Action
                let isLocked = false
                if (collection.onDemandVideoCategory === "EAT") {
                    video = video as IEatOnDemandVideoResponse
                    title = video.title
                    isLocked = video.isLocked
                    instructor = _.get(video, "instructorInfo.name", undefined) ? `by ${video.instructorInfo.name}` : undefined
                    videoId = video.onDemandVideoId
                    const durationInSecs = _.get(video, "meta.durationInSecs", undefined)
                    if (durationInSecs) {
                        if (durationInSecs < 60) {
                            duration = `${durationInSecs} SECS`
                        } else {
                            const durationMinutes = Math.round(durationInSecs / 60)
                            duration = durationMinutes === 1 ? `1 MIN` : `${durationMinutes} MINS`
                        }
                    }
                    imageUrl = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent) ? video.media["APP_VIDEO_THUMBNAIL_URL"] : video.media["WEB_VIDEO_THUMBNAIL_URL"]

                    const action = {
                        actionType: "NAVIGATION",
                        url: await LiveUtil.generateOnDemandContentVideoNavigationUrl(
                            ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent) ? video.media["APP_VIDEO_URL"] : video.media["WEB_VIDEO_URL"],
                            videoId, collection.onDemandVideoCategory,
                            userContext, interfaces
                        ),
                        meta: {
                            title: title,
                            checkDownloadStatus: true
                        }
                    }
                    diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, action, isUserEligibleForTrial, "EAT", "on_demand_eat_session", bucketId, blockInternationalUser)
                } else {
                    video = video as IPopLiveOnDemandVideoResponse
                    title = video.title
                    isLocked = video.isLocked
                    instructor = _.get(video, "meta.instructorInfo.name", undefined) ? `by ${video.meta.instructorInfo.name}` : undefined
                    videoId = video.popLiveOnDemandVideoId
                    const durationInSecs = _.get(video, "meta.durationInSec", undefined)
                    if (durationInSecs) {
                        if (durationInSecs < 60) {
                            duration = `${durationInSecs} SECS`
                        } else {
                            const durationMinutes = Math.round(durationInSecs / 60)
                            duration = durationMinutes === 1 ? `1 MIN` : `${durationMinutes} MINS`
                        }
                    }
                    imageUrl = ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent) ? video.media["APP_VIDEO_THUMBNAIL_URL"] : video.media["WEB_VIDEO_THUMBNAIL_URL"]

                    const action = {
                        actionType: "NAVIGATION",
                        url: await LiveUtil.generateOnDemandContentVideoNavigationUrl(
                            ["APP", "MBROWSER"].includes(userContext.sessionInfo.userAgent) ? video.media["APP_VIDEO_URL"] : video.media["WEB_VIDEO_URL"],
                            videoId, collection.onDemandVideoCategory,
                            userContext, interfaces
                        ),
                        meta: {
                            title: title,
                            checkDownloadStatus: true
                        }
                    }
                    diyAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked, action, isUserEligibleForTrial, "HOBBY", "on_demand_pop_session", bucketId, blockInternationalUser)
                }
                const isDeeplinkShareSupported = AppUtil.isDeeplinkShareForOnDemandCollectionVideosSupported(userContext)
                let shareMessage: string
                if (isDeeplinkShareSupported) {
                    shareMessage = await this.getDeeplinkShareMessage(interfaces, userContext, collection, video, videoId)
                }
                widgetPromises.push(new CollectionsListItemWidget(title, duration, instructor, imageUrl, videoId, false, diyAction, shareMessage).buildView(undefined, undefined, {}) as Promise<IBaseWidget>)
            }
        }

        return Promise.all(widgetPromises)
    }

    async getDeeplinkShareMessage(interfaces: CFServiceInterfaces, userContext: UserContext, collection: IOnDemandVideoCollectionResponse, video: IConvertedOnDemandVideoResponse, videoId: string): Promise<string> {
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const deeplink = await interfaces.deeplinkService.getOnDemandCollectionShareLink(tenant, collection.onDemandVideoCategory, collection.onDemandVideoCollectionId, collection.title, video.title, video.media.APP_VIDEO_THUMBNAIL_URL, videoId)
        const shareMessage = deeplink ? getShareMesssage(deeplink, collection.onDemandVideoCategory) : undefined

        return shareMessage
    }
}
