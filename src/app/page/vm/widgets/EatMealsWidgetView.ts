import * as _ from "lodash"
import { Action, MealAction } from "../../../common/views/WidgetView"
import { MAX_CART_SIZE } from "../../../util/MealUtil"
import { EatMealsWidget } from "@curefit/vm-models"
import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    SessionInfo,
    UserContext
} from "@curefit/vm-models"
import { SlotUtil } from "@curefit/eat-util"
import { OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID, getCategoryPriority } from "../../../util/EatUtil"
import { EatMealCardType, ListingBrandIdType } from "@curefit/eat-common"
import { EatCLPTabs } from "@curefit/eat-common"
import { DeliveryArea } from "@curefit/eat-common"
import { DelayMode, DelayModeReason } from "@curefit/eat-common"
import { FoodInventory, MealSlot, MenuType } from "@curefit/eat-common"
import { ProductMealSlotMenu } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { SubscriptionType } from "@curefit/base-common"
import { NutritionTags, NutritionTag } from "@curefit/food-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { OfferV2, OfferV2Lite } from "@curefit/offer-common"
import { capitalizeFirstLetter, ILogger, Timezone } from "@curefit/util-common"
import { eternalPromise } from "@curefit/util-common"
import { MealItem } from "../../PageWidgets"
import { PreferredLocation } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { MealCardWidget } from "./MealsCardWidget"
import { CFUserProfile } from "../CFUserProfile"
import { TimeUtil } from "@curefit/util-common"
import { MealSlotSelectorWidget } from "./MealSlotSelectorWidget"
import { TitleWidget } from "@curefit/vm-models"
import { EatFilterWidget, Filter } from "@curefit/vm-models"
import { MealUtil } from "@curefit/base-utils"
import { ActionUtil } from "@curefit/base-utils"
import { EatCategoryWidget } from "@curefit/vm-models"
import { CafeSlot, DeliverySlot, FoodCategory } from "@curefit/eat-common"
import { EatCategoryNavWidget } from "@curefit/vm-models"
import { EatTitleWidget } from "@curefit/vm-models"
import { EatFilterContainerWidgetView } from "@curefit/vm-models"
import { SoldOutTitleWidget } from "@curefit/vm-models"
import { GridWidget } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"
import AppUtil from "../../../util/AppUtil"
import { FitclubBusiness } from "../../../fitclub/FitclubBusiness"

const TOP_MEALSLOT_CATEGORY_MAP: { [mealSlot: string]: string[] } = {
    ["BREAKFAST"]: ["POWER_BREAKFASTS", "HEALTH_BITES"],
    ["LUNCH"]: ["INDIAN_MEALS", "HEALTH_BITES"],
    ["SNACKS"]: ["SNACKS", "HEALTH_BITES"],
    ["DINNER"]: ["INDIAN_MEALS", "HEALTH_BITES"],
    ["ALL"]: ["INDIAN_MEALS", "HEALTH_BITES"]
}

export class EatMealsWidgetView extends EatMealsWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<IBaseWidget | IBaseWidget[]> {
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const deliveryArea: DeliveryArea = await userProfile.deliveryAreaPromise
        const deliveryAreaTz = await interfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId)
        const isOnline = deliveryArea.channel === "ONLINE"
        const mealSlots = await userProfile.availableMealSlotsPromise
        let day = TimeUtil.todaysDate(deliveryAreaTz)
        let tabSlot: MenuType
        const isMenuCategorizationSupported = MealUtil.isMenuCategorizationSupported(userContext)
        const currentMealSlot = SlotUtil.getCurrentMealSlotFrom(mealSlots, deliveryArea.channel, deliveryAreaTz)
        const isCultCafe: boolean = deliveryArea.kioskType === "CAFE"
        let orderTimes
        if (isCultCafe) {
            orderTimes = await interfaces.kiosksDemandService.getKioskSlotTimes(deliveryArea.kioskIds[0])
        }
        const { isServiceable, isAfterLastSlot } = SlotUtil.isServiceable(mealSlots, deliveryArea, deliveryAreaTz, orderTimes)
        const isWeb: boolean = userContext.sessionInfo.userAgent !== "APP"
        // don't need this anymore
        if (this.eatClpTab === "EAT_NOW" && !isServiceable && !isCultCafe) {
            return undefined
        }
        const nextMealSlot: MealSlot = queryParams.mealSlot ? <MealSlot>queryParams.mealSlot : ((deliveryArea.kioskType === "CAFE") ? <MealSlot>"ALL" : SlotUtil.getNextAvailableMealSlot(currentMealSlot, mealSlots, deliveryAreaTz).mealSlot)
        const preferredLocation = await userProfile.preferredLocationPromise
        const isFitclubMember = AppUtil.isFitClubApplicable(
            userContext,
            await userProfile.fitclubMembershipPromise
        )
        let isFitClubSupported = await FitclubBusiness.isFitClubSupported(userContext) || isFitclubMember
        isFitClubSupported = isFitClubSupported && !isCultCafe
        sharedData["FITCLUB_OFFERS"] = []
        const cartOffersResult = await userContext.userProfile.eatCartOffersPromise
        const fitCashOffer = _.maxBy(_.filter(cartOffersResult, (offer: OfferV2) => {
            return offer.addons[0] && offer.addons[0].addonType === "FITCASH"
        }), "priority")
        let productMenus: ProductMealSlotMenu[] = []
        let inventoryResult: FoodInventory
        let recommendedMealMapPromise
        let selectedDeliverySlot: DeliverySlot = undefined
        if (queryParams.deliverySlot) {
            if (isCultCafe) {
                selectedDeliverySlot = SlotUtil.getDeliverySlotForCafe(<CafeSlot>queryParams.deliverySlot)
            } else {
                selectedDeliverySlot = SlotUtil.getSlotById(queryParams.deliverySlot)
            }
        }
        if (this.eatClpTab === "EAT_NOW") {
            recommendedMealMapPromise = this.getRecommendedMealMap(userProfile.userId, day, interfaces, tabSlot)
            const menuAvailability = await interfaces.capacityService.getCurrentMenu(preferredLocation, deliveryAreaTz, true, selectedDeliverySlot)
            tabSlot = menuAvailability.menuType
            productMenus = menuAvailability.currentMenu
            inventoryResult = menuAvailability.inventoryResult
        } else {
            if (queryParams.date) {
                day = queryParams.date
            } else if (isAfterLastSlot || currentMealSlot === mealSlots[mealSlots.length - 1]) {
                day = MealUtil.getNextAvailableMenuDate(deliveryArea.channel, deliveryAreaTz)
            }
            recommendedMealMapPromise = this.getRecommendedMealMap(userProfile.userId, day, interfaces, tabSlot)
            const menuAvailability = await interfaces.eatApiClientService.getMenuByMenuType(userProfile.userId, nextMealSlot, day, preferredLocation, true, isCultCafe ? selectedDeliverySlot : undefined, deliveryAreaTz, "EAT_FIT" as ListingBrandIdType)
            productMenus = menuAvailability.menus
            day = menuAvailability.day
            inventoryResult = menuAvailability.inventoryResult
            tabSlot = nextMealSlot
        }
        sharedData[this.eatClpTab] = {
            menuType: tabSlot,
            date: day
        }
        const delayMode = this.getDelayMode(tabSlot, deliveryArea, deliveryAreaTz)
        const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")
        const recommendedMealMap = await recommendedMealMapPromise
        const eatSingleOffersResult = await userProfile.eatSingleOffersPromise
        const productIds = _.map(productMenus, menu => menu.productId)
        const productMap = await interfaces.catalogueService.getProductMap(productIds)

        const mealItemPromises = _.map(productMenus, async (menu) => {
            const product = productMap[menu.productId]
            const offerDetails = OfferUtil.getSingleOfferAndPrice(product, day, eatSingleOffersResult, tabSlot)
            const availability = OfferUtil.getAvailabilityV1(product, inventoryResult)
            const actions: (Action | MealAction)[] = await this.getMealActions(product, offerDetails, day, tabSlot, preferredLocation, sessionInfo, this.eatClpTab, stopOrders, availability, interfaces.logger, userContext, false, isCultCafe ? queryParams.deliverySlot : undefined)
            let image
            if (sessionInfo.userAgent === "DESKTOP") {
                image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "HERO", product.imageVersion)
            } else {
                image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", product.landscapeImageExists ? "LANDSCAPE" : "HERO", product.imageVersion)
            }
            let parentProduct
            if (!_.isNil(product.parentProductId)) {
                parentProduct = await interfaces.catalogueService.getProduct(product.parentProductId)
            }
            const nutritionInfo = EatUtil.computeNutritionalInfo(product, parentProduct, "EAT_FIT")

            const imageThumbnail = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            const mealItem: MealItem = {
                title: product.title,
                titleWithoutUnits: product.titleWithoutUnits,
                calories: Math.ceil(Number(nutritionInfo.Calories["Total Calories"])),
                price: offerDetails.price,
                date: day,
                image: image,
                imageThumbnail: imageThumbnail,
                isInventorySet: availability.total > 0,
                stock: Math.min(MAX_CART_SIZE, availability.left),
                actions: actions,
                offerIds: _.map(offerDetails.offers, (offer) => { return offer.offerId }),
                categoryId: product.categoryId,
                productId: product.productId,
                isVeg: product.attributes["isVeg"] === "TRUE",
                salesTag: this.salesTagMap[menu.salesTag],
                isAllDayItem: menu.allDay,
                shipmentWeight: product.shipmentWeight,
                foodCategoryId: product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID,
                nutritiontags: product.nutritionTags,
                displayUnitQty: EatUtil.getQuantityTitle(product),
                variantTitle: EatUtil.getVariantTitle(product, "EAT_FIT"),
                listingBrand: "EAT_FIT"
            }
            return mealItem
        })
        const mealItems: MealItem[] = await Promise.all(mealItemPromises)
        const mealItemsWithInventory = _.filter(mealItems, mealItem => { return mealItem.isInventorySet })
        const categoryIdArray = _.uniq(mealItemsWithInventory.map((mealItem) => {
            return mealItem.foodCategoryId
        }))
        const clpCategories: { [id: string]: FoodCategory } = interfaces.foodCategoryService.getCLPCategories(categoryIdArray, "EAT_FIT")
        const clpCategoryArr = _.uniqBy(_.map(categoryIdArray, (categoryId) => { return clpCategories[categoryId] }), (category) => { return category.categoryId })
        const channel = EatUtil.getDeliveryChannelFromLocation(preferredLocation)
        clpCategoryArr.sort((categoryA, categoryB) => {
            const categoryPriorityA = getCategoryPriority(categoryA, tabSlot, channel)
            const categoryPriorityB = getCategoryPriority(categoryB, tabSlot, channel)
            return categoryPriorityA > categoryPriorityB ? -1 : 1
        })
        sharedData[this.eatClpTab].clpCategoryArr = clpCategoryArr
        const sortedMealItems = EatUtil.sortMealItems(mealItemsWithInventory, recommendedMealMap, tabSlot, productMap, clpCategories, channel)
        const categoryWidgetPromises: Promise<IBaseWidget>[] = _.map(clpCategoryArr, (clpCategory) => {
            return new EatCategoryWidget(clpCategory.name, clpCategory.categoryId).buildView(interfaces, userContext, queryParams)
        })
        const cardViewType: EatMealCardType = "SMALL_CARD"
        const mealWidgetPromises = _.map(sortedMealItems, (mealItem) => {
            return new MealCardWidget(mealItem, tabSlot, clpCategories, cardViewType, fitCashOffer, isFitClubSupported && isFitclubMember).buildView(interfaces, userContext, queryParams)
        })
        const categoryNavWidget = await new EatCategoryNavWidget(clpCategoryArr, this.eatClpTab).buildView(interfaces, userContext, queryParams)
        const categoryWidgets = await Promise.all(categoryWidgetPromises)
        const categoryWidgetMap = _.mapKeys(categoryWidgets, (widget) => {
            const categoryWidget = <EatCategoryWidget>widget
            return categoryWidget.categoryId
        })
        const mealWidgets = await Promise.all(mealWidgetPromises)
        const filterListWidgets: IBaseWidget[] = []
        const finalWidets: IBaseWidget[] = []
        if (this.eatClpTab === "EAT_LATER") {
            let widget
            if (isCultCafe) {
                const title = momentTz.tz(day, deliveryAreaTz).calendar(null, {
                    sameDay: "[TODAY'S]",
                    nextDay: "[TOMORROW'S]",
                    nextWeek: "dddd['S]",
                    lastDay: "dddd['S]",
                    lastWeek: "dddd['S]",
                    sameElse: "Do['s]"
                }).toUpperCase() + " MENU"
                widget = await new EatTitleWidget(title).buildView(interfaces, userContext, queryParams)
            } else {
                widget = await new MealSlotSelectorWidget(currentMealSlot, nextMealSlot, day, mealSlots).buildView(interfaces, userContext, queryParams)
            }
            if (isMenuCategorizationSupported || isWeb) {
                filterListWidgets.push(widget)
            } else {
                finalWidets.push(widget)
            }
        } else {
            const slot = tabSlot === "ALL" ? "ALL DAY" : tabSlot.toUpperCase()
            let title: string = slot + " MENU"
            if (isMenuCategorizationSupported || isWeb) {
                if (isCultCafe) {
                    title = "TODAY'S MENU"
                }
                const titleWidget = await new EatTitleWidget(title).buildView(interfaces, userContext, queryParams)
                filterListWidgets.push(titleWidget)
            } else {
                const titleWidget = await new TitleWidget(slot + " MENU").buildView(interfaces, userContext, queryParams)
                finalWidets.push(titleWidget)
            }
        }
        const filterWidget = await new EatFilterWidget(this.getFilters(mealItems), this.eatClpTab).buildView(interfaces, userContext, queryParams)
        if (isMenuCategorizationSupported || isWeb) {
            filterListWidgets.push(filterWidget)
            if (!isWeb)
                filterListWidgets.push(categoryNavWidget)
            const filterListWidget = await new EatFilterContainerWidgetView(filterListWidgets).buildView(interfaces, userContext, queryParams)
            finalWidets.push(filterListWidget)
        } else {
            finalWidets.push(filterWidget)
        }

        let soldOutWidgetAppended = false
        const soldOutTitleWidget = await new SoldOutTitleWidget("SOLD OUT").buildView(interfaces, userContext, queryParams)
        const webGridWidget = new GridWidget()
        webGridWidget.widgets = []
        let currentCategoryId: string = undefined
        mealWidgets.forEach((mealWidget) => {
            const mealCardWidget = <MealCardWidget>mealWidget
            if (isMenuCategorizationSupported
                && !soldOutWidgetAppended
                && mealCardWidget.stock == 0) {
                finalWidets.push(soldOutTitleWidget)
                soldOutWidgetAppended = true
            }

            if (isMenuCategorizationSupported && currentCategoryId !== mealCardWidget.categoryId
                && mealCardWidget.stock > 0) {
                currentCategoryId = mealCardWidget.categoryId
                finalWidets.push(categoryWidgetMap[currentCategoryId])
            }
            if (userContext.sessionInfo.userAgent === "APP") {
                finalWidets.push(mealCardWidget)
            } else {
                webGridWidget.widgets.push(mealCardWidget)
            }
        })
        if (userContext.sessionInfo.userAgent !== "APP") {
            finalWidets.push(await webGridWidget.buildView(interfaces, userContext, queryParams))
        }
        return finalWidets
    }

    getFilters(mealItems: MealItem[]) {
        const vegFilter: Filter = {
            filterType: "SINGLE_SELECT",
            title: "FOOD",
            property: "isVeg",
            options: [{
                optionText: "All",
                optionValue: undefined
            },
            {
                optionText: "Veg",
                optionValue: true
            }]
        }
        const calorieFilter: Filter = {
            filterType: "SINGLE_SELECT",
            title: "CALORIES",
            property: "calorieBucket",
            options: [{
                optionText: "All",
                optionValue: undefined
            },
            {
                optionText: "0 - 200",
                optionValue: 0
            },
            {
                optionText: "200 - 500",
                optionValue: 1
            },
            {
                optionText: "500+",
                optionValue: 2
            }]
        }
        const tagMap = _.keyBy(NutritionTags, (tag) => {
            return tag
        })
        mealItems.forEach((mealItem) => {
            if (mealItem.nutritiontags) {
                mealItem.nutritiontags.forEach((mealTag) => {
                    if (tagMap[mealTag] && mealTag !== "EGGLESS") {
                        tagMap[mealTag] = undefined
                    }
                })
            }
        })
        const categoryFilter: Filter = {
            filterType: "MULTI_SELECT",
            title: "NUTRITION",
            property: "nutritionTags",
            options: _.map(NutritionTags, (tag: NutritionTag) => {
                const formattedTag = capitalizeFirstLetter(tag).replace("_", " ")
                if (tagMap[tag] === undefined) {
                    return {
                        optionText: formattedTag,
                        optionValue: formattedTag
                    }
                } else {
                    return undefined
                }
            }).filter((option) => {
                return option !== undefined
            })
        }
        const filters = [vegFilter, calorieFilter]
        if (categoryFilter.options.length > 0) {
            filters.push(categoryFilter)
        }
        return filters
    }

    async getMealActions(product: Product, offerDetails: { price: ProductPrice, offers: OfferV2Lite[], offerProduct: Product }, day: string, menuType: MenuType, preferredLocation: PreferredLocation, sessionInfo: SessionInfo, clpTab: EatCLPTabs, stopOrders: boolean, availability: { left: number, total: number }, logger: ILogger, userContext: UserContext, isOnboardingCheckoutSupported: boolean, cafeDeliverySlot?: string): Promise<(Action | MealAction)[]> {
        const actions: (MealAction | Action)[] = []
        const seoParams: SeoUrlParams = {
            productName: product.title
        }
        actions.push({
            url: ActionUtil.foodSingle(product.productId, day, menuType, !stopOrders, undefined, userContext.sessionInfo.userAgent, seoParams, cafeDeliverySlot, "EAT_FIT"),
            actionType: "WIDGET_NAVIGATION"
        })

        if (!AppUtil.isWeb(userContext) && !isOnboardingCheckoutSupported && !sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            // actions.push(action)
            actions.push({ ...action, title: "ADD" })
            return actions
        }

        if (preferredLocation.isInServicableArea) {
            const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })
            const image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "THUMBNAIL", product.imageVersion)
            const action: MealAction = {
                actionType: "BUY_MEAL",
                url: "curefit://cartcheckout",
                title: "BUY",
                date: day,
                image: image,
                productId: product.productId,
                productName: product.title,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds,
                price: offerDetails.price,
                maxCartSize: MAX_CART_SIZE,
                stock: Math.min(MAX_CART_SIZE, availability.left),
                clpTab: clpTab,
                mealSlot: {
                    id: menuType,
                    name: capitalizeFirstLetter(menuType.toLowerCase())
                },
                listingBrand: "EAT_FIT"
            }
            // actions.push(action)
            actions.push({ ...action, actionType: "ADD_TO_CART", title: "ADD" })
        } else {
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD",
                meta: {
                    title: "Unserviceable Location",
                    subTitle: "We currently do not deliver to this area. Please update your location at the top of the page",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        if (stopOrders) {
            actions.splice(1, 2)
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "CLOSED",
                meta: {
                    title: "Ordering closed",
                    subTitle: "We're currently closed for orders. Please check back with us in sometime",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        return actions
    }

    async getRecommendedMealMap(userId: string, day: string, interfaces: IServiceInterfaces, menuType: MenuType) {
        const recommendedMealResult = await eternalPromise(interfaces.cerberusService.getRecommendedMeals(userId, day, menuType))
        return EatUtil.getRecommendedMealMap(recommendedMealResult)
    }

    getDelayMode(menuType: MenuType, deliveryArea: DeliveryArea, timeZone: Timezone): { mealSlot: MealSlot, mode: DelayMode, reason: DelayModeReason, revertDrivingScaleFactor?: number } {
        let mealSlot: MealSlot = undefined
        if (menuType === "ALL") {
            mealSlot = SlotUtil.getMealSlotExact(TimeUtil.now(timeZone))
        }
        else {
            mealSlot = menuType
        }
        if (deliveryArea.delayMode) {
            return deliveryArea.delayMode.find((delayMode) => {
                return delayMode.mealSlot === mealSlot
            })
        }
    }

}
