import { injectable } from "inversify"
import { IBaseWidget, FAQWidgetV2, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
interface Links {
    question: string,
    answer: string,
    feedback: {
        articleId: number,
        text: string,
        positiveCase: string,
        negativeCase: string
    }
}

@injectable()
export class FAQWidgetView extends FAQWidgetV2 {
    links: Links[]
    faqTitle: string

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const articles = await interfaces.supportArticleService.getArticlesFromIds(this.articles)
        this.faqTitle = this.title
        this.links = articles.map((article) => {
            return {
                question: article.title,
                answer: article.descriptionText,
                feedback: {
                    articleId: article.id,
                    text: "Do you find this helpful?",
                    positiveCase: "YES",
                    negativeCase: "NO"
                }
            }
        })
        return this
    }
}