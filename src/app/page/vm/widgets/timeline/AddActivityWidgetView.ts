import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import * as _ from "lodash"
import { ProductType } from "@curefit/product-common"
import { City } from "@curefit/location-common"
import { ActionUtil as EtherActionUtil } from "@curefit/base-utils"
import ActionUtil from "../../../../util/ActionUtil"
import { AllAction } from "../card/CardListWidget"
import { Relation, SubUserRelation, User } from "@curefit/user-common"
import { AddActivityWidget } from "@curefit/vm-models"
import { Action } from "../../../../common/views/WidgetView"
import { CultSummary } from "@curefit/cult-common"
import { AnnouncementDetails } from "../../../../announcement/AnnouncementViewBuilder"
import AppUtil from "../../../../util/AppUtil"
import CultUtil from "../../../../util/CultUtil"
import { ClassType } from "../../../../cult/CultBusiness"
import LiveUtil from "../../../../util/LiveUtil"

interface AddActivityCard {
    title: string
    subTitle: string
    icon: string
    action: AllAction
    color: string
}
export class AddActivityWidgetView extends AddActivityWidget {
    cards: AddActivityCard[] = []
    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const city: City = userContext.userProfile.city
        const user: User = await userContext.userPromise

        let childUsers: User[] = undefined
        if (Array.isArray(user.subUserRelations)) {
            const childUserIds = user.subUserRelations.map(relation => relation.subUserId)
            childUsers = Object.values(await interfaces.userCache.getUsers(childUserIds))
        }
        const cultAndMindSummary = await interfaces.userCache.getCultSummary(userContext.userProfile.userId)
        let announcementDetails: AnnouncementDetails
        const isNuxSupported = await AppUtil.isNUXSupported(userContext, user.isInternalUser, "FITNESS", interfaces.hamletBusiness)
        // no need to create announcement entry in redis for users who does not have nux applied for them
        if (isNuxSupported && cultAndMindSummary && CultUtil.isCompletedClassCountZero(cultAndMindSummary)) {
            announcementDetails = <AnnouncementDetails>await interfaces.announcementBusiness.createAndGetAnnouncementDetails(userContext, "NUX_LANDING_PAGE")
        }

        const [isGymfitEnabled, isTrialClassBooking] = await Promise.all([
            AppUtil.isGymfitVerticalEnabled(userContext),
            interfaces.cultBusiness.isBookFreeCultClassSupported(userContext),
        ])
        const isLivePTSupported = AppUtil.isLivePTSupported(userContext)
        city.availableOfferings.forEach((offering) => {
            const addActivity: AddActivityCard = this.createActivtyActionForType(offering, userContext, user, cultAndMindSummary, announcementDetails, isNuxSupported, interfaces, isGymfitEnabled, isLivePTSupported, childUsers, isTrialClassBooking)
            if (!_.isNil(addActivity)) {
                this.cards.push(addActivity)
            }
        })

        //  add whole.fit card
        const userProfile = userContext.userProfile
        const preferredLocation = await userProfile.preferredLocationPromise
        if (!_.isEmpty(preferredLocation) && !_.isEmpty(preferredLocation.area)) {
            const wholeFitPreferredLocation = await userProfile.wholeFitPreferredLocationPromise
            if (!_.isNil(wholeFitPreferredLocation) && !_.isNil(wholeFitPreferredLocation.area)) {
                const addActivity: AddActivityCard = this.createActivtyActionForType("WHOLE_FIT", userContext, user, cultAndMindSummary, announcementDetails, isNuxSupported, interfaces, isGymfitEnabled, isLivePTSupported, childUsers, isTrialClassBooking)
                this.cards.splice(4, 0, addActivity)
            }
        }
        return this
    }

    createActivtyActionForType(productType: ProductType, userContext: UserContext, user: User, cultAndMindSummary: CultSummary, announcementDetails: AnnouncementDetails, isNuxSupported: boolean, interfaces: CFServiceInterfaces, isGymfitEnabled: boolean, isLivePTSupported: boolean, subUsers?: User[], isTrialClassBooking?: ClassType): AddActivityCard {
        const { cult, mind } = isTrialClassBooking
        const isNewClassBookingSupported = AppUtil.isNewClassBookingSuppoted(userContext, user.isInternalUser)
        const isNewLiveClassBookingPageSupported = AppUtil.isNewLiveClassBookingPageSupported(userContext)
        if (productType === "FITNESS") {
            // let action: Action = ActionUtil.getClassBookingAction(userContext, productType, user, subUsers, "addActivity")
            // if (isNuxSupported && cultAndMindSummary && CultUtil.isCompletedClassCountZero(cultAndMindSummary) && announcementDetails.state === "CREATED") {
            //     action = {
            //         actionType: "NAVIGATION",
            //         url: "curefit://nuxlandingpage"
            //     }
            // }
            const action: Action = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "addActivity")
            return {
                title: (cult && mind) ? "BOOK FREE" : "BOOK",
                subTitle: "cult class",
                icon: "/image/addActivity/widgeticon/workout_icon.png",
                action: action,
                color: "#8080B3"
            }
        } else if (productType === "DIY_FITNESS") {
            const fitnessWodWidgetId = "ed7e89ea-aba0-49c5-be7f-89468b4d6b9e"
            const fitnessDiySeriesId = "a0b04951-0ff2-4794-a4d0-a8bf34e8b2f2"
            const widgetId = AppUtil.isDiyWodSupported(userContext) ? fitnessWodWidgetId : fitnessDiySeriesId
            return {
                title: "WORKOUT",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/workout_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=CultAtHome&widgetId=" + widgetId
                },
                color: "#8080B3"
            }

        } else if (productType === "LIVE") {
            const action: Action = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "addActivity")
            return {
                title: "JOIN",
                subTitle: LiveUtil.getCultLiveBranding(userContext),
                icon: "/image/addActivity/widgeticon/live_icon_2.png",
                action,
                color: "#6C82FF"
            }
        } else if (productType === "MIND") {
            // return {
            //     title: mind ? "BOOK FREE" : "BOOK",
            //     subTitle: "mind class",
            //     icon: "/image/addActivity/widgeticon/mind_icon.png",
            //     action: {
            //         actionType: "NAVIGATION",
            //         url: EtherActionUtil.getBookCultClassUrl("MIND", true, "addActivity", undefined, userContext.sessionInfo.sessionData.mindCenterId)
            //     },
            //     color: "#F46DA0"
            // }
            return {
                title: "JOIN",
                subTitle: "mind.live",
                icon: "/image/addActivity/widgeticon/mind_icon.png",
                action: LiveUtil.getLiveClassBookingPageAction(userContext, "MIND", "addActivity"),
                color: "#F46DA0"
            }
        } else if (productType === "DIY_MEDITATION") {
            const mindWodWidgetId = "51472cde-b800-47fd-8384-b2288c0bab11"
            const mindDiySeriesId = "04171629-c44c-4920-8eb6-584921b2b16a"
            const widgetId = AppUtil.isDiyWodSupported(userContext) ? mindWodWidgetId : mindDiySeriesId
            return {
                title: "MEDITATE",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/mind_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=MindAtHome&widgetId=" + widgetId
                },
                color: "#F46DA0"
            }
        } else if (productType === "FOOD") {
            return {
                title: "ORDER",
                subTitle: "meals",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: EtherActionUtil.eatFitClp()
                },
                color: "#FFA522"
            }
        } else if (productType === "RECIPE") {
            return {
                title: "COOK",
                subTitle: "at home",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: AppUtil.RecipeRateAgainNotAllowedSupported(userContext) && !AppUtil.isInternationalApp(userContext) ? "curefit://livefitclp?pageId=live&selectedTab=eatlive" : "curefit://eatrecipe",
                },
                color: "#FFA522"
            }
        } else if (productType === "CONSULTATION") {
            return {
                title: "CONSULT",
                subTitle: "doctor",
                icon: "/image/addActivity/widgeticon/care_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getConsultationClpPage(),
                },
                color: "#45C6CF"
            }
        } else if (productType === "GEAR") {
            return {
                title: "BUY",
                subTitle: "sportswear",
                icon: "/image/addActivity/widgeticon/gear_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://cultgearclp?pageId=GearTab"
                },
                color: "#7FB486"
            }
        } else if (productType === "THERAPY") {
            return {
                title: "BOOK",
                subTitle: "therapy",
                icon: "/image/addActivity/widgeticon/therapy_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=MindTherapy",
                },
                color: "#D67B9D"
            }
        } else if (productType === "WHOLE_FIT") {
            return {
                title: "ORDER",
                subTitle: "groceries",
                icon: "/image/addActivity/widgeticon/whole-fit.png",
                action: {
                    actionType: "NAVIGATION",
                    url: AppUtil.isWholeFitV2ReviewSupported(userContext)
                        ? "curefit://tabpage?pageId=eatclp&selectedTab=wholefitv2"
                        : "curefit://tabpage?pageId=eatclp&selectedTab=wholefitv2"
                },
                color: "#7FB486"
            }
        } else if (productType === "GYMFIT_FITNESS_PRODUCT" && isGymfitEnabled) {
            return {
                title: "BOOK",
                subTitle: "gymfit class",
                icon: "/image/addActivity/widgeticon/gym_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://allgyms?centerType=GYM"
                },
                color: "#7e93b4"
            }
        } else if (productType === "LIVE_PERSONAL_TRAINING" && isLivePTSupported) {
            return {
                title: "BOOK",
                subTitle: "live PT",
                icon: "/image/addActivity/widgeticon/live_pt_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://tabpage?pageId=cult&selectedTab=LivePT"
                },
                color: "#ab79ff"
            }
        } else if (productType === "NUTRITIONIST_CONSULTATION") {
            return {
                title: "CONSULT",
                subTitle: "nutritionist",
                icon: "/image/addActivity/widgeticon/eat_icon.png",
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://listpage?pageId=clplc"
                },
                color: "#45C6CF"
            }
        }
    }
}
