import { UserContext } from "@curefit/userinfo-common"
import { <PERSON><PERSON>, IBaseWidget, RecommendationProductType, RecommendationWidget } from "@curefit/vm-models"
import { DigitalCatalogueEntryV1, ImageData, StreamData } from "@curefit/diy-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { CardListItem, CardListWidgetFooter, CardListWidgetView } from "../card/CardListWidget"
import {
    CultProductDetails,
    DIYProductDetails,
    EatProductDetails,
    GymProductDetails,
    RecommendationEngine,
    RecommendationRequest
} from "@curefit/recommendation-client"
import * as _ from "lodash"
import { capitalizeFirstLetter, eternalPromise, TimeUtil, Timezone } from "@curefit/util-common"
import { Action, MealAction } from "../../../../common/views/WidgetView"
import { ActionUtil as BaseActionUtil, ActionUtilV1, Offer<PERSON>til, RUPEE_SYMBOL, SeoUrlParams } from "@curefit/base-utils"
import { UrlPathBuilder } from "@curefit/product-common"
import { FoodProduct, MealSlot } from "@curefit/eat-common"
import { TimelineHelper } from "./TimelineHelper"
import { MAX_CART_SIZE } from "../../../../util/MealUtil"
import ActionUtil from "../../../../util/ActionUtil"
import { GymfitCenter, GymfitCheckIn, MediaType } from "@curefit/gymfit-common"
import { User } from "@curefit/user-common"
import LiveUtil from "../../../../util/LiveUtil"
import { PreferenceDetail } from "../../../../cult/CultBusiness"
import { SlotUtil } from "@curefit/eat-util"
import { BaseOfferRequestParams, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import AppUtil from "../../../../util/AppUtil"
import { EatOfferRequestParamsV3 } from "@curefit/offer-common/src/Offer"
import { BaseServiceConfig } from "@curefit/config-mongo"
import { LivePackUtil } from "../../../../util/LivePackUtil"
import { CultSummary } from "@curefit/cult-common"
import { ComplimentaryAccessMembership } from "@curefit/cult-client"
import GymfitUtil from "../../../../util/GymfitUtil"

export class RecommendationWidgetView extends RecommendationWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {

        if (this.productType === "DIY_FITNESS" || this.productType === "DIY_MEDITATION") {
            return new DIYRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams)
        }
        else if (this.productType === "FITNESS" || this.productType === "MIND") {
            return new CultOrMindRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams)
        } else if (this.productType === "FOOD") {
            return new FoodRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams)
        } else if (this.productType === "GYMFIT_FITNESS_PRODUCT") {
            return new GymRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams
            )
        } else if (this.productType == "LIVE") {
            return new LiveRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams
            )
        } else if (this.productType === "CARE") {
            return new CareRecommendationView(this.productType, this.header).buildView(
                interfaces,
                userContext,
                queryParams)
        }
    }
    protected getBaseRecommendationRequest(userContext: UserContext): RecommendationRequest {
        const recommendationRequest: RecommendationRequest = {
            timezone: userContext.userProfile.timezone,
            userInfo: {
                userId: userContext.userProfile.userId,
                location: {
                    cityId: userContext.userProfile.city.cityId,
                    latLong: {
                        lat: userContext.sessionInfo.lat,
                        long: userContext.sessionInfo.lon
                    },
                    countryId: AppUtil.getCountryId(userContext)
                },
                tenant: AppUtil.getTenantFromUserContext(userContext),
            },
            count: 3,
            filters: {
            },
            fromTimeEpoch: undefined
        }
        return recommendationRequest
    }
}


class DIYRecommendationView extends RecommendationWidgetView {
    constructor(produtType: RecommendationProductType, header: Header) {
        super()
        this.productType = produtType
        this.header = header
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {

        let recommendationEngine: RecommendationEngine
        let blockInternationalUser = false
        if (this.productType === "DIY_FITNESS") {
            blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
            recommendationEngine = RecommendationEngine.DIY_FITNESS_RECOMMENDATION_ENGINE
        } else if (this.productType === "DIY_MEDITATION") {
            recommendationEngine = RecommendationEngine.DIY_MEDITATION_RECOMMENDATION_ENGINE
        }


        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)

        const recommendationRequest = this.getBaseRecommendationRequest(userContext)
        const recommendationsResponse = await interfaces.recommendationService.getRecommendations(recommendationEngine, recommendationRequest)

        if (!_.isEmpty(recommendationsResponse.recommendations)) {
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
            const cardListItems: CardListItem[] = _.map(recommendationsResponse.recommendations, recommendation => {
                const diyProductDetails: DIYProductDetails = <DIYProductDetails>recommendation.productDetails
                const diyProduct = diyProductDetails.product
                const diyPack = diyProductDetails.pack
                return TimelineHelper.diyItemToCardListItem(userContext, diyPack, diyProduct, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, blockInternationalUser)
            })
            let footer: CardListWidgetFooter
            if (userContext.sessionInfo.appVersion >= 7.63) {
                footer = {
                    contentType: "VIEW_MORE_FOOTER",
                    title: "More",
                    action: {
                        actionType: "NAVIGATION",
                        url: this.productType === "DIY_FITNESS" ? this.getAtHomeFitnessDeeplink(userContext) :
                            this.getAtHomeMindDeeplink(userContext)
                    }

                }
            }
            return new CardListWidgetView(this.header.title, "DIY_RECOMMENDATIONS", cardListItems, footer).buildView(interfaces, userContext, queryParams)
        }


    }

    private getAtHomeFitnessDeeplink(userContext: UserContext): string {
        const fitnessWodWidgetId = "ed7e89ea-aba0-49c5-be7f-89468b4d6b9e"
        const fitnessDiySeriesId = "a0b04951-0ff2-4794-a4d0-a8bf34e8b2f2"
        const widgetId = AppUtil.isDiyWodSupported(userContext) ? fitnessWodWidgetId : fitnessDiySeriesId
        return "curefit://listpage?pageId=" + AppUtil.getCultPage(userContext) + "&widgetId=" + widgetId
    }

    private getAtHomeMindDeeplink(userContext: UserContext): string {
        const mindWodWidgetId = "51472cde-b800-47fd-8384-b2288c0bab11"
        const mindDiySeriesId = "04171629-c44c-4920-8eb6-584921b2b16a"
        const widgetId = AppUtil.isDiyWodSupported(userContext) ? mindWodWidgetId : mindDiySeriesId
        return "curefit://listpage?pageId=" + AppUtil.getMindPage(userContext) + "&widgetId=" + widgetId
    }
}

class CultOrMindRecommendationView extends RecommendationWidgetView {
    constructor(productType: RecommendationProductType, header: Header) {
        super()
        this.productType = productType
        this.header = header
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {

        const recommendationEngine: RecommendationEngine = RecommendationEngine.CULT_RECOMMENDATION_ENGINE
        const recommendationRequest = this.getBaseRecommendationRequest(userContext)
        recommendationRequest.userInfo.location.cityId = userContext.userProfile.city.cultCityId.toString()

        const recommendationsResponse = await interfaces.recommendationService.getRecommendations(recommendationEngine, recommendationRequest)
        let productType
        if (!_.isEmpty(recommendationsResponse.recommendations)) {
            const cardListItems: CardListItem[] = _.map(recommendationsResponse.recommendations, recommendation => {
                const cultProductDetails: CultProductDetails = <CultProductDetails>recommendation.productDetails

                const classDate = TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, cultProductDetails.classDate, "YYYY-MM-DD")
                const bookAction: Action = {
                    title: "BOOK",
                    actionType: "NAVIGATION",
                    url: BaseActionUtil.getBookCultClassUrl(recommendation.productType === "CULT" ? "FITNESS" : "MIND", true, "hometab", undefined, cultProductDetails.centerId, cultProductDetails.classId, classDate)
                }
                productType = recommendation.productType === "CULT" ? "FITNESS" : "MIND"
                // Generate image URL
                const document = _.find(cultProductDetails.workoutDocument, document => {
                    return document.tagID === 11
                })
                const imageUrl: string = document ? "/" + document.URL : undefined
                const recommendationItem: CardListItem = {
                    title: cultProductDetails.workoutName + "-" + cultProductDetails.centerName,
                    subTitle: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, cultProductDetails.classDate, "ddd, D MMM, h:mma"),
                    action: bookAction,
                    leftInfo: {
                        images: [imageUrl]
                    },
                    rightInfo: {
                        action: bookAction,
                        moreAction: undefined
                    },
                }
                return recommendationItem
            })

            let footer: CardListWidgetFooter
            if (userContext.sessionInfo.appVersion >= 7.63) {
                footer = {
                    contentType: "VIEW_MORE_FOOTER",
                    title: "View full schedule",
                    action: ActionUtil.getClassBookingAction(userContext, productType, await userContext.userPromise)

                }
            }

            return new CardListWidgetView(this.header.title, "CLASS_RECOMMENDATIONS", cardListItems, footer).buildView(interfaces, userContext, queryParams)
        }

    }
}

export interface EatRecommendationConfig extends BaseServiceConfig {
    configs?: {
        startTime?: {
            hour: number,
            min: number
        }
        endTime?: {
            hour: number,
            min: number
        }
    }
}

class FoodRecommendationView extends RecommendationWidgetView {
    constructor(productType: RecommendationProductType, header: Header) {
        super()
        this.productType = productType
        this.header = header
    }
    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const timezone = TimeUtil.IST_TIMEZONE

        const config: EatRecommendationConfig = interfaces.configService.getConfig(<any>"EAT_RECO")
        if (!this.isRecoAvailableForTime(config, timezone)) {
            return null
        }

        const recommendationRequest = this.getBaseRecommendationRequest(userContext)
        recommendationRequest.userInfo.location.areaId = userContext.userProfile.areaId
        const dateMealSlot = await this.getRecommendationDateMealSlot(recommendationRequest, interfaces)
        recommendationRequest.filters.date = dateMealSlot.recommendationDate
        recommendationRequest.filters.mealSlot = dateMealSlot.mealSlot


        //  const offersPromise = this.getSingleOffers(interfaces, userContext, dateMealSlot.recommendationDate, dateMealSlot.mealSlot)
        const { recommendations } = await interfaces.recommendationService.getRecommendations(RecommendationEngine.EAT_RECOMMENDATION_ENGINE, recommendationRequest)
        if (_.isEmpty(recommendations)) {
            return null
        }
        // Pricing offer call is disabled as this is a very heavy call
        // const eatSingleOffersResult = await offersPromise
        const cardListItems: CardListItem[] = _.map(recommendations, recommendation => {
            const eatProductDetails = recommendation.productDetails as EatProductDetails
            const foodProduct = eatProductDetails.product as FoodProduct
            const dateString = TimeUtil.formatDateInTimeZone(timezone, recommendation.date)
            // const offerDetails = OfferUtil.getSingleOfferAndPrice(foodProduct, dateString, eatSingleOffersResult, eatProductDetails.mealSlot)
            // const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })
            const image = UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, foodProduct.productType, "THUMBNAIL", foodProduct.imageVersion)
            const checkoutParams = userContext.sessionInfo.appVersion < 7.64 ? `?productId=${foodProduct.productId}&listingBrand=EAT_FIT` : "" // Added fix for older versions logging events without productId
            const action: MealAction = {
                actionType: "BUY_MEAL",
                url: "curefit://cartcheckout" + checkoutParams,
                title: "ORDER",
                date: dateString,
                image: image,
                listingBrand: "EAT_FIT",
                productId: foodProduct.productId,
                productName: foodProduct.title,
                offerId: undefined,
                offerIds: [],
                price: foodProduct.price, // offerDetails.price,
                maxCartSize: MAX_CART_SIZE,
                stock: Math.min(MAX_CART_SIZE, eatProductDetails.available),
                slot: eatProductDetails.mealSlot,
                mealSlot: {
                    id: eatProductDetails.displayMealSlot,
                    name: capitalizeFirstLetter(eatProductDetails.displayMealSlot.toLowerCase())
                }
            }
            const recommendationItem: CardListItem = {
                title: foodProduct.title,
                subTitle: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, recommendation.date, "D MMM"), // 06 April, not using preferred loc timezone
                action: action,
                leftInfo: {
                    images: [UrlPathBuilder.getSingleImagePath(foodProduct.parentProductId ? foodProduct.parentProductId : foodProduct.productId, "FOOD", "THUMBNAIL", foodProduct.imageVersion)]
                },
                rightInfo: undefined
                /*rightInfo: {
                    title: RUPEE_SYMBOL + "" + offerDetails.price.listingPrice,
                    subTitle: (offerDetails.price.mrp - offerDetails.price.listingPrice > 0) ? RUPEE_SYMBOL + "" + offerDetails.price.mrp : undefined,
                    action: action,
                    moreAction: undefined
                },*/
            }
            if (eatProductDetails.displayMealSlot && eatProductDetails.displayMealSlot !== "ALL") {
                this.header.title = _.capitalize(eatProductDetails.displayMealSlot) + " suggestions"
            }
            return recommendationItem
        })
        let footer: CardListWidgetFooter
        if (userContext.sessionInfo.appVersion >= 7.63) {
            footer = {
                contentType: "VIEW_MORE_FOOTER",
                title: "All meals",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.eatFitClp()
                }
            }
        }
        return new CardListWidgetView(this.header.title, "FOOD_RECOMMENDATIONS", cardListItems, footer).buildView(interfaces, userContext, queryParams)
    }

    private isRecoAvailableForTime(config: EatRecommendationConfig, tz: Timezone): boolean {
        if (_.isNil(config) || _.isNil(config.configs)) {
            return true
        }
        if (_.isNil(config.configs.startTime) && _.isNil(config.configs.endTime)) {
            return true
        }
        const currentTime = TimeUtil.getMomentNow(_.defaultTo(tz, "Asia/Kolkata"))
        if (!_.isNil(config.configs.startTime)) {
            const startTime = config.configs.startTime
            if (currentTime.hour() < startTime.hour) {
                return false
            }
            if ((currentTime.hour() == startTime.hour) && (currentTime.minutes() < startTime.min)) {
                return false
            }
        }

        if (!_.isNil(config.configs.endTime)) {
            const endTime = config.configs.endTime
            if (currentTime.hour() > endTime.hour) {
                return false
            }
            if ((currentTime.hour() == endTime.hour) && (currentTime.minutes() > endTime.min)) {
                return false
            }
        }
        return true

    }

    private async getRecommendationDateMealSlot(request: RecommendationRequest, interfaces: CFServiceInterfaces): Promise<{ mealSlot: MealSlot, recommendationDate: string }> {
        const areaId: string = request.userInfo.location.areaId
        const area = await interfaces.deliveryAreaService.getDeliveryArea(areaId)
        let timezone: Timezone = <Timezone>request.timezone
        if (_.isNil(timezone)) {
            timezone = await interfaces.deliveryAreaService.getTimeZoneForArea(area)
        }
        const epoch: number = request.fromTimeEpoch
        let recommendationDate = TimeUtil.formatEpochInTimeZoneDateFns(timezone, epoch, "yyyy-MM-dd")
        const areaMealSlots = await interfaces.deliveryAreaService.getMealSlotsForArea(areaId)
        let mealSlot: MealSlot = undefined
        if (!_.isNil(request.filters) && !_.isNil(request.filters.mealSlot)) {
            mealSlot = request.filters.mealSlot
            !_.isNil(request.filters.date) && (recommendationDate = request.filters.date)
        } else {
            mealSlot = SlotUtil.getCurrentMealSlotFrom(areaMealSlots, area.channel, timezone)
            if (_.isNil(mealSlot) || !SlotUtil.isServiceable(areaMealSlots, area, timezone).isServiceable) {
                const nextMealSlot: { mealSlot: MealSlot, date: string } = SlotUtil.getNextAvailableMealSlot(mealSlot, areaMealSlots, timezone)
                mealSlot = nextMealSlot.mealSlot
                recommendationDate = nextMealSlot.date
            }
        }

        return { mealSlot, recommendationDate }
    }

    private async getSingleOffers(interfaces: CFServiceInterfaces, userContext: UserContext, recommendationDate: string, mealSlot: MealSlot) {
        const userProfile = userContext.userProfile
        const baseOfferRequestParam: BaseOfferRequestParams = {
            source: AppUtil.callSourceFromContext(userContext),
            userId: userContext.userProfile.userId,
            deviceId: userContext.sessionInfo.deviceId,
            cityId: userContext.userProfile.cityId
        }

        const eatOfferRequestParams: EatOfferRequestParamsV3 = Object.assign({}, baseOfferRequestParam, {
            areaId: userProfile.areaId,
            dateMealSlotMap: {
                [recommendationDate]: [mealSlot]
            }
        })

        return {} as FoodSinglePriceOfferResponse
    }
}

class GymRecommendationView extends RecommendationWidgetView {
    constructor(productType: RecommendationProductType, header: Header) {
        super()
        this.productType = productType
        this.header = header
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const recommendationEngine = RecommendationEngine.GYM_RECOMMENDATION_ENGINE
        const recommendationRequest = this.getBaseRecommendationRequest(userContext)
        let recommendationsResponse
        try {
            recommendationsResponse = await interfaces.recommendationService.getRecommendations(recommendationEngine, recommendationRequest)
        } catch (e) {
            console.log(e)
        }
        if (recommendationsResponse && !_.isEmpty(recommendationsResponse.recommendations)) {
            const goldMemberships = await userContext.userProfile.promiseMapCache.getPromise("cultpass-gold-memberships", { userId: userContext.userProfile.userId })
            const goldTrialDetails = await userContext.userProfile.promiseMapCache.getPromise("cultpass-trial-usage", { userId: userContext.userProfile.userId })
            const complimentaryMemberships: ComplimentaryAccessMembership[] = await userContext.userProfile.promiseMapCache.getPromise("cult-complimentary-access", { userId: userContext.userProfile.userId })
            const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
            const cardListItems: CardListItem[] = await Promise.all(_.map(recommendationsResponse.recommendations, async (recommendation) => {
                const gymProductDetails: GymProductDetails = <GymProductDetails>recommendation.productDetails
                const action = await interfaces.gymfitBusiness.getCheckinCTAForUser(userContext, gymProductDetails.center, goldMemberships, goldTrialDetails, hasComplimentaryAccess)
                const imageMedia = gymProductDetails.center.media && gymProductDetails.center.media.clpMedia ? gymProductDetails.center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined
                const seoUrlParam: SeoUrlParams = GymfitUtil.getCenterSeoUrlParam(gymProductDetails.center)
                const recommendationItem: CardListItem = {
                    title: gymProductDetails.center.name,
                    subTitle: "",
                    action: {
                        actionType: "NAVIGATION",
                        url: AppUtil.isWeb(userContext) ? ActionUtilV1.getGymCenterWebUrl(seoUrlParam, gymProductDetails.center.id) : `curefit://gymfitcenter?centerId=${gymProductDetails.centerId}`
                    },
                    leftInfo: {
                        images: imageMedia ? [imageMedia.mediaUrl] : []
                    },
                    rightInfo: {
                        action,
                        moreAction: null
                    }
                }
                return recommendationItem
            }))
            let footer: CardListWidgetFooter
            if (userContext.sessionInfo.appVersion >= 7.63) {
                footer = {
                    contentType: "VIEW_MORE_FOOTER",
                    title: "More",
                    action: {
                        actionType: "NAVIGATION",
                        url: "curefit://allgyms?centerType=GYM"
                    }
                }
            }
            return new CardListWidgetView(this.header.title, "FOOD_RECOMMENDATIONS", cardListItems, footer).buildView(interfaces, userContext, queryParams)
        }
    }
}

class LiveRecommendationView extends RecommendationWidgetView {
    constructor(productType: RecommendationProductType, header: Header) {
        super()
        this.productType = productType
        this.header = header
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        let recommendationsResponse
        try {
            recommendationsResponse = await interfaces.diyService.getLiveSessionRecommendations(userContext.userProfile.userId, AppUtil.getCountryId(userContext))
        } catch (e) {
            interfaces.logger.error(e)
        }
        const subscribedEntries = await interfaces.diyService.getSubscribedVideos(userContext.userProfile.userId)
        const user: User = await userContext.userPromise
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        if (recommendationsResponse && !_.isEmpty(recommendationsResponse)) {
            const cardListItems: CardListItem[] = await Promise.all(_.map(recommendationsResponse, async (recommendation: DigitalCatalogueEntryV1) => {
                // const cultPreference: PreferenceDetail = (await eternalPromise(interfaces.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
                const bookingPref = false   // (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
                // const firestoreEnabled = await LiveUtil.isFirestoreEnabled(interfaces.diyService, (<any>recommendation)._id)
                const countMeInAction = await LiveUtil.getStatusBasedSessionAction(user, userContext, recommendation, subscribedEntries, userContext.sessionInfo, 0, "live_class_recommendation_widget", bookingPref, interfaces.classInviteLinkCreator, true, interfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId)
                const classDetailAction = LiveUtil.getLiveSessionDetailActionFromLiveClassId(undefined, (<any>recommendation)._id, undefined, recommendation.contentCategory, userContext, undefined)

                const imageMedia = []
                imageMedia.push(recommendation.bannerImages.mobileImage)
                const recommendationItem: CardListItem = {
                    title: recommendation.title,
                    subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, recommendation.scheduledTimeEpoch, "ddd, D MMM, h:mma"),
                    action: classDetailAction,
                    leftInfo: {
                        images: imageMedia ? imageMedia : []
                    },
                    rightInfo: {
                        action: countMeInAction,
                        moreAction: null
                    }
                }
                return recommendationItem
            }))
            let footer: CardListWidgetFooter
            if (userContext.sessionInfo.appVersion >= 7.63) {
                const bookingPageAction = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", undefined, user)
                footer = {
                    contentType: "VIEW_MORE_FOOTER",
                    title: "View full schedule",
                    action: {
                        actionType: "NAVIGATION",
                        url: bookingPageAction.url
                    }
                }
            }
            return new CardListWidgetView(this.header.title, "LIVE_RECOMMENDATIONS", cardListItems, footer).buildView(interfaces, userContext, queryParams)
        }
    }
}


class CareRecommendationView extends RecommendationWidgetView {
    constructor(productType: RecommendationProductType, header: Header) {
        super()
        this.productType = productType
        this.header = header
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const isBangalore = userContext.userProfile.cityId == "Bangalore"
        const cardListItems: CardListItem[] = [{
            title: "Doctor Consult @ Home",
            subTitle: "Expert care at your fingertips",
            action: {
                "title": "BOOK",
                "actionType": "NAVIGATION",
                "url": "curefit://tabpage?pageId=careclptab&selectedTab=clpconsultation"
            },
            leftInfo: {
                images: [
                    "/image/carefit/consultation/CONS-timeline4.png"
                ]
            },
            rightInfo: {
                action: {
                    "title": "BOOK",
                    "actionType": "NAVIGATION",
                    "url": "curefit://tabpage?pageId=careclptab&selectedTab=clpconsultation"
                },
                moreAction: null
            }
        }]
        if (isBangalore) {
            cardListItems.push({
                title: "Diagnostic Tests @ Home",
                subTitle: "Safe, Convenient and Quick",
                action: {
                    "title": "BOOK",
                    "actionType": "NAVIGATION",
                    "url": "curefit://listpage?pageId=clphcu"
                },
                leftInfo: {
                    images: [
                        "/image/carefit/diagnostics/DIAG_timeline05.png"
                    ]
                },
                rightInfo: {
                    action: {
                        "title": "BOOK",
                        "actionType": "NAVIGATION",
                        "url": "curefit://listpage?pageId=clphcu"
                    },
                    moreAction: null
                }
            })
        }
        return new CardListWidgetView(this.header.title, "MEDICAL_REPORTS", cardListItems, undefined).buildView(interfaces, userContext, queryParams)
    }
}
