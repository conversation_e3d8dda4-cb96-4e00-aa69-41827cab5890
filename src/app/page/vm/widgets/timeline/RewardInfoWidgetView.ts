import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TimeUtil } from "@curefit/util-common"
import { CardListWidgetView, CardListItem } from "../card/CardListWidget"
import * as _ from "lodash"
import { RewardInfoWidget } from "@curefit/vm-models"
import { CultBooking } from "@curefit/cult-common"
import * as momentTz from "moment-timezone"
import { UserRewardRequestParams } from "@curefit/quest-common"
import { Action } from "../../../../common/views/WidgetView"

export class RewardInfoWidgetView extends RewardInfoWidget {
    title: string
    subTitle: string
    imageContent: string
    imageUrl: string
    action: Action


    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const userRewardRequestParams: UserRewardRequestParams = {
            userId: userContext.userProfile.userId,
            redeemed: false,
            fromDate: TimeUtil.subtractFromCurrentDate(userContext.userProfile.timezone, 30, "day").format("YYYY-MM-DD")
        }
        const rewards = await interfaces.questService.getUserRewards(userRewardRequestParams)
        if (!_.isEmpty(rewards.results)) {
            if (rewards.results.length > 1) {
                this.title = `Hurray! ${rewards.results.length} rewards are waiting for you`
                this.imageUrl = "/image/icons/fitclub/fitclub_unclaimed.png"
                // this.imageContent = rewards.results.length + "" // todo(@hunny): remove when UI is fixed
                this.action = {
                    actionType: "NAVIGATION",
                    url: "curefit://rewardlistpage?rewardType=unclaimed&title=Unclaimed Rewards"
                }
            } else {
                this.title = "Hurray! A reward is waiting for you"
                this.imageUrl = "/image/icons/fitclub/fitclub_unclaimed.png"
                const expiringReward = rewards.results[0]
                const expiryDate = TimeUtil.addToDate(expiringReward.date.date, userContext.userProfile.timezone, 30, "days")
                this.subTitle = "Valid till " + expiryDate.format("Do MMM YY")
                this.action = {
                    actionType: "CLAIM_FITCLUB_REWARD",
                    meta: {
                        userRewardId: expiringReward.userRewardId
                    }
                }
            }
            return this
        }
    }
}
