import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { CardListWidgetView, CardListItem } from "../card/CardListWidget"
import * as _ from "lodash"
import { PrescriptionWidget } from "@curefit/vm-models"

export class PresecriptionWidgetView extends PrescriptionWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const items: CardListItem[] = []
        if (!_.isEmpty(items)) {
            return new CardListWidgetView("Prescription", "MEDICAL_REPORTS", items).buildView(interfaces, userContext, queryParams)
        }
    }
}