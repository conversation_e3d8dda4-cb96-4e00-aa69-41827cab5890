import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { Action } from "@curefit/vm-models"
import { ActionUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { ProfileWidget } from "@curefit/vm-models"


export class ProfileWidgetView extends ProfileWidget {

    profile: {
        firstName: string
        lastName: string
        profilePictureUrl: string
        action: Action
    }
    fitcash: {
        icon: string
        description: string
        action: Action
        tooltip?: {
            heading: string
            submitText: string
        }
    }

    showToolTip(balance: number) {
        if (balance > 50) {
            return (
                {
                    tooltip: {
                        heading: "Use your available fitcash balance to buy a meal for free",
                        submitText: "OKAY"
                    }
                }
            )
        }
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const user = await userContext.userPromise
        const action: Action = userContext.sessionInfo.isUserLoggedIn ? {
                actionType: "NAVIGATION",
                url: "curefit://accountview"
            } :
            {
                actionType: "LOGOUT",
                title: "Login"
            }
        this.profile = {
            firstName: user.firstName,
            lastName: user.lastName,
            profilePictureUrl: user.profilePictureUrl,
            action: action
        }

        if (userContext.userProfile.city.countryId === "IN") {
            const walletBalance = await interfaces.fitcashService.balance(userContext.userProfile.userId, userContext.userProfile.city.country.currencyCode)
            const balance = Math.round(walletBalance.balance / 100)
            this.fitcash = {
                icon: "FITCASH",
                description: `${balance}`,
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://fitcash"
                },
                ...(
                    this.showToolTip(balance)
                )
            }
        }

        return this
    }
}
