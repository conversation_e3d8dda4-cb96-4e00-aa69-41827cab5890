import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget, CompletedActivitiesWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { CardListWidgetView, CardListItemWithTimestamp, CardListItemRightInfo, DEFAULT_PAGINATION_START, getDefaultPaginationCount } from "../card/CardListWidget"
import * as _ from "lodash"
import { ChallengeWidget } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"
import { _isStepsChallenge, _getJoinChallengeAction } from "../../../../maximus/Helper"
import { UrlPathBuilder, ChallengeImageTypes, Product, ActivityType, ProductType } from "@curefit/product-common"
import { SocialColors } from "../../../../common/views/Social"
import { Action } from "../../../../common/views/WidgetView"
import { ActivityDS, ActivityState } from "@curefit/logging-common"
import ActivityLoggingUtil from "../../../../util/ActivityLoggingUtil"
import { CultBooking, User, BulkBookingResponse } from "@curefit/cult-common"
import { User as CommonUser } from "@curefit/user-common"
import TimelineUtil from "../../../../util/TimelineUtil"
import ActionUtil from "../../../../util/ActionUtil"
import { TimelineHelper } from "./TimelineHelper"
import { FoodBooking, FoodBookingProduct } from "@curefit/alfred-client"
import { ListingBrandIdType, MealSlot, MenuType } from "@curefit/eat-common"
import { SlotUtil } from "@curefit/eat-util"
import { SortOrder } from "@curefit/mongo-utils"
import OrderTrackingStatusWidget from "../../../../common/views/OrderTrackingStatusWidget"
import { DIYPack, DIYProduct } from "@curefit/diy-common"
import { ActionUtil as BaseActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { WalkActivity, SleepActivity } from "@curefit/atlas-client"
import { ConsultationProduct, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { BookingDetail, TimelineActivity, IHealthfaceService } from "@curefit/albus-client"
import { ILogger } from "@curefit/base"
import CareUtil from "../../../../util/CareUtil"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { GymfitCheckIn, GymfitCheckInState } from "@curefit/gymfit-common"
import AppUtil from "../../../../util/AppUtil"
import LiveUtil from "../../../../util/LiveUtil"
import { ICatalogueService } from "@curefit/catalog-client"

export class CompletedActivitiesWidgetView extends CompletedActivitiesWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const user = await userContext.userPromise
        const subUserIds = user.subUserRelations && user.subUserRelations.map(subUserRelation => subUserRelation.subUserId) || []
        const allUserIds = [user.id, ...subUserIds]
        userContext.sessionInfo.appVersion
        const start = queryParams.start ? Number(queryParams.start) : DEFAULT_PAGINATION_START
        const count = queryParams.count ? Number(queryParams.count) : getDefaultPaginationCount(userContext.sessionInfo.appVersion)
        const lastActivityDate = queryParams.lastActivityDate || undefined

        const loggedActivities = await interfaces.loggingService.getActivitiesFor({
            userId: allUserIds,
            show: [true],
            activityType: ["CULT_CLASS", "EATFIT_MEAL", "MIND_CLASS", "WALK", "SLEEP", "DIY_FITNESS", "DIY_MEDITATION",
                "CONSULTATION", "AT_HOME_SAMPLE_COLLECTION", "IN_CENTRE_VISIT_FOR_TEST", "CONSULTATION_PT", "GYMFIT_CHECKIN", "LIVE_SESSION"],
            sortFields: [{ field: "date", order: SortOrder.DESC }],
            start: start,
            count: count
        })

        let nextUrl
        if (loggedActivities.activities.length < count) {
            nextUrl = undefined
        } else {
            nextUrl = `/page/widget?widgetId=${queryParams.widgetId}&start=${start + count}&lastActivityDate=${loggedActivities.activities[loggedActivities.activities.length - 1].date}&count=10`
        }

        const dates = this.getAllDatesInActivitiesWindow(userContext.userProfile.timezone, loggedActivities.activities)
        const dateToSleepInfo: { [date: string]: boolean } = {}
        dates.forEach(date => {
            dateToSleepInfo[date] = false
        })
        const userMapPromise: Promise<{ [userId: string]: User }> = interfaces.userCache.getUsers(allUserIds)
        const cultBookingIds: string[] = []
        const mindBookingIds: string[] = []
        loggedActivities.activities.forEach(activity => {
            if (activity.activityType === "CULT_CLASS")
                cultBookingIds.push(activity.meta.fulfilmentId)
            if (activity.activityType === "MIND_CLASS")
                mindBookingIds.push(activity.meta.fulfilmentId)
        })
        let cultBookingsPromise: Promise<BulkBookingResponse>, mindBookingsPromise: Promise<BulkBookingResponse>
        if (!_.isEmpty(cultBookingIds)) {
            cultBookingsPromise = interfaces.cultFitService.bulkBookings(allUserIds, undefined, undefined, cultBookingIds)
        }
        if (!_.isEmpty(mindBookingIds)) {
            mindBookingsPromise = interfaces.mindFitService.bulkBookings(allUserIds, undefined, undefined, mindBookingIds)
        }
        const cardListItemsPromises = loggedActivities.activities.map((loggedActivity: ActivityDS) => {
            switch (loggedActivity.activityType) {
                case "CULT_CLASS":
                    return this.cultOrMindClassToCardListItem(interfaces, userContext, loggedActivity, userMapPromise, loggedActivity.activityType, cultBookingsPromise)
                case "MIND_CLASS":
                    return this.cultOrMindClassToCardListItem(interfaces, userContext, loggedActivity, userMapPromise, loggedActivity.activityType, mindBookingsPromise)
                case "DIY_FITNESS":
                case "DIY_MEDITATION":
                    return this.diyItemToCardListItem(interfaces, userContext, loggedActivity)
                case "WALK":
                    return this.walkToCardListItem(interfaces, userContext, loggedActivity)
                case "SLEEP":
                    dateToSleepInfo[loggedActivity.date] = true
                    return this.sleepToCardListItem(interfaces, userContext, loggedActivity)
                case "CONSULTATION":
                case "CONSULTATION_PT":
                    return this.consultationToCardListItem(interfaces, userContext, loggedActivity)
                case "AT_HOME_SAMPLE_COLLECTION":
                case "IN_CENTRE_VISIT_FOR_TEST":
                    return this.diagnosticsToCardListItem(interfaces, userContext, loggedActivity)
                case "GYMFIT_CHECKIN":
                    return this.gymfitCheckinToCardListItem(interfaces, userContext, loggedActivity)
                case "LIVE_SESSION":
                    return this.liveSessionToCardListItem(interfaces, userContext, loggedActivity, user)

            }
            return undefined
        })

        // To add log sleep action  if not logged already. There might be a chance of showing log
        // sleep if the sleep activity data slipped to next page. There is no clean fix as of now
        const cardListItems: CardListItemWithTimestamp[] = await Promise.all(cardListItemsPromises)
        Object.keys(dateToSleepInfo).forEach(date => {
            if (!dateToSleepInfo[date] && date !== lastActivityDate) {
                const logSleepAction = this.getLogSleepAction(date, userContext.userProfile.timezone)
                if (!AppUtil.isInternationalApp(userContext) && logSleepAction) {
                    cardListItems.push(logSleepAction)
                }
            }
        })

        const filteredItems = _.filter(cardListItems, cardListItem => !_.isNil(cardListItem))
        if (!_.isEmpty(filteredItems)) {
            const sortedItems = TimelineHelper.groupUpcomingOrCompletedItems(userContext.userProfile.timezone, filteredItems, true, lastActivityDate)
            return new CardListWidgetView("Completed", "COMPLETED_ACTIVITES", sortedItems, undefined, nextUrl).buildView(interfaces, userContext, queryParams)
        }
    }

    private getAllDatesInActivitiesWindow(tz: Timezone, activities: ActivityDS[]): string[] {
        if (activities.length === 0) {
            return []
        } else {
            return TimeUtil.datesBetween(tz, activities[activities.length - 1].date, activities[0].date)
        }
    }

    private getLogSleepAction(date: string, tz: Timezone): CardListItemWithTimestamp {
        const yesterday: string = TimeUtil.getDaysFrom(tz, date, 2, true)[1]
        const sleepStartTime: number = TimeUtil.getDate(yesterday, 23, 0, tz).getTime()
        const sleepEndTime: number = TimeUtil.getDate(date, 7, 0, tz).getTime()
        if (new Date().getTime() >= sleepEndTime) {
            return {
                title: "Log your sleep",
                subTitle: "Goal 7 - 9 hrs",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getLogSleepUrl(date, sleepStartTime, sleepEndTime)
                },
                rightInfo: undefined,
                date: date,
                timestamp: sleepEndTime,
                leftInfo: {
                    title: "SLEEP"
                }
            } as CardListItemWithTimestamp
        }
    }

    private async cultOrMindClassToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                                activity: ActivityDS, userMapPromise: Promise<{ [userId: string]: User }>, activityType: ActivityType,
                                                bookingsResponsePromise: Promise<BulkBookingResponse>): Promise<CardListItemWithTimestamp> {
        try {
            const bookingsResponseMap: BulkBookingResponse = await bookingsResponsePromise
            const cultbooking: CultBooking = bookingsResponseMap[activity.userId].bookings.find(booking => booking.id.toString() === activity.meta.fulfilmentId.toString())
            const productType: ProductType = activityType === "CULT_CLASS" ? "FITNESS" : "MIND"
            const activityTitle = activityType === "CULT_CLASS" ? "CULT" : "MIND"
            const status = TimelineUtil.getCultStatus(cultbooking)
            const userMap: { [userId: string]: User } = await userMapPromise
            let subUserDetail
            if (activity.userId != userContext.userProfile.userId) {
                subUserDetail = userMap[activity.userId]
            }
            if (cultbooking.label !== "Cancelled" && cultbooking.label !== "Dropped out") {
                const classStartDate = TimelineHelper.getClassStartDate(userContext, cultbooking)
                return {
                    title: TimelineHelper.buildCultTitle(userContext, cultbooking, subUserDetail),
                    subTitle: TimelineHelper.buildCultSubTitle(userContext, classStartDate, cultbooking),
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getCultMindClassUrl(productType, cultbooking.bookingNumber)
                    },
                    rightInfo: status === "DONE" ? {
                        icon: "LOG",
                        action: undefined,
                        moreAction: undefined
                    } : undefined,
                    leftInfo: {
                        title: activityTitle
                    },
                    date: activity.date,
                    timestamp: activity.scoreTime
                } as CardListItemWithTimestamp
            }
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private async gymfitCheckinToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext, activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        try {
            const checkin: GymfitCheckIn = await interfaces.gymfitService.getCheckinById(parseInt(activity.clientActivityId), activity.userId)
            return {
                title: checkin.event.baseEvent.center.name + ", " + checkin.event.baseEvent.center.locality,
                subTitle: TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, checkin.startTime, "h:mm A") + " - " + TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, checkin.endTime, "h:mm A"),
                action: {
                    actionType: "NAVIGATION",
                    url: interfaces.gymfitBusiness.getCheckInActionUrl(userContext, checkin.event.baseEvent.center, checkin.id)
                },
                rightInfo: checkin.state === GymfitCheckInState.VALIDATED ? {
                    icon: "LOG",
                    action: undefined,
                    moreAction: undefined
                } : undefined,
                leftInfo: {
                    title: activity.activityName
                },
                date: activity.date,
                timestamp: activity.scoreTime
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private async liveSessionToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext, activity: ActivityDS, user: CommonUser): Promise<CardListItemWithTimestamp> {
        if (!AppUtil.isLiveSessionReportSupported(userContext, user)) {
            return undefined
        }
        try {
            const digitalCatalogue = await interfaces.diyService.getDigitalCatalogueEntry(activity.meta.contentId)
            return {
                title: digitalCatalogue.title,
                subTitle: "by " + digitalCatalogue.trainerName,
                action: !LiveUtil.isVanillaLiveFitFormat(digitalCatalogue.format) && digitalCatalogue.format !== "MIND_PODCAST" ?
                    {
                        actionType: "NAVIGATION",
                        url: `curefit://livesessionreport?contentId=${activity.meta.contentId}&pageFrom=completedActivitiesWidget`
                    } : undefined,
                rightInfo: activity.status === "DONE" ? {
                    icon: "LOG",
                    action: undefined,
                    moreAction: undefined
                } : undefined,
                leftInfo: {
                    title: activity.activityName
                },
                date: activity.date,
                timestamp: activity.scoreTime
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private async diyItemToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                        activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        try {
            const isDIYFitness = activity.activityType === "DIY_FITNESS"
            const diyProduct = <DIYProduct>(isDIYFitness ? await interfaces.diyService.getDIYFitnessProductsByProductIds(userContext.userProfile.userId, [activity.productId], AppUtil.getTenantFromUserContext(userContext))
                : await interfaces.diyService.getDIYMeditationProductsByProductIds(userContext.userProfile.userId, [activity.productId], AppUtil.getTenantFromUserContext(userContext)))[0]
            let diyPack
            if (!_.isEmpty(activity.meta.packId)) {
                diyPack = <DIYPack>await interfaces.catalogueService.getProduct(activity.meta.packId)
            }
            const sessionIndex = diyPack ? diyPack.sessionIds.indexOf(activity.productId) + 1 : 0
            const activityTitle = activity.activityType === "DIY_FITNESS" ? "WORKOUT" : "MEDITATE"

            let url
            if (activity.meta && activity.activityType !== "DIY_MEDITATION" && "v2" === activity.meta.activityVersion) {
                url = "curefit://livesessionreport?contentId=" + activity.productId + "&pageFrom=completedActivitiesWidget&userSessionId=" + activity.meta.userSessionId + "&contentType=" + activity.activityType
            } else if (activity.meta && activity.meta.fulfilmentId) {
                url = "curefit://diyreportspage?url=/digital/diySessionSummary?fulfilmentId=" + activity.meta.fulfilmentId
            } else if (isDIYFitness) {
                url = ActionUtil.getDIYSessionDetailUrl(userContext, diyProduct.productId, undefined, diyProduct.format)
            } else if (activity.meta && !_.isEmpty(activity.meta.packId)) {
                url = ActionUtilV1.diyPackProductPage(diyPack, userContext.sessionInfo.userAgent)
            }
            return {
                title: diyPack ? diyPack.title : diyProduct.title,
                subTitle: diyPack ? `Session ${sessionIndex}` : diyProduct.trainerName ? `by ${diyProduct.trainerName}` : "",
                action: {
                    actionType: "NAVIGATION",
                    url: url,
                },
                rightInfo: {
                    icon: "LOG",
                    action: undefined,
                    moreAction: undefined
                },
                leftInfo: {
                    title: activityTitle
                },
                date: activity.date,
                timestamp: activity.scoreTime
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("activity: " + JSON.stringify(activity), e)
        }
    }

    private async walkToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                     activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        try {
            const walkActivity: WalkActivity = TimelineUtil.createStepsAtlasActivity(activity)

            const tz = userContext.userProfile.timezone
            if ((TimeUtil.diffInDaysReal(tz, activity.date, TimeUtil.todaysDate(tz)) === 0) && (walkActivity.steps.done < walkActivity.steps.goal)) {
                return undefined
            }

            const rightData: CardListItemRightInfo = {
                icon: "LOG",
                action: undefined,
                moreAction: undefined
            }
            return {
                title: walkActivity.steps.done + " Steps",
                subTitle: "Goal 10k Steps",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getStepsUrl(walkActivity)
                },
                rightInfo: walkActivity.status === "DONE" ? rightData : undefined,
                date: activity.date,
                timestamp: activity.scoreTime,
                leftInfo: {
                    title: "STEPS"
                }
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private async sleepToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                      activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        if (AppUtil.isInternationalApp(userContext)) {
            return undefined
        }
        try {
            const isReviewed: boolean = _.get(activity, "meta.sleep.sourceType") === "USER" ? true : false
            const sleepActivity: SleepActivity = TimelineUtil.createSleepAtlasActivity(activity, isReviewed)
            const rightData: CardListItemRightInfo = {
                icon: "LOG",
                action: undefined,
                moreAction: undefined
            }
            return {
                title: this.sleepDisplayText(sleepActivity.duration),
                subTitle: "Goal 7 - 9 hrs",
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getSleepUrl(sleepActivity, userContext)
                },
                rightInfo: sleepActivity.status === "DONE" ? rightData : undefined,
                date: activity.date,
                timestamp: activity.scoreTime,
                leftInfo: {
                    title: "SLEEP"
                }
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private async diagnosticsToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                            activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        try {
            const tz = userContext.userProfile.timezone
            const bookingId: number = Number(activity.clientActivityId.split("-")[0])
            const bookingDetail: BookingDetail = await interfaces.healthfaceService.getBookingDetail(bookingId)
            const isMissed: boolean = !_.isEmpty(bookingDetail.consultationOrderResponse) && bookingDetail.consultationOrderResponse.status === "MISSED"
            const status: ActivityState = this.getCareStatus(bookingDetail, "DIAGNOSTICS", undefined, isMissed)

            const action: Action = await this.getCareActivityAction(userContext, "DIAGNOSTICS", bookingDetail, interfaces.healthfaceService, interfaces.logger, interfaces.catalogueService)
            // Do not remove - Fixing Timeline Subtitle for home + center info with subcategory or wrong info will be shown in timeline
            const subCategory: SUB_CATEGORY_CODE = activity.activityType === "AT_HOME_SAMPLE_COLLECTION" ? "AT_HOME_SLOT" : "IN_CENTRE_SLOT"
            return {
                title: activity.activityType === "AT_HOME_SAMPLE_COLLECTION" ? "At Home Test" : "At Center Test",
                subTitle: TimelineHelper.getSubTitle(userContext, bookingDetail, subCategory),
                action: action,
                rightInfo: status === "DONE" ? {
                    icon: "LOG",
                    action: undefined,
                    moreAction: undefined
                } : undefined,
                date: activity.date,
                timestamp: activity.scoreTime,
                leftInfo: {
                    title: "CARE"
                }
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }
    private async consultationToCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext,
                                             activity: ActivityDS): Promise<CardListItemWithTimestamp> {
        try {
            const tz = userContext.userProfile.timezone
            const bookingId: number = Number(activity.clientActivityId.split("-")[0])
            const appointmentId: number = Number(activity.clientActivityId.split("-")[1])
            const baseService = activity.activityType === "CONSULTATION_PT" ? interfaces.cultPTService : interfaces.healthfaceService
            const bookingDetail: BookingDetail = await baseService.getBookingDetail(bookingId, appointmentId)
            const product: ConsultationProduct = <ConsultationProduct>await interfaces.catalogueService.getProduct(bookingDetail.booking.productCode)
            const title: string = product.timelineTitle
            const isMissed: boolean = !_.isEmpty(bookingDetail.consultationOrderResponse) && bookingDetail.consultationOrderResponse.status === "MISSED"
            const status: ActivityState = this.getCareStatus(bookingDetail, "CONSULTATION",
                bookingDetail.consultationOrderResponse.consultationUserState, isMissed)

            const action = await this.getCareActivityAction(userContext, "CONSULTATION", bookingDetail, interfaces.healthfaceService, interfaces.logger, interfaces.catalogueService)
            return {
                title: title,
                subTitle: TimelineHelper.getSubTitle(userContext, bookingDetail),
                action: action,
                rightInfo: status === "DONE" ? {
                    icon: "LOG",
                    action: undefined,
                    moreAction: undefined
                } : undefined,
                date: activity.date,
                timestamp: activity.scoreTime,
                leftInfo: {
                    title: activity.activityType === "CONSULTATION_PT" ? "CULT" : "CARE"
                }
            } as CardListItemWithTimestamp
        } catch (e) {
            interfaces.logger.error("Error " + e.stack)
        }
    }

    private getCareStatus(bookingInfo: BookingDetail, activityType: ActivityType, userActivityStatus: string, isMissed: boolean): ActivityState {
        const status: ActivityState = "TODO"
        if (activityType !== "CONSULTATION" && userActivityStatus) {
            switch (userActivityStatus) {
                case "DONE": return "DONE"
                case "MISSED": return "SKIPPED"
                case "SCHEDULED":
                case "NONE": return "TODO"
            }
        }
        if (isMissed === true) {
            return "SKIPPED"
        } else {
            if (CareUtil.isComplteted(bookingInfo))
                return "DONE"
            return "TODO"
        }
    }
    private async getCareActivityAction(userContext: UserContext, activityType: ActivityType, bookingInfo: BookingDetail, healthfaceService: IHealthfaceService, logger: ILogger, catalogueService: ICatalogueService): Promise<Action> {
        const productCode = bookingInfo.booking.productCode

        switch (activityType) {
            case "CONSULTATION":
                const consultationProduct = <ConsultationProduct>await catalogueService.getProduct(productCode)
                const vertical = CareUtil.getVerticalForConsultation(consultationProduct.doctorType)

                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: BaseActionUtil.teleconsultationSingle(userContext, productCode, consultationProduct.urlPath, bookingInfo.booking.id.toString(), undefined, vertical)
                }
            case "DIAGNOSTICS":
                if (bookingInfo.booking.rootBookingId !== -1) {
                    const rootBooking = await healthfaceService.getBookingDetail(bookingInfo.booking.rootBookingId)
                    if (rootBooking.booking.categoryCode === "BUNDLE") {
                        return {
                            title: "VIEW",
                            actionType: "NAVIGATION",
                            url: BaseActionUtil.carefitbundle(productCode, rootBooking.booking.subCategoryCode, rootBooking.booking.id.toString())
                        }
                    }
                }
                return {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: BaseActionUtil.diagnostics(productCode, bookingInfo.booking.id.toString())
                }
            default: logger.error(`getCareActivityAction not handled for ${activityType}`)
        }
    }

    private sleepDisplayText(durationInSeconds: number): string {
        const minutes = durationInSeconds / 60
        const displayHours = Math.floor(minutes / 60)
        const displayMins = minutes % 60
        if (displayMins === 0) {
            return `${displayHours} hrs`
        } else {
            return `${displayHours} hrs ${Math.round(displayMins)} min`
        }
    }
}
