import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TimeUtil } from "@curefit/util-common"
import { CardListWidgetView, CardListItem } from "../card/CardListWidget"
import * as _ from "lodash"
import { FitnessReportWidget } from "@curefit/vm-models"
import { CultBooking } from "@curefit/cult-common"
import * as momentTz from "moment-timezone"
import { Action } from "../../../../common/views/WidgetView"
import CultUtil from "../../../../util/CultUtil"

export class FitnessReportWidgetView extends FitnessReportWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const pageNumber: any = 1
        const pageSize: any = 20
        const userId = userContext.userProfile.userId
        const cultBulkBookingResponse = await interfaces.cultFitService.getPulseBookingsByPage(userId, pageNumber, pageSize)
        const cultBookingResponse = cultBulkBookingResponse[userId]
        if (!_.isEmpty(cultBookingResponse.bookings)) {
            const cardListItems = _.map(cultBookingResponse.bookings, booking => {
                return this.cultBookingToCardListItem(userContext, booking)
            })
            const filteredItems = _.filter(cardListItems, cardListItem => !_.isNil(cardListItem))
            if (!_.isEmpty(filteredItems)) {
                return new CardListWidgetView("Fitness Report", "FITNESS_REPORTS", filteredItems).buildView(interfaces, userContext, queryParams)
            }
        }
    }

    private cultBookingToCardListItem(userContext: UserContext, booking: CultBooking): CardListItem {
        const tz = userContext.userProfile.timezone
        const hour: string = momentTz.tz(booking.Class.startTime, "HH:mm:ss", tz).format("HH")
        const minute: string = momentTz.tz(booking.Class.startTime, "HH:mm:ss", tz).format("mm")
        const classDate = TimeUtil.getDate(booking.Class.date, +hour, +minute, tz)
        if (TimeUtil.diffInDays(tz, booking.Class.date, TimeUtil.todaysDateWithTimezone(tz)) > 7) {
            return undefined
        }
        const pulseReportPageAction: Action = {
            "actionType": "NAVIGATION",
            "title": "VIEW",
            url: `curefit://pulsereport?bookingId=${booking.id}`
        }
        const cardListItem: CardListItem = {
            title: CultUtil.pulsifyClassName(booking.CultClass, userContext), //  only pulse class comes in report
            subTitle: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, classDate, "ddd, Ha") + ", " + booking.Center.name,
            leftInfo: {
                title: TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, classDate, "MMM Do")
            },
            action: pulseReportPageAction,
            rightInfo: {
                action: pulseReportPageAction,
                moreAction: undefined
            }
        }
        return cardListItem
    }
}