import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget, BaseDataItem } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TryOutWidget } from "@curefit/vm-models"
import * as _ from "lodash"
import { CardVideoWidget } from "@curefit/vm-models"
import { HeaderTitleWidget, TryOutItem } from "@curefit/vm-models"
import { ProductType } from "@curefit/product-common"
import { City } from "@curefit/location-common"

const TRY_OUT_PRODUCT_TYPES: ProductType[] = ["FITNESS", "MIND", "FOOD"]
export class TryOutWidgetView extends TryOutWidget {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [name: string]: string }): Promise<IBaseWidget | IBaseWidget[]> {
        const tryoutPromises = _.map(this.data, async tryOutItem => {
            if (tryOutItem.segmentIds) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(tryOutItem.segmentIds, userContext)
                if (segment) {
                    return tryOutItem
                }
            } else {
                return tryOutItem
            }
        })
        const tryOutProductTypesForCity = _.intersection(TRY_OUT_PRODUCT_TYPES, userContext.userProfile.city.availableOfferings)
        const tryOutItems = (await Promise.all(tryoutPromises)).filter(tryOutItem => !_.isNil(tryOutItem) &&
            !this.checkForTryOutItemTTL(userContext, tryOutItem, queryParams).isExpired)
        if (!_.isEmpty(tryOutItems)) {
            const tryOutCardVideoWidgets = _.map(tryOutItems, tryOutItem => {
                tryOutItem.videoUri = "curefit-content/" + tryOutItem.videoUri
                tryOutItem.thumbnailVideoUri = "curefit-content/" + tryOutItem.thumbnailVideoUri
                return new CardVideoWidget(tryOutItem)
            })
            const widgets = []
            // const title = tryOutProductTypesForCity.length === tryOutItems.length ? "Get started" : "You may like"
            // widgets.push(new HeaderTitleWidget(title))
            widgets.push(...tryOutCardVideoWidgets)
            return widgets
        }

    }



    private checkForTryOutItemTTL(userContext: UserContext, tryOutItem: TryOutItem, queryParams: { [name: string]: string }): {
        isExpired: boolean,
        isSessionUpdated: boolean
    } {
        const widgetId = queryParams.widgetId
        if (tryOutItem.ttlFromFirstDisplayTime) {
            const widget = _.find(userContext.sessionInfo.sessionData.widgetStore, widget => {
                return widget.widgetId === widgetId && widget.meta.productType === tryOutItem.productType
            })
            if (widget) {
                return {
                    isExpired: Date.now() - widget.meta.widgetFirstShownTimeInMillis > tryOutItem.ttlFromFirstDisplayTime ? true : false, // compute
                    isSessionUpdated: false
                }
            } else {
                const widgetMeta = {
                    widgetId: widgetId,
                    meta: {
                        widgetFirstShownTimeInMillis: Date.now(),
                        productType: tryOutItem.productType
                    }
                }
                if (userContext.sessionInfo.sessionData.widgetStore) {
                    userContext.sessionInfo.sessionData.widgetStore.push(widgetMeta)
                } else {
                    userContext.sessionInfo.sessionData.widgetStore = [widgetMeta]
                }
                userContext.sessionInfo.isSessionUpdateNeeded = true
                return {
                    isExpired: false,
                    isSessionUpdated: true
                }
            }
        } else {
            return {
                isExpired: false,
                isSessionUpdated: false
            }
        }
    }

}
