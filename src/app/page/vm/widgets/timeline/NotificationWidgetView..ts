import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { NotificationWidget } from "@curefit/vm-models"
import { INotification } from "../../../../common/views/NotificationWidget"
import { InAppNotificationData, NotificationMeta } from "@curefit/iris-common"
import * as _ from "lodash"
import { InAppNotificationsService, IRIS_CLIENT_TYPES } from "@curefit/iris-client"
import { inject } from "inversify"
import { INAPP_NOTIFICATION_APPID } from "../../../../util/AppUtil"


export class NotificationWidgetView extends NotificationWidget {

    items: INotification[]

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const serverNotifications = await this.getServerNotifications(userContext.userProfile.userId, interfaces.inAppNotificationsService)
        if (!_.isEmpty(serverNotifications)) {
            this.items = serverNotifications
            return this
        }
    }


    private async getServerNotifications(userId: string, inAppNotificationService: InAppNotificationsService): Promise<INotification[]> {
        let inAppNotifications = []
        try {
            inAppNotifications = await inAppNotificationService.getActiveInAppNotificationsForUser(userId, INAPP_NOTIFICATION_APPID)
        } catch (e) {
            return
        }
        const serverNotifications = _.map(inAppNotifications, (inAppNotification) => {
            const inAppNotificationData: InAppNotificationData = JSON.parse(inAppNotification.dataBlob)
            const notification: INotification = {
                title: inAppNotificationData.title,
                action: {
                    actionType: "NAVIGATION",
                    url: inAppNotificationData.action.url,
                    title: inAppNotificationData.action.title
                },
                closeAction: {
                    actionType: "REST_API",
                    meta: {
                        method: "POST",
                        url: "/user/inAppNotification/" + inAppNotification.notificationId,
                        body: { "state": "CLOSED" }
                    }
                },
                notificationId: inAppNotification.notificationId,
                notificationType: inAppNotification.type,
                image: inAppNotificationData.image,
                textColor: inAppNotificationData.textColor,
                backgroundColor: inAppNotificationData.backgroundColor
            }
            return notification
        })
        return serverNotifications
    }
}