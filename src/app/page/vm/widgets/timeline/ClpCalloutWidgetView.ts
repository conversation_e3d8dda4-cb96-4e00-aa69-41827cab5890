import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget, CalloutWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import * as _ from "lodash"
import { AnnouncementDetails } from "../../../../announcement/AnnouncementViewBuilder"
import CultUtil from "../../../../util/CultUtil"

export class ClpCalloutWidgetView extends CalloutWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [name: string]: string }): Promise<IBaseWidget> {
        if (this.type === "FITNESS_REPORT") {
            const { lastWeekStartDateFormated } = CultUtil.getLastWeekStartDate(userContext)
            const announcementDetails = <AnnouncementDetails>await interfaces.announcementBusiness.getReportGeneratedAnnouncement(userContext.userProfile.userId, lastWeekStartDateFormated)
            if (announcementDetails && announcementDetails.state === "CREATED") {
                this.action = {
                    actionType: "NAVIGATION",
                    url: (process.env.ENVIRONMENT === "STAGE" || process.env.ENVIRONMENT === "LOCAL") ? "curefit://tabpage?pageId=cult&widgetId=4cace466-8f72-4a49-934c-16457ef13d11" : "curefit://tabpage?pageId=cult&widgetId=d04c5d88-f54e-468f-9ef0-b09b519889ad"
                }
                this.dismissAction = {
                    actionType: "REST_API",
                    title: "DISMISS",
                    meta: {
                        method: "POST",
                        url: `/user/announcement/${announcementDetails.announcementId}`,
                        body: { "state": "DISMISSED" }
                    }
                }
                this.contentMetric = {
                    lastWeekStartDate: lastWeekStartDateFormated
                }
                return this
            }
            return null
        }
    }
}
