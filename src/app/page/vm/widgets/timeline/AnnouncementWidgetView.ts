import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget, CardVideoWidget, CardVideoItem, BaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { AnnouncementWidget } from "@curefit/vm-models"
import { AnnouncementDetails } from "../../../../announcement/AnnouncementViewBuilder"
import AppUtil from "../../../../util/AppUtil"
import CultUtil, { FIRST_CLASS_PRE_WORKOUT_VIDEO, FIRST_CLASS_POST_WORKOUT_VIDEO, FIRST_CLASS_PRE_WORKOUT_THUMBNAIL, FIRST_CLASS_POST_WORKOUT_THUMBNAIL } from "../../../../util/CultUtil"

export class AnnouncementWidgetView extends AnnouncementWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [name: string]: string }): Promise<IBaseWidget> {
        const cultAndMindSummary = await interfaces.userCache.getCultSummary(userContext.userProfile.userId)
        const userId = userContext.userProfile.userId
        let title = "", subTitle = "", navigationUrl = "", meta = {}, showAnnouncement = false, videoUri = "", image = ""
        const bookingSummary = cultAndMindSummary && cultAndMindSummary.bookingSummary
        let firstClassTips = null
        if ((bookingSummary && bookingSummary.cult.completedCount === 0 && bookingSummary.cult.bookedCount > 0) || !bookingSummary) {
            firstClassTips = await interfaces.cultFitService.getPreWorkoutFirstClassTips(userId, "CUREFIT_APP")
        }
        const currentUser = await interfaces.userCache.getUser(userContext.userProfile.userId)
        if (AppUtil.isNUXSupported(userContext, currentUser.isInternalUser, "FITNESS", interfaces.hamletBusiness) && bookingSummary && bookingSummary.cult.bookedCount >= 1) {
            if (firstClassTips && firstClassTips.booking) {
                title = "Get ready for your first class"
                subTitle = "Everything you need to know."
                navigationUrl = "curefit://classworkouttips"
                meta = { preWorkoutTips: true }
                showAnnouncement = true
                image = FIRST_CLASS_PRE_WORKOUT_THUMBNAIL
                videoUri = FIRST_CLASS_PRE_WORKOUT_VIDEO
            } else if (AppUtil.isNUXSupported(userContext, currentUser.isInternalUser, "FITNESS", interfaces.hamletBusiness) && bookingSummary && bookingSummary.cult.completedCount === 1) {
                const announcementDetails = <AnnouncementDetails>await interfaces.announcementBusiness.getNuxFirstClassCompletedAnnouncement(userContext.userProfile.userId, "FIRST_CLASS_COMPLETED")
                if (!announcementDetails) {
                    return null
                }
                title = "Recovery tips after your first class"
                subTitle = "Everything you need to know."
                navigationUrl = "curefit://classworkouttips"
                meta = { postWorkoutTips: true }
                showAnnouncement = true
                image = FIRST_CLASS_POST_WORKOUT_THUMBNAIL
                videoUri = FIRST_CLASS_POST_WORKOUT_VIDEO
            }
        }
        if (showAnnouncement) {
            const videoWidgetItem: CardVideoItem = {
                image: image,
                videoUri: videoUri,
                thumbnailVideoUri: "",
                displayPlayIcon: true,
                footer: {
                    title: title,
                    subTitle: subTitle,
                    rightText: "READ",
                    action: {
                        actionType: "NAVIGATION",
                        url: navigationUrl,
                        meta: meta
                    }
                }
            }
            return new CardVideoWidget(videoWidgetItem)
        } else {
            return null
        }
    }
}
