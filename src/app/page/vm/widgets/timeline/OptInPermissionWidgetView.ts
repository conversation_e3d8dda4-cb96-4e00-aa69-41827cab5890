import { IBaseWidget, OptInPermissionWidget } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import * as _ from "lodash"
import { WhatsappEnum } from "@curefit/user-common"
import { PermissionWidgetModel } from "@curefit/apps-common/dist/src/widgets/vm/cult"
import { Action } from "@curefit/apps-common/dist/src/actions/actions"


export class OptInPermissionWidgetView extends OptInPermissionWidget {

    title: string
    iconUrl: string
    message: string
    action: Action
    private closeAction?: Action

    constructor(title: string, message: string, iconUrl: string, action?: Action, closeAction?: Action) {
        super()
        this.widgetType = "OPT_IN_PERMISSION_WIDGET"

        // If any of mandatory values passed are undefined, resort to default values.
        if (_.isEmpty(title) || _.isEmpty(message) || _.isEmpty(iconUrl)) {
            this.setOptinWidget()
        } else {
            this.title = title
            this.message = message
            this.iconUrl = iconUrl
        }

        this.action = action ? action : this.getOptInAction()
        this.closeAction = closeAction ? closeAction : this.getCloseAction()
    }

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        try {
            const userId = userContext.userProfile.userId
            if (!userId || _.isEmpty(userId)) return undefined
            const state = await interfaces.userService.getUserWhatsappState(userId)

            if (!state || state.length < 1 || !state[0])
                return undefined

            // check if user is already opted in
            // 1. If already opted in, return undefined.
            // 2. If pending, send the widget.
            // 3. If opted out, send undefined.

            switch (parseInt(state[0].attrValue)) {
                case WhatsappEnum.APPROVED:
                    return undefined
                case WhatsappEnum.PENDING:
                    return this
                case WhatsappEnum.DENIED:
                    return undefined
            }

            return undefined

        } catch (e) {
            interfaces.logger.error(`Error while showing Opt-In Permission widget`, e)
        }

    }


    private setOptinWidget() {
        this.title = "Would you like to get your weekly fitness report on WhatsApp?"
        this.iconUrl = "https://cdn-images.cure.fit/www-curefit-com/image/upload/image/optin/whatsapp_icon.png"
        this.message = "Don't worry we will not spam and you can always opt out."
    }


    public getOptInAction(): Action {
        return {
            actionType: "OPT_IN",
            title: "OPT IN",
            url: "/user/whatsappOptIn"
        }
    }

    public getCloseAction(): Action {
        return {
            actionType: "OPT_OUT",
            url: "/user/whatsappOptOut"
        }
    }

}
