import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TimeUtil } from "@curefit/util-common"
import { CardListWidgetView, CardListItem } from "../card/CardListWidget"
import * as _ from "lodash"
import { CultBooking } from "@curefit/cult-common"
import * as momentTz from "moment-timezone"
import { Action } from "../../../../common/views/WidgetView"
import { MedicalReportWidget } from "@curefit/vm-models"
import { DiagnosticsTestOrderResponse } from "@curefit/albus-client"
import { ActionUtil } from "@curefit/base-utils"
import CollectionUtil from "../../../../util/CollectionUtil"

export class MedicalReportWidgetView extends MedicalReportWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const pageNumber: any = 1
        const pageSize: any = 20
        const userId = userContext.userProfile.userId
        let diagnostics: DiagnosticsTestOrderResponse[] = CollectionUtil.getNullSafeList(await interfaces.healthfaceService.getDiagnosticTests(userId, pageNumber, pageSize))
        if (_.isEmpty(diagnostics)) {
            return undefined
        }
        diagnostics.filter(diagnostic => { })
        const diagnosticPromises = _.map(diagnostics, async diagnostic => {
            diagnostic.firstProductName = (await interfaces.catalogueService.getProduct(diagnostic.productCodes[0])).title
            return diagnostic
        })
        diagnostics = await Promise.all(diagnosticPromises)
        if (!_.isEmpty(diagnostics)) {
            const cardListItems = _.map(diagnostics, diagnostic => {
                return this.diagnosticToCardListItem(userContext, diagnostic)
            })
            const filteredItems = _.filter(cardListItems, cardListItem => !_.isNil(cardListItem))
            if (!_.isEmpty(filteredItems)) {
                return new CardListWidgetView("Diagnostics Report", "MEDICAL_REPORTS", filteredItems).buildView(interfaces, userContext, queryParams)
            }
        }
    }

    private diagnosticToCardListItem(userContext: UserContext, diagnosticTest: DiagnosticsTestOrderResponse): CardListItem {
        const diagnosticsAction: Action = {
            title: "VIEW",
            actionType: "NAVIGATION",
            url: ActionUtil.diagnostics("DIAGNOSTIC_TEST", diagnosticTest.bookingId.toString())
        }
        const tz = userContext.userProfile.timezone
        const diagnosticTestDate = this.diagnosticTestDate(diagnosticTest, userContext, "YYYY-MM-DD")
        if (diagnosticTestDate && TimeUtil.diffInDays(tz, diagnosticTestDate, TimeUtil.todaysDateWithTimezone(tz)) > 7) {
            return undefined
        }
        let title = diagnosticTest.firstProductName
        if (diagnosticTest.productCodes.length > 1) {
            title += ` | +${diagnosticTest.productCodes.length - 1} TESTS`
        }
        const cardListItem: CardListItem = {
            title: title,
            subTitle: `For ${diagnosticTest.patient.name}`,
            leftInfo: {
                title: this.diagnosticTestDate(diagnosticTest, userContext, "MMM D")
            },
            action: diagnosticsAction,
            rightInfo: {
                action: diagnosticsAction,
                moreAction: undefined
            }
        }
        return cardListItem
    }

    private diagnosticTestDate(diagnosticTest: DiagnosticsTestOrderResponse, userContext: UserContext, format: string): string {
        let date
        if (diagnosticTest.inCentreDiagnosticOrder &&
            diagnosticTest.inCentreDiagnosticOrder.slot &&
            diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime
        ) {
            date = diagnosticTest.inCentreDiagnosticOrder.slot.workingStartTime
        } else if (diagnosticTest.atHomeDiagnosticOrder &&
            diagnosticTest.atHomeDiagnosticOrder.startTime
        ) {
            date = diagnosticTest.atHomeDiagnosticOrder.startTime
        }
        if (date)
            return momentTz.tz(date, userContext.userProfile.timezone).format(format)

    }
}