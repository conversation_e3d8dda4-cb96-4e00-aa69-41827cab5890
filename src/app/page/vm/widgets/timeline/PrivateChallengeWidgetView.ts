import * as _ from "lodash"

import { IBaseWidget, PrivateChallengeWidget } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"

import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { SocialColors } from "../../../../common/views/Social"
import ActionUtil from "../../../../util/ActionUtil"
import ChallengesViewBuilder from "../../../../challenges/ChallengesViewBuilder"
import { CardListWidgetView } from "../card/CardListWidget"
import { EnrolmentResponse, EntityType, HomeWidgetConfig, InviteResponse } from "@curefit/riddler-common"
import * as mustache from "mustache"

const AUTO_INVITE_CHALLENGES = ["cult_live_21_day_challenge"]

export class PrivateChallengeWidgetView extends PrivateChallengeWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        try {
            const [homepageResponse, {invites: autoInvites}] =
                await Promise.all([
                    interfaces.riddlerService.getActiveInvitesAndRecentEnrolments(userContext.userProfile.userId, true, false),
                    interfaces.riddlerService.getActiveInvites("AUTO", true)
                ])
            const enrolments = (homepageResponse as any).recentEnrolments || []
            const invites = homepageResponse.invites || []
            for (const autoInvite of autoInvites) {
                if (this.isAutoInviteApplicable(autoInvite, enrolments, invites)) {
                    invites.push(autoInvite)
                }
            }
            const [challengeInvitesView, activeChallengesView] = await Promise.all([
                this.getChallengeInvitesView(invites, userContext),
                this.getActiveChallengesView(enrolments, interfaces, userContext)
            ])
            const items: any[] = []
            items.push(...challengeInvitesView)
            items.push(...activeChallengesView)

            if (!_.isEmpty(items)) {
                items[items.length - 1].showDivider = false
            }
            const calloutWidget = this.getBanner(invites.concat(enrolments)[0])
            if (!_.isEmpty(items)) {
                const user = await userContext.userPromise
                return new CardListWidgetView(`Hey ${user.firstName}`, "CHALLENGES", items, undefined, undefined, calloutWidget).buildView(interfaces, userContext, queryParams)
            }
        } catch (e) {
            interfaces.logger.error(`Error while showing private challenge widget: ${e.message} ${JSON.stringify(e, ["stack"])}`)
            throw e
        }
    }

    private isAutoInviteApplicable(autoInvite: InviteResponse, recentEnrolments: EnrolmentResponse[], invites: InviteResponse[]) {
        if (autoInvite.status !== "ACTIVE") {
            return false
        }
        for (const enrolment of recentEnrolments) {
            if (enrolment.challengeId === autoInvite.challengeId) {
                return false
            }
        }
        for (const invite of invites) {
            if (invite.challengeId === autoInvite.challengeId) {
                return false
            }
        }
        return true
    }

    private getBanner(response: InviteResponse | EnrolmentResponse) {
        if (_.isNil(response)) {
            return undefined
        }
        const homeWidget = this.getHomeWidget(_.get(response as any, "enrolmentId") ? "ENROLMENT" : "INVITE", response)
        if (_.isNil(homeWidget) || _.isNil(homeWidget.homeBanner)) {
            return undefined
        }
        const gradient = homeWidget.homeBanner.bgColor && homeWidget.homeBanner.bgColor.split("`!`!`")
        return {
            title: homeWidget.homeBanner.title,
            imageUri: homeWidget.homeBanner.icon,
            linearGradientColors: gradient || ["#cf72f3", "#495ee3"],
            borderRadius: 0,
            containerStyle: {marginTop: 2, marginBottom: 2},
            sceneWidth: 100 - 3,
            padding: {
                paddingVertical: 8,
                paddingHorizontal: 18
            }
        }
    }

    async getChallengeInvitesView(invites: InviteResponse[], userContext: UserContext) {
        return await Promise.all(invites
            .filter(invite => invite.status === "ACTIVE")
            .map(async invite => {
                const {challenge} = invite
                const cardListItem: any = {
                    title: challenge.uiConfig.inviteText,
                    subTitle: invite.expiry ?
                        `Enter before ${TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, invite.expiry, "MMM DD, YYYY")}` :
                        "",
                    viewType: "CHALLENGE_INVITE",
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getChallengeDetailsUrl(challenge.challengeId, "INVITE", invite.inviteId)
                    },
                    rightInfo: {
                        action: {
                            actionType: "NAVIGATION",
                            url: ActionUtil.getChallengeDetailsUrl(challenge.challengeId, "INVITE", invite.inviteId),
                            title: "ENTER",
                            meta: {
                                textColor: SocialColors.reddishPink,
                                inviteId: invite.inviteId,
                                challengeId: invite.challengeId
                            }
                        },
                        moreAction: {
                            actionType: "ACTION_LIST",
                            actions: [
                                {
                                    actionType: "NAVIGATION",
                                    url: ActionUtil.getChallengeDetailsUrl(invite.challenge.challengeId, "INVITE", invite.inviteId),
                                    title: "View Details",
                                    meta: {
                                        textColor: SocialColors.purple,
                                        inviteId: invite.inviteId,
                                        challengeId: challenge.challengeId
                                    }
                                },
                                {
                                    title: "Not Interested",
                                    actionType: "SHOW_ALERT_MODAL",
                                    meta: {
                                        title: challenge.uiConfig.declineInviteMessage.title || "Don’t want to participate?",
                                        subTitle: challenge.uiConfig.declineInviteMessage.text,
                                        actions: [{
                                            actionType: "HIDE_ALERT_MODAL",
                                            title: "CANCEL"
                                        }, {
                                            actionType: "REJECT_INVITE",
                                            title: "I'M SURE",
                                            meta: {
                                                textColor: SocialColors.purple,
                                                inviteId: invite.inviteId,
                                                challengeId: invite.challengeId
                                            }
                                        }]
                                    }
                                }
                            ],
                            icon: "MANAGE"
                        },
                    },
                    leftInfo: {
                        images: [challenge.uiConfig.homeWidgetImageUrl]
                    },
                    showDivider: true
                }
                return cardListItem
            }))
    }

    async getActiveChallengesView(enrolments: EnrolmentResponse[], interfaces: CFServiceInterfaces, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        return await Promise.all(enrolments
            .filter(enrolment => !["FORCE_EXIT", "INACTIVE"].includes(enrolment.status))
            .map(async enrolment => {
                const homeWidget = this.getHomeWidget("ENROLMENT", enrolment)
                const vars = await ChallengesViewBuilder.getVariables(interfaces.expressionBuilderFactory, enrolment, userContext.userProfile.timezone)
                const progress = ChallengesViewBuilder._getProgress(tz, enrolment, "HOME_WIDGET", vars)
                const subtitle = (homeWidget && homeWidget.item && homeWidget.item.description) || ChallengesViewBuilder._getChallengesSubTitle(tz, ChallengesViewBuilder._getStartDate(enrolment, tz), enrolment.endDate)
                return {
                    title: mustache.render((homeWidget && homeWidget.item && homeWidget.item.title) || enrolment.challenge.title, vars),
                    subTitle: mustache.render(subtitle, vars),
                    viewType: "CHALLENGE_INVITE",
                    rightInfo: this.getEnrolmentRightInfo(enrolment, homeWidget),
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getChallengeDetailsUrl(enrolment.challengeId, "ENROLMENT", enrolment.enrolmentId)
                    },
                    progress: progress && {
                        ...progress,
                        style: {
                            paddingTop: 20
                        }
                    },
                    leftInfo: homeWidget && homeWidget.item && homeWidget.item.icon ? {
                        images: [homeWidget.item.icon]
                    } : undefined,
                    showDivider: true
                }
            }))
    }

    private getHomeWidget(refType: EntityType, refData: EnrolmentResponse | InviteResponse) {
        const {challenge} = refData
        if (_.isEmpty(challenge.uiConfig.homeWidgetConfigs)) {
            return undefined
        }
        return challenge.uiConfig.homeWidgetConfigs.find(config => config.refType === refType && config.statuses.includes(refData.status as any))
    }

    private getEnrolmentRightInfo(enrolment: EnrolmentResponse, homeWidget: HomeWidgetConfig) {
        switch (enrolment.status) {
            case "ACTIVE":
                return {
                    action: (homeWidget && homeWidget.action) || enrolment.challenge.uiConfig.enrolmentWidgetAction || {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getChallengeDetailsUrl(enrolment.challengeId, "ENROLMENT", enrolment.enrolmentId),
                        title: "VIEW",
                    },
                    meta: {
                        textColor: SocialColors.reddishPink,
                        enrolmentId: enrolment.enrolmentId,
                        challengeId: enrolment.challengeId
                    }
                }
            default:
                return {
                    action: (homeWidget && homeWidget.action) || {
                        actionType: "NAVIGATION",
                        url: ActionUtil.getChallengeDetailsUrl(enrolment.challengeId, "ENROLMENT", enrolment.enrolmentId),
                        title: "VIEW",
                    },
                    meta: {
                        textColor: SocialColors.reddishPink,
                        enrolmentId: enrolment.enrolmentId,
                        challengeId: enrolment.challengeId
                    }
                }

        }
    }
}
