import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TimeUtil } from "@curefit/util-common"
import { CardListWidgetView, CardListItem, CardListDataItemType, CardListWidgetFooter } from "../card/CardListWidget"
import * as _ from "lodash"
import { Action } from "../../../../common/views/WidgetView"
import { ActivityDS, ActivityTypeDS } from "@curefit/logging-common"
import { TrackYourHabitWidget } from "@curefit/vm-models"
import TimelineUtil from "../../../../util/TimelineUtil"
import ActionUtil from "../../../../util/ActionUtil"
import { SleepActivity, WalkActivity } from "@curefit/atlas-client"

export class TrackYourHabitWidgetView extends TrackYourHabitWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const today = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        const isStepsEnabled = queryParams["isStepsEnabled"]
        const isSleepEnabled = queryParams["isSleepEnabled"]
        const activityTypes: ActivityTypeDS[] = []
        let sleepActivity: ActivityDS = undefined
        let stepsActivity: ActivityDS = undefined
        if (isSleepEnabled) {
            activityTypes.push("SLEEP")
        }
        if (isStepsEnabled) {
            activityTypes.push("WALK")
        }
        if (!_.isEmpty(activityTypes)) {
            const loggedActivitiesPromise: Promise<{ activities: ActivityDS[] }> = interfaces.loggingService.getActivitiesFor({
                userId: [userContext.userProfile.userId],
                date: [today],
                show: [true],
                activityType: activityTypes
            })
            const result: { activities: ActivityDS[] } = await loggedActivitiesPromise
            sleepActivity = _.find(result.activities, loggedActivity => loggedActivity.activityType === "SLEEP")
            stepsActivity = _.find(result.activities, loggedActivity => loggedActivity.activityType === "WALK")
        }
        const sleepTrackCard = this.getSleepTrackCardItem(userContext, today, sleepActivity, isSleepEnabled && isSleepEnabled === "true")
        const stepsTrackCard = this.getStepsTrackCardItem(userContext, today, stepsActivity, isStepsEnabled && isStepsEnabled === "true")

        const footer: CardListWidgetFooter = {
            contentType: "MESSAGE_FOOTER",
            title: "Tip: Edit sleep details to give us the accurate hours"
        }
        return new CardListWidgetView("Track your habits", "TRACK_HABITS", [stepsTrackCard, sleepTrackCard], footer).buildView(interfaces, userContext, queryParams)

    }
    private getSleepTrackCardItem(userContext: UserContext, today: string, activity: ActivityDS,
        isSleepEnabled: boolean): CardListItem {
        let sleepAction: Action, sleepEnableAction: Action
        let title = "---", subTitle

        if (!isSleepEnabled) {
            sleepEnableAction = {
                title: "ENABLE",
                actionType: "ENABLE_SLEEP_PERMISSION"
            }
        } else {
            if (activity) {
                const isReviewed: boolean = _.get(activity, "meta.sleep.sourceType") === "USER" ? true : false
                const sleepActivity: SleepActivity = TimelineUtil.createSleepAtlasActivity(activity, isReviewed)
                sleepAction = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getSleepUrl(sleepActivity, userContext)
                }
                title = this.sleepDisplayText(sleepActivity.duration)
                title = title.replace(" Sleep", "")
                subTitle = "Today"
            } else {
                const tz = userContext.userProfile.timezone
                const yesterday = TimeUtil.getDaysFrom(tz, today, 2, true)[1]
                const sleepStartTime = TimeUtil.getDate(yesterday, 23, 0, tz).getTime()
                const sleepEndTime = TimeUtil.getDate(today, 7, 0, tz).getTime()
                sleepAction = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getLogSleepUrl(today, sleepStartTime, sleepEndTime)
                }
            }
        }


        const trackHabitItem: CardListItem = {
            title: "Daily Sleep",
            subTitle: "Goal  7 - 9 hr",
            leftInfo: {
                images: ["/image/permission/sleep_access.png"]
            },
            action: sleepAction,
            rightInfo: {
                title: title,
                subTitle: subTitle,
                action: sleepEnableAction,
                moreAction: undefined
            }
        }
        return trackHabitItem
    }

    private sleepDisplayText(durationInSeconds: number): string {
        const minutes = durationInSeconds / 60
        const displayHours = Math.floor(minutes / 60)
        const displayMins = minutes % 60
        if (displayMins === 0) {
            return `${displayHours} hrs Sleep`
        } else {
            return `${displayHours} hrs ${Math.round(displayMins)} min Sleep`
        }
    }

    private getStepsTrackCardItem(userContext: UserContext, today: string, activity: ActivityDS,
        isStepsEnabled: boolean): CardListItem {
        let title = "---", subTitle
        let stepsEnableAction: Action, stepsAction: Action

        if (!isStepsEnabled) {
            stepsEnableAction = {
                title: "ENABLE",
                actionType: "HEALTH_KIT_PERMISSION",
                meta: { "howitworks": ActionUtil.getStepsHowItWorks() }
            }

        }
        if (activity) {
            const walkActivity: WalkActivity = TimelineUtil.createStepsAtlasActivity(activity)
            title = walkActivity.steps.done + ""
            subTitle = "Today"
            stepsAction = {
                actionType: "NAVIGATION",
                url: ActionUtil.getStepsUrl(walkActivity)
            }
        }

        const trackHabitItem: CardListItem = {
            title: "Daily Steps",
            subTitle: "Goal  10000 steps",
            leftInfo: {
                images: ["/image/permission/steps_access.png"]
            },
            action: stepsAction,
            rightInfo: {
                title: title,
                subTitle: subTitle,
                action: stepsEnableAction,
                moreAction: undefined
            }
        }
        return trackHabitItem
    }
}
