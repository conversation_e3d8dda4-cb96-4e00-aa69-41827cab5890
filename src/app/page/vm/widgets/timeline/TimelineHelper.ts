import { SUB_CATEGORY_CODE } from "@curefit/care-common"
import { CultBooking, User } from "@curefit/cult-common"
import { UserContext } from "@curefit/vm-models"
import { TimeUtil, Timezone } from "@curefit/util-common"
import CultUtil from "../../../../util/CultUtil"
import * as momentTz from "moment-timezone"
import { FoodBooking } from "@curefit/alfred-client"
import { Product, UrlPathBuilder } from "@curefit/product-common"
import { FoodShipmentStatus, ListingBrandIdType, MealSlot, MenuType, OrderTrackingStatus } from "@curefit/eat-common"
import { SlotUtil } from "@curefit/eat-util"
import * as _ from "lodash"
import { DIYPack, DIYProduct } from "@curefit/diy-common"
import AtlasUtil from "../../../../util/AtlasUtil"
import { Action } from "../../../../common/views/WidgetView"
import { ActionUtil } from "@curefit/base-utils"
import { CardListItem, CardListItemWithTimestamp } from "../card/CardListWidget"
import { TimelineActivity, BookingDetail } from "@curefit/albus-client"
import EatUtil from "../../../../util/EatUtil"
import { tz } from "moment-timezone/moment-timezone"
import LiveUtil from "../../../../util/LiveUtil"

export class TimelineHelper {

    public static buildCultTitle(userContext: UserContext, booking: CultBooking, subUserDetail?: User): string {
        // Build title - class name + <?kids name>
        let title = CultUtil.pulsifyClassName(booking.CultClass, userContext)
        if (subUserDetail) {
            title = title + " for " + subUserDetail.firstName + " " +
                subUserDetail.lastName
        }
        return title
    }

    public static buildCultSubTitle(userContext: UserContext, classStartDate: Date, booking: CultBooking): string {
        // Build subTitle - time, center
        const tz = userContext.userProfile.timezone
        const startTime: string = TimeUtil.get12HRTimeFormat(classStartDate, tz)
        const subTitile = startTime + ", " + booking.Center.name
        return subTitile
    }

    public static getClassStartDate(userContext: UserContext, booking: CultBooking): Date {
        const tz = userContext.userProfile.timezone
        const hour: string = momentTz.tz(booking.Class.startTime, "HH:mm:ss", tz).format("HH")
        const minute: string = momentTz.tz(booking.Class.startTime, "HH:mm:ss", tz).format("mm")
        const dateString = booking.Class.date
        const dateTime = TimeUtil.getDate(dateString, +hour, +minute, userContext.userProfile.timezone)
        return dateTime
    }

    public static buildEatTitle(userContext: UserContext, booking: FoodBooking, product: Product, mealSlot: MenuType): string {
        // Assign title
        const isCafe: boolean = !_.isNil(booking.address) && (booking.address.kioskType === "CAFE")
        let title = !isCafe ? _.capitalize(mealSlot) : "Workout Snack"
        if (booking.listingBrand === "WHOLE_FIT") {
            title = "whole.fit"
        }
        else if (mealSlot === "ALL") {
            title = "Order"
        }
        if (booking.products.length > 1) {
            title = title + " - " + booking.products.length + " items ordered"
        } else {
            title = title + " - " + product.title
        }
        return title
    }

    public static buildEatSubTitle(orderStatus: { eta: Date, status: string, statusText: string }, userContext: UserContext, booking: FoodBooking): string {
        // Assign subtitle (ETA, current status)
        let subTitle
        let timeDiff = _.ceil((orderStatus.eta.getTime() - new Date().getTime()) / 60000)
        if (orderStatus.status === "ARRIVED" || timeDiff < 0) {
            timeDiff = 2
        }
        let etaText
        const etaStr: string = TimeUtil.get12HRTimeFormat(orderStatus.eta, userContext.userProfile.timezone)
        if (timeDiff > 60) {
            etaText = "ETA " + etaStr
        } else {
            etaText = "ETA " + timeDiff + " mins"
        }
        // to  show the eta on the right only when the shipment is created.
        if ((booking.deliveryChannel === "KIOSK" || booking.deliveryChannel === "CAFE")) {
            const tz = userContext.userProfile.timezone
            const mealSlot: MealSlot = SlotUtil.getMealSlotForSlotId(booking.deliverySlot.slotId, tz)
            subTitle = this.getOrderStatusBasedSubtitle(booking.state, mealSlot, etaText, booking.listingBrand)
        }
        else {
            subTitle = etaText
            if (booking.state !== "NOT_STARTED") {
                subTitle = subTitle + " • " + orderStatus.statusText
            }
        }
        return subTitle
    }

    public static diyItemToCardListItem(userContext: UserContext, diyPack: DIYPack, diyProduct: DIYProduct, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, bucketId: string, blockInternationalUser: boolean): CardListItem {
        const content = AtlasUtil.getContentDetailV2(diyProduct)
        const contentMeta = AtlasUtil.getContentMetaV2(diyProduct, diyPack)

        const playAction: Action = {
            actionType: "NAVIGATION",
            title: "PLAY",
            url: (content.type === "audio" ? ActionUtil.audioUrl(content.URL,
                UrlPathBuilder.prefixSlash(diyPack.imageDetails.heroImage), contentMeta) :
                ActionUtil.diyVideoUrl(diyProduct, content.URL, UrlPathBuilder.prefixSlash(diyPack.imageDetails.heroImage),
                    contentMeta)),
            meta: {
                content: content,
                meta: contentMeta
            }
        }
        const cardListItem: CardListItem = {
            title: diyProduct.title,
            subTitle: "From " + diyPack.title,
            action: LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, diyProduct.locked, playAction, isUserEligibleForTrial, diyProduct.productType, "upcoming_widget_cta", bucketId, blockInternationalUser, diyProduct.format),
            leftInfo: {
                images: [UrlPathBuilder.prefixSlash(diyProduct.imageDetails.thumbnailImage)]
            },
            rightInfo: {
                action: playAction,
                moreAction: undefined
            },
        }
        return cardListItem
    }

    public static groupUpcomingOrCompletedItems(tz: Timezone, items: CardListItemWithTimestamp[], isDescending: boolean, lastActivityDate?: string): CardListItemWithTimestamp[] {
        const itemsByDate = _.groupBy(items, item => item.date)

        const sortedDates = Object.keys(itemsByDate).sort((a, b) => {
            if (isDescending) {
                return a < b ? 1 : -1
            } else {
                return a < b ? -1 : 1
            }
        })

        sortedDates.forEach(date => {
            const items = itemsByDate[date]
            items.sort((a, b) => {
                if (a.timestamp && b.timestamp) {
                    return isDescending ? b.timestamp - a.timestamp : a.timestamp - b.timestamp
                } else if (a.timestamp) {
                    return -1
                } else {
                    return 1
                }
            })
            // Adding the date header to the first item alone
            // skipping the header in case lastActivityDate from previous page is same as any of the current page item
            if (!lastActivityDate || (lastActivityDate && lastActivityDate !== date)) {
                items[0].header = {
                    title: TimeUtil.getDayText(date, tz, {
                        sameDay: "[Today]",
                        nextDay: "[Tomorrow]",
                        nextWeek: "ddd, DD MMM",
                        lastDay: "[Yesterday]",
                        lastWeek: "ddd, DD MMM",
                        sameElse: "ddd, DD MMM"
                    })
                }
            }


        })
        const finalList: CardListItemWithTimestamp[] = []
        sortedDates.forEach(date => {
            finalList.push(...itemsByDate[date])
        })
        return finalList
    }

    public static getSubTitle(userContext: UserContext, bookingInfo: BookingDetail, subCategory?: SUB_CATEGORY_CODE) {
        const subCategoryCode = subCategory ? subCategory : _.get(bookingInfo, "booking.subCategoryCode", undefined)
        switch (subCategoryCode) {
            case "CF_INCENTRE_CONSULTATION":
                return TimelineHelper.inCenterConsultationSubTitle(userContext, bookingInfo)
            case "CF_ONLINE_CONSULTATION":
                return TimelineHelper.onlineConsultationSubTitle(userContext, bookingInfo)
            case "AT_HOME_SLOT":
                return TimelineHelper.atHomeSubTitle(userContext, bookingInfo)
            case "IN_CENTRE_SLOT":
                return TimelineHelper.atCenterSubTitle(userContext, bookingInfo)
            case "DIAGNOSTIC_TEST":
                return TimelineHelper.atCenterSubTitle(userContext, bookingInfo)
        }
    }

    public static inCenterConsultationSubTitle(userContext: UserContext, bookingInfo: BookingDetail): string {
        const consultationOrderResponse = bookingInfo.consultationOrderResponse
        const patient = consultationOrderResponse.patient
        const center = consultationOrderResponse.center
        const startTime = this.getStartTimeText(consultationOrderResponse.startTime, userContext.userProfile.timezone)
        return `${startTime}, ${center.name} for ${patient.name}`
    }

    public static onlineConsultationSubTitle(userContext: UserContext, bookingInfo: BookingDetail): string {
        const consultationOrderResponse = bookingInfo.consultationOrderResponse
        const doctor = consultationOrderResponse.doctor
        const startTime = this.getStartTimeText(consultationOrderResponse.startTime, userContext.userProfile.timezone)
        return `${startTime}, With ${doctor.name} `
    }

    public static atHomeSubTitle(userContext: UserContext, bookingInfo: BookingDetail): string {
        const atHomeDiagnosticOrder = bookingInfo.diagnosticsTestOrderResponse[0].atHomeDiagnosticOrder
        const patient = bookingInfo.diagnosticsTestOrderResponse[0].patient
        const startTime = this.getStartTimeText(atHomeDiagnosticOrder.startTime, userContext.userProfile.timezone)
        return `${startTime}, for ${patient.name}`

    }

    public static atCenterSubTitle(userContext: UserContext, bookingInfo: BookingDetail): string {
        const inCenterDiagnosticOrder = bookingInfo.diagnosticsTestOrderResponse[0].inCentreDiagnosticOrder
        const patient = bookingInfo.diagnosticsTestOrderResponse[0].patient
        const center = inCenterDiagnosticOrder.slot.diagnosticCentre
        const startTime = this.getStartTimeText(inCenterDiagnosticOrder.slot.workingStartTime, userContext.userProfile.timezone)
        return `${startTime}, ${center.name} for ${patient.name}`
    }

    public static getStartTimeText(time: number, timezone: Timezone): string {
        const parsedStartDate = TimeUtil.parseDateFromEpoch(time)
        const parsedStartDateMoment = TimeUtil.getMomentForDate(parsedStartDate, timezone)
        return TimeUtil.getTimeIn12HRFormat(TimeUtil.formatDateInTimeZone(timezone,
            parsedStartDate), parsedStartDateMoment.hours(), parsedStartDateMoment.minutes(), true, timezone)
    }

    public static getOrderStatusBasedSubtitle(state: FoodShipmentStatus | "NOT_STARTED" | "CANCELLED", mealSlot: MealSlot, etaText: string, listingBrand: ListingBrandIdType): string {
        const status: string = EatUtil.getEatFitDeliveryStatus(state, listingBrand)
        switch (status) {
            case "PREPARING":
                return etaText + "." + `Preparing ${EatUtil.getMealSlotCamelCase(mealSlot)}.`
            case "PACKING":
                return etaText + "." + `${EatUtil.getMealSlotCamelCase(mealSlot)} being packed.`
            case "ON_ITS_WAY":
                return etaText + "." + `${EatUtil.getMealSlotCamelCase(mealSlot)} is on the way`
            case "ARRIVED":
                return "Ready for pick up"
            case "DELIVERED":
                return "Picked Up"
            case "REJECTED":
                return "Rejected"
            case "CANCELLED":
                return undefined
        }
        return undefined
    }
}
