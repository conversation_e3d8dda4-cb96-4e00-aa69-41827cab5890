import { UserContext } from "@curefit/userinfo-common"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { CardListWidgetView, CardListContentType, CardListItem, CardListItemRightInfo } from "../card/CardListWidget"
import * as _ from "lodash"
import { ChallengeWidget } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"
import { ChallengeEnrollment, ChallengeEnrollmentDetail, LeaderBoardWithDetails, Metric } from "@curefit/maximus-common"
import { _isStepsChallenge, _getJoinChallengeAction } from "../../../../maximus/Helper"
import { UrlPathBuilder, ChallengeImageTypes } from "@curefit/product-common"
import { SocialColors } from "../../../../common/views/Social"
import { Action } from "../../../../common/views/WidgetView"

export class ChallengeWidgetView extends ChallengeWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const enrolledChallengesAsCardListItem = this.getEnrolledChallengesAsCardListItem(interfaces, userContext)
        const invitesAsCardListItem = this.getInvitesAsCardListItem(interfaces, userContext)
        const items: CardListItem[] = []
        items.push(...await invitesAsCardListItem)
        items.push(...await enrolledChallengesAsCardListItem)
        if (!_.isEmpty(items)) {
            return new CardListWidgetView("Challenges", "CHALLENGES", items).buildView(interfaces, userContext, queryParams)
        }
    }

    async getInvitesAsCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<CardListItem[]> {
        return []
    }

    async getEnrolledChallengesAsCardListItem(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<CardListItem[]> {
        return []
    }

    private getEnrolledChallengeRank(enrollmentDetail: ChallengeEnrollmentDetail): string {
        const leaderboardMeta = enrollmentDetail.leaderboardMeta[0]
        if (_.isNil(leaderboardMeta.rank) || _isStepsChallenge(enrollmentDetail.challenge.challengeId)) {
            return undefined
        }
        const totalParticipants = leaderboardMeta.totalEnrollments < 70000 ? leaderboardMeta.totalEnrollments : "70000+"
        return `Rank ${leaderboardMeta.rank} of ${totalParticipants}`
    }

    private getEnrolledChallengeSubTitle(enrollmentDetail: ChallengeEnrollmentDetail, tz: Timezone): string {
        const startDiff = momentTz.tz(tz).diff(momentTz.tz(enrollmentDetail.challenge.startDate, tz), "seconds")
        const endDiff = momentTz.tz(enrollmentDetail.challenge.endDate, tz).add(1, "days").diff(momentTz.tz(tz), "seconds")

        let subTitle = ""

        if (startDiff >= 0 && endDiff > 0) {
            // ACTIVE
            subTitle = `Ends on ${TimeUtil.formatDateInTimeZone(tz, enrollmentDetail.challenge.endDate, "DD MMM")}`
        } else if (endDiff < 0) {
            // ENDED
            subTitle = `Ended on ${TimeUtil.formatDateInTimeZone(tz, enrollmentDetail.challenge.endDate, "DD MMM, YYYY")}`
        } else {
            // NOT STARTED
            // check if less than 24hrs are remaining
            if ((-startDiff) / 3600 < 24) {
                subTitle = "Starts tomorrow"
            } else {
                const startDiffDays = momentTz.tz(tz).diff(momentTz.tz(enrollmentDetail.challenge.startDate, tz), "days")
                const days = startDiffDays === -1 ? "day" : "days"
                subTitle = `${-startDiffDays + 1} ${days} remaining`
            }
        }

        const rank = this.getEnrolledChallengeRank(enrollmentDetail)
        if (rank) {
            subTitle = `${subTitle}\n${rank}`
        }

        return subTitle

    }

    private getEnrolledChallengeRightInfo(enrollmentDetail: ChallengeEnrollmentDetail): CardListItemRightInfo {
        const leaderboardMeta = enrollmentDetail.leaderboardMeta[0]
        const leaderBoardDetail = leaderboardMeta.leaderBoardDetails
        const suffix = this._getAcitivityName(leaderBoardDetail)
        return {
            subTitle: `${leaderboardMeta.score} ${suffix}`,
            action: undefined,
            moreAction: undefined
        } as CardListItemRightInfo
    }

    _getAcitivityName(leaderboard: LeaderBoardWithDetails): string {
        switch (leaderboard.metric) {
            case Metric.CULT_SCORE:
                return "Score"
            case Metric.ACTIVITY_TIME:
                return "mins"
            case Metric.STEPS:
                return "Steps"
            case Metric.RUNS:
                return "Runs"
            case Metric.CALORIES:
                return "Calories"
            default:
                return "Points"
        }
    }

}
