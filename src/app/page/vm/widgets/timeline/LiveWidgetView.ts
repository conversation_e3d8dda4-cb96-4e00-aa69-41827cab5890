import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { CardListWidgetView, UpcomingItem } from "../card/CardListWidget"
import { LiveWidget } from "@curefit/vm-models"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { TimelineHelper } from "./TimelineHelper"
import { LiveUpcomingBuilder } from "./upcoming/LiveUpcomingBuilder"
import { LivePackUtil } from "../../../../util/LivePackUtil"


export class LiveWidgetView extends LiveWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const startDate = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        const endDate = TimeUtil.addDays(userContext.userProfile.timezone, startDate, 1)
        const items: UpcomingItem[] = []
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const liveUpcomingItemsPromise = new LiveUpcomingBuilder().getUpcoming(interfaces, userContext, startDate, endDate, "LIVE_ACTIVITIES", isUserEligibleForMonetisation, isUserEligibleForTrial)
        items.push(... await liveUpcomingItemsPromise)
        if (!_.isEmpty(items)) {
            return new CardListWidgetView("Live", "LIVE_ACTIVITIES",
                TimelineHelper.groupUpcomingOrCompletedItems(userContext.userProfile.timezone, items, false)).buildView(interfaces, userContext, queryParams)
        }
    }
}
