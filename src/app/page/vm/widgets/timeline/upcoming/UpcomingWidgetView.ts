import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { CardListWidgetView, UpcomingItem } from "../../card/CardListWidget"
import { UpcomingWidget } from "@curefit/vm-models"
import { EatUpcomingBuilder } from "./EatUpcomingBuilder"
import { CareUpcomingBuilder } from "./CareUpcomingBuilder"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { FoodBooking } from "@curefit/alfred-client"
import { GenericError } from "@curefit/error-client"
import { DIYUpcomingBuilder } from "./DIYUpcomingBuilder"
import { TimelineHelper } from "../TimelineHelper"
import { LiveUpcomingBuilder } from "./LiveUpcomingBuilder"
import { CfApiConfig } from "@curefit/config-mongo"
import { LivePackUtil } from "../../../../../util/LivePackUtil"
import AppUtil from "../../../../../util/AppUtil"

export class UpcomingWidgetView extends UpcomingWidget {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        const startDate = TimeUtil.todaysDateWithTimezone(userContext.userProfile.timezone)
        const endDate = TimeUtil.addDays(userContext.userProfile.timezone, startDate, 7)
        const items: UpcomingItem[] = []
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return undefined
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        // const cultUpcomingItemsPromise = new CultAndMindUpcomingClassBuilder().getCultUpcomingClasses(userMapPromise, interfaces, userContext, startDate, endDate, foodBookingsPromise)
        // const mindUpcomingItemsPromise = new CultAndMindUpcomingClassBuilder().getMindUpcomingClasses(userMapPromise, interfaces, userContext, startDate, endDate)
        // const gymUpcomingItemsPromise = new GymUpcomingBuilder().getGymUpcomingClass(interfaces, userContext)
        const careUpcomingItemsPromise = new CareUpcomingBuilder().getCareUpcoming("CARE", interfaces, userContext, startDate, endDate)
        const ptUpcomingItemsPromise = new CareUpcomingBuilder().getCareUpcoming("CULTFIT", interfaces, userContext, startDate, endDate)
        // Passing  startDate as endDate as we want to show only current day diy in upcoming
        const liveUpcomingItemsPromise = new LiveUpcomingBuilder().getUpcoming(interfaces, userContext, startDate, endDate, "UPCOMING_ACTIVITIES", isUserEligibleForMonetisation, isUserEligibleForTrial)

        const cfApiConfig: CfApiConfig = interfaces.configService.getConfig<CfApiConfig>("CF_API")
        let diyUpcomingItemsPromise
        if (cfApiConfig.configs && cfApiConfig.configs.isDiyUpcomingEnabled) {
            diyUpcomingItemsPromise = new DIYUpcomingBuilder().getDIYUpcoming(interfaces, userContext, startDate, startDate, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId)
        }
        // const cultAndMindUpcomingItems = [...await cultUpcomingItemsPromise, ...await mindUpcomingItemsPromise]
        const cultAndMindUpcomingItems: UpcomingItem[] = []
        items.push(...cultAndMindUpcomingItems)
        // items.push(... await gymUpcomingItemsPromise)
        items.push(... await careUpcomingItemsPromise)
        items.push(... await ptUpcomingItemsPromise)
        items.push(... await liveUpcomingItemsPromise)
        if (diyUpcomingItemsPromise)
            items.push(... await diyUpcomingItemsPromise)
        if (!_.isEmpty(items)) {
            return new CardListWidgetView("Upcoming", "UPCOMING_ACTIVITIES",
                TimelineHelper.groupUpcomingOrCompletedItems(userContext.userProfile.timezone, items, false)).buildView(interfaces, userContext, queryParams)
        }
    }

    private filterCafeUpcomingItems(eatUpcomingItemsIncludingCafe: UpcomingItem[], cultAndMindUpcomingItems: UpcomingItem[]): UpcomingItem[] {
        const eatUpcomingItems = eatUpcomingItemsIncludingCafe.filter(eatUpcomingItem => {
            if (_.isNil(eatUpcomingItem.meta) || eatUpcomingItem.meta.referrerSource !== "CULT_CLASS") {
                return true
            }
            const status: boolean = cultAndMindUpcomingItems.some(cultAndMindUpcomingItem => {
                if (!_.isNil(cultAndMindUpcomingItem.meta) && cultAndMindUpcomingItem.meta.referrerSource === "CULT_CLASS" && cultAndMindUpcomingItem.meta.referrerId === eatUpcomingItem.meta.referrerId) {
                    return true
                }
            })
            if (!status) {
                return true
            }
            else {
                return false
            }
        })
        return eatUpcomingItems
    }
    private async getFoodBookingPromise(interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string): Promise<FoodBooking[]> {
        const tz = userContext.userProfile.timezone
        const futureDatesArray: string[] = TimeUtil.futureDatesWithinRange(tz, startDate, endDate)
        const userId = userContext.userProfile.userId

        // Check if dates are in past
        let bookings: Promise<FoodBooking[]>
        if (futureDatesArray.length !== 0) {
            // If future(including TODAY) dates are present, then check and generate meal widgets for these
            try {
                bookings = Promise.resolve([])
                    // interfaces.shipmentService.getFutureFoodBookings(tz, userId, futureDatesArray[0], futureDatesArray.length)
            } catch (e) {
                interfaces.logger.error("Unable to build EatFit widgets for timeline. Error while fetching future bookings. User ID : " + userId)
                const genericErr: GenericError = new GenericError({message: "Error contacts shipment service for future bookings!", context: {userId: userId}})
                genericErr.statusCode = 500
                interfaces.rollbarService.handleError(genericErr)
                return []
            }
        }
        return bookings ? bookings : []
    }

}
