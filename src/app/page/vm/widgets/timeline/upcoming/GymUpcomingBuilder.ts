import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { UpcomingItem } from "../../card/CardListWidget"
import { TimeUtil, Timezone } from "@curefit/util-common"
import { GymfitCenterCategory, GymfitCheckIn, GymfitCheckInState, MediaType } from "@curefit/gymfit-common"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common"
import { SUPPORT_DEEP_LINK } from "../../../../../util/AppUtil"

export class GymUpcomingBuilder {
    public async getGymUpcomingClass(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<UpcomingItem[]> {
        try {
            const upcomingGymClass = await interfaces.gymfitService.getUpcomingGymfitCheckIns(userContext.userProfile.userId)
            if (!upcomingGymClass || _.isEmpty(upcomingGymClass)) {
                return []
            }
            const tz = userContext.userProfile.timezone
            return await this.buildUpcomingGymClass(interfaces, upcomingGymClass, tz, userContext)
        } catch (exception) {
            interfaces.logger.error("Unable to build GymFit widgets for timeline. User ID : " + userContext.userProfile.userId + exception.stack)
            console.log(exception.stack)
            return []
        }
    }

    private async buildUpcomingGymClass(interfaces: CFServiceInterfaces, upcomingGymClasses: GymfitCheckIn[], tz: Timezone, userContext: UserContext): Promise<UpcomingItem[]> {
        return Promise.all(upcomingGymClasses.map(async (upcomingGymClass) => {
            const leftImages: string[] = []
            if (upcomingGymClass.event.baseEvent.center.media.clpMedia && upcomingGymClass.event.baseEvent.center.media.clpMedia.length) {
                const imageMedia = upcomingGymClass.event.baseEvent.center.media.clpMedia.find(media => media.type === MediaType.IMAGE)
                leftImages.push(imageMedia ? imageMedia.mediaUrl : undefined)
            }
            let classType = ""
            if (upcomingGymClass.centerOffering.center.category === GymfitCenterCategory.GX) {
                classType = upcomingGymClass.event.baseEvent.activity ? upcomingGymClass.event.baseEvent.activity.name + " - " : ""
            }
            return  {
                title: `${classType}${upcomingGymClass.event.baseEvent.center.name}`,
                subTitle: this.getGymTimeAndPlace(upcomingGymClass.startTime, upcomingGymClass.endTime, upcomingGymClass.event.baseEvent.center.locality, tz),
                action: {
                    actionType: "NAVIGATION",
                    url: interfaces.gymfitBusiness.getCheckInActionUrl(userContext, upcomingGymClass.event.baseEvent.center, upcomingGymClass.id)
                },
                leftInfo: {
                    images: leftImages
                },
                rightInfo: {
                    action: {
                        title: "NAVIGATE",
                        actionType: "NAVIGATION",
                        url: ActionUtil.viewMap(upcomingGymClass.event.baseEvent.center.address.mapUrl)
                    },
                    moreAction: {
                        icon: "MANAGE",
                        actionType: "ACTION_LIST",
                        actions: await this.getPossibleActions(interfaces, upcomingGymClass, upcomingGymClass.state, userContext)
                    }
                },
                date: TimeUtil.formatEpochInTimeZoneDateFns(tz, upcomingGymClass.startTime, "yyyy-MM-dd"),
                timestamp: upcomingGymClass.startTime,
                footer: {
                    title: `CODE | ${upcomingGymClass.passCode}`
                },
                meta: undefined
            } as UpcomingItem
        }))
    }

    private async getPossibleActions(interfaces: CFServiceInterfaces, checkin: GymfitCheckIn, checkinState: GymfitCheckInState, userContext: UserContext): Promise<Action[]> {
        const possibleActions: Action[] = []
        const issues = await interfaces.issueBusiness.getGymfitFitnessCheckinIssues(parseInt(checkin.id),
            interfaces.gymfitBusiness.getGymCheckInStatus(userContext.userProfile.timezone, checkin), userContext)
        if (checkinState === GymfitCheckInState.CREATED) {
            possibleActions.push({
                actionType: "CANCEL_GYM_CHECKIN",
                title: "Cancel",
                meta: {checkinId: checkin.id}
            })
        }
        possibleActions.push({
            actionType: "REPORT_ISSUE",
            actionId: "REPORT_ISSUE",
            title: "Need Help",
            url: SUPPORT_DEEP_LINK,
            meta: {issues}
        })
        return possibleActions
    }

    private getGymTimeAndPlace(startTime: number, endTime: number, locality: string, tz: Timezone): string {
        const startHours = TimeUtil.formatEpochInTimeZoneDateFns(tz, startTime, "hh:mm aaaa")
        const endHours = TimeUtil.formatEpochInTimeZoneDateFns(tz, endTime, "hh:mm aaaa")
        return `${startHours} - ${endHours}, ${locality}`
    }
}
