import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import {
    CardListContentType,
    UpcomingItem,
    UpcomingItemFooter,
    UpcomingItemTooltip
} from "../../card/CardListWidget"
import { DigitalCatalogueEntryV1, DigitalCatalogueEntryV2, SocialDataResponse, VideoStatus } from "@curefit/diy-common"
import { UrlPathBuilder } from "@curefit/product-common"
import LiveUtil from "../../../../../util/LiveUtil"
import AppUtil from "../../../../../util/AppUtil"
import { ClassInviteLinkCreator } from "../../../../../cult/invitebuddy/ClassInviteLinkCreator"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { CultBuddiesJoiningListSmallWidget, PageTypes } from "@curefit/apps-common"
import { CacheHelper } from "../../../../../util/CacheHelper"
import { LivePackUtil } from "../../../../../util/LivePackUtil"


export class LiveUpcomingBuilder {

    subscribedEntries: string[] = []
    widgetType: CardListContentType
    sessionInfo: SessionInfo

    public async getUpcoming(interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string, widgetType: CardListContentType, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean): Promise<UpcomingItem[]> {
        try {
            const user: User = await userContext.userPromise
            if (!AppUtil.isLiveClassSupported(userContext, user)) {
                return []
            }
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
            this.widgetType = widgetType
            this.sessionInfo = userContext.sessionInfo
            const countryId = AppUtil.getCountryId(userContext)
            const isLiveQnASupported = await AppUtil.isLiveQnASupported(userContext)
            // const cultPreference: PreferenceDetail = (await eternalPromise(interfaces.cultBusiness.getClassRemindersPreference(userContext, userContext.sessionInfo.sessionData.cityId, userContext.userProfile.userId, "FITNESS"))).obj
            const allSubscribedLiveClasses = await interfaces.diyService.getUpcomingSubscribedLiveClasses(user.id, AppUtil.getTenantFromUserContext(userContext), countryId)
            const subscribedLiveClasses = LiveUtil.filterInteractiveSessionOrRealLiveWhenNotSupported(allSubscribedLiveClasses, isLiveQnASupported, userContext)
            const socialDataMap = await interfaces.diyService.getSocialDataForSessionsAsMap(user.id, subscribedLiveClasses.map(subscribedLiveClass => (<any>subscribedLiveClass)._id), !AppUtil.isLiveLazyInviteLinkActionSupported(userContext), AppUtil.getTenantFromUserContext(userContext))
            const bookingPref = false   // (_.isEmpty(cultPreference)) ? false : cultPreference.bookingEmailPreference
            return this.buildItemFromBookings(user, subscribedLiveClasses, socialDataMap, userContext, interfaces.classInviteLinkCreator, bookingPref, interfaces.diyService, interfaces.userCache, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, blockInternationalUser)
        } catch (e) {
            interfaces.logger.error(`Unable to build live widgets for timeline. User ID : ${userContext.userProfile.userId}, widgetType : ${widgetType}, ${e.stack}`)
            return []
        }
    }

    private getStatuses(): VideoStatus[] {
        switch (this.widgetType) {
            case "UPCOMING_ACTIVITIES":
                return ["UPCOMING", "PREPARED", "LIVE"]
            case "LIVE_ACTIVITIES":
                return ["LIVE"]
            case "COMPLETED_ACTIVITES":
                return ["ENDED"]
        }
        return []
    }

    private async buildItemFromBookings(user: User, subscribedVideoDetails: DigitalCatalogueEntryV2[], socialDataMap: { [sessionId: string]: SocialDataResponse }, userContext: UserContext, classInviteLinkCreator: ClassInviteLinkCreator, cultCalendarPreference: boolean, diyFulfilmentService: IDIYFulfilmentService, userCache: CacheHelper, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, bucketId: string, blockInternationalUser: boolean): Promise<UpcomingItem[]> {

        // const { card } = await interfaces.cultBusiness.isUserEligibleForInviteOffer(userContext, CULT_LIVE_CAMPAIGN_ID)
        const widgetRowItemPromises = subscribedVideoDetails.map(session => {
            return this.bookingToWidgetRowItem(user, userContext, session, socialDataMap[(<any>session)._id], classInviteLinkCreator, cultCalendarPreference, diyFulfilmentService, userCache, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, blockInternationalUser, null)
        })
        const widgetRowItems = await Promise.all(widgetRowItemPromises)
        return _.filter(widgetRowItems, widgetRowItem => !_.isNil(widgetRowItem))
    }

    private async bookingToWidgetRowItem(user: User, userContext: UserContext, liveSession: DigitalCatalogueEntryV2, socialData: SocialDataResponse, classInviteLinkCreator: ClassInviteLinkCreator, cultCalendarPreference: boolean, diyFulfilmentService: IDIYFulfilmentService, userCache: CacheHelper, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, bucketId: string, blockInternationalUser: boolean, card?: any): Promise<UpcomingItem> {

        if (AppUtil.isWeb(userContext) && liveSession.format === "HOBBY") {
            // disabling poplive upcoming for web/mweb
            return undefined
        }
        const attendingUsers = _.isNil(socialData) ? [] : socialData.attendingUsers
        const date = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, liveSession.scheduledTimeEpoch)
        const timestamp = liveSession.scheduledTimeEpoch
        const title = liveSession.title
        const subTitle = TimeUtil.formatEpochInTimeZone(userContext.userProfile.timezone, liveSession.scheduledTimeEpoch, "h:mm A") + ", Live | At Home"
        // const firestoreEnabled = await LiveUtil.isFirestoreEnabled(interfaces.diyService, (<any>liveSession)._id)
        let action
        if (Date.now() > liveSession.playerStartTimeEpoch) {
            action = LiveUtil.getLiveVideoPlayerAction(userContext, user, liveSession, this.sessionInfo, "upcoming_widget_cta")
            action = LiveUtil.getLiveSessionAction(isUserEligibleForMonetisation, liveSession.locked, action, isUserEligibleForTrial, userContext, liveSession.format, "upcoming_widget_cta", bucketId, blockInternationalUser)
        }
        else {
            action = LiveUtil.getLiveSessionDetailAction(liveSession, "upcoming_widget_cta", userContext)
        }
        const leftInfo = {
            images: [UrlPathBuilder.prefixSlash(liveSession.bannerImages.mobileImage)]
        }
        const widgetRowItem: UpcomingItem = {
            date: date,
            timestamp: timestamp,
            title: title,
            subTitle: subTitle,
            action: action,
            leftInfo: leftInfo,
            rightInfo: LiveUtil.getRightInfoForUpcomingWidget(user, userContext, liveSession, this.sessionInfo, "upcoming_widget_cta"),
            footers: [],
            buddiesJoining: (!LiveUtil.isVanillaLiveFitFormat(liveSession.format) && await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) && !_.isEmpty(attendingUsers) ?
                await LiveUtil.getBuddiesJoiningListSmallView(attendingUsers, userCache, PageTypes.HomeTab, true) as CultBuddiesJoiningListSmallWidget : undefined
        }
        if ((!LiveUtil.isVanillaLiveFitFormat(liveSession.format) && (liveSession.status == "UPCOMING" || liveSession.status == "PREPARED"))
            && AppUtil.isShareActionWidgetSupported(userContext)) {

            let footer

            if (card) {
                footer = await this.buildInviteFriendsWithEarningFooter(userContext, liveSession, classInviteLinkCreator, socialData, card && { ...card, source: "live-upcoming-widget" })
            } else {
                footer = await this.buildInviteFriendsFooter(userContext, liveSession, classInviteLinkCreator, socialData)
            }

            if (footer) {
                widgetRowItem.footers.push(footer)
            }
        }
        return widgetRowItem
    }

    private async buildInviteFriendsFooter(userContext: UserContext, liveSession: DigitalCatalogueEntryV1, classInviteLinkCreator: ClassInviteLinkCreator, socialDataResponse?: SocialDataResponse): Promise<UpcomingItemFooter> {
        const action = await LiveUtil.getShareAction(userContext, classInviteLinkCreator, liveSession, socialDataResponse)
        if (action) {
            const title = "Invite your friend"
            const icon = userContext.sessionInfo.appVersion >= 8.15 ? "BUDDY" : "WHATSAPP_SHARE"
            const tooltip: UpcomingItemTooltip = {
                title: "NEW",
                text: "Invite your buddies over WhatsApp to join you for your workout",
            }
            return {
                title: title,
                action: action,
                icon: icon,
                tooltip: tooltip
            }
        }
    }

    private async buildInviteFriendsWithEarningFooter(userContext: UserContext, liveSession: DigitalCatalogueEntryV1, classInviteLinkCreator: ClassInviteLinkCreator, socialDataResponse?: SocialDataResponse, card?: any): Promise<UpcomingItemFooter> {
        const action = await LiveUtil.getShareAction(userContext, classInviteLinkCreator, liveSession, null, card)
        if (action) {
            const title = "Invite friend & earn 50 Fitcash"
            const icon = userContext.sessionInfo.appVersion >= 8.15 ? "INFO" : "WHATSAPP_SHARE"
            const tooltip: UpcomingItemTooltip = {
                title: "NOTE",
                text: "Invite your friends to cultpass HOME classes and get a chance to earn fitcash",
            }
            return {
                title: title,
                action: action,
                icon: icon,
                tooltip: tooltip,
                iconAction: {
                    "title": "How it works",
                    "viewType": "LINK",
                    "actionType": "NAVIGATION",
                    "url": "curefit://referraltncpage?source=cult_live",
                    "meta": {
                        "title": "How it works"
                    }
                }
            }
        }
    }
}
