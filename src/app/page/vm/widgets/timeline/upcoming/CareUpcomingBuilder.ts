import CareUtil from "./../../../../../util/CareUtil"
import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { UpcomingItem } from "../../card/CardListWidget"
import TimelineUtil from "../../../../../util/TimelineUtil"
import { Action, ActionList } from "../../../../../common/views/WidgetView"
import { HealthfaceTenant } from "@curefit/product-common"
import { TimelineActivityV2, TimelineResponseV2, ConsultationInstructionResponse } from "@curefit/albus-client"
import ActionUtil from "../../../../../util/ActionUtil"
import { CFUserProfile } from "../../../CFUserProfile"
import { TimelineHelper } from "../TimelineHelper"
import AppUtil, { LIVE_PT_ZOOM_MEETING_JOIN_TITLE } from "../../../../../util/AppUtil"
import { ConsultationProduct } from "@curefit/care-common"


export class CareUpcomingBuilder {

    async getCareUpcoming(tenant: HealthfaceTenant, interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string): Promise<UpcomingItem[]> {
        const tz = userContext.userProfile.timezone
        const futureDatesArray: string[] = TimeUtil.futureDatesWithinRange(tz, startDate, endDate)
        const userId = userContext.userProfile.userId
        try {
            const timelineResponse: TimelineResponseV2 = await this.getTimelineResponse(tenant, userContext, interfaces, futureDatesArray)
            if (timelineResponse && timelineResponse.upcomingActivities) {

                const upcomingItemPromises: Promise<UpcomingItem>[] = []
                _.forEach(timelineResponse.upcomingActivities, timelineActivity => {
                    upcomingItemPromises.push(this.buildUpcomingItemFromTimelineActivity(interfaces, timelineActivity, userContext))
                })
                const upcomingItems = await Promise.all(upcomingItemPromises)
                return _.filter(upcomingItems, upcomingItem => !_.isNil(upcomingItem))
            }
            return []
        } catch (e) {
            interfaces.logger.error("Unable to build carefit widgets for timeline. User ID : " + userId + e.stack)
            return []
        }
    }

    private async getTimelineResponse(tenant: HealthfaceTenant, userContext: UserContext, interfaces: CFServiceInterfaces, futureDatesArray: string[]): Promise<TimelineResponseV2> {
        const userProfile: CFUserProfile = userContext.userProfile
        // TODO: Till Care handles web flows
        if (tenant === "CARE") {
            if (!userProfile.careTimelinePromise) {
                userProfile.careTimelinePromise = interfaces.healthfaceService.getTimelineActivitiesV2(userContext,
                    TimeUtil.getDate(_.head(futureDatesArray), 0, 0, userProfile.timezone).getTime(),
                    TimeUtil.getDate(_.last(futureDatesArray), 0, 0, userProfile.timezone).getTime()
                )
            }
            return userProfile.careTimelinePromise
        } else if (tenant === "CULTFIT") {
            if (!userProfile.ptTimelinePromise) {
                userProfile.ptTimelinePromise = interfaces.cultPTService.getTimelineActivitiesV2(userContext,
                    TimeUtil.getDate(_.head(futureDatesArray), 0, 0, userProfile.timezone).getTime(),
                    TimeUtil.getDate(_.last(futureDatesArray), 0, 0, userProfile.timezone).getTime()
                )
            }
            return userProfile.ptTimelinePromise
        }

    }

    private async buildUpcomingItemFromTimelineActivity(interfaces: CFServiceInterfaces, careFitTimelineActivity: TimelineActivityV2, userContext: UserContext): Promise<UpcomingItem> {
        const timezone = userContext.userProfile.timezone
        const primaryAndSecondaryActions = await this.buildPrimaryAndSecondaryAction(userContext, careFitTimelineActivity, interfaces)
        const startTime = TimelineHelper.getStartTimeText(careFitTimelineActivity.timestamp, timezone)
        const isDocUnavailableUISupported = CareUtil.isDoctorNonAvailable(careFitTimelineActivity.status) && CareUtil.isDoctorUnavailabilitySupported(userContext)
        const consultationProduct = <ConsultationProduct> await interfaces.catalogueService.getProduct(careFitTimelineActivity.productCode)
        const action = ActionUtil.getCareActivityActionV2(userContext, careFitTimelineActivity, consultationProduct?.urlPath, interfaces.logger)
        let note = undefined
        if (CareUtil.isLivePTDoctorType(careFitTimelineActivity.doctorType) || CareUtil.isLiveSGTDoctorType(careFitTimelineActivity.doctorType)) {
            if (careFitTimelineActivity.status === "MISSED") {
                note = "You have missed your class"
            } else if (!CareUtil.isZoomLinkActionEnabled(careFitTimelineActivity.activityActions)) {
                note = "Join link will be enabled 10 mins before session"
            }
        }
        const refreshCardEpoch = careFitTimelineActivity.timestamp - (10 * 60 * 1000) // 10 mins before start time of session
        const upcomingItem: UpcomingItem = {
            title: careFitTimelineActivity.title,
            subTitle: `${startTime}, ${careFitTimelineActivity.description}`,
            action: action,
            leftInfo: {
                images: [careFitTimelineActivity.timelineImageUrl]
            },
            rightInfo: {
                action: primaryAndSecondaryActions.primaryAction,
                moreAction: primaryAndSecondaryActions.secondaryActionList
            },
            date: TimeUtil.formatDateInTimeZone(timezone, TimeUtil.parseDateFromEpoch(careFitTimelineActivity.timestamp)),
            timestamp: careFitTimelineActivity.timestamp,
            viewType: isDocUnavailableUISupported ? "DOCTOR_UNAVAILABLE" : undefined,
            note,
            calloutInfo: isDocUnavailableUISupported ? CareUtil.getDoctorUnavailablityWidget(true, action) : undefined,
            refreshCardEpoch
        }
        return upcomingItem
    }


    private async buildPrimaryAndSecondaryAction(userContext: UserContext, careFitTimelineActivity: TimelineActivityV2, interfaces: CFServiceInterfaces): Promise<{
        primaryAction: Action,
        secondaryActionList: ActionList
    }> {
        const possibleActions: Action[] = await this.getPossibleActionsForCareFitActivity(userContext, careFitTimelineActivity, interfaces)
        const navigateAction = _.find(possibleActions, possibleAction => possibleAction.title === "Navigate")
        let secondaryActions: Action[]
        let primaryAction: Action
        // To have navigate as primary action
        if (navigateAction) {
            primaryAction = navigateAction
            secondaryActions = _.filter(possibleActions, possibleAction => possibleAction.title !== "Navigate")
        } else if (!_.isEmpty(possibleActions)) {
            const primaryJoinAction = _.find(possibleActions, possibleAction => possibleAction.title === LIVE_PT_ZOOM_MEETING_JOIN_TITLE)
            if (!_.isEmpty(primaryJoinAction)) {
                primaryAction = primaryJoinAction
                secondaryActions = possibleActions
            } else {
                primaryAction = possibleActions[0]
                secondaryActions = possibleActions.slice(1)
            }
        }
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (primaryAction) {
            primaryAction.title = primaryAction.title.toUpperCase()
        }
        return {
            primaryAction, secondaryActionList
        }
    }

    private async getPossibleActionsForCareFitActivity(
        userContext: UserContext,
        careFitTimelineActivity: TimelineActivityV2,
        interfaces: CFServiceInterfaces
    ): Promise<Action[]> {
        const possibleActions: Action[] = []
        let consultationInstruction: ConsultationInstructionResponse[] = []
        if (careFitTimelineActivity.subCategoryCode === "CF_ONLINE_CONSULTATION") {
            if (!(CareUtil.isLivePTDoctorType(careFitTimelineActivity.doctorType) ||
                 CareUtil.isLiveSGTDoctorType(careFitTimelineActivity.doctorType)
                )) {
                const consultationProduct = await interfaces.catalogService.getProduct(careFitTimelineActivity.productCode)
                if (consultationProduct) {
                    consultationInstruction = await interfaces.healthfaceService.getConsultationInstructionsV2(
                        consultationProduct.productId,
                        consultationProduct.doctorType,
                        "video",
                        CareUtil.getInstructionTenant(consultationProduct)
                    )
                }
            }
        }
        // To do remove it when launching couple therapist in web
        const patientsList = !AppUtil.isWeb(userContext) && _.isEmpty(careFitTimelineActivity.secondaryPatientIds) && CareUtil.isCoupleTherapist(careFitTimelineActivity.doctorType) ? await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userContext.userProfile) : []
        const otherActions: Action[] = await ActionUtil.getCareQuickActionsV2(userContext, await userContext.userPromise, careFitTimelineActivity, interfaces.productBusiness, interfaces.logger, consultationInstruction, patientsList, interfaces.hamletBusiness)
        if (!_.isEmpty(otherActions)) {
            const smartActions: Action[] = TimelineUtil.createSmartManageOptionActions(otherActions)
            smartActions.forEach(action => {
                if (action.actionType === "ACTION_LIST") {
                    const actionList: ActionList = <ActionList>action
                    possibleActions.push(...actionList.actions)
                } else {
                    possibleActions.push(action)
                }
            })
        }
        return possibleActions
    }

}
