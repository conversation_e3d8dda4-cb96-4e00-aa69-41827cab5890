import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { TimeUtil } from "@curefit/util-common"
import { ProductType } from "@curefit/product-common"
import * as _ from "lodash"
import { UpcomingItem } from "../../card/CardListWidget"
import { TimelineHelper } from "../TimelineHelper"
import { DIYUserDay, DIYPack, DIYProduct } from "@curefit/diy-common"
import { Action, ActionList } from "../../../../../common/views/WidgetView"
import { ActionUtil as EtherActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import CultUtil from "../../../../../util/CultUtil"
import AppUtil from "../../../../../util/AppUtil"
export class DIYUpcomingBuilder {

    public async getDIYUpcoming(interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, bucketId: string): Promise<UpcomingItem[]> {
        try {
            const diyUserDays = await interfaces.diyService.getDIYCalendarForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), startDate, endDate, true, 1)
            const todoDiys = diyUserDays.filter(diyUserDay => diyUserDay.status === "TODO")
            return Promise.all(_.map(todoDiys, diyUserDay => this.diyToUpcomingItem(interfaces, userContext, diyUserDay, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId)))
        } catch (e) {
            interfaces.logger.error("Unable to build diy widgets for timeline. User ID : " + userContext.userProfile.userId + e.stack)
            return []
        }
    }

    private async diyToUpcomingItem(interfaces: CFServiceInterfaces, userContext: UserContext, diyUserDay: DIYUserDay, isUserEligibleForMonetisation: boolean, isUserEligibleForTrial: boolean, bucketId: string): Promise<UpcomingItem> {

        const tz = userContext.userProfile.timezone
        const userId = userContext.userProfile.userId
        const diyPack = <DIYPack>await interfaces.catalogueService.getProduct(diyUserDay.packId)
        const packType: ProductType = diyPack.productType
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const diyProducts: DIYProduct[] = packType === "DIY_MEDITATION_PACK" ? await interfaces.diyService.getDIYMeditationProductsByProductIds(userId, [diyUserDay.productId], tenant)
            : await interfaces.diyService.getDIYFitnessProductsByProductIds(userId, [diyUserDay.productId], tenant)
        const diyProduct = diyProducts[0]
        const date = TimeUtil.formatDateInTimeZone(tz, diyUserDay.date)
        const timestamp = !_.isNil(diyUserDay.preferredTime) ? TimeUtil.getDate(date, diyUserDay.preferredTime.hour, diyUserDay.preferredTime.min, tz).getTime() : undefined
        const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
        const upcomingItem: UpcomingItem = { date, timestamp, ...TimelineHelper.diyItemToCardListItem(userContext, diyPack, diyProduct, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId, blockInternationalUser) }

        if (!_.isNil(diyUserDay.preferredTime)) {
            const preferredTimeFormatted: string = TimeUtil.getTimeIn12HRFormat(date, diyUserDay.preferredTime.hour,
                diyUserDay.preferredTime.min, true, tz)
            upcomingItem.subTitle = preferredTimeFormatted + ", " + upcomingItem.subTitle
        }
        upcomingItem.rightInfo.moreAction = this.diyMoreActions(diyPack, userContext)
        upcomingItem.footer = {
            title: "Invite buddies on WhatsApp",
            icon: "WHATSAPP_SHARE",
            action: CultUtil.getDiyShareAction(diyPack.productId, diyProduct.productId, diyPack.productType, true, diyPack.title)
        }
        return upcomingItem

    }

    private diyMoreActions(diyPack: DIYPack, userContext: UserContext): ActionList {
        const packUrl = ActionUtilV1.diyPackProductPage(diyPack, userContext.sessionInfo.userAgent)

        return {
            actionType: "ACTION_LIST", actions: [
                {
                    actionType: "NAVIGATION",
                    url: packUrl,
                    title: "View pack"
                }
            ], icon: "MANAGE"
        }
    }
}
