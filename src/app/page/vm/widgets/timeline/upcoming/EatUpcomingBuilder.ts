import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { FoodProduct, MenuType } from "@curefit/eat-common"
import * as _ from "lodash"
import { UpcomingItem } from "../../card/CardListWidget"
import { FoodBooking, FoodPackBooking } from "@curefit/alfred-client"
import { SlotUtil } from "@curefit/eat-util"
import TimelineUtil from "../../../../../util/TimelineUtil"
import { Action, ActionList } from "../../../../../common/views/WidgetView"
import IProductBusiness from "../../../../../product/IProductBusiness"
import { UrlPathBuilder } from "@curefit/product-common"
import { TimelineHelper } from "../TimelineHelper"
import { ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION } from "../../../../../util/OrderUtil"

export class EatUpcomingBuilder {

    private getEat3PTitle(fcName: string, numProducts: number): string {
        let title =  fcName + " - " +  numProducts
        if ( numProducts > 1)
            title = title + " items ordered"
        else
            title = title + " item ordered"
        return title
    }

    private getEat3PSubTitle(timeStamp: number): string {
        let subtitle = "ETA " + timeStamp
        if ( timeStamp > 1)
            subtitle = subtitle + " mins"
        else
            subtitle = subtitle + " min"
        return subtitle
    }

    private async getUpcomingItemForEat3P(interfaces: CFServiceInterfaces, booking: FoodBooking, userContext: UserContext): Promise<UpcomingItem> {
        const marketplaceFoodBookingDetails = booking.marketplaceFoodBookingDetails
        const orderId = booking.orderId
        const productListingIds = marketplaceFoodBookingDetails.productListingIds
        let productMap: { [productId: string]: FoodProduct }
        productMap = await interfaces.catalogService.getFoodProductListingMap(productListingIds, "ONLINE", marketplaceFoodBookingDetails.fcLocation)
        const images = _.map(marketplaceFoodBookingDetails.productListingIds, productListingId => {
            const product = productMap[productListingId]
            const imageUrl = !_.isNil(product) && !_.isNil(product.imageVersion) && product.imageVersion > 0 ? UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "LANDSCAPE", product.imageVersion) : undefined
            return imageUrl
        })
        const timeStamp = new Date(booking.eta).getMinutes()
        const title = this.getEat3PTitle(marketplaceFoodBookingDetails.fcName, productListingIds.length)
        const subTitle = this.getEat3PSubTitle(timeStamp)

        const upcomingItem: UpcomingItem = {
            title: title,
            subTitle: (timeStamp > 0) ?   subTitle : undefined,
            action: {
                actionType: "NAVIGATION",
                url: `curefit://myorderdetail?orderId=${orderId}`
            },
            leftInfo: {
                images: images
            },
            rightInfo: {
                action: {
                    actionType: "NAVIGATION",
                    title: "TRACK",
                    url: `${ORDER_CONFIRMATION_EAT_MARKETPLACE_ACTION}?orderId=${orderId}`
                },
                moreAction: undefined
            },
            date: booking.deliveryDate,
            timestamp: timeStamp,
            meta: booking.meta
        }
        return upcomingItem
    }


    private async buildPrimaryAndSecondaryAction(userContext: UserContext, booking: FoodBooking, packBooking: FoodPackBooking, productBusiness: IProductBusiness): Promise<{
        primaryAction: Action,
        secondaryActionList: ActionList,
        changeMealAction: Action
    }> {
        const possibleActions: Action[] = await productBusiness.getPossibleActionsForFoodBooking(booking, userContext, null, null, ["CHANGE_MEAL", "PICKUP_MEAL", "TRACK_MEAL", "UNDO_CANCEL_MEAL", "UNDO_CANCEL_CART", "CANCEL_MEAL", "CANCEL_CART"], packBooking)
        let changeMealAction
        const actionsOtherThanChangeMeal: Action[] = []
        let isTrackPresent
        _.forEach(possibleActions, action => {
            if (action.icon === "LOCATION" || action.icon === "PICKUP_MEAL") {
                isTrackPresent = true
            }
            if (action.icon === "CHANGE_MEAL") {
                changeMealAction = action
            } else {
                actionsOtherThanChangeMeal.push(action)
            }
        })
        if (!isTrackPresent) {
            const viewAction: Action = {
                actionType: "NAVIGATION",
                title: "VIEW",
                url: await productBusiness.getNavigationUrlForFoodBooking(userContext, booking)

            }
            actionsOtherThanChangeMeal.splice(0, 0, viewAction)
        }
        const primaryAction: Action = actionsOtherThanChangeMeal[0]
        const secondaryActions: Action[] = actionsOtherThanChangeMeal.slice(1)
        const secondaryActionList: ActionList = _.isEmpty(secondaryActions) ? undefined : { actionType: "ACTION_LIST", actions: secondaryActions, icon: "MANAGE" }
        if (primaryAction) {
            primaryAction.title = primaryAction.title.toUpperCase()
        }
        return {
            primaryAction, secondaryActionList, changeMealAction
        }
    }

}
