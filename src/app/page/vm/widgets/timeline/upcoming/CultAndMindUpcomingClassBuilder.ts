import { CFServiceInterfaces } from "../../../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import { FoodProduct as Product, MealSlot, MenuType } from "@curefit/eat-common"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { ActivityState } from "@curefit/logging-common"
import * as _ from "lodash"
import CultUtil, { DUBAI_ACTUAL_CITY_ID, RUNNING_EVENT_WORKOUT_ID } from "../../../../../util/CultUtil"
import { BulkBookingResponse, CultBooking, CultClass } from "@curefit/cult-common"
import { UpcomingItem, UpcomingItemTooltip } from "../../card/CardListWidget"
import AppUtil from "../../../../../util/AppUtil"
import ActionUtil from "../../../../../util/ActionUtil"
import { Action, ManageOptionPayload } from "../../../../../common/views/WidgetView"
import TimelineUtil from "../../../../../util/TimelineUtil"
import IProductBusiness from "../../../../../product/IProductBusiness"
import { TimelineHelper } from "../TimelineHelper"
import { FoodBooking } from "@curefit/alfred-client"
import { StartEndTime } from "@curefit/base-common"
import { ActionUtil as FoodUtil, SeoUrlParams } from "@curefit/base-utils"
import { ClassInviteLinkCreator } from "../../../../../cult/invitebuddy/ClassInviteLinkCreator"
import { CultBuddiesJoiningListSmallWidget, PageTypes } from "@curefit/apps-common"

export class CultAndMindUpcomingClassBuilder {

    public async getCultUpcomingClasses(userMapPromise: Promise<{ [userId: string]: User }>, interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string, foodBookingPromise: Promise<FoodBooking[]>): Promise<UpcomingItem[]> {
        try {
            const userMap: { [userId: string]: User } = await userMapPromise
            const userIds = Object.keys(userMap)
            const cultBookingsResponse = await interfaces.cultFitService.bulkBookings(userIds, endDate, startDate, undefined, await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext) )
            const foodBookings = await foodBookingPromise
            return this.buildUpcomingItemFromBookings(userMap, interfaces, cultBookingsResponse, "FITNESS", userContext, foodBookings)
        } catch (e) {
            interfaces.logger.error("Unable to build cult widgets for timeline. User ID : " + userContext.userProfile.userId + e.stack)
            return []
        }
    }

    public async getMindUpcomingClasses(userMapPromise: Promise<{ [userId: string]: User }>, interfaces: CFServiceInterfaces, userContext: UserContext, startDate: string, endDate: string): Promise<UpcomingItem[]> {
        try {
            const userMap: { [userId: string]: User } = await userMapPromise
            const userIds = Object.keys(userMap)
            const mindBookingsResponse = await interfaces.mindFitService.bulkBookings(userIds, endDate, startDate, undefined, await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext))
            return this.buildUpcomingItemFromBookings(userMap, interfaces, mindBookingsResponse, "MIND", userContext)
        } catch (e) {
            interfaces.logger.error("Unable to build mind widgets for timeline. User ID : " + userContext.userProfile.userId + e.stack)
            return []
        }
    }

    private async buildUpcomingItemFromBookings(userMap: { [userId: string]: User }, interfaces: CFServiceInterfaces, bookingsMap: BulkBookingResponse,
        productType: ProductType, userContext: UserContext, foodBookings?: FoodBooking[]): Promise<UpcomingItem[]> {
        const bookingUserIds = Object.keys(bookingsMap)

        const foodProductMap: { [bookingNumber: string]: FoodBooking } = {}
        for (const i in foodBookings) {
            if (!_.isNil(foodBookings[i].meta) && foodBookings[i].meta.referrerSource === "CULT_CLASS" && foodBookings[i].meta.referrerId) {
                const cultBookingNumber: string = foodBookings[i].meta.referrerId
                foodProductMap[cultBookingNumber] = foodBookings[i]
            }
        }
        const upcomingItemPromises: Promise<UpcomingItem>[] = []
        _.forEach(bookingUserIds, userId => {
            const bookings = bookingsMap[userId].bookings
            const waitlistBookings = bookingsMap[userId].waitlists
            if (!_.isEmpty(bookings)) {
                _.map(bookings, booking => {
                    const foodBooking: FoodBooking = !_.isNil(foodProductMap) ? foodProductMap[booking.bookingNumber] : undefined
                    upcomingItemPromises.push(this.bookingToUpcomingItem(userId, productType, userContext, booking, interfaces, userMap, foodBooking))
                })
            }
            if (!_.isEmpty(waitlistBookings)) {
                _.map(waitlistBookings, booking => {
                    upcomingItemPromises.push(this.bookingToUpcomingItem(userId, productType, userContext, booking, interfaces, userMap))
                })
            }
        })
        const upcomingItems = await Promise.all(upcomingItemPromises)
        return _.filter(upcomingItems, upcomingItem => !_.isNil(upcomingItem))
    }

    private async bookingToUpcomingItem(userId: string, productType: ProductType,
        userContext: UserContext, booking: CultBooking, interfaces: CFServiceInterfaces,
        userMap: { [userId: string]: User }, foodBooking?: FoodBooking): Promise<UpcomingItem> {
        const workoutId: string = _.get(booking, "Class.Workout.id", "0")
        const isCultScoreWorkout: boolean = CultUtil.isCultScoreWorkout(parseInt(workoutId))
        const cultClassID: string = booking.CultClass.centerID.toString()
        const kioskId: string = interfaces.kiosksDemandService.getKioskIdGivenCenterId(cultClassID)
        const isCafe: boolean = await this.checkIfCafe(userContext, interfaces, booking, cultClassID)
        const productMap = await this.getCafeProductMap(interfaces, foodBooking, isCafe)
        const status: ActivityState = TimelineUtil.getCultStatus(booking)
        const isDroppedOutBooking: boolean = booking.label === "Dropped out"
        if (status === "TODO" || isCultScoreWorkout || booking.state === "PENDING" || booking.state === "REJECTED" || isDroppedOutBooking) {
            const subUserDetail = userId != userContext.userProfile.userId ? userMap[userId] : undefined
            const classStartDate = TimelineHelper.getClassStartDate(userContext, booking)

            const leftImages: string[] = []
            leftImages.push(this.getClassImage(booking))
            if (!_.isNil(foodBooking)) {
                leftImages.push(... this.getCafeFoodImages(foodBooking, productMap))
            }
            let footerType: string = undefined
            if (this.isFooterRequired(isCafe, booking, AppUtil.isCallReminderSupported(userContext))) {
                if (isCafe) {
                    footerType = "CAFE"
                }
                else if (booking.ivrConfig && booking.ivrConfig.isIVRApplicable) {
                    footerType = "IVR"
                }
            }
            const attendingUsers = booking.attendingUsers
            const classLink = booking.classLink
            const useFooterArrayInsteadOfObject = AppUtil.isUpcomingItemFootersArraySupported(userContext)
            const upcomingItem: UpcomingItem = {
                title: TimelineHelper.buildCultTitle(userContext, booking, subUserDetail),
                subTitle: TimelineHelper.buildCultSubTitle(userContext, classStartDate, booking),
                action: !isDroppedOutBooking ? this.buildBookingDetailAction(booking, productType) : undefined,
                leftInfo: {
                    images: leftImages
                },
                rightInfo: {
                    title: this.getRightInfoTitle(booking, userContext),
                    action: this.buildNavigationAction(booking, productType),
                    moreAction: await this.buildMoreActions(userContext, productType, booking, interfaces.productBusiness, foodBooking)
                },
                date: booking.Class.date,
                timestamp: classStartDate.getTime(),
                footer: (!useFooterArrayInsteadOfObject && footerType) ? await this.buildFooter(userContext, booking, kioskId, footerType, productType, productMap, foodBooking) : undefined,
                footers: [],
                meta: undefined,
                buddiesJoining: (await AppUtil.isBuddiesJoiningClassWidgetsSupported(userContext)) && !_.isEmpty(attendingUsers) ?
                    await CultUtil.getBuddiesJoiningListSmallViewOld(attendingUsers, interfaces.userCache, PageTypes.HomeTab, isDroppedOutBooking) as CultBuddiesJoiningListSmallWidget : undefined
            }
            if (productType === "FITNESS") {
                upcomingItem.meta = {
                    referrerSource: "CULT_CLASS",
                    referrerId: booking.bookingNumber
                }
            }
            if (useFooterArrayInsteadOfObject) {
                if (footerType) {
                    upcomingItem.footers.push(await this.buildFooter(userContext, booking, kioskId, footerType, productType, productMap, foodBooking))
                }
                if (AppUtil.isUpcomingItemTooltipSupported(userContext) && !isDroppedOutBooking && booking.state !== "REJECTED") {
                    const footer = await this.buildFooter(userContext, booking, kioskId, "SHARE", productType, productMap, foodBooking, interfaces.classInviteLinkCreator, classLink)
                    if (footer) {
                        upcomingItem.footers.push(footer)
                    }
                }
            }
            return upcomingItem
        }

    }

    private isFooterRequired(isCafe: boolean, booking: CultBooking, isUserEligibleForCallReminder: boolean): boolean {
        return isCafe || (isUserEligibleForCallReminder && booking.ivrConfig && booking.ivrConfig.isIVRApplicable && booking.state !== "REJECTED" && booking.state !== "CANCELLED")
    }

    private getClassImage(booking: CultBooking): string {
        const document = _.find(booking.Class.Workout.documents, document => {
            return document.tagID === 11
        })
        const imageUrl: string = document ? "/" + document.URL : undefined
        return imageUrl
    }

    private getRightInfoTitle(booking: CultBooking, userContext: UserContext): string {
        const isPulseEnabled = CultUtil.isClassAvailableForPulse(booking.CultClass, AppUtil.isCultPulseFeatureSupported(userContext))
        if (booking.label === "Dropped out") {
            return booking.label
        }
        if (isPulseEnabled && !_.isEmpty(booking.pulseDeviceName)) {
            return CultUtil.pulsifyDeviceName(booking)
        }
        if (booking.wlBookingNumber) {
            return booking.state === "PENDING" ? `Waitlisted #${booking.waitlistNumber}` : "Not Confirmed"
        }
        if (!_.isEmpty(booking.info) && !_.isNil(booking.info.signOutOTP)) {
            return `PIN: ${booking.info.signOutOTP}`
        }
    }

    private async buildMoreActions(userContext: UserContext, productType: ProductType, booking: CultBooking, productBusiness: IProductBusiness, foodBooking?: FoodBooking): Promise<Action> {
        const possibleActions: Action[] = []
        let cultManageOptions
        if (booking.wlBookingNumber) {
            cultManageOptions = await productBusiness.getCultWaitlistManageOptions(userContext,
                productType, booking)
        } else {
            cultManageOptions = await productBusiness.getCultManageOptions(userContext,
                productType, booking, undefined, true, false)
        }
        const enabledOptions = cultManageOptions.manageOptions.options.filter((option: ManageOptionPayload) => {
            return option.isEnabled || option.showDisabled
        })
        const timezone = userContext.userProfile.timezone
        if (enabledOptions.length > 0) {
            const cancelClassAction = await ActionUtil.cancelClassAction(enabledOptions, userContext)
            if (cancelClassAction) {
                possibleActions.push(cancelClassAction)
                if (!cancelClassAction.disabled && foodBooking) {
                    possibleActions.push({
                        ...cancelClassAction,
                        meta: { ...cancelClassAction.meta, fulfilmentId: foodBooking.fulfilmentId, date: TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone) },
                        title: "Cancel class + Meal"
                    })
                }

            }
            const dropoutAction = ActionUtil.classDropoutAction(enabledOptions)
            if (dropoutAction) {
                possibleActions.push(dropoutAction)
                if (!dropoutAction.disabled && foodBooking) {
                    possibleActions.push({
                        ...dropoutAction,
                        meta: { ...dropoutAction.meta, fulfilmentId: foodBooking.fulfilmentId, date: TimeUtil.formatDateStringInTimeZone(foodBooking.deliveryDate, timezone) },
                        title: "Dropout + Cancel Meal"
                    })
                }
            }
            const cultScoreLogAction = ActionUtil.cultScoreLogAction(enabledOptions)
            if (cultScoreLogAction) {
                possibleActions.push(cultScoreLogAction)
            }
        }
        if (_.isEmpty(possibleActions)) {
            return undefined
        } else {
            const action: Action = {
                icon: "MANAGE",
                actionType: "ACTION_LIST",
                actions: possibleActions
            }
            return action
        }
    }

    private buildNavigationAction(booking: CultBooking, productType: ProductType): Action {
        const isRunningEvent = booking.Class.Workout.id === RUNNING_EVENT_WORKOUT_ID
        const isUpcomingOrOngoingClass = booking.label === "Upcoming" || booking.label === "Ongoing"
        if (!isRunningEvent && isUpcomingOrOngoingClass) {
            return {
                actionType: "EXTERNAL_DEEP_LINK",
                url: booking.Center.placeUrl,
                title: "NAVIGATE"
            }
        }
    }

    private buildBookingDetailAction(booking: CultBooking, productType: ProductType): Action {
        let action: Action
        if (booking.state === "REJECTED") {
            action = {
                actionType: "SHOW_ALERT_MODAL",
                meta: {
                    title: "",
                    subTitle: "Waitlist was not confirmed due to unavailability of open slots.",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Okay" }]
                }
            }

        } else if (booking.label === "Dropped out") {
            action = undefined
        } else {
            action = {
                actionType: "NAVIGATION",
                url: ActionUtil.getCultMindClassUrl(productType, booking.bookingNumber || booking.wlBookingNumber)
            }
        }
        return action
    }
    private getCafeFoodImages(foodBooking: FoodBooking, productMap: { [productId: string]: Product }): string[] {
        const images: string[] = []
        const productQuantityMap: { [productId: string]: number } = {}
        for (const j in foodBooking.products) {
            if (_.isNil(productQuantityMap[foodBooking.products[j].productId])) {
                productQuantityMap[foodBooking.products[j].productId] = 0
            }
            const quantity: number = 1
            productQuantityMap[foodBooking.products[j].productId] += quantity
        }
        for (const productId in productMap) {
            const product: Product = productMap[productId]
            const url: string = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, "FOOD", "THUMBNAIL", product.imageVersion)
            let quantity: number = !_.isNil(productQuantityMap[productId]) ? productQuantityMap[productId] : 0
            while (quantity > 0) {
                images.push(url)
                quantity--
            }
        }
        return images
    }
    private async buildFooter(userContext: UserContext, booking: CultBooking, kioskId: string, footerType: string, productType: ProductType, productMap: { [productId: string]: Product }, foodBooking?: FoodBooking, classInviteLinkCreator?: ClassInviteLinkCreator, classLink?: string) {
        let title: string
        let subTitle: string
        let icon: string
        let action: Action
        if (footerType === "CAFE") {
            if (_.isNil(foodBooking)) {
                const tz = userContext.userProfile.timezone
                title = "Supplement your workout"
                subTitle = undefined
                action = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.getEatClpUrl(kioskId, booking, tz, true),
                }
                icon = "ADD_BLACK"
            }
            else {
                const menuType: MenuType = "ALL"
                const text: string = (foodBooking.products.length === 1) ? "item Ordered" : "items Ordered"
                title = "Cult Cafe - " + `${foodBooking.products.length} ` + text
                subTitle = undefined
                const seoParams: SeoUrlParams = {
                    productName: productMap[foodBooking.productId].title
                }
                action = {
                    actionType: "NAVIGATION",
                    url: (foodBooking.products.length === 1) ? FoodUtil.foodSingle(foodBooking.productId, foodBooking.deliveryDate, menuType, false, foodBooking.fulfilmentId, userContext.sessionInfo.userAgent, seoParams) : FoodUtil.foodCart(foodBooking.fulfilmentId, <MealSlot>"ALL", foodBooking.deliveryDate)
                }
                icon = undefined
            }
        } else if (footerType === "IVR") {
            title = "Call Reminder:"
            subTitle = booking.ivrConfig.status ? "ON" : "OFF"
            action = {
                actionType: "NAVIGATION",
                url: ActionUtil.getCultMindClassUrl(productType, booking.bookingNumber || booking.wlBookingNumber)
            }
            icon = "RIGHT_ARROW"
        } else if (footerType === "SHARE") {
            action = CultUtil.getLazyInviteLinkAction(userContext, productType, booking)
            if (!action) {
                return undefined
            }
            title = "Invite buddies on WhatsApp"
            subTitle = undefined
            icon = "WHATSAPP_SHARE"
        }
        return { title, subTitle, action, icon }
    }
    private async checkIfCafe(userContext: UserContext, interfaces: CFServiceInterfaces, booking: CultBooking, cultClassID: string): Promise<boolean> {
        const cultClass: CultClass = booking.Class
        const startTime: string = cultClass.startTime
        const endTime: string = cultClass.endTime
        const start: string[] = startTime.split(":")
        const end: string[] = endTime.split(":")
        const startEndTime: StartEndTime = {
            startingHours: { hour: parseInt(start[0]), min: parseInt(start[1]) },
            closingHours: { hour: parseInt(end[0]), min: parseInt(end[1]) }
        }
        const showAddSnackWidget: boolean = await interfaces.kiosksDemandService.shouldShowAddSnackWidget(cultClassID, cultClass.date, startEndTime)
        const kioskId = await interfaces.kiosksDemandService.getKioskIdGivenCenterId(cultClassID)
        const areaForCafe = kioskId ? await interfaces.deliveryAreaService.findAreaForKiosk(kioskId) : undefined
        const hasCurrencySupport = (!_.isNil(areaForCafe) && areaForCafe.cityId === DUBAI_ACTUAL_CITY_ID) ? AppUtil.isEatCurrencySupported(userContext) : true
        const isDateValidForAreaId = !areaForCafe ? true : interfaces.menuService.isDateValidForAreaId(cultClass.date, areaForCafe.areaId)
        return showAddSnackWidget && _.isNil(booking.wlBookingNumber) && isDateValidForAreaId && hasCurrencySupport
    }
    private async getCafeProductMap(interfaces: CFServiceInterfaces, foodBooking: FoodBooking, isCafe: boolean): Promise<{ [productId: string]: Product }> {
        let productMap: { [productId: string]: Product }
        if (isCafe && !_.isNil(foodBooking)) {
            const productIds: string[] = []
            for (const j in foodBooking.products) {
                const productId: string = foodBooking.products[j].productId
                productIds.push(productId)
            }
            productMap = await interfaces.catalogueService.getProductMap(productIds)
        }

        return productMap
    }
}
