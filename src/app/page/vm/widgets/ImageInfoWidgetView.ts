import { IBaseWidget, ImageInfoWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { GymfitCenter, GymFitCenterSearchFilters, MediaType } from "@curefit/gymfit-common"
import { Movement } from "@curefit/fitness-common"
import { AWS_S3_BASE_URL } from "../../../util/AppUtil"


export class ImageInfoWidgetView extends ImageInfoWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_NEARBY_GYMS") {
            return new GymfitNearbyGymsView(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "GYMFIT_WORKOUT") {
            return new GymfitWorkoutWidgets(this).buildView(interfaces)
        }
    }
}

class GymfitNearbyGymsView extends ImageInfoWidget {

    constructor(imageInfoWidget: ImageInfoWidget) {
        super()
        this.header = imageInfoWidget.header
        this.productType = imageInfoWidget.productType
        this.cards = imageInfoWidget.cards
        this.cardType = imageInfoWidget.cardType
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const cityId = userContext.sessionInfo.sessionData.cityId
        const userId = userContext.userProfile.userId
        const searchParams: GymFitCenterSearchFilters = {
            sortBy: "DISTANCE",
            latitude: userContext.sessionInfo.lat,
            longitude: userContext.sessionInfo.lon,
            pageNumber: 1,
            pageSize: 5,
            cityId,
            userId
        }
        const gymfitCenters: GymfitCenter[] = await interfaces.gymfitService.searchCenter(cityId, searchParams, userId, true)
        this.cardType = "LARGE"
        this.cards = gymfitCenters.map(center => {
            const logo = (center.media && center.media.clpMedia ? center.media.clpMedia.find(media => media.type === MediaType.IMAGE) : undefined)
            const image = (center.media && center.media.heroMedia ? center.media.heroMedia.find(media => media.type === MediaType.IMAGE) : undefined)
            return {
               title: center.name,
                subTitle: center.locality,
                imageUrl: image ? image.mediaUrl : undefined,
                logoUrl: logo ? logo.mediaUrl : undefined,
                action: undefined
            }
        })
        return this
    }
}

class GymfitWorkoutWidgets extends ImageInfoWidget {

    constructor(imageInfoWidget: ImageInfoWidget) {
        super()
        this.header = imageInfoWidget.header
        this.productType = imageInfoWidget.productType
        this.cards = imageInfoWidget.cards
        this.cardType = imageInfoWidget.cardType
        this.showDivider = true
    }

    async buildView(interfaces: CFServiceInterfaces): Promise<IBaseWidget> {
        const allMovements: Movement[] = await interfaces.herculeService.searchMovements(["GYMFIT"])
        this.cardType = "LARGE"
        if (allMovements) {
            const size = allMovements.length > 5 ? 5 : allMovements.length
            this.cards = allMovements.slice(0, size).map(movement => {
                const imageMedia = movement.media ? movement.media.find(media => media.type === "IMAGE") : null
                const videoMedia = movement.media ? movement.media.find(media => media.type === "VIDEO") : null
                return {
                    title: movement.title,
                    subTitle: movement.bodyParts.join(", "),
                    imageUrl: imageMedia ? AWS_S3_BASE_URL + imageMedia.url : null,
                    action: {
                        actionType: "NAVIGATION",
                        url: `curefit://videoplayer?videoUrl=${videoMedia ? videoMedia.url : null}`
                    }
                }
            })
        }
        return this
    }
}
