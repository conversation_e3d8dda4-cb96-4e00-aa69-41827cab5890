import { IBaseWidget, TimerWidgetV2, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { UserOfferEligibilityResponse } from "@curefit/offer-common"
import { pluralizeStringIfRequired } from "@curefit/util-common"

export class TimerWidgetV2View extends TimerWidgetV2 {
    roundedCorners: boolean = true

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {

        if (this.data.timerEndTimeWithTz) {
            this.data.timerEndTime = new Date(this.data.timerEndTimeWithTz.date.toString()).getTime()
            if (this.data.timerEndTime < new Date().getTime()) {
                return undefined
            }
        } else if (this.data.privateOfferId) {
            const eligibility: UserOfferEligibilityResponse = await interfaces.offerService.getOfferEligibilityForUser(this.data.privateOfferId, userContext.userProfile.userId, "USERID")
            if (eligibility && eligibility.status == "OK") {
                const timerEndTime: number = eligibility.userOfferEligibility.endDate.getTime()
                if (timerEndTime > new Date().getTime()) {
                    this.data.timerEndTime = timerEndTime
                } else {
                    // private offer has expired for user
                    return undefined
                }
            } else {
                // no private offer active for user
                return undefined
            }
        } else {
            // incorrect configuration, one of offerId or timerEndTime must be available
            return undefined
        }

        if (this.data.timerEndTime) {
            const diffInMilis = this.data.timerEndTime - new Date().getTime()
            const days = Math.floor(diffInMilis / (1000 * 60 * 60 * 24))
            const hours = Math.floor(diffInMilis / (1000 * 60 * 60)) % 24
            if (days > 0) {
                this.data.timerTimeText = `${days}${pluralizeStringIfRequired(" day", days)} ${hours} hours`
            }
        }
        return this
    }
}

export default TimerWidgetV2View
