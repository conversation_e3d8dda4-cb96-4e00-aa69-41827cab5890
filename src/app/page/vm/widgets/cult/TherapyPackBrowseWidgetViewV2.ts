import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import * as _ from "lodash"
import { ConsultationProduct, DiagnosticProductResponse } from "@curefit/care-common"
import { ProductPrice, Product } from "@curefit/product-common"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { BaseWidget, IServiceInterfaces, UserContext, Action, CareWidgetUtil, TherapyPackBrowseWidgetV2, CLPTherapyBrowseItem } from "@curefit/vm-models"
import AppUtil from "../../../../util/AppUtil"
import { CareUtil } from "../../../../util/CareUtil"

export class TherapyPackBrowseWidgetViewV2 extends TherapyPackBrowseWidgetV2 {

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<BaseWidget> {
        const { userProfile, sessionInfo } = userContext

        if (_.isEmpty(this.items)) {
            return undefined
        }

        const itemPromises: Promise<CLPTherapyBrowseItem> [] = _.map(this.items, async item => {

            let minPricedProduct: Product
            let minProductOfferAndPrice: {
                price: ProductPrice;
                offers: OfferV2[];
            }
            let minPricedConsulationProduct: Product
            let minPricedBundleProduct: Product
            let consultationProductOffers: PackOffersResponse
            let bundleOffers: PackOffersResponse
            let minPricedBundleProductResponse: DiagnosticProductResponse
            let isMinPricedProductIsBundle

            if (item.groupType) {
                const consultationSellableProductResponse = await interfaces.healthfaceService.getConsultationSellableProducts(userProfile.cityId, item.groupType)
                if (consultationSellableProductResponse.consultationTypes.length === 0) {
                    return undefined
                }
                const { products: consultationSellableProducts } = consultationSellableProductResponse.consultationTypes[0]
                const productCodes = consultationSellableProducts.map(product => product.code)
                consultationProductOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "CONSULTATION",
                    productCodes,
                    "CUREFIT_APP",
                    interfaces,
                    true
                )
                const consultationProducts = await interfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
                minPricedConsulationProduct = _.minBy(consultationProducts, product => {
                    const offerDetails = OfferUtil.getPackOfferAndPrice(product, consultationProductOffers)
                    return Math.ceil(offerDetails.price.listingPrice)
                })
            }

            if (item.subCategoryCode && item.setCode && item.clubCode) {
                const bundleProductResponses: DiagnosticProductResponse[] = await CareWidgetUtil.getCareProductFromUserContext(
                    userContext,
                    interfaces,
                    "BUNDLE",
                    item.subCategoryCode,
                    item.setCode,
                    item.clubCode
                )
                if (!_.isEmpty(bundleProductResponses)) {
                    bundleOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                        userContext,
                        "BUNDLE",
                        bundleProductResponses.map(product => product.productCode),
                        AppUtil.callSourceFromContext(userContext),
                        interfaces,
                        true
                    )
                    minPricedBundleProductResponse = _.minBy(bundleProductResponses, bundleProductResponse => {
                        const diagnosticProduct = CareUtil.toDiagnosticsProduct(bundleProductResponse)
                        const offerDetails = OfferUtil.getPackOfferAndPrice(diagnosticProduct, bundleOffers)
                        return bundleProductResponse?.infoSection?.numberOfSessions
                            ? Math.ceil(offerDetails.price.listingPrice / Number(bundleProductResponse.infoSection.numberOfSessions))
                            : Math.ceil(offerDetails.price.listingPrice)
                    })
                    minPricedBundleProduct = CareUtil.toDiagnosticsProduct(minPricedBundleProductResponse)
                }
            }

            if (minPricedConsulationProduct && minPricedBundleProduct) {
                const minConsulationPriceAndOffer = OfferUtil.getPackOfferAndPrice(minPricedConsulationProduct, consultationProductOffers)
                const minBundlePriceAndOffer = OfferUtil.getPackOfferAndPrice(minPricedBundleProduct, bundleOffers)
                const bundleListingPrice = minPricedBundleProductResponse?.infoSection?.numberOfSessions
                    ? Math.ceil(minBundlePriceAndOffer.price.listingPrice / Number( minPricedBundleProductResponse?.infoSection?.numberOfSessions))
                    : minBundlePriceAndOffer.price.listingPrice
                const isConsultationLowPricedProduct = minConsulationPriceAndOffer.price.listingPrice < bundleListingPrice
                isMinPricedProductIsBundle = !isConsultationLowPricedProduct
                minPricedProduct = isConsultationLowPricedProduct ? minPricedConsulationProduct : minPricedBundleProduct
                minProductOfferAndPrice = isConsultationLowPricedProduct ? minConsulationPriceAndOffer : minBundlePriceAndOffer
            } else if (minPricedConsulationProduct) {
                minPricedProduct = minPricedConsulationProduct
                minProductOfferAndPrice = OfferUtil.getPackOfferAndPrice(minPricedConsulationProduct, consultationProductOffers)
            } else if (minPricedBundleProduct) {
                minPricedProduct = minPricedBundleProduct
                isMinPricedProductIsBundle = true
                minProductOfferAndPrice = OfferUtil.getPackOfferAndPrice(minPricedBundleProduct, bundleOffers)
            }

            if (minPricedProduct) {
                const offers = _.map(minProductOfferAndPrice.offers, (offer: OfferV2) => {
                    if (offer.displayContexts?.includes("NONE") || !offer.description) return undefined
                    return offer
                })
                let mrp, listingPrice
                if (isMinPricedProductIsBundle && minPricedBundleProductResponse?.infoSection?.numberOfSessions) {
                    listingPrice = Math.ceil(minProductOfferAndPrice.price.listingPrice / Number(minPricedBundleProductResponse.infoSection.numberOfSessions))
                    mrp = Math.ceil(minProductOfferAndPrice.price.mrp / Number(minPricedBundleProductResponse.infoSection.numberOfSessions))
                } else {
                    mrp = minProductOfferAndPrice.price.mrp
                    listingPrice = minProductOfferAndPrice.price.listingPrice
                }
                const packItem: CLPTherapyBrowseItem = {
                    ...item,
                    title: item.title || minPricedProduct.subTitle,
                    durationText: item.durationText || (_.get(minPricedProduct, "duration") ? `${_.get(minPricedProduct, "duration") / 60000} minutes session` : undefined),
                    imageUrl: item.imageUrl || minPricedProduct.imageUrl,
                    currency: minProductOfferAndPrice.price.currency || "INR",
                    price: mrp,
                    discountPrice: listingPrice,
                    pricePrefix: "Starting from",
                    priceSuffix: "/session",
                    action: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.carefitservice(
                            item.subCategoryCode,
                            minPricedProduct.productId,
                            item.groupType,
                            undefined,
                            item.setCode,
                            item.clubCode,
                            sessionInfo.userAgent,
                            (minPricedProduct as any)?.doctorType
                        )
                    },
                    offers: offers.filter(offer => !_.isNil(offer)),
                }
                return packItem
            }
            return undefined
        })

        this.items = await Promise.all<CLPTherapyBrowseItem>(itemPromises)
        this.items = this.items.filter(item => !_.isEmpty(item))
        if (_.isEmpty(this.items)) {
            return undefined
        }
        return this
    }
}
