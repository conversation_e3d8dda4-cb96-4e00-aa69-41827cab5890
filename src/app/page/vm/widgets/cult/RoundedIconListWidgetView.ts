import {  IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { RoundedIconItems, RoundedIconListWidget as RoundedIconListWidgetApp } from "@curefit/apps-common"
import { RoundedIconListWidget } from "@curefit/vm-models"
import { LivePTGoal } from "@curefit/cult-common"
import { ICultServiceOld } from "@curefit/cult-client"
import { ProductType } from "@curefit/product-common"
export class RoundedIconListWidgetView extends RoundedIconListWidget {
    items: RoundedIconItems[] = []
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const widget: RoundedIconListWidgetApp = await this.getWidgetView(userContext, this.productType, interfaces.cultFitService)
        this.items = widget.items
        return this
    }

    public async getWidgetView(userContext: UserContext, productType: ProductType, cultFitService: ICultServiceOld): Promise<RoundedIconListWidgetApp> {
        const goals: LivePTGoal[] = await cultFitService.getAllLivePTGoals(userContext.userProfile.userId, "CUREFIT_APP")
        const isLiveSGT = productType === "LIVE_SGT"
        const goalItems: RoundedIconItems[] = goals.map(goal => {
            return {
                icon: goal.imageUrl,
                action: {
                    actionType: "NAVIGATION",
                    url: isLiveSGT ? "curefit://userform?formId=LIVE_SGT_ONBOARDING" : "curefit://userform?formId=LIVE_PT_ONBOARDING_2"
                },
                title: goal.name
            }
        })
        return {
            widgetType: "ROUNDED_ICON_LIST_WIDGET",
            header: {
                title: "Set a Goal"
            },
            items: goalItems
        }
    }
}
