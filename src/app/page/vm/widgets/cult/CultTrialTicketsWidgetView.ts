import { CultTrialTicketsWidget, IBaseWidget, RoundedImageGridWidget, } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { Action, CultTrialTicketDetail, CultTrialTicketItem, CultTrialTicketState } from "@curefit/apps-common"
import { CFUserProfile } from "../../CFUserProfile"
import { CultBooking, CultSummary, MembershipStates } from "@curefit/cult-common"
import { TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { appendZeroInSingleDigitNumber } from "../../../../util/StringUtil"
import { ConsultationProduct, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { CareUtil, LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../../../util/CareUtil"
import { BookingDetail, TrialMembershipInfo } from "@curefit/albus-client"
import { ActiveConsultationResponse } from "@curefit/albus-client/dist/src/models"
import { UserMembershipsAndTrialUsages, GymfitStatus, TrialUsage } from "@curefit/gymfit-common"
import GymfitUtil from "../../../../util/GymfitUtil"
import { ComplimentaryAccessMembership } from "@curefit/cult-client"

export class CultTrialTicketsWidgetView extends CultTrialTicketsWidget {

    initialIndex: number = 0
    items: CultTrialTicketItem[]

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_FITNESS_PRODUCT") {
            return new GymFitTrialTicketsWidgetView(this).buildView(interfaces, userContext, queryParams)
        }
        if (this.productType === "LIVE_PERSONAL_TRAINING" || this.productType === "LIVE_SGT") {
            return new LivePTTrialTicketsWidgetView(this).buildView(interfaces, userContext, queryParams)
        }
        const userProfile = userContext.userProfile as CFUserProfile
        if (!userProfile.city.isCultAvailableForBooking) {
            return undefined
        }
        const baseService = this.productType === "FITNESS" ? interfaces.cultFitService : interfaces.mindFitService
        const trialEligibilityResponse = await baseService.getUserTrialEligibility(userProfile.userId, "CUREFIT_API")
        const cultTrialEligibility = trialEligibilityResponse.trialEligibility
        const cultTrialBookings = trialEligibilityResponse.trialBookings
        const upcomingBookings = _.filter(cultTrialBookings, trialBooking => trialBooking.label === "Upcoming") || []
        const hasUpcomingTrialBooking = !_.isEmpty(upcomingBookings)
        // experiment for widget with single ticket
        if (cultTrialEligibility.isEligible && cultTrialEligibility.consumptionCount === 0 && this.numberOfTicketsOnTheScreen === 1) {
            const ticketItem: CultTrialTicketItem = {
                trialNumber: `FREE TRIAL`,
                ticketDetails: this.getCultTrialNotUsedTicket(),
                backgroundColor: (this.ticketConfig && this.ticketConfig.availableTicketColor) || "#414152",
                contentMetric: {
                    contentId: "FREE_TRIAL_SINGLE"
                }
            }
            this.items = [ticketItem]
        } else {
            if ((cultTrialEligibility.isEligible || hasUpcomingTrialBooking) && cultTrialEligibility.maxCount > 0) {
                this.numberOfTicketsOnTheScreen = 2
                this.items = []
                let trialTicketCount = 0
                if (cultTrialEligibility.consumptionCount > 0 ) {
                    for (let i = 0; i < cultTrialEligibility.consumptionCount - upcomingBookings.length; i++) {
                        // first place all completed trial booking tickets
                        trialTicketCount++
                        const ticketItem: CultTrialTicketItem = {
                            trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                            ticketDetails: this.getCultTrialRedeemedTicket(),
                            backgroundColor: (this.ticketConfig && this.ticketConfig.usedTicketColor) || "#cacaca",
                            contentMetric: {
                                contentId: "FREE_TRIAL_REDEEMED"
                            }
                        }
                        this.items.push(ticketItem)
                    }

                    // all upcoming booking trial tickets
                    _.map(upcomingBookings, (trialBooking: CultBooking) => {
                        trialTicketCount++
                        const ticketItem: CultTrialTicketItem = {
                            trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                            ticketDetails: this.getCultTrialBookedTicket(trialBooking, userContext),
                            backgroundColor: (this.ticketConfig && this.ticketConfig.bookedTicketColor) || "#5bdbb6",
                            contentMetric: {
                                contentId: "FREE_TRIAL_BOOKED"
                            }
                        }
                        this.items.push(ticketItem)
                    })

                }
                // remaining tickets
                if (cultTrialEligibility.maxCount > cultTrialEligibility.consumptionCount) {
                    this.initialIndex = trialTicketCount
                    for (let i = 0; i < cultTrialEligibility.maxCount - cultTrialEligibility.consumptionCount; i++) {
                        trialTicketCount++
                        const ticketItem: CultTrialTicketItem = {
                            trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                            ticketDetails: this.getCultTrialNotUsedTicket(),
                            backgroundColor: (this.ticketConfig && this.ticketConfig.availableTicketColor) || "#414152",
                            contentMetric: {
                                contentId: "FREE_TRIAL_REMAINING"
                            }
                        }
                        this.items.push(ticketItem)
                    }
                }
            }
        }
        if (_.isEmpty(this.items)) {
            return undefined
        }
        // to show all tickets if only 2 tickets present
        if (this.items.length === 2) {
            this.initialIndex = 0
        }
        return this
    }


    private getCultTrialBookedTicket(bookingDetail: CultBooking, userContext: UserContext): CultTrialTicketDetail {
        if (bookingDetail.label === "Upcoming") {
            const tz = userContext.userProfile.timezone
            const cultClass = bookingDetail.CultClass
            const classStartTime = TimeUtil.formatDateStringInTimeZone(
                cultClass.date + " " + cultClass.startTime,
                tz,
                "h:mm A"
            )
            const subTitle = `${TimeUtil.formatDateStringInTimeZone(
                cultClass.date,
                tz,
                "ddd D MMM"
            )}, ${classStartTime}\n${cultClass.Center.name}`
            return {
                ticketState: CultTrialTicketState.CLASS_BOOKED,
                meta: {
                    title: cultClass.Workout.name,
                    subTitle,
                    cardAction: {
                        actionType: "NAVIGATION",
                        url: ActionUtil.cultFitBooking(bookingDetail.bookingNumber)
                    }
                }
            }
        }
    }

    private getCultTrialNotUsedTicket(): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.NOT_USED,
            meta: {
                buttonAction: {
                    actionType: "NAVIGATION",
                    title: "TRY FOR FREE",
                    url: ActionUtil.getBookCultClassUrl("FITNESS", true, "trialWidget")
                }
            }
        }
    }

    private getCultTrialRedeemedTicket(): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.TICKET_REDEEMED,
            meta: {
                title: "Redeemed"
            }
        }
    }
}

class LivePTTrialTicketsWidgetView extends CultTrialTicketsWidget {
    initialIndex: number = 0
    items: CultTrialTicketItem[]
    constructor(widget: CultTrialTicketsWidget) {
        super()
        this.productType = widget.productType
        this.header = widget.header
        this.ticketConfig = widget.ticketConfig
        this.numberOfTicketsOnTheScreen = widget.numberOfTicketsOnTheScreen
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const subCategoryCode: SUB_CATEGORY_CODE = this.productType === "LIVE_SGT" ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING"
        const userTrialEligibilityItems: TrialMembershipInfo[] = await interfaces.cultPTService.getUserTrialEligibility(Number(userContext.userProfile.userId), "CONSULTATION", subCategoryCode, "CULTFIT")
        if (_.isEmpty(userTrialEligibilityItems) || userTrialEligibilityItems[0].totalTickets === 0) {
            return undefined
        }
        const userTrialEligibility =  userTrialEligibilityItems[0]
        const totalTickets = userTrialEligibility.totalTickets
        const totalTicketsConsumed = userTrialEligibility.totalTicketsConsumed
        const productId = subCategoryCode === "LIVE_PERSONAL_TRAINING" ? LIVE_PT_SNC_PRODUCT_ID : LIVE_SGT_SNC_PRODUCT_ID
        const sessionBookAction = await interfaces.careBusiness.getLivePTSessionBookAction(userContext, {
            productId,
            actionTitle: "BOOK NOW",
            subCategoryCode
        })
        if (this.numberOfTicketsOnTheScreen === 1 && totalTicketsConsumed === 0) {
            const ticketItem: CultTrialTicketItem = {
                trialNumber: `FREE TRIAL`,
                ticketDetails: this.getPTTrialNotUsedTicket(sessionBookAction),
                backgroundColor: (this.ticketConfig && this.ticketConfig.availableTicketColor) || "#414152",
                contentMetric: {
                    contentId: `${this.productType}_FREE_TRIAL_SINGLE`
                }
            }
            this.items = [ticketItem]
        } else {
            this.numberOfTicketsOnTheScreen = 2
            this.items = []
            let trialTicketCount = 0
            const upcomingBookings = !_.isEmpty(userTrialEligibility.activeConsultations) ? userTrialEligibility.activeConsultations : []
            const redeemedTicketsCount = totalTicketsConsumed - upcomingBookings.length
            if (totalTicketsConsumed > 0) {
                for (let i = 0; i < redeemedTicketsCount; i++) {
                    // first place all completed trial booking tickets
                    trialTicketCount++
                    const ticketItem: CultTrialTicketItem = {
                        trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                        ticketDetails: this.getPTTrialRedeemedTicket(),
                        backgroundColor: (this.ticketConfig && this.ticketConfig.usedTicketColor) || "#cacaca",
                        contentMetric: {
                            contentId: `${this.productType}_FREE_TRIAL_REDEEMED`
                        }
                    }
                    this.items.push(ticketItem)
                }
                // all upcoming booking trial tickets
                await Promise.all(_.map(upcomingBookings, async (trialBooking: ActiveConsultationResponse) => {
                    trialTicketCount++
                    const ticketItem: CultTrialTicketItem = {
                        trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                        ticketDetails: await this.getPTTrialBookedTicket(trialBooking, userContext, interfaces),
                        backgroundColor: (this.ticketConfig && this.ticketConfig.bookedTicketColor) || "#5bdbb6",
                        contentMetric: {
                            contentId: `${this.productType}_FREE_TRIAL_BOOKED`
                        }
                    }
                    this.items.push(ticketItem)
                }))
            }
            // remaining tickets
            if (totalTickets > totalTicketsConsumed) {
                this.initialIndex = trialTicketCount
                for (let i = 0; i < totalTickets - totalTicketsConsumed; i++) {
                    trialTicketCount++
                    const ticketItem: CultTrialTicketItem = {
                        trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                        ticketDetails: await this.getPTTrialNotUsedTicket(sessionBookAction),
                        backgroundColor: (this.ticketConfig && this.ticketConfig.availableTicketColor) || "#414152",
                        contentMetric: {
                            contentId: `${this.productType}_FREE_TRIAL_REMAINING`
                        }
                    }
                    this.items.push(ticketItem)
                }
            }
        }
        if (_.isEmpty(this.items)) {
            return undefined
        }
        // to show all tickets if only 2 tickets present
        if (this.items.length === 2) {
            this.initialIndex = 0
        }
        return this
    }

    private getPTTrialNotUsedTicket(sessionBookAction: Action): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.NOT_USED,
            meta: {
                buttonAction: sessionBookAction
            }
        }
    }

    private getPTTrialRedeemedTicket(): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.TICKET_REDEEMED,
            meta: {
                title: "Redeemed"
            }
        }
    }

    private async getPTTrialBookedTicket(bookingDetail: ActiveConsultationResponse, userContext: UserContext, interfaces: CFServiceInterfaces): Promise<CultTrialTicketDetail> {
        const tz = userContext.userProfile.timezone
        const classStartTime = TimeUtil.formatEpochInTimeZone(tz, bookingDetail.startDate, "D MMM, h:mm A")
        const product = <ConsultationProduct>await interfaces.catalogueService.getProduct(bookingDetail.productCode)
        const vertical = CareUtil.getVerticalForConsultation(product?.doctorType)
        const actionUrl = ActionUtil.teleconsultationSingle(userContext, bookingDetail.productCode, product.urlPath, bookingDetail.bookingId.toString(), undefined, vertical)
        return {
            ticketState: CultTrialTicketState.CLASS_BOOKED,
            meta: {
                title: bookingDetail.consultationProduct.productSpecs.cultWorkoutName,
                subTitle: classStartTime,
                cardAction: {
                    actionType: "NAVIGATION",
                    url: actionUrl
                }
            }
        }
    }
}

class GymFitTrialTicketsWidgetView extends CultTrialTicketsWidget {
    initialIndex: number = 0
    items: CultTrialTicketItem[]
    constructor(widget: CultTrialTicketsWidget) {
        super()
        this.productType = widget.productType
        this.header = widget.header
        this.ticketConfig = widget.ticketConfig
        this.numberOfTicketsOnTheScreen = widget.numberOfTicketsOnTheScreen
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const { membershipSummary: { current: { cult } } }: CultSummary = await userContext.userProfile.promiseMapCache.getPromise("cult-mind-summary", { userId: userContext.userProfile.userId })
        const complimentaryMemberships: ComplimentaryAccessMembership[] = await userContext.userProfile.promiseMapCache.getPromise("cult-complimentary-access", { userId: userContext.userProfile.userId })
        const hasComplimentaryAccess = GymfitUtil.hasComplimentaryAccess(userContext, complimentaryMemberships)
        if (hasComplimentaryAccess || (cult && (cult.state === MembershipStates.ACTIVE || cult.state === MembershipStates.PAUSED))) {
            return undefined
        }
        const trialDetails: TrialUsage = await interfaces.gymfitService.getTrialUsage(userContext.userProfile.userId, userContext.sessionInfo.deviceId)
        if (trialDetails?.status === GymfitStatus.ACTIVE) {
            if (trialDetails.used === trialDetails.maxCount) {
                return undefined
            }
            const futureBookings = trialDetails.futureBookingCount ?? 0
            const ticketConsumedCount = trialDetails.used + futureBookings
            if (trialDetails.used < trialDetails.maxCount) {
                this.items = []
                let trialTicketCount = 0
                for (; trialTicketCount < ticketConsumedCount;) {
                    trialTicketCount++
                    const ticketItem: CultTrialTicketItem = {
                        trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                        ticketDetails: this.getGymFitTrialRedeemedTicket(),
                        backgroundColor: (this.ticketConfig && this.ticketConfig.usedTicketColor) || "#cacaca",
                        contentMetric: {
                            contentId: `${this.productType}_FREE_TRIAL_REDEEMED`
                        }
                    }
                    this.items.push(ticketItem)
                }
                for (; trialTicketCount < trialDetails.maxCount;) {
                    trialTicketCount++
                    const ticketItem: CultTrialTicketItem = {
                        trialNumber: `${appendZeroInSingleDigitNumber(trialTicketCount)}`,
                        ticketDetails: this.getGymFitTrialNotUsedTicket(),
                        backgroundColor: (this.ticketConfig && this.ticketConfig.availableTicketColor) || "#414152",
                        contentMetric: {
                            contentId: `${this.productType}_FREE_TRIAL_REMAINING`
                        }
                    }
                    this.items.push(ticketItem)
                }
                return this
            }
        }
        return undefined
    }

    private getGymFitTrialNotUsedTicket(): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.NOT_USED,
            meta: {
                buttonAction: {
                    actionType: "NAVIGATION",
                    title: "TRY FOR FREE",
                    url: "curefit://allgyms?CTA=TRY FOR FREE&centerType=GYM"
                }
            }
        }
    }

    private getGymFitTrialRedeemedTicket(): CultTrialTicketDetail {
        return {
            ticketState: CultTrialTicketState.TICKET_REDEEMED,
            meta: {
                title: "Redeemed"
            }
        }
    }
}
