import { IBaseWidget, PtGoalSummaryWidget, UserContext } from "@curefit/vm-models"
import {
    Action,
    ActionType,
    Activity,
    ActivityListWidget,
    FitnessDetails,
    InfoActionWidget,
    PageTypes,
    PTGoalSummaryWidget,
    WidgetView
} from "@curefit/apps-common"
import { LivePTUserProfile, UserScoresAndState } from "@curefit/cult-common"
import * as _ from "lodash"
import { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../../../util/CareUtil"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import {
    CareTeam,
    ConsultationSellableProduct,
    HealthfaceProductInfo,
    IHealthfaceService,
    LivePTProductSpec
} from "@curefit/albus-client"
import { ICultServiceOld } from "@curefit/cult-client"
import { CareBusiness, ICareBusiness } from "../../../../care/CareBusiness"
import { ProductType } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"

export class PtGoalSummaryWidgetView extends PtGoalSummaryWidget {
    fitnessDetails: FitnessDetails[] = []
    footerWidget?: WidgetView
    cardAction?: Action
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const ptGoalSummaryWidget: PTGoalSummaryWidget = await this.getWidgetView(userContext, this.productType, interfaces.cultFitService, interfaces.healthfaceService, interfaces.careBusiness)
        if (!_.isEmpty(ptGoalSummaryWidget)) {
            return this
        }
    }

    public async getWidgetView(userContext: UserContext, productType: ProductType, cultFitService: ICultServiceOld, healthfaceService: IHealthfaceService, careBuisness: ICareBusiness): Promise<PTGoalSummaryWidget> {
        const userGoal: LivePTUserProfile = await cultFitService.getLivePTUserProfile(userContext.userProfile.userId, "CUREFIT_APP")
        if (_.isEmpty(userGoal)) {
            return undefined
        }
        this.header = {
            title: "Fitness Journey",
            seemore: {
                actionType: "NAVIGATION",
                url: `curefit://liveptgoal?goalId=${userGoal.goalId}&productType=${productType}`
            }
        }

        this.cardAction = {
            actionType: "NAVIGATION",
            url: `curefit://liveptgoal?goalId=${userGoal.goalId}&productType=${productType}`
        }

        this.fitnessDetails.push({
            icon: userGoal.imageUrl,
            title: "GOAL",
            subTitle: userGoal.goal,
        })

        const assessment: UserScoresAndState = await cultFitService.getLivePTUserAssessmentScore(userContext.userProfile.userId, "CUREFIT_APP")

        if (assessment.state === "CLASS_NOT_BOOKED") {
            if (productType === "LIVE_PERSONAL_TRAINING") {
                this.footerWidget = await this.getInfoFooterWidgetWithAction(userContext, productType, careBuisness)
            }
        } else if (assessment.state === "CLASS_NOT_STARTED" || assessment.state === "ASSESSMENT_NOT_FILLED") {
            if (productType === "LIVE_PERSONAL_TRAINING") {
                this.footerWidget = await this.getInfoFooterWidgetWithoutAction(assessment.message)
            }
        } else if (assessment.state == "ASSESSMENT_AVAILABLE") {
            const goingWell = _.join(_.map(assessment.userScores.goingWell.qualities, quality => quality.name), ", ")
            const improvmentsNeeded = _.join(_.map(assessment.userScores.improveAt.qualities, quality => quality.name), ", ")
            if (!_.isEmpty(goingWell)) {
                this.fitnessDetails.push({
                    icon: "/image/icons/going_well.png",
                    title: "WHAT'S GOING WELL",
                    subTitle: goingWell,
                })
            }
            if (!_.isEmpty(improvmentsNeeded)) {
                this.fitnessDetails.push({
                    icon: "/image/icons/improvement.png",
                    title: "GET STRONGER AT",
                    subTitle: improvmentsNeeded,
                })
            }
            // this.footerWidget = await this.getActivityListWidget(userContext, productType, careBuisness, healthfaceService)
        }
        return {
            widgetType: "PT_GOAL_SUMMARY_WIDGET",
            header: {
                title: this.header.title,
                // @ts-ignore
                seemore: this.header.seemore
            },
            fitnessDetails: this.fitnessDetails,
            footerWidget: this.footerWidget,
            cardAction: this.cardAction
        }
    }

    private async getInfoFooterWidgetWithAction(userContext: UserContext, productType: ProductType, careBuisness: ICareBusiness): Promise<WidgetView> {
        const subCategoryCode = productType === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        const productId = productType === "LIVE_PERSONAL_TRAINING" ? LIVE_PT_SNC_PRODUCT_ID : LIVE_SGT_SNC_PRODUCT_ID
        const widget: InfoActionWidget = {
            widgetType: "INFO_ACTION_WIDGET",
            info: "Book 1st session for the trainer to assess your fitness level",
            action: await careBuisness.getLivePTSessionBookAction(userContext, { productId, actionTitle: "BOOK NOW", subCategoryCode })
        }
        widget.containerStyle = {
            backgroundColor: "#f3f4f7",
            marginTop: 12,
            marginBottom: -20,
            borderBottomRightRadius: 10,
            borderBottomLeftRadius: 10,
        }
        return widget
    }

    private getInfoFooterWidgetWithoutAction(info: string): WidgetView {
        const footerWidgetWithoutAssessment: InfoActionWidget = {
            widgetType: "INFO_ACTION_WIDGET",
            info: info
        }
        return footerWidgetWithoutAssessment
    }

    private async getActivityListWidget(userContext: UserContext, productType: ProductType, careBuisness: ICareBusiness, healthfaceService: IHealthfaceService): Promise<WidgetView> {
        const subCategoryCode = productType === "LIVE_PERSONAL_TRAINING" ? "LIVE_PERSONAL_TRAINING" : "LIVE_SGT"
        const patientsList = healthfaceService.getAllPatients(userContext.userProfile.userId)
        const products: HealthfaceProductInfo[] = await healthfaceService.getConsultationProductsInfoByGroupType(userContext.userProfile.cityId, productType, "CULTFIT")
        const workoutIds = _.map(products, product => {
            const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
            return (consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec).cultWorkoutId
        })
        const workoutIdsString = _.join(workoutIds, ",")
        const activities: Activity[] = await Promise.all(_.map(products, async product => {
            let subTitle

            const consultationSellableProduct = product.baseSellableProduct as ConsultationSellableProduct
            const patients = await patientsList
            if (!_.isEmpty(patients)) {
                const selfPatient = patients.find(patient => patient.relationship === "Self")
                if (!_.isEmpty(selfPatient)) {
                    const preferedDoctor: CareTeam[] = await healthfaceService.getPreferredDoctorForDoctorType(userContext.userProfile.userId, selfPatient.id, consultationSellableProduct.consultationProduct.doctorType, false, "CULTFIT")
                    if (!_.isEmpty(preferedDoctor)) {
                        subTitle = `with ${preferedDoctor[0].doctor.name}`
                    }
                }
            }
            const workout = consultationSellableProduct.consultationProduct.productSpecs as LivePTProductSpec
            const seoParams: SeoUrlParams = {
              productName: workout.cultWorkoutName
            }
            const url = ActionUtil.cultWorkoutV2(workout.cultWorkoutId.toString(), undefined, userContext.sessionInfo.userAgent, seoParams, productType, PageTypes.CultWorkoutPageV2, workoutIdsString)
            const rightAction = await careBuisness.getLivePTSessionBookAction(userContext, { productId: consultationSellableProduct.productCode, actionTitle: "BOOK", subCategoryCode })
            const navigationActionType: ActionType = "NAVIGATION"
            return {
                title: workout.cultWorkoutName,
                subTitle: subTitle,
                image: workout.workoutSmallImageUrl,
                action: {
                    actionType: navigationActionType,
                    url: url
                },
                rightAction
            }
        }))
        const footerWorkoutWidget: ActivityListWidget = {
            widgetType: "ACTIVITY_LIST_WIDGET",
            header: {
                title: "Book Next Session"
            },
            activities: activities
        }
        return footerWorkoutWidget
    }
}
