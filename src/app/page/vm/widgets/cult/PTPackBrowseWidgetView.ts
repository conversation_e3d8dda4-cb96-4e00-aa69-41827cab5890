import * as _ from "lodash"
import {
    ConsultationProduct,
    DiagnosticProductResponse,
    HealthfaceTenant,
    SUB_CATEGORY_CODE
} from "@curefit/care-common"
import { BundleSessionSellableProduct } from "@curefit/albus-client"
import { ActionUtil, CareUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { CareOfferRequestParams, OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { Action } from "@curefit/vm-models"
import {
    BaseWidget,
    CareWidgetUtil,
    CultPackItem,
    IServiceInterfaces,
    OfferItem,
    PTPackProwseWidget,
    UserContext
} from "@curefit/vm-models"
import { pluralizeStringIfRequired } from "@curefit/util-common"
import AppUtil from "../../../../util/AppUtil"
import { CareUtil as AppCareUtil, LIVE_PT_SNC_PRODUCT_ID } from "../../../../util/CareUtil"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { ProductPrice } from "@curefit/product-common"

const OFFERS_TO_BE_SHOWN_UPFRONT = 2

export class PTPackBrowseWidgetView extends PTPackProwseWidget {

    data: CultPackItem[]
    subCategoryCode: SUB_CATEGORY_CODE = "LIVE_PERSONAL_TRAINING"

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<BaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const ppcProduct: CultPackItem = await this.getPPCProductDetail(userContext, interfaces)
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(this.subCategoryCode)
        let bundleProductIds: string[] = []
        const products: DiagnosticProductResponse[] = await interfaces.healthfaceService.browseProducts("BUNDLE", this.subCategoryCode, tenant, true, undefined, undefined, undefined, undefined, true)
        bundleProductIds = products.map(product => product.productCode)
        if (_.isEmpty(bundleProductIds) && _.isEmpty(ppcProduct)) {
            return undefined
        }
        this.data = [ppcProduct]
        if (_.isEmpty(bundleProductIds)) {
            return this
        }
        const bundleOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            bundleProductIds,
            AppUtil.callSourceFromContext(userContext),
            interfaces,
            true
        )
        const packItemPromises = _.map(bundleProductIds, async (productId) => {
            const healthfaceProductPromise = interfaces.healthfaceService.getProductInfoDetails("BUNDLE", {
                subCategoryCode: this.subCategoryCode,
                productCodeCsv: productId
            }, tenant)
            const sellableProduct: BundleSessionSellableProduct = <BundleSessionSellableProduct>((await healthfaceProductPromise)[0].baseSellableProduct)
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(sellableProduct, bundleOffers)
            sellableProduct.listingPrice = offerDetails.price.listingPrice
            const offersToShow = this.getOffersToShow(offerDetails)
            const packItem: CultPackItem = {
                title: AppUtil.isLivePTNewClpSupported(userContext) ? `${sellableProduct.infoSection.numberOfSessions} Sessions` : `${sellableProduct.infoSection.numberOfSessions} Sessions\n${sellableProduct.duration} days validity`,
                subTitle: `${sellableProduct.duration} days validity`,
                price: {
                    listingPrice: sellableProduct.listingPrice,
                    mrp: sellableProduct.mrp,
                    currency: offerDetails.price.currency
                },
                action: {
                    title: "BUY",
                    actionType: "NAVIGATION",
                    url: ActionUtil.carefitbundle(sellableProduct.productCode, this.subCategoryCode)
                },
                contentMetric: {
                    contentId: sellableProduct.productName
                },
                background: {
                    gradientColors: ["#FCC161", "#FA9F86"]
                },
                offers: offersToShow
            }
            return packItem
        })
        const packsData = await Promise.all<CultPackItem>(packItemPromises)
        this.data.push(...packsData)
        return this
    }

    private getOffersToShow(offerDetails: {
        price: ProductPrice;
        offers: OfferV2[];
    }) {
        const offerItems: OfferItem[] = []
        const offerTexts: string[] = this.getOfferTextForPack(offerDetails.offers)
        _.forEach(offerTexts, offerText => {
            offerItems.push({
                iconType: "/image/icons/cult/tick.png",
                title: offerText,
            })
        })
        const offersToShowUpfront = Math.min(OFFERS_TO_BE_SHOWN_UPFRONT, offerItems.length)
        const offersToShow = offerItems.slice(0, offersToShowUpfront)
        if (!_.isEmpty(offersToShow) && offerItems.length > offersToShowUpfront) {
            // show "+1 Offer" at the last offer
            offersToShow[offersToShow.length - 1].seeMore = {
                title: "+ " + (offerItems.length - offersToShowUpfront) + pluralizeStringIfRequired(" Offer", offerDetails.offers.length - offersToShowUpfront),
                actionType: undefined
            }
        }
        return offersToShow
    }

    private getOfferTextForPack(offers: OfferV2[]) {
        const offerTexts: string[] = []
        if (!_.isEmpty(offers)) {
            for (const offer of offers) {
                let offerText = undefined
                if (offer.uiLabels && !_.isEmpty(offer.uiLabels.cartLabel)) {
                    offerText = offer.uiLabels.cartLabel
                } else {
                    _.forEach(offer.addons, addon => {
                        if (addon && addon.uiLabels && !_.isEmpty(addon.uiLabels.cartLabel)) {
                            offerText = addon.uiLabels.cartLabel
                        }
                    })
                }
                if (!_.isEmpty(offerText)) {
                    offerTexts.push(offerText)
                }
            }
        }
        return offerTexts
    }

    private async getPPCProductDetail(userContext: UserContext, interfaces: CFServiceInterfaces): Promise<CultPackItem> {
        const productId = LIVE_PT_SNC_PRODUCT_ID
        const consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "CONSULTATION",
            [productId],
            AppUtil.callSourceFromContext(userContext),
            interfaces,
            true
        )
        const product: ConsultationProduct = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productId))
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, consultationOffer)
        const offersToShow = this.getOffersToShow(offerDetails)
        if (product && CareUtil.isLivePTDoctorType(product.doctorType)) {
            const action = <Action>await interfaces.careBusiness.getLivePTSessionBookAction(userContext, {productId, actionTitle: "BUY", subCategoryCode: "LIVE_PERSONAL_TRAINING"})
            const ppcItem: CultPackItem = {
                title: `1 Session\nUnlimited validity`,
                price: {
                    listingPrice: offerDetails.price.listingPrice,
                    mrp: offerDetails.price.mrp,
                    currency: offerDetails.price.currency
                },
                action,
                contentMetric: {
                    contentId: product.name
                },
                background: {
                    gradientColors: ["#FCC161", "#FA9F86"]
                },
                offers: offersToShow
            }
            return ppcItem
        }
    }
}
