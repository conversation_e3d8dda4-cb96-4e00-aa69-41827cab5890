import {  CareWidgetUtil, IBaseWidget, IServiceInterfaces, TrainerSelectionWidget, UserContext } from "@curefit/vm-models"
import { DoctorRecommendationRequest } from "@curefit/albus-client"
import * as _ from "lodash"
import { Action, TrainerInfo } from "@curefit/apps-common"
import AppUtil from "../../../../util/AppUtil"
import { ActionUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import CareUtil  from "../../../../util/CareUtil"
import { ConsultationProduct, LIVE_PT_DOCTOR_TYPES } from "@curefit/care-common"
export class TrainerSelectionWidgetView extends TrainerSelectionWidget {
    title: string
    action: Action
    trainers: TrainerInfo[] = []
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const patients = await interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)
        if (!_.isEmpty(patients)) {
            const selfPatient = patients.find(patient => patient.relationship === "Self")
            if (!_.isEmpty(selfPatient)) {
                const request: DoctorRecommendationRequest = {
                    subCategoryCode: "LIVE_PERSONAL_TRAINING",
                    patientId: selfPatient.id,
                    doctorTypeCodesCsv: LIVE_PT_DOCTOR_TYPES,
                    excludeDoctors: []
                }
                const recommendations = await interfaces.cultPTService.getDoctorRecommendations(request, "CULTFIT")
                if (!_.isEmpty(recommendations)) {
                    const productId = recommendations[0].consultationProducts[0].productCode
                    const consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
                        userContext,
                        "CONSULTATION",
                        [productId],
                        AppUtil.callSourceFromContext(userContext),
                        interfaces,
                        true
                    )
                    const product: ConsultationProduct = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productId))
                    const offerDetails = OfferUtil.getPackOfferAndPrice(product, consultationOffer)

                    const priceTitle = offerDetails.price.listingPrice === 0 ? "" : ` @${RUPEE_SYMBOL}${offerDetails.price.listingPrice}`
                    const actionTitle = `BOOK A SESSION${priceTitle}`

                    this.action = {
                        actionType: "NAVIGATION",
                        url: `${ActionUtil.selectCareDateV1(productId, undefined, undefined)}&doctorId=${recommendations[0].id}&patientId=${selfPatient.id}`,
                        title: actionTitle
                    }
                    recommendations.map(recommendation => {
                        const productId = recommendation.consultationProducts[0].productCode
                        const richDescriptions = CareUtil.getLivePTTrainerRichDescriptions(recommendation.recommendationAffinity)
                        const modalAction: Action = {
                            actionType: "SHOW_DOCTOR_DETAILS_MODAL_V2",
                            meta: {
                                image: recommendation.displayImage,
                                title: recommendation.name,
                                subtitles: [
                                    CareUtil.getFormatDetailedNameFromDoctorType(recommendation.primarySubServiceType.code),
                                    recommendation.qualification,
                                    recommendation.experience ? `${recommendation.experience} years of experience` : ""
                                ],
                                richDescriptions: richDescriptions,
                                description: recommendation.description,
                                action: {
                                    actionType: "NAVIGATION",
                                    title: "BOOK A SESSION",
                                    url: `${ActionUtil.selectCareDateV1(productId, undefined, undefined)}&doctorId=${recommendation.id}&patientId=${selfPatient.id}`,
                                    analyticsData: {
                                        actionType: "SLOT_PAGE"
                                    }
                                }
                            },
                        }
                        this.trainers.push({
                            title: recommendation.name,
                            subtitle: CareUtil.getFormatNameFromDoctorType(recommendation.primarySubServiceType.code),
                            image: recommendation.displayImage,
                            id: recommendation.id,
                            action: modalAction
                        })
                    })
                    return this
                }
            }
        }
    }
}

