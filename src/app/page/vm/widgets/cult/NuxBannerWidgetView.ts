import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget, BannerCarouselWidget, NuxBannerWidget, Action } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import AppUtil from "../../../../util/AppUtil"
import { BannerCarouselWidgetView } from "../sale/BannerCarouselWidgetView"
import CultUtil from "../../../../util/CultUtil"

export class NuxBannerWidgetView extends BannerCarouselWidgetView {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [name: string]: string }): Promise<IBaseWidget> {
        const isInternalUser = false
        const isNuxSupported = await AppUtil.isNUXSupported(userContext, isInternalUser, this.productType, interfaces.hamletBusiness)
        const cultAndMindSummary = await interfaces.userCache.getCultSummary(userContext.userProfile.userId)
        if (isNuxSupported && CultUtil.isCompletedClassCountLessThenThree(cultAndMindSummary)) {
            const action: Action = {
                actionType: "NAVIGATION",
                url: await CultUtil.getActionUrlForNuxBanner(interfaces.cultFitService, userContext.userProfile.userId, interfaces.userCache, interfaces.announcementBusiness, userContext)
            }
            this.widgetType = "BANNER_CAROUSEL_WIDGET"
            if (this.data && this.data.length > 0) {
                this.data[0].action = action
            }
            return super.buildView(interfaces, userContext, queryParams)
        }
        return undefined
    }
}