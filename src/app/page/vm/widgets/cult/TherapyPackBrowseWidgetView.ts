import * as _ from "lodash"
import { DiagnosticProductResponse, HealthfaceTenant, SUB_CATEGORY_CODE, Patient } from "@curefit/care-common"
import { ProductPrice } from "@curefit/product-common"
import { BundleSessionSellableProduct } from "@curefit/albus-client"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import { CareOfferRequestParams } from "@curefit/offer-common"
import { BaseWidget, ContentMetric, IServiceInterfaces, TherapyPackBrowseWidget, UserContext, Action, CareWidgetUtil } from "@curefit/vm-models"
import AppUtil from "../../../../util/AppUtil"
import { getNutritionistCLPUrl, getPreBookingActions } from "../../../../util/NCUtil"
import { CareUtil } from "../../../../util/CareUtil"
import { BASE_TYPES, Logger } from "@curefit/base"
import { pluralizeStringIfRequired } from "@curefit/util-common"

interface TherapyPackItem {
    title: string
    subTitle?: string
    price: ProductPrice
    meta?: string
    action: Action
    contentMetric: ContentMetric
}

export class TherapyPackBrowseWidgetView extends TherapyPackBrowseWidget {

    data: TherapyPackItem[]

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<BaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const tenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(this.subCategoryCode)
        let bundleProductIds: string[] = []
        if (this.filterType) {
            interfaces.logger.info("Personal Training logs filterType", this.filterType, tenant, this.subCategoryCode)
            const bundleSellableProductResponse = await interfaces.healthfaceService.getBundleSellableProducts(this.subCategoryCode, tenant)
            const filteredBundleType = _.find(bundleSellableProductResponse.bundleTypes, bundleType => bundleType.type === this.filterType)
            if (!_.isEmpty(filteredBundleType)) {
                bundleProductIds = filteredBundleType.products
            }
        } else {
            const products: DiagnosticProductResponse[] = await CareWidgetUtil.getCareProductFromUserContext(
                userContext,
                interfaces,
                "BUNDLE",
                this.subCategoryCode,
                this.setCode,
                this.clubCode
            )
            bundleProductIds = products.map(product => product.productCode)
        }

        interfaces.logger.info("Personal Training logs bundle product ids", bundleProductIds)
        if (_.isEmpty(bundleProductIds)) {
            return undefined
        }
        const bundleOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            bundleProductIds,
            AppUtil.callSourceFromContext(userContext),
            interfaces
        )
        const packItemPromises = _.map(bundleProductIds, async (productId) => {
            const healthfaceProductPromise = interfaces.healthfaceService.getProductInfoDetailsCached("BUNDLE", this.subCategoryCode, productId, tenant)
            const sellableProduct: BundleSessionSellableProduct = <BundleSessionSellableProduct>((await healthfaceProductPromise)[0].baseSellableProduct)
            if (sellableProduct.productStatus !== "LIVE") {
                return
            }

            interfaces.logger.info("Personal Training logs sellable products", sellableProduct)
            const offerDetails = OfferUtil.getPackOfferAndPriceForCare(sellableProduct, bundleOffers)
            sellableProduct.listingPrice = offerDetails.price.listingPrice
            let action = undefined
            if (this.subCategoryCode === "NUTRITIONIST") {
                const patientsList: Patient[] = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userContext.userProfile)
                action = getPreBookingActions(userContext, sellableProduct.infoSection.numberOfSessions, true, [], `curefit://carecartcheckout?productId=${sellableProduct.productCode}&subCategoryCode=${sellableProduct?.subCategoryCode}`, patientsList)[0]
            }
            const packItem: TherapyPackItem = {
                title: `${sellableProduct.infoSection.numberOfSessions} ${pluralizeStringIfRequired("Session", sellableProduct.infoSection.numberOfSessions)}`,
                price: {
                    listingPrice: sellableProduct.listingPrice,
                    mrp: this.showMRP ? sellableProduct.mrp : sellableProduct.listingPrice,
                    currency: offerDetails.price.currency
                },
                meta: this.getMeta(sellableProduct),
                action: !_.isNil(action) ? action : {
                    actionType: "NAVIGATION",
                    url: ActionUtil.carefitbundle(
                        sellableProduct.productCode,
                        this.subCategoryCode,
                        undefined,
                        this.filterType,
                        undefined,
                        undefined,
                        undefined,
                        undefined,
                        sessionInfo.userAgent
                    )
                },
                contentMetric: {
                    contentId: sellableProduct.productName
                }
            }
            return packItem
        })
        this.data = await Promise.all<TherapyPackItem>(packItemPromises)
        this.data = this.data.filter(item => !_.isEmpty(item))
        interfaces.logger.info("Personal Training logs final data", this.data)
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
    private getMeta(sellableProduct: BundleSessionSellableProduct) {
        const perSessionPrice = Math.floor(sellableProduct.listingPrice / sellableProduct.infoSection.numberOfSessions)
        return `${RUPEE_SYMBOL} ${perSessionPrice}/session`
    }
}
