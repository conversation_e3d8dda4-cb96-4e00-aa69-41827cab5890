import { CareWidgetUtil, I<PERSON><PERSON>Widget, LivePTTrialWidget, UserContext } from "@curefit/vm-models"
import { <PERSON>, DoctorRecommendationRequest, SUB_CATEGORY_CODE } from "@curefit/albus-client"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common"
import AppUtil from "../../../../util/AppUtil"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../../../util/CareUtil"
import { ConsultationProduct, LIVE_PT_DOCTOR_TYPES } from "@curefit/care-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { ProductType } from "@curefit/product-common"

export class LivePTTrialWidgetView extends LivePTTrialWidget {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (_.isEmpty(this.items)) {
            return undefined
        }
        for (const item of this.items) {
            const productType: ProductType = item.productType
            if (productType === "LIVE_PERSONAL_TRAINING") {
                let productId = LIVE_PT_SNC_PRODUCT_ID
                let recommendations: Doctor[]
                const patients = await interfaces.cultPTService.getAllPatients(userContext.userProfile.userId)
                if (!_.isEmpty(patients)) {
                    const selfPatient = patients.find(patient => patient.relationship === "Self")
                    if (!_.isEmpty(selfPatient)) {
                        const request: DoctorRecommendationRequest = {
                            subCategoryCode: "LIVE_PERSONAL_TRAINING",
                            patientId: selfPatient.id,
                            doctorTypeCodesCsv: LIVE_PT_DOCTOR_TYPES,
                            excludeDoctors: []
                        }
                        recommendations = await interfaces.cultPTService.getDoctorRecommendations(request, "CULTFIT")
                        if (!_.isEmpty(recommendations)) {
                            item.rightView.trainers = []
                            productId = recommendations[0].consultationProducts[0].productCode
                            recommendations.map(recommendation => {
                                item.rightView.trainers.push({
                                    image: recommendation.displayImage
                                })
                            })
                        }
                    }
                }

                const { action, analyticsData } = await this.getCTAAction(userContext, interfaces, productId, "LIVE_PERSONAL_TRAINING", recommendations)
                item.action = action
                item.analyticsData = analyticsData
            } else if (productType === "LIVE_SGT") {
                const { action, analyticsData } = await this.getCTAAction(userContext, interfaces, LIVE_SGT_SNC_PRODUCT_ID, "LIVE_SGT")
                item.action = action
                item.analyticsData = analyticsData
            }
        }

        return this
    }

    async getCTAAction(userContext: UserContext, interfaces: CFServiceInterfaces, productId: string, subCategoryCode: SUB_CATEGORY_CODE, recommendations?: Doctor[]): Promise<{ action: Action, analyticsData: any }> {
        let doctorId
        if (!_.isEmpty(recommendations)) {
            doctorId = recommendations[0].id
        }

        const consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "CONSULTATION",
            [productId],
            AppUtil.callSourceFromContext(userContext),
            interfaces
        )
        const product: ConsultationProduct = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productId))
        const offerDetails = OfferUtil.getPackOfferAndPrice(product, consultationOffer)

        const priceTitle = offerDetails.price.listingPrice === 0 ? " FREE" : ` ${RUPEE_SYMBOL}${offerDetails.price.listingPrice}`
        const actionTitle = `TRY FOR${priceTitle}`

        const action = await interfaces.careBusiness.getLivePTSessionBookAction(userContext, {
            productId,
            actionTitle,
            doctorId: doctorId,
            recommendations,
            fromTrialWidget: true,
            subCategoryCode
        })

        const analyticsData = {
            price: priceTitle
        }
        return { action, analyticsData }
    }
}

