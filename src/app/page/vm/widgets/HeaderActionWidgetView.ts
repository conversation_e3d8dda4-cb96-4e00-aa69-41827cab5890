import { Header<PERSON>ction<PERSON><PERSON>, HeaderActionWidget } from "@curefit/vm-models/dist/src/models/widgets/HeaderActionWidget"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { IBaseWidget } from "@curefit/vm-models"
import { GymfitListingUpgrade, GymfitFitnessProduct } from "@curefit/gymfit-common"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"

export class HeaderActionWidgetView extends HeaderActionWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GYMFIT_UPGRADE") {
            return new GymfitUpgradeView(this).buildView(interfaces, userContext, queryParams)
        }
    }
}

class GymfitUpgradeView extends HeaderActionWidget {
    constructor(headerActionWidget: HeaderActionWidget) {
        super()
        this.productType = headerActionWidget.productType
        this.header = headerActionWidget.header
        this.cards = headerActionWidget.cards
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const cityId = userContext.sessionInfo.sessionData.cityId
        const userId = userContext.userProfile.userId

        const upgrades: GymfitListingUpgrade[] = await interfaces.gymfitService.getGymfitUpgrade(cityId, userId)
        if (_.isEmpty(upgrades)) {
            return null
        }
        const upgrade = upgrades[0]
        interfaces.logger.info(`PMS::DEPR GymfitUpgradeView: Found upgrade ${upgrade.listingId} for user ${userId}`)
        const product: GymfitFitnessProduct = await interfaces.catalogueService.getGymfitFitnessProductByGymfitListingId(upgrade.listingId) // PMS::TODO Migrate
        const card: HeaderActionCard = {
            title: product.title,
            meta: {
                title: "STARTING FROM",
                price: upgrade.price.toString()
            },
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.gymfitPack(product.productId)
            }
        }
        this.cards = [card]
        return this
    }
}
