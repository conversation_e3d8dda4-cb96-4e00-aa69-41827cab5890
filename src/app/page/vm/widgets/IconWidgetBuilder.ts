import { UserContext, IBaseWidget, IconWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import * as _ from "lodash"
import { NCHorizontalListWidgetStyle } from "../../../eat/nutritionist/NCStyles"

export class IconWidget<PERSON><PERSON>er extends IconWidget {

    // used this for NC for the time being, feel free to add params in widgetData and use your custom styles
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: {[param: string]: any}): Promise<IBaseWidget> {
        _.isNil(this.numberOfIcon) ? this.numberOfIcon = 3 : null
        if (_.isNil(this.viewStyle)) {
         this.viewStyle = { paddingVertical: 20 }
        }
        return this
    }
}