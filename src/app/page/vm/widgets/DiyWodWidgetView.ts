import { BaseWidget, Header, IBaseWidget, UserContext } from "@curefit/vm-models"
import { DIYFitnessProductExtended, DIYMeditationProduct, DIYWOD } from "@curefit/diy-common"
import { ProductType, UrlPathBuilder } from "@curefit/product-common"
import { Action, WorkoutCard } from "@curefit/apps-common"
import { ActionUtil } from "@curefit/base-utils"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import LiveUtil from "../../../util/LiveUtil"
import * as _ from "lodash"
import AppUtil from "../../../util/AppUtil"
import { TimeUtil } from "@curefit/util-common"
import { LivePackUtil } from "../../../util/LivePackUtil"

const MIN_COMPLETION_THRESHOLD = 100
const DATE_FORMAT = "YYYY-MM-DD"

const EventTypes = {
    SURYANAMASKARS: "SURYANAMASKARS",
}

export class DiyWodWidgetView extends BaseWidget {
    header: {
        title?: string,
        subTitle?: string,
        moreIcon?: Action,
    }

    workoutCard: WorkoutCard

    productType: ProductType

    eventType?: string

    constructor() {
        super("DIY_WOD")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        switch (this.productType) {
            case "DIY_FITNESS":
            case "DIY_MEDITATION":
                return this.buildWODOrMODView(interfaces, userContext)
            case "FITNESS_EVENT":
                /*if (this.eventType === EventTypes.SURYANAMASKARS) {
                    return this.buildSuryanamaskarasView(interfaces, userContext)
                }*/
                return this
            default:
                return this
        }
    }

    private async buildWODOrMODView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
        const userId = userContext.userProfile.userId
        this.header = {
            title: this.productType === "DIY_FITNESS" ? "Workout from Home" : "Mindfulness At Home",
            subTitle: this.productType === "DIY_FITNESS" ? "Workout in the comfort of your home, your time" : "Meditation at your convenience",
        }
        if (userContext.sessionInfo.isUserLoggedIn) {
            this.header.moreIcon = {
                actionType: "NAVIGATION",
                icon: "FAVORITE",
                url: `curefit://diyfavorites?productType=${this.productType}`
            }
        }
        const diyWod: DIYWOD = await interfaces.diyService.getDIYWOD(userId, this.productType, AppUtil.getCountryId(userContext))
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        const diyProducts: (DIYFitnessProductExtended | DIYMeditationProduct)[] = await (this.productType === "DIY_FITNESS" ? interfaces.diyService.getDIYFitnessProductsByProductIds(userId, [diyWod.productId], tenant)
            : interfaces.diyService.getDIYMeditationProductsByProductIds(userId, [diyWod.productId], tenant))
        if (!_.isEmpty(diyProducts) && diyWod) {
            const diyProduct = diyProducts[0]
            const workoutInfo: {
                value: string;
                unit: string;
            }[] = []
            let completionCount
            if (diyWod.metadata.completionCount > MIN_COMPLETION_THRESHOLD) {
                if (diyWod.metadata.completionCount < 1000) {
                    // Rounding off to nearest hundred
                    completionCount = Math.round(diyWod.metadata.completionCount / 100) * 100
                } else {
                    completionCount = `${Math.floor(diyWod.metadata.completionCount / 1000)}k`
                }
                workoutInfo.push({ value: `${completionCount}+`, unit: "members" })
            }
            workoutInfo.push({ value: Math.floor(diyProduct.duration / (1000 * 60)).toString(), unit: "mins" })
            if (this.productType === "DIY_FITNESS") {
                const fitnessProduct = diyProduct as DIYFitnessProductExtended
                workoutInfo.push({ value: fitnessProduct.calorie?.toString(), unit: "calories" })
            }
            const contentUrl = diyProduct.contentType === "audio" ? UrlPathBuilder.getAudioPath(diyProduct.contentId, diyProduct.contentFormat)
                : UrlPathBuilder.getVideoPath(diyProduct.contentId, diyProduct.contentFormat)
            const meta = {
                activityId: diyProduct.productId,
                packId: diyProduct.productId,
                contentId: diyProduct.contentId,
                consumptionRequired: true,
                activityType: this.productType,
                title: diyProduct.title,
                image: UrlPathBuilder.prefixSlash(diyProduct.imageDetails.todayImage)
            }
            const action: Action = {
                actionType: "NAVIGATION",
                url: (diyProduct.contentType === "audio" ? ActionUtil.audioUrl(contentUrl, UrlPathBuilder.prefixSlash(diyProduct.imageDetails.heroImage), meta) : ActionUtil.videoUrl(contentUrl, UrlPathBuilder.prefixSlash(diyProduct.imageDetails.heroImage), meta)),
                title: "START"
            }
            const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
            const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
            const blockInternationalUser = await AppUtil.blockFitnessContentForInternationalUser(userContext, interfaces.maxmindService, interfaces.logger)
            const newAction = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, diyProduct.locked, action, isUserEligibleForTrial, diyProduct.productType, this.productType === "DIY_FITNESS" ? "diy_wod" : "diy_mod", bucketId, blockInternationalUser, diyProduct.format)
            this.workoutCard = {
                workoutTitle: this.productType === "DIY_FITNESS" ? "Today's Workout" : "Today's Meditation",
                workoutName: diyWod.packName ? diyWod.packName.toUpperCase() : "",
                imageUrl: this.productType === "DIY_FITNESS" ? "image/clp/fitness_diy_wod.png" : "image/clp/mind_diy_wod.png",
                workoutInfo,
                cardAction: newAction,
                showLocked: diyProduct.locked && isUserEligibleForMonetisation && !isUserEligibleForTrial,
                action: newAction
            }
        }
        return this
    }

    /* private async buildSuryanamaskarasView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
         const tz = userContext.userProfile.timezone
         const suryaNamaskaraStats = await interfaces.diyService.getSuryaNamasaraStats(TimeUtil.formatDateInTimeZone(tz, TimeUtil.getDateNow(tz), DATE_FORMAT))
         this.header = {}
         const workoutInfo: {
             value: string;
             unit: string;
         }[] = []
         workoutInfo.push({ value: this.formatLargeNumber(suryaNamaskaraStats.sessions), unit: "sessions" })
         workoutInfo.push({ value: this.formatLargeNumber(suryaNamaskaraStats.uniqUsers), unit: "users" })
         const action: Action = {
             actionType: "NAVIGATION",
             url: "curefit://liveclassbooking?productType=MIND&isLiveBookingPage=true",
             title: "JOIN",
         }
         this.workoutCard = {
             workoutTitle: this.formatLargeNumber(suryaNamaskaraStats.suryaNamaskaras),
             workoutName: "Suryanamaskars completed",
             imageUrl: "image/clp/suryanamaskar.png",
             workoutInfo,
             cardAction: action,
             showLocked: false,
             action: action
         }
         return this
     }*/

    private formatLargeNumber(num: number): string {
        if (num < 1000) {
            return num.toString()
        } else if (num < 1000000) {
            return `${Math.floor(num / 1000)}k`
        } else {
            return `${Math.floor(num / 1000000)}M`
        }
    }
}
