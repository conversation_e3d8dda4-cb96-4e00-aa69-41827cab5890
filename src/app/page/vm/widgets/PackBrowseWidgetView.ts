import {
    CLPPackItem,
    ConsultationSellableProductItem,
    ConsultationSellableProductResponse
} from "@curefit/albus-client"
import {
    ActionUtil,
    ActionUtilV1,
    MealUtil,
    OfferUtil,
    SeoUrlParams
} from "@curefit/base-utils"
import {
    ConsultationProduct,
    CONSULTATION_PACK_SUBCATEGORIES,
    DiagnosticProduct,
    DiagnosticProductResponse,
    DiagnosticTestProduct,
    SKIN_HAIR_SUBCATEGORIES
} from "@curefit/care-common"
import { CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import {
    DIYFitnessPack,
    DIYMeditationPack
} from "@curefit/diy-common"
import { FoodInventory, FoodPack, FoodPackOption } from "@curefit/eat-common"
import { SlotUtil } from "@curefit/eat-util"
import { PackOffersResponse } from "@curefit/offer-common"
import { Product, ProductPrice, ProductType, UrlPathBuilder } from "@curefit/product-common"
import { UserContext } from "@curefit/userinfo-common"
import { pluralizeStringIfRequired, titleCase } from "@curefit/util-common"
import {
    Action,
    CareWidgetUtil,
    IBaseWidget,
    IServiceInterfaces,
    PackBrowseWidget,
    WidgetWithMetric
} from "@curefit/vm-models"
import * as _ from "lodash"
import AppUtil, {
    CARE_CLP_NEW_DESIGN_SUPPORTED,
    IS_SMALL_CARDS_DIAG_CLP_SUPPORTED,
    IS_SMALL_CARDS_DIAG_LIST_CLP_SUPPORTED,
    PACK_BROWSE_WIDGET_PRICE_DETAIL_SUPPORTED
} from "../../../util/AppUtil"
import CareUtil from "../../../util/CareUtil"
import CultUtil, { CULT_NAS_PRICE_HIKE_BANNER_ID, DEFAULT_IMAGE_URL, MIND_NAS_PRICE_HIKE_BANNER_ID } from "../../../util/CultUtil"
import { DEFAULT_TICKET_SIZE } from "../../../util/EatUtil"
import { CFUserProfile } from "../CFUserProfile"
import CatalogueServiceUtilities from "../../../util/CatalogueServiceUtilities"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"

const GROUP_TYPE_DOCTOR = "DOCTOR"
const GROUP_TYPE_GP = "GP"
const GROUP_TYPE_DERMA = "DERMATOLOGIST"

export class PackBrowseWidgetView extends PackBrowseWidget {
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        interfaces.logger.info(`PackBrowseWidget productType :: ${this.productType}`)
        // Backwards Compatibility
        if (this.showDivider === true && _.isUndefined(this.dividerType)) {
            this.dividerType = "LARGE"
        }
        if (this.productType === "FITNESS" || this.productType === "MIND") {
            return new CenterPackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        }
        else if (this.productType === "DIY_FITNESS" || this.productType === "DIY_MEDITATION")
            return new DiyPackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "FOOD")
            return new MealPackViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "BUNDLE")
            return new BundlePackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "MISC")
            return new MiscBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "CONSULTATION")
            return new ConsultCLPBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "FITNESS_SINGLE_CENTER" || this.productType === "MIND_SINGLE_CENTER")
            return new CenterSpecificPackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        else if (this.productType === "FITNESS_JUNIOR") {
            return new CultJuniorPackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "DIAGNOSTICS") {
            return new DiagnosticsPackBrowseViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "GEAR") {
            return this
        } else
            return undefined
    }

}

interface PackItem {
    title?: string
    subTitle?: string
    imageUrl: string
    price: ProductPrice
    action: Action
    items?: CLPPackItem[]
    priceInSingleLine?: boolean
    productInfoDetail?: {
        price: ProductPrice,
        title: string
        pricePrefixText?: string
        priceSuffixText?: string
    }
    largeImageUrl?: string
}

interface FoodPackItem extends PackItem {
    subTitle: string
    isOfferApplied: boolean
}

interface SubscriptionPackItem extends PackItem {
    isSubscriptionPack: boolean
    initialPrice: ProductPrice
    renewalPrice: ProductPrice
}

interface ClpExperimentResponse {
    showLargeCard: boolean,
    showInfo: boolean
}

class CenterPackBrowseViewWidget extends PackBrowseWidget {

    data: PackItem[]
    packPromotion?: PackItem
    bannerWidget?: WidgetWithMetric

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const packsPromise = this.productType === "FITNESS" ? userProfile.cultFitnessPacksPromise : Promise.resolve([] as OfflineFitnessPack[])
        const packs = await packsPromise
        const packOffersV3Response = this.productType === "FITNESS" ?
            await (userProfile as CFUserProfile).getCultProductPrices(packs.map(p => p.id)) : undefined
        const packItems: PackItem[] = []
        _.forEach(packs, fitnessPack => {
            // Don't show 12 months pack at CLP
            if (fitnessPack.status === "ACTIVE" && this.productType === "FITNESS") {
                const offerDetails = CultUtil.getOfferDetailsPMS(fitnessPack, packOffersV3Response)
                const price = offerDetails.price
                price.discountText = (price.listingPrice < price.mrp) ? (price.discountText || "Offer price") : undefined
                const packAction = CatalogueServiceUtilities.getPackPageAction(fitnessPack, sessionInfo.userAgent)
                const packItem: PackItem = {
                    title: "Price",
                    imageUrl: CatalogueServiceUtilities.getFitnessProductImage(fitnessPack, "MAGAZINE", sessionInfo.userAgent),
                    action: {
                        actionType: "NAVIGATION", url: packAction
                    },
                    price: price
                }
                packItems.push(packItem)

            }
        })
        const widgetId = this.productType === "FITNESS" ? CULT_NAS_PRICE_HIKE_BANNER_ID : MIND_NAS_PRICE_HIKE_BANNER_ID
        const widgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, queryParams, undefined)
        if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
            this.bannerWidget = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
        }
        this.data = packItems
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}


class CenterSpecificPackBrowseViewWidget extends PackBrowseWidget { // OLD SELECT PACKS ARE DEPRECATED

    data: PackItem[]
    bannerWidget?: WidgetWithMetric

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        // const { userProfile, sessionInfo } = userContext
        // const mappedProductType = this.productType === "FITNESS_SINGLE_CENTER" ? "FITNESS" : "MIND"
        // const packsPromise = mappedProductType === "FITNESS" ? userProfile.singleCenterCultPacksPromise : userProfile.singleCenterMindPacksPromise
        interfaces.logger.error("PMS::DEPR - CenterSpecificPackBrowseViewWidget is deprecated", {queryParams, userId: userContext.userProfile?.userId})
        return undefined

        // const filteredPacks = packs.filter(pack => {
        //     return pack.state === "ACTIVE"
        // })
        // if (_.isEmpty(filteredPacks)) {
        //     return undefined
        // }
        // const packItems: PackItem[] = []
        // const packProductIds = filteredPacks.map(fp => mappedProductType === "FITNESS" ? CatalogueServiceV2Utilities.getCultPackProductId(fp.id) : CatalogueServiceV2Utilities.getMindPackProductId(fp.id))
        // const filteredPackProducts = await interfaces.catalogueServicePMS.getProducts(packProductIds)
        // _.forEach(filteredPackProducts, fitnessPack => {
        //     // Don't show 12 months pack at CLP
        //     if (fitnessPack.status === "ACTIVE") {
        //         const offerDetails = CultUtil.getOfferDetailsPMS(fitnessPack, packOffersV3Response)
        //         const price = offerDetails.price
        //         price.discountText = undefined // to not show discount text
        //         const offerBrowsePageAction = ActionUtil.cultofferBrowse(CatalogueServiceUtilities.extractPackId(fitnessPack.productId).toString(), "CENTERWISE_PACK")
        //         const packItem: PackItem = {
        //             title: "Starting From",
        //             imageUrl: CatalogueServiceUtilities.getFitnessProductImage(fitnessPack, "MAGAZINE", sessionInfo.userAgent),
        //             action: {
        //                 actionType: "NAVIGATION", url: offerBrowsePageAction
        //             },
        //             price: price
        //         }
        //         packItems.push(packItem)

        //     }
        // })
        // const widgetId = mappedProductType === "FITNESS" ? CULT_NAS_PRICE_HIKE_BANNER_ID : MIND_NAS_PRICE_HIKE_BANNER_ID
        // const widgetResponse = await interfaces.widgetBuilder.buildWidgets([widgetId], interfaces, userContext, queryParams, undefined)
        // if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets)) {
        //     this.bannerWidget = widgetResponse.widgets[0] && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
        // }
        // this.data = packItems
        // return this
    }
}

class CultJuniorPackBrowseViewWidget extends PackBrowseWidget {

    data: PackItem[]

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> { // Junior packs are deprecated
        const { userProfile, sessionInfo } = userContext
        interfaces.logger.error("PMS::DEPR - Junior packs are deprecated", {userProfile})
        return this
    }
}


class DiyPackBrowseViewWidget extends PackBrowseWidget {

    data: PackItem[]

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const countryId = AppUtil.getCountryId(userContext)
        let packs: (DIYFitnessPack | DIYMeditationPack)[]
        if (queryParams && queryParams.seriesId) {
            const series = await interfaces.diyService.getDIYSeriesById(userProfile.userId, queryParams.seriesId, <ProductType>this.productType, countryId)
            packs = this.productType === "DIY_FITNESS" ? await interfaces.diyService.getDIYFitnessPacksForIds(userProfile.userId, series.packIds) :
                await interfaces.diyService.getDIYMeditationPacksForIds(userProfile.userId, series.packIds)
            this.header = {
                title: series.name,
                subTitle: `${series.packIds.length} ${pluralizeStringIfRequired("module", series.packIds.length)}`
            }
        } else {
            const userId = userContext.userProfile.userId
            packs = this.productType === "DIY_FITNESS" ? await interfaces.diyService.getDIYFitnessPacks(userId) : await interfaces.diyService.getDIYMeditationPacks(userId)
        }

        this.data = _.map(packs, pack => {
            const seoParams: SeoUrlParams = {
                productName: pack.title
            }
            const packItem: PackItem = {
                title: `${pack.sessionIds.length} ${pluralizeStringIfRequired("session", pack.sessionIds.length)}`,
                price: {
                    mrp: 0,
                    listingPrice: 0,
                    currency: "INR"
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(pack, userContext.sessionInfo.userAgent)
                },
                imageUrl: UrlPathBuilder.prefixSlash(sessionInfo.userAgent === "APP" ? pack.imageDetails.magazineImage : pack.imageDetails.magazineWebImage)
            }
            return packItem
        })
        return this
    }
}

class MealPackViewWidget extends PackBrowseWidget {

    data: FoodPackItem[]

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        // Get the meal slot for which meals will be shown
        const { userProfile, sessionInfo } = userContext
        const deliveryArea = await userProfile.deliveryAreaPromise
        const areaTz = interfaces.deliveryAreaService.getTimeZoneForArea(deliveryArea)
        const mealSlots = await userProfile.availableMealSlotsPromise
        let selectedMealSlot = SlotUtil.getCurrentMealSlotFrom(mealSlots, deliveryArea.channel, areaTz)
        if (selectedMealSlot === undefined) {
            const nextMealSlot = SlotUtil.getNextAvailableMealSlot(selectedMealSlot, mealSlots, areaTz)
            selectedMealSlot = nextMealSlot.mealSlot
        }

        const packHeaderTitle: string = selectedMealSlot + " PACKS"
        this.header.title = packHeaderTitle

        const mealPacksPromise: Promise<FoodPack[]> = interfaces.catalogueService.getFoodPacksByMealSlot(selectedMealSlot, deliveryArea.channel, "PRIMARY")
        const inventoryPromise: Promise<FoodInventory> = interfaces.capacityService.getInventoryForPacksBasedOnMenuAlone(userProfile.areaId, selectedMealSlot)

        const inventory: FoodInventory = await inventoryPromise
        const availableMealPacks: Set<string> = MealUtil.getAvailableMealPacksFromInventory(inventory)

        const mealPacks: FoodPack[] = await mealPacksPromise
        mealPacks.sort((a, b) => {
            return a.ordering < b.ordering ? -1 : 1
        })

        const packOffersResult = await userProfile.eatPackOffersPromise

        mealPacks.forEach(mealPack => {
            if (availableMealPacks.has(mealPack.productId)) {
                const foodPackOption: FoodPackOption = MealUtil.findDefaultSubscriptionOption(mealPack, packOffersResult)
                const result = OfferUtil.getFoodPackOfferAndPrice(mealPack, foodPackOption, packOffersResult)
                if (mealPack && mealPack.status === "LIVE") {
                    if (foodPackOption.subscriptionType !== undefined) {
                        result.price.listingPrice = Math.round(result.price.listingPrice / foodPackOption.numTickets)
                        result.price.mrp = Math.round(result.price.mrp / foodPackOption.numTickets)
                        result.price.title = "Price"
                    }
                    const seoParams: SeoUrlParams = {
                        productName: mealPack.title
                    }
                    this.data.push({
                        title: "Starting from",
                        price: result.price,
                        subTitle: foodPackOption.subscriptionType === undefined ? `${DEFAULT_TICKET_SIZE} meals at` : "Starting from",
                        isOfferApplied: !_.isEmpty(result.offers),
                        imageUrl: UrlPathBuilder.getPackImagePath(mealPack.productId, "FOOD", "MAGAZINE", mealPack.imageVersion, sessionInfo.userAgent),
                        action: {
                            url: ActionUtil.foodPack(mealPack.productId, undefined, DEFAULT_TICKET_SIZE, true, userContext.sessionInfo.userAgent, seoParams),
                            actionType: "NAVIGATION"
                        }
                    })
                }
            }
        })

        if (_.isEmpty(this.data)) {
            return undefined
        }

        return this
    }
}

class BundlePackBrowseViewWidget extends PackBrowseWidget {

    data: PackItem[]
    gridSize: number
    packAction: Action
    useNewLayout: {
        APP: boolean;
        MBROWSER: boolean;
        DESKTOP: boolean;
    }
    bannerWidget?: WidgetWithMetric

    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.subCategoryCode = packBrowseWidget.subCategoryCode
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
        this.setCode = packBrowseWidget.setCode
        this.clubCode = packBrowseWidget.clubCode
        this.useNewLayout = packBrowseWidget.useNewLayout
        this.bannerWidgetId = packBrowseWidget.bannerWidgetId
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {

        const { userProfile, sessionInfo } = userContext
        // Add this check for website to show different grid size
        if (sessionInfo.userAgent != "APP" && this.subCategoryCode === "DIAG_PACK" && this.layoutType === "GRID") {
            this.gridSize = sessionInfo.userAgent === "DESKTOP" ? 5 : 2
        }
        // To do discuss whether to enable this check
        // if ((this.subCategoryCode === "MP" && sessionInfo.appVersion >= MP_V2_SUPPORTED) || (this.subCategoryCode === "MP_V2" && sessionInfo.appVersion < MP_V2_SUPPORTED)) {
        //     return undefined
        // }
        const products: DiagnosticProductResponse[] = await CareWidgetUtil.getCareProductFromUserContext(
            userContext,
            interfaces,
            this.productType as any,
            this.subCategoryCode,
            this.setCode,
            this.clubCode
        )
        if (_.isEmpty(products)) {
            return undefined
        }
        const bundleoffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
            userContext,
            "BUNDLE",
            products.map(product => product.productCode),
            AppUtil.callSourceFromContext(userContext),
            interfaces
        )

        if (this.bannerWidgetId) {
            const widgetResponse = await interfaces.widgetBuilder.buildWidgets([this.bannerWidgetId], interfaces, userContext, queryParams, undefined)
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets) && Array.isArray(widgetResponse.widgets)) {
                this.bannerWidget = !_.isEmpty(widgetResponse.widgets[0]) && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            }
        }

        if (SKIN_HAIR_SUBCATEGORIES.indexOf(this.subCategoryCode) !== -1 || CONSULTATION_PACK_SUBCATEGORIES.indexOf(this.subCategoryCode) !== -1) {
            const clubCodeMap: Map<string, DiagnosticProductResponse[]> = new Map<string, DiagnosticProductResponse[]>()
            let trailProduct: DiagnosticProductResponse
            for (const product of products) {
                if (product.infoSection.isTrial) {
                    trailProduct = product
                    continue
                }
                if (!clubCodeMap.has(product.clubCode)) {
                    clubCodeMap.set(product.clubCode, [])
                }
                clubCodeMap.get(product.clubCode).push(product)
            }
            this.data = []
            for (const value of clubCodeMap.values()) {
                this.data.push(this.getSkinHairPackItem(userContext, value, bundleoffers))
            }
            this.packAction = trailProduct ? {
                title: trailProduct.infoSection.trialText,
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(trailProduct.productCode, trailProduct.subCategoryCode, undefined, undefined, undefined, undefined, trailProduct.setCode, trailProduct.clubCode)
            } : undefined
            return this
        }

        let packItemDetails: any
        this.data = await Promise.all(_.map(products, async product => {
            const diagnosticsProduct = CareUtil.toDiagnosticsProduct(product) as DiagnosticProduct
            let price: ProductPrice
            let title: string
            if (this.subCategoryCode === "MP") {
                title = "Onboarding"
                const details = await this.getManagedPlanPrice(interfaces, userContext, userProfile.userId, sessionInfo.deviceId, product)
                price = details.price
                packItemDetails = details.packItemDetails
            } else if (this.subCategoryCode === "MP_V2") {
                title = "Starting from"
                const details = await this.getManagedPlanPriceV2(interfaces, userContext, userProfile.userId, sessionInfo.deviceId, product)
                price = details.price
                packItemDetails = details.packItemDetails
            } else {
                title = "Price"
                const offerDetails = OfferUtil.getPackOfferAndPrice(diagnosticsProduct, bundleoffers)
                price = offerDetails.price
                price.discountText = (price.listingPrice < price.mrp) ? "Offer price" : undefined
            }
            const packItem: PackItem = {
                title,
                price: price,
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.carefitbundle(diagnosticsProduct.productId, diagnosticsProduct.subCategoryCode)
                },
                imageUrl: diagnosticsProduct.imageUrl
            }
            if (!_.isEmpty(packItemDetails)) {
                packItem.items = packItemDetails
            }
            return packItem
        }))
        return this
    }

    getSkinHairPackItem(userContext: UserContext, products: DiagnosticProductResponse[], offers: PackOffersResponse): PackItem {
        const items: CLPPackItem[] = []
        let minPricedProduct: DiagnosticProduct, minPrice: ProductPrice, minMRPPrice, minListingPrice = Number.MAX_VALUE
        for (const product of products) {
            const diagnosticProduct = CareUtil.toDiagnosticsProduct(product) as DiagnosticProduct
            const price = OfferUtil.getPackOfferAndPrice(diagnosticProduct, offers).price
            const sessionPrice = Math.ceil(price.listingPrice / Number(product.infoSection.numberOfSessions))
            if (sessionPrice <= minListingPrice) {
                minPricedProduct = diagnosticProduct
                minPrice = price
                minMRPPrice = Math.ceil(price.mrp / Number(product.infoSection.numberOfSessions))
                minListingPrice = sessionPrice
            }
        }
        if (_.isEmpty(minPricedProduct)) {
            return undefined
        }
        const isConsultationPack = CONSULTATION_PACK_SUBCATEGORIES.includes(minPricedProduct.subCategoryCode)
        items.push({
            title: "Starting from",
            price: minMRPPrice,
            discountPrice: minListingPrice,
            hasOfferTag: false,
            showPriceCut: minListingPrice < minMRPPrice,
            discountText: undefined,
            priceSuffix: isConsultationPack ? " / month" : " / session",
            isMultiLine: true
        })
        return {
            title: items[0].title,
            price: minPrice,
            action: {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(minPricedProduct.productId, products[0].subCategoryCode, undefined, undefined, undefined, undefined, products[0].setCode, products[0].clubCode, userContext.sessionInfo.userAgent)
            },
            imageUrl: products[0].imageUrl,
            items: items
        }
    }

    async getManagedPlanPrice(interfaces: IServiceInterfaces, userContext: UserContext, userId: string, deviceId: string, diagnosticsProduct: DiagnosticProductResponse): Promise<any> {
        const mandatoryProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY"
            && product.baseSellableProduct.subCategoryCode !== "MP_SUBS" && product.baseSellableProduct.categoryCode !== "DEVICE")
        const deviceProducts = diagnosticsProduct.childProducts.filter(product => product.childProductType === "CHILD_MANDATORY" && product.baseSellableProduct.categoryCode === "DEVICE" && product.baseSellableProduct.subCategoryCode !== "INT_WELCOME_KIT")
        const subscriptionProducts = diagnosticsProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
        let onBoardingPrice: number = 0
        let originalOnboardingPrice: number = 0
        let recurringMonthlyPrice: number = 0
        let originalRecurringMonthlyPrice: number = 0
        const packItemDetails: CLPPackItem[] = []
        if (!_.isEmpty(mandatoryProducts)) {
            const offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                mandatoryProducts.map(product => product.baseSellableProduct.productCode),
                AppUtil.callSourceFromContext(userContext),
                interfaces
            )
            const mandatoryProductPrice = mandatoryProducts.map(product => OfferUtil.getPackOfferAndPriceForCare(product.baseSellableProduct, offers).price)
            onBoardingPrice += mandatoryProductPrice.map(price => price.listingPrice).reduce((a, b) => a + b)
            originalOnboardingPrice += mandatoryProductPrice.map(price => price.mrp).reduce((a, b) => a + b)
        }

        if (!_.isEmpty(deviceProducts)) {
            const offers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "DEVICE",
                deviceProducts.map(product => product.baseSellableProduct.productCode),
                AppUtil.callSourceFromContext(userContext),
                interfaces
            )
            const deviceProductPrice = deviceProducts.map(product => OfferUtil.getPackOfferAndPriceForCare(product.baseSellableProduct, offers).price)
            onBoardingPrice += deviceProductPrice.map(price => price.listingPrice).reduce((a, b) => a + b)
            originalOnboardingPrice += deviceProductPrice.map(price => price.mrp).reduce((a, b) => a + b)
        }

        if (!_.isEmpty(subscriptionProducts)) {
            const minSubscriptionProduct = _.minBy(subscriptionProducts, product => product.baseSellableProduct.duration)
            const durationInMonth = Math.floor(minSubscriptionProduct.baseSellableProduct.duration / 30)
            const subscriprionOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                [minSubscriptionProduct.baseSellableProduct.productCode],
                AppUtil.callSourceFromContext(userContext),
                interfaces
            )
            const monthlyPrice = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, subscriprionOffer).price
            recurringMonthlyPrice = monthlyPrice.listingPrice / durationInMonth
            originalRecurringMonthlyPrice = monthlyPrice.mrp / durationInMonth
        }
        onBoardingPrice = Number(onBoardingPrice)
        originalOnboardingPrice = Number(originalOnboardingPrice)
        recurringMonthlyPrice = Number(recurringMonthlyPrice)
        originalRecurringMonthlyPrice = Number(originalRecurringMonthlyPrice)
        packItemDetails.push({
            title: "Onboarding",
            price: originalOnboardingPrice,
            discountPrice: onBoardingPrice,
            hasOfferTag: false,
            showPriceCut: onBoardingPrice < originalOnboardingPrice,
            discountText: undefined
        })
        packItemDetails.push({
            title: "Monthly",
            price: originalRecurringMonthlyPrice,
            discountPrice: recurringMonthlyPrice,
            hasOfferTag: false,
            showPriceCut: recurringMonthlyPrice < originalRecurringMonthlyPrice,
            discountText: undefined,
            hasBackgroundColor: true
        })
        return {
            price: {
                listingPrice: recurringMonthlyPrice,
                mrp: onBoardingPrice,
                showPriceCut: false,
                discountText: "Monthly"
            },
            packItemDetails: packItemDetails
        }
    }

    async getManagedPlanPriceV2(interfaces: IServiceInterfaces, userContext: UserContext, userId: string, deviceId: string, diagnosticsProduct: DiagnosticProductResponse): Promise<any> {
        const subscriptionProducts = diagnosticsProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
        let recurringMonthlyPrice: number = 0
        let originalRecurringMonthlyPrice: number = 0
        const packItemDetails: CLPPackItem[] = []

        if (!_.isEmpty(subscriptionProducts)) {
            const subscriprionOffers = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "BUNDLE",
                subscriptionProducts.map(product => product.baseSellableProduct.productCode),
                AppUtil.callSourceFromContext(userContext),
                interfaces
            )
            const minSubscriptionProduct = _.minBy(subscriptionProducts, subsProduct => {
                const price = OfferUtil.getPackOfferAndPriceForCare(subsProduct.baseSellableProduct, subscriprionOffers).price
                const durationInMonth = Math.floor(subsProduct.baseSellableProduct.duration / 30)
                return Math.ceil(price.listingPrice / durationInMonth)
            })
            const durationInMonth = Math.floor(minSubscriptionProduct.baseSellableProduct.duration / 30)
            const minsubsPrice = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, subscriprionOffers).price
            recurringMonthlyPrice = Math.ceil(minsubsPrice.listingPrice / durationInMonth)
            originalRecurringMonthlyPrice = Math.ceil(minsubsPrice.mrp / durationInMonth)
        }
        recurringMonthlyPrice = Number(recurringMonthlyPrice)
        originalRecurringMonthlyPrice = Number(originalRecurringMonthlyPrice)
        packItemDetails.push({
            title: "Starting from",
            price: originalRecurringMonthlyPrice,
            discountPrice: recurringMonthlyPrice,
            hasOfferTag: false,
            showPriceCut: recurringMonthlyPrice < originalRecurringMonthlyPrice,
            discountText: undefined,
            priceSuffix: "/mo*",
            isMultiLine: true
        })
        return {
            price: {
                listingPrice: recurringMonthlyPrice,
                mrp: originalRecurringMonthlyPrice,
                discountText: undefined,
                showPriceCut: recurringMonthlyPrice < originalRecurringMonthlyPrice,
            },
            packItemDetails: packItemDetails
        }
    }
}

class DiagnosticsPackBrowseViewWidget extends PackBrowseWidget {

    data: PackItem[]
    gridSize: number
    packAction: Action
    useNewLayout: {
        APP: boolean;
        MBROWSER: boolean;
        DESKTOP: boolean;
    }
    bannerWidget?: WidgetWithMetric
    constructor(packBrowseWidget: PackBrowseWidget) {
        super()
        this.productType = packBrowseWidget.productType
        this.header = packBrowseWidget.header
        this.showDivider = packBrowseWidget.showDivider
        this.subCategoryCode = packBrowseWidget.subCategoryCode
        this.dividerType = packBrowseWidget.dividerType
        this.layoutType = packBrowseWidget.layoutType
        this.useNewLayout = packBrowseWidget.useNewLayout
        this.bannerWidgetId = packBrowseWidget.bannerWidgetId
        this.groupTypes = packBrowseWidget.groupTypes || []
        this.priceInSingleLine = packBrowseWidget.priceInSingleLine
        this.showPriceDetail = packBrowseWidget.showPriceDetail
        this.isClpAction = packBrowseWidget.isClpAction
        this.seeMoreAction = packBrowseWidget.seeMoreAction
        this.largeCardCount = packBrowseWidget.largeCardCount
    }
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {

        const { userProfile, sessionInfo } = userContext
        const isWeb = AppUtil.isWeb(userContext)
        const isDesktop = AppUtil.isDesktop(userContext)
        // Add this check for website to show different grid size
        if (sessionInfo.userAgent != "APP" && this.layoutType === "GRID") {
            this.gridSize = isDesktop ? 5 : 2
        }
        const params = { "pageNo": 0, "pageSize": 100, "isItemNeeded": false, tags: this.groupTypes, "diagnosticProductAttributesRequestParams": [{ "attributeName": "SHOW_ON_CLP", "attributeValue": "true" }] }
        let productsCatalogue
        try {
            productsCatalogue = await interfaces.diagnosticService.getProductsSearchWithAttributes(params)
        } catch (e) {
            interfaces.logger.info("Product Search With Attributes: ", e)
        }
        const products = productsCatalogue?.content
        if (_.isEmpty(products)) {
            return undefined
        }
        products.sort((a, b) => {
            const sortDataA = a?.productAttributes?.find((data: any) => data.attributeName === "CLP_ORDER")
            const sortOrderA = sortDataA?.attributeValue
            const sortDataB = b?.productAttributes?.find((data: any) => data.attributeName === "CLP_ORDER")
            const sortOrderB = sortDataB?.attributeValue
            if (sortOrderA === undefined && sortOrderB === undefined) return 0
            if (sortOrderA === undefined) return 1
            if (sortOrderB === undefined) return -1
            return Number(sortOrderA) > Number(sortOrderB) ? -1 : 1
        }
        )
        const productIds = products?.map((product) => { return product.code })
        const offers = await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "DIAGNOSTICS", productIds, AppUtil.callSourceFromContext(userContext), interfaces, true)
        const isSmallCardsClpSupported = this.layoutType === "GRID"
            ? userContext.sessionInfo.appVersion >= IS_SMALL_CARDS_DIAG_CLP_SUPPORTED && this.useNewLayout?.APP
            : userContext.sessionInfo.appVersion >= IS_SMALL_CARDS_DIAG_LIST_CLP_SUPPORTED && this.useNewLayout?.APP
        const isPriceDetailUISupported = isSmallCardsClpSupported && this.showPriceDetail && (userContext.sessionInfo.appVersion >= PACK_BROWSE_WIDGET_PRICE_DETAIL_SUPPORTED || AppUtil.isWellnessTabSupported(userContext))
        const magazineImageAttribute = isPriceDetailUISupported
            ? "SMALL_THUMBNAIL_IMAGE_V2"
            : isSmallCardsClpSupported ? "MAGAZINE_IMAGE" : "MAGAZINE_IMAGE_WEB"
        const largeImageAttribute = "LARGE_THUMBNAIL_IMAGE"
        const smallImageAttribute = "SMALL_THUMBNAIL_IMAGE_V3"
        this.useNewLayout = { APP: isSmallCardsClpSupported, MBROWSER: this.useNewLayout?.MBROWSER, DESKTOP: this.useNewLayout?.DESKTOP }
        this.data = products.map((product: any) => {
            product.price = { listingPrice: product.listingPrice, mrp: product.mrp, currency: "INR" }
            product.productId = product.code
            let price: ProductPrice
            let title: string
            title = "Price"
            const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
            price = offerDetails.price
            price.discountText = (price.listingPrice < price.mrp) ? "Offer price" : undefined

            const imageData = product?.productAttributes?.find((data: any) => data.attributeName === magazineImageAttribute)
            let imageUrl = imageData?.attributeValue

            const heroImageData = product?.productAttributes?.productAttributes?.find((data: any) => data.attributeName === "HERO_IMAGE")
            const heroImageImageUrl = heroImageData?.attributeValue || "/image/singles/care/hcu/HCU_Diagnostics_1.png"

            // Added for flutter image flow
            const largeImageData = AppUtil.isFromFlutterAppFlow(userContext) ?  product?.productAttributes?.find((data: any) => data.attributeName === largeImageAttribute) : undefined
            const largeImageUrl = largeImageData?.attributeValue || heroImageImageUrl
            const smallImageData = AppUtil.isFromFlutterAppFlow(userContext) ? product?.productAttributes?.find((data: any) => data.attributeName === smallImageAttribute) : undefined
            imageUrl =  smallImageData?.attributeValue || imageUrl

            const packItem: PackItem = {
                title,
                price: price,
                action: {
                    actionType: "NAVIGATION",
                    url: isWeb ? ActionUtilV1.diagnosticTestProductPage(product.slugValue) : `curefit://carecartproduct?id=${product.code}&subCategoryCode=DIAGNOSTICS`
                },
                imageUrl,
                largeImageUrl,
                productInfoDetail: isPriceDetailUISupported ? {
                    price,
                    title: (<DiagnosticTestProduct>product).name
                } : undefined,
                priceInSingleLine: this.priceInSingleLine
            }
            return packItem
        })
        return this
    }
}

class MiscBrowseViewWidget extends BundlePackBrowseViewWidget {

    data: PackItem[]

    constructor(packBrowseWidget: PackBrowseWidget) {
        super(packBrowseWidget)
        this.productIds = packBrowseWidget.productIds
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (!_.isEmpty(this.productIds)) {
            const { userProfile, sessionInfo } = userContext
            const productPromises = _.map(this.productIds, async productId => await interfaces.catalogueService.getProduct(productId))
            const products: Product[] = await Promise.all(productPromises)
            this.data = await Promise.all(_.map(products, async product => {
                if (product.productType === "MANAGED_GOAL") {
                    const packItem: PackItem = {
                        title: "Price",
                        price: {
                            listingPrice: product.price.listingPrice,
                            mrp: product.price.mrp,
                            currency: product.price.currency,
                            showPriceCut: false,
                            discountText: "Offer Price"
                        },
                        action: {
                            actionType: "NAVIGATION",
                            url: `curefit://goalselectionpage?productIds=${this.productIds.join(",")}`
                        },
                        imageUrl: product.imgDetails["thumb"].url
                    }
                    return packItem
                } else if (product.productType === "BUNDLE") {
                    const diagnosticsProduct = <DiagnosticProductResponse>((await interfaces.healthfaceService.getProductInfoDetails("BUNDLE", {
                        subCategoryCode: "MP",
                        productCodeCsv: product.productId
                    }))[0]).baseSellableProduct
                    if (diagnosticsProduct.subCategoryCode === "MP") {
                        const details = await this.getManagedPlanPrice(interfaces, userContext, userProfile.userId, sessionInfo.deviceId, diagnosticsProduct)
                        const packItem: PackItem = {
                            title: "Onboarding",
                            price: details.price,
                            items: details.items,
                            action: {
                                actionType: "NAVIGATION",
                                url: ActionUtil.carefitbundle(diagnosticsProduct.productCode, diagnosticsProduct.subCategoryCode)
                            },
                            imageUrl: diagnosticsProduct.imageUrl
                        }
                        if (!_.isEmpty(details.packItemDetails)) {
                            packItem.items = details.packItemDetails
                        }
                        return packItem
                    } else if (diagnosticsProduct.subCategoryCode === "AI_MG") {
                        const subscriptionProducts = diagnosticsProduct.childProducts.filter(product => product.baseSellableProduct.subCategoryCode === "MP_SUBS")
                        let recurringMonthlyPrice = 0
                        let originalRecurringMonthlyPrice = 0
                        let currency = "INR"
                        if (!_.isEmpty(subscriptionProducts)) {
                            const minSubscriptionProduct = _.minBy(subscriptionProducts, product => product.baseSellableProduct.duration)
                            const durationInMonth = Math.floor(minSubscriptionProduct.baseSellableProduct.duration / 30)
                            const subscriprionOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
                                userContext,
                                "BUNDLE",
                                [minSubscriptionProduct.baseSellableProduct.productCode],
                                AppUtil.callSourceFromContext(userContext),
                                interfaces
                            )
                            const monthlyPrice = OfferUtil.getPackOfferAndPriceForCare(minSubscriptionProduct.baseSellableProduct, subscriprionOffer).price
                            recurringMonthlyPrice = monthlyPrice.listingPrice / durationInMonth
                            originalRecurringMonthlyPrice = monthlyPrice.mrp / durationInMonth
                            currency = monthlyPrice.currency
                        }
                        const packItem: PackItem = {
                            title: "Price",
                            price: {
                                listingPrice: recurringMonthlyPrice,
                                mrp: originalRecurringMonthlyPrice,
                                currency: currency,
                                showPriceCut: recurringMonthlyPrice !== originalRecurringMonthlyPrice,
                                discountText: recurringMonthlyPrice !== originalRecurringMonthlyPrice ? "Offer Price" : undefined
                            },
                            action: {
                                actionType: "NAVIGATION",
                                url: ActionUtil.carefitbundle(diagnosticsProduct.productCode, diagnosticsProduct.subCategoryCode)
                            },
                            imageUrl: diagnosticsProduct.imageUrl
                        }

                        return packItem
                    }
                }
            }))
            return this
        }
    }

}

class ConsultCLPBrowseViewWidget extends BundlePackBrowseViewWidget {

    data: PackItem[]
    gridSize: number
    showDivider: boolean
    useNewLayout: {
        APP: boolean;
        MBROWSER: boolean;
        DESKTOP: boolean;
    }
    bannerWidget?: WidgetWithMetric
    constructor(packBrowseWidget: PackBrowseWidget) {
        super(packBrowseWidget)
        this.productIds = packBrowseWidget.productIds
        this.layoutType = packBrowseWidget.layoutType
        this.groupTypes = packBrowseWidget.groupTypes
        this.useNewLayout = packBrowseWidget.useNewLayout
        this.bannerWidgetId = packBrowseWidget.bannerWidgetId
        this.showPriceDetail = packBrowseWidget.showPriceDetail
        this.isClpAction = packBrowseWidget.isClpAction
        this.seeMoreAction = packBrowseWidget.seeMoreAction
        this.largeCardCount = packBrowseWidget.largeCardCount
        this.vertical = packBrowseWidget.vertical
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const currency = "INR"
        const { userProfile, sessionInfo } = userContext
        const centerId = CareUtil.getCenterDetailFromQueryParam(queryParams)
        const isNewCenterSpecifiFlowSupported = CareUtil.isNewCenterSpecifiFlowSupported(userContext)
        const consultationSellableProductPromises = _.map(this.groupTypes, groupType => {
            return interfaces.healthfaceService.getConsultationSellableProducts(userProfile.cityId, groupType, centerId)
        })
        const consultationSellableProducts: ConsultationSellableProductResponse[] = await Promise.all(consultationSellableProductPromises)

        const consultationTypes: ConsultationSellableProductItem[] = []
        _.forEach(consultationSellableProducts, consultationSellableProduct => {
            consultationTypes.push(...consultationSellableProduct.consultationTypes)
        })
        if (consultationTypes.length === 0) {
            return undefined
        }

        const isNewCLPDesignSupported = userContext.sessionInfo.appVersion >= CARE_CLP_NEW_DESIGN_SUPPORTED && this.useNewLayout && this.useNewLayout.APP
        const isPriceDetailUISupported = isNewCLPDesignSupported && this.showPriceDetail && (userContext.sessionInfo.appVersion >= PACK_BROWSE_WIDGET_PRICE_DETAIL_SUPPORTED || AppUtil.isWellnessTabSupported(userContext))

        // this.layoutType = sessionInfo.userAgent === "DESKTOP" ?  "LIST" : "GRID"
        this.showDivider = sessionInfo.userAgent === "DESKTOP" ? true : false
        const productCodes = _.flatMap(consultationTypes.map(type => type.products.map(product => product.code)))
        interfaces.logger.info(`PackBrowseWidget Product ID :: ${productCodes}`)
        const allProductDetailsPromise = interfaces.catalogueService.getProducts(productCodes) as Promise<ConsultationProduct[]>
        const consultationOfferPromise = isPriceDetailUISupported ? await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", productCodes, AppUtil.callSourceFromContext(userContext), interfaces) : undefined
        if (consultationOfferPromise) {
            interfaces.logger.info("PackBrowseWidget consultation Offers ::" + JSON.stringify(consultationOfferPromise))
        }
        this.data = await Promise.all(_.map(consultationTypes, async consultationItem => {
            const productCodes = _.map(consultationItem.products, product => product.code)
            let productDetails = (await allProductDetailsPromise).filter(product => productCodes.includes(product.productId))
            const packItemDetails: CLPPackItem[] = []
            let price, productInfoDetail: PackItem["productInfoDetail"]
            if (!_.isEmpty(packItemDetails)) {
                // Added this for backward compatiblity
                if (packItemDetails.length > 1) {
                    price = {
                        listingPrice: packItemDetails[1].discountPrice,
                        mrp: packItemDetails[0].discountPrice,
                        currency,
                        showPriceCut: false,
                        discountText: packItemDetails[1].title
                    }
                } else {
                    price = {
                        listingPrice: packItemDetails[0].discountPrice,
                        mrp: packItemDetails[0].discountPrice,
                        currency,
                        showPriceCut: false,
                        discountText: undefined
                    }
                }
            }
            productDetails = productDetails.reverse()
            const action = this.getProductItemAction(userContext, productDetails, isNewCenterSpecifiFlowSupported, centerId)
            if (isPriceDetailUISupported && consultationOfferPromise) {
                const offers = await consultationOfferPromise
                const minPricedProduct = _.minBy(productDetails, product => {
                    const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
                    return Math.ceil(offerDetails.price.listingPrice)
                })
                const offerDetails = OfferUtil.getPackOfferAndPrice(minPricedProduct, offers)
                productInfoDetail = {
                    title: minPricedProduct?.infoSection?.shortTitle || `${titleCase(CareUtil.getDoctorText(minPricedProduct.doctorType))}s`,
                    price: {
                        mrp: offerDetails.price.listingPrice,
                        listingPrice: offerDetails.price.listingPrice,
                        currency: offerDetails.price.currency,
                    },
                    pricePrefixText: "Starting from"
                }
            }
            const packItem: PackItem = {
                action: action,
                title: !_.isEmpty(packItemDetails) ? packItemDetails[0].title : undefined,
                items: packItemDetails,
                price,
                productInfoDetail,
                ...this.getImageUrlDetails(userContext, isPriceDetailUISupported, isNewCLPDesignSupported, productDetails, consultationItem)

            }
            return packItem
        }))

        if (this.data && this.vertical && this.vertical === "ENTERPRISE_CORP") {
            try {
                this.data = this.data.sort((a, b) => {
                    const priceA: number = (a.productInfoDetail?.price ?? a.price)?.listingPrice ?? 0
                    const priceB: number = (b.productInfoDetail?.price ?? b.price)?.listingPrice ?? 0
                    return priceA < priceB ? -1 : priceA > priceB ? 1 : 0
                })
            }
            catch (e) {
                interfaces.logger.error(`PACK_BROWSE_WIDGET ENTERPRISE_CORP Sort Exception ${e.message}`)
            }
        }
        if (this.bannerWidgetId) {
            const widgetResponse = await interfaces.widgetBuilder.buildWidgets([this.bannerWidgetId], interfaces, userContext, queryParams, undefined)
            if (!_.isEmpty(widgetResponse) && !_.isEmpty(widgetResponse.widgets) && Array.isArray(widgetResponse.widgets)) {
                this.bannerWidget = !_.isEmpty(widgetResponse.widgets[0]) && widgetResponse.widgets[0].widgetType === "BANNER_CAROUSEL_WIDGET" ? widgetResponse.widgets[0] : undefined
            }
        }
        return this
    }

    getImageUrlDetails(userContext: UserContext,
        isPriceDetailUISupported: boolean,
        isNewCLPDesignSupported: boolean,
        productDetails: ConsultationProduct[],
        consultationItem: ConsultationSellableProductItem
    ) {
        if (AppUtil.isFromFlutterAppFlow(userContext)) {
            return {
                imageUrl: isPriceDetailUISupported
                            ? productDetails[0].infoSection?.smallThumbnailImageV3Url || productDetails[0].infoSection?.smallThumbnailImageV2Url
                            : (isNewCLPDesignSupported ? consultationItem.smallThumbnailUrl : consultationItem.thumbnailUrl),
                largeImageUrl: productDetails[0].infoSection?.largeThumbnailImageUrl || productDetails[0].heroImageUrl
            }
        }
        return {
            imageUrl: isPriceDetailUISupported
                        ? productDetails[0].infoSection?.smallThumbnailImageV2Url
                        : (isNewCLPDesignSupported ? consultationItem.smallThumbnailUrl : consultationItem.thumbnailUrl)
        }
    }

    getProductItemAction(userContext: UserContext, productDetails: ConsultationProduct[], isNewCenterSpecifiFlowSupported: boolean, centerId?: number) {
        if (this.isClpAction) {
            if (CareUtil.isNutritionistDoctor(productDetails[0].doctorType)) {
                return {
                    actionType: "NAVIGATION",
                    url: "curefit://listPage?pageId=nutritionistclp"
                }
            }
            if (CareUtil.isTherapyOnlyDoctorType(productDetails[0].doctorType)) {
                return {
                    actionType: "NAVIGATION",
                    url: "curefit://listPage?pageId=MindTherapy"
                }
            }
        }
        return isNewCenterSpecifiFlowSupported ? CareUtil.getCareCenterBrowseAction(userContext, productDetails, true, centerId) : CareUtil.getConsultationNavigationAction(userContext, productDetails, false)
    }

    getPackItemDetailsList(productDetails: ConsultationProduct[], offers: PackOffersResponse, isNewCenterSpecifiFlowSupported: boolean): CLPPackItem[] {
        if (isNewCenterSpecifiFlowSupported) {
            const minPricedProduct = _.minBy(productDetails, product => {
                const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
                return Math.ceil(offerDetails.price.listingPrice)
            })
            const offerDetails = OfferUtil.getPackOfferAndPrice(minPricedProduct, offers)
            return [{
                title: "Starting",
                price: offerDetails.price.mrp,
                discountPrice: offerDetails.price.mrp, // offerDetails.price.listingPrice,
                hasOfferTag: false,
                currency: offerDetails.price.currency || "INR",
                showPriceCut: false // offerDetails.price.listingPrice < offerDetails.price.mrp
            }]
        } else {
            return _.map(productDetails, product => {
                const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
                const title = product.consultationMode === "ONLINE" ? "Video" : "Center"
                return {
                    title: title,
                    price: offerDetails.price.mrp,
                    discountPrice: offerDetails.price.mrp, // offerDetails.price.listingPrice,
                    hasOfferTag: false,
                    currency: offerDetails.price.currency || "INR",
                    showPriceCut: false // offerDetails.price.listingPrice < offerDetails.price.mrp
                }
            })
        }
    }
}
