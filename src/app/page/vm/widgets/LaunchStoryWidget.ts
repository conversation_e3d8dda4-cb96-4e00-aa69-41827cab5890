import { IBaseWidget, UserContext } from "@curefit/vm-models"

import { GearBrowseWidget } from "./ProductBrowseWidget"
import { CFServiceInterfaces } from "../ServiceInterfaces"

export class LaunchStoryWidget extends GearBrowseWidget {
  async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
    if (this.productType === "GEAR") {
        return new GearBrowseWidget(this, "LAUNCH_STORY_WIDGET").buildView(interfaces, userContext, queryParams)
    } else {
        return undefined
    }
  }
}
