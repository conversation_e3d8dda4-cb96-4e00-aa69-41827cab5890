import * as _ from "lodash"
import { Action, MealAction, getOffersWidget, OfferCalloutWidget } from "../../../common/views/WidgetView"
import { MAX_CART_SIZE } from "../../../util/MealUtil"
import { EatClpRecommendationWidget, EatMealsWidget, EatMenuWidget } from "@curefit/vm-models"
import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    SessionInfo,
    UserContext
} from "@curefit/vm-models"
import { SlotUtil } from "@curefit/eat-util"
import { ActionUtilV1, OfferUtil, SeoUrlParams } from "@curefit/base-utils"
import EatUtil, { DEFAULT_FOOD_CATEGORY_ID, getCategoryPriority } from "../../../util/EatUtil"
import { EatCLPTabs, Kiosk, ListingBrandIdType } from "@curefit/eat-common"
import { DeliveryArea } from "@curefit/eat-common"
import { DelayMode, DelayModeReason } from "@curefit/eat-common"
import { FoodInventory, MealSlot, MenuType } from "@curefit/eat-common"
import { ProductMealSlotMenu } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { OfferV2Lite, FoodSinglePriceOfferResponse } from "@curefit/offer-common"
import { capitalizeFirstLetter, ILogger, Timezone } from "@curefit/util-common"
import { eternalPromise } from "@curefit/util-common"
import { BannerCarouselWidget, MealItem } from "../../PageWidgets"
import { PreferredLocation } from "@curefit/userinfo-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { MealCardWidget } from "./MealsCardWidget"
import { CFUserProfile } from "../CFUserProfile"
import { TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import { EatCategoryWidget } from "@curefit/vm-models"
import { CafeSlot, DeliverySlot, FoodCategory } from "@curefit/eat-common"
import AppUtil from "../../../util/AppUtil"
import { FoodProduct } from "@curefit/eat-common"
import { EatExpandableCategoryWidget } from "@curefit/vm-models/dist/src/models/widgets/EatExpandableCategoryWidget"
import { UserAgentType as UserAgent } from "@curefit/base-common"

const clone = require("clone")

const RECOMMENDED_CATEGORY_ID = "RECOMMENDED_MEALS"
const REORDER_CATEGORY_ID = "REORDER_MEALS"

export class EatMealsWidgetViewV2 extends EatMealsWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<IBaseWidget | IBaseWidget[]> {
        const outletId = queryParams.outletId
        if (outletId) {
            return undefined
        }
        const userProfile = userContext.userProfile as CFUserProfile
        // Await for mandatory parameter needed for all apis
        const deliveryArea = await userProfile.deliveryAreaPromise
        const deliveryAreaTz = await interfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId)
        const cafeDay: string = TimeUtil.todaysDate(deliveryAreaTz)
        const kiosk: Kiosk = interfaces.kiosksDemandService.getKiosk(deliveryArea.kioskIds[0])

        const productList: {productId: string, availability: {left: number}}[]
            = await interfaces.cafeProductService.getProducts(deliveryArea.kioskIds[0])
        const allProducts: string[] = productList.map(p => {return p.productId})
        const availablityMap = _.keyBy(productList, p => p.productId)

        const productMap = await interfaces.catalogueService.getProductMap(allProducts)

        const mealItems: MealItem[] = await Promise.all(allProducts.map(p => {
            return this.getCafeMealItem(productMap[p], availablityMap[p].availability, userContext, interfaces, deliveryArea.kioskIds[0], queryParams.deliverySlot, cafeDay)
        }))

        const mealItemsWithInventory = _.filter(mealItems, mealItem => { return mealItem.isInventorySet })

        const categoryIdArray = _.uniq(mealItemsWithInventory.map((mealItem) => {
            return mealItem.foodCategoryId
        }))
        const clpCategories: { [id: string]: FoodCategory } = interfaces.foodCategoryService.getCLPCategories(categoryIdArray, "EAT_FIT")

        const date: Date = TimeUtil.parseDate(cafeDay, deliveryAreaTz)
        const dayOfWeek: number = date.getDay()
        interfaces.logger.info("day of week: " + dayOfWeek)
        const clpCategoryArr: FoodCategory[] = _.uniqBy(_.map(categoryIdArray, (categoryId) => { return clpCategories[categoryId] }), (category) => { return category.categoryId })
        const clpCategoriesByCategoryId: { [categoryId: string]: FoodCategory } = _.keyBy(clpCategoryArr, item => item.categoryId)

        const isEatClpShowMoreLessSupported: boolean = await AppUtil.isEatClpShowMoreLessSupported(userContext)
        const categoryWidgetPromises: Promise<IBaseWidget>[] = _.map(clpCategoryArr, (clpCategory: FoodCategory) => {
            const isExpandable = clpCategory.isExpandable && isEatClpShowMoreLessSupported
            if (isExpandable) {
                return new EatExpandableCategoryWidget(clpCategory.name, clpCategory.categoryId, clpCategory.minCardsInCollapsedState,
                    [], this.eatClpTab).buildView(interfaces as IServiceInterfaces, userContext, queryParams)
            }
            return new EatCategoryWidget(clpCategory.name, clpCategory.categoryId).buildView(interfaces, userContext, queryParams)
        })

        const categoryWidgets = await Promise.all(categoryWidgetPromises)
        const categoryWidgetMap: {[categoryId: string]: IBaseWidget} = _.mapKeys(categoryWidgets, (widget: IBaseWidget) => {
            if (widget instanceof EatCategoryWidget || widget instanceof EatExpandableCategoryWidget) {
                return widget.categoryId
            }
        })

        const mealWidgetPromises = _.map(mealItemsWithInventory, (mealItem: MealItem) => {
            return new MealCardWidget(mealItem, "ALL", clpCategories, "SMALL_CARD", undefined, false, false, undefined).buildView(interfaces, userContext, queryParams)
        })
        const mealWidgets = await Promise.all(mealWidgetPromises)
        const mealWidgetsByCategoryId: {[categoryId: string]: MealCardWidget[]} = {}
        mealWidgets.forEach((mealWidget) => {
            const mealCardWidget = <MealCardWidget>mealWidget
            if (!Object.keys(mealWidgetsByCategoryId).includes(mealCardWidget.categoryId)) {
                mealWidgetsByCategoryId[mealCardWidget.categoryId] = []
            }
            mealWidgetsByCategoryId[mealCardWidget.categoryId].push(mealCardWidget)
        })

        const finalWidets: IBaseWidget[] = []

        if (kiosk.isManagedByVendor) {
            finalWidets.push(new BannerCarouselWidget("900:456", [
                    {
                        id: kiosk.kioskId,
                        image: "image/banners/cafe_banner_vendor.png",
                        contentMetric: {
                            contentId: "kiosk_vendor_banner"
                        },
                        action: undefined
                    }], {
                    bannerWidth: 900,
                    bannerHeight: 456,
                    noVerticalPadding: true,
                    verticalPadding: 0,
                    edgeToEdge: false,
                    noAutoPlay: false,
                    v2: true,
                    autoScroll: false,
                    showPagination: false,
                }) as unknown as IBaseWidget
            )
        }
        for (const categoryId in mealWidgetsByCategoryId) {
            const clpCategory = clpCategoriesByCategoryId[categoryId]
            const isExpandable = clpCategory.isExpandable && isEatClpShowMoreLessSupported
            if (isExpandable) {
                const eatExpandableCategoryWidget = <EatExpandableCategoryWidget>categoryWidgetMap[categoryId]
                eatExpandableCategoryWidget.widgets.push(...mealWidgetsByCategoryId[categoryId])
                finalWidets.push(eatExpandableCategoryWidget)
            }
        }
        for (const categoryId in mealWidgetsByCategoryId) {
            const clpCategory = clpCategoriesByCategoryId[categoryId]
            const isExpandable = clpCategory.isExpandable && isEatClpShowMoreLessSupported
            if (!isExpandable) {
                const eatCategoryWidget = <EatCategoryWidget>categoryWidgetMap[categoryId]
                finalWidets.push(eatCategoryWidget)
                finalWidets.push(...mealWidgetsByCategoryId[categoryId])
            }
        }
        return finalWidets
    }

    async getCafeMealItem(product: FoodProduct, availability: {left: number}, userContext: UserContext, interfaces: CFServiceInterfaces, kioskId: string, deliverySlot: string, day: string): Promise<MealItem> {
        const mealCardImageNumber: number = 1
        const sessionInfo: SessionInfo = userContext.sessionInfo
        const nutritionInfo = EatUtil.computeNutritionalInfo(product, undefined, "EAT_FIT")
        const maxCartSize = product.maxCartQty ? product.maxCartQty : MAX_CART_SIZE
        const imageThumbnail = UrlPathBuilder.getSingleImagePath(product.productId, "FOOD", "THUMBNAIL", product.imageVersion, mealCardImageNumber)
        let image
        const imageProductId = EatUtil.getProductId(product)
        if (sessionInfo.userAgent === "DESKTOP") {
            image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.imageVersion, mealCardImageNumber)
        } else {
            image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", product.landscapeImageExists ? "LANDSCAPE" : "HERO", product.imageVersion, mealCardImageNumber)
        }

        const actions: (Action | MealAction)[] =
            this.getCafeMealActions(product, userContext, day, kioskId, maxCartSize, {left: availability.left, total: availability.left}, this.eatClpTab)

        return {
            title: product.title,
            titleWithoutUnits: product.titleWithoutUnits,
            calories: Math.ceil(Number(nutritionInfo.Calories["Total Calories"])) >= 0 ? Math.ceil(Number(nutritionInfo.Calories["Total Calories"])) : NaN,
            price: product.price,
            date: day,
            image: image,
            imageThumbnail: imageThumbnail,
            isInventorySet: availability.left > 0,
            stock: product.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(maxCartSize, availability.left),
            actions: actions,
            categoryId: product.categoryId,
            productId: product.productId,
            displayUnitQty: EatUtil.getQuantityTitle(product),
            isVeg: product.attributes["isVeg"] === "TRUE",
            isAllDayItem: true,
            shipmentWeight: product.shipmentWeight,
            foodCategoryId: product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID,
            nutritiontags: product.nutritionTags,
            listingBrand: "EAT_FIT",
        }
    }

    getMealItem(request: MealItemRequest, isOnboardingCheckoutSupported: boolean, isCultCafe: boolean, imageNumber: number, isRecommended: boolean, isReorderMeal: boolean, parentProductMap?: { [productId: string]: Product }, productToVariantMap?: { [productId: string]: string[] }, maxAllowedCartSize: number = MAX_CART_SIZE): MealItem {
        const product = request.product
        const categoryId = isRecommended ? RECOMMENDED_CATEGORY_ID : product.categoryId
        const sessionInfo = request.sessionInfo
        const inventoryResult = request.inventoryResult
        const interfaces = request.interfaces
        const tabSlot = request.tabSlot
        const preferredLocation = request.preferredLocation
        const menu = request.menu
        const userContext = request.userContext
        const stopOrders = request.stopOrders
        const queryParams = request.queryParams
        const day = request.day
        const offerDetails = OfferUtil.getSingleOfferAndPrice(request.product, day, request.singleOfferResult, tabSlot)
        const availability = OfferUtil.getAvailabilityV1(product, inventoryResult)
        const actions: (Action | MealAction)[] = this.getMealActions(product, offerDetails, day, tabSlot, preferredLocation, sessionInfo, this.eatClpTab, stopOrders, availability, interfaces.logger, userContext, isOnboardingCheckoutSupported, isCultCafe ? queryParams.deliverySlot : undefined, categoryId, maxAllowedCartSize)
        let mealCardImageNumber = imageNumber
        if (product.primaryImageCount < mealCardImageNumber) {
            mealCardImageNumber = 1
        }
        let image
        const imageProductId = EatUtil.getProductId(product)
        if (sessionInfo.userAgent === "DESKTOP") {
            image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "HERO", product.imageVersion, mealCardImageNumber)
        } else {
            image = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", product.landscapeImageExists ? "LANDSCAPE" : "HERO", product.imageVersion, mealCardImageNumber)
        }
        let parentProduct
        if (!_.isNil(product.parentProductId) && !_.isNil(parentProductMap)) {
            parentProduct = parentProductMap[product.parentProductId]
        }
        const nutritionInfo = EatUtil.computeNutritionalInfo(product, parentProduct, "EAT_FIT")
        const maxCartSize = product.maxCartQty ? product.maxCartQty : MAX_CART_SIZE
        const imageThumbnail = UrlPathBuilder.getSingleImagePath(imageProductId, "FOOD", "THUMBNAIL", product.imageVersion, mealCardImageNumber)
        const mealItem: MealItem = {
            title: product.title,
            titleWithoutUnits: product.titleWithoutUnits,
            calories: Math.ceil(Number(nutritionInfo.Calories["Total Calories"])) >= 0 ? Math.ceil(Number(nutritionInfo.Calories["Total Calories"])) : NaN,
            price: offerDetails.price,
            date: day,
            image: image,
            imageThumbnail: imageThumbnail,
            isInventorySet: availability.total > 0,
            stock: offerDetails.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(maxCartSize, availability.left),
            actions: actions,
            offerIds: _.map(offerDetails.offers, (offer) => { return offer.offerId }),
            categoryId: categoryId,
            productId: product.productId,
            displayUnitQty: EatUtil.getQuantityTitle(product),
            variantTitle: EatUtil.getVariantTitle(product, "EAT_FIT"),
            isVeg: product.attributes["isVeg"] === "TRUE",
            salesTag: menu ? this.salesTagMap[menu.salesTag] : undefined,
            isAllDayItem: menu ? menu.allDay : undefined,
            shipmentWeight: product.shipmentWeight,
            foodCategoryId: isReorderMeal ? REORDER_CATEGORY_ID : (product.foodCategoryId ? product.foodCategoryId : DEFAULT_FOOD_CATEGORY_ID),
            nutritiontags: product.nutritionTags,
            listingBrand: "EAT_FIT"
        }
        if (!isCultCafe && AppUtil.isEatVariantsSupported(userContext)) {
            if (product.parentProductId) {
                mealItem.parentProductId = product.parentProductId
            } else if (productToVariantMap && productToVariantMap[product.productId]) {
                mealItem.variants = productToVariantMap[product.productId]
            }
        } else {
            mealItem.parentProductId = undefined
        }
        return mealItem
    }

    getCafeMealActions(product: Product, userContext: UserContext, day: string, kioskId: string,
                       maxAllowedCartSize: number, availability: { left: number, total: number },
                       clpTab: EatCLPTabs): (Action | MealAction)[] {
        const actions: (MealAction | Action)[] = []
        const seoParams = {
            productName: product.title
        }
        actions.push({
            url: EatMealsWidgetViewV2.foodSingle(product.productId, day, "ALL", true, kioskId, undefined, userContext.sessionInfo.userAgent, seoParams, undefined, "EAT_FIT", product.categoryId),
            actionType: "WIDGET_NAVIGATION"
        })

        if (!AppUtil.isWeb(userContext) && !userContext.sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push({ ...action, title: "ADD" })
            return actions
        }
        const imageProductId = EatUtil.getProductId(product)
        const maxCartSize = product.maxCartQty ? product.maxCartQty : MAX_CART_SIZE
        const image = UrlPathBuilder.getSingleImagePath(imageProductId, product.productType, "THUMBNAIL", product.imageVersion)
        const action: MealAction = {
            actionType: "BUY_MEAL",
            url: "curefit://cartcheckout",
            title: "BUY",
            date: day,
            image: image,
            productId: product.productId,
            productName: product.title,
            option: {
                kioskId: kioskId
            },
            offerIds: [],
            price: product.price,
            maxCartSize: maxAllowedCartSize,
            stock: product.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(maxCartSize, availability.left),
            clpTab: clpTab,
            mealSlot: {
                id: "ALL",
                name: "All"
            },
            listingBrand: "EAT_FIT"
        }
        actions.push({ ...action, actionType: "ADD_TO_CART", title: "ADD" })
        return actions
    }
    getMealActions(product: Product, offerDetails: { price: ProductPrice, offers: OfferV2Lite[], offerProduct: Product }, day: string, menuType: MenuType, preferredLocation: PreferredLocation, sessionInfo: SessionInfo, clpTab: EatCLPTabs, stopOrders: boolean, availability: { left: number, total: number }, logger: ILogger, userContext: UserContext, isOnboardingCheckoutSupported: boolean, cafeDeliverySlot?: string, categoryId?: string, maxAllowedCartSize: number = MAX_CART_SIZE): (Action | MealAction)[] {
        const actions: (MealAction | Action)[] = []
        const seoParams = {
            productName: product.title
        }
        actions.push({
            url: ActionUtil.foodSingle(product.productId, day, menuType, !stopOrders, undefined, userContext.sessionInfo.userAgent, seoParams, cafeDeliverySlot, "EAT_FIT", categoryId),
            actionType: "WIDGET_NAVIGATION"
        })

        if (!AppUtil.isWeb(userContext) && !isOnboardingCheckoutSupported && !sessionInfo.isUserLoggedIn) {
            const action: Action = {
                actionType: "SHOW_ALERT_MODAL",
                title: "BUY",
                meta: {
                    title: "Login Required!",
                    subTitle: "Please login to continue",
                    actions: [{ actionType: "LOGOUT", title: "Login" }]
                }
            }
            actions.push({ ...action, title: "ADD" })
            return actions
        }

        const imageProductId = EatUtil.getProductId(product)
        if (preferredLocation.isInServicableArea) {
            const maxCartSize = product.maxCartQty ? product.maxCartQty : MAX_CART_SIZE
            const offerIds = _.isEmpty(offerDetails) ? [] : _.map(offerDetails.offers, offer => { return offer.offerId })
            const image = UrlPathBuilder.getSingleImagePath(imageProductId, product.productType, "THUMBNAIL", product.imageVersion)
            const action: MealAction = {
                actionType: "BUY_MEAL",
                url: "curefit://cartcheckout",
                title: "BUY",
                date: day,
                image: image,
                productId: product.productId,
                productName: product.title,
                offerId: !_.isEmpty(offerIds) ? offerIds[0] : undefined,
                offerIds: offerIds,
                price: offerDetails.price,
                maxCartSize: maxAllowedCartSize,
                stock: offerDetails.price.listingPrice === 0 && availability.left > 0 ? 1 : Math.min(maxCartSize, availability.left),
                clpTab: clpTab,
                mealSlot: {
                    id: menuType,
                    name: capitalizeFirstLetter(menuType.toLowerCase())
                },
                listingBrand: "EAT_FIT"
            }
            actions.push({ ...action, actionType: "ADD_TO_CART", title: "ADD" })
        } else {
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "ADD",
                meta: {
                    title: "Unserviceable Location",
                    subTitle: "We currently do not deliver to this area. Please update your location at the top of the page",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        if (stopOrders) {
            actions.splice(1, 2)
            actions.push({
                actionType: "SHOW_ALERT_MODAL",
                title: "CLOSED",
                meta: {
                    title: "Ordering closed",
                    subTitle: "We're currently closed for orders. Please check back with us in sometime",
                    actions: [{ actionType: "HIDE_ALERT_MODAL", title: "Ok" }]
                }
            })
        }
        return actions
    }

    async getRecommendedMealMap(userId: string, day: string, interfaces: IServiceInterfaces, menuType: MenuType) {
        const recommendedMealResult = await eternalPromise(interfaces.cerberusService.getRecommendedMeals(userId, day, menuType))
        return EatUtil.getRecommendedMealMap(recommendedMealResult)
    }

    protected getSelectedDeliverySlot(queryParams: any, isCultCafe: boolean): DeliverySlot {
        let selectedDeliverySlot
        if (queryParams.deliverySlot) {
            if (isCultCafe) {
                selectedDeliverySlot = SlotUtil.getDeliverySlotForCafe(<CafeSlot>queryParams.deliverySlot)
            } else {
                selectedDeliverySlot = SlotUtil.getSlotById(queryParams.deliverySlot)
            }
        }
        return selectedDeliverySlot
    }

    getDelayMode(menuType: MenuType, deliveryArea: DeliveryArea, timeZone: Timezone): { mealSlot: MealSlot, mode: DelayMode, reason: DelayModeReason, revertDrivingScaleFactor?: number } {
        let mealSlot: MealSlot = undefined
        if (menuType === "ALL") {
            mealSlot = SlotUtil.getMealSlotExact(TimeUtil.now(timeZone))
        }
        else {
            mealSlot = menuType
        }
        if (deliveryArea.delayMode) {
            return deliveryArea.delayMode.find((delayMode) => {
                return delayMode.mealSlot === mealSlot
            })
        }
    }

    handleParentProductNotPresent(allProducts: Product[], byProductId: { [productId: string]: Product }) {
        for (const product of allProducts) {
            const parentProductId = product.parentProductId
            if (!_.isNil(parentProductId) && _.isNil(byProductId[parentProductId])) {
                allProducts.forEach(pdt => {
                    if (!_.isNil(pdt.parentProductId) && (pdt.parentProductId === parentProductId)) {
                        pdt.parentProductId = product.productId
                    }
                })
                // remove the parentProductId since this will act as a parent for all other variants
                delete product.parentProductId
            }
        }

    }

    handleParentProductSoldOut(mealItems: MealItem[]) {
        const byProductId: { [productId: string]: MealItem } = _.keyBy(mealItems, item => item.productId)
        const soldOutParents: MealItem[] = []
        for (const mealItem of mealItems) {
            const parentProductId = mealItem.parentProductId
            const isVariant = !_.isNil(parentProductId)
            const parentProduct = byProductId[mealItem.parentProductId]
            if (isVariant && !_.isNil(parentProduct) && parentProduct.stock === 0) {
                const parentFound = _.find(soldOutParents, sp => {
                    return sp.productId === parentProductId
                })
                if (_.isNil(parentFound)) {
                    const parentClone = clone(parentProduct)
                    soldOutParents.push(parentClone)
                }
            }
        }

        const variantGroupsByParentProductId: { [productId: string]: MealItem[] } = {}
        if (!_.isEmpty(soldOutParents)) {
            soldOutParents.forEach(soldOutProduct => {
                for (const mealItem of mealItems) {
                    if (soldOutProduct.productId === mealItem.parentProductId) {
                        if (_.isNil(variantGroupsByParentProductId[soldOutProduct.productId])) {
                            variantGroupsByParentProductId[soldOutProduct.productId] = []
                            variantGroupsByParentProductId[soldOutProduct.productId].push(mealItem)
                        } else {
                            variantGroupsByParentProductId[soldOutProduct.productId].push(mealItem)
                        }
                    }
                }
            })
        }

        Object.keys(variantGroupsByParentProductId).forEach(parentProductId => {
            const variantGroup: MealItem[] = variantGroupsByParentProductId[parentProductId]
            const parentProduct: MealItem = byProductId[parentProductId]
            parentProduct.variants = undefined
            variantGroup.push(parentProduct)
            let newParentProductId: string
            const variantIds: string[] = []
            variantGroup.map((item, index) => {
                if (index === 0) {
                    newParentProductId = item.productId
                    item.parentProductId = undefined
                } else {
                    item.parentProductId = newParentProductId
                }
                if (index > 0) {
                    variantIds.push(item.productId)
                }
            })
            variantGroup[0].variants = variantIds

            mealItems.map(mealItem => {
                variantGroup.forEach(variant => {
                    if (mealItem.productId === variant.productId) {
                        mealItem = variant
                    }
                })
            })
        })
    }

    static foodSingle(productId: string, date: string, slot: MenuType, enableBuy: boolean = false, kioskId: string, fulfilmentId?: string,
                      userAgent?: UserAgent, seoUrlParams?: SeoUrlParams, deliverySlot?: string, listingBrand?: ListingBrandIdType,
                      categoryId?: string) {
        let url = "curefit://mealsingles"
        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            url = ActionUtilV1.foodSingle(seoUrlParams, productId)
        }
        let obj: any
        obj = { productId, enableBuy, slot, fulfilmentId, date, categoryId, kioskId }
        let queryParams = ActionUtil.serializeAsQueryParams(obj)
        if (!_.isNil(deliverySlot)) {
            queryParams += "&deliverySlot=" + deliverySlot
        }
        if (!_.isNil(listingBrand)) {
            queryParams += "&listingBrand=" + listingBrand
        }

        return url + queryParams
    }
}

export interface MealItemRequest {
    sessionInfo: SessionInfo,
    product: FoodProduct,
    interfaces: CFServiceInterfaces,
    day: string,
    singleOfferResult: FoodSinglePriceOfferResponse,
    inventoryResult: FoodInventory,
    tabSlot: MenuType,
    preferredLocation: PreferredLocation,
    menu?: ProductMealSlotMenu,
    userContext: UserContext,
    stopOrders: boolean,
    queryParams: { [filterName: string]: string }
}
