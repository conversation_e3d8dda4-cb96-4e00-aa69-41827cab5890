import {
    IBaseWidget,
    UserContext,
    GymfitSearchWidget
} from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"


export class GymfitSearchWIdgetView extends GymfitSearchWidget {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        this.placeholderText = "Search by gym name or locality"
        this.isDisabled = false
        this.action = {
            actionType: "NAVIGATION",
            url: "curefit://allgyms?centerType=GYM",
            meta: {
                shouldAutoFocusSearch: true,
            }
        }
        return this
    }
}
