import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { EatMealCardType, ListingBrandIdType } from "@curefit/eat-common"
import { MenuType } from "@curefit/eat-common"
import { ProductPrice } from "@curefit/product-common"
import { MealItem } from "../../PageWidgets"
import { Action } from "../../../common/views/WidgetView"
import { CalorieBuckets } from "@curefit/vm-models"
import { capitalizeFirstLetter } from "@curefit/util-common"
import * as _ from "lodash"
import { FoodCategory } from "@curefit/eat-common"
import { ATTACH_CATEGORIES } from "../../../util/EatUtil"
import { OfferV2 } from "@curefit/offer-common"
import { FitClubUtil } from "@curefit/fitclub-client"
import { MeasurementUnit } from "@curefit/food-common"
import AppUtil from "../../../util/AppUtil"
import { FMMealNutritionProfile } from "../../../eat/marketplace/FMMealNutritionProfile"

export class MealCardWidget extends BaseWidget {

    title: string
    titleWithoutUnits?: string
    calories: string
    productId: string
    stock: number
    price: ProductPrice
    date: string
    actions: Action[]
    isVeg: boolean
    isInventorySet: boolean
    image: string
    imageThumbnail: string
    mealSlot: MenuType
    salesTag?: string
    isAllDayItem?: boolean
    isLowStock?: boolean
    calorieBucket: number
    nutritionTags: string[]
    categoryId: string
    showAttach?: boolean
    offerIds?: string[]
    cartOfferText?: string
    cardViewType: EatMealCardType | "FM_MEAL_CARD"
    fitCash: number
    isFitClubEnabled: boolean
    qty?: number
    parentProductId?: string
    variants?: string[]
    unit?: MeasurementUnit
    displayUnitQty?: string
    variantTitle?: string
    listingBrand?: ListingBrandIdType
    fitcashOffer: {text: string; style?: any}[]
    fitcashIcon: string
    nutritionProfile?: FMMealNutritionProfile
    description?: string
    tag?: string
    customisableText?: string

    constructor(mealItem: MealItem, mealSlot: MenuType, clpCategories: { [id: string]: FoodCategory }, cardViewType: EatMealCardType | "FM_MEAL_CARD", fitCashOffer: OfferV2, isFitClubEnabled: boolean, isRecommendedItem?: boolean, cartOfferText?: string, showAttach?: boolean, showDivider?: boolean) {
        super("MEAL_CARD_WIDGET")
        const fitcash = fitCashOffer ? FitClubUtil.getFitCash(fitCashOffer, mealItem.price.listingPrice, true) : 0
        this.showDivider = !!showDivider
        this.title = mealItem.title
        this.titleWithoutUnits = mealItem.titleWithoutUnits
        this.calories = mealItem.calories >= 0 && !_.isNaN(mealItem.calories) ? mealItem.calories + " Cal" + (mealItem.shipmentWeight > 1 ? " per serving" : "") : undefined
        this.actions = mealItem.actions
        this.date = mealItem.date
        this.stock = mealItem.stock
        this.isInventorySet = mealItem.isInventorySet
        this.isVeg = mealItem.isVeg
        this.productId = mealItem.productId
        this.price = mealItem.price
        this.image = mealItem.image
        this.mealSlot = mealSlot
        this.imageThumbnail = mealItem.imageThumbnail
        this.offerIds = mealItem.offerIds
        this.salesTag = mealItem.salesTag
        this.isAllDayItem = mealItem.isAllDayItem
        this.isLowStock = mealItem.stock > 0 && mealItem.stock < 3
        this.categoryId = isRecommendedItem ? clpCategories[mealItem.categoryId].categoryId : clpCategories[mealItem.foodCategoryId].categoryId
        this.showAttach = showAttach || ATTACH_CATEGORIES.includes(this.categoryId)
        this.cardViewType = cardViewType
        this.calorieBucket = CalorieBuckets.findIndex((bucketUpperLimit: number) => {
            return mealItem.calories <= bucketUpperLimit
        })
        this.nutritionTags = _.map(mealItem.nutritiontags, (tag) => {
            return capitalizeFirstLetter(tag).replace("_", " ")
        })
        if (_.isEmpty(this.nutritionTags)) {
            this.nutritionTags = undefined
        }
        this.fitCash = fitCashOffer && isFitClubEnabled ? fitcash : undefined
        this.isFitClubEnabled = isFitClubEnabled
        this.qty = mealItem.qty
        this.parentProductId = mealItem.parentProductId
        this.variants = mealItem.variants
        this.unit = mealItem.unit
        this.displayUnitQty = mealItem.displayUnitQty
        this.variantTitle = mealItem.variantTitle
        this.listingBrand = mealItem.listingBrand
        this.cartOfferText = cartOfferText
        this.fitcashIcon = "FITCASH"
        this.fitcashOffer = fitcash > 0 ? [{
            text: "Upto ",
            style: {
                color: "#8d93a0"
            }
        }, {
            text: `${fitcash} Fitcash`,
            style: {
                fontFamily: "BrandonText-Regular",
            }
        }] : undefined
        this.customisableText = mealItem.customisableText
        this.description = mealItem.description
        // wire these
        this.nutritionProfile = mealItem.nutritionProfileInfo
        this.tag = undefined
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (!AppUtil.isNutritionalTagFilterSupported(userContext) && !_.isNil(this.nutritionTags)) {
            this.nutritionTags = this.nutritionTags.filter((filter, index) => {
                return this.cardViewType === "SMALL_CARD" ? index < 1 : index < 2
            })
        }
        return this
    }
}
