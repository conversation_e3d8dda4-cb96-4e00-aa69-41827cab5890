import { BaseWidget, IBaseWidget, MyLiveClassWidget } from "@curefit/vm-models"
import { User } from "@curefit/user-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import LiveUtil from "../../../util/LiveUtil"
import { eternalPromise } from "@curefit/util-common"
import { SessionInfo, UserContext } from "@curefit/userinfo-common"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { LiveFitWorkoutFormat } from "@curefit/diy-common"
import AppUtil from "../../../util/AppUtil"
import { Activity, MyLiveClassesWidget } from "@curefit/apps-common"
import { AppointmentActionsWithContext, DOCTOR_TYPE, IHealthfaceService } from "@curefit/albus-client"
import { ActionUtil } from "@curefit/base-utils"
import { ICatalogueService } from "@curefit/catalog-client"
import { ConsultationProduct, LIVE_PT_DOCTOR_TYPES, LIVE_SGT_DOCTOR_TYPES } from "@curefit/care-common"
import { HamletBusiness } from "@curefit/hamlet-node-sdk"
import { ICareBusiness } from "../../../care/CareBusiness"
import { SimpleWod } from "@curefit/fitness-common"
import { IHerculesService } from "@curefit/hercules-client"
import CultUtil from "../../../util/CultUtil"
import { ProductType } from "@curefit/product-common"
import { LivePackUtil } from "../../../util/LivePackUtil"
import CareUtil from "../../../util/CareUtil"

export class MyLiveClassesWidgetView extends MyLiveClassWidget {
    title?: string
    data?: any
    sessionInfo?: SessionInfo
    classes?: Activity[]

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        const user: User = await userContext.userPromise
        const { sessionInfo } = userContext
        interfaces.logger.info(`productType: ${this.productType}`)
        let formats: LiveFitWorkoutFormat[] = []
        const productType = _.isEmpty(this.productType) ? "FITNESS" : this.productType
        formats = LiveUtil.getFormatsBasedOnProductType(productType)
        const inUserLoggedIn = _.get(userContext, "sessionInfo.isUserLoggedIn", false)

        if (!inUserLoggedIn) {
            return undefined
        }

        if (productType === "LIVE_PERSONAL_TRAINING" || productType === "LIVE_SGT") {
            const widget = await this.getPTClassesWidgetView(userContext, productType, interfaces.healthfaceService, interfaces.catalogueService, interfaces.herculeService, interfaces.hamletBusiness, interfaces.careBusiness)
            if (!widget) {
                return undefined
            }
            return this
        }
        const countryId = AppUtil.getCountryId(userContext)
        const isLiveQnASupported = await AppUtil.isLiveQnASupported(userContext)
        let subscribedLiveClasses = await interfaces.diyService.getUpcomingSubscribedLiveClasses(user.id, AppUtil.getTenantFromUserContext(userContext), countryId)
        if (!subscribedLiveClasses || _.isEmpty(subscribedLiveClasses)) {
            return undefined
        }

        subscribedLiveClasses = LiveUtil.filterInteractiveSessionOrRealLiveWhenNotSupported(subscribedLiveClasses, isLiveQnASupported, userContext)

        subscribedLiveClasses = subscribedLiveClasses.filter(subscribedLiveClass => formats.includes(subscribedLiveClass.format))
        // @ts-ignore
        const subscribedClassIds = subscribedLiveClasses.map(liveClass => liveClass._id)
        const sessionCount = LiveUtil.getNumberOfSessions(user, subscribedLiveClasses)
        const bucketId = await AppUtil.getBucketIdForMandatoryOnboardingExp(userContext, interfaces.hamletBusiness)
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        const subscribedClassResponse = subscribedLiveClasses.map(async liveClass => {
            // const firestoreEnabled = await LiveUtil.isFirestoreEnabled(interfaces.diyService, (<any>liveClass)._id)
            const buttonAction = await eternalPromise(LiveUtil.getStatusBasedSessionAction(user, userContext, liveClass, subscribedClassIds, sessionInfo, sessionCount, "my_live_classes_widget", false, interfaces.classInviteLinkCreator, liveClass.locked, interfaces, isUserEligibleForMonetisation, isUserEligibleForTrial, bucketId))
            const bookingPageAction = LiveUtil.getLiveSessionDetailAction(liveClass, "my_live_classes_widget", userContext)
            const description = TimeUtil.formatEpochInTimeZoneDateFns(userContext.userProfile.timezone, liveClass.scheduledTimeEpoch, "h:mm a") + ", Live | At Home"
            return {
                title: liveClass.title,
                tags: liveClass.tags,
                status: liveClass.status,
                duration: liveClass.duration,
                scheduledTimeEpoch: liveClass.scheduledTimeEpoch,
                playerStartTimeEpoch: liveClass.playerStartTimeEpoch,
                category: liveClass.category,
                timeZone: liveClass.timeZone,
                videoData: liveClass.videoData,
                bannerImages: liveClass.bannerImages,
                sessionEndImages: liveClass.sessionEndImages,
                sessionStartImages: liveClass.sessionStartImages,
                description: description,
                buttonAction: buttonAction.obj,
                cardAction: bookingPageAction
            }
        })
        this.title = "My Live Classes"
        this.data = await Promise.all(subscribedClassResponse)
        if (this.data.length) { return this }
    }

    public async getPTClassesWidgetView(userContext: UserContext, productType: ProductType, healthfaceService: IHealthfaceService, catalogueService: ICatalogueService, herculesService: IHerculesService, hamletBusiness?: HamletBusiness, careBusiness?: ICareBusiness, containerStyle?: any, headerTitleStyle?: any): Promise<MyLiveClassesWidget> {
        const doctorTypes: DOCTOR_TYPE[] = productType === "LIVE_PERSONAL_TRAINING" ? LIVE_PT_DOCTOR_TYPES : LIVE_SGT_DOCTOR_TYPES
        const activeConsultations = await healthfaceService.getActiveConsultations(Number(userContext.userProfile.userId), doctorTypes, "CULTFIT")
        if (_.isEmpty(activeConsultations)) {
            return undefined
        }
        const isLivePT = productType === "LIVE_PERSONAL_TRAINING"
        const isLiveSGT = productType === "LIVE_SGT"
        const classesPromise: Promise<Activity>[] = _.map(activeConsultations, async activeConsultation => {
            let workoutData: { wodSubtitle: string, workouts: string }
            if (isLiveSGT && !_.isEmpty(activeConsultation.metadata) && !_.isEmpty(activeConsultation.metadata.wodId)) {
                const wod: SimpleWod = await herculesService.getSimpleWodById(activeConsultation.metadata.wodId)
                workoutData = this.getWorkoutData(wod)
            }
            let doctor
            if (activeConsultation.doctorId) {
                doctor = await healthfaceService.getDoctorDetails(Number(activeConsultation.doctorId))
            }
            const product = <ConsultationProduct>await catalogueService.getProduct(activeConsultation.productCode)
            const actionUrl = ActionUtil.teleconsultationSingle(userContext, activeConsultation.productCode, product.urlPath, activeConsultation.bookingId.toString(), undefined, "CULTFIT")
            const tz = userContext.userProfile.timezone
            const startDate = TimeUtil.formatEpochInTimeZoneDateFns(tz, activeConsultation.startDate, "yyyy-MM-dd")
            const todaysDate: string = TimeUtil.todaysDate(tz)
            let startDateText
            if (TimeUtil.diffInDays(tz, startDate, todaysDate)) {
                startDateText = TimeUtil.getDayText(startDate, tz)
            } else {
                startDateText = TimeUtil.formatEpochInTimeZoneDateFns(tz, activeConsultation.startDate, "dd MMM")
            }
            const startTimeText = TimeUtil.formatEpochInTimeZoneDateFns(tz, activeConsultation.startDate, "hh:mm a")
            const footerTexts = [`${startDateText}, ${startTimeText}`]
            if (doctor) {
                footerTexts.push(`With ${doctor.name}`)
            }
            const activity: Activity = {
                title: isLiveSGT ? product.title : "Online Personal Training",
                subTitle: workoutData ? workoutData.wodSubtitle : isLivePT ? activeConsultation.consultationProduct.productSpecs.cultWorkoutName : "",
                image: activeConsultation.imageUrl,
                action: {
                    actionType: "NAVIGATION",
                    url: actionUrl
                },
                sections: workoutData && workoutData.workouts ? [
                    {
                        title: "WORKOUT",
                        text: workoutData.workouts
                    }
                ] : undefined,
                footer: {
                    texts: footerTexts
                }
            }
            if (!AppUtil.isNewLivePTBookingPageSupported(userContext) && !AppUtil.isWeb(userContext)) {
                activity.footer.info = `${startDateText}, ${startTimeText}`
                activity.subTitle = `With ${doctor.name}`
            }
            const isTwilioSupportedForSGT = await AppUtil.isLiveSGTTwilioSupported(userContext, hamletBusiness)
            if (isTwilioSupportedForSGT && !_.isEmpty(activeConsultation.appointmentActionsWithContext) && isLiveSGT && activeConsultation.appointmentActionsWithContext.videoActionWithContext.action.actionPermitted) {
                const appointmentActionsWithContext: AppointmentActionsWithContext = activeConsultation.appointmentActionsWithContext
                if (!_.isEmpty(appointmentActionsWithContext)) {
                    activity.footer.action = CareUtil.getTwilioQuickJoinAction(userContext, activeConsultation, actionUrl)
                }
            } else if (!_.isEmpty(activeConsultation.allowedActions) && activeConsultation.allowedActions.includes("JOIN_ZOOM_MEETING")) {
                activity.footer.action = await careBusiness.getJoinZoomMeetingActionfromUrl(userContext, activeConsultation.zoomLink, activeConsultation.patientId, activeConsultation.consultationProduct.doctorType, activeConsultation.bookingId, activeConsultation.zoomParticipantId, "JOIN")
            } else {
                activity.footer.action = {
                    actionType: "NAVIGATION",
                    title: "VIEW",
                    url: actionUrl,
                }
            }
            return activity
        })
        this.classes = await Promise.all(classesPromise)
        return {
            widgetType: "MY_LIVE_CLASSES_WIDGET",
            header: {
                title: "Upcoming Sessions",
                subTitle: "Join link will be enabled 10 mins before session",
                titleStyle: headerTitleStyle,
            },
            containerStyle: containerStyle,
            classes: this.classes
        }
    }

    private getWorkoutData(wod: SimpleWod): { wodSubtitle: string, workouts: string } {
        return {
            wodSubtitle: CultUtil.getWodBodyParts(wod),
            workouts: CultUtil.getWodMovements(wod)
        }
    }
}
