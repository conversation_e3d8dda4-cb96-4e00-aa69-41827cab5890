import { IBaseWidget, IServiceInterfaces, NotServiceableWidget, UserContext } from "@curefit/vm-models"

export class NotServiceableWidgetView extends NotServiceableWidget {
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        this.image = "/image/clp/non_servicable_location.png"
        this.title = "Oh! we are not available in your location"
        this.action = {
            actionType: "SHOW_CHANGE_CITY",
            meta: {
                canSkip: true
            },
            title: "CHANGE LOCATION",
        }
        this.showDivider = false
        this.dividerType = "NONE"
        return this
    }
}