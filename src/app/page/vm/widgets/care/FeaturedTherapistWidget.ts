import { DoctorDetailWidgetV2 } from "./../../../../common/views/WidgetView"
import * as _ from "lodash"
import { Action, BaseWidget, CareWidgetUtil, Header, IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../../../page/vm/ServiceInterfaces"
import { AgentFilterInfos, AgentRankingScheme, TherapistFilter, TherapyEvaluationResponse } from "@curefit/albus-client"
import { CareUtil, CUSTOM_SUPPORTED_DOCTOR_FILTER } from "../../../../util/CareUtil"
import AppUtil from "../../../../util/AppUtil"
import { getDoctorDetailWidgetV2 } from "../../../../care/CareDoctorSearchPageView"
import { ConsultationProduct, Patient } from "@curefit/care-common"

export class FeaturedTherapistWidget extends BaseWidget {

    header: Header
    list: DoctorDetailWidgetV2[]
    productId: string
    applyUserFilter?: boolean
    useCustomFilter?: TherapistFilter[]
    noOfDoctorToShow?: number
    doctorCardFooterAction: Action
    onlyFeaturedRequired?: boolean
    footerSeeMoreAction?: Action
    isHorizontal?: boolean

    constructor() {
        super("FEATURED_THERAPIST_WIDGET")
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: any }): Promise<IBaseWidget> {
        if (!this.productId) {
            return undefined
        }

        const product = <ConsultationProduct>(await interfaces.catalogueService.getProduct(this.productId))

        if (product) {
            const categoryFiltersPromise = interfaces.healthfaceService.getDoctorListingFilters(this.productId)
            const therapistFormResponsePromise: Promise<TherapyEvaluationResponse> = this.applyUserFilter ? interfaces.healthfaceService.getTherapyFormEvaluation() : Promise.resolve({ therapistFilters: this.useCustomFilter || []})
            const selfPatient = userContext.sessionInfo.isUserLoggedIn ? await CareUtil.getOrCreatePatient(userContext, interfaces.userCache, interfaces.healthfaceService) : undefined
            const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [this.productId], AppUtil.callSourceFromContext(userContext), interfaces, false)
            const appliedFilters =  !_.isEmpty((await therapistFormResponsePromise)?.therapistFilters)  ? {
                "SUB_SPECIALITY_FILTER": (await therapistFormResponsePromise)?.therapistFilters?.join(",")
            } : undefined


            if (!this?.header?.seemore) {
                this.header.seemore = {
                    actionType: "APPLY_DOCTOR_LISTING_FILTER",
                    meta: {
                        url: CareUtil.specialistListingAction(userContext, product, false, selfPatient?.id, undefined, undefined, undefined, undefined, undefined, undefined, undefined, true)?.url,
                        appliedFilters
                    }
                }
            }

            this.footerSeeMoreAction = this?.header?.seemore

            const isOfflineMode = product.consultationMode === "INCENTRE"
            const cityId = isOfflineMode ? userContext.userProfile.cityId : undefined
            const isOverRideMindTherapyDoctortypeProduct = _.get(product, "infoSection.isPartOfConsultationPack")
            const categoryFilters = (await categoryFiltersPromise).agentFilterInfos.filter(item => !CUSTOM_SUPPORTED_DOCTOR_FILTER.includes(item.internalName))
            const doctorsList = await interfaces.healthfaceService.getAllDoctorDetailsWithAvailabilityV3({
                types: [product.doctorType],
                cityCode: cityId,
                consultationMode: product.consultationMode,
                slotsPerDoctor: 3,
                userId:  userContext.userProfile.userId,
                patientIds: undefined,
                doctorAvailability: [],
                languages: [],
                cities: [],
                pageNum: 0,
                pageSize: this.noOfDoctorToShow || 4,
                productCode: isOverRideMindTherapyDoctortypeProduct ? this.productId : undefined,
                userCityCode: userContext.userProfile.cityId,
                latitude: !_.isNaN(userContext.sessionInfo.lat) ? userContext.sessionInfo.lat : undefined,
                longitude: !_.isNaN(userContext.sessionInfo.lon) ? userContext.sessionInfo.lon : undefined,
                agentAttributeFilters: categoryFilters && appliedFilters ? CareUtil.createAgentAttributeFilters(appliedFilters, categoryFilters) : undefined,
                rankingScheme: AgentRankingScheme.COMPREHENSIVE,
                onlyFeaturedRequired: this.onlyFeaturedRequired != undefined ? this.onlyFeaturedRequired : true
            })

            if (_.isEmpty(doctorsList)) {
                return undefined
            }

            this.list = await Promise.all(doctorsList.map(doctor => {
                return getDoctorDetailWidgetV2(userContext, product, doctor, {
                    productId: this.productId,
                    patientId: selfPatient?.id,
                    isDoctorSearch: true,
                    selectedFilterItems: [],
                    categoryFilters: categoryFilters,
                    isPriceAvailableInListingApi: true,
                    isUnifiedDoctorUISupported: true
                }, {}, false, offerPromise, true)
            }))
            const patients: Patient[] = userContext.sessionInfo.isUserLoggedIn ? (userContext?.userProfile?.carePatientPromise ? await userContext?.userProfile?.carePatientPromise : await interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)) : []
            this.doctorCardFooterAction = CareUtil.getDoctorFooterCardAction(userContext, product, patients, {
                productId: this.productId,
                patientId: selfPatient?.id,
                isDoctorSearch: true,
                selectedFilterItems: [],
                categoryFilters: categoryFilters,
                isPriceAvailableInListingApi: true,
                isUnifiedDoctorUISupported: true
            })

            return this
        }


        return undefined

    }
}
