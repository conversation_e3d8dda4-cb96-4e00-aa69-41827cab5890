import * as _ from "lodash"
import { Action, MealAction } from "../../../common/views/WidgetView"
import { MAX_CART_SIZE } from "../../../util/MealUtil"
import { EatMealsWidget, WidgetType } from "@curefit/vm-models"
import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    SessionInfo,
    UserContext
} from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { RUPEE_SYMBOL } from "@curefit/base-utils"
import { DepositRequest } from "@curefit/fitcash-common"
import { Constants } from "@curefit/base-utils"

interface FitCashDescriptionData {
    balance: number
    title: string
    description1: string
    description2: string
    icon: string
}

export class FitCashDescriptionWidget
    extends BaseWidget {
    data: FitCashDescriptionData
    action: Action
    constructor(widgetType: WidgetType = "FC_DESCRIPTION_WIDGET") {
        super(widgetType)
    }
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
        const userId: string = userContext.userProfile.userId
        try {
            const segments: Record<string, string> = await interfaces.redisDao.getHashAllFields(`Fitcash::Seeding::Segments`)
            for (const segmentId in segments) {
                // for all segments user belongs to
                const details = JSON.parse(segments[segmentId])
                if (await interfaces.redisDao.existsInSet(`Fitcash::Seeding::Users::${segmentId}`, userId)) {
                    // deposit for user
                    const depositRequest: DepositRequest = {
                        userId: userId,
                        transactionId:  `${details.campaignId}:${segmentId}:${userId}`,
                        amount: details.amount,
                        currency: details.currency,
                        type: details.type,
                        expiresOn: details.expiresOn,
                        vertical: details.vertical,
                        title: details.title,
                        subTitle: details.subTitle,
                        meta: details.meta
                    }
                    try {
                        await interfaces.fitcashService.deposit(depositRequest)
                        await interfaces.redisDao.removeFromSet(`Fitcash::Seeding::Users::${segmentId}`, userId)
                    } catch (e) {
                        interfaces.logger.info(`Depositing seeding fitcash for user ${userId} on eatclp via queue`)
                        const attributes: Map<string, any> = new Map<string, any>()
                        attributes.set("EVENT_TYPE", "SEEDING_DEPOSIT")
                        interfaces.queueService.sendMessage(Constants.getSQSQueue("FITCASH-DEPOSITS"), depositRequest, attributes)
                    }
                }
            }
        } catch (e) {
            interfaces.logger.error(`Error while depositing seeding fitcash for user ${userId} on eatclp`)
        }

        const walletBalance = await interfaces.fitcashService.balance(
            userContext.userProfile.userId,
            userContext.userProfile.city.country.currencyCode
        )
        const balance = Math.round(walletBalance.balance / 100)
        if (balance <= 0) {
            return
        }
        this.data = {
            balance,
            title: "Fitcash Balance",
            description1: `1 Fitcash =  1 ${RUPEE_SYMBOL}`,
            description2: "Auto applies\nbefore payment",
            icon: "FITCASH",
        }
        return this
    }
}