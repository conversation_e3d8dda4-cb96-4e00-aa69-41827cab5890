import { BannerActionWidget, CareWidgetUtil, IBaseWidget, UserContext } from "@curefit/vm-models"
import { ConsultationProduct } from "@curefit/care-common"
import { CareUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import AppUtil from "../../../util/AppUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"

export class BannerActionWidgetView extends BannerActionWidget {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {

        const { userProfile, sessionInfo } = userContext
        if (this.productType === "LIVE_PERSONAL_TRAINING") {
            const consultationOffer = await CareWidgetUtil.getCareProductOffersFromUserContext(
                userContext,
                "CONSULTATION",
                [this.productId],
                AppUtil.callSourceFromContext(userContext),
                interfaces
            )
            const product: ConsultationProduct = <ConsultationProduct>(await interfaces.catalogueService.getProduct(this.productId))
            const offerDetails = OfferUtil.getPackOfferAndPrice(product, consultationOffer)
            if (product && CareUtil.isLivePTDoctorType(product.doctorType)) {
                const priceTitle = offerDetails.price.listingPrice === 0 ? "Free" : `${RUPEE_SYMBOL} ${offerDetails.price.listingPrice}`
                const actionTitle = `Book class for ${priceTitle}`
                this.action = await interfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId: this.productId, actionTitle, subCategoryCode: "LIVE_PERSONAL_TRAINING" })
            }
        }

        return this
    }
}
