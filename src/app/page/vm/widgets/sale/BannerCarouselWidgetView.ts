import { WidgetTemplate } from "@curefit/vm-common"
import {
    addSegmentDetail,
    Action,
    BannerCarouselWidget, BannerExperiment,
    BannerItem, BannerVariant, DimensionValue,
    IBaseWidget,
    IServiceInterfaces,
    UserContext,
    validateBaseDataItemCondition,
    OLD_TEMPLATE_ID_PARAM_NAME,
    PMS_TEMPLATE_ID_PARAM_NAME
} from "@curefit/vm-models"
import * as _ from "lodash"
import { UrlPathBuilder, HealthfaceTenant, ProductType } from "@curefit/product-common"
import { ActionUtil } from "../../../../util/ActionUtil"
import { User } from "@curefit/user-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import CultUtil, { CultOrMindSummary, transformCultSummaryMap } from "../../../../util/CultUtil"
import { UserOfferEligibilityResponse } from "@curefit/offer-common"
import { TimeUtil, CdnUtil } from "@curefit/util-common"
import CareUtil, {
    CareUtil as AppCareUtil,
    LIVE_PT_SNC_PRODUCT_ID,
    LIVE_SGT_SNC_PRODUCT_ID
} from "../../../../util/CareUtil"
import { ConsultationProduct, Patient, DiagnosticProductResponse } from "@curefit/care-common"
import { AnnouncementKey } from "../../../../announcement/Announcement"
import { getPreBookingActions } from "../../../../util/NCUtil"
import { BundleSessionSellableProduct } from "@curefit/albus-client"
import { IntelligentDimension, LivePlatform } from "@curefit/vm-models"
import { GENDER_KEY, PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY, INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP, FORMAT_AFFINITY_CHECK, WEEKDAY_CHECK, WEEKEND_CHECK } from "./BannerUtil"
import AppUtil from "../../../../util/AppUtil"
import { UserAgent } from "@curefit/base-common"
import { SkuPackUtil } from "../../../../util/SkuPackUtil"

export class BannerCarouselWidgetView extends BannerCarouselWidget {
    layoutProps: any

    private getDimensionValue(dimensionValues: DimensionValue[], dimension: IntelligentDimension): string {
        const values = dimensionValues
            .filter(dimensionValue => dimensionValue.dimension === dimension)
            .map(x => x.value)
            .filter(x => x)
        return _.isEmpty(values) ? null : values.find(x => x)
    }

    private bannerVariantToBannerItem(bannerVariant: BannerVariant): Partial<BannerItem> {
        const bannerItem: Partial<BannerItem> = {
            action: bannerVariant.action,
            actionV2: bannerVariant.actionV2,
            image: bannerVariant.image,
            webImage: bannerVariant.webImage,
            mwebImage: bannerVariant.mwebImage,
            videoUrl: bannerVariant.videoUrl,
            loopVideo: bannerVariant.loopVideo,
            contentMetric: bannerVariant.contentMetric,
            liveAllowedPlatforms: bannerVariant.liveAllowedPlatforms
        }

        const usedIntelligentDimensions = bannerVariant.dimensionValues.map(dimensionValue => dimensionValue.dimension).join(",")
        bannerItem.contentMetric.usedIntelligentDimensions = usedIntelligentDimensions
        return bannerItem
    }

    private checkIfTimeOfDaySatisfied(userContext: UserContext, bannerVariant: BannerVariant): boolean {
        const todValueToCheck = this.getDimensionValue(bannerVariant.dimensionValues, "TIME_OF_DAY")
        // If there is no condition value specified in banner variant then no need to evaluate
        if (!todValueToCheck) {
            return true
        }
        const timeRange = todValueToCheck.split("-")
        const startTimeHourMin = timeRange[0].split(":")
        const endTimeHourMin = timeRange[1].split(":")

        const startTime = parseInt(startTimeHourMin[0]) * 60 + parseInt(startTimeHourMin[1])
        const endTime = parseInt(endTimeHourMin[0]) * 60 + parseInt(endTimeHourMin[1])

        const hourMin = TimeUtil.now(userContext.userProfile.city.timezone)
        const currentTime = hourMin.hour * 60 + hourMin.min
        return startTime <= currentTime && currentTime < endTime
    }

    private checkLiveBannerCondition(bannerItem: Partial<BannerItem>, userContext: UserContext, queryParams: { [filterName: string]: string }): boolean {
        const ua = userContext.sessionInfo.userAgent
        const osName = userContext.sessionInfo.osName
        const livePlatform: LivePlatform = UserAgent.DESKTOP === ua || UserAgent.MBROWSER === ua
            ? "LIVE_WEB" : osName == "android" ? "LIVE_ANDROID" : "LIVE_IOS"
        // If condition is specified check and return accordingly
        if (!_.isEmpty(bannerItem.liveAllowedPlatforms)) {
            return bannerItem.liveAllowedPlatforms.find(a => a === livePlatform) ? true : false
        }

        // If any live page the condition is not specified treat it as invalid
        if (queryParams["isLivePage"] === "TRUE") {
            if (_.isEmpty(bannerItem.liveAllowedPlatforms)) {
                return false
            }
        }
        return true
    }

    private async getPersonalizedBanners(interfaces: CFServiceInterfaces, userContext: UserContext, experimentId: string, maxNumBanners: number,
        experiments: BannerExperiment[], queryParams: { [filterName: string]: string }): Promise<Partial<BannerItem>[]> {
        const attributesToFetch = Object.values(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY)
        attributesToFetch.push(...Object.values(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP))

        const hamletExperimentMap = (await interfaces.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(userContext, [experimentId]))).assignmentsMap
        const bucket = hamletExperimentMap[experimentId]?.bucket?.bucketId ?? null
        if (!bucket) {
            return []
        }
        const userBannerExperiment = experiments.filter(experiment => experiment.bucketId === bucket).find(x => x)
        const appTenant = AppUtil.getAppTenantFromUserContext(userContext)
        const userAttributesResponse = await interfaces.userAttributeClient.getCachedUserAttributes(parseInt(userContext.userProfile.userId), appTenant, attributesToFetch)
        const userAttributes = userAttributesResponse.attributes
        let bannerVariants = userBannerExperiment.variants
        const sortDimensions = userBannerExperiment.dimensions.filter(
            dimension => "CATEGORY_AFFINITY" == dimension ||
                FORMAT_AFFINITY_CHECK.includes(dimension)
        )
        if (!_.isEmpty(sortDimensions)) {
            bannerVariants = this.sortByCategoryOrFormatAffinity(sortDimensions, bannerVariants, userAttributes)
        }
        const tz = userContext.userProfile.city.timezone
        const date = TimeUtil.todaysDate(tz)
        const isWeekDay = TimeUtil.isWeekDay(date, TimeUtil.DEFAULT_DATE_FORMAT, tz)
        return bannerVariants
            .filter(bannerVariant => {
                if (userBannerExperiment.dimensions.includes("GENDER") && !this.checkIfGenderSatisfied(bannerVariant, userAttributes)) {
                    return false
                }
                if (userBannerExperiment.dimensions.includes("TIME_OF_DAY") && !this.checkIfTimeOfDaySatisfied(userContext, bannerVariant)) {
                    return false
                }
                if (isWeekDay) {
                    for (const dimension of WEEKDAY_CHECK) {
                        if (userBannerExperiment.dimensions.includes(dimension)
                            && !this.checkRashiAttribute(bannerVariant, dimension, userAttributes)) {
                            return false
                        }
                    }
                } else {
                    for (const dimension of WEEKEND_CHECK) {
                        if (userBannerExperiment.dimensions.includes(dimension)
                            && !this.checkRashiAttribute(bannerVariant, dimension, userAttributes)) {
                            return false
                        }
                    }
                }
                if (userBannerExperiment.dimensions.includes("AGE") && !this.checkIfAgeSatisfied(userContext, bannerVariant, userAttributes)) {
                    return false
                }
                return true
            })
            .map(bannerVariant => this.bannerVariantToBannerItem(bannerVariant))
            .filter(x => !!x)
            .filter(x => this.checkLiveBannerCondition(x, userContext, queryParams))
            .slice(0, maxNumBanners)

    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        // Temp hack as having issues with cyclops portal
        this.edgeToEdge = this.edgeToEdge === "TRUE" ? true : (this.edgeToEdge === "FALSE" ? false : this.edgeToEdge)
        this.maxNumBanners = this.maxNumBanners ? parseInt(this.maxNumBanners.toString()) : undefined
        const userProfile = userContext.userProfile
        const now = new Date()
        const { sessionInfo } = userContext
        const result = this.checkForWidgetTTL(userContext, queryParams)
        if (result.isExpired) {
            return undefined
        }

        // Check if any personalized banner available, if not uses the default config
        const bannerItemPromises = (this.data ?? []).map(async (banner: BannerItem) => {
            if (banner.bannerExperimentId) {
                const personalizedBanners = await this.getPersonalizedBanners(interfaces, userContext,
                    banner.bannerExperimentId, banner.maxNumberOfPersonalizedBanners,
                    banner.bannerExperiments, queryParams)
                if (personalizedBanners && personalizedBanners.length > 0) {
                    return personalizedBanners
                }
            }
            return [banner]
        })
        let bannerItems: Partial<BannerItem>[] = []
        for (const bannerItemPromise of bannerItemPromises) {
            const bannerItemPromiseResults = await bannerItemPromise
            bannerItems = bannerItems.concat(bannerItemPromiseResults)
        }
        const bannerPromises = _.map(bannerItems, async bannerItem => {
            const banner = bannerItem as BannerItem

            await SkuPackUtil.getSkuTitle(banner, interfaces, userContext, queryParams)

            if (!_.isEmpty(banner.actionV2)) {
                const vmAction = await interfaces.userActionMappingBusiness.getVMActionFromActionV2(banner.actionV2, interfaces, userContext)
                if (!_.isEmpty(vmAction)) {
                    banner.action = vmAction
                }
            }
            // Check for banner action segments, deleting action object, when segment doesnot match
            if (banner.action && !_.isEmpty(banner.action.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.action.segmentIds, userContext)
                if (!segment) {
                    delete banner.action
                }
            }

            if (banner.action && !_.isEmpty(banner.action.actionType) && banner.action.actionType === "SHOW_DYNAMIC_INTERVENTION") {
                const announcementId = _.get(banner, "action.meta.announcementId")
                const announcement = await interfaces.announcementDao.read(AnnouncementKey.from(userContext.userProfile.userId, announcementId))
                if (announcement && announcement.state == "DISMISSED") {
                    // fetch intervention based on id
                    // replace banner's action
                    // const view = await interfaces.announcementBusiness.announcementViewBuilder.buildAnnouncementView(userContext, announcement)

                    // getAllValidAnnouncements returns an array of announcement, but since we're
                    // sending only 1 id, it'll return only one announcement.
                    const data = await interfaces.announcementBusiness.getAllValidAnnouncements(interfaces.pageService, userContext, false, [announcementId])
                    if (data && !_.isEmpty(data)) {
                        banner.action = _.get(data[0], "announcementData.action")
                    }
                }
            }

            if (banner.videoUrl) {
                if (banner.action) {
                    const relativeUrl = "curefit-content/" + banner.videoUrl
                    const url = `curefit://videoplayer?videoUrl=${encodeURIComponent(relativeUrl)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(relativeUrl))}`
                }
                banner.videoUrl = UrlPathBuilder.getPrefixedVideoPath(banner.videoUrl)
            }

            if (banner.timer && banner.timer.timerEndTimeWithTz) {
                const timerEndTime = new Date(banner.timer.timerEndTimeWithTz.date.toString()).getTime()
                if (timerEndTime > now.getTime()) {
                    banner.timer.timerEndTime = new Date(banner.timer.timerEndTimeWithTz.date.toString()).getTime()
                    banner.timer.action = banner.action
                } else {
                    // removing banner timer, if timerEndtime is less than current time
                    delete banner.timer
                }
            }

            // Temp fix: Remove the timer if there is no timer end time given as app is rendering empty layout in this case
            if (banner.timer && !banner.timer.timerEndTimeWithTz) {
                delete banner.timer
            }

            if (banner.timer && banner.timer.privateOfferId) {
                const user: User = await userContext.userPromise
                if (!user.isPhoneVerified || !user.phone) {
                    return undefined
                }
                const eligibility: UserOfferEligibilityResponse = await interfaces.offerService.getOfferEligibilityForUser(banner.timer.privateOfferId, user.phone, "PHONE")
                if (eligibility && eligibility.status === "OK") {
                    const privateOfferEndTime: number = new Date(eligibility.userOfferEligibility.endDate.toString()).getTime()
                    if (privateOfferEndTime > new Date().getTime()) {
                        banner.timer.timerEndTime = privateOfferEndTime
                    } else {
                        // private offer has expired for user
                        return undefined
                    }
                } else {
                    // no private offer active for user
                    return undefined
                }
            }

            if (sessionInfo.userAgent === "MBROWSER") {
                banner.image = UrlPathBuilder.prefixSlash(banner.mwebImage)
            } else if (sessionInfo.userAgent === "DESKTOP") {
                banner.image = UrlPathBuilder.prefixSlash(banner.webImage)
            } else {
                banner.image = UrlPathBuilder.prefixSlash(banner.image)
            }

            // Adding Widget Id to Meta
            if (banner.action && banner.action.actionType === "SCROLL_TO_WIDGET") {
                if (banner.action.meta) {
                    banner.action.meta = Object.assign({}, banner.action.meta, { widgetId: banner.action.widgetId })
                } else {
                    banner.action.meta = { widgetId: banner.action.widgetId }
                }
                delete banner.action.widgetId
            }


            // handle custom action
            if (!_.isEmpty(banner.action) && banner.action.actionType === "BOOK_CULT_JUNIOR_TRIAL") {
                const primaryUser: User = await userContext.userPromise
                const subUsersPromises = primaryUser && Array.isArray(primaryUser.subUserRelations) && primaryUser.subUserRelations.map(item => interfaces.userCache.getUser(item.subUserId))
                let subUsers: User[] = subUsersPromises && await Promise.all(subUsersPromises)
                const bulkCultSummary = await interfaces.userCache.getCultSummaryForAllSubUsers(primaryUser.id)
                const cultMembershipDataMap: { [userId: string]: CultOrMindSummary } = transformCultSummaryMap(bulkCultSummary, "CULT_FIT")
                if (!_.isEmpty(cultMembershipDataMap)) {
                    const trialEligibleSubUserIds = _.map(Object.keys(cultMembershipDataMap), userId => {
                        const cultMembershipData: CultOrMindSummary = cultMembershipDataMap[userId]
                        if (cultMembershipData.trialEligibility) {
                            return userId
                        }
                    })
                    subUsers = _.map(subUsers, subUser => {
                        if (trialEligibleSubUserIds.indexOf(subUser.id) < 0) {
                            return { ...subUser, disabled: true }
                        } else {
                            return subUser
                        }
                    })
                }
                banner.action = <Action>ActionUtil.getCultJuniorClassBookingAction(userContext, "FITNESS", primaryUser, subUsers, "clpTrialBanner")
                if (banner.action.meta) {
                    banner.action.meta.calloutText = "All trials have been used for child names shown in disabled state"
                }
            } else if (!_.isEmpty(banner.action) && banner.action.actionType === "BOOK_LIVE_PT_SESSION") {
                banner.action = <Action>await interfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId: LIVE_PT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_PERSONAL_TRAINING" })
            } else if (!_.isEmpty(banner.action) && banner.action.actionType === "BOOK_LIVE_SGT_SESSION") {
                banner.action = <Action>await interfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId: LIVE_SGT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_SGT" })
            } else if (!_.isEmpty(banner.action) && banner.action.actionType === "SHOW_PATIENT_SELECTION") {
                if (banner.action.meta && banner.action.meta.productId) {
                    if (!userContext.sessionInfo.isUserLoggedIn) {
                        banner.action = {
                            actionType: "SHOW_ALERT_MODAL",
                            title: "BOOK",
                            meta: {
                                title: "Login Required!",
                                subTitle: "Please login to continue",
                                actions: [{ actionType: "LOGOUT", title: "Login" }]
                            }
                        }
                    } else {
                        if (banner.action.meta.subCategoryCode) {
                            const subCategoryCode = banner.action.meta.subCategoryCode
                            const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
                            const healthfaceProduct = await interfaces.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: subCategoryCode, productCodeCsv: banner.action.meta.productId }, healthfaceTenant)
                            if (healthfaceProduct.length > 0) {
                                const bundleProduct = <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
                                const patientsList: Patient[] = await interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)
                                const action: Action = getPreBookingActions(userContext, bundleProduct.infoSection.numberOfSessions, true, [], `curefit://carecartcheckout?productId=${bundleProduct.productCode}&subCategoryCode=${bundleProduct?.subCategoryCode}`, patientsList)[0]
                                banner.action = action
                            } else {
                                banner.action = undefined
                            }
                        } else {
                            const product: ConsultationProduct = <ConsultationProduct>(await interfaces.catalogueService.getProduct(banner.action.meta.productId))
                            const isGP99Product = CareUtil.isGP99DoctorType(product.doctorType)
                            if (isGP99Product) {
                                const patientsList: Patient[] = await interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)
                                banner.action = { ...CareUtil.consultationCheckoutAction(userContext, product, patientsList), title: "BOOK" }
                            } else {
                                banner.action = undefined
                            }
                        }
                    }
                }
            }
            if (!_.isEmpty(banner.action) && (banner.action.url === "curefit://classbooking" || banner.action.url === "curefit://classbookingv2") && userContext.userProfile) {
                const currentUser: User = await userContext.userPromise
                const nuxLandingPageUrl = await CultUtil.getNuxLandingPageUrl(userContext, currentUser, interfaces.hamletBusiness, interfaces.userCache, interfaces.announcementBusiness)
                if (nuxLandingPageUrl) {
                    banner.action.url = nuxLandingPageUrl
                }
            }
            if (!_.isEmpty(banner.action) && banner.action.url &&
                (banner.action.url.includes(OLD_TEMPLATE_ID_PARAM_NAME) || banner.action.url.includes(PMS_TEMPLATE_ID_PARAM_NAME))) {
                const action: any = await this.getPackIdUrlFromTemplate(interfaces, userContext, banner.action) // PMS changes required
                // It's just not be configured for certain cities hence with this check we are preventing it from failing
                if (action) {
                    banner.action = action
                } else {
                    return undefined
                }
            }
            if (!_.isEmpty(banner.timer) && !_.isEmpty(banner.timer.action) && banner.timer.action.url &&
                (banner.timer.action.url.includes(OLD_TEMPLATE_ID_PARAM_NAME) || banner.timer.action.url.includes(PMS_TEMPLATE_ID_PARAM_NAME))) {
                const action = await this.getPackIdUrlFromTemplate(interfaces, userContext, banner.timer.action)
                // It's just not be configured for certain cities hence with this check we are preventing it from failing
                if (action) {
                    banner.timer.action = action
                } else {
                    return undefined
                }
            }


            if (!validateBaseDataItemCondition(banner, now, userContext.userProfile.timezone)) {
                return undefined
            }
            if (!this.checkLiveBannerCondition(banner, userContext, queryParams)) {
                return undefined
            }

            if (banner.contentMetric) {
                banner.contentMetric.contentUrl = banner.image
            }
            if (!_.isEmpty(banner.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.segmentIds, userContext)
                if (segment) {
                    await this.appendOfferTnCIfConfigured(banner, interfaces)
                    addSegmentDetail(banner.contentMetric, segment)
                    return banner
                }
            } else {
                await this.appendOfferTnCIfConfigured(banner, interfaces)
                return banner
            }
        })
        const banners = await Promise.all(bannerPromises)
        this.data = banners.filter(banner => !!banner)
        this.data = this.data.slice(0, this.maxNumBanners)

        if (_.isEmpty(this.data)) {
            return undefined
        }
        if (!_.isNil(this.templateId)) {
            const widgetTemplate: WidgetTemplate = await interfaces.pageService.getWidgetTemplate(this.templateId)
            if (_.isNil(widgetTemplate)) {
                return undefined
            }
            if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
                this.layoutProps = widgetTemplate.templateData.app
            } else {
                this.layoutProps = widgetTemplate.templateData.web
            }

            if (!this.layoutProps) {
                return undefined
            }

            // Backwards compatibility //
            this.edgeToEdge = this.layoutProps.edgeToEdge
            this.backgroundColor = this.layoutProps.backgroundColor
            if (_.isNumber(this.layoutProps.bannerWidth) && _.isNumber(this.layoutProps.bannerHeight)) {
                this.bannerRatio = this.layoutProps.bannerWidth + ":" + this.layoutProps.bannerHeight
            }
            if (this.showDivider === true && _.isUndefined(this.dividerType)) {
                this.dividerType = "LARGE"
            }
            // End of backwards compatibility code //
        }
        return this
    }

    private async appendOfferTnCIfConfigured(banner: BannerItem, interfaces: IServiceInterfaces) {
        // Add offer t&c as action if offer id is tagged
        if (banner.offerId) {
            const offer = (await interfaces.offerServiceV3.getOffer(banner.offerId))?.data
            banner.action = {
                "actionType": "SHOW_OFFERS_TNC_MODAL",
                "meta": {
                    "title": "Terms and Conditions",
                    "subTitle": "",
                    "dataItems": offer.tNc
                }
            }
        }
    }

    private checkForWidgetTTL(userContext: UserContext, queryParams: { [name: string]: string }): {
        isExpired: boolean
    } {
        const widgetId = queryParams.widgetId
        const widget = _.find(userContext.sessionInfo.sessionData.widgetStore, widget => {
            return widget.widgetId === widgetId
        })
        if (this.ttlExpiryInMs) {
            return this.checkForTTLExpiryInMs(widgetId, userContext, widget)
        } else if (this.ttlViewCount) {
            return this.checkForViewCountTTL(widgetId, userContext, widget)
        } else {
            return {
                isExpired: false
            }
        }
    }

    private checkIfGenderSatisfied(bannerVariant: BannerVariant, userAttributes: Map<string, any>): boolean {
        const conditionValue = this.getDimensionValue(bannerVariant.dimensionValues,
            "GENDER")
        // If there is no condition value specified in banner variant then no need to evaluate
        if (conditionValue == null) {
            return true
        }
        let genderValue = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP["GENDER"])
        if (genderValue == null) {
            genderValue = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP["GENDER_PREDICTED"])
        }
        const finalValue = genderValue == null ? "default" : genderValue
        return conditionValue.toLowerCase() == finalValue.toLowerCase()
    }

    private checkIfAgeSatisfied(userContext: UserContext, bannerVariant: BannerVariant, userAttributes: Map<string, any>): boolean {
        const ageValueToCheck = this.getDimensionValue(bannerVariant.dimensionValues,
            "AGE")
        // If there is no condition value specified in banner variant then no need to evaluate
        if (ageValueToCheck == null) {
            return true
        }
        const ageRange = ageValueToCheck.split("-")
        const birthday = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP["BIRTHDAY"])
        if (birthday == null) {
            if (ageRange[0].toLowerCase() == "default")
                return true
            else
                return false
        } else {
            if (ageRange[0].toLowerCase() == "default")
                return false
            const now = TimeUtil.getMomentNow(userContext.userProfile.timezone)
            const birthdayDate = TimeUtil.getMomentForDateString(birthday, userContext.userProfile.timezone,
                "YYYY-MM-DDTHH:mm:ss.sssZ")
            const age = now.diff(birthday, "year")
            if (Number(ageRange[0]) <= Number(age) &&
                Number(age) <= Number(ageRange[1])) {
                return true
            } else {
                return false
            }
        }
    }
    private checkRashiAttribute(bannerVariant: BannerVariant, dimension: IntelligentDimension, userAttributes: Map<string, any>): boolean {
        const conditionValue = this.getDimensionValue(bannerVariant.dimensionValues, dimension)
        // If there is no condition value specified in banner variant then no need to evaluate
        if (conditionValue == null) {
            return true
        }
        const rashiAttributeValue = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP[dimension])
        const finalValue = rashiAttributeValue == null ? "default" : rashiAttributeValue.toLowerCase()
        return conditionValue.toLowerCase() == finalValue
    }

    private checkBannerValidAccordingToAffinity(bannerVariant: BannerVariant, dimension: IntelligentDimension,
        userAttributes: Map<string, any>): boolean {
        const dimensionValue: String = this.getDimensionValue(bannerVariant.dimensionValues, dimension)
        if (dimensionValue == null || "DEFAULT" == dimensionValue) {
            return true
        } else {
            if (FORMAT_AFFINITY_CHECK.includes(dimension)) {
                const affinityValue = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP[dimension])
                if (affinityValue != null && this.getAffinityValueBasedOnArrayIndex(dimensionValue, dimension,
                    userAttributes) != null) {
                    return true
                } else {
                    return false
                }
            } else if ("CATEGORY_AFFINITY" == dimension) {
                const affinityValue = userAttributes.get(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY[<ProductType>(dimensionValue)])
                if (affinityValue != null) {
                    return true
                } else {
                    return false
                }
            } else {
                // Not supported config
                return false
            }
        }
    }

    private getAffinityValueBasedOnArrayIndex(entityToSearch: String, dimension: IntelligentDimension,
        userAttributes: Map<string, any>): Number {
        const rashiValue = userAttributes.get(INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP[dimension])
        if (rashiValue == null) {
            return null
        }
        const rashiValues: String[] = JSON.parse(rashiValue)
        return rashiValues.indexOf(entityToSearch) == -1 ? null : 100 - rashiValues.indexOf(entityToSearch)
    }

    private getAffinityValueForCategoryAffinity(productType: ProductType, userAttributes: Map<string, any>): Number {
        const value = userAttributes.get(PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY[productType])
        return value != null ? Number(value) : null
    }

    private compareBannerAccordingToAffinity(bannerVariantA: BannerVariant, bannerVariantB: BannerVariant,
        dimension: IntelligentDimension, userAttributes: Map<string, any>) {
        const dimensionValueA = this.getDimensionValue(bannerVariantA.dimensionValues, dimension)
        const dimensionValueB = this.getDimensionValue(bannerVariantB.dimensionValues, dimension)
        if (dimensionValueA != null && dimensionValueB == null)
            return -1
        if (dimensionValueA == null && dimensionValueB != null)
            return 1
        if (dimensionValueA == null && dimensionValueB == null)
            return 0

        let affinityValueA, affinityValueB
        if (FORMAT_AFFINITY_CHECK.includes(dimension)) {
            affinityValueA = this.getAffinityValueBasedOnArrayIndex(dimensionValueA, dimension, userAttributes)
            affinityValueB = this.getAffinityValueBasedOnArrayIndex(dimensionValueB, dimension, userAttributes)
        } else if ("CATEGORY_AFFINITY" == dimension) {
            affinityValueA = this.getAffinityValueForCategoryAffinity(<ProductType>(dimensionValueA), userAttributes)
            affinityValueB = this.getAffinityValueForCategoryAffinity(<ProductType>(dimensionValueB), userAttributes)
        }
        if (affinityValueA != null && affinityValueB == null) {
            return -1
        }
        if (affinityValueA == null && affinityValueB != null) {
            return 1
        }
        if (affinityValueA != null && affinityValueB != null) {
            return affinityValueA > affinityValueB ? -1 :
                affinityValueA < affinityValueB ? 1 : 0
        }
        return 0
    }

    private sortByCategoryOrFormatAffinity(dimensions: IntelligentDimension[], bannerVariants: BannerVariant[],
        userAttributes: Map<string, any>): BannerVariant[] {
        bannerVariants = bannerVariants.filter(bannerVariant => {
            for (const dimension of dimensions) {
                if (!this.checkBannerValidAccordingToAffinity(bannerVariant, dimension, userAttributes)) {
                    return false
                }
            }
            return true
        })

        bannerVariants.sort((bannerVariantA, bannerVariantB) => {
            let result = 0
            for (const dimension of dimensions) {
                result = this.compareBannerAccordingToAffinity(bannerVariantA, bannerVariantB, dimension, userAttributes)
                if (result != 0)
                    return result

            }
            return result
        })
        return bannerVariants
    }

    private checkForTTLExpiryInMs(widgetId: string, userContext: UserContext, widget?: { widgetId: string, meta: any }) {
        if (widget) {
            if (widget.meta.widgetFirstShownTimeInMillis) {
                return {
                    isExpired: Date.now() - widget.meta.widgetFirstShownTimeInMillis > this.ttlExpiryInMs ? true : false, // compute
                }
            } else {
                // This is to handle when the condition change from counter to millis
                widget.meta["widgetFirstShownTimeInMillis"] = Date.now()
                userContext.sessionInfo.isSessionUpdateNeeded = true
            }
        } else {
            const widget = {
                widgetId: widgetId,
                meta: {
                    widgetFirstShownTimeInMillis: Date.now(),
                }
            }
            this.updateWidgetInWidgetStore(userContext, widget)
        }
        return {
            isExpired: false
        }
    }

    private checkForViewCountTTL(widgetId: string, userContext: UserContext, widget?: { widgetId: string, meta: any }) {
        if (widget) {
            if (widget.meta.viewCount) {
                if (widget.meta.viewCount > this.ttlViewCount) {
                    return {
                        isExpired: true
                    }
                }
                widget.meta = {
                    viewCount: widget.meta.viewCount + 1
                }
                userContext.sessionInfo.isSessionUpdateNeeded = true
            } else {
                // This is to handle when the condition change from millis to counter
                widget.meta["viewCount"] = 1
                userContext.sessionInfo.isSessionUpdateNeeded = true
            }
        } else {
            const widget = {
                widgetId: widgetId,
                meta: {
                    viewCount: 1
                }
            }
            this.updateWidgetInWidgetStore(userContext, widget)
        }
        return {
            isExpired: false
        }
    }

    private updateWidgetInWidgetStore(userContext: UserContext, widget: { widgetId: string, meta: any }) {
        if (userContext.sessionInfo.sessionData.widgetStore) {
            userContext.sessionInfo.sessionData.widgetStore.push(widget)
        } else {
            userContext.sessionInfo.sessionData.widgetStore = [widget]
        }
        userContext.sessionInfo.isSessionUpdateNeeded = true
    }
}

export class ImageListWidgetView extends BannerCarouselWidgetView {
    constructor() {
        super("IMAGE_LIST_WIDGET")
    }
}

export class EmbeddedImageListWidgetView extends BannerCarouselWidgetView {
    constructor() {
        super("EMBEDDED_IMAGE_LIST_WIDGET")
    }
}

export class WebCarouselWidgetView extends BannerCarouselWidgetView {
    constructor() {
        super("WEB_CAROUSEL_WIDGET")
    }
}
