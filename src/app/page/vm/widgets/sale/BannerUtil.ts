import { ProductType } from "@curefit/product-common/dist/src/BaseProduct"
import { IntelligentDimension } from "@curefit/vm-models"

export const GENDER_KEY = "gender"

export const PRODUCT_TYPE_TO_AFFINITY_SCORE_KEY: Partial<{ [key in ProductType]: string }> = {
    "LIVE": "vertical_affinity_cult_live_with_decay",
    "FOOD": "vertical_affinity_eat_with_decay",
    "LIVE_SGT": "vertical_affinity_sgt_live_with_decay",
    "GEAR": "vertical_affinity_cult_gear",
    "LIVE_PERSONAL_TRAINING": "vertical_affinity_pt_live_with_decay",
    "NUTRITIONIST_CONSULTATION": "vertical_affinity_nutrition_consultation",
    "RECIPE": "vertical_affinity_eat_live_with_decay",
    "WHOLE_FIT": "vertical_affinity_whole_fit",
    "DIAGNOSTICS": "vertical_affinity_care_diagnostics",
    "CONSULTATION": "vertical_affinity_care_consultation",
    "THERAPY": "vertical_affinity_mind_consultation",
    "DIY_MEDITATION": "vertical_affinity_mind_live_with_decay",
}

export const PRODUCT_TYPE_TO_USAGE_FREQUENCY_KEY: Partial<{ [key in ProductType]: string }> = {
    "LIVE": "usage_frequency_cult_live",
    "FOOD": "usage_frequency_eat",
    "LIVE_SGT": "usage_frequency_sgt_live",
    "GEAR": "usage_frequency_cult_gear",
    "LIVE_PERSONAL_TRAINING": "usage_frequency_pt_live",
    "NUTRITIONIST_CONSULTATION": "usage_frequency_nutrition_consultation",
    "RECIPE": "usage_frequency_eat_live",
    "WHOLE_FIT": "usage_frequency_whole_fit",
    "DIAGNOSTICS": "usage_frequency_care_diagnostics",
    "CONSULTATION": "usage_frequency_care_consultation",
    "THERAPY": "usage_frequency_mind_consultation",
    "DIY_MEDITATION": "usage_frequency_mind_live",
}

export const INTELLIGENT_DIMENSION_TO_RASHI_KEY_MAP: Partial<{ [key in IntelligentDimension]: string }> = {
    "GENDER": "gender",
    "GENDER_PREDICTED": "gender_predicted",
    "AGE": "age",
    "BIRTHDAY": "birthday",
    "CULT_LIVE_WORKOUT_FORMAT_AFFINITY": "workout_format_affinity_cult_live_top",
    "CULT_LIVE_WORKOUT_TIME_AFFINITY_WEEKDAY": "workout_time_affinity_cult_live_weekday",
    "CULT_LIVE_WORKOUT_TIME_AFFINITY_WEEKEND": "workout_time_affinity_cult_live_weekend",
    "CULT_LIVE_WORKOUT_INTENSITY": "workout_intensity_affinity_cult_live_overall",
    "PT_LIVE_WORKOUT_FORMAT_AFFINITY": "workout_format_affinity_pt_live_top",
    "PT_LIVE_WORKOUT_TIME_AFFINITY_WEEKDAY": "workout_time_affinity_pt_live_weekday",
    "PT_LIVE_WORKOUT_TIME_AFFINITY_WEEKEND": "workout_time_affinity_pt_live_weekend",
    "CULT_WORKOUT_FORMAT_AFFINITY": "workout_format_affinity_cult_center_top",
    "CULT_WORKOUT_TIME_AFFINITY_WEEKDAY": "workout_time_affinity_cult_center_weekday",
    "CULT_WORKOUT_TIME_AFFINITY_WEEKEND": "workout_time_affinity_cult_center_weekend",


}

export const DIRECT_ATTRIBUTE_EQUALITY_CHECK: IntelligentDimension[] = ["GENDER", "CULT_LIVE_WORKOUT_INTENSITY"]
export const FORMAT_AFFINITY_CHECK: IntelligentDimension[] = ["CULT_LIVE_WORKOUT_FORMAT_AFFINITY", "PT_LIVE_WORKOUT_FORMAT_AFFINITY", "CULT_WORKOUT_FORMAT_AFFINITY"]
export const WEEKDAY_CHECK: IntelligentDimension[] = ["CULT_LIVE_WORKOUT_TIME_AFFINITY_WEEKDAY", "PT_LIVE_WORKOUT_TIME_AFFINITY_WEEKDAY", "CULT_WORKOUT_TIME_AFFINITY_WEEKDAY"]
export const WEEKEND_CHECK: IntelligentDimension[] = ["CULT_LIVE_WORKOUT_TIME_AFFINITY_WEEKEND", "PT_LIVE_WORKOUT_TIME_AFFINITY_WEEKEND", "CULT_WORKOUT_TIME_AFFINITY_WEEKEND"]

export const WIDGET_ID_TO_PRODUCT_TYPE = (() => {
    if (process.env.ENVIRONMENT === "STAGE") {
        return {
            "BLACK": "c913e627-6804-4f03-a395-419574b5fe3e_aurora",
            "GOLD": "c913e627-6804-4f03-a395-419574b5fe3e_aurora-gym",
            "LIVE": "ed2cbc2b-a955-4b06-805d-0963aff5e255",
            "PLAY": "4c833a2b-231e-4ec8-a2d5-8326dfe80613"
        }
    }
    return {
        "BLACK": "503e3ea7-aaf6-4e1b-ab9e-6a130960bf6a",
        "GOLD": "503e3ea7-aaf6-4e1b-ab9e-6a130960bf6agold",
        "LIVE": "086bec96-7b01-40db-a2de-9d5ea942d708",
        "PLAY": "10493fee-58aa-42ea-8f9c-8fef7d31efe4"
    }
})()




