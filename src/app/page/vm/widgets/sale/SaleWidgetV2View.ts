import { WidgetType, OfferInfoContainerStyle, TextStyle, ImageStyle, OfferInfoBorderStyle } from "@curefit/vm-common"
import { IBaseWidget, UserContext, SaleWidgetV2, SaleBannerProductInfo, SaleBannerOfferInfo, UserProfile, SaleBannerItem } from "@curefit/vm-models"
import * as _ from "lodash"
import { UrlPathBuilder, ProductPrice } from "@curefit/product-common"
import {
    addSegmentDetail,
    validateBaseDataItemCondition,
    SaleBannerItemV2,
    SaleBannerOfferInfoWithCity,
    OfferDetails
} from "@curefit/vm-models"
import { WidgetTemplate } from "@curefit/vm-common"
import { User } from "@curefit/user-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import CultUtil from "../../../../util/CultUtil"
import { CdnUtil } from "@curefit/util-common"
import { OfferUtil } from "@curefit/base-utils"
import { OfferV2, PackOffersResponse, PackOfferItem, OfferAddon } from "@curefit/offer-common"
import AppUtil, { OfferV2WithTnc } from "../../../../util/AppUtil"
import { CFUserProfile } from "../../CFUserProfile"
import { Namespace, OfflineFitnessPack, RestrictionLevel } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../../../util/CatalogueServiceUtilities"

const SALE_WIDGET_V2_VERSION = 8.13

export interface OfferDetailStyle {
    offerTagStyle: TextStyle
    offerInfoStyle: TextStyle
    offerImageStyle: ImageStyle
    offerIconStyle: TextStyle
    offerInfoContainerStyle: OfferInfoContainerStyle
}

interface OfferDetailsWithStyle extends OfferDetails {
    style?: OfferDetailStyle
}
interface SaleBannerOfferInfoV2WithStyle {
    offerDetails: OfferDetailsWithStyle[]
}
interface SaleBannerItemV2Style extends SaleBannerItemV2 {
    offerInfo: SaleBannerOfferInfoV2WithStyle
}
interface LayoutProps {
    roundedCorners: boolean,
    noVerticalPadding: boolean,
    edgeToEdge: boolean,
    interContentSpacing: number,
    autoScroll: boolean,
    showPagination: boolean,
    alignment: string,
    widgetBackgroundColor: string,
    contentBackgroundColor: string,
    isImageGradient: boolean,
    bannerOriginalWidth: number,
    bannerOriginalHeight: number,
    bannerHeight: number,
    contentOrientation: string,
    imageContentPosition: string,
    listingPriceStyle: string,
    mrpPriceStyle: string,
    titleTagStyle: TextStyle,
    titleStyle: TextStyle,
    ctaStyle: TextStyle,
    paymentOfferTextStyle: TextStyle,
    paymentOfferPriceStyle: TextStyle,
    addonOfferIconStyle: TextStyle,
    offerTagStyle: TextStyle,
    offerInfoStyle: TextStyle,
    paymentOfferPriceSuffixStyle: TextStyle,
    offerInfoBorderStyle: OfferInfoBorderStyle,
    offerInfoContainerStyle: OfferInfoContainerStyle,
    widgetBackgroundImage: string
}
class SaleBannerProductInfoView extends SaleBannerProductInfo {
    productName: string
    price: ProductPrice
}

interface OfferStyleTemplate {
    listingPriceStyle: string
    mrpPriceStyle: string
    offerInfoStyle: string
    titleTagStyle: string
    titleStyle: string
    addonOfferIconStyle: string
    ctaStyle: string
    paymentOfferTextStyle: string
    paymentOfferPriceStyle: string
    paymentOfferPriceSuffixStyle: string
}

interface OfferDetail {
    offerId: string,
    description: string,
    offerIconText: string,
    style: OfferDetailStyle,
    offerTag: string
    offerImage: string
}

export interface OfferV2WithStylesProperty extends OfferV2 {
    offerIconText?: string
    styles?: OfferDetailStyle,
    offerTag?: string,
    offerImage?: string
}
interface PaymentOfferDetail {
    description: string
    price: number
    offerType: string

}
class SaleBannerOfferInfoView extends SaleBannerOfferInfo {
    offerDetails: OfferDetail[]
    paymentOfferDetail?: PaymentOfferDetail
}


class OfferDetailWithStyles extends OfferDetails {
    styles: OfferDetailStyle
}
export class SaleWidgetV2View extends SaleWidgetV2 {
    layoutProps: LayoutProps

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const now = new Date()
        const userProfile = userContext.userProfile

        const { sessionInfo } = userContext
        const bannerPromises = _.map(this.data, async banner => {

            // Check for banner action segments, deleting action object, when segment doesnot match
            if (banner.action && !_.isEmpty(banner.action.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.action.segmentIds, userContext)
                if (!segment) {
                    delete banner.action
                }
            }

            if (banner.videoUrl) {
                if (banner.action) {
                    const relativeUrl = "curefit-content/" + banner.videoUrl
                    const url = `curefit://videoplayer?videoUrl=${encodeURIComponent(relativeUrl)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(relativeUrl))}`
                }
                banner.videoUrl = UrlPathBuilder.getPrefixedVideoPath(banner.videoUrl)
            }



            if (sessionInfo.userAgent === "MBROWSER") {
                banner.image = UrlPathBuilder.prefixSlash(banner.mwebImage)
            } else if (sessionInfo.userAgent === "DESKTOP") {
                banner.image = UrlPathBuilder.prefixSlash(banner.webImage)
            } else {
                banner.image = UrlPathBuilder.prefixSlash(banner.image)
            }

            // Adding Widget Id to Meta
            if (banner.action && banner.action.actionType === "SCROLL_TO_WIDGET") {
                if (banner.action.meta) {
                    banner.action.meta = Object.assign({}, banner.action.meta, { widgetId: banner.action.widgetId })
                } else {
                    banner.action.meta = { widgetId: banner.action.widgetId }
                }
                delete banner.action.widgetId
            }


            if (!_.isEmpty(banner.action) && (banner.action.url === "curefit://classbooking" || banner.action.url === "curefit://classbookingv2") && userContext.userProfile) {
                const currentUser: User = await userContext.userPromise
                const nuxLandingPageUrl = await CultUtil.getNuxLandingPageUrl(userContext, currentUser, interfaces.hamletBusiness, interfaces.userCache, interfaces.announcementBusiness)
                if (nuxLandingPageUrl) {
                    banner.action.url = nuxLandingPageUrl
                }
            }

            if (!validateBaseDataItemCondition(banner, now, userContext.userProfile.timezone)) {
                return undefined
            }
            if (!_.isEmpty(banner.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.segmentIds, userContext)
                if (segment) {
                    addSegmentDetail(banner.contentMetric, segment)
                    return banner
                }
            } else {
                return banner
            }
        })
        const banners = await Promise.all(bannerPromises)
        this.data = banners.filter(banner => { return banner })

        if (_.isEmpty(this.data)) {
            return undefined
        }
        if (!_.isNil(this.templateId)) {
            const widgetTemplate: WidgetTemplate = await interfaces.pageService.getWidgetTemplate(this.templateId)

            if (_.isNil(widgetTemplate)) {
                return undefined
            }
            if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
                let expandedStyleApp = {}
                if (widgetTemplate && widgetTemplate.templateData.app) {
                    expandedStyleApp = await this.expandStyleInformation(interfaces, widgetTemplate.templateData.app)
                }
                this.layoutProps = { ...widgetTemplate.templateData.app, ...expandedStyleApp }
            } else {
                let expandedStyleWeb = {}
                if (widgetTemplate && widgetTemplate.templateData.web) {
                    expandedStyleWeb = await this.expandStyleInformation(interfaces, widgetTemplate.templateData.web)
                }
                this.layoutProps = { ...widgetTemplate.templateData.web, ...expandedStyleWeb }
            }

            if (!this.layoutProps) {
                return undefined
            }
        }
        const saleBannerItemsPromise = _.map(this.data, async (item, index) => {
            let fitnessPack: OfflineFitnessPack
            if (item.productInfo.productId && _.isNaN(Number(item.productInfo.productId))) { // TEMP Hack to identify PMS productId
                const packTemplateResponse = await interfaces.offlineFitnessPackService.getPackFromTemplate({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productId: item.productInfo.productId,
                    restrictionLevel: RestrictionLevel.CITY,
                    restrictionValue: userProfile.city.cityId,
                    fetchPack: true
                })
                fitnessPack = packTemplateResponse.packEntry
            } else {
                fitnessPack = await interfaces.catalogueServicePMS.getCultPackFromTemplate(Number(item.productInfo.productId), userProfile.city.cultCityId) // PMS::TODO Remove once completely migrated
            }
            if (!fitnessPack) {
                return undefined
            }
            if (item.offerInfoOverrides && item.offerInfoOverrides.length) {
                _.forEach(item.offerInfoOverrides, override => {
                    if (_.findIndex(override.cityId, userProfile.cityId) !== -1)
                        item.offerInfo = override
                })
            }

            if (item.offerInfo && item.offerInfo.offerDetails.length) {
                for (let i = 0; i < item.offerInfo.offerDetails.length; i++) {
                    const styles = await this.getOfferStyle(sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER" ? item.offerInfo.offerDetails[i].offerTemplateIdApp : item.offerInfo.offerDetails[i].offerTemplateIdWeb, interfaces)
                    const offerDetailsWithStyles: OfferDetailWithStyles = { ...item.offerInfo.offerDetails[i], styles }
                    item.offerInfo.offerDetails[i] = offerDetailsWithStyles
                }
            }
            const offersV3Response = item.productInfo.productType === "FITNESS" ?
                await (userProfile as CFUserProfile).getCultProductPrices([fitnessPack.id]) :
                undefined
            const priceAndOfferIds = CultUtil.getPackPriceAndOfferIdV2(fitnessPack, offersV3Response)
            item.productInfo = await this.buildProductInfoView(fitnessPack, priceAndOfferIds.price, item.productInfo)
            const offerItem = CultUtil.getOfferItem(offersV3Response, fitnessPack.productId)
            !!item.offerInfo ? item.offerInfo = await this.buildOfferInfoView(fitnessPack, priceAndOfferIds.price, offerItem, item) : null
            return item
        })
        const saleBannerItems = await Promise.all(saleBannerItemsPromise)
        this.data = saleBannerItems.filter(data => { return data })
        this.layoutProps = this.checkAppVersion(this.layoutProps, this.data, userContext)
        this.widgetType = this.checkAppVersionForWidgetType(userContext)
        return this
    }

    private async expandStyleInformation(interfaces: CFServiceInterfaces, template: OfferStyleTemplate) {
        const ids: string[] = Object.values(template)
        const templates: WidgetTemplate[] = await interfaces.pageService.getWidgetTemplates(ids)
        const listingPriceStyle = template.listingPriceStyle ? templates[ids.indexOf(template.listingPriceStyle)].templateData : null
        const mrpPriceStyle = template.mrpPriceStyle ? templates[ids.indexOf(template.mrpPriceStyle)].templateData : null
        const offerInfoStyle = template.offerInfoStyle ? templates[ids.indexOf(template.offerInfoStyle)].templateData : null
        const titleTagStyle = template.titleTagStyle ? templates[ids.indexOf(template.titleTagStyle)].templateData : null
        const titleStyle = template.titleStyle ? templates[ids.indexOf(template.titleStyle)].templateData : null
        const addonOfferIconStyle = template.addonOfferIconStyle ? templates[ids.indexOf(template.addonOfferIconStyle)].templateData : null
        const ctaStyle = template.ctaStyle ? templates[ids.indexOf(template.ctaStyle)].templateData : null
        const paymentOfferTextStyle = template.paymentOfferTextStyle ? templates[ids.indexOf(template.paymentOfferTextStyle)].templateData : null
        const paymentOfferPriceStyle = template.paymentOfferPriceStyle ? templates[ids.indexOf(template.paymentOfferPriceStyle)].templateData : null
        const paymentOfferPriceSuffixStyle = template.paymentOfferPriceSuffixStyle ? templates[ids.indexOf(template.paymentOfferPriceSuffixStyle)].templateData : null
        return {
            listingPriceStyle,
            mrpPriceStyle,
            offerInfoStyle,
            titleTagStyle,
            titleStyle,
            addonOfferIconStyle,
            ctaStyle,
            paymentOfferTextStyle,
            paymentOfferPriceStyle,
            paymentOfferPriceSuffixStyle
        }
    }
    private checkAppVersionForWidgetType(userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext) || appVersion > SALE_WIDGET_V2_VERSION) {
            return "SALE_WIDGET_V2"
        } else {
            return "SALE_WIDGET"
        }
    }
    private checkAppVersion(layoutProps: LayoutProps, data: SaleBannerItemV2Style[], userContext: UserContext) {
        const { appVersion } = userContext.sessionInfo
        if (AppUtil.isWeb(userContext)) {
            return layoutProps
        } else if (appVersion < SALE_WIDGET_V2_VERSION && data[0].offerInfo && data[0].offerInfo.offerDetails && data[0].offerInfo.offerDetails[0]) {
            layoutProps.offerTagStyle = data[0].offerInfo.offerDetails[0].style.offerTagStyle
            layoutProps.offerInfoStyle = data[0].offerInfo.offerDetails[0].style.offerInfoStyle
            layoutProps.offerInfoContainerStyle = data[0].offerInfo.offerDetails[0].style.offerInfoContainerStyle
            return layoutProps
        } else {
            return layoutProps
        }
    }

    private async getOfferStyle(id: string, interfaces: CFServiceInterfaces) {
        const widgetTemplate: any = (await interfaces.pageService.getWidgetTemplate(id)).templateData
        const ids: string[] = [widgetTemplate.offerTagStyle, widgetTemplate.offerIconStyle, widgetTemplate.offerInfoStyle, widgetTemplate.offerImageStyle]
        const templates: WidgetTemplate[] = await interfaces.pageService.getWidgetTemplates(ids)
        const offerTagStyle = widgetTemplate.offerTagStyle ? templates[0].templateData : null
        const offerIconStyle = widgetTemplate.offerIconStyle ? templates[1].templateData : null
        const offerInfoStyle = widgetTemplate.offerInfoStyle ? templates[2].templateData : null
        const offerImageStyle = widgetTemplate.offerImageStyle ? templates[3].templateData : null
        return { ...widgetTemplate, offerTagStyle, offerIconStyle, offerInfoStyle, offerImageStyle }
    }


    private getUserEligibleOfferWithConfiguration(offerItem: PackOfferItem, item: SaleBannerItemV2): OfferV2WithStylesProperty[] {
        const userEligibleOffers: OfferV2WithStylesProperty[] = []
        _.forEach(offerItem.offers, (offer: OfferV2) => {
            const index = _.findIndex(item.offerInfo.offerDetails, (itemOffer: OfferV2) => {
                if (itemOffer.offerId === offer.offerId) {
                    offer = { ...offer, ...itemOffer }
                    return true
                } else {
                    return false
                }
            })
            if (index !== -1) {
                userEligibleOffers.push(offer)
                return
            }
            const allOfferAddonTypes = OfferUtil.getAllAddOnOfferTypes(offer)
            const intersectionOfferTypes = _.intersection(allOfferAddonTypes, ["NO_COST_EMI", "PAYMENT"])
            if (intersectionOfferTypes.length && item.paymentOfferId === offer.offerId) {
                userEligibleOffers.push(offer)
            }
        })
        return userEligibleOffers
    }

    async buildProductInfoView(fitnessPack: OfflineFitnessPack, price: ProductPrice, productInfo: SaleBannerProductInfo): Promise<SaleBannerProductInfoView> {
        const productInfoView: SaleBannerProductInfoView = Object.assign({},
            productInfo,
            {
                productName: CatalogueServiceUtilities.getFitnessDisplayName(fitnessPack),
                price: price
            })
        return productInfoView
    }

    async buildOfferInfoView(fitnessPack: OfflineFitnessPack, price: ProductPrice, offerItem: PackOfferItem, item: SaleBannerItemV2): Promise<SaleBannerOfferInfoView> {
        if (offerItem) {
            const validOffer: OfferV2WithStylesProperty[] = this.getUserEligibleOfferWithConfiguration(offerItem, item)
            const seggregatedOffers = OfferUtil.segregateOffers(validOffer, ["NO_COST_EMI", "PAYMENT"])
            const otherOffers: OfferV2WithStylesProperty[] = seggregatedOffers.get("OTHER_OFFERS")
            const offerDataItems = AppUtil.getOffersWithAddonsAndLabels(otherOffers, "saleBannerLabel")
            const offerDetails: OfferDetail[] = []
            offerDataItems.forEach((offerItem: OfferV2WithTnc) => {
                const index = _.findIndex(otherOffers, (offer) => {
                    return offer.offerId === offerItem.offerId
                })
                offerDetails.push({
                    offerId: offerItem.offerId,
                    description: offerItem.description,
                    offerIconText: otherOffers[index].offerIconText,
                    style: otherOffers[index].styles,
                    offerTag: otherOffers[index].offerTag,
                    offerImage: otherOffers[index].offerImage
                })
            })
            const { higherPriorityOfferaddOnType, higherPriorityOffer } = this.getHigherPriorityOffer([...seggregatedOffers.get("NO_COST_EMI"), ...seggregatedOffers.get("PAYMENT")])
            const offerInfoView: SaleBannerOfferInfoView = Object.assign({},
                item.offerInfo,
                {
                    offerDetails: offerDetails,
                    paymentOfferDetail: !!higherPriorityOffer ? higherPriorityOfferaddOnType === "NO_COST_EMI" ? this.emiOfferDetail(fitnessPack, price, higherPriorityOffer) : this.paymentOfferDetail(price, higherPriorityOffer) : undefined
                }
            )
            return offerInfoView
        }
    }

    private getHigherPriorityOffer(offers: OfferV2[]) {
        if (!_.isEmpty(offers)) {
            let higherPriorityOffer = offers[0]
            let addOnType
            offers.forEach((offer: OfferV2) => {
                if (higherPriorityOffer.level < offer.level || (higherPriorityOffer.level === offer.level && higherPriorityOffer.priority < offer.priority)) {
                    higherPriorityOffer = offer
                }
            })
            higherPriorityOffer.addons.forEach((addOn: OfferAddon) => {
                if (addOn.addonType === "NO_COST_EMI") {
                    addOnType = "NO_COST_EMI"
                } else if (addOn.addonType === "PAYMENT") {
                    addOnType = "PAYMENT"
                }
            })
            return {
                higherPriorityOfferaddOnType: addOnType,
                higherPriorityOffer: higherPriorityOffer
            }
        }
        return {}
    }

    private emiOfferDetail(fitnessPack: OfflineFitnessPack, price: ProductPrice, emiOffer: OfferV2): PaymentOfferDetail {
        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            // Max supported emi duration in months
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })
        const numberOfMonths = CultUtil.getEmiTenureForPack(fitnessPack.product.durationInDays, maxEmiTenure)
        const emiOfferDetail: PaymentOfferDetail = {
            description: emiOffer.uiLabels.saleBannerLabel,
            price: Math.round(price.listingPrice / numberOfMonths),
            offerType: "EMI"
        }
        return emiOfferDetail
    }

    private paymentOfferDetail(price: ProductPrice, paymentOffer: OfferV2): PaymentOfferDetail {
        const paymentOfferDetail: PaymentOfferDetail = {
            description: paymentOffer.uiLabels.saleBannerLabel,
            price: price.listingPrice,
            offerType: "INSTANT_DISCOUNT"
        }
        return paymentOfferDetail
    }
}
