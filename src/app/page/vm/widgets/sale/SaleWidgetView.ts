import { WidgetType } from "@curefit/vm-common"
import { IBaseWidget, UserContext, SaleWidget, SaleBannerProductInfo, SaleBannerOfferInfo, UserProfile } from "@curefit/vm-models"
import * as _ from "lodash"
import { UrlPathBuilder, ProductPrice } from "@curefit/product-common"
import {
    addSegmentDetail,
    validateBaseDataItemCondition
} from "@curefit/vm-models"
import { WidgetTemplate } from "@curefit/vm-common"
import { User } from "@curefit/user-common"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import CultUtil from "../../../../util/CultUtil"
import { CdnUtil } from "@curefit/util-common"
import { OfferUtil } from "@curefit/base-utils"
import { OfferV2, PackOffersResponse, PackOfferItem } from "@curefit/offer-common"
import AppUtil from "../../../../util/AppUtil"
import { CFUserProfile } from "../../CFUserProfile"
import { Namespace, OfflineFitnessPack, RestrictionLevel } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../../../util/CatalogueServiceUtilities"

class SaleBannerProductInfoView extends SaleBannerProductInfo {
    productName: string
    price: ProductPrice
}

interface OfferDetail {
    description: string
}
interface PaymentOfferDetail {
    description: string
    price: number
    offerType: string

}
class SaleBannerOfferInfoView extends SaleBannerOfferInfo {
    offerDetails: OfferDetail[]
    paymentOfferDetail?: PaymentOfferDetail
}
export class SaleWidgetView extends SaleWidget {
    layoutProps: any

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const now = new Date()
        const userProfile = userContext.userProfile
        this.maxNumBanners = this.maxNumBanners ? parseInt(this.maxNumBanners.toString()) : undefined

        const { sessionInfo } = userContext
        const bannerPromises = _.map(this.data, async banner => {

            // Check for banner action segments, deleting action object, when segment doesnot match
            if (banner.action && !_.isEmpty(banner.action.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.action.segmentIds, userContext)
                if (!segment) {
                    delete banner.action
                }
            }

            if (banner.videoUrl) {
                if (banner.action) {
                    const relativeUrl = "curefit-content/" + banner.videoUrl
                    const url = `curefit://videoplayer?videoUrl=${encodeURIComponent(relativeUrl)}&absoluteVideoUrl=${encodeURIComponent(CdnUtil.getCdnUrl(relativeUrl))}`
                }
                banner.videoUrl = UrlPathBuilder.getPrefixedVideoPath(banner.videoUrl)
            }



            if (sessionInfo.userAgent === "MBROWSER") {
                banner.image = UrlPathBuilder.prefixSlash(banner.mwebImage)
            } else if (sessionInfo.userAgent === "DESKTOP") {
                banner.image = UrlPathBuilder.prefixSlash(banner.webImage)
            } else {
                banner.image = UrlPathBuilder.prefixSlash(banner.image)
            }

            // Adding Widget Id to Meta
            if (banner.action && banner.action.actionType === "SCROLL_TO_WIDGET") {
                if (banner.action.meta) {
                    banner.action.meta = Object.assign({}, banner.action.meta, { widgetId: banner.action.widgetId })
                } else {
                    banner.action.meta = { widgetId: banner.action.widgetId }
                }
                delete banner.action.widgetId
            }


            if (!_.isEmpty(banner.action) && (banner.action.url === "curefit://classbooking" || banner.action.url === "curefit://classbookingv2") && userContext.userProfile) {
                const currentUser: User = await userContext.userPromise
                const nuxLandingPageUrl = await CultUtil.getNuxLandingPageUrl(userContext, currentUser, interfaces.hamletBusiness, interfaces.userCache, interfaces.announcementBusiness)
                if (nuxLandingPageUrl) {
                    banner.action.url = nuxLandingPageUrl
                }
            }

            if (!validateBaseDataItemCondition(banner, now, userContext.userProfile.timezone)) {
                return undefined
            }
            if (!_.isEmpty(banner.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.segmentIds, userContext)
                if (segment) {
                    addSegmentDetail(banner.contentMetric, segment)
                    return banner
                }
            } else {
                return banner
            }
        })
        const banners = await Promise.all(bannerPromises)
        this.data = banners.filter(banner => { return banner })
        this.data = this.data.slice(0, this.maxNumBanners)

        if (_.isEmpty(this.data)) {
            return undefined
        }
        if (!_.isNil(this.templateId)) {
            const widgetTemplate: WidgetTemplate = await interfaces.pageService.getWidgetTemplate(this.templateId)
            if (_.isNil(widgetTemplate)) {
                return undefined
            }
            if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
                this.layoutProps = widgetTemplate.templateData.app
            } else {
                this.layoutProps = widgetTemplate.templateData.web
            }

            if (!this.layoutProps) {
                return undefined
            }
        }
        const saleBannerItemsPromise = _.map(this.data, async (item, index) => {
            let fitnessPack: OfflineFitnessPack
            if (item.productInfo.productId && _.isNaN(Number(item.productInfo.productId))) { // TEMP Hack to identify PMS productId
                const packTemplateResponse = await interfaces.offlineFitnessPackService.getPackFromTemplate({
                    namespace: Namespace.OFFLINE_FITNESS,
                    productId: item.productInfo.productId,
                    restrictionLevel: RestrictionLevel.CITY,
                    restrictionValue: userProfile.city.cityId,
                    fetchPack: true
                })
                fitnessPack = packTemplateResponse.packEntry
            } else {
                fitnessPack = await interfaces.catalogueServicePMS.getCultPackFromTemplate(Number(item.productInfo.productId), userProfile.city.cultCityId)
            }
            if (!fitnessPack) {
                return undefined
            }
            const offersV3Response = item.productInfo.productType === "FITNESS" ?
                await (userProfile as CFUserProfile).getCultProductPrices([fitnessPack.productId]) :
                undefined
            const offerItem = CultUtil.getOfferItem(offersV3Response, fitnessPack.productId)
            const priceAndOfferIds = CultUtil.getPackPriceAndOfferIdV2(fitnessPack, offersV3Response)
            item.productInfo = await this.buildProductInfoView(fitnessPack, priceAndOfferIds.price, item.productInfo)
            !!item.offerInfo ? item.offerInfo = await this.buildOfferInfoView(fitnessPack, priceAndOfferIds.price, offerItem, item.offerInfo) : null
            return item
        })
        const saleBannerItems = await Promise.all(saleBannerItemsPromise)
        this.data = saleBannerItems.filter(data => { return data })
        return this
    }

    async buildProductInfoView(fitnessPack: OfflineFitnessPack, price: ProductPrice, productInfo: SaleBannerProductInfo): Promise<SaleBannerProductInfoView> {
        const productInfoView: SaleBannerProductInfoView = Object.assign({},
            productInfo,
            {
                productName: CatalogueServiceUtilities.getFitnessDisplayName(fitnessPack),
                price: price
            })
        return productInfoView
    }

    async buildOfferInfoView(fitnessPack: OfflineFitnessPack, price: ProductPrice, offerItem: PackOfferItem, offerInfo: SaleBannerOfferInfo): Promise<SaleBannerOfferInfoView> {
        if (offerItem) {
            const seggregatedOffers = OfferUtil.segregateOffers(offerItem.offers, ["NO_COST_EMI", "PAYMENT"])
            const otherOffers: OfferV2[] = seggregatedOffers.get("OTHER_OFFERS")
            const offerDataItems: any[] = AppUtil.getOffersWithAddonsAndLabels(otherOffers, "saleBannerLabel")
            const offerDetails: OfferDetail[] = _.map(offerDataItems, offer => { return { description: offer.description } })
            const { higherPriorityOfferaddOnType, higherPriorityOffer } = this.getHigherPriorityOffer([...seggregatedOffers.get("NO_COST_EMI"), ...seggregatedOffers.get("PAYMENT")])
            const offerInfoView: SaleBannerOfferInfoView = Object.assign({},
                offerInfo,
                {
                    offerDetails: offerDetails.slice(0, offerInfo.numOffersToCommuicate),
                    paymentOfferDetail: !!higherPriorityOffer ? higherPriorityOfferaddOnType === "NO_COST_EMI" ? this.emiOfferDetail(fitnessPack, price, higherPriorityOffer) : this.paymentOfferDetail(price, higherPriorityOffer) : undefined
                }
            )
            return offerInfoView
        }


    }

    private getHigherPriorityOffer(offers: any) {
        if (!_.isEmpty(offers)) {
            let higherPriorityOffer = offers[0]
            let addOnType
            offers.forEach((offer: any) => {
                if (higherPriorityOffer.level < offer.level || (higherPriorityOffer.level === offer.level && higherPriorityOffer.priority < offer.priority)) {
                    higherPriorityOffer = offer
                }
            })
            higherPriorityOffer.addons.forEach((addOn: any) => {
                if (addOn.addonType === "NO_COST_EMI") {
                    addOnType = "NO_COST_EMI"
                } else if (addOn.addonType === "PAYMENT") {
                    addOnType = "PAYMENT"
                }
            })
            return {
                higherPriorityOfferaddOnType: addOnType,
                higherPriorityOffer: higherPriorityOffer
            }
        }
        return {}
    }

    private emiOfferDetail(fitnessPack: OfflineFitnessPack, price: ProductPrice, emiOffer: OfferV2): PaymentOfferDetail {
        let maxEmiTenure = 12
        emiOffer.addons.forEach((addOn) => {
            // Max supported emi duration in months
            if (addOn.config && addOn.config.maxEmiTenure) {
                maxEmiTenure = addOn.config.maxEmiTenure
            }
        })
        const numberOfMonths = CultUtil.getEmiTenureForPack(fitnessPack.product.durationInDays, maxEmiTenure)
        const emiOfferDetail: PaymentOfferDetail = {
            description: emiOffer.uiLabels.saleBannerLabel,
            price: Math.round(price.listingPrice / numberOfMonths),
            offerType: "EMI"
        }
        return emiOfferDetail
    }

    private paymentOfferDetail(price: ProductPrice, paymentOffer: OfferV2): PaymentOfferDetail {
        const paymentOfferDetail: PaymentOfferDetail = {
            description: paymentOffer.uiLabels.saleBannerLabel,
            price: price.listingPrice,
            offerType: "INSTANT_DISCOUNT"
        }
        return paymentOfferDetail
    }
}
