import {
    Action, addSegmentDetail,
    BannerGridWidget,
    BannerItem,
    getPreBookingActions,
    IBaseWidget,
    IServiceInterfaces,
    UserContext, validateBaseDataItemCondition, WidgetTemplate
} from "@curefit/vm-models"
import { HealthfaceTenant, UrlPathBuilder } from "@curefit/product-common"
import { CareUtil } from "@curefit/base-utils"
import { BundleSessionSellableProduct, Patient } from "@curefit/albus-client"
import * as _ from "lodash"
import { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../../../util/CareUtil"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import { SkuPackUtil } from "../../../../util/SkuPackUtil"

export class BannerGridWidgetView extends BannerGridWidget {
    layoutProps: any

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        // Temp hack as having issues with cyclops portal
        this.edgeToEdge = this.edgeToEdge === "TRUE" ? true : (this.edgeToEdge === "FALSE" ? false : this.edgeToEdge)
        this.maxNumBanners = this.maxNumBanners ? parseInt(this.maxNumBanners.toString()) : undefined
        this.numberOfColumns = this.numberOfColumns ? parseInt(this.numberOfColumns.toString()) : undefined
        // Backwards Compatibility
        if (this.showDivider === true && _.isUndefined(this.dividerType)) {
            this.dividerType = "LARGE"
        }

        const now = new Date()
        const { sessionInfo } = userContext
        const bannerPromises = _.map(this.data, async (banner: BannerItem) => {
            if (sessionInfo.userAgent === "MBROWSER") {
                banner.image = UrlPathBuilder.prefixSlash(banner.mwebImage)
            } else if (sessionInfo.userAgent === "DESKTOP") {
                banner.image = UrlPathBuilder.prefixSlash(banner.webImage)
            } else {
                banner.image = UrlPathBuilder.prefixSlash(banner.image)
            }

            await SkuPackUtil.getSkuTitle(banner, interfaces, userContext, queryParams)

            if (!_.isEmpty(banner.actionV2)) {
                const vmAction = await interfaces.userActionMappingBusiness.getVMActionFromActionV2(banner.actionV2, interfaces, userContext)
                if (!_.isEmpty(vmAction)) {
                    banner.action = vmAction
                }
            }
            // Check for banner action segments, deleting action object, when segment doesnot match
            if (banner.action && !_.isEmpty(banner.action.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.action.segmentIds, userContext)
                if (!segment) {
                    delete banner.action
                }
            }

            if (banner.action && !_.isEmpty(banner.action) && banner.action.actionType === "SHOW_PATIENT_SELECTION") {
                if (banner.action.meta && banner.action.meta.productId) {
                    if (!userContext.sessionInfo.isUserLoggedIn) {
                        banner.action = {
                            actionType: "SHOW_ALERT_MODAL",
                            title: "BOOK",
                            meta: {
                                title: "Login Required!",
                                subTitle: "Please login to continue",
                                actions: [{actionType: "LOGOUT", title: "Login"}]
                            }
                        }
                    } else {
                        if (banner.action.meta.subCategoryCode) {
                            const subCategoryCode = banner.action.meta.subCategoryCode
                            const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
                            const healthfaceProduct = await interfaces.healthfaceService.getProductInfoDetails("BUNDLE", {
                                subCategoryCode: subCategoryCode,
                                productCodeCsv: banner.action.meta.productId
                            }, healthfaceTenant)
                            if (healthfaceProduct.length > 0) {
                                const bundleProduct = <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
                                const patientsList: Patient[] = await interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)
                                const action: Action = getPreBookingActions(userContext, bundleProduct.infoSection.numberOfSessions, true, [], `curefit://carecartcheckout?productId=${bundleProduct.productCode}&subCategoryCode=${bundleProduct?.subCategoryCode}`, patientsList)[0]
                                banner.action = action
                            } else {
                                banner.action = undefined
                            }
                        }
                    }
                }
            }

            // Adding Widget Id to Meta
            if (banner.action && banner.action.actionType === "SCROLL_TO_WIDGET") {
                if (banner.action.meta) {
                    banner.action.meta = Object.assign({}, banner.action.meta, { widgetId: banner.action.widgetId })
                } else {
                    banner.action.meta = { widgetId: banner.action.widgetId }
                }
                delete banner.action.widgetId
            }
            if (banner.action) {
                const action: any = await this.getPackIdUrlFromTemplate(interfaces, userContext, banner.action)
                banner.action = action
            }

            if (!_.isEmpty(banner.action) && banner.action.actionType === "BOOK_LIVE_PT_SESSION") {
                banner.action = <Action>await interfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId: LIVE_PT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_PERSONAL_TRAINING" })
            } else if (!_.isEmpty(banner.action) && banner.action.actionType === "BOOK_LIVE_SGT_SESSION") {
                banner.action = <Action>await interfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId: LIVE_SGT_SNC_PRODUCT_ID, subCategoryCode: "LIVE_SGT" })
            }

            // Add offer t&c as action if offer id is tagged
            if (banner.offerId) {
                const offer = (await interfaces.offerServiceV3.getOffer(banner.offerId))?.data
                banner.action = {
                    "actionType": "SHOW_OFFERS_TNC_MODAL",
                    "meta": {
                        "title": "Terms and Conditions",
                        "subTitle": "",
                        "dataItems": offer.tNc
                    }
                }
            }

            if (!validateBaseDataItemCondition(banner, now, userContext.userProfile.timezone)) {
                return undefined
            }

            if (banner.contentMetric) {
                banner.contentMetric.contentUrl = banner.image
            }

            if (!_.isEmpty(banner.segmentIds)) {
                const segment = await interfaces.segmentService.doesUserBelongToAnySegment(banner.segmentIds, userContext)
                if (segment) {
                    addSegmentDetail(banner.contentMetric, segment)
                    return banner
                }
            } else {
                return banner
            }


        })

        const banners: BannerItem[] = await Promise.all<BannerItem>(bannerPromises)
        this.data = banners.filter(banner => !!banner)
        this.data = this.data.slice(0, this.maxNumBanners)

        if (_.isEmpty(this.data)) {
            return undefined
        }
        if (!_.isNil(this.templateId)) {
            const widgetTemplate: WidgetTemplate = await interfaces.pageService.getWidgetTemplate(this.templateId)
            if (_.isNil(widgetTemplate)) {
                return undefined
            }
            if (sessionInfo.userAgent === "APP" || sessionInfo.userAgent === "MBROWSER") {
                this.layoutProps = widgetTemplate.templateData.app
            } else {
                this.layoutProps = widgetTemplate.templateData.web
            }
            if (_.isNil(this.layoutProps)) {
                return undefined
            }
            // Backwards compatibility //
            this.edgeToEdge = this.layoutProps.edgeToEdge
            this.numberOfColumns = this.layoutProps.numberOfColumns
            if (_.isNumber(this.layoutProps.bannerWidth) && _.isNumber(this.layoutProps.bannerHeight)) {
                this.bannerRatio = this.layoutProps.bannerWidth + ":" + this.layoutProps.bannerHeight
            }
            if (this.showDivider === true && _.isUndefined(this.dividerType)) {
                this.dividerType = "LARGE"
            }
            // End of backwards compatibility code //
        }
        return this
    }
}
