import { DIYPackCardWidget, IDIYCard } from "@curefit/vm-models/dist/src/models/widgets/DIYPackCardWidget"
import { IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import AppUtil from "../../../util/AppUtil"
import LiveUtil from "../../../util/LiveUtil"

const backgroundColors = ["#e1c2c1", "#dacfdd", "#c8d2c5"]

export class DIYPackCardWidgetView extends DIYPackCardWidget {
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget> {
        const countryId = AppUtil.getCountryId(userContext)
        const liveSeries = await interfaces.diyService.getDIYPacksForLiveSeries(this.productType ? this.productType : "DIY_FITNESS", countryId)
        this.header = {
            title: this.header && this.header.title ? this.header.title : (AppUtil.isIntlWidgetNamesSupported(userContext) ? "Explore by Formats" : `${LiveUtil.getCultLiveBranding(userContext)} Past Collection`)
        }
        this.items = []
        liveSeries.map((series, index) => {
            const item: IDIYCard = {
                image: series.imageDetails.clpImage,
                packId: series.productId,
                packMeta: {
                    classes: series.sessionIds ? `${series.sessionIds.length} classes` : "0 classes"
                },
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtilV1.diyPackProductPage(series, userContext.sessionInfo.userAgent)
                }
            }
            if (AppUtil.isNewLiveDiyPackSupported(userContext)) {
                this.items.push(item)
            } else if (series.imageDetails.tempClpImage) {
                item.title = series.title
                item.backgroundColor = backgroundColors[index % 3]
                item.image = series.imageDetails.tempClpImage
                this.items.push(item)
            }
        })
        return this
    }
}
