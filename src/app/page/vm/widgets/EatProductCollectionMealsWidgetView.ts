import { EatMealsWidgetViewV2, MealItemRequest } from "./EatMealsWidgetViewV2"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { UserContext } from "@curefit/userinfo-common"
import { BaseWidget, IBaseWidget, SoldOutTitleWidget, WholefitBrandWidget, EatFilterContainerWidgetView } from "@curefit/vm-models"
import { CFUserProfile } from "../CFUserProfile"
import { SlotUtil } from "@curefit/eat-util"
import EatUtil from "../../../util/EatUtil"
import {
    FoodCategory,
    FoodInventory,
    FoodProduct as Product,
    MenuType,
    ProductCollection,
    ProductMealSlotMenu
} from "@curefit/eat-common"
import * as _ from "lodash"
import { MealItem } from "../../PageWidgets"
import { GridWidget } from "@curefit/vm-models"
import { FoodSinglePriceOfferResponse, OfferV2 } from "@curefit/offer-common"
import { MealCardWidget } from "./MealsCardWidget"
import AppUtil, {
    EAT_MEAL_ITEM_IMAGE_EXPERIMENT_PROD,
    EAT_MEAL_ITEM_IMAGE_EXPERIMENT_STAGE,
} from "../../../util/AppUtil"
import { MealSlotSelectorWidgetV2 } from "./MealSlotSelectorWidgetV2"
import { ImagePathBuilder, ProductCollectionsImageCategory } from "@curefit/product-common"
import { MAX_CART_SIZE } from "../../../util/MealUtil"
const clone = require("clone")

export class EatProductCollectionMealsWidgetView extends EatMealsWidgetViewV2 {
    constructor() {
        super()
    }
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: any }, sharedData?: any): Promise<BaseWidget[]> {
        const collectionId: string = queryParams["collectionId"]
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        // Await for mandatory parameter needed for all apis
        const deliveryArea = await userProfile.deliveryAreaPromise
        const [deliveryAreaTz, mealSlots, preferredLocation, cartOffersResult] = await Promise.all([
            interfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId),
            userProfile.availableMealSlotsPromise,
            userProfile.preferredLocationPromise,
            userContext.userProfile.eatCartOffersPromise
        ])
        const cityWiseMaxCartSize = _.get(interfaces.configService.getConfig("EAT_CLP_OVERRIDES").configs, "citywiseMaxCartSize")
        const maxAllowedCartSize = _.get(cityWiseMaxCartSize, deliveryArea.cityId, MAX_CART_SIZE)
        const currentMealSlot = SlotUtil.getCurrentMealSlotFrom(mealSlots, deliveryArea.channel, deliveryAreaTz)
        const selectedMealSlotAndDay = EatUtil.getSelectedMealSlotAndDay(queryParams, deliveryArea, deliveryAreaTz,
            mealSlots, currentMealSlot, undefined)
        const selectedDeliverySlot = this.getSelectedDeliverySlot(queryParams, false)
        const day = selectedMealSlotAndDay.day
        const tabSlot: MenuType = selectedMealSlotAndDay.mealSlot

        const menuAvailabilityPromise = interfaces.eatApiClientService.getProductCollectionMenu(userProfile.userId, tabSlot, day, preferredLocation, "EAT_FIT", collectionId, true, selectedDeliverySlot, deliveryAreaTz)

        let productMenus: ProductMealSlotMenu[] = []
        let inventoryResult: FoodInventory
        const menuAvailability = await menuAvailabilityPromise
        productMenus = menuAvailability.menus
        inventoryResult = menuAvailability.inventoryResult
        const delayMode = this.getDelayMode(tabSlot, deliveryArea, deliveryAreaTz)
        const stopOrders = delayMode !== undefined && (delayMode.mode === "STOP_ORDERS" || delayMode.mode === "STOP_ORDERS_ENABLE_CANCEL")

        const productIds = _.map(productMenus, menu => menu.productId)
        const productMap = await interfaces.catalogueService.getProductMap(productIds)

        // Logic for variants
        const productToVariantMap: { [productId: string]: string[] } = {}
        const allProducts: Product[] = clone(_.values(productMap))
        const parentProductMap = await interfaces.catalogueService.getProductMap(_.compact(allProducts.map(p => p.parentProductId)))

        // creating widget promises
        const mealSlotSelectorWidgetPromise = new MealSlotSelectorWidgetV2(currentMealSlot, tabSlot, day, mealSlots, await userProfile.availableDeliveryAreasPromise).buildView(interfaces, userContext, queryParams)
        const imageWidgetPromise = this.getImageWidget(interfaces, userContext, menuAvailability.productCollection)

        const byProductId: { [productId: string]: Product } = _.keyBy(allProducts, product => product.productId)
        productMenus = productMenus.filter(menu => {
            return !_.isNil(byProductId[menu.productId])
        })

        const mealCardImageNumber = 1
        const singleOfferResult: FoodSinglePriceOfferResponse = await userProfile.eatSingleOffersPromise
        const mealItems: MealItem[] = _.map(productMenus, menu => {
            const product = productMap[menu.productId]
            const request: MealItemRequest = {
                sessionInfo: sessionInfo,
                product: product,
                stopOrders: stopOrders,
                interfaces: interfaces,
                singleOfferResult: singleOfferResult,
                day: day,
                inventoryResult: inventoryResult,
                tabSlot: tabSlot,
                preferredLocation: preferredLocation,
                userContext: userContext,
                menu: menu,
                queryParams: queryParams
            }
            return this.getMealItem(request, false, false, mealCardImageNumber, false, false, parentProductMap, productToVariantMap, maxAllowedCartSize)
        })

        const categoryIdArray = _.uniq(mealItems.map((mealItem) => {
            return mealItem.foodCategoryId
        }))
        const clpCategories: { [id: string]: FoodCategory } = interfaces.foodCategoryService.getCLPCategories(categoryIdArray, "EAT_FIT")
        const fitCashOffer = _.maxBy(_.filter(cartOffersResult, (offer: OfferV2) => {
            return offer.addons[0] && offer.addons[0].addonType === "FITCASH"
        }), "priority")

        const mealCardPromise: Promise<IBaseWidget>[] = []
        const soldOutMealCardPromise: Promise<IBaseWidget>[] = []
        _.forEach(mealItems, (mealItem) => {
            const cardPromise = new MealCardWidget(mealItem, tabSlot, clpCategories, "SMALL_CARD", fitCashOffer, true, false).buildView(interfaces, userContext, queryParams)
            if (mealItem.stock) {
                mealCardPromise.push(cardPromise)
            } else {
                soldOutMealCardPromise.push(cardPromise)
            }
        })

        let finalWidget: BaseWidget[] = []

        if (soldOutMealCardPromise.length) {
            soldOutMealCardPromise.unshift(new SoldOutTitleWidget("SOLD OUT").buildView(interfaces, userContext, queryParams))
        }
        if (AppUtil.isWeb(userContext)) {
            const webGridWidget = new GridWidget()
            webGridWidget.widgets = []
            const [imageWidget, mealSlotSelectorWidget, ...mealCards] = await Promise.all([
                imageWidgetPromise,
                mealSlotSelectorWidgetPromise,
                ...mealCardPromise,
                ...soldOutMealCardPromise
            ])
            webGridWidget.widgets.push(...mealCards.filter(item => item.widgetType === "MEAL_CARD_WIDGET"))
            const filterListWidgets = [mealSlotSelectorWidget]
            const filterListWidget = await new EatFilterContainerWidgetView(filterListWidgets).buildView(interfaces, userContext, queryParams)
            finalWidget.push(imageWidget, filterListWidget, webGridWidget)
        } else {
            finalWidget.push(...await Promise.all([
                imageWidgetPromise,
                mealSlotSelectorWidgetPromise,
                ...mealCardPromise,
                ...soldOutMealCardPromise
            ]))
        }


        finalWidget = finalWidget.filter(widget => !_.isNil(widget))
        return finalWidget


    }

    async getImageWidget(interfaces: CFServiceInterfaces, userContext: UserContext, productCollection: ProductCollection): Promise<BaseWidget> {
        const widget: WholefitBrandWidget = new WholefitBrandWidget()
        widget.imageUrl = ImagePathBuilder.getProductCollectionImagePath(productCollection.productCollectionId, productCollection.imageVersionMap["HERO"], ProductCollectionsImageCategory.HERO)
        return widget
    }
}
