import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/vm-models"
import { ActionUtil } from "@curefit/base-utils"
import * as _ from "lodash"
import { CultTrainer } from "@curefit/cult-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { TrainersWidget } from "@curefit/vm-models"
import { BaseDataItemWithMetric } from "@curefit/vm-models"
import AppUtil from "../../../util/AppUtil"
import CultUtil from "../../../util/CultUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"

class TrainerWidgetItem extends BaseDataItemWithMetric {
    title: string
    description: string
    image: string
    action: Action
}

export class TrainersViewWidget extends TrainersWidget {

    data: TrainerWidgetItem[] = []
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const { userProfile, sessionInfo } = userContext
        const city = userProfile.city
        const trainers = this.productType === "FITNESS" ? await interfaces.cultFitService.browseFitnessTrainer(undefined, "CUREFIT_API", city.cultCityId) :
            await interfaces.mindFitService.browseFitnessTrainer(undefined, "CUREFIT_API", city.cultCityId)

        trainers.forEach(trainer => {
            const image = this.getTrainerImage(trainer, sessionInfo.userAgent)
            const description = CultUtil.getCultTrainerDescription(trainer.description)
            let nextAction: Action
            if (AppUtil.isNewTrainerViewSupported(userContext)) {
                nextAction = {
                    actionType: "SHOW_DOCTOR_DETAILS_MODAL",
                    meta: { ...trainer, displayImage: image, descriptionArray: trainer.description }
                }
            } else {
                nextAction = {
                    actionType: "NAVIGATION",
                    url: ActionUtil.trainerInfoCard(trainer.name, "", description, image)
                }
            }
            this.data.push({
                title: trainer.name,
                contentMetric: {
                    contentId: trainer.id,
                },
                description: description,
                image: image,
                action: nextAction
            })
        })

        this.showDivider = false

        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }

    getTrainerImage(trainer: CultTrainer, userAgent: UserAgent): string {
        return trainer.imageURL ? "/" + trainer.imageURL : undefined
    }
}
