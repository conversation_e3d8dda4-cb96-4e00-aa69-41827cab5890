import {
    OfferCalloutItem,
    OfferCallOutWidget
} from "@curefit/vm-models"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { OfferV2UserView } from "@curefit/offer-common"
import { TimeUtil } from "@curefit/util-common"
import * as _ from "lodash"
import { CardListWidgetView, CardListItem } from "./card/CardListWidget"
import { UrlPathBuilder } from "@curefit/product-common"

export class OfferCalloutWidgetView extends OfferCallOutWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const groupedItems = _.groupBy(this.data, item => item.offerId)
        const offerIds: string[] = Object.keys(groupedItems)
        const { userProfile, sessionInfo } = userContext
        const user = await userContext.userPromise
        const offers: OfferV2UserView[] = await interfaces.offerService.isOfferActive(offerIds, {
            userId: userProfile.userId,
            deviceId: sessionInfo.deviceId,
            email: user.email,
            workEmail: user.workEmail,
            phone: user.phone
        })

        if (!_.isEmpty(offers)) {
            // @ts-ignore
            const cardListItems: CardListItem[] = offers
                .filter(offer => !_.isEmpty(groupedItems[offer.offerId]))
                .map((offer: OfferV2UserView) => {
                    const dataItem: OfferCalloutItem = groupedItems[offer.offerId][0]
                    return {
                        title: dataItem.title,
                        subTitle: "Valid till " + TimeUtil.formatDateInTimeZone(userContext.userProfile.timezone, offer.eligibilityEndDate, "ddd, D MMM"),
                        action: dataItem.action,
                        leftInfo: {
                            images: [UrlPathBuilder.prefixSlash(dataItem.image)]
                        },
                        rightInfo: {
                            action: dataItem.action,
                            moreAction: undefined
                        },
                    }
                })
            if (!_.isEmpty(cardListItems)) {
                return new CardListWidgetView("Only For You", "OFFER_CALLOUT_WIDGET", cardListItems).buildView(interfaces, userContext, queryParams)
            }
        }
    }
}
