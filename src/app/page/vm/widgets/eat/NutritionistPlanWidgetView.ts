import * as _ from "lodash"
import { IBaseWidget, UserContext, BaseWidget, IServiceInterfaces, NutritionistPageWidget, TimerWidgetV2, ProductListWidget, CareWidgetUtil } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"
import {
  Action,
  ProductBenefit,
  INutritionistSessionInfo,
  INutritionistPlanWidget,
  WidgetHeader,
  IOfferInfo,
  InfoCard
} from "@curefit/apps-common"
import { Patient, HealthfaceTenant, SUB_CATEGORY_CODE, DiagnosticProductResponse, ConsultationProduct, ManagedPlanPackInfo } from "@curefit/care-common"
import TimerWidgetV2View from "../TimerWidgetV2View"
import { CareUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import AppUtil from "../../../../util/AppUtil"
import { BundleSessionSellableProduct, HealthfaceProductInfo, CLPPackItem, ActiveBundleOrderDetail } from "@curefit/albus-client"
import { OfferV2, PackOffersResponse } from "@curefit/offer-common"
import { Header, ListSubType } from "../../../../common/views/WidgetView"
import { getPreBookingActions, getProductBenefitWidget, getSingleSessionAboutSection } from "../../../../util/NCUtil"


interface IOfferTimerWidget {
  timerStyle: any
  offerEndDate: any
  timerTitle: string
}

interface INutritionPlanWidgetParams {
  productId: string
  title: string
  description: string
  subCategoryCode: SUB_CATEGORY_CODE
  subtitle?: string
  recommendedProductId?: string
  info?: [{
    productId: string;
    title: string;
    subTitle: string;
    description: string;
    showOfferIcon?: boolean;
    priority: string
  }]
  tags?: [{
    productId: string
    displayName: string
  }]
  activeMemberships?: ActiveBundleOrderDetail[]
  showOfferTimer?: boolean
  displayProductIds?: string[]
  planInfo?: [{title: string, icon: string}]
}
export default class NutritionistPlanWidget extends BaseWidget implements INutritionistPlanWidget {
  subCategoryCode: SUB_CATEGORY_CODE
  widgets: IBaseWidget[]
  title: string
  subTitle?: string
  description: string
  containerStyles?: any
  items: INutritionistSessionInfo[]
  offerTimerWidget: IBaseWidget
  action: Action
  selectedProductId: string
  recommendedProductId?: string
  tags?: [{
    productId: string
    displayName: string
  }]
  info?: [{
    productId: string;
    title: string;
    subTitle: string;
    description: string;
    showOfferIcon?: boolean;
    priority: string;
  }]
  activeMemberships?: ActiveBundleOrderDetail[]
  showOfferTimer?: boolean
  displayProductIds?: string[]
  planInfo?: [{title: string, icon: string}]

  constructor(params: INutritionPlanWidgetParams) {
    super("NUTRITIONIST_PLAN_WIDGET")
    this.title = params.title
    this.description = params.description
    this.subTitle = params.subtitle
    this.selectedProductId = params.productId
    this.subCategoryCode = params.subCategoryCode
    this.recommendedProductId = params.recommendedProductId
    this.tags = params.tags
    this.info = params.info
    this.activeMemberships = params.activeMemberships
    this.showOfferTimer = params.showOfferTimer
    this.displayProductIds = params.displayProductIds
    this.planInfo = params.planInfo
  }

  async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget | IBaseWidget[]> {
    return this
  }

  async createWidget(
    userContext: UserContext,
    interfaces: IServiceInterfaces,
    bundleProducts?: BundleSessionSellableProduct[],
    bundleoffers?: PackOffersResponse,
    patientsList?: Patient[],
  ): Promise<NutritionistPlanWidget> {
    const appliedOffers: any[] = []
    if (!_.isEmpty(this.activeMemberships)) {
      this.selectedProductId = undefined
    }
    this.items = await this.getNutritionistSessionInfo(bundleProducts, this.selectedProductId, userContext, interfaces, bundleoffers, appliedOffers, patientsList)
    if (!_.isEmpty(appliedOffers) && this.showOfferTimer) {
      this.offerTimerWidget = await this.createOfferTimerWidget({
        offerEndDate: appliedOffers[0].endDate, timerTitle: "OFFER EXPIRES IN:", timerStyle: {
          background: "#32363f",
          borderTopRightRadius: 5,
          borderTopLeftRadius: 5,
        }
      }, interfaces as CFServiceInterfaces, userContext)
    }
    return this
  }


  private getPackItemDetailsList(productDetails: ConsultationProduct[], offers: PackOffersResponse, appliedOffers?: any): { packItems: CLPPackItem[], offers?: OfferV2[] } {
    const minPricedProduct = _.minBy(productDetails, product => {
      const offerDetails = OfferUtil.getPackOfferAndPrice(product, offers)
      return Math.ceil(offerDetails.price.listingPrice)
    })
    const offerDetails = OfferUtil.getPackOfferAndPrice(minPricedProduct, offers)
    return {
      packItems: [{
        title: "1 Session",
        price: offerDetails.price.mrp,
        discountPrice: offerDetails.price.listingPrice,
        hasOfferTag: false,
        currency: offerDetails.price.currency || "INR",
        showPriceCut: offerDetails.price.listingPrice < offerDetails.price.mrp
      }], offers: offerDetails.offers
    }
  }

  private async getSingleSessionProductSessionInfo(userContext: UserContext, interfaces: IServiceInterfaces, sellableProductId: string, appliedOffers: any): Promise<INutritionistSessionInfo | undefined> {
    const consultationSellableProduct = await interfaces.healthfaceService.getConsultationSellableProducts(userContext.userProfile.cityId, "LC")
    const { products } = consultationSellableProduct.consultationTypes[0]
    const productCodes = products.map(product => product.code)
    const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
      userContext,
      "CONSULTATION",
      productCodes,
      AppUtil.callSourceFromContext(userContext),
      interfaces,
      true
    )
    const productDetails = await interfaces.catalogueService.getProducts(productCodes) as ConsultationProduct[]
    if (this.displayProductIds && !this.displayProductIds.includes(productDetails[0].productId)) {
      return undefined
    }
    const currency = "INR"
    const offersResponse = await offerPromise
    const packOfferInfo = this.getPackItemDetailsList(productDetails, offersResponse, appliedOffers)
    const packItemDetails: CLPPackItem[] = packOfferInfo.packItems

    const browseAction = CareUtil.careDoctorBrowseAction(userContext, productDetails[0]) as Action
    const url = this.getSingleSessionActionUrl(browseAction.url)
    browseAction.url = url
    let productInfo = this.info ? this.info.find((productInfo) => {
      return productInfo.productId === productDetails[0].productId
    }) : undefined

    if (!productInfo) {
      productInfo = {
        title: `1 Session`,
        description: getSingleSessionAboutSection(),
        subTitle: "",
        productId: productDetails[0].productId,
        priority: "1"
      }
    }

    const leftInfo: any = {
      title: productInfo.title
    }
    const rightInfo = {
      title: `PAY ${RUPEE_SYMBOL}${packItemDetails[0].discountPrice}`,
    }
    browseAction.leftInfo = leftInfo
    browseAction.rightInfo = rightInfo
    browseAction.title = undefined
    let offerInfo: IOfferInfo
    if (packOfferInfo.offers && !_.isEmpty(packOfferInfo.offers)) {
      const packOffer = packOfferInfo.offers.find((offer) => {
        return !_.isNil(offer?.uiLabels?.cartLabel) && !_.isEmpty(offer?.uiLabels?.cartLabel)
      })
      if (packOffer?.uiLabels?.cartLabel) {
        offerInfo = {
          title: packOffer?.uiLabels?.cartLabel,
          iconUrl: productInfo.showOfferIcon && packOffer?.paymentIconURL ? packOffer.paymentIconURL : undefined
        }
      }
    }
    let tagInfo
    if (this.tags) {
      tagInfo = _.find(this.tags, (tag) => {
        return tag.productId === productDetails[0].productId
      })
    }
    const data: INutritionistSessionInfo = {
      priority: Number(productInfo.priority),
      tag: tagInfo,
      offerInfo: offerInfo,
      productId: productDetails[0].productId,
      title: productInfo.title,
      description: productInfo.description,
      subTitle: productInfo.subTitle,
      price: {
        listingPrice: packItemDetails[0].discountPrice,
        mrp: packItemDetails[0].price,
        currency,
        showPriceCut: packItemDetails[0].showPriceCut
      },
      action: browseAction,
      backgroundColor: productDetails[0].productId === this.recommendedProductId ? "#F2F4F8" : "#FFFFFF"
    }
    return data
  }
  private async getNutritionistSessionInfo(bundleProducts: BundleSessionSellableProduct[], sellableProductId: string, userContext: UserContext, interfaces: IServiceInterfaces, bundleoffers?: PackOffersResponse, appliedOffers?: any, patientsList?: Patient[]): Promise<INutritionistSessionInfo[]> {
    const infoSections: INutritionistSessionInfo[] = []
    const singleSession = await this.getSingleSessionProductSessionInfo(userContext, interfaces, sellableProductId, appliedOffers)
    if (!_.isNil(singleSession)) {
      infoSections.push(singleSession)
    }
    bundleProducts.forEach((product: BundleSessionSellableProduct, index) => {
      if (this.displayProductIds && !this.displayProductIds.includes(product.productCode)) {
        return
      }
      if (!_.isNil(product.infoSection)) {
        const offerDetails = OfferUtil.getPackOfferAndPriceForCare(product, bundleoffers)
        if (product.productCode === sellableProductId && offerDetails && offerDetails.offers) {
          appliedOffers.push(...offerDetails.offers)
        }
        product.listingPrice = offerDetails.price.listingPrice
        const offerIds = _.map(offerDetails.offers, (offer: any) => {
          return offer.offerId
        })

        let productInfo = this.info ? this.info.find((productInfo) => {
          return productInfo.productId === product.productCode
        }) : undefined

        if (!productInfo) {
          productInfo = {
            title: _.get(product, "infoSection.sellingTitle") ? product.infoSection.sellingTitle : `${product.infoSection.numberOfSessions} Sessions`,
            description: product.infoSection.aboutSection,
            subTitle: product.infoSection.headerDescription,
            productId: product.productCode,
            priority: String(index)
          }
        }

        const action: Action = getPreBookingActions(userContext, product.infoSection.numberOfSessions, true, offerIds, `curefit://carecartcheckout?productId=${product.productCode}&subCategoryCode=${product?.subCategoryCode}`, patientsList)[0]
        const leftInfo: any = {
          title: productInfo.title,
        }
        const rightInfo = {
          title: `PAY ${RUPEE_SYMBOL}${offerDetails.price.listingPrice}`,
        }
        action.leftInfo = leftInfo
        action.rightInfo = rightInfo
        action.title = undefined
        let offerInfo: IOfferInfo
        if (offerDetails && !_.isEmpty(offerDetails.offers)) {
          const packOffer = offerDetails.offers.find((offer) => {
            return !_.isNil(offer?.uiLabels?.cartLabel) && !_.isEmpty(offer?.uiLabels?.cartLabel)
          })
          if (packOffer?.uiLabels?.cartLabel) {
            offerInfo = {
              title: packOffer?.uiLabels?.cartLabel,
              iconUrl: productInfo.showOfferIcon && packOffer?.paymentIconURL ? packOffer.paymentIconURL : undefined
            }
          }
        }
        let tagInfo
        if (this.tags) {
          tagInfo = _.find(this.tags, (tag) => {
            return tag.productId === product.productCode
          })
        }
        infoSections.push({
          priority: Number(productInfo.priority),
          offerInfo: offerInfo,
          productId: product.productCode,
          title: productInfo.title,
          subTitle: productInfo.subTitle,
          description: productInfo.description,
          price: {
            mrp: product.mrp,
            listingPrice: product.listingPrice,
            showPriceCut: product.listingPrice < product.mrp,
            currency: offerDetails.price.currency
          },
          action,
          tag: tagInfo,
          backgroundColor: product.productCode === this.recommendedProductId ? "#F2F4F8" : "#FFFFFF"
        })
      }
    })
    return infoSections.sort((a, b) => {
      return a.priority < b.priority ? -1 : 1
  })
  }

  private async createOfferTimerWidget(params: IOfferTimerWidget, interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
    const timerWidgetV2 = new TimerWidgetV2View()
    timerWidgetV2.timerStyle = params.timerStyle
    timerWidgetV2.roundedCorners = false
    timerWidgetV2.data = {
      timerEndTimeWithTz: {
        date: params.offerEndDate,
        timezone: userContext.userProfile.timezone
      },
      title: params.timerTitle,
      timerEndTime: undefined,
      privateOfferId: undefined,
      style: undefined,
      action: undefined
    }
    return timerWidgetV2.buildView(interfaces, userContext, undefined)
  }


  private getSingleSessionActionUrl(url: string) {
    url += `&showSearchWidget=true&name=Select%20a%20Dietician`
    return url
  }
}

export class NutritionistPageWidgetView extends NutritionistPageWidget {

  widgets: IBaseWidget[]
  subCategoryCode: SUB_CATEGORY_CODE

  async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: {
    [filterName: string]: string;
  }): Promise<IBaseWidget | IBaseWidget[]> {
    if (queryParams && queryParams.productId) {
      this.productId = queryParams.productId
    }
    this.subCategoryCode = "NUTRITIONIST"
    this.widgets = []
    const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(this.subCategoryCode)
    const patientsListPromise = interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)
    let bundleProductIds: string[] = []
    const products: DiagnosticProductResponse[] = await interfaces.healthfaceService.browseProducts("BUNDLE", this.subCategoryCode, healthfaceTenant, true, undefined, undefined, undefined, undefined, true)
    bundleProductIds = products.map(product => product.productCode)
    const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
      userContext,
      "BUNDLE",
      bundleProductIds,
      AppUtil.callSourceFromContext(userContext),
      interfaces,
      true
    )
    const bundleProductsPromises = _.map(bundleProductIds, async (productId) => {
      return interfaces.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: this.subCategoryCode, productCodeCsv: productId }, healthfaceTenant)
    })
    const activeMemberships = await interfaces.healthfaceService.getActiveBundleOrders(userContext.userProfile.userId, "BUNDLE", this.subCategoryCode)

    const healthfaceProducts = await Promise.all(bundleProductsPromises)
    let bundleProducts = _.map(healthfaceProducts, (healthfaceProduct: HealthfaceProductInfo[]) => {
      return <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
    })
    if (!this.displayProductIds && AppUtil.isPersonalCoachWidgetSupported(userContext)) {
      bundleProducts = bundleProducts.filter((product) => {
        return !["NUTRITIONIST_12", "NUTRITIONIST_24"].includes(product.productCode)
      })
    }
    const params: INutritionPlanWidgetParams = {
      productId: this.productId,
      title: this.title,
      description: this.description,
      subCategoryCode: this.subCategoryCode,
      subtitle: this.subTitle,
      recommendedProductId: this.recommendedProductId,
      info: this.info,
      activeMemberships: activeMemberships,
      tags: this.tags,
      showOfferTimer: this.showOfferTimer,
      displayProductIds: this.displayProductIds,
      planInfo: this.planInfo
    }
    const planWidget = await new NutritionistPlanWidget(params).createWidget(userContext, interfaces, bundleProducts, await offerPromise, await patientsListPromise)
    const benefitWidget = await new NCProductBenefitWidget().buildView(interfaces, userContext, undefined)
    let howItWorksItem, whatsInPackItem
    if (bundleProducts.length > 0) {
      bundleProducts[0].infoSection.children.map(infoSection => {
        switch (infoSection.type) {
          case "PACK_STEPS":
            howItWorksItem = infoSection
            break
          case "PACK_CONTENTS_DETAILED":
            whatsInPackItem = infoSection
            break
        }
      })
    }
    if (_.isEmpty(activeMemberships)) {
      this.widgets.push(planWidget)
      if (this.showBenefits) {
        this.widgets.push(benefitWidget)
      }
      if (this.showHowItWorks) {
        this.widgets.push(await new NCHowItWorksWidget(howItWorksItem).buildView(interfaces, userContext, undefined))
      }
    } else {
      if (this.showBenefits) {
        this.widgets.push(benefitWidget)
      }
      if (this.showHowItWorks) {
        this.widgets.push(await new NCHowItWorksWidget(howItWorksItem).buildView(interfaces, userContext, undefined))
      }
      this.widgets.push(planWidget)
    }
    return this.widgets
  }
}

export class NCProductBenefitWidget extends BaseWidget {
  data: ProductBenefit[]
  header: WidgetHeader

  constructor() {
    super("PRODUCT_BENEFIT_WIDGET")
  }
  async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
    const { data, header } = getProductBenefitWidget()
    this.data = data
    this.header = header
    return this
  }
}
export class NCHowItWorksWidget extends BaseWidget {
  howItWorksItem: ManagedPlanPackInfo
  items: InfoCard[]
  header: Header
  hasDividerBelow: boolean
  type: ListSubType
  constructor(howItWorksItem: ManagedPlanPackInfo) {
    super("PRODUCT_LIST_WIDGET")
    this.howItWorksItem = howItWorksItem
    this.type = "SMALL"
  }

  async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
    if (_.isEmpty(this.howItWorksItem)) {
      return undefined
    }
    const header: Header = {
      title: this.howItWorksItem.title,
      color: "#000000"
    }
    const items: InfoCard[] = []
    this.howItWorksItem.children.forEach(item => {
      items.push({
        subTitle: item.desc,
        icon: item.imageUrl
      })
    })
    this.items = items
    this.header = header
    this.hasDividerBelow = false
    return this
  }
}