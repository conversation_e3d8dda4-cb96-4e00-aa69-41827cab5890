import { IBaseWidget, User<PERSON>ontext, BaseWidget, IServiceInterfaces, TimerWidgetV2, ProductListWidget, PackDescriptionWidget, CareWidgetUtil } from "@curefit/vm-models"
import { BundleSessionSellableProduct, HealthfaceProductInfo } from "@curefit/albus-client"
import _ = require("lodash")
import { HealthfaceTenant } from "@curefit/product-common"
import { CareUtil, OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import AppUtil from "../../../../util/AppUtil"
import { getPreBookingActions } from "../../../../util/NCUtil"
import { IOfferInfo } from "@curefit/apps-common"

export class PackDescriptionWidgetView extends PackDescriptionWidget {

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        switch (this.type) {
            case "NUTRITIONIST":
                return new NCPackDescriptionWidgetView(this).buildView(interfaces, userContext, queryParams)
            default:
                return undefined
        }
    }
}

export class NCPackDescriptionWidgetView extends PackDescriptionWidget {
    price: {
        mrp: number
        listingPrice: number
        showPriceCut: boolean
        currency: string
    }
    iconWidget?: [{ icon: string, text: string}]
    layoutProps?: any
    offerList?: {
        offerDescription: string,
        tnc?: string[];
    }[]

    constructor(widget: PackDescriptionWidget) {
        super()
        this.info = widget.info
        this.widgetType = widget.widgetType
        this.offerList = []
    }
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: {
        [productId: string]: string;
    }): Promise<IBaseWidget> {
        let productId: string
        if (queryParams && queryParams.productId) {
            productId = queryParams.productId
        }

        const subCategoryCode = "NUTRITIONIST"
        const healthfaceTenant: HealthfaceTenant = CareUtil.getHealthfaceTenant(subCategoryCode)
        const healthfaceProduct = await interfaces.healthfaceService.getProductInfoDetails("BUNDLE", { subCategoryCode: subCategoryCode, productCodeCsv: productId }, healthfaceTenant)
        const bundleProduct = <BundleSessionSellableProduct>healthfaceProduct[0].baseSellableProduct
        const patientsListPromise = interfaces.healthfaceService.getAllPatients(userContext.userProfile.userId)

        if (this.info) {
            const productInfo = this.info.find((value => {
                return value.productId === productId
            }))
            if (productInfo) {
                this.title = productInfo.title
                this.description = productInfo.description
                this.subTitle = productInfo.subTitle
                this.desktopImageUrl = productInfo.desktopImageUrl
                this.breadcrumb = productInfo.breadcrumb
                this.bulletPoints = productInfo.bulletPoints
                const offerPromise = CareWidgetUtil.getCareProductOffersFromUserContext(
                    userContext,
                    "BUNDLE",
                    [productId],
                    AppUtil.callSourceFromContext(userContext),
                    interfaces,
                    true
                )
                const bundleOfferResponse = await offerPromise
                const offerDetails = OfferUtil.getPackOfferAndPriceForCare(bundleProduct, bundleOfferResponse)
                const offerPrice = offerDetails.price
                this.price = {
                    mrp: offerPrice.mrp,
                    listingPrice: offerPrice.listingPrice,
                    showPriceCut: offerPrice.listingPrice < offerPrice.mrp,
                    currency: offerPrice.currency
                }
                const offerIds = _.map(offerDetails.offers, (offer: any) => {
                    return offer.offerId
                })
                if (offerDetails.offers && !_.isEmpty(offerDetails.offers)) {
                    const packOffer = offerDetails.offers.find((offer) => {
                        return !_.isNil(offer?.uiLabels?.cartLabel) && !_.isEmpty(offer?.uiLabels?.cartLabel)
                    })
                    if (packOffer?.uiLabels?.cartLabel) {
                        this.offerDescription = packOffer.uiLabels.cartLabel
                    }
                }
                this.layoutProps = { marginTop: -15 }
                this.action = getPreBookingActions(userContext, bundleProduct.infoSection.numberOfSessions, true, offerIds, `curefit://carecartcheckout?productId=${bundleProduct.productCode}&subCategoryCode=${bundleProduct?.subCategoryCode}`, await  patientsListPromise, `Buy for ${RUPEE_SYMBOL}${offerPrice.listingPrice}`)[0]
                if (productInfo.iconInfo) {
                    this.iconWidget = productInfo.iconInfo
                }
                this.info = undefined

                // todo: remove dummy data
                if (!_.isEmpty(offerDetails) && !_.isEmpty(offerDetails.offers)) {
                    offerDetails.offers.forEach( offer => {
                        this.offerList.push({
                            offerDescription: offer.description,
                            tnc: offer.tNc
                        })
                    })
                }
            }

        }

        return this
    }
}