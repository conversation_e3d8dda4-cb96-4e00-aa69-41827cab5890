import { BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { ProductPrice } from "@curefit/product-common"
import { Action } from "@curefit/apps-common"
import { EatMealCardType, ListingBrandIdType, MenuType } from "@curefit/eat-common"
import { MeasurementUnit } from "@curefit/food-common"
import { WholefitMealItem } from "../../../wholefit/WholefitCommon"
import { CFServiceInterfaces } from "../ServiceInterfaces"

export class WholefitMealCardWidget extends BaseWidget {
    title: string
    titleWithoutUnits?: string
    calories?: string
    productId: string
    stock: number
    price: ProductPrice
    date: string
    actions: Action[]
    isVeg: boolean
    isInventorySet: boolean
    image: string
    imageThumbnail: string
    mealSlot: MenuType
    salesTag?: string
    isAllDayItem?: boolean
    isLowStock?: boolean
    calorieBucket: number
    nutritionTags: string[]
    categoryId: string
    showAttach?: boolean
    offerIds?: string[]
    cardViewType: EatMealCardType
    qty?: number
    parentProductId?: string
    variants?: {
        price: ProductPrice,
        productId: string,
        displayUnitQty: string,
        variantTitle?: string,
        stock: number,
        offerIds: string[]
    }[]
    unit?: MeasurementUnit
    displayUnitQty?: string
    variantTitle?: string
    listingBrand?: ListingBrandIdType
    brandName?: string

     constructor(mealItem: WholefitMealItem) {
        super("WHOLE_FIT_MEAL_CARD_WIDGET")
        this.showDivider = true
        this.title = mealItem.title
        this.titleWithoutUnits = mealItem.titleWithoutUnits
        this.productId = mealItem.productId
        this.stock = mealItem.stock
        this.price =  mealItem.price
        this.date =  mealItem.date
        this.actions = mealItem.actions
        this.isVeg =  mealItem.isVeg
        this.image = mealItem.image
        this.imageThumbnail = mealItem.imageThumbnail
        this.mealSlot = mealItem.mealSlot
        this.categoryId = mealItem.categoryId
        this.offerIds = mealItem.offerIds
        this.qty = mealItem.qty
        this.variants = mealItem.variants
        this.unit = mealItem.unit
        this.displayUnitQty = mealItem.displayUnitQty
         this.variantTitle = mealItem.variantTitle
        this.listingBrand = "WHOLE_FIT"
        this.brandName = mealItem.brandName
    }

    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: {[filter: string]: any}): Promise<IBaseWidget> {
        return this
    }
}
