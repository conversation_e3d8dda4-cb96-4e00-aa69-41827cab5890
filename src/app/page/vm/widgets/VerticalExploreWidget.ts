import { Action, BaseWidget, UserContext } from "@curefit/vm-models"
import { IBaseWidget } from "@curefit/vm-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"

export class VerticalExploreWidget extends BaseWidget implements IBaseWidget {
    title?: string
    subtitle?: string
    image?: {
        imageUrl: string
        style: any
    }
    bullets?: {
        icon?: string
        title: string
        action: Action
    }[]

    constructor() {
        super("VERTICAL_EXPLORE_WIDGET")
    }

    async buildView(interfaces?: CFServiceInterfaces, userContext?: UserContext, queryParams?: any): Promise<BaseWidget> {
        return this
    }
}
