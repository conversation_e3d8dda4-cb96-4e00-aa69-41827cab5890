import * as _ from "lodash"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { RecipeListWidget } from "./RecipeListWidget"
import { RecipeTabWidget } from "@curefit/vm-models"
import { DIYCategory } from "@curefit/diy-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import AppUtil from "../../../util/AppUtil"

export class RecipeTabWidgetView extends RecipeTabWidget {

    /**
     * returns an array of tabs
     * one tab contains a title (which is recipe category) and a list of IWidgets (one widget here denotes one recipe data)
     *
     */
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const countryId = AppUtil.getCountryId(userContext)
        const recipeCategories = await interfaces.diyService.getRecipeCategories(countryId)
        const filteredCategories = recipeCategories.filter(recipe => {
            return recipe.status === "LIVE"
        })
        filteredCategories.sort((recipe1: DIYCategory, recipe2: DIYCategory) => {
            const recipe1Priority = recipe1.sortInfo ? recipe1.sortInfo.priority : 0
            const recipe2Priority = recipe2.sortInfo ? recipe2.sortInfo.priority : 0
            const diff = recipe1Priority - recipe2Priority
            return diff > 0 ? -1 : diff < 0 ? 1 : diff
        })
        const ALL_CATEGORY_ID = "recipes_all"
        queryParams.start = "0"
        queryParams.count = "10"
        queryParams.diyCategory = ALL_CATEGORY_ID
        const allTab = await new RecipeListWidget().buildView(interfaces, userContext, queryParams)
        this.tabs.push({
            tabId: ALL_CATEGORY_ID,
            title: "All",
            vegFilter: "all",
            itemList: allTab,
            nextQuery: {
                tabId: ALL_CATEGORY_ID,
                start: +queryParams.count + (+queryParams.start),
                count: +queryParams.count
            }
        })
        for (let i = 0; i < filteredCategories.length; i++) {
            const category = filteredCategories[i]
            this.tabs.push({
                title: category.name,
                tabId: (<any>category)._id,
                vegFilter: "all",
                nextQuery: {
                    tabId: (<any>category)._id,
                    start: 0,
                    count: +queryParams.count
                }
            })
        }
        this.selectedTabId = _.isEmpty(queryParams.selectedTabId) ? ALL_CATEGORY_ID : queryParams.selectedTabId
        return this
    }
}

