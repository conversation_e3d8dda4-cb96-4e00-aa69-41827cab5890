import * as _ from "lodash"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { IBaseWidget, UserContext, Action } from "@curefit/vm-models"
import { CFUserProfile } from "../CFUserProfile"
import { WidgetType } from "@curefit/vm-common"
import { ProductBrowseWidget } from "@curefit/vm-models"
import { ActionUtil } from "@curefit/base-utils"
import { CatalogueProduct, Annotation } from "@curefit/gear-common"
import { ProductPrice } from "@curefit/product-common"
import { GearUtil } from "@curefit/gearvault-client"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import AppUtil from "../../../util/AppUtil"
import GearProductViewBuilder from "../../../product/GearProductViewBuilder"
import { UserInfo } from "@curefit/user-common"
import { GearProductPricesResponse } from "@curefit/offer-common"
import { OrderSource } from "@curefit/order-common"

export const PRODUCT_BROWSE_TYPES = ["GEAR"]

export class GearBrowseWidget extends ProductBrowseWidget {
    data: ProductItem[]

    constructor(productBrowseWidget: ProductBrowseWidget, widgetTypeOverride?: WidgetType) {
        super(widgetTypeOverride)
        if (productBrowseWidget) {
            this.productType = productBrowseWidget.productType
            this.layoutType = productBrowseWidget.layoutType
            this.showDivider = productBrowseWidget.showDivider
            this.dividerType = productBrowseWidget.dividerType
            this.layoutType = productBrowseWidget.layoutType
            this.collection = productBrowseWidget.collection
            this.header = productBrowseWidget.header
            this.layoutProps = productBrowseWidget.layoutProps
        }
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const PRODUCT_LIMIT = 5
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const response: { products: CatalogueProduct[] } = await interfaces.gearCLPService.getCataloguePageWith(this.collection.name, 1, PRODUCT_LIMIT)

        this.header = {
            seemore: {
                url: ActionUtil.gearCollection(this.collection.name),
                actionType: "NAVIGATION"
            },
            subTitle: this.collection.subTitle,
            title: this.collection.title,
            banner: this.header && this.header.banner,
            ...this.header
        }

        const orderSource: OrderSource = AppUtil.callSourceFromContext(userContext)
        const userInfo: UserInfo = {userId: userProfile.userId, deviceId: sessionInfo.deviceId}
        const productOffers: GearProductPricesResponse = await interfaces.offerServiceV3.getGearProductPrices(_.map(response.products, p => `${p.id}`), userInfo, orderSource)
        _.map(response.products, p => `${p.id}`)
        const products = GearUtil.addDiscountInformationToProducts(
            _.map(response.products, p => GearUtil.convertProductVariantsToGearSKUs(p)),
            productOffers.priceMap
        )

        const isGearBrandNameSupported = AppUtil.isGearBrandNameSupported(userContext)

        const productItems: ProductItem[] = []
        _.map(products, (p: CatalogueProduct) => {
            productItems.push({
                brand: p.brand,
                title: isGearBrandNameSupported || p.brand == null ? p.name : `${p.brand.toUpperCase()} ${p.name}`,
                imageUrl: p.master.images[0].product_url,
                annotations: _.map(p.annotations, (annotation: string) => GearProductViewBuilder.getStyledAnnotation(annotation)),
                price: {
                    mrp: p.discount_percent ? Number(p.price) : undefined,
                    listingPrice: Number(p.price),
                    primaryPrice: p.display_price,
                    secondaryPrice: p.discount_percent ? `${RUPEE_SYMBOL} ${p.mrp}` : undefined,
                    discountPercent: p.discount_percent || undefined,
                    currency: "INR"
                } as ProductPrice,
                action: {
                    actionType: "NAVIGATION",
                    url: ActionUtil.gearProduct(`${p.id}`, undefined, AppUtil.isCultSportWebApp(userContext) ? p.slug : undefined)
                }
            } as ProductItem)
        })
        // To return empty widget if no productItem is available
        if (_.isEmpty(productItems)) {
            return undefined
        }
        /*  adding check to push only the first 5 items
            TODO:: adding support for the see more card
        */
        this.data = productItems
        return this
    }

}

export class ProductBrowseWidgetView extends GearBrowseWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (this.productType === "GEAR") {
            return new GearBrowseWidget(this).buildView(interfaces, userContext, queryParams)
        } else {
            return undefined
        }
    }
}

export interface ProductItem {
    title?: string
    brand: string,
    imageUrl: string
    price: ProductPriceWithDiscount
    action: Action
    annotations?: Annotation[]
}

interface ProductPriceWithDiscount extends ProductPrice {
    displayPrice?: string
    secondaryPrice?: string
    discountPercent?: number
}
