import { MembershipWidgetV2, MembershipProductType, IServiceInterfaces } from "@curefit/vm-models"
import { IBaseWidget, UserContext, } from "@curefit/vm-models"
import { ProductType } from "@curefit/product-common"
import { eternalPromise, pluralizeStringIfRequired, TimeUtil, Timezone } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import * as momentTz from "moment-timezone"
import * as _ from "lodash"
import { Action } from "@curefit/apps-common"
import { FoodProduct as Product } from "@curefit/eat-common"
import { CatalogueServiceV2Utilities } from "@curefit/catalog-client"
import CultUtil, { transformCultSummaryMap, CultMembershipState, CultOrMindSummary, UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD, isMembershipCurrent } from "../../../util/CultUtil"
import { MembershipDetails, CultMembership } from "@curefit/cult-common"
import { CFUserProfile } from "../CFUserProfile"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { MembershipDataV2 } from "@curefit/apps-common"
import { User } from "@curefit/user-common"
import AppUtil from "../../../util/AppUtil"
import { ConsultationProduct, CONSULTATION_PACK_SUBCATEGORIES, DiagnosticProduct, Doctor, LIVE_PT_DOCTOR_TYPES, Patient, SKIN_HAIR_SUBCATEGORIES, SUB_CATEGORY_CODE } from "@curefit/care-common"
import { CareUtil, LAB_TEST_CYCLOP_SEGMENT } from "../../../util/CareUtil"
import LiveUtil from "../../../util/LiveUtil"
import { Membership } from "@curefit/membership-commons"
import { getMembershipState, getNutritionistCLPUrl } from "../../../util/NCUtil"
import { ActiveBundleOrderDetail, ActiveConsultationResponse, AggregatedMembershipInfo, BookingDetail, BundleOrderAction } from "@curefit/albus-client"
import { GymfitMembership, GymfitCenterCategory, GymfitStatus } from "@curefit/gymfit-common"
import GymfitUtil from "../../../util/GymfitUtil"
import GymfitBusiness from "../../../gymfit/GymfitBusiness"
import { SKIN_HAIR_SEGMENT_VALIDATION_API_KEY } from "../../../util/VMUtil"
import { ActionType } from "@curefit/apps-common/dist/src/actions/types"
import { CONSULTATION_STATUS } from "@curefit/albus-client/dist/src/models"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import CatalogueServiceUtilities from "../../../util/CatalogueServiceUtilities"

const CARE_PRODUCT_TYPES = ["HCU_PACK", "PHYSIOTHERAPY_PACK", "CONSULTATION", "BUNDLE"]
const LAB_TEST_PACKS_SUB_CATEGORY_CODE = ["HCU_PACK", "DIAG_PACK"]
const SESSION_BASED_SUB_CATEGORY_CODE = ["PHYSIOTHERAPY", "NUTRITIONIST", ...SKIN_HAIR_SUBCATEGORIES, "MIND_THERAPY"]

export class MembershipViewWidgetV2 extends MembershipWidgetV2 {

    async buildView(
        interfaces: CFServiceInterfaces,
        userContext: UserContext,
        queryParams: { [filterName: string]: string }
    ): Promise<IBaseWidget> {
        if (this.productType === "FITNESS" || this.productType === "MIND") {
            return new CenterMembershipViewWidgetV2(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "LIVE_PERSONAL_TRAINING" || this.productType === "PERSONAL_TRAINING" || this.productType === "LIVE_SGT") {
            return new PTMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "CF_LIVE" || this.productType === "LIVE_FITNESS") {
            return new CFLiveMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "NUTRITIONIST_CONSULTATION") {
            return new NCMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (CareUtil.isMindTherapyPageWeb(userContext, this.productType)) {
            return new TherapyViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (CARE_PRODUCT_TYPES.includes(this.productType)) {
            return new CareActiveBundleAndConsultationMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        } else if (this.productType === "GYMFIT_FITNESS_PRODUCT") {
            return new GymfitMembershipViewWidget(this).buildView(interfaces, userContext, queryParams)
        }
        return undefined
    }
}

export class GymfitMembershipViewWidget extends MembershipWidgetV2 {
    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        let goldMemberships: Membership[] = await userContext.userProfile.promiseMapCache.getPromise("cultpass-gold-memberships", { userId: userContext.userProfile.userId })
        const tz = userContext.userProfile.timezone
        const isWeb = AppUtil.isWeb(userContext)
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
        const now = Date.now()

        goldMemberships = goldMemberships.filter(membership => membership.end >= now && (membership.status === "PURCHASED" || membership.status === "PAUSED"))
        goldMemberships.sort((a, b) => {
            return (a.end > b.end ? 1 : -1)
        })
        const membershipDataPromises = _.map(
            goldMemberships,
            async (membership) => {
                const endDate = TimeUtil.getMomentForDate(new Date(membership.end), tz)
                const startDate = TimeUtil.getMomentForDate(new Date(membership.start), tz)
                const numDaysToEndFromToday = endDate.diff(today, "days")
                const endDateFormatted = endDate.format("D MMM YYYY")
                const completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
                const membershipState = GymfitUtil.getGoldMembershipState(membership, tz)
                const startString = membershipState === "UPCOMING" ? "Starts: " : "Started: "
                const packTagAndColor = GymfitUtil.getGymPackTagAndColor(membershipState, numDaysToEndFromToday)
                let membershipFooter: { title: string; subTitle: string; action: Action } = null
                const cultBenefit = GymfitUtil.getCultBenefit(membership)
                if (cultBenefit) {
                    const { maxTickets, ticketsUsed } = cultBenefit
                    const remainingCount = maxTickets - ticketsUsed
                    membershipFooter = {
                        title: remainingCount + "/" + maxTickets, subTitle: "cult classes remaining", action: {
                            actionType: "NAVIGATION",
                            title: membership.status === "PURCHASED" && remainingCount > 0 ? "BOOK" : "",
                            url: "curefit://classbookingv2?pageFrom=addActivity&productType=FITNESS",
                        }
                    }
                }
                const membershipStateMeta = GymfitUtil.isPauseEnabled() && GymfitUtil.isPauseSupported(userContext)
                    ? GymfitUtil.getMembershipStateMeta(membership, userContext, null) : null
                const membershipStateAction = GymfitUtil.isPauseEnabled() && GymfitUtil.isPauseSupported(userContext)
                    ? await this.getMembershipStateAction(userContext, membership, this.productType, interfaces) : null
                interfaces.logger.info("In membership state action after state action")
                const membershipData: MembershipDataV2 = {
                    membershipState: membershipState,
                    action: {
                        actionType: "NAVIGATION",
                        url: isWeb ? GymfitUtil.getGoldMembershipPackDetailsWebUrl(membership) : await GymfitUtil.getGoldMembershipDetailsUrl(membership, userContext),
                    },
                    title: membership.name,
                    membershipStateMeta: membershipStateMeta,
                    progressBar: {
                        leftText: startString + TimeUtil.formatEpochInTimeZone(tz, membership.start, "DD MMM YYYY"),
                        rightText: "Ends: " + TimeUtil.formatEpochInTimeZone(tz, membership.end, "DD MMM YYYY") + ((membership.remainingPauseCount === membership.maxPauseCount && _.isEmpty(membership.activePause) ) ? "" : " (updated)"),
                        total: endDate.diff(startDate, "days"),
                        completed: completed,
                        type: "FITNESS",
                        noPadding: true,
                        progressBarColor: packTagAndColor.color,
                        progressBarTextStyle:
                            membershipState === "EXPIRED" ? { color: "#97a4c9" } : {},
                    },
                    membershipStateAction: membershipStateAction,
                    cardBackgroundStyle: isWeb ? {} : { elevation: 0, shadowOpacity: 0, borderWidth: 1, borderColor: "#e2e4e8" },
                    cardTextStyle:
                        membershipState === "EXPIRED" ? { color: "#FFFFFF" } : {},
                    tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                    contentMetric: {
                        contentId: membership.id.toString(),
                    },
                    footer: membershipFooter,
                    noteInfo: null
                }
                return membershipData
            }
        )
        const membershipData: MembershipDataV2[] = await Promise.all(
            membershipDataPromises
        )
        this.data = membershipData.sort(function (a, b) {
            return (a.membershipState > b.membershipState ? 1 : -1)
        })
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }

    async getMembershipStateAction(userContext: UserContext, membership: Membership, packProductType: string, interfaces: CFServiceInterfaces): Promise<Action> {
        interfaces.logger.info("In membership state action")
        const userInfo = await userContext.userPromise
        if (GymfitUtil.isCurrentGoldMembership(membership)) {
            switch (membership.status) {
                case "PURCHASED": {
                    if (!_.isEmpty(membership.activePause)) {
                        // CANCEL PAUSE
                        interfaces.logger.info("In membership state action cancel pause")
                        return GymfitUtil.getMembershipUnpauseAction(membership, packProductType, userContext, userInfo.isInternalUser)
                    }  else {
                        // PAUSE PACK
                        interfaces.logger.info("In membership state action pause pack")
                        const pausePackData = await GymfitUtil.getPackPauseResumeDetails(membership, packProductType, userContext)
                        interfaces.logger.info("In membership state action pause pack after details")
                        if (pausePackData && pausePackData.isPauseAllowed) {
                            interfaces.logger.info("In membership state action pause pack after is Pause allowed")
                            return GymfitUtil.getPackPauseResumeAction(pausePackData, membership)
                        }
                    }
                    return null
                }
                case "PAUSED": {
                    // RESUME PACK
                    interfaces.logger.info("In membership state action resume pack")
                    const pausePackData = await GymfitUtil.getPackPauseResumeDetails(membership, packProductType, userContext)
                    if (pausePackData && pausePackData.isPauseAllowed) {
                        return GymfitUtil.getPackPauseResumeAction(pausePackData, membership)
                    }
                    return null
                }
                default:
                    return null
            }
        } else {
            return null
        }
    }
}

export class CenterMembershipViewWidgetV2 extends MembershipWidgetV2 {

    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const membershipList = await CultUtil.getEliteMembershipsFromMembershipService(userContext, interfaces.membershipService)
        if (_.isEmpty(membershipList)) {
            return undefined
        }

        let hasUpcomingMembership = false
        const memberships: Membership[] = []
        const isExistingMember = _.some(membershipList, (membership) => !CultUtil.isMembershipExpired(userContext, membership))
        if (!isExistingMember) {
            memberships.push(CultUtil.getPreviousMembershipFromMembershipList(userContext, membershipList))
        } else {
            const currentMembership = CultUtil.getCurrentMembershipFromMembershipList(userContext, membershipList)
            if (!_.isNil(currentMembership)) {
                memberships.push(currentMembership)
            }
            const upcomingMembership = CultUtil.getUpcomingMembershipFromMembershipList(userContext, membershipList)
            if (!_.isNil(upcomingMembership)) {
                hasUpcomingMembership = true
                memberships.push(upcomingMembership)
            }
        }

        const membershipDataPromisesForUser: Promise<MembershipDataV2>[] = _.map(memberships, async (membership) => {
            let removeMembershipCard = false
            const endDate = TimeUtil.getMomentForDateString( TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.end, "yyyy-MM-dd"), tz)
            const startDate = TimeUtil.getMomentForDateString( TimeUtil.formatEpochInTimeZoneDateFns(tz, membership.start, "yyyy-MM-dd"), tz)
            const userInfo = await userContext.userPromise
            const pack = (await interfaces.catalogueServicePMS.getProduct(membership.productId))
            const cultMembershipId = membership.metadata["membershipId"]
            if (!cultMembershipId) {
                return undefined
            }
            const isSubscriptionPack = false
            const packProductType = "FITNESS"
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDateFormatted = startDate.format("D MMM YYYY")
            const numDaysToEndFromToday = endDate.diff(today, "days")

            const packPageAction: Action = {
                actionType: "NAVIGATION",
                url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membership.id.toString(), userContext, sessionInfo.userAgent, undefined, true)
            }
            const detailedMembership = await interfaces.cultFitService.getMembershipById(cultMembershipId, userProfile.userId)
            const membershipState = CultUtil.getMembershipStateV2(membership, tz)
            const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
            const membershipStateMeta = this.getMembershipStateMeta(detailedMembership, membership, membershipState, userContext)
            const hasUsedPauseEarlier = detailedMembership.pauseDaysUsedTillDate > 0

            const membershipData: MembershipDataV2 = {
                membershipState: membership.status === "PAUSED" ? "PAUSED" : "ACTIVE",
                action: packPageAction,
                title: pack.title,
                membershipStateMeta: membershipStateMeta,
                progressBar: {
                    leftText: `${packTagAndColor.tag === "UPCOMING" ? "Starts:" : "Started:"} ${startDateFormatted}`,
                    rightText: `Ends: ${endDateFormatted} ${(!hasUsedPauseEarlier && _.isEmpty(membership.activePause)) ? "" : "(updated)"}`,
                    total: endDate.diff(startDate, "days"),
                    completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
                    type: "FITNESS",
                    noPadding: true,
                    progressBarColor: packTagAndColor.color,
                    progressBarTextStyle: membershipState === "EXPIRED" ? { color: "#97a4c9" } : {}
                },
                membershipStateAction: null,
                cardBackgroundStyle: membershipState === "EXPIRED" ? { backgroundColor: packProductType === "FITNESS" ? "#28282e" : "#6278c4" } : {},
                cardTextStyle: membershipState === "EXPIRED" ? { color: "#FFFFFF" } : {},
                tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                contentMetric: {
                    contentId: cultMembershipId.toString()
                },
                noteInfo: null
            }

            const membershipStateAction = await this.getMembershipStateAction(detailedMembership, membership, packProductType, userContext, userInfo, membershipState, packPageAction, today, interfaces, hasUpcomingMembership)
            if (membershipStateAction) {
                membershipData.membershipStateAction = membershipStateAction
            }

            const city = userContext.userProfile.city
            if (city.cultCityId !== detailedMembership.cityID &&
                detailedMembership.freeAwayClassesRemainingForMonth < UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD) {
                membershipData.calloutInfo = {
                    leftText: `${city.name} | ${detailedMembership.freeAwayClassesRemainingForMonth > 1 ? "Available Free Classes" : "Available Free Class"}`,
                    rightText: detailedMembership.freeAwayClassesRemainingForMonth.toString(),
                    icon: "/image/icons/cult/location-pink.png"
                }
            }

            // For Expired or about to expire Pack and when there is no future membership
            if (isSubscriptionPack && detailedMembership.userSubscription.state === "ACTIVE") {
                const autoRenewalDate = TimeUtil.getMomentForDateString(detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate, tz)
                const manualRenewalAllowedDate = TimeUtil.getMomentForDateString(TimeUtil.addDays(tz, detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate,
                    detailedMembership.userSubscription.renewalGraceDays - 1), tz)
                if (autoRenewalDate.isBefore(today) && manualRenewalAllowedDate.isBefore(today)) {
                    removeMembershipCard = true
                }
            }

            return removeMembershipCard ? undefined : membershipData
        })

        const membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromisesForUser)
        const membershipDataList = membershipData.filter(item => item)
        if (membershipDataList.length > 0) {
            this.data = membershipDataList
            return this
        }
        return undefined
    }

    private async getMembershipStateAction(detailedMembership: CultMembership, membership: Membership, packProductType: MembershipProductType, userContext: UserContext, userInfo: User, membershipState: String, packPageAction: Action, today: momentTz.Moment, interfaces: CFServiceInterfaces, hasUpcomingMembership: boolean): Promise<Action> {
        const tz = userContext.userProfile.timezone
        switch (membershipState) {
            case "ACTIVE": {
                // For Subscription pack renew
                // if (!_.isEmpty(pack.subscriptionPack) && detailedMembership.userSubscription.state === "ACTIVE") {
                //     const autoRenewalDate = TimeUtil.getMomentForDateString(detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate, tz)
                //     const manualRenewalAllowedDate = TimeUtil.getMomentForDateString(TimeUtil.addDays(tz, detailedMembership.userSubscription.userSubscriptionCycle.nextBillingDate,
                //         detailedMembership.userSubscription.renewalGraceDays - 1), tz)
                // }

                if (!_.isEmpty(membership.activePause)) {
                    // CANCEL PAUSE
                    return CultUtil.getMembershipUnpauseAction(detailedMembership, packProductType, userContext, userInfo.isInternalUser)
                } else {
                    // PAUSE PACK
                    const pausePackData = await CultUtil.getPackPauseResumeDetails(detailedMembership, packProductType, userContext, interfaces)
                    if (pausePackData && pausePackData.isPauseAllowed) {
                        return CultUtil.getPackPauseResumeAction(pausePackData, detailedMembership)
                    }
                }
                return null
            }
            case "PAUSED": {
                // RESUME PACK
                const pausePackData = await CultUtil.getPackPauseResumeDetails(detailedMembership, packProductType, userContext, interfaces)
                if (pausePackData && pausePackData.isPauseAllowed) {
                    return CultUtil.getPackPauseResumeAction(pausePackData, detailedMembership)
                }
                return null
            }
            case "UPCOMING": {
                // CHANGE START DATE
                // if (!_.isEmpty(pack.subscriptionPack)) {
                //     return null
                // }
                return CultUtil.getChangeStartDateAction(detailedMembership, packProductType)
            }
            case "EXPIRING":
            case "EXPIRED": {
                if (!hasUpcomingMembership) {
                    return {
                        ...packPageAction,
                        title: "RENEW"
                    }
                }
                return null
            }
            default:
                return null
        }
    }

    private getMembershipStateMeta(detailedMembership: CultMembership, membership: Membership, membershipState: String, userContext: UserContext) {
        const tz = userContext.userProfile.timezone
        switch (membershipState) {
            case "PAUSED": {
                const pauseDate = detailedMembership.ActivePause ? detailedMembership.ActivePause.maxEndDate ? detailedMembership.ActivePause.maxEndDate : undefined : undefined
                if (pauseDate) {
                    const formatedPauseDate = TimeUtil.formatDateInTimeZone(tz, pauseDate)
                    const todaysDate = TimeUtil.todaysDate(tz)
                    const diffDays = TimeUtil.diffInDays(userContext.userProfile.timezone, todaysDate, formatedPauseDate)
                    return { title: diffDays === 0 ? `Resumes Tonight` : `Resumes in ${diffDays} days` }
                }
            }
            case "ACTIVE": {
                const isUpcomingPause = membership.status === "PURCHASED" && !_.isEmpty(membership.activePause)
                if (isUpcomingPause) {
                    const todayDateString = TimeUtil.todaysDateWithTimezone(tz)
                    const pauseStartDate = TimeUtil.formatEpochInTimeZone(tz, membership.activePause.start)
                    const startDateText = TimeUtil.diffInDays(tz, pauseStartDate, todayDateString) > 1 ? `on ${momentTz.tz(pauseStartDate, tz).format("DD MMM")}` : "tonight"
                    return { title: `Pause starts ${startDateText}` }
                }
                return null
            }
            default:
                return null
        }
    }
}

export class NCMembershipViewWidget extends MembershipWidgetV2 {

    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const tz = userContext.userProfile.timezone
        const patientsList = await interfaces.healthfaceService.getAllPatients(userProfile.userId)
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        if (!selfPatient) {
            return undefined
        }
        const subCategoryCode = "NUTRITIONIST"
        const activeMemberships = await interfaces.healthfaceService.getActiveBundleOrders(userProfile.userId, "BUNDLE", subCategoryCode)
        if (_.isEmpty(activeMemberships)) {
            return undefined
        }
        const membershipDataPromises = _.map(activeMemberships, async membership => {
            const productPromise = interfaces.catalogueService.getProduct(membership.productCode)
            const aggregatedMembershipInfosPromise = interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(membership.bookingId, membership.patientId, "CONSULTATION")
            const product = await productPromise
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.endDate)), tz)
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.startDate)), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const membershipState = CultUtil.getLivePTMembershipState(membership, tz)
            const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const aggregatedMembershipInfo = (await aggregatedMembershipInfosPromise)[0]
            const packPageAction: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(membership.productCode, subCategoryCode, membership.bookingId.toString())
            }
            const remainingSessionCount = aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed
            const membershipData: MembershipDataV2 = {
                membershipState: membershipState,
                action: packPageAction,
                title: product.title,
                membershipStateMeta: null,
                progressBar: {
                    leftText: `${aggregatedMembershipInfo.totalTicketsConsumed}/${aggregatedMembershipInfo.totalTickets} Completed`,
                    rightText: `Ends: ${endDateFormatted}`,
                    total: aggregatedMembershipInfo.totalTickets,
                    completed: aggregatedMembershipInfo.totalTicketsConsumed,
                    type: "FITNESS",
                    noPadding: true,
                    progressBarColor: packTagAndColor.color,
                    progressBarTextStyle: membershipState === "EXPIRED" ? { color: "#97a4c9" } : {}
                },
                membershipStateAction: null,
                cardBackgroundStyle: membershipState === "EXPIRED" ? { backgroundColor: "#28282e" } : {},
                cardTextStyle: membershipState === "EXPIRED" ? { color: "#FFFFFF" } : {},
                tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                contentMetric: {
                    contentId: membership.bookingId.toString()
                }
            }
            if (membershipState === "ACTIVE" || membershipState === "EXPIRING") {
                const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                    productMeta.productDetail = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productMeta.productCode))
                    return productMeta
                })
                const productMetas = await Promise.all(membershipProductsPromises)
                const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                if (remainingSessionCount > 0) {
                    membershipData.ctaAction = CareUtil.specialistListingAction(userContext, consultationProducts[0], false, membership.patientId, membership.bookingId, undefined, undefined, false, undefined, undefined, undefined, true, "Select a  Dietician")
                    membershipData.ctaAction.title = "Book Session"
                } else {
                    membershipData.ctaAction = {
                        actionType: "NAVIGATION",
                        title: "Renew",
                        url: getNutritionistCLPUrl()
                    }
                }
            }
            return membershipData
        })
        const membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromises)
        this.data = membershipData
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }

}

export class PTMembershipViewWidget extends MembershipWidgetV2 {

    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const subCategoryCode = <SUB_CATEGORY_CODE>this.productType
        const isLivePT = this.productType === "LIVE_PERSONAL_TRAINING"
        const activeMemberships = await interfaces.cultPTService.getActiveBundleOrders(userProfile.userId, "BUNDLE", subCategoryCode)
        if (_.isEmpty(activeMemberships)) {
            return undefined
        }
        const membershipDataPromises = _.map(activeMemberships, async membership => {
            const productPromise = interfaces.catalogueService.getProduct(membership.productCode)
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.endDate)), tz)
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.startDate)), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const numDaysToMembershipStart = startDate.diff(today, "days")
            const membershipState = isLivePT ? (numDaysToMembershipStart > 0 ? "UPCOMING" : membership.packStatus) : membership.packStatus
            const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDateFormatted = startDate.format("D MMM YYYY")
            let total = endDate.diff(startDate, "days")
            let completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
            const packPageAction: Action = {
                actionType: "NAVIGATION",
                url: `${ActionUtil.carefitbundle(membership.productCode, membership.subCategoryCode, membership.bookingId.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)}`
            }
            const product = await productPromise
            let aggregatedMembershipInfo
            if (membershipState !== "EXPIRED") {
                aggregatedMembershipInfo = membership.aggregatedUserMemberships[0]
            }
            let leftText, rightText
            let leftTextStyle, rightTextStyle
            let bookClassAction: Action
            if (membershipState === "EXPIRED" || membershipState === "COMPLETED") {
                leftText = membershipState === "EXPIRED" ? `Expired on ${endDateFormatted}` : "All sessions completed"
                rightText = ""
                if (membershipState === "COMPLETED") {
                    total = aggregatedMembershipInfo.totalTickets
                    completed = aggregatedMembershipInfo.totalTicketsConsumed
                }
            } else {
                total = membership.subCategoryCode === "LIVE_PERSONAL_TRAINING" ? aggregatedMembershipInfo.totalTickets : total
                completed = membership.subCategoryCode === "LIVE_PERSONAL_TRAINING" ? aggregatedMembershipInfo.totalTicketsConsumed : completed
                const remainingSessionCount = aggregatedMembershipInfo.totalTickets - aggregatedMembershipInfo.totalTicketsConsumed
                rightText = membershipState === "EXPIRING_SOON" ? `${numDaysToEndFromToday} ${pluralizeStringIfRequired("day", numDaysToEndFromToday)} left` : `Ends: ${endDateFormatted}`
                leftText = membershipState === "FINISHING_SOON" ? `${remainingSessionCount} ${pluralizeStringIfRequired("session", remainingSessionCount)} left` : membership.subCategoryCode === "LIVE_PERSONAL_TRAINING" ? `${remainingSessionCount}/${aggregatedMembershipInfo.totalTickets} Available` : `Started : ${startDateFormatted}`
                if (membershipState === "FINISHING_SOON") {
                    leftTextStyle = {
                        color: "#ffa300"
                    }
                } else if (membershipState === "EXPIRING_SOON") {
                    rightTextStyle = {
                        color: "#ffa300"
                    }
                }
                if (!_.isEmpty(aggregatedMembershipInfo.membershipAction) && aggregatedMembershipInfo.membershipAction.allowBooking) {
                    const sellableProducts = await interfaces.cultPTService.getConsultationSellableProductsNotCached(userContext.userProfile.cityId, this.productType)
                    const productId = sellableProducts.consultationTypes[0]?.products[0]?.code
                    const membershipProductsPromises = _.map(aggregatedMembershipInfo.productMetas, async (productMeta) => {
                        productMeta.productDetail = <ConsultationProduct>(await interfaces.catalogueService.getProduct(productMeta.productCode))
                        return productMeta
                    })
                    const productMetas = await Promise.all(membershipProductsPromises)
                    const consultationProducts = _.map(productMetas, productMeta => <ConsultationProduct>productMeta.productDetail)
                    if (this.productType === "PERSONAL_TRAINING") {
                        bookClassAction = await CareUtil.getPTSessionBookAction(userContext, interfaces.cultPTService, consultationProducts[0], membership.patientId, membership.productCode, membership.bookingId)
                    } else {
                        bookClassAction = await interfaces.careBusiness.getLivePTSessionBookAction(userContext, {
                            productId: productId || consultationProducts[0].productId,
                            actionTitle: "BOOK SESSION",
                            parentBookingId: membership.bookingId,
                            subCategoryCode: membership.subCategoryCode
                        })
                    }
                }
            }
            const membershipData: MembershipDataV2 = {
                membershipState: membershipState,
                action: packPageAction,
                title: product.title,
                membershipStateMeta: null,
                progressBar: {
                    leftText,
                    rightText,
                    total,
                    completed,
                    type: "FITNESS",
                    noPadding: true,
                    progressBarColor: packTagAndColor.color,
                    leftTextStyle,
                    rightTextStyle
                },
                membershipStateAction: this.getMembershipStateAction(membership.packAction, this.productType, membership.productCode),
                cardBackgroundStyle: {},
                cardTextStyle: {},
                tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                contentMetric: {
                    contentId: membership.bookingId.toString()
                }
            }
            if (AppUtil.isWeb(userContext)) {
                membershipData.membershipStateAction = bookClassAction
            } else {
                // Since page is having CTA, we dont need to show book CTA in widget
                // membershipData.ctaAction = bookClassAction
            }
            return membershipData
        })

        const membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromises)
        this.data = membershipData
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }

    private getMembershipStateAction(packAction: BundleOrderAction, productType: MembershipProductType, productId: string): Action {
        if (productType === "PERSONAL_TRAINING") {
            return
        }
        if (!_.isEmpty(packAction) && packAction.renewPack) {
            return {
                actionType: "NAVIGATION",
                title: "RENEW",
                url: `curefit://bundlesession?id=${productId}&subCategoryCode=${productType}`
            }
        }
    }
}


export class CFLiveMembershipViewWidget extends MembershipWidgetV2 {

    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }

    getMembershipStateAction(userContext: UserContext, membershipState: string, membership: Membership, product: Product): Action {
        const isTrialMembership = !_.isEmpty(membership.metadata) && membership.metadata.isTrial
        if (isTrialMembership) {
            if (membershipState === "ACTIVE" || membershipState === "EXPIRING") { // active membership case
                const action = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "membershipWidget")
                action.title = "BOOK A CLASS"
                return action
            } else {
                const isIOS = userContext.sessionInfo.osName && userContext.sessionInfo.osName === "ios"
                return LiveUtil.getMembershipExpiredModalAction("BECOME A MEMBER", isIOS, "live_membership_widget")
            }
            // else if (trial ended)
        } else if (membershipState === "EXPIRED") {
            const isIOS = userContext.sessionInfo.osName && userContext.sessionInfo.osName === "ios"
            return LiveUtil.getMembershipExpiredModalAction("BECOME A MEMBER", isIOS, "live_membership_widget")
        } else if (membershipState === "EXPIRING") {
            const subscriptionOptions = product.subscriptionOptions
            const planChangeSources = subscriptionOptions?.planChangeSources
            const requestSource = AppUtil.getRequestSource(userContext)
            let renewSupportedOnSource = false
            if (!_.isEmpty(planChangeSources)) {
                planChangeSources.map(planChangeSource => {
                    if (planChangeSource.source === requestSource.source && requestSource.osName === planChangeSource.osName) {
                        renewSupportedOnSource = true
                    }
                })
                if (subscriptionOptions.autoRenewing === false && renewSupportedOnSource && !product?.appleIAPProductDetails?.productId && !product?.androidIAPProductDetails?.productId) {
                    const isIOS = userContext.sessionInfo.osName && userContext.sessionInfo.osName === "ios"
                    return LiveUtil.getMembershipExpiredModalAction("RENEW", isIOS, "live_membership_widget")
                }
            }
            const action = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "membershipWidget")
            action.title = "BOOK A CLASS"
            return action
        } else if (membershipState === "ACTIVE") {
            const action = LiveUtil.getLiveClassBookingPageAction(userContext, "FITNESS", "membershipWidget")
            action.title = "BOOK A CLASS"
            return action
        }
        // for upcoming memberships, we don't need an action
        return undefined
    }

    getCardAction(userContext: UserContext, membership: Membership, membershipState: string): Action {
        const isIOS = userContext.sessionInfo.osName === "ios"
        if (isIOS && membershipState === "EXPIRED") {
            return LiveUtil.getMembershipExpiredModalAction("BECOME A MEMBER", isIOS, "live_membership_widget")
        }
        return {
            title: "VIEW MEMBERSHIP",
            actionType: "NAVIGATION",
            url: `curefit://livemembershippage?membershipId=${membership.id}`
        }
    }

    getLeftText(packTagAndColor: any, startDateFormatted: string, duration: number, isTrial: boolean, userContext: UserContext) {
        if (packTagAndColor.tag === "EXPIRED" && isTrial) {
            return `${duration}/${duration} Completed`
        }
        if (AppUtil.isWeb(userContext)) {
            return startDateFormatted
        }
        return `${packTagAndColor.tag === "UPCOMING" ? "Starts:" : "Started:"} ${startDateFormatted}`
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        if (!AppUtil.isLivePackSupported(userContext)) {
            return undefined
        }
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let activeMemberships = await interfaces.diyService.getAllMembershipDetails(userProfile.userId, tenant)
        let lastUserMembership
        if (_.isEmpty(activeMemberships)) {
            try {
                lastUserMembership = await interfaces.diyService.getMembershipDetails(userProfile.userId, tenant)
            } catch (e) {
                return undefined
            }
            activeMemberships = [lastUserMembership]
        }
        const membershipDataPromises = _.map(activeMemberships, async membership => {
            const isTrialMembership = !_.isEmpty(membership.metadata) && membership.metadata.isTrial
            const productPromise = interfaces.catalogueService.getProduct(membership.productId)
            const product = await productPromise
            if (!product) {
                return undefined
            }
            if (product.productType !== "CF_LIVE" && product.productType !== "LIVE_FITNESS") {
                return undefined
            }
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.end)), tz)
            const startDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(membership.start)), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const membershipState = LiveUtil.getLiveFitMembershipState(userContext, membership, product, isTrialMembership)
            const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
            const endDateFormatted = endDate.format("D MMM YYYY")
            const startDateFormatted = startDate.format("D MMM YYYY")
            const total = endDate.diff(startDate, "days")
            const membershipData: MembershipDataV2 = {
                membershipState: membershipState,
                action: this.getCardAction(userContext, membership, membershipState),
                title: product.title,
                start: membership.start,
                end: membership.end,
                membershipStateMeta: null,
                progressBar: {
                    leftText: this.getLeftText(packTagAndColor, startDateFormatted, total, isTrialMembership, userContext),
                    rightText: AppUtil.isWeb(userContext) ? endDateFormatted : `Ends: ${endDateFormatted}`,
                    total: total,
                    completed: numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days"),
                    type: "FITNESS",
                    noPadding: true,
                    progressBarColor: packTagAndColor.color,
                    progressBarTextStyle: membershipState === "EXPIRED" ? { color: "#97a4c9" } : {}
                },
                membershipStateAction: this.getMembershipStateAction(userContext, membershipState, membership, product),
                cardBackgroundStyle: membershipState === "EXPIRED" ? { backgroundColor: "#28282e" } : {},
                cardTextStyle: membershipState === "EXPIRED" ? { color: "#FFFFFF" } : {},
                tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                contentMetric: {
                    contentId: membership.productId
                }
            }
            // add action
            return membershipData
        })

        let membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromises)
        if (_.isEmpty(membershipData)) {
            return undefined
        }
        membershipData = membershipData.filter(membership => !_.isEmpty(membership))
        membershipData.sort((a, b) => {
            if (a.membershipState === "ACTIVE" || a.membershipState === "EXPIRING") {
                return -1
            }
            return 0
        })
        this.data = membershipData
        return this
    }
}


class TherapyViewWidget extends MembershipWidgetV2 {
    data: MembershipDataV2[]

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
    }


    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const tz = userContext.userProfile.timezone
        const userProfile = userContext.userProfile as CFUserProfile
        const sessionInfo = userContext.sessionInfo
        const patientsList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userProfile)
        const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
        if (!selfPatient) {
            return undefined
        }
        let activePackBookings = await interfaces.healthfaceService.getActivePacksByBookingType(userProfile.userId, selfPatient.id, "MIND", "BUNDLE", "MIND_THERAPY")
        if (_.isEmpty(activePackBookings)) {
            return undefined
        }
        activePackBookings = activePackBookings.filter(activeBooking => activeBooking.booking.status === "CONFIRMED")
        if (_.isEmpty(activePackBookings)) {
            return undefined
        }

        const membershipDataPromises = _.map(activePackBookings, async activePackBooking => {
            const productPromise = interfaces.catalogueService.getProduct(activePackBooking.bundleOrderResponse.productCode)
            const aggregatedMembershipInfosPromise = interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.booking.id, selfPatient.id, "CONSULTATION")
            const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activePackBooking.bundleOrderResponse.expiryTimeEpoch)), tz)
            const product = await productPromise
            const startDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activePackBooking.bundleOrderResponse.startTimeEpoch))
            const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDate(tz), tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const membershipState = CareUtil.getTherapyMemberShipState(activePackBooking, tz)
            const packTagAndColor = CultUtil.getCultPackTagAndColor(membershipState, numDaysToEndFromToday)
            const endDateFormatted = endDate.format("D MMM YYYY")

            const aggregatedMembershipInfo = (await aggregatedMembershipInfosPromise)[0]

            const packPageAction: Action = {
                actionType: "NAVIGATION",
                url: ActionUtil.carefitbundle(activePackBooking.bundleOrderResponse.productCode, activePackBooking.booking.subCategoryCode, activePackBooking.booking.id.toString(), undefined, undefined, undefined, undefined, undefined, userContext.sessionInfo.userAgent)
            }

            const membershipData: MembershipDataV2 = {
                membershipState: membershipState,
                action: packPageAction,
                title: product.title,
                membershipStateMeta: null,
                progressBar: {
                    leftText: `${aggregatedMembershipInfo.totalTicketsConsumed}/${aggregatedMembershipInfo.totalTickets} Completed`,
                    rightText: `Ends: ${endDateFormatted}`,
                    total: aggregatedMembershipInfo.totalTickets,
                    completed: aggregatedMembershipInfo.totalTicketsConsumed,
                    type: "FITNESS",
                    noPadding: true,
                    progressBarColor: packTagAndColor.color,
                    progressBarTextStyle: membershipState === "EXPIRED" ? { color: "#97a4c9" } : {}
                },
                membershipStateAction: null,
                cardBackgroundStyle: membershipState === "EXPIRED" ? { backgroundColor: "#28282e" } : {},
                cardTextStyle: membershipState === "EXPIRED" ? { color: "#FFFFFF" } : {},
                tag: { title: packTagAndColor.tag, color: packTagAndColor.color },
                contentMetric: {
                    contentId: activePackBooking.bundleOrderResponse.bookingId.toString()
                }
            }
            return membershipData
        })

        const membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromises)
        this.data = membershipData
        if (_.isEmpty(this.data)) {
            return undefined
        }
        return this
    }
}

class CareActiveBundleAndConsultationMembershipViewWidget extends MembershipWidgetV2 {
    data: MembershipDataV2[]
    ctaAction?: Action

    constructor(membershipWidget: MembershipWidgetV2) {
        super()
        this.showAction = membershipWidget.showAction
        this.productType = membershipWidget.productType
        this.header = membershipWidget.header
        this.showDivider = membershipWidget.showDivider
        this.dividerType = membershipWidget.dividerType
        this.data = []
        this.doctorTypes = membershipWidget.doctorTypes || []
        this.subCategoryCodes = membershipWidget.subCategoryCodes
        this.showActiveConsultation = membershipWidget.showActiveConsultation
    }


    private async isUserIsInDiagnosticsServiceableSegment(interfaces: CFServiceInterfaces, userContext: UserContext) {
        if (AppUtil.isProdLike) {
            const segment = await interfaces.segmentService.doesUserBelongToSegment(LAB_TEST_CYCLOP_SEGMENT, userContext)
            if (_.isEmpty(segment)) {
                return false
            }
        }
        return true
    }

    private async isUserIsInSkinHairServiceableSegment(userContext: UserContext) {
        if (AppUtil.isProdLike) {
            const segment = await userContext.userProfile.promiseMapCache.getPromise(SKIN_HAIR_SEGMENT_VALIDATION_API_KEY, {
                segmentId: "f37eb59f-2290-4e7c-976a-27956026985e", userContext
            })
            if (_.isEmpty(segment)) {
                return false
            }
        }
        return true
    }

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userProfile = userContext.userProfile as CFUserProfile
        if (!userContext.sessionInfo.isUserLoggedIn) {
            return undefined
        }
        const patientList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userProfile)
        if (_.isEmpty(patientList)) {
            return undefined
        }

        let activePackBookingsPromise: any[] = []
        let activeDiagnosticBookingDetailsPromise: Promise<any[]>
        const scheduledConsultationState = ["BOOKED", "SCHEDULED", "RESCHEDULED", "VITALS_COLLECTED", "STARTED", "CONSULTATION_STARTED"]
        if (!_.isEmpty(this.subCategoryCodes)) {
            activePackBookingsPromise = this.subCategoryCodes.map(async item => {
                if (item === "DIAGNOSTIC_TEST") {
                    return undefined
                }
                if (CareUtil.isPartOfSkinProducts(item) && !await this.isUserIsInSkinHairServiceableSegment(userContext)) {
                    return undefined
                }
                return interfaces.healthfaceService.getActiveBundleOrders(userProfile.userId, "BUNDLE", item)
            })
            if (this.subCategoryCodes.includes("DIAGNOSTIC_TEST") && await this.isUserIsInDiagnosticsServiceableSegment(interfaces, userContext)) {
                activeDiagnosticBookingDetailsPromise = (async () => {
                    try {
                        const bookings = await interfaces.healthfaceService.getPrescriptionLedDiagnostics(userProfile.userId, "DIAGNOSTIC_TEST", 0, 5)
                        if (!_.isEmpty(bookings)) {
                            const productCodes = bookings.map(item => item?.diagnosticsTestOrderResponse?.[0]?.productCodes?.[0]).filter(Boolean)
                            const productMap = await interfaces.catalogueService.getProductMap(productCodes)
                            const initialDiagnosticBookings = await Promise.all(this.getActiveDiagnosticBookings(interfaces, bookings, productMap))
                            return _.filter(initialDiagnosticBookings, function (item) { return !!item })
                        }
                    } catch (err) {
                        interfaces.logger.error("Failed to fetch activeDiagnosticBookingDetailsPromise " + err)
                    }
                    return []
                })()
            }
        }

        if (!_.isEmpty(this.doctorTypes) || this.showActiveConsultation) {
            if (this.ifLivePtWidget()) {
                activePackBookingsPromise.push(interfaces.cultPTService.getActiveConsultations(Number(userProfile.userId), LIVE_PT_DOCTOR_TYPES, "CULTFIT"))
            } else {
                activePackBookingsPromise.push(interfaces.healthfaceService.getActiveConsultations(Number(userProfile.userId), this.doctorTypes, undefined, true))
            }
        }

        const results = await Promise.all(activePackBookingsPromise.map(p => p.catch((e: any) => e)))

        const mergedBookings = results.concat(await activeDiagnosticBookingDetailsPromise)
        const uniqueBookings = _.filter(mergedBookings, function (item) { return !!item })
        let activePackBookings = uniqueBookings.filter(result => !(result instanceof Error))
        if (_.isEmpty(activePackBookings)) {
            return undefined
        } else {
            if (this.showAction && this.doctorTypes) {
                const consultations = await interfaces.healthfaceService.getConsultations(userContext.userProfile.userId, 0, 100, undefined, false, undefined, this.doctorTypes[0], undefined)
                const consultation = consultations.find((item) => {
                    return item.hasDigitalDocument
                })
                if (consultation) {
                    this.ctaAction = {
                        actionType: "OPEN_PDF_LINK",
                        title: "View Plan",
                        meta: {
                            title: "Your Plan",
                            consultationId: consultation.id
                        }
                    }
                }
            }

            activePackBookings = _.flatMap(activePackBookings)
            const covidConsultationBooking: ActiveConsultationResponse =  activePackBookings.find((item: ActiveConsultationResponse) => _.get(item?.consultationProduct?.productSpecs, "isCovidConsWithFollowups", false))
            if (!_.isEmpty(covidConsultationBooking)) {
                // This change is to show all scheduled consultation upfront followed by covid consultation and other consultation and bundle pack.

                const activeScheduledConsultation = activePackBookings.filter((item: any ) => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const completedCovidConsultationBookings = activePackBookings.filter(item => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && _.get(item?.consultationProduct?.productSpecs, "isCovidConsWithFollowups", false) && !scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const completedCovidConsultationBookingIds = completedCovidConsultationBookings.map(item => item.bookingId)

                const activeOtherConsultation = activePackBookings.filter((item: any ) => !["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && !completedCovidConsultationBookingIds.includes(item?.bookingId) && !scheduledConsultationState.includes(item?.status)).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                const activeBundleBookings = activePackBookings.filter((item: ActiveBundleOrderDetail) => ["DIAGNOSTICS", "BUNDLE"].includes(item?.categoryCode) && item?.subCategoryCode).sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })

                activePackBookings = activeScheduledConsultation.concat(completedCovidConsultationBookings, activeOtherConsultation, activeBundleBookings).filter(Boolean)
            } else {
                activePackBookings = activePackBookings.sort((a: ActiveConsultationResponse | ActiveBundleOrderDetail, b: ActiveConsultationResponse | ActiveBundleOrderDetail) => {
                    return momentTz(b.startDate).diff(momentTz(a.startDate))
                })
            }
            const productCodes = activePackBookings.map(activePackBooking => activePackBooking.productCode)
            const allProductsPromise = interfaces.catalogueService.getProducts(productCodes)
            const membershipDataPromises = _.map(activePackBookings, async activePackBooking => {
                const finalProduct = (await allProductsPromise).find((product: DiagnosticProduct | ConsultationProduct) => product.productId === activePackBooking.productCode)
                const product = finalProduct as DiagnosticProduct
                if (LAB_TEST_PACKS_SUB_CATEGORY_CODE.includes(activePackBooking?.subCategoryCode) ||
                    (activePackBooking?.categoryCode === "DIAGNOSTICS" && activePackBooking?.subCategoryCode === "DIAGNOSTIC_TEST")
                ) {
                    return this.getDiagnosticMemberDetails(userContext, product, activePackBooking, interfaces, patientList)
                }
                if (product?.subCategoryCode && this.subCategoryCodes.indexOf(product.subCategoryCode) !== -1) {
                    if ((
                        CareUtil.isPartOfConsultationCovidPackProducts(product.productId) ||
                        CareUtil.isPartOfConsultationPackAyurvedaProducts(product.setCode) ||
                        product.subCategoryCode === "PHYSIOTHERAPY"
                    ) &&
                        userContext.sessionInfo.userAgent !== "APP"
                    ) {
                        return undefined
                    }
                    return this.getBundleDetails(userContext, product, interfaces, activePackBooking as ActiveBundleOrderDetail, patientList)
                } else {
                    const product = finalProduct as ConsultationProduct
                    if (CareUtil.isSupportGroupProduct(product)) {
                        return this.getSupportGroupDetails(product, userContext, interfaces, activePackBooking as ActiveConsultationResponse)
                    } else {
                        return this.getConsultationDetails(product, userContext, interfaces, activePackBooking as ActiveConsultationResponse, patientList)
                    }
                }
            })
            const membershipData: MembershipDataV2[] = await Promise.all(membershipDataPromises)
            this.data = _.filter(membershipData, x => !!x)
        }
        if (_.isEmpty(this.data)) {
            return undefined
        }
        this.data = this.data.map(item => ({
            ...item,
            isCareFlow: true
        }))
        return this
    }

    async getBundleDetails(userContext: UserContext, product: DiagnosticProduct, interfaces: IServiceInterfaces, activePackBooking: ActiveBundleOrderDetail, patientList: Patient[]): Promise<MembershipDataV2> {
        const { progressBar, membershipStateAction, tag, color, action, imageUrl } = await this.getBundleSessionStateActionAndColor(userContext, product, interfaces, activePackBooking)
        const patient = patientList.find(patient => patient.id == activePackBooking.patientId)
        const membershipData: MembershipDataV2 = {
            membershipState: "ACTIVE",
            action,
            title: this.getBundleTitle(product, userContext),
            subtitle: activePackBooking?.totalSessionCount?.toString(),
            membershipStateMeta: null,
            progressBar,
            membershipStateAction,
            cardBackgroundStyle: { minHeight: 0 },
            cardTextStyle: {},
            tag: { title: tag, color: color },
            contentMetric: {
                contentId: activePackBooking.bookingId.toString()
            },
            infoItems: [
                `For ${patient.name}`
            ],
            imageUrl,
            itemType: "BUNDLE"
        }
        return membershipData
    }

    getBundleTitle(product: DiagnosticProduct, userContext: UserContext) {
        if (product.subCategoryCode === "MIND_THERAPY") {
            const isFromFlutterAppFlow = AppUtil.isFromFlutterAppFlow(userContext)
            switch (product?.setCode) {
                case "COUPLE_THERAPY_SET": return isFromFlutterAppFlow ? "Couple Therapy" : `Couple Therapy - ${product.title}`
                case "MIND_THERAPY_SET": return isFromFlutterAppFlow ? "Therapy" : `Therapy - ${product.title}`
                default: return `${product.title}`
            }
        }
        return product.title
    }

    async getConsultationDetails(
        product: ConsultationProduct,
        userContext: UserContext,
        interfaces: IServiceInterfaces,
        activeConsultation: ActiveConsultationResponse,
        patientList: Patient[] = []
    ): Promise<MembershipDataV2> {

        // const isMindTherapyFlutterAppSupported = CareUtil.isMindDoctorType(product.doctorType) && await AppUtil.checkIfUserPartOfMindTherapyAuroraExperiment(userContext, interfaces.hamletBusiness)
        // if (isMindTherapyFlutterAppSupported) {
        //     userContext.sessionInfo.appSource = AppUtil.getFlutterAppSource()
        // }
        const doctorPromise = interfaces.healthfaceService.getDoctorDetails(Number(activeConsultation.doctorId), undefined, undefined, undefined, true)
        const appSource = userContext.sessionInfo.appSource
        if (!CareUtil.isMindDoctorType(product.doctorType)) {
            userContext.sessionInfo.appSource = null
        }
        const actionUrl = ActionUtil.teleconsultationSingle(userContext, activeConsultation.productCode, product.urlPath, activeConsultation.bookingId.toString(), undefined, CareUtil.getVerticalForConsultation(product.doctorType))
        userContext.sessionInfo.appSource = appSource
        const tz = userContext.userProfile.timezone
        const patient = patientList.find(patient => patient.id == activeConsultation.patientId)
        let packPageAction: Action = {
            title: "MANAGE",
            actionType: "NAVIGATION",
            url: actionUrl
        }
        const unreadMsgCount = _.get(activeConsultation, "appointmentActionsWithContext.chatActionWithContext.context.twilioUnreadMessageCountViewMap.PATIENT.unreadMessageCount")
        const doctor = await doctorPromise
        const metaStateActionPromise = this.getConsultationAction(userContext, activeConsultation, product, doctor, interfaces)
        const { tag, color } = CareActiveBundleAndConsultationMembershipViewWidget.getConsultationStateAndColor(activeConsultation, product)

        const now = new Date().getTime()
        if (now >= activeConsultation.startDate - 900000 && now <= activeConsultation.endDate) {
            const isVideoEnabled = activeConsultation?.appointmentActionsWithContext?.videoActionWithContext?.action?.actionPermitted
            if (isVideoEnabled && activeConsultation.status !== "COMPLETED" && !CareUtil.isCoupleTherapist(product.doctorType)) {
                packPageAction = await this.getVideoActionForConsultation(userContext, activeConsultation, product, doctor, interfaces)
            }
        }
        const membershipData: MembershipDataV2 = {
            membershipState: "ACTIVE",
            action: packPageAction,
            title: product.title,
            membershipStateMeta: null,
            progressBar: undefined,
            membershipStateAction: await metaStateActionPromise,
            infoItems: [
                `For ${patient.name} | ${TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activeConsultation.startDate), "ddd, D MMM, hh:mm A")}`
            ],
            cardBackgroundStyle: {},
            cardTextStyle: {},
            tag: { title: tag, color: color },
            contentMetric: {
                contentId: activeConsultation.bookingId.toString()
            },
            footerInfo: {
                text: doctor.name,
                image: doctor.displayImage,
                bulletedInfo: unreadMsgCount > 0 ? {
                    text: `${unreadMsgCount} unread messages`,
                    bulletColor: "#ff5942"
                } : undefined
            },
            itemType: "CONSULTATION"
        }
        return membershipData
    }

    async getSupportGroupDetails(
            product: ConsultationProduct,
            userContext: UserContext,
            interfaces: IServiceInterfaces,
            consultation: ActiveConsultationResponse,
    ): Promise<MembershipDataV2> {
        const tz = userContext.userProfile.timezone
        const actionUrl = ActionUtil.teleconsultationSingle(userContext, consultation.productCode, product.urlPath, consultation.bookingId.toString(), undefined, CareUtil.getVerticalForConsultation(product.doctorType))
        let action: Action
        const now = new Date().getTime()
        if (now < consultation.startDate - 900000 || now > consultation.endDate) {
            action = {
                title: "MANAGE",
                actionType: "NAVIGATION",
                url: actionUrl
            }
        } else {
            if (consultation.patientZoomLink) {
                action = {
                    title: "JOIN",
                    url: consultation.patientZoomLink,
                    actionType: "OPEN_WEBPAGE",
                }
            } else {
                /* TO BE IMPLEMENTED
                action = {
                    title: "CHECKIN",
                    url: "",
                    actionType: "NAVIGATION",
                }
                */
            }
        }
        const doctor = await interfaces.healthfaceService.getDoctorDetails(Number(consultation.doctorId), undefined, undefined, undefined, true)
        const {tag, color} = CareActiveBundleAndConsultationMembershipViewWidget.getSupportGroupStateAndColor(consultation.status)
        return {
            title: product.title,
            infoItems: [TimeUtil.formatEpochInTimeZone(tz, consultation.startDate, "ddd, DD MMM, hh:mm a")],
            footerInfo: {
                text: doctor.name,
                image: doctor.displayImage,
                bulletedInfo: {
                    text: "Moderator",
                },
            },
            tag: {
                title: tag,
                color,
            },
            action,
            itemType: "SUPPORT_GROUP",
            membershipState: null,
            membershipStateMeta: null,
            progressBar: null,
            membershipStateAction: null,
            cardBackgroundStyle: null,
            cardTextStyle: null,
        }
    }

    async getDiagnosticMemberDetails(
        userContext: UserContext,
        product: DiagnosticProduct,
        activePackBooking: ActiveBundleOrderDetail,
        interfaces: IServiceInterfaces,
        patientList: Patient[]
    ): Promise<MembershipDataV2> {
        const patient = patientList.find(patient => patient.id == activePackBooking.patientId)
        const isDiagPack = LAB_TEST_PACKS_SUB_CATEGORY_CODE.includes(activePackBooking?.subCategoryCode)
        const { membershipStateAction, tag, color, action, infoItems = [], progressBar } = isDiagPack
            ? await this.getDiagnosticsStateActionAndColor(userContext, product, interfaces, activePackBooking)
            : await this.getAlaCarteDiagnosticsStateActionAndColor(userContext, product, interfaces, activePackBooking)
        const membershipData: MembershipDataV2 = {
            membershipState: "ACTIVE",
            action,
            title: isDiagPack ? product.title : activePackBooking.title,
            membershipStateMeta: null,
            progressBar: progressBar,
            membershipStateAction: membershipStateAction as Action,
            infoItems: [
                `For ${patient.name}`,
                ...infoItems
            ],
            cardBackgroundStyle: { minHeight: 0 },
            cardTextStyle: {},
            tag: { title: tag, color: color },
            contentMetric: {
                contentId: activePackBooking.bookingId.toString()
            },
            itemType: "DIAGNOSTICS"
        }
        return membershipData
    }

    private async getBundleSessionStateActionAndColor(
        userContext: UserContext,
        product: DiagnosticProduct,
        interfaces: IServiceInterfaces,
        activePackBooking: ActiveBundleOrderDetail
    ) {
        const tz = userContext.userProfile.timezone
        let tag, color, membershipStateAction
        const aggregatedMembershipInfos = !_.isEmpty(activePackBooking.aggregatedUserMemberships)
            ? activePackBooking.aggregatedUserMemberships
            : await interfaces.healthfaceService.getAggregatedMembershipsByRootBookingId(activePackBooking.bookingId, activePackBooking.patientId, "CONSULTATION")
        const aggregatedMembershipInfo = aggregatedMembershipInfos?.[0] || undefined
        // const isMindTherapyAuroraThemeSupported = product.subCategoryCode === "MIND_THERAPY" && AppUtil.isMindTherapyAuroraThemeSupported(userContext)
        // if (isMindTherapyAuroraThemeSupported) {
        //     userContext.sessionInfo.appSource = AppUtil.getFlutterAppSource()
        // }
        // const appSource = isMindTherapyAuroraThemeSupported ? AppUtil.getFlutterAppSource() : userContext.sessionInfo.appSource
        const appSource = userContext.sessionInfo.appSource
        const action: Action = {
            actionType: "NAVIGATION",
            title: "VIEW",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, activePackBooking.bookingId.toString(), undefined, undefined, undefined, product.setCode, product.clubCode, userContext.sessionInfo.userAgent, undefined, appSource)
        }
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(aggregatedMembershipInfo?.startEndEpoch?.endEpoch || activePackBooking.endDate)), tz)
        const endDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(activePackBooking.endDate), "D MMM YYYY")
        const renewActionUrl = ActionUtil.carefitbundle(product.productId, product.subCategoryCode, undefined, undefined, undefined, undefined, product.setCode, product.clubCode, userContext.sessionInfo.userAgent)
        if (
            SESSION_BASED_SUB_CATEGORY_CODE.includes(activePackBooking.subCategoryCode) ||
            CareUtil.isPartOfConsultationPackAyurvedaProducts(product?.setCode)
        ) {
            const isMindTherapyFlutterApp = AppUtil.isFromFlutterAppFlow(userContext) && product.subCategoryCode === "MIND_THERAPY"
            const total = aggregatedMembershipInfo?.totalTickets || activePackBooking?.totalSessionCount || 0
            const completed = aggregatedMembershipInfo?.totalTicketsConsumed || activePackBooking?.completedSessionCount || 0
            const renewActionUrl = ActionUtil.carefitbundle(product.productId, product.subCategoryCode, undefined, undefined, undefined, undefined, product.setCode, product.clubCode, userContext.sessionInfo.userAgent)
            const sessionRemaining = total - completed
            const isPartOfSkinProducts = CareUtil.isPartOfSkinProducts(product.subCategoryCode)
            const daysDiff = endDate.diff(today, "day")
            const leftTextSuffix = daysDiff >= 0 ? ` • ${daysDiff} days remaining` : ""
            const progressBar = {
                leftText: (completed === 0 ? `Book your session!` : `${sessionRemaining}/${total} Sessions Remaining`) + (isMindTherapyFlutterApp ? leftTextSuffix : ""),
                rightText: isMindTherapyFlutterApp ? undefined : `Ends: ${endDateFormatted}`,
                total,
                completed,
                type: "FITNESS",
                noPadding: true,
                progressBarColor: "#008300",
                progressBarTextStyle: {},
                isSplitView: true,
                leftTextStyle: { color: "#8d93a0" },
                rightTextStyle: { color: "#8d93a0" }
            }
            if (endDate < today && sessionRemaining > 1) {
                tag = "EXPIRED"
                color = "#b00020"
                membershipStateAction = isPartOfSkinProducts ? null : {
                    actionType: "NAVIGATION",
                    title: "RENEW",
                    url: renewActionUrl
                }
                progressBar.leftText = `${completed}/${total} Session Completed`
                progressBar.rightText = `Expired: ${endDateFormatted}`
                progressBar.progressBarColor = "#767676"
            } else {
                if (sessionRemaining === 0) {
                    tag = "COMPLETED"
                    color = "#55565b"
                    membershipStateAction = isPartOfSkinProducts ? null : {
                        actionType: "NAVIGATION",
                        title: "RENEW",
                        url: renewActionUrl
                    }
                    progressBar.leftText = `All Sessions Completed`
                    progressBar.rightText = undefined
                } else {
                    tag = "ACTIVE"
                    color = "#008300"
                    const consultationProduct = await interfaces.catalogueService.getProduct(aggregatedMembershipInfo?.productMetas?.[0]?.productCode) as ConsultationProduct
                    membershipStateAction = isPartOfSkinProducts ?
                        null :
                        (consultationProduct ? {
                            ...CareUtil.specialistListingAction(
                                userContext,
                                consultationProduct,
                                false,
                                activePackBooking.patientId,
                                activePackBooking.bookingId, undefined, undefined, false, undefined, undefined, undefined, true, `Select a ${CareUtil.getDoctorText(consultationProduct.doctorType)}`),
                            actionType: "NAVIGATION",
                            title: "CONSULT"
                        } : action)
                }
            }
            return {
                tag,
                color,
                progressBar,
                membershipStateAction: isMindTherapyFlutterApp ? {
                    actions: [membershipStateAction],
                    actionType: "ACTION_LIST",
                    iconUrl: "image/vm/e1640759-67b4-494e-b973-defdd1436362.png",
                    title: membershipStateAction?.title
                } : membershipStateAction,
                action,
                imageUrl: isMindTherapyFlutterApp ? "image/vm/e839ce61-54d3-44e7-ab6b-8dd668b30045.png" : undefined
            }
        } else {
            const isCovidPack = CareUtil.isPartOfConsultationPackProducts(activePackBooking.subCategoryCode) && (
                CareUtil.isPartOfConsultationCovidPackProducts(product.productId) ||
                CareUtil.isPartOfConsultationCovidPackProducts(product.setCode)
            )
            const startDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(aggregatedMembershipInfo?.startEndEpoch?.startEpoch || activePackBooking.startDate), "D MMM YYYY")
            const startDate = TimeUtil.getDefaultMomentForDateString(startDateFormatted, tz)
            const numDaysToEndFromToday = endDate.diff(today, "days")
            const total = endDate.diff(startDate, "days")
            const completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
            const progressBar = {
                leftText: `Started: ${startDateFormatted}`,
                rightText: `Ends: ${endDateFormatted}`,
                total,
                completed,
                type: "FITNESS",
                noPadding: true,
                progressBarColor: "#008300",
                progressBarTextStyle: {},
                isSplitView: false,
                leftTextStyle: { color: "#8d93a0" },
                rightTextStyle: { color: "#8d93a0" }
            }
            if (endDate < today) {
                tag = "EXPIRED"
                color = "#b00020"
                membershipStateAction = {
                    actionType: "NAVIGATION",
                    title: "RENEW",
                    url: renewActionUrl
                }
                progressBar.rightText = `Ended: ${endDateFormatted}`
                progressBar.progressBarColor = "#767676"
            } else {
                tag = "ACTIVE"
                color = "#008300"
                const consultationProduct = isCovidPack ? undefined : await interfaces.catalogueService.getProduct(aggregatedMembershipInfo?.productMetas?.[0]?.productCode) as ConsultationProduct
                membershipStateAction = isCovidPack || !consultationProduct ? action : {
                    ...CareUtil.specialistListingAction(
                        userContext,
                        consultationProduct,
                        false,
                        activePackBooking.patientId,
                        activePackBooking.bookingId, undefined, undefined, false, undefined, undefined, undefined, true, `Select a ${CareUtil.getDoctorText(consultationProduct.doctorType)}`),
                    actionType: "NAVIGATION",
                    title: "CONSULT"
                }
            }
            return {
                tag,
                color,
                progressBar,
                membershipStateAction,
                action
            }
        }
    }

    private static getSupportGroupStateAndColor(status: CONSULTATION_STATUS) {
        let tag, color
        switch (status) {
            case "CUSTOMER_MISSED": {
                tag = "MISSED"
                color = "#f7c744"
                break
            }
            case "SCHEDULED": {
                tag = "SCHEDULED"
                color = "#0fe498"
                break
            }
            case "COMPLETED": {
                tag = "COMPLETED"
                color = "#00beff"
                break
            }
            case "STARTED": {
                tag = "STARTED"
                color = "#00beff"
                break
            }
            default: {
                tag = "CANCELLED"
                color = "#ff5942"
                break
            }
        }
        return {tag, color}
    }

    private static getConsultationStateAndColor(
        activeConsultation: ActiveConsultationResponse,
        product: ConsultationProduct
    ) {
        let tag, color
        const status: string = activeConsultation.status
        switch (status) {
            case "AGENT_MISSED": {
                tag = `${CareUtil.getDoctorText(product.doctorType).toUpperCase()} MISSED`
                color = "#f7c744"
                break
            }
            case "MISSED":
            case "CUSTOMER_MISSED": {
                tag = "MISSED"
                color = "#f7c744"
                break
            }
            case "BOOKED":
            case "SCHEDULED":
            case "RESCHEDULED":
            case "VITALS_COLLECTED": {
                tag = "SCHEDULED"
                color = "#0fe498"
                break
            }
            case "DOCTOR_UNAVAILABLE": {
                tag = "DOCTOR UNAVAILABLE"
                color = "#ff5942"
                break
            }
            case "COMPLETED":
            case "CONSULTATION_COMPLETED":
            case "PRESCRIPTION_GENERATED": {
                tag = "COMPLETED"
                color = "#00beff"
                break
            }
            case "STARTED":
            case "CONSULTATION_STARTED": {
                tag = "STARTED"
                color = "#00beff"
                break
            }
            default: {
                tag = "EXPIRED"
                color = "#ff5942"
                break
            }
        }
        return {
            tag,
            color
        }
    }

    private async getConsultationAction(
        userContext: UserContext,
        activeConsultation: ActiveConsultationResponse,
        product: ConsultationProduct,
        doctor: Doctor,
        interfaces: IServiceInterfaces
    ) {
        let action
        const isChatEnabled = activeConsultation?.appointmentActionsWithContext?.chatActionWithContext?.action?.actionPermitted
        const isVideoEnabled = activeConsultation?.appointmentActionsWithContext?.videoActionWithContext?.action?.actionPermitted
        const isRescheduleEnabled = activeConsultation?.appointmentActionsWithContext?.rescheduleActionWithContext?.action?.actionPermitted
        const hasPrescription = activeConsultation.hasDigitalDocument && activeConsultation?.hasPrescription
        if (!AppUtil.isFromFlutterAppFlow(userContext)) {
            if (activeConsultation.status === "COMPLETED" && hasPrescription && !CareUtil.isTherapyOnlyDoctorType(product.doctorType)) {
                action = _.get(activeConsultation?.consultationProduct?.productSpecs, "isCovidConsWithFollowups", false)  ? {
                    actionType: "NAVIGATION",
                    title: "BOOK FOLLOW-UP",
                    url: ActionUtil.teleconsultationSingle(userContext, activeConsultation.productCode, product.urlPath, activeConsultation.bookingId.toString(), undefined, CareUtil.getVerticalForConsultation(product.doctorType))
                } : {
                    actionType: "NAVIGATION",
                    title: "VIEW PRESCRIPTION",
                    url: `curefit://carefitPrescription?tcBookingId=${activeConsultation.bookingId}&productId=${activeConsultation.productCode}`
                }
            } else if (["MISSED", "CUSTOMER_MISSED", "AGENT_MISSED", "DOCTOR_UNAVAILABLE"].includes(activeConsultation.status) && isRescheduleEnabled) {
                action = {
                    actionType: "RESCHEDULE_TC",
                    title: "RESCHEDULE",
                    url: `curefit://rescheduleTc?parentBookingId=${activeConsultation.bookingId}`,
                    meta: {
                        "tcBookingId": activeConsultation.bookingId,
                        "centerId": doctor.doctorCenterMapping?.[0]?.centerId,
                        "productId": product.productId,
                        "patientId": activeConsultation.patientId
                    }
                }
            } else if (isVideoEnabled && activeConsultation.status !== "COMPLETED" && !CareUtil.isCoupleTherapist(product.doctorType)) {
                action = this.getVideoActionForConsultation(userContext, activeConsultation, product, doctor, interfaces)
            }
        }
        if (!action) {
            if (isChatEnabled) {
                action = {
                    ...CareUtil.getChatMessageAction(
                        userContext,
                        activeConsultation?.appointmentActionsWithContext.chatActionWithContext,
                        activeConsultation.patientId,
                        doctor.name,
                        CareUtil.getChatChannelWithAppointmentContext(activeConsultation?.appointmentActionsWithContext),
                        doctor.displayImage,
                        doctor.qualification,
                        activeConsultation.appointmentId,
                        activeConsultation.bookingId
                    ),
                    imageUrl: AppUtil.isFromFlutterAppFlow(userContext) ? "image/vm/f34ae50c-95f5-4b19-b6bd-e1f13aa05550.png" : "image/icons/care_checkout_info_item/chat.png",
                    title: "MESSAGE"
                }
            } else {
                const appSource = userContext.sessionInfo.appSource
                if (!CareUtil.isMindDoctorType(product.doctorType)) {
                    userContext.sessionInfo.appSource = null
                }
                action = {
                    title: "VIEW",
                    actionType: "NAVIGATION",
                    url: ActionUtil.teleconsultationSingle(userContext, product.productId, product.urlPath, activeConsultation.bookingId.toString(), undefined, CareUtil.getVerticalForConsultation(product.doctorType))
                }
                userContext.sessionInfo.appSource = appSource
            }
        }
        return action as Action
    }

    private async getVideoActionForConsultation(userContext: UserContext, activeConsultation: ActiveConsultationResponse,
                                                product: ConsultationProduct, doctor: Doctor, interfaces: IServiceInterfaces): Promise<Action> {
        if (CareUtil.isSupportGroupProduct(product)) {
            const status = activeConsultation.status
            if (status === "COMPLETED" || status === "CUSTOMER_MISSED") {
                return undefined
            }
            const zoomLink = activeConsultation.patientZoomLink
            if (!zoomLink) {
                return undefined
            }
            const isActionDisabled = new Date().getTime() < activeConsultation.startDate - 900000
                    || new Date().getTime() > activeConsultation.endDate

            return {
                title: "JOIN",
                subtitle: isActionDisabled ? "Link will activate 15 mins before session" : undefined,
                url: zoomLink,
                actionType: AppUtil.isWeb(userContext) ? "EXTERNAL_DEEP_LINK" : "OPEN_WEBPAGE",
                disabled: isActionDisabled,
                isEnabled: !isActionDisabled
            }
        }
        return {
            ...CareUtil.getVideoCallJoinActionWithParams(
                userContext,
                await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userContext.userProfile),
                await userContext.userPromise,
                product,
                doctor,
                activeConsultation.startDate,
                activeConsultation.bookingId,
                activeConsultation.appointmentId,
                activeConsultation.patientId,
                activeConsultation.appointmentActionsWithContext?.videoActionWithContext?.context?.twilioCommunicationMode?.modeName,
                CareUtil.getChatChannelWithAppointmentContext(activeConsultation?.appointmentActionsWithContext),
                AppUtil.isFromFlutterAppFlow(userContext)
                        ? await interfaces.healthfaceService.getStaticInstructionsV3(product.productId, product.doctorType, "video", CareUtil.getInstructionTenant(product))
                        : await interfaces.healthfaceService.getConsultationInstructionsV2(product.productId, product.doctorType, "video", CareUtil.getInstructionTenant(product)),
                [],
                undefined
            )[0],
            title: "JOIN"
        }
    }

    private async getDiagnosticsStateActionAndColor(
        userContext: UserContext,
        product: DiagnosticProduct,
        interfaces: IServiceInterfaces,
        activePackBooking: ActiveBundleOrderDetail
    ) {
        const tz = userContext.userProfile.timezone
        const action: Action = {
            actionType: "NAVIGATION",
            title: "MANAGE",
            url: ActionUtil.carefitbundle(product.productId, product.subCategoryCode, activePackBooking.bookingId.toString(), undefined, undefined, undefined, product.setCode, product.clubCode, userContext.sessionInfo.userAgent)
        }
        const today = TimeUtil.getMomentForDateString(TimeUtil.todaysDateWithTimezone(tz), tz)
        const endDate = TimeUtil.getMomentForDateString(TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(activePackBooking.endDate)), tz)
        const endDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(activePackBooking.endDate), "D MMM YYYY")
        const startDateFormatted = TimeUtil.formatEpochInTimeZone(tz, Number(activePackBooking.startDate), "D MMM YYYY")
        const startDate = TimeUtil.getDefaultMomentForDateString(startDateFormatted, tz)
        const numDaysToEndFromToday = endDate.diff(today, "days")
        const total = endDate.diff(startDate, "days")
        const completed = numDaysToEndFromToday > 0 ? today.diff(startDate, "days") : endDate.diff(startDate, "days")
        const progressBar = {
            leftText: `Started: ${startDateFormatted}`,
            rightText: `Ends: ${endDateFormatted}`,
            total,
            completed,
            type: "FITNESS",
            noPadding: true,
            progressBarColor: "#008300",
            progressBarTextStyle: {},
            isSplitView: false,
            leftTextStyle: { color: "#8d93a0" },
            rightTextStyle: { color: "#8d93a0" }
        }
        return {
            tag: "ACTIVE",
            color: "#008300",
            membershipStateAction: action,
            action,
            progressBar,
            infoItems: [] as any[]
        }
    }

    private async getAlaCarteDiagnosticsStateActionAndColor(
        userContext: UserContext,
        product: DiagnosticProduct,
        interfaces: IServiceInterfaces,
        activePackBooking: any
    ) {
        let tag, color, membershipStateAction
        const action: Action = {
            actionType: "NAVIGATION",
            title: "MANAGE",
            url: ActionUtil.diagnostics(activePackBooking.subCategoryCode, activePackBooking.bookingId.toString())
        }
        tag = "ACTIVE"
        color = "#008300"
        membershipStateAction = activePackBooking?.phelboNumber ? {
            actionType: "PHONE_CALL_NAVIGATION",
            icon: "PHONE_CALL",
            title: "Call Associate",
            meta: {
              phoneNumber: activePackBooking?.phelboNumber
            }
        } : action
        return {
            tag,
            color,
            membershipStateAction,
            action,
            progressBar: undefined as any,
            infoItems: [] as any[]
        }
    }

    ifLivePtWidget(): boolean {
        return !_.isEmpty(_.intersection(this.doctorTypes || [], LIVE_PT_DOCTOR_TYPES))
    }

    getActiveDiagnosticBookings(interfaces: IServiceInterfaces, activeDiagnosticBookingDetails: BookingDetail[], productMap: {[productId: string]: Product}): Promise<any>[] {
        const currentTime = momentTz().valueOf()
        const activeDiagnosticBookings = activeDiagnosticBookingDetails.map(async (item) => {
            if (item?.diagnosticsTestOrderResponse[0]?.status === "REPORT_GENERATED") {
                const currentStateTransitionTime = momentTz(item?.diagnosticsTestOrderResponse[0]?.currentStateTransitionTime).valueOf()
                const differenceInDays = (currentTime - currentStateTransitionTime) / (86400 * 1000)
                if (differenceInDays > 7) {
                    return undefined
                }
            }

            // Added this check to avoid showing up orders older than 90 days so that we can avoid latency
            if (item.booking.createdAt) {
                const createdAtDateTime =  momentTz(item.booking.createdAt).valueOf()
                const differenceInDays = (currentTime - createdAtDateTime) / (86400 * 1000)
                if (differenceInDays > 90) {
                    return undefined
                }
            }
            // Removed rootbooking check to avoid unneccessary latency
            // const rootBookingDetail = item.booking.rootBookingId !== item.booking.id && item.booking.rootBookingId !== -1 ? await interfaces.healthfaceService.getBookingDetail(Number(item.booking.rootBookingId)) : null
            // if (rootBookingDetail?.booking?.categoryCode === "BUNDLE") {
            //     return undefined
            // }
            const diagnosticTestTitle = productMap[item.diagnosticsTestOrderResponse[0].productCodes[0]]?.title
            const extraTestsTitle = (item.diagnosticsTestOrderResponse[0].productCodes.length >= 2) ? " + " + (item.diagnosticsTestOrderResponse[0].productCodes.length - 1) + (item.diagnosticsTestOrderResponse[0].productCodes.length >= 3 ? " Tests" : " Test") : ""
            const phelboNumber = item?.diagnosticsTestOrderResponse?.[0]?.atHomeStepInfo?.allowedActions?.includes("PHLEBO_CALLING") ? item?.diagnosticsTestOrderResponse?.[0]?.atHomeDiagnosticOrder?.phleboMobileNumber : undefined
            return {
                bookingId: item.booking.id,
                userId: item.booking.userId,
                patientId: item.booking.patientId,
                subCategoryCode: item.booking.subCategoryCode,
                categoryCode: item.booking.categoryCode,
                startDate: item.booking.createdAt,
                productCode: item.booking.productCode,
                title: diagnosticTestTitle + extraTestsTitle,
                phelboNumber,
            }
        })
        return activeDiagnosticBookings
    }
}
