import * as _ from "lodash"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { BaseWidget, IBaseWidget, UserContext } from "@curefit/vm-models"
import { IsIn, ValidateNested } from "class-validator"
import { Type } from "class-transformer"
import { WidgetType } from "@curefit/vm-common"
import { Action } from "@curefit/vm-models"
import { Header } from "@curefit/vm-models"
import { ProductPrice, ProductType } from "@curefit/product-common"
import { TimeUtil } from "@curefit/util-common"
import { ActionUtil } from "@curefit/base-utils"
import { Order } from "@curefit/order-common"
export const PRODUCT_BROWSE_TYPES = ["GEAR"]
export const LAYOUT_TYPES = ["GRID", "LIST"]
export type LAYOUT_TYPE = "GRID" | "LIST"

export class RecentGearOrderWidget extends BaseWidget {
  constructor() {
    super("RECENT_GEAR_ORDER_WIDGET")
    this.showDivider = true
    this.dividerType = "LARGE"
    this.title = "This is the recent gear order widget"
  }

  @ValidateNested()
  @Type(() => Header)
  title: string

  async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
    const { userProfile } = userContext
    const tz = userContext.userProfile.timezone
    const secondsInADay = 60 * 60 * 24 * 1000
    // const today = TimeUtil.formatEpochInTimeZoneDateFns(tz, TimeUtil.getCurrentEpoch() + secondsInADay, "yyyy-MM-dd")
    /*       const orders = await interfaces.orderService.getRecentGearOrder(userProfile.userId, 30, today)
          const activeOrders = _.filter(orders, (order) => {
              return !(_.includes(_.flatten(_.map(order.products, (product) => {
                  return _.map(product.statusHistory, "status")
              })), "CANCELLED"))
          })
          const order = activeOrders.length > 0 ? activeOrders[0] : null

     */
    const order: Order = null
    const data: any = {}
    if (!_.isEmpty(order)) {
      const { orderId } = order
      this.action = {
        actionType: "NAVIGATION",
        title: "TRACK",
        url: ActionUtil.gearOrder(`${orderId}`)
      }

      const { productSnapshots } = order
      data["orderItemsCount"] = productSnapshots.length
      const allProductNames: string[] = []
      productSnapshots.forEach((product) => {
        allProductNames.push(product.title)
      })
      if (data["orderItemsCount"] > 1) {
        data["title"] = `${data["orderItemsCount"]} items (${allProductNames.join(",")})`
      } else {
        data["title"] = `${allProductNames.join(",")}`
      }
      const { imageUrls } = productSnapshots[0]
      if (imageUrls.length > 0) data["previewImageUrl"] = imageUrls[0]

      const gearOrder = await interfaces.gearService.getOrderWithShipmentStatus(orderId)
      const gearLastShipment: any = _.last(gearOrder.shipments)
      const shipmentHistory = gearLastShipment.history
      const shipmentStateHistory = _.map(shipmentHistory, "state")
      const isPickedUp = _.includes(shipmentStateHistory, "picked_up")
      const isExchangePickedUp = _.includes(shipmentStateHistory, "exchange_picked_up")
      const isDelivered = _.includes(shipmentStateHistory, "delivered")
      const isReturnShipment = gearLastShipment.shipment_type === "return"
      const isCanceled = gearLastShipment.state === "canceled"
      if (isReturnShipment) {
        if (isPickedUp) {
          data["estimatedTime"] = "Package has been PickedUp"
        } else {
          const pickupDate = TimeUtil.formatDateInTimeZoneDateFns(tz, new Date(gearLastShipment.customer_edd), "do MMMM")
          data["estimatedTime"] = `Will be PickedUp by ${pickupDate}`
        }
      } else {
        if (isDelivered || isExchangePickedUp) {
          const deliveredDate = new Date((_.last(shipmentHistory) as any).time)
          deliveredDate.setDate(deliveredDate.getDate() + 30)
          const returnableDate = TimeUtil.formatDateInTimeZoneDateFns(tz, deliveredDate, "do MMMM")
          data["estimatedTime"] = `Returnable till ${returnableDate}`
        } else {
          const date = TimeUtil.formatDateInTimeZoneDateFns(tz, new Date(gearLastShipment.customer_edd), "do MMMM")
          data["estimatedTime"] = `Delivery by ${date}`
        }
      }
      if (isReturnShipment || isDelivered || isExchangePickedUp || isCanceled) {
        return null
      }
    }
    this.data = data
    return this
  }

  @IsIn(PRODUCT_BROWSE_TYPES)
  productType: ProductType

  @IsIn(LAYOUT_TYPES)
  layoutType: LAYOUT_TYPE

  widgetType: WidgetType = "RECENT_GEAR_ORDER_WIDGET"
  data: {}
  action: Action
}
