import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { Action } from "@curefit/vm-models"
import { UrlPathBuilder } from "@curefit/product-common"
import * as _ from "lodash"

export class RecipePackWidget extends BaseWidget {

    recipeId: string
    image: string
    title: string
    isVeg: boolean
    action: Action
    preparationTime: number
    description: string
    icon: string
    analyticsData: any
    bookmarkMeta?: {
        status: boolean,
        action: Action
    }
    isLocked: boolean
    isVideoCard: boolean
    onImageText: string

    constructor(image: string, title: string, isVeg: boolean, action: Action, preparationTime: number, id: string, isBookMarked: boolean, isLocked: boolean, description?: string, isVideoCard?: boolean, onImageText?: string, icon?: string) {
        super("RECIPE_PACK_WIDGET")
        this.recipeId = id
        this.title = title
        this.image = UrlPathBuilder.prefixSlash(image)
        this.isVeg = isVeg
        this.action = action
        this.preparationTime = preparationTime
        const mins = Math.floor(preparationTime / 60)
        this.bookmarkMeta = !_.isNil(isBookMarked) ? {
            status: isBookMarked, // current bookmark status of the recipe
            action: {
                actionType: isBookMarked ? "UNBOOKMARK_RECIPE" : "BOOKMARK_RECIPE",
                meta: {
                    recipeId: id,
                    isBookMarked: !isBookMarked, // tells the client what the user wants to do
                }
            }
        } : undefined
        if (mins < 2 ) {
            this.description = "Takes " + mins + " Min"
        }
        this.description = description ? description : "Takes " + mins + " Mins"
        this.icon = !_.isNil(icon) ? icon : (!_.isNil(isVeg) ? (isVeg ? "VEG" : "NON_VEG") : undefined)
        this.analyticsData = {
            eventName: "RECIPE_CLICK_EVENT",
            meta: {
                id: id,
                title: title,
                isVeg: isVeg
            }
        }
        this.isLocked = false
        this.isVideoCard = !!isVideoCard
        this.onImageText = onImageText
    }

    async buildView(interfaces?: IServiceInterfaces, userContext?: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }
}
