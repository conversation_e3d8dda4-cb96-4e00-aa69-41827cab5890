import { BaseWidget, IBaseWidget, IServiceInterfaces, UserContext } from "@curefit/vm-models"
import * as _ from "lodash"

export class TitleWidgetBuilder extends BaseWidget {
    title: string
    constructor() {
        super("EAT_TITLE_WIDGET")
    }
    async buildView(interfaces: IServiceInterfaces, userContext: UserContext, queryParams: { [p: string]: string }): Promise<IBaseWidget | IBaseWidget[]> {
        const pageId = _.get(queryParams, "pageId", undefined)

        switch (pageId) {
            case "eatrecipe":
            case "eatliveweb":
            case "eatliverecipe":
            case "eatlive":
                this.title = String(_.get(queryParams, "filters.bookmarkedOnly")) === "false" ? "Browse All Recipes" : "Bookmarked Recipes"
                break
            default:
                this.title = undefined
        }
        if (_.isNil(this.title)) {
            interfaces.logger.error(`Title widget broke for pageId ${pageId}`)
            return undefined
        }
        return this
    }
}