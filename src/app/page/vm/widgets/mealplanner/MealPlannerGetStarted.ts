// import { BaseWidget, IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"
import { Action } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { BaseWidget, IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"

export default class MealPlannerGetStarted extends BaseWidget {

  action: Action
  title: string
  desc: string

  constructor() {
    super("MEAL_PLANNER_GET_STARTED")
  }

  async buildView(interfaces: IServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
    const doesPlanExist = await interfaces.gmfService.checkExistsUserPlan(userContext.userProfile.userId)
    interfaces.logger.info("[MEAL PLANNER] before")
    if (doesPlanExist) {
      return null
    }
    interfaces.logger.info("[MEAL PLANNER] after")
    this.title = "Get your Personalised Meal Plan"
    this.desc = "Tell us a little about yourself and we will recommend a tailor-made meal plan for you."
    this.action = {
      actionType: "NAVIGATION",
      url: "curefit://mealplannerlandingpage"
    }
    return this
  }
}
