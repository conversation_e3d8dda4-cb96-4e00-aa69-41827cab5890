// import { BaseWidget, IBaseWidget, IServiceInterfaces } from "@curefit/vm-models"
import { Action } from "@curefit/vm-models"
import { UserContext } from "@curefit/userinfo-common"
import * as _ from "lodash"
import { BaseWidget, IBaseWidget } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../../ServiceInterfaces"

export default class MealPlannerUpcoming extends BaseWidget {

  action: Action
  products: any

  constructor() {
    super("MEAL_PLANNER_UPCOMING")
  }

  async buildView(interfaces: CFServiceInterfaces, userContext: UserContext): Promise<IBaseWidget> {
    const doesPlanExist = await interfaces.gmfService.checkExistsUserPlan(userContext.userProfile.userId)
    if (!doesPlanExist) {
      return null
    }
    this.products = await interfaces.mealPlannerBusiness.getProducts(userContext)
    this.action = {
      actionType: "NAVIGATION",
      url: "curefit://mealplannerlandingpage"
    }
    return this
  }
}
