import { PackOffersResponse } from "@curefit/offer-service-client"
import { WidgetView } from "./../../../common/views/WidgetView"
import * as _ from "lodash"
import { BaseWidget, CareWidgetUtil, MyDoctorsWidget, UserContext } from "@curefit/vm-models"
import { Action } from "@curefit/apps-common"
import { Center, DOCTOR_TYPE, DiagnosticProductResponse, Doctor } from "@curefit/care-common"
import { MyDoctor, ConsultationProductResponse } from "@curefit/care-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import CareUtil from "../../../util/CareUtil"
import AppUtil from "../../../util/AppUtil"
import { CareTeam, Patient } from "@curefit/albus-client"
import { IDoctorAvailableSlot } from "../../../care/CareDoctorSearchPageView"
import { OfferUtil, RUPEE_SYMBOL } from "@curefit/base-utils"
import * as momentTz from "moment-timezone"

interface DoctorWithAction {
  doctor: MyDoctor
  action: Action
  slotAction?: Action
  patientInfo?: string
  productInfo?: {
    title: string
    subTitle: string
  }
}

export class MyDoctorsWidgetView extends MyDoctorsWidget {
  doctors: DoctorWithAction[]
  doctorTypes: DOCTOR_TYPE[]

  async buildView(
    interfaces: CFServiceInterfaces,
    userContext: UserContext
  ): Promise<any> {
    const patientList = await CareUtil.getPatientListFromUserProfile(interfaces.healthfaceService, userContext.userProfile)
    if (_.isEmpty(patientList)) {
      return undefined
    }
    const isMindTherapistDoctorType = !_.isEmpty(this.doctorTypes) && this.doctorTypes.includes("MIND_THERAPIST")
    if (isMindTherapistDoctorType && AppUtil.isTherapistRecommendationV2UINotSupported(userContext)) {
      const { preferredTherapist, selfPatientId } = await this.getMindTherapistPreferredDoctor(interfaces, userContext, patientList, "MIND_THERAPIST")
      if (!_.isEmpty(preferredTherapist)) {
        return CareUtil.getPreferredDoctorBookingWidget(userContext, preferredTherapist, selfPatientId)
      }
      return undefined
    } else {
      let doctors: any[] = []
      if (isMindTherapistDoctorType) {
        this.doctors = await Promise.all(this.doctorTypes.map(async (doctorType: string, index: number) => {
          const { preferredTherapist, selfPatientId  } = await this.getMindTherapistPreferredDoctor(interfaces, userContext, patientList, doctorType, true)
          const patientId = selfPatientId
          if (!_.isEmpty(preferredTherapist)) {
            if (!_.isEmpty(preferredTherapist.consultationSellableProductUserProductInfos)) {
              if (preferredTherapist?.doctor && !preferredTherapist?.doctor?.slugValue && AppUtil.isWeb(userContext)) {
                const doctorValue = await interfaces.healthfaceService.getDoctorSlugMappingById(preferredTherapist?.doctor.id) as Doctor
                preferredTherapist.doctor.slugValue = doctorValue?.seoSlugValue
              }
              return await this.getDoctorItem(userContext, interfaces, preferredTherapist, index, isMindTherapistDoctorType, patientId, _.get(preferredTherapist, "consultationSellableProductUserProductInfos.0.userMembershipInfos.0.bookingId"), patientList)
            }
          }
          return undefined
        }))
        this.doctors = this.doctors.filter(Boolean)
      } else {
        doctors = await interfaces.healthfaceService.getPreferredDoctors(
          userContext.userProfile.userId,
          undefined,
          !_.isEmpty(this.doctorTypes) ? this.doctorTypes : undefined
        )
        this.doctors = (await Promise.all(doctors.map(async (doctorItem: MyDoctor | CareTeam, index: number) => {
          return await this.getDoctorItem(userContext, interfaces, doctorItem, index, isMindTherapistDoctorType, undefined, undefined)
        }))).filter(Boolean)
      }
      if (!_.isEmpty(this.doctors)) {
        return this
      }
      return undefined
    }
  }

  async getDoctorItem(userContext: UserContext, interfaces: CFServiceInterfaces, doctorItem: MyDoctor | CareTeam, index: number, isMindTherapistDoctorType: boolean, patientId: number, parentBookingId?: number, patientsList?: Patient[]) {
    const { productId, doctorType, finalProduct } = this.getOnlineConsultationProductInfo(isMindTherapistDoctorType, doctorItem)
        if (productId) {
          const doctor: any = isMindTherapistDoctorType ? (doctorItem as CareTeam).doctor : doctorItem
          const doctorWithAction: any = doctor
          doctorWithAction.speciality = _.get(doctor, "myDoctorType.displayValue")

          // Overriding widget to send offer price
          if (isMindTherapistDoctorType && AppUtil.isFromFlutterAppFlow(userContext)) {
              const consultationProduct = CareUtil.toConsultationProduct(finalProduct as DiagnosticProductResponse)
              const offers: PackOffersResponse = await CareWidgetUtil.getCareProductOffersFromUserContext(userContext, "CONSULTATION", [consultationProduct.productId], AppUtil.callSourceFromContext(userContext), interfaces, false)
              const doctorCardFooterAction =  CareUtil.getDoctorFooterCardAction(userContext, consultationProduct, patientsList, {
                patientId,
                parentBookingId,
                productId: productId,
                isDoctorSearch : true,
            })
            let doctorAvailableSlots: IDoctorAvailableSlot[] = []
            if (!_.isEmpty(doctor?.earliestAvailabilityList)) {
                const centerId = _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "earliestAvailabilityList[0].centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined)))
                doctorAvailableSlots = CareUtil.getDoctorEarliestAvailabilitySlotAction(userContext, doctor, consultationProduct.productId, patientId, parentBookingId, consultationProduct, Number(centerId), false, true)
                const slotText = momentTz.tz(momentTz.tz(doctor?.earliestAvailability?.startTime || doctor?.earliestAvailabilityList?.[0]?.startTime, userContext.userProfile.timezone), userContext.userProfile.timezone).calendar(null, {
                  lastDay: "[]",
                  sameDay: "[]",
                  nextDay: "[Available Tomorrow]",
                  lastWeek: "MMM DD",
                  nextWeek: "MMM DD",
                  sameElse: "MMM DD",
              })
                doctorWithAction.slotAction = {
                  title: slotText,
                  slotInfos: doctorAvailableSlots,
                  slotNextAction: doctorCardFooterAction,
                  actionQueryParam: `&doctorId=${doctor.id}&centerId=${centerId}`,
                }
            }
            if (patientId && !_.isEmpty(patientsList)) {
              const patient: Patient = patientsList.find(patient => patient.id === patientId)
              if (patient) {
                doctorWithAction.patientInfo = patient?.name
              }
            }
            let subTitle = (consultationProduct.duration / 60000) + " mins"
            if (offers && !parentBookingId) {
              const offerDetails = OfferUtil.getPackOfferAndPrice(consultationProduct, offers)
              subTitle += offerDetails?.price?.listingPrice > 0 ? " • starting " + RUPEE_SYMBOL + offerDetails?.price?.listingPrice : ""
            }
            doctorWithAction.productInfo = {
              title: consultationProduct.title,
              subTitle
            }
          }

          doctorWithAction.action = {
            ...CareUtil.getDoctorBookingScreenAction("Consult", {
              doctorId: doctor.id,
              slugValue: doctor.seoSlugValue || doctor.slugValue,
              centerId: _.get(doctor, "earliestAvailability.centerId", _.get(doctor, "doctorCenterMapping[0].centerId", undefined)), // will cause discrepancy in centerid between listing page and clp widget for doctors mapped to multiple centers as earliestAvailibility is not there for getPreferredDoctors api
              productId,
              isDoctorSearch: "true",
              patientId,
              parentBookingId,
              doctorType: doctorType || doctor?.primarySubServiceType?.code,
              appsource: userContext.sessionInfo.appSource
            }, userContext),
            analyticsData: {
              eventKey: "widget_click",
              eventData: {
                  actionType: "my_doctor_widget_item_click",
                  position: index,
                  doctorId: doctor.id
              }
            },
          }
          return {
            id: doctorWithAction?.id,
            name: doctorWithAction?.name,
            qualification: doctorWithAction?.qualification,
            displayImage: doctorWithAction?.displayImage,
            speciality: doctorWithAction?.speciality,
            action: doctorWithAction.action,
            productInfo: doctorWithAction?.productInfo,
            patientInfo: doctorWithAction?.patientInfo,
            slotAction: doctorWithAction?.slotAction
          } as any
        }
        return null
  }

  async getMindTherapistPreferredDoctor(
    interfaces: CFServiceInterfaces,
    userContext: UserContext,
    patientList: Patient[],
    doctorType: string,
    isEarliestAvailabilityRequired?: boolean
  ) {
    const userId = userContext.userProfile.userId
    const selfPatient = !_.isEmpty(patientList) ? _.find(patientList, patient => patient.relationship === "Self") : undefined
    if (selfPatient) {
      const preferredTherapists = await interfaces.healthfaceService.getPreferredDoctorForDoctorType(userId, selfPatient.id, doctorType, isEarliestAvailabilityRequired)
      if (!_.isEmpty(preferredTherapists)) {
        if (!_.isEmpty(preferredTherapists[0]?.earliestAvailabilities)) {
          preferredTherapists[0].doctor.earliestAvailabilityList = preferredTherapists[0]?.earliestAvailabilities
        }
        return {preferredTherapist: preferredTherapists[0], selfPatientId: selfPatient.id}
      }
    }
    return {preferredTherapist: undefined, selfPatientId: undefined}
  }

  getOnlineConsultationProductInfo(isMindTherapistDoctorType: boolean, doctor: MyDoctor| CareTeam) {
    let onlineProductCode, doctorType, finalProduct
    if (isMindTherapistDoctorType) {
      doctor = doctor as CareTeam
      if (!_.isEmpty(doctor.consultationSellableProductUserProductInfos)) {
        const products = <DiagnosticProductResponse[]>doctor.consultationSellableProductUserProductInfos.map(
          (product) => product.baseSellableProduct
        )
        const finalOnlineProduct = products.filter(product => {
          return (product?.consultationProduct as any)?.productStatus === "LIVE" && (doctor as CareTeam)?.doctorTypeCode?.code === (product?.consultationProduct as any)?.doctorType
        }).find(product => {
            return product.consultationProduct.consultationMode === "ONLINE"
          }
        )
        finalProduct = finalOnlineProduct
        onlineProductCode = finalOnlineProduct && finalOnlineProduct.productCode
        doctorType = finalOnlineProduct?.consultationProduct?.doctorType
      }
    } else {
      doctor = doctor as MyDoctor
      let product
      if (doctor.consultationProducts) {
        product = doctor.consultationProducts.find(
          (product: ConsultationProductResponse) => {
            return product.consultationMode === "ONLINE"
          }
        )
      }
      finalProduct = product
      onlineProductCode = product && product.productCode
      doctorType = product?.doctorType
    }
    return { productId: onlineProductCode, doctorType, finalProduct }
  }
}
