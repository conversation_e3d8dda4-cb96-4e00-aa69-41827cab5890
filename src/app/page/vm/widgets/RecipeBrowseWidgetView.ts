import * as _ from "lodash"
import {
    BaseWidget,
    IBaseWidget,
    IServiceInterfaces,
    UserContext
} from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { ActionUtil } from "@curefit/base-utils"
import { RecipeBrowseWidget } from "@curefit/vm-models"
import { SortOrder } from "@curefit/mongo-utils"
import { RecipePackWidget } from "./RecipePackWidget"
import { Action } from "../../../common/views/WidgetView"
import { DIYCategory, DIYRecipeView } from "@curefit/diy-common"
import { injectable } from "inversify"
import AppUtil from "../../../util/AppUtil"
import LiveUtil, { RECIPE_PRODUCT_TYPE } from "../../../util/LiveUtil"
import { LivePackUtil } from "../../../util/LivePackUtil"

@injectable()
export class RecipeBrowseWidgetView extends RecipeBrowseWidget {
    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<IBaseWidget | IBaseWidget[]> {

        const countryId = AppUtil.getCountryId(userContext)
        const recipeCategories = await interfaces.diyService.getRecipeCategories(countryId)

        let start: number
        let count: number
        let categoryId: string
        let vegOnly: boolean
        let bookmarkedOnly: boolean = false
        let passedTabId: string
        const userId = userContext.userProfile.userId
        const appVersion = userContext.sessionInfo.appVersion

        let offset = 0
        if (_.get(queryParams, "filters")) {
            const pageNumber = !_.isNil(queryParams.pageNumber) ? Number(queryParams.pageNumber) : 0
            count = !_.isNil(queryParams.pageSize) ? Number(queryParams.pageSize) : 10
            start = pageNumber * count
            bookmarkedOnly = String(_.get(queryParams, "filters.bookmarkedOnly", false)) === "true"
            categoryId = _.get(queryParams, "filters.categoryId", "recipes_all")
            vegOnly = String(_.get(queryParams, "filters.vegOnly", false)) === "true"
            passedTabId = categoryId // this is the tabId for the selected tab
        } else {
            start = (queryParams.start !== undefined && queryParams.start !== "undefined") ? Number(queryParams.start) : 0
            count = (queryParams.count !== undefined && queryParams.count !== "undefined") ? Number(queryParams.count) : 10
            offset = 1
            categoryId = (queryParams.categoryId !== undefined && queryParams.categoryId !== "undefined") ? queryParams.categoryId : "recipes_all"
            vegOnly = (queryParams.vegOnly === "true")
        }

        const filteredCategories = recipeCategories.filter(recipe => {
            return recipe.status === "LIVE"
        })
        filteredCategories.sort((recipe1: DIYCategory, recipe2: DIYCategory) => {
            const recipe1Priority = recipe1.sortInfo ? recipe1.sortInfo.priority : 0
            const recipe2Priority = recipe2.sortInfo ? recipe2.sortInfo.priority : 0
            const diff = recipe1Priority - recipe2Priority
            return diff > 0 ? -1 : diff < 0 ? 1 : diff
        })
        const ALL_CATEGORY_ID = "recipes_all"
        const tabs = []
        tabs.push({
            id: ALL_CATEGORY_ID,
            title: "All",
            action: {
                actionType: "RECIPE_FILTER",
                categoryId: ALL_CATEGORY_ID,
                start: 0,
                count: count
            },
            analyticsData: {
                eventName: "RECIPE_CATEGORY",
                meta: {
                    tabId: ALL_CATEGORY_ID,
                    title: "All"
                }
            }
        })
        for (let i = 0; i < filteredCategories.length; i++) {
            const category = filteredCategories[i]
            tabs.push({
                title: category.name,
                id: (<any>category)._id,
                action: {
                    actionType: "RECIPE_FILTER",
                    categoryId: (<any>category)._id,
                    start: 0,
                    count: count
                },
                analyticsData: {
                    eventName: "RECIPE_CATEGORY",
                    meta: {
                        tabId: (<any>category)._id,
                        title: category.name
                    }
                }
            })
        }
        const selectedTabId = !_.isEmpty(queryParams.selectedTabId) ? queryParams.selectedTabId : (
            passedTabId ? passedTabId : ALL_CATEGORY_ID
        )

        const widgets: IBaseWidget[] = []
        if (AppUtil.isNewRecipeFilterWidgetSupported(userContext)) {
            if (!await AppUtil.isRecipeFilterV2WidgetSupported(userContext)) {
                widgets.push(new RecipeFilterContainerWidget(bookmarkedOnly, vegOnly))
            }
            if (!bookmarkedOnly) {
                if (AppUtil.HorizontalTabWidgetSupported(userContext)) {
                    widgets.push(new HorizontalTabWidget(tabs, selectedTabId))
                }
            }
        } else {
            if (queryParams.successiveFetch !== "true") {
                widgets.push(new SwitchWidgetV2(vegOnly))
                if (AppUtil.HorizontalTabWidgetSupported(userContext)) {
                    widgets.push(new HorizontalTabWidget(tabs, selectedTabId))
                }
            }
        }
        const tenant = AppUtil.getTenantFromUserContext(userContext)
        let recipeItems: DIYRecipeView[]
        // querying one extra item to identify if we should make next query
        if (bookmarkedOnly) {
            recipeItems = await interfaces.diyService.getAllSubscribedRecipesForUser(userId, tenant, start, count + offset)
        } else {
            if (categoryId === "recipes_all") {
                recipeItems = await interfaces.diyService.getAllRecipes(vegOnly, tenant, start, count + offset, "productId", SortOrder.DESC, userId, countryId)
            } else {
                recipeItems = await interfaces.diyService.getRecipesForCategory(categoryId, vegOnly, tenant, start, count + offset, "productId", SortOrder.DESC, userId, countryId)
            }
        }
        let nextQueryEnabled: boolean = true
        if (recipeItems.length <= count) {
            nextQueryEnabled = false
        }
        if (recipeItems.length === count + 1) {
            recipeItems = recipeItems.splice(0, count)
        }

        if (recipeItems.length === 0) {
            widgets.push({ message: "No recipes to show.", widgetType: "NO_DATA_MESSAGE_WIDGET" } as unknown as BaseWidget)
            return widgets
        }
        const { isUserEligibleForMonetisation, isUserEligibleForTrial } = await LivePackUtil.checkIfMonetisationEnabledAndEligibleForTrial(interfaces.cultBusiness, interfaces.diyService, userContext)
        for (let i = 0; i < recipeItems.length; i = i + 2) {
            const isRecipe1Bookmarked = bookmarkedOnly ? true : _.get(recipeItems[i], "userMeta.isSubscribed", false)
            const recipeItemAction1 = LiveUtil.getDIYRecipeAction(recipeItems[i], userContext.sessionInfo.userAgent)
            const isLocked1 = recipeItems[i].locked && isUserEligibleForMonetisation
            const diyAction1 = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked1, recipeItemAction1, isUserEligibleForTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
            const magazine1 = new RecipePackWidget(recipeItems[i].imageDetails.thumbnailImage, recipeItems[i].title, recipeItems[i].isVeg, diyAction1,
                recipeItems[i].preparationTime, recipeItems[i].id, isRecipe1Bookmarked, isLocked1)
            let magazine2
            const isRecipe2Bookamrked = bookmarkedOnly ? true : _.get(recipeItems[i + 1], "userMeta.isSubscribed", false)
            if (recipeItems[i + 1] !== undefined) {
                const recipeItemAction2 = LiveUtil.getDIYRecipeAction(recipeItems[i + 1], userContext.sessionInfo.userAgent)
                const isLocked2 = recipeItems[i + 1].locked && isUserEligibleForMonetisation
                const diyAction2 = LiveUtil.getDIYSessionAction(userContext, isUserEligibleForMonetisation, isLocked2, recipeItemAction2, isUserEligibleForTrial, RECIPE_PRODUCT_TYPE, "recipe_pack_widget", "", false)
                magazine2 = new RecipePackWidget(recipeItems[i + 1].imageDetails.thumbnailImage, recipeItems[i + 1].title, recipeItems[i + 1].isVeg, diyAction2, recipeItems[i + 1].preparationTime, recipeItems[i + 1].id, isRecipe2Bookamrked, isLocked2)
            }
            widgets.push(new BrowseDoubleMagazineWidget(magazine1, magazine2, start + count, count, nextQueryEnabled))
        }
        return widgets
    }
}

export class BrowseDoubleMagazineWidget extends BaseWidget {

    items: any[]
    nextQuery?: {
        start: number
        count: number
    }

    constructor(magazine1: IBaseWidget, magazine2: IBaseWidget, start: number, count: number, nextQueryEnabled: boolean) {
        super("BROWSE_DOUBLE_MAGAZINE_WIDGET")
        this.items = [magazine1]
        if (!_.isNil(magazine2)) {
            this.items.push(magazine2)
        }
        if (nextQueryEnabled) {
            this.nextQuery = {
                start: start,
                count: count
            }
        }
    }

    async buildView(interfaces?: IServiceInterfaces, userContext?: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class SwitchWidgetV2 extends BaseWidget {

    title: string
    isEnabled: boolean
    action: Action
    analyticsData: any

    constructor(isEnabled: boolean) {
        super("SWITCH_WIDGET_V2")
        this.title = "Veg only"
        this.isEnabled = isEnabled
        this.action = {
            actionType: "RECIPE_FILTER",
            isEnabled: true
        }
        this.analyticsData = {
            eventName: "recipe_veg_filter_toggle",
            enabledText: "veg_only",
            disableText: "all"
        }
    }

    async buildView(interfaces?: IServiceInterfaces, userContext?: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class HorizontalTabWidget extends BaseWidget {

    selectedTabId: string
    tabs: any[]

    constructor(tabs: any[], selectedTabId: string) {
        super("HORIZONTAL_TAB_WIDGET")
        this.tabs = tabs
        this.selectedTabId = selectedTabId
    }

    async buildView(interfaces?: IServiceInterfaces, userContext?: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }
}

export class RecipeFilterContainerWidget extends BaseWidget {
    widgets: any[]
    constructor(bookMarkedOnly: boolean = false, vegOnly: boolean = false) {
        super("RECIPE_FILTER_CONTAINER_WIDGET")
        const filters: {
            type: string,
            meta?: any
        }[] = [
                {
                    type: "BOOKMARKED",
                    meta: {
                        isEnabled: bookMarkedOnly
                    }
                }
            ]
        if (!bookMarkedOnly) {
            filters.unshift({
                type: "VEG",
                meta: {
                    isEnabled: vegOnly,
                    title: "VEG",
                }
            })
        }
        this.widgets = [
            {
                widgetType: "EAT_TITLE_WIDGET",
                title: bookMarkedOnly ? "BOOKMARKED RECIPES" : "BROWSE RECIPE"
            },
            {
                widgetType: "RECIPE_FILTER_WIDGET",
                filters: filters
            }
        ]
    }

    async buildView(interfaces?: IServiceInterfaces, userContext?: UserContext, queryParams?: { [filterName: string]: string }): Promise<IBaseWidget> {
        return this
    }
}
