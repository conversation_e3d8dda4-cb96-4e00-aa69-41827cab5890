import * as _ from "lodash"
import { IBaseWidget, UserContext } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { ListWidgetView } from "@curefit/vm-models"
import { WhySubscribeCardWidget } from "@curefit/vm-models"
import { TitleWidget } from "@curefit/vm-models"

export class EatWhySubscribeWidget extends ListWidgetView {

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        this.headerWidget = new TitleWidget("Why Subscribe")
        const items = interfaces.mealPackPageConfig.whySubscribeItems
        this.widgets = await Promise.all(_.map(items, async (item) => {
            return new WhySubscribeCardWidget(item.title, item.image).buildView(interfaces, userContext, queryParams)
        }))
        return this
    }

}
