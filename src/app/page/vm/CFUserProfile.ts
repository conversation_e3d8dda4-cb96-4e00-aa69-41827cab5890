import { UserProfile } from "@curefit/userinfo-common"
import { CultSummary, UserTrialEligibility } from "@curefit/cult-common"
import { UserGoalsSummary } from "@curefit/gmf-common"
import { UserMapping } from "@curefit/issue-common"
import { UserSegmentDetail } from "@curefit/gmf-common"
import {
    SavingsSummaryResponse
} from "@curefit/fitclub-models"
import { TimelineResponseV2 } from "@curefit/albus-client"
import { ProductType } from "@curefit/product-common"
import { WalletBalance } from "@curefit/fitcash-common"
import { DeliveryArea } from "@curefit/eat-common"
import { UserMembershipsAndTrialUsages } from "@curefit/gymfit-common"
import { UserAssignment } from "@curefit/hamlet-common"
import { UserForm } from "@curefit/cfs-common"
import { CultProductPricesResponse, FoodMarketplaceApplicableCartOffersResponse } from "@curefit/offer-common"

export interface CFUserProfile extends UserProfile {
    cultMindSummary?: Promise<CultSummary>
    cultMindSummaryMap?: Promise<{ [userId: string]: CultSummary }>
    gymfitMembershipPromise?: Promise<UserMembershipsAndTrialUsages>
    userPlanExistsPromise?: Promise<any>
    userSegmentDetailPromise?: Promise<UserSegmentDetail>
    userPlanSummaryPrmoise?: Promise<UserGoalsSummary>
    userMappedSegments?: Promise<UserMapping>
    fitclubMembershipPromise?: Promise<boolean>
    fitClubSavingsPromise?: Promise<SavingsSummaryResponse>
    careTimelinePromise?: Promise<TimelineResponseV2>
    ptTimelinePromise?: Promise<TimelineResponseV2>
    dIYProductFulfilmentInfo?: Promise<{ productType: ProductType, contentConsumedCount: number }[]>
    walletBalancePromise?: Promise<WalletBalance>
    availableDeliveryAreasPromise?: Promise<DeliveryArea[]>
    cultsportSegmentsMetaData?: {
        articleTypes?: string[];
        collectionSlugs?: string[];
        brands?: string[];
        categories?: string[];
        productIds?: string[];
    }

    /**
     * @deprecated - do not evaluate for all experiments
     */
    hamletUserExperimentPromise?: Promise<{
        assignmentsMap: { [experimentId: string]: UserAssignment }
    }>
    cultUserTrialEligibilityPromise?: Promise<UserTrialEligibility>
    doesLivePTRecommendationExistPromise?: Promise<boolean>
    livePTOnboardingCompletePromise?: Promise<{
        isOnboarded: boolean
    }>
    curefitLiveOffersPromise?: Promise<{
        offerIds: string[];
    }>

    getCultProductPrices?: (productIds: string[]) => Promise<CultProductPricesResponse>

    foodMarketplaceCartOffersPromise?: Promise<FoodMarketplaceApplicableCartOffersResponse>
}
