import { Logger, BASE_TYPES } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import * as express from "express"
import { inject, injectable } from "inversify"
import * as _ from "lodash"
import {
    IBaseWidget, ISegmentService,
    SessionInfo,
    UserContext,
    UserProfile,
    WidgetWithMetric
} from "@curefit/vm-models"
import {
    BaseOfferRequestParams,
    BulkApiOfferType,
    BulkOfferAPIRequest,
    CareOfferRequestParams,
    EatOfferRequestParams,
    FoodPackOffersResponseV2,
    FoodSinglePriceOfferResponse
} from "@curefit/offer-common"
import {
    CustomSheetProps,
    IFeedback,
    IFeedbackView,
    Page as VMPage,
    PageIcon,
    PageMetric,
    PageOverride,
    PageType,
    Tab
} from "@curefit/vm-common"
import { Action } from "@curefit/apps-common"
import { Segment, ImageCarouselAnnouncement, NewFeatureAnnouncement, ImagePopupAnnouncement, PolicyChangeAnnouncement } from "@curefit/vm-common"
import IUserBusiness from "../../user/IUserBusiness"
import EatUtil from "../../util/EatUtil"
import { CFServiceInterfaces } from "./ServiceInterfaces"
import { CFUserProfile } from "./CFUserProfile"
import { EatClpHook } from "../pageHooks/EatClpHook"
import {
    CACHE_TYPES, IAppVersionReadWriteCache,
    IEatReadWriteCache
} from "@curefit/cache-utils"
import { ErrorFactory } from "@curefit/error-client"
import { IPageService } from "@curefit/vm-models"
import { CacheHelper } from "../../util/CacheHelper"
import { UserGoalSearchParams } from "@curefit/gmf-common"
import AppUtil from "../../util/AppUtil"
import { EatNowHook } from "../pageHooks/EatNowHook"
import { FitclubMembershipHook } from "../pageHooks/FitclubMembershipHook"
import { FitClubActiveMembershipHook } from "../pageHooks/FitClubActiveMembershipHook"
import { EatLaterHook } from "../pageHooks/EatLaterHook"
import { City, LatLong } from "@curefit/location-common"
import { IWidgetBuilder } from "@curefit/vm-models"
import { DropoutHIWPageHook } from "../pageHooks/DropoutHIWPageHook"
import { Action as WidgetAction } from "../../common/views/WidgetView"
import { ListingBrandIdType, MenuType } from "@curefit/eat-common"
import { OrderNowHook } from "../pageHooks/OrderNowHook"
import { WholeFitHook } from "../pageHooks/WholeFitHook"
import { FitclubMembershipV2Hook } from "../pageHooks/FitclubMembershipV2Hook"
import { AnnouncementBusiness } from "../../announcement/AnnouncementBusiness"
import { PromiseCache } from "../../util/VMUtil"
import { EatOfferRequestParamsV3 } from "@curefit/offer-common/src/Offer"
import { ALL_MEAL_SLOTS } from "@curefit/eat"
import { PageSelector, AreaPageSelector, CityPageSelector } from "../Page"
import { WholefitV2Hook } from "../pageHooks/WholefitV2Hook"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { SlotUtil } from "@curefit/eat-util"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { DoctorRecommendationRequest } from "@curefit/albus-client"
import { ErrorCodes } from "../../error/ErrorCodes"
import { LIVE_PT_DOCTOR_TYPES } from "@curefit/care-common"
import { OFFER_SERVICE_CLIENT_TYPES, OfferServiceV3 } from "@curefit/offer-service-client"
import { UserInfo } from "@curefit/user-common"
import CareUtil, { LIVE_PT_SNC_PRODUCT_ID, LIVE_SGT_SNC_PRODUCT_ID } from "../../util/CareUtil"
import GymfitUtil from "../../util/GymfitUtil"
import { ActionUtil, OfferUtil } from "@curefit/base-utils"
import FoodMarketplaceUtil from "../../util/FoodMarketplaceUtil"
import { Tenant } from "@curefit/user-common/dist/src/Tenants"
import { WellnessClpHook } from "../pageHooks/WellnessClpHook"
import { FitnesshubPageHook } from "../pageHooks/FitnesshubPageHook"
import EnterpriseUtil from "../../util/EnterpriseUtil"
import { CoachMark, CoachMarkConfig } from "../CoachMarkConfig"
import { eternalPromise } from "@curefit/util-common"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService, ICatalogueServicePMS } from "@curefit/catalog-client"
import { ProgramPageHook } from "../pageHooks/ProgramPageHook"
import { CoachProgramPageHook } from "../pageHooks/CoachProgramPageHook"
import { HamletConfigRequest } from "@curefit/hamlet-common"
import { SportshubPageHook } from "../pageHooks/SportshubPageHook"
import CatalogueServiceUtilities from "../../util/CatalogueServiceUtilities"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"
import OffersUtil from "../../util/OffersUtil"

export interface Section {
    id: string
    name: string
    priority: number
    pageMetric: PageMetric
    superScript?: string
}

export interface Page {
    pageType: PageType
    name: string
    icon?: PageIcon
    rightBarButton?: Action
    searchBarButton?: Action
    floatingButtonAction?: Action
    initAction?: Action
    pageSelector?: PageSelector
    pageMetric: PageMetric
    actions?: Action[] | WidgetAction[]
    pageData?: any
    announcementData?: ImageCarouselAnnouncement | NewFeatureAnnouncement | ImagePopupAnnouncement | PolicyChangeAnnouncement
    coachMark?: CoachMark
    pageId?: string
    feedback?: IFeedback[]
    showMedicalRecordToolTip?: boolean
    diagnosticCart?: any
    showToolTip?: boolean
    theme?: string
    auroraTheme?: string
    backgroundAnimation?: string
    userPreferredLatLong?: any
    corpLogo?: string
    corpName?: string
    webTheme?: string
    actionLayout?: string
    customSheetProps?: CustomSheetProps
}

export interface ListPage extends Page {
    header?: WidgetWithMetric[]
    footer?: WidgetWithMetric[]
    body: WidgetWithMetric[]
    pageLoadActions?: Action[] | WidgetAction[]
}

export interface TabPage extends Page {
    selectedTabIndex: number
    pulsatingDotIndex?: number
    pulsatingDotVersion?: number
    sections: Section[]
    lists: {
        [key: string]: {
            pageMetric: PageMetric,
            header?: IBaseWidget[],
            footer?: IBaseWidget[],
            body: IBaseWidget[],
            actions: Action[]
            pageLoadActions: Action[] | WidgetAction[],
            searchBarButton?: Action
            floatingButtonAction?: Action
        }
    },
    feedback?: IFeedbackView[]
}

export interface JavaWidgetResponse {
    widgets: WidgetWithMetric[]
}

export interface IPageHook {
    pageId: string

    preparePage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, serviceInterfaces: CFServiceInterfaces): Promise<VMPage>

    callPageHook(serviceInterfaces: CFServiceInterfaces, pageView: Promise<Page>, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any): Promise<Page>
}

export const pageIdHookMap = new Map<string, IPageHook>()
export const VM_PAGE_LOCATION_AWARE = ["foodmpordernow", "eatclp", "eatlater", "eatnow", "eatsubscribe", "fitstart", "sale", "home", "independenceday", "hometab", "eatordernow", "wholefit", "eatmarketplace", "eatfclistingpage", "wholefitclp", "wholefitv2", "eatproductcollectionpage", "storeclp", "wellnessclp", "wellnesslist", "wellness_hub", "foodmpoutletlist"]
export const WHOLE_FIT_SUPPORTED_PAGE_IDS = ["eatclp", "eatordernow", "wholefit", "hometab", "wholefitclp", "wholefitv2", "eatsubscribe", "eatmarketplace", "eatlater", "eatfclistingpage", "storeclp", "wellnessclp", "wellnesslist", "wellness_hub", "foodmpoutletlist"]
export const CARE_SUPPORTED_PAGES_IDS = ["careclp", "care", "careclptab", "clpconsultation", "clphcu", "clpmp", "planmp", "clpphysio", "clpskinhair", "clphealthpack"]
export const WELLNESS_CLP_PAGE_IDS = ["wellnessclp", "wellnesslist", "wellness_hub"]

@injectable()
class VMPageBuilder {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.EatClpPageHook) private eatClpPageHook: EatClpHook,
        @inject(CUREFIT_API_TYPES.EatNowPageHook) private eatnowPageHook: EatNowHook,
        @inject(CUREFIT_API_TYPES.EatLaterPageHook) private eatLaterPageHook: EatLaterHook,
        @inject(CUREFIT_API_TYPES.OrderNowPageHook) private orderNowPageHook: OrderNowHook,
        @inject(CUREFIT_API_TYPES.WholeFitPageHook) private wholeFitPageHook: WholeFitHook,
        @inject(CUREFIT_API_TYPES.WholefitV2PageHook) private wholefitv2PageHook: WholefitV2Hook,
        @inject(CUREFIT_API_TYPES.FitclubMembershipPageHook) private FitclubMembershipPageHook: FitclubMembershipHook,
        @inject(CUREFIT_API_TYPES.FitclubMembershipV2PageHook) private FitclubMembershipV2PageHook: FitclubMembershipV2Hook,
        @inject(CUREFIT_API_TYPES.FitClubActiveMembershipPageHook) private FitClubActiveMembershipPageHook: FitClubActiveMembershipHook,
        @inject(CUREFIT_API_TYPES.EatLaterPageHook) private eatlaterPageHook: EatLaterHook,
        @inject(CUREFIT_API_TYPES.DropoutHIWPageHook) private dropoutHIWPageHook: DropoutHIWPageHook,
        @inject(CUREFIT_API_TYPES.WellnessClpHook) private wellnessClpHook: WellnessClpHook,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(CUREFIT_API_TYPES.WidgetBuilder) protected widgetBuilder: IWidgetBuilder,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(CACHE_TYPES.AppVersionReadWriteCache) protected appVersionWriteHelper: IAppVersionReadWriteCache,
        @inject(CACHE_TYPES.EatReadOnlyCache) private eatCache: IEatReadWriteCache,
        @inject(CUREFIT_API_TYPES.AnnouncementBusiness) private announcementBusiness: AnnouncementBusiness,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) private offerServiceV3: OfferServiceV3,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.EnterpriseUtil) private enterpriseUtil: EnterpriseUtil,
        @inject(CUREFIT_API_TYPES.CoachMarkConfig) private coachMarkConfig: CoachMarkConfig,
        @inject(CUREFIT_API_TYPES.FitnesshubPageHook) private fitnesshubPageHook: FitnesshubPageHook,
        @inject(CUREFIT_API_TYPES.SportshubPageHook) private sportshubPageHook: SportshubPageHook,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) private membershipService: IMembershipService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) private catalogueServicePMS: ICatalogueServicePMS,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) private offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(CUREFIT_API_TYPES.ProgramPageHook) private programPageHook: ProgramPageHook,
        @inject(CUREFIT_API_TYPES.CoachProgramPageHook) private coachProgramPageHook: CoachProgramPageHook,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
    ) {
    }

    async getPage(pageId: string, userContext: UserContext,
        queryParams: { [filterName: string]: string }, req?: express.Request): Promise<Page> {
        let page: VMPage = await this.pageService.getPage(pageId, AppUtil.getTenantFromUserContext(userContext))
        let pageView = undefined
        const sharedData: any = {}
        sharedData["pageId"] = pageId
        queryParams["isLivePage"] = page.isLivePage ? "TRUE" : "FALSE"
        queryParams["pageTheme"] = page.theme
        queryParams["pageId"] = pageId

        // Temp hack until we have profile service in place
        await this.addMembershipQueryBasedOnPageId(userContext, page.pageId, userContext.userProfile, userContext.sessionInfo, queryParams)
        let pageHook = pageIdHookMap.get(pageId)
        if (_.isNil(pageHook) && queryParams["pageHook"]) {
            pageHook = pageIdHookMap.get(queryParams["pageHook"])
        }

        if (!_.isNil(pageHook)) {
            page = await pageHook.preparePage(page, userContext, queryParams, this.serviceInterfaces)
        }
        if (page.pageType === "LIST_PAGE") {
            pageView = this.buildListPage(page, userContext, queryParams, sharedData, req)
        } else if (page.pageType === "TAB_PAGE") {
            pageView = this.buildTabPage(page, userContext, queryParams, sharedData, req)
        }
        if (!_.isNil(pageView) && !_.isNil(pageHook)) {
            pageView = pageHook.callPageHook(this.serviceInterfaces, pageView, userContext, queryParams, sharedData)
        }
        return pageView
    }

    private async buildListPage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any, req?: express.Request) {
        const userProfile: CFUserProfile = userContext.userProfile
        const sessionInfo: SessionInfo = userContext.sessionInfo
        // Checks if its a conditional page
        if (!_.isEmpty(page.segmentIds)) {
            const doesUserBelongToAnySgmt = await this.segmentService.doesUserBelongToAnySegment(page.segmentIds, userContext)
            // Throw unauthorized if the user does not belong to any of the tagged segments
            if (!doesUserBelongToAnySgmt) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 401).withDebugMessage("user does not belong to segment").build()
            }
        }

        const pageOverrideResult = await this.getPageOverrideIfExists(page, userContext)
        const pageToRender = pageOverrideResult && pageOverrideResult.pageOverride || page
        const positonOverrideMap = _.mapKeys(pageToRender.widgetPositionOverrides, (override) => {
            return override.widgetId
        })
        if (page.pageId && page.pageId === "enterpriseclp" && !userContext.sessionInfo.isUserLoggedIn) {
            throw this.errorFactory.withCode(ErrorCodes.NOT_LOGGED_IN_ERR, 400).build()
        }
        const headerPromise = !_.isEmpty(pageToRender.header) ? this.widgetBuilder.buildWidgets(pageToRender.header, this.serviceInterfaces, userContext, queryParams, positonOverrideMap, sharedData, req) : undefined
        const bodyPromise = this.widgetBuilder.buildWidgets(pageToRender.body, this.serviceInterfaces, userContext, queryParams, positonOverrideMap, sharedData, req)
        const pageActionsPromise = !_.isEmpty(pageToRender.pageActions) ? this.buildActions(pageToRender.pageActions, userContext) : undefined
        const pageLoadActionsPromise = !_.isEmpty(pageToRender.pageLoadActions) ? this.buildPageLoadActions(pageToRender.pageLoadActions, userContext) : undefined

        const coachMarkPromise = this.coachMarkConfig.getCoachMark(userContext, page.pageId, null)

        const widgetPromiseArray: Array<Promise<any>> = []
        headerPromise && widgetPromiseArray.push(headerPromise)
        bodyPromise && widgetPromiseArray.push(bodyPromise)

        const pageSelectorPromise = this.buildPageSelector(userContext, page, userProfile, sessionInfo, queryParams)
        let showSearchBarButton = false
        if (page.searchBarButton) {
            showSearchBarButton = _.isEmpty(page.searchBarButton.segmentIds) || !_.isEmpty(await this.segmentService.doesUserBelongToAnySegment(page.searchBarButton.segmentIds, userContext))
        }
        const pageView: ListPage = {
            pageType: page.pageType,
            name: page.name,
            icon: page.icon,
            body: _.flatten(_.map(await Promise.all(widgetPromiseArray), o => o.widgets)),
            pageSelector: await pageSelectorPromise,
            actions: await pageActionsPromise,
            pageLoadActions: await pageLoadActionsPromise,
            rightBarButton: page.rightBarButton,
            initAction: page.initAction,
            searchBarButton: showSearchBarButton ? page.searchBarButton : undefined,
            floatingButtonAction: pageToRender.floatingButtonAction,
            theme: page.theme ? page.theme : undefined,
            auroraTheme: page.auroraTheme ? page.auroraTheme : undefined,
            webTheme: page.webTheme ? page.webTheme : undefined,
            backgroundAnimation: page.backgroundAnimation ? page.backgroundAnimation : undefined,
            customSheetProps: page.customSheetProps != null ? {
                heightRatio: page.customSheetProps.heightRatio,
                scrollable: page.customSheetProps.scrollable,
                showTopNotch: page.customSheetProps.showTopNotch,
                backgroundColor: this.checkVal(page.customSheetProps.backgroundColor) ? page.customSheetProps.backgroundColor : null,
                backgroundColorOpacity: this.checkVal(page.customSheetProps.backgroundColorOpacity) ? page.customSheetProps.backgroundColorOpacity : null,
                bgImageUrl: this.checkVal(page.customSheetProps.bgImageUrl) ? page.customSheetProps.bgImageUrl : null,
                blurEnabled: page.customSheetProps.blurEnabled,
                barrierColor: this.checkVal(page.customSheetProps.barrierColor) ? page.customSheetProps.barrierColor : null,
                barrierColorOpacity: this.checkVal(page.customSheetProps.barrierColorOpacity) ? page.customSheetProps.barrierColorOpacity : null,
                backdropClickable: page.customSheetProps.backdropClickable,
                backButtonDisabled: page.customSheetProps.backButtonDisabled,
                canDoAutoPopOnPushNav: page.customSheetProps.canDoAutoPopOnPushNav,
            } : null,
            pageMetric: {
                pageId: page.pageId,
                pageName: page.name,
                pagesegmentId: pageOverrideResult && pageOverrideResult.segment.segmentId,
                pageSegmentName: pageOverrideResult && pageOverrideResult.segment.name
            }
        }

        const announcement = !_.isEmpty(page.announcementIds) ? await this.announcementBusiness.getSingleActiveAnnouncement(this.pageService, this.segmentService, userContext, false, page.announcementIds) : null
        if (announcement) {
            pageView.announcementData = announcement.announcementData
        }
        const coachMark = await coachMarkPromise
        if (coachMark) {
            pageView.coachMark = coachMark
        }

        if (page.pageId && page.pageId === "enterpriseclp" && AppUtil.isEnterpriseCLPSupported(userContext)) {
            const corp = await this.enterpriseUtil.getCorpLogo(userContext.userProfile.userId)
            if (corp != null) {
                pageView.corpLogo = corp.logoUrl
                pageView.corpName = corp.name
            }
        }
        return pageView
    }

    public async getWidgetLayout(userContext: UserContext, page: VMPage, queryParams: { [filterName: string]: string }): Promise<WidgetWithMetric[]> {
        const pageOverrideResult = await this.getPageOverrideIfExists(page, userContext)
        const pageToRender = pageOverrideResult && pageOverrideResult.pageOverride || page
        const positonOverrideMap = _.mapKeys(pageToRender.widgetPositionOverrides, (override) => { return override.widgetId })
        const headerPromise = !_.isEmpty(pageToRender.header) ? this.widgetBuilder.buildWidgets(pageToRender.header, this.serviceInterfaces, userContext, queryParams, positonOverrideMap, { pageId: page.pageId }) : undefined
        return headerPromise ? (await headerPromise).widgets : undefined
    }

    async getPageOverrideIfExists(page: VMPage, userContext: UserContext): Promise<{ pageOverride: PageOverride, segment: Segment }> {
        if (!_.isEmpty(page.pageOverrides)) {
            const pageOverrideCheckPromises = _.map(page.pageOverrides, async pageOverride => {
                const segmentId = pageOverride.segmentId
                const segment = await this.segmentService.doesUserBelongToSegment(segmentId, userContext)
                if (segment) {
                    return {
                        pageOverride, segment
                    }
                } else {
                    return undefined
                }
            })
            const pageOverrideCheckResults = await Promise.all(pageOverrideCheckPromises)
            // Return the first override that user belongs to
            return _.find(pageOverrideCheckResults, x => !!x)
        }
        return undefined
    }

    private async addMembershipQueryBasedOnPageId(userContext: UserContext, pageId: string, userProfile: CFUserProfile, sessionInfo: SessionInfo, queryParams: { [filterName: string]: string }) {
        const baseOfferRequestParam: BaseOfferRequestParams = {
            source: AppUtil.callSourceFromContext(userContext),
            userId: userProfile.userId,
            deviceId: sessionInfo.deviceId,
            cityId: userProfile.cityId
        }
        const tz = userContext.userProfile.timezone
        const userId = userProfile.userId
        const requiredPageId = queryParams.selectedTab ? queryParams.selectedTab : pageId
        const countryId = AppUtil.getCountryId(userContext)
        userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)

        userProfile.userMappedSegments = this.cacheHelper.getUserMappedSegments(userProfile.userId)
        const user = await userContext.userPromise
        const userInfo: UserInfo = {
            userId: userId,
            deviceId: userContext.sessionInfo.deviceId,
            email: user.email,
            phone: user.phone,
            workEmail: user.workEmail
        }
        if (requiredPageId === "CultAtCenter" || requiredPageId === "cult" || requiredPageId === "Flash_sale" || requiredPageId === "cult_product_testing" || requiredPageId.includes("CultAtCenter")) {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.cultMindSummaryMap = this.cacheHelper.getCultSummaryForAllSubUsers(userProfile.userId)
            const city: City = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)

            userProfile.cultFitnessPacksPromise = CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userProfile.userId, city.cityId)

            userProfile.cultCentersPromise = this.serviceInterfaces.cultFitService.browseFitnessCenter(tz, "CUREFIT_API", false, city.cultCityId, true,
                undefined, false, userProfile.subUserId || userProfile.userId)
            // userProfile.singleCenterCultPacksPromise = Promise.resolve([]) // Deprecating old cult select
            // userProfile.cultJuniorPacksPromise = Promise.resolve([]) // Deprecating Junior cult pack
            userProfile.cultUserTrialEligibilityPromise = this.serviceInterfaces.cultFitService.getUserTrialEligibility(userProfile.userId, "CUREFIT_API")
            userProfile.cultDiyMembershipPromise = this.serviceInterfaces.diyService.getDIYPackFulfilmentsForUserForProductType(userProfile.userId, "DIY_FITNESS_PACK")
            userProfile.cultDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_FITNESS", countryId)
            const userSegments: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.getCultProductPrices = OffersUtil.getCultPackPricesWithProductIds(this.serviceInterfaces.offerServiceV3, {
                userInfo,
                source: baseOfferRequestParam.source,
                cityId: baseOfferRequestParam.cityId,
                cultCityId: city.cultCityId,
                userSegmentIds: userSegments
            })
            await this.addWalletBalancePromise(userProfile)
            await this.addCultPTSegmentPromise(userProfile)
        } else if (
            requiredPageId === "CultAtHome" ||
            requiredPageId === "live" ||
            requiredPageId === "cult-intl" ||
            requiredPageId === "CultLiveIntlPage" ||
            requiredPageId === "workouts"
        ) {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.cultDiyMembershipPromise = this.serviceInterfaces.diyService.getDIYPackFulfilmentsForUserForProductType(userProfile.userId, "DIY_FITNESS_PACK")
            userProfile.cultDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_FITNESS", countryId)
            const userSegments: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.curefitLiveOffersPromise = this.offerServiceV3.getAllApplicableOffersForLive(userInfo, userSegments)
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (requiredPageId === "CultPT" || requiredPageId === "LivePT" || requiredPageId === "LiveSGT" || requiredPageId === "livept_product_testing") {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.cultMindSummaryMap = this.cacheHelper.getCultSummaryForAllSubUsers(userProfile.userId)
            const requiredOfferTypes: BulkApiOfferType[] = ["CONSULTATION", "BUNDLE"]
            userProfile.cultDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_FITNESS", countryId)
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes,
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            await this.addWalletBalancePromise(userProfile)
            await this.addLivePTSegmentPromise(userProfile)
            userProfile.livePTOnboardingCompletePromise = this.serviceInterfaces.cultFitService.getLivePTOnboardingComplete({ appName: "CUREFIT_API", userId: userContext.userProfile.userId })
        } else if (requiredPageId === "CultGear") {
            const requiredOfferTypes: BulkApiOfferType[] = ["GEAR_CART", "GEAR_PRODUCTS"]
            const userInfo: UserInfo = {
                userId: userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            }
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.gearBulkOffersPromise = this.offerServiceV3.getGearBulkOffers(requiredOfferTypes, userInfo, baseOfferRequestParam.source, userSegmentIds)
            await this.addWalletBalancePromise(userProfile)
            await this.addCultPTSegmentPromise(userProfile)
        } else if (requiredPageId === "MindTherapy" || requiredPageId === "therapyclp" || requiredPageId.includes("therapy")) {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.cultMindSummaryMap = this.cacheHelper.getCultSummaryForAllSubUsers(userProfile.userId)
            const requiredOfferTypes: BulkApiOfferType[] = ["BUNDLE", "CONSULTATION"]
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes,
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (requiredPageId === "MindAtCenter" || requiredPageId === "MindAtHome" || requiredPageId === "MindTherapy" || requiredPageId === "MindLiveIntlPage") {
            const city = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
            userProfile.mindCentersPromise = this.serviceInterfaces.mindFitService.browseFitnessCenter(tz, "CUREFIT_API", false, city.cultCityId, true,
                undefined, false, userProfile.subUserId || userProfile.userId)
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.cultMindSummaryMap = this.cacheHelper.getCultSummaryForAllSubUsers(userProfile.userId)
            userProfile.mindDiyMembershipPromise = this.serviceInterfaces.diyService.getDIYPackFulfilmentsForUserForProductType(userProfile.userId, "DIY_MEDITATION_PACK")
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            // userProfile.mindProductPricesPromise = this.serviceInterfaces.offerServiceV3.getMindPackPrices({
            //     userInfo,
            //     source: baseOfferRequestParam.source,
            //     cityId: baseOfferRequestParam.cityId,
            //     cultCityId: city.cultCityId,
            //     userSegmentIds
            // })
            userProfile.mindDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_MEDITATION", countryId)
            // userProfile.mindPacksPromise = Promise.resolve([])
            // userProfile.singleCenterMindPacksPromise = Promise.resolve([])
            // TODO: remove this offers call from here, when pageid fix is pushed.
            const requiredOfferTypes: BulkApiOfferType[] = ["BUNDLE", "CONSULTATION"]
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes,
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            userProfile.mindDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_MEDITATION", countryId)
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (
            requiredPageId === "mind" ||
            requiredPageId === "mind-intl" ||
            requiredPageId === "MindLive" ||
            requiredPageId === "meditation"
        ) {
            userProfile.mindDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_MEDITATION", countryId)
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            userProfile.mindDiyMembershipPromise = this.serviceInterfaces.diyService.getDIYPackFulfilmentsForUserForProductType(userProfile.userId, "DIY_MEDITATION_PACK")
            userProfile.mindDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_MEDITATION", countryId)
            // TODO: remove this offers call from here, when pageid fix is pushed.
            const requiredOfferTypes: BulkApiOfferType[] = ["BUNDLE", "CONSULTATION"]
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes,
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (CARE_SUPPORTED_PAGES_IDS.includes(requiredPageId)) {
            if (requiredPageId === "planmp") {
                userProfile.userSegmentDetailPromise = this.serviceInterfaces.gmfService.getUserSegment(userProfile.userId)
                const userGoalSearchParams: UserGoalSearchParams = {
                    userId: userProfile.userId,
                    statuses: ["LIVE"],
                    isSelf: true
                }
                userProfile.userPlanSummaryPrmoise = this.serviceInterfaces.gmfService.getUserGoalsSummary(userProfile.userId, userGoalSearchParams)
            }
            // Once browse product call supports will make this call with multiple subcategory codes
            switch (requiredPageId) {
                case "clphcu": {
                    userProfile.careBundleProductPromise = this.serviceInterfaces.healthfaceService.browseProducts("BUNDLE", "DIAG_PACK", CareUtil.getHealthfaceTenant("DIAG_PACK"), true, undefined, undefined, undefined, undefined, true)
                    break
                }
                case "clpphysio": {
                    userProfile.careBundleProductPromise = this.serviceInterfaces.healthfaceService.browseProducts("BUNDLE", "PHYSIOTHERAPY", CareUtil.getHealthfaceTenant("PHYSIOTHERAPY"), true, undefined, undefined, undefined, undefined, true)
                    break
                }
                case "clpskinhair": {
                    userProfile.careBundleProductPromise = this.serviceInterfaces.healthfaceService.browseProducts("BUNDLE", "SKIN_PACK", CareUtil.getHealthfaceTenant("SKIN_PACK"), true, undefined, undefined, false, undefined, true)
                    break
                }
                case "clphealthpack": {
                    userProfile.careBundleProductPromise = this.serviceInterfaces.healthfaceService.browseProducts("BUNDLE", "CONSULTATION_PACK", CareUtil.getHealthfaceTenant("CONSULTATION_PACK"), true, undefined, undefined, false, undefined, true)
                    break
                }
            }
            const requiredOfferTypes: BulkApiOfferType[] = ["CONSULTATION", "DIAGNOSTICS", "BUNDLE"]
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes,
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (_.startsWith(requiredPageId, "fitstart") || requiredPageId.indexOf("sale") >= 0 || _.startsWith(requiredPageId, "independenceday") ||
            requiredPageId.toLowerCase().indexOf("stepitup") >= 0) {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            const userInfo: UserInfo = {
                userId: userId,
                deviceId: userContext.sessionInfo.deviceId,
                email: user.email,
                phone: user.phone,
                workEmail: user.workEmail
            }
            const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes: ["CONSULTATION"],
                userSegmentIds,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            userProfile.curefitLiveOffersPromise = this.offerServiceV3.getAllApplicableOffersForLive(userInfo, userSegmentIds)
        } else if (requiredPageId === "hometab" || requiredPageId === "webVerticalInfo" || requiredPageId === "home-intl") {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            // userProfile.gymfitMembershipPromise = this.serviceInterfaces.gymfitService.getUserMembershipsAndTrialUsages(userProfile.userId)
            // userProfile.dIYProductFulfilmentInfo = this.serviceInterfaces.diyService.getDIYProductFulfilmentInfo(userProfile.userId)
        } else if (_.startsWith(requiredPageId, "home")) {
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            const city = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
            // userProfile.cultPacksPromise = this.serviceInterfaces.cultFitService.browsePacks("CUREFIT_API", userProfile.userId, city.cultCityId, true)
            userProfile.cultFitnessPacksPromise = CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userProfile.userId, city.cityId)
            userProfile.availableMealSlotsPromise = this.serviceInterfaces.deliveryAreaService.getMealSlotsForArea(userProfile.areaId)
            const userSegments: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            // userProfile.mindProductPricesPromise = this.serviceInterfaces.offerServiceV3.getMindPackPrices({
            //     userInfo,
            //     source: baseOfferRequestParam.source,
            //     cityId: baseOfferRequestParam.cityId,
            //     cultCityId: city.cultCityId,
            //     userSegmentIds: userSegments
            // })
            userProfile.getCultProductPrices = OffersUtil.getCultPackPricesWithProductIds(this.serviceInterfaces.offerServiceV3, {
                userInfo,
                source: baseOfferRequestParam.source,
                cityId: baseOfferRequestParam.cityId,
                cultCityId: city.cultCityId,
                userSegmentIds: userSegments
            })
            userProfile.cultDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_FITNESS", countryId)
            userProfile.deliveryAreaPromise = this.serviceInterfaces.deliveryAreaService.getDeliveryArea(userProfile.areaId)
            userProfile.mindDiySeriesPromise = this.serviceInterfaces.diyService.getDIYSeries(userProfile.userId, "DIY_MEDITATION", countryId)
        } else if (
            requiredPageId === "eatclp" ||
            requiredPageId === "eatnow" ||
            requiredPageId === "eatlater" ||
            requiredPageId === "eatsubscribe" ||
            requiredPageId === "eatrecipe" ||
            requiredPageId === "eatordernow" ||
            requiredPageId === "wholefit" ||
            requiredPageId === "eatmarketplace" ||
            requiredPageId === "eatfclistingpage" ||
            requiredPageId === "wholefitv2" ||
            requiredPageId === "eatproductcollectionpage" ||
            requiredPageId === "eat-intl" ||
            requiredPageId === "EatLiveIntlPage" ||
            requiredPageId === "recipes" ||
            _.includes(requiredPageId, "foodmp")
        ) {
            if (queryParams["outletId"]) {
                /*
                 * The case of Food Marketplace listing, we do not need Eatfit promises
                 */
                const { cityId } = userProfile
                const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
                userProfile.foodMarketplaceCartOffersPromise = this.offerServiceV3.getApplicableFoodMPCartOffers({
                    cityId,
                    userInfo,
                    userSegmentIds,
                    brandId: queryParams["brandId"],
                    outletId: queryParams["outletId"],
                    cuisine: queryParams["cuisine"]
                })
            } else {
                userProfile.deliveryAreaPromise = this.serviceInterfaces.deliveryAreaService.getDeliveryArea(userProfile.areaId)
                const deliveryArea = await userProfile.deliveryAreaPromise
                const listingBrand = EatUtil.getListingBrandForPageId(requiredPageId, { query: queryParams })
                userProfile.availableMealSlotsPromise = this.serviceInterfaces.deliveryAreaService.getMealSlotsForArea(userProfile.areaId)
                const deliveryAreaTz = await this.serviceInterfaces.deliveryAreaService.getTimeZoneForAreaId(deliveryArea.areaId)
                const mealSlots = await userProfile.availableMealSlotsPromise
                const currentMealSlot = SlotUtil.getCurrentMealSlotFrom(mealSlots, deliveryArea.channel, deliveryAreaTz)
                const isCultCafe: boolean = deliveryArea.kioskType === "CAFE"
                let orderTimes
                if (isCultCafe) {
                    orderTimes = await this.serviceInterfaces.kiosksDemandService.getKioskSlotTimes(deliveryArea.kioskIds[0])
                }
                const selectedMealSlotAndDay = EatUtil.getSelectedMealSlotAndDay(queryParams, deliveryArea, deliveryAreaTz,
                    mealSlots, currentMealSlot, orderTimes)
                const singleOffersRequest = this.getEatSingleOfferRequestParam(userProfile.areaId,
                    listingBrand === "FOOD_MARKETPLACE" ? undefined : listingBrand, baseOfferRequestParam, { [selectedMealSlotAndDay.day]: [selectedMealSlotAndDay.mealSlot] })
                userProfile.eatSingleOffersPromise = Promise.resolve({} as FoodSinglePriceOfferResponse)
                userProfile.eatPackOffersPromise = Promise.resolve({} as FoodPackOffersResponseV2)
                userProfile.eatCartOffersPromise = Promise.resolve([]) // Removing eat offers
                userProfile.fitclubMembershipPromise = this.serviceInterfaces.fitclubBusiness.isFitclubMember(userProfile.userId)
                userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            }
            await this.addHealthSegmentPromise(userProfile)
            await this.addWalletBalancePromise(userProfile)
        } else if (requiredPageId === "plantab" || requiredPageId === "ai_plan" || requiredPageId === "activeplans" || requiredPageId === "activepacks") {
            // Commenting as the gmf api is causing issues in mongo and plan product is suspended
            // userProfile.userSegmentDetailPromise = this.serviceInterfaces.gmfService.getUserSegment(userProfile.userId)
            // const userGoalSearchParams: UserGoalSearchParams = {
            //     userId: userProfile.userId,
            //     statuses: ["LIVE"],
            //     isSelf: true
            // }
            // userProfile.userPlanSummaryPrmoise = this.serviceInterfaces.gmfService.getUserGoalsSummary(userProfile.userId, userGoalSearchParams)
        } else if (requiredPageId === "clpaimg") {
            userProfile.userSegmentDetailPromise = this.serviceInterfaces.gmfService.getUserSegment(userProfile.userId)
            const userGoalSearchParams: UserGoalSearchParams = {
                userId: userProfile.userId,
                statuses: ["LIVE"],
                isSelf: true
            }
            userProfile.userPlanSummaryPrmoise = this.serviceInterfaces.gmfService.getUserGoalsSummary(userProfile.userId, userGoalSearchParams)
            // const careOfferRequestParams: CareOfferRequestParams = {
            //     ...baseOfferRequestParam,
            //     productType: "BUNDLE"
            // }
            // userProfile.careOffersPromise = this.serviceInterfaces.offerService.getCareOffers(careOfferRequestParams)
            const userSegments: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.careBulkOffersIdPromises = this.offerServiceV3.getAllApplicableOffersForCare({
                userInfo,
                requiredOfferTypes: ["BUNDLE"],
                userSegmentIds: userSegments,
                cityId: baseOfferRequestParam.cityId,
                source: baseOfferRequestParam.source
            })
            userProfile.cultMindSummary = this.cacheHelper.getCultSummary(userProfile.userId)
            await this.addWalletBalancePromise(userProfile)
            await this.addHealthSegmentPromise(userProfile)
        } else if (requiredPageId === "FitclubActiveMembershipPage") {
            userProfile.fitclubMembershipPromise = this.serviceInterfaces.fitclubBusiness.isFitclubMember(userProfile.userId)
        } else if (requiredPageId === "booking_reco_view" ||
            requiredPageId === "cult_booking_reco_view" || requiredPageId === "eat_singles_booking_reco_view" ||
            requiredPageId === "mind_booking_reco_view" || requiredPageId === "cultlive_booking_reco_view") {

        } else if (requiredPageId === "gymfit" || requiredPageId === "gymfitList") {
            const city: City = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
            // userProfile.gymfitMembershipPromise = this.serviceInterfaces.gymfitService.getUserMembershipsAndTrialUsages(userProfile.userId)
            userProfile.gymfitActiveCheckin = this.serviceInterfaces.gymfitService.getActiveGymfitCheckIn(userProfile.userId)
            // Promises added to show cult pass black pack widget on gymfitList tab. Only CULT_UNLIMITED packs will be shown.
            // userProfile.cultPacksPromise = this.serviceInterfaces.cultFitService.browsePacks("CUREFIT_API", userProfile.userId, city.cultCityId, true)

            userProfile.cultFitnessPacksPromise = CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userId, city.cityId)

            const userSegments: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
            userProfile.getCultProductPrices = OffersUtil.getCultPackPricesWithProductIds(this.serviceInterfaces.offerServiceV3, {
                userInfo,
                source: baseOfferRequestParam.source,
                cityId: baseOfferRequestParam.cityId,
                cultCityId: city.cultCityId,
                userSegmentIds: userSegments
            })
            // Promise added to show membership detail widget of cult pass black.
            userProfile.cultMindSummaryMap = this.cacheHelper.getCultSummaryForAllSubUsers(userProfile.userId)
            await this.addCultPTSegmentPromise(userProfile)
        } else if (WELLNESS_CLP_PAGE_IDS.includes(requiredPageId)) {
            userProfile.careBulkOffersIdPromises = (async () => {
                const requiredOfferTypes: BulkApiOfferType[] = ["CONSULTATION", "DIAGNOSTICS", "BUNDLE"]
                const userSegmentIds: string[] = await userProfile.promiseMapCache.getPromise("user-platform-segments", { userId })
                return this.offerServiceV3.getAllApplicableOffersForCare({
                    userInfo,
                    requiredOfferTypes,
                    userSegmentIds,
                    cityId: baseOfferRequestParam.cityId,
                    source: baseOfferRequestParam.source
                })
            })()
            userProfile.carePatientPromise = this.serviceInterfaces.healthfaceService.getAllPatients(userProfile.userId)
        }
    }

    private getCareConsultationOfferRequestParam(baseRequestParams: BaseOfferRequestParams) {
        const careOfferRequestParams: CareOfferRequestParams = Object.assign({}, baseRequestParams, {
            productType: "CONSULTATION"
        })
        return careOfferRequestParams
    }

    private getEatSingleOfferRequestParam(areaId: string,
        listingBrand: ListingBrandIdType = "EAT_FIT",
        baseRequestParams: BaseOfferRequestParams, dateMealSlotMap: {
            [date: string]: MenuType[];
        }): EatOfferRequestParamsV3 {
        const eatOfferRequestParams: EatOfferRequestParamsV3 = Object.assign({}, baseRequestParams, {
            areaId: areaId,
            dateMealSlotMap: dateMealSlotMap,
            listingBrand: listingBrand
        })
        return eatOfferRequestParams
    }

    private getEatPackOffersRequestParam(areaId: string, baseRequestParams: BaseOfferRequestParams): EatOfferRequestParams {
        const eatOfferRequestParams: EatOfferRequestParams = Object.assign({}, baseRequestParams, {
            areaId: areaId
        })
        return eatOfferRequestParams
    }

    private async addWalletBalancePromise(userProfile: CFUserProfile) {
        userProfile.walletBalancePromise = this.serviceInterfaces.fitcashService.balance(userProfile.userId, userProfile.city.country.currencyCode)
    }

    private async addHealthSegmentPromise(userProfile: UserProfile) {
        try {
            // Adding Patient Promise to Userprofile so it can be accessed from widget
            userProfile.carePatientPromise = this.serviceInterfaces.healthfaceService.getAllPatients(userProfile.userId)
            const patientsList = await userProfile.carePatientPromise
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            if (selfPatient) {
                userProfile.healthSegmentsPromise = this.serviceInterfaces.healthfaceService.getHealthSegment(userProfile.userId, selfPatient.id)
            } else {
                userProfile.healthSegmentsPromise = Promise.resolve([])
            }
        } catch (error) {
            this.logger.error(`Error building healthSegmentsPromise for ${userProfile.userId} statusCode:${error.statusCode}, name:${error.name}, title:${error.title}, message:${error.message}, trace:${error.stack}`)
        }
    }

    private async addCultPTSegmentPromise(userProfile: UserProfile) {
        try {
            const patientsList = await this.serviceInterfaces.healthfaceService.getAllPatients(userProfile.userId)
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            if (selfPatient) {
                userProfile.cultPTSegmentsPromise = this.serviceInterfaces.cultPTService.getHealthSegment(userProfile.userId, selfPatient.id)
            } else {
                userProfile.cultPTSegmentsPromise = Promise.resolve([])
            }
        } catch (error) {
            this.logger.error(`Error building cultPTSegmentsPromise for ${userProfile.userId} statusCode:${error.statusCode}, name:${error.name}, title:${error.title}, message:${error.message}, trace:${error.stack}`)
        }
    }

    private async addLivePTSegmentPromise(userProfile: CFUserProfile) {
        try {
            const patientsList = await this.serviceInterfaces.healthfaceService.getAllPatients(userProfile.userId)
            const selfPatient = !_.isEmpty(patientsList) ? _.find(patientsList, patient => patient.relationship === "Self") : undefined
            if (selfPatient) {
                userProfile.cultPTSegmentsPromise = this.serviceInterfaces.cultPTService.getHealthSegment(userProfile.userId, selfPatient.id)
                const doctorRecommendationRequest: DoctorRecommendationRequest = {
                    subCategoryCode: "LIVE_PERSONAL_TRAINING",
                    doctorTypeCodesCsv: LIVE_PT_DOCTOR_TYPES,
                    patientId: selfPatient.id,
                    excludeDoctors: undefined
                }
                userProfile.doesLivePTRecommendationExistPromise = this.serviceInterfaces.healthfaceService.haveDoctorRecommendations(doctorRecommendationRequest, "CULTFIT")
            } else {
                userProfile.cultPTSegmentsPromise = Promise.resolve([])
            }
        } catch (error) {
            this.logger.error(`Error building livePTSegmentsPromise for ${userProfile.userId} statusCode:${error.statusCode}, name:${error.name}, title:${error.title}, message:${error.message}, trace:${error.stack}`)
        }
    }

    private async buildTabPage(page: VMPage, userContext: UserContext, queryParams: { [filterName: string]: string }, sharedData?: any, req?: express.Request) {
        let showSearchBarButton = false
        if (page.searchBarButton) {
            showSearchBarButton = _.isEmpty(page.searchBarButton.segmentIds) || !_.isEmpty(await this.segmentService.doesUserBelongToAnySegment(page.searchBarButton.segmentIds, userContext))
        }
        const pageView: TabPage = {
            sections: [],
            lists: {},
            name: page.name,
            icon: page.icon,
            pageType: page.pageType,
            rightBarButton: page.rightBarButton,
            searchBarButton: showSearchBarButton ? page.searchBarButton : undefined,
            floatingButtonAction: page.floatingButtonAction,
            selectedTabIndex: 0,
            theme: page.theme ? page.theme : undefined,
            auroraTheme: page.auroraTheme ? page.auroraTheme : undefined,
            backgroundAnimation: page.backgroundAnimation ? page.backgroundAnimation : undefined,
            pageMetric: {
                pageId: page.pageId,
                pageName: page.name
            }
        }
        const userProfile: CFUserProfile = userContext.userProfile
        const sessionInfo: SessionInfo = userContext.sessionInfo
        let selectedTab = queryParams.selectedTab
        userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
        const tabPageOverrideResult = await this.getPageOverrideIfExists(page, userContext)
        const tabPageOverride = tabPageOverrideResult && tabPageOverrideResult.pageOverride
        page.tabs = tabPageOverride && tabPageOverride.tabs ? tabPageOverride.tabs : page.tabs

        const pageSelectorPromise = this.buildPageSelector(userContext, page, userProfile, sessionInfo, queryParams)
        const tabs = await this.getTabsForPage(userContext, page, queryParams)
        const announcement = !_.isEmpty(page.announcementIds) ? await this.announcementBusiness.getSingleActiveAnnouncement(this.pageService, this.segmentService, userContext, false, page.announcementIds) : null
        if (announcement) {
            pageView.announcementData = announcement.announcementData
        }
        if (selectedTab) {
            // this is required because client is dumb and might send a tab which is not available
            const filteredTab = tabs.find((tab) => {
                return tab.subPageId === selectedTab
            })
            if (!filteredTab) {
                selectedTab = undefined
            }
        }
        if (!selectedTab && !_.isEmpty(tabs)) {
            selectedTab = _.minBy(tabs, "priority").subPageId // default to highest priority sub page
            this.logger.debug(`selectedTab set from newTabs ${selectedTab}`)
        }

        const coachMarkPromise = this.coachMarkConfig.getCoachMark(userContext, page.pageId, tabs.map(tab => tab.subPageId))

        const sectionPromises = _.map(tabs, async tab => {
            const subPage = await this.pageService.getPage(tab.subPageId, AppUtil.getTenantFromUserContext(userContext))
            this.logger.debug(`subPage ${subPage.pageId}`)

            let showSubPageSearchBarButton = false
            if (subPage?.searchBarButton) {
                showSubPageSearchBarButton = _.isEmpty(subPage?.searchBarButton?.segmentIds) || !_.isEmpty(await this.segmentService.doesUserBelongToAnySegment(subPage?.searchBarButton?.segmentIds, userContext))
            }

            // Get any override if present
            const pageOverrideResult = await this.getPageOverrideIfExists(subPage, userContext)
            pageView.sections.push({
                name: tab.name,
                id: tab.subPageId,
                priority: tab.priority,
                pageMetric: {
                    pageId: subPage.pageId,
                    pageName: subPage.name,
                    pagesegmentId: pageOverrideResult && pageOverrideResult.segment.segmentId,
                    pageSegmentName: pageOverrideResult && pageOverrideResult.segment.name
                }
            })
            if (!AppUtil.isVMPageOptimizationSupported(userContext) || (tab.subPageId === selectedTab)) {
                this.logger.debug(`setting page data selectedTab ${selectedTab} subPageId ${tab.subPageId}`)
                const pageToRender = pageOverrideResult && pageOverrideResult.pageOverride || subPage
                const positonOverrideMap = _.mapKeys(pageToRender.widgetPositionOverrides, (override) => {
                    return override.widgetId
                })
                const clonedQueryParams = _.clone(queryParams)
                clonedQueryParams["isLivePage"] = subPage.isLivePage || page.pageId === "fitnesshub" ? "TRUE" : "FALSE"
                clonedQueryParams["pageId"] = tab.subPageId

                const headerPromise = !_.isEmpty(pageToRender.header) ? this.widgetBuilder.buildWidgets(pageToRender.header, this.serviceInterfaces, userContext, clonedQueryParams, positonOverrideMap, sharedData) : undefined
                const bodyPromise = this.widgetBuilder.buildWidgets(pageToRender.body, this.serviceInterfaces, userContext, clonedQueryParams, positonOverrideMap, sharedData, req)
                const pageActionsPromise = !_.isEmpty(pageToRender.pageActions) ? this.buildActions(pageToRender.pageActions, userContext) : undefined
                const pageLoadActionsPromise = !_.isEmpty(pageToRender.pageLoadActions) ? this.buildPageLoadActions(pageToRender.pageLoadActions, userContext) : undefined

                const widgetPromiseArray: Array<Promise<any>> = []
                headerPromise && widgetPromiseArray.push(headerPromise)
                bodyPromise && widgetPromiseArray.push(bodyPromise)

                pageView.lists[tab.subPageId] = {
                    pageMetric: {
                        pageId: subPage.pageId,
                        pageName: subPage.name,
                        pagesegmentId: pageOverrideResult && pageOverrideResult.segment.segmentId,
                        pageSegmentName: pageOverrideResult && pageOverrideResult.segment.name
                    },
                    header: headerPromise ? (await headerPromise).widgets : undefined,
                    body: _.flatten(_.map(await Promise.all(widgetPromiseArray), o => o.widgets)),
                    actions: await pageActionsPromise,
                    pageLoadActions: await pageLoadActionsPromise,
                    searchBarButton: showSubPageSearchBarButton ? subPage.searchBarButton : undefined,
                    floatingButtonAction: pageToRender.floatingButtonAction,
                }
            }
        })
        await Promise.all(sectionPromises)

        const coachMark = await coachMarkPromise
        if (coachMark) {
            pageView.coachMark = coachMark
        }
        pageView.pageSelector = await pageSelectorPromise
        pageView.sections.sort((a, b) => {
            return a.priority < b.priority ? -1 : 1
        })
        pageView.pulsatingDotIndex = pageView.sections.findIndex(a => {
            return a.id === page.pulsatingDotTab
        })
        pageView.pulsatingDotVersion = page.pulsatingDotVersion
        pageView.selectedTabIndex = pageView.sections.findIndex(a => {
            return a.id === selectedTab
        })
        pageView.selectedTabIndex = pageView.selectedTabIndex === -1 ? 0 : pageView.selectedTabIndex
        return pageView
    }

    public async getTabsForPage(userContext: UserContext, page: VMPage, queryParams: { [filterName: string]: string }): Promise<Tab[]> {
        let selectedTab = queryParams.selectedTab
        const hasOutletId = !_.isNil(queryParams.outletId) && page.pageId === "eatclp"
        this.logger.debug(`selected tab from query ${selectedTab}`)
        if (!_.isEmpty(selectedTab) && selectedTab === "undefined") { // client sending undefined tab, handling in api side.
            selectedTab = undefined
            this.logger.info(`selected tab undefined ${selectedTab}`)
        }
        const userProfile: CFUserProfile = userContext.userProfile
        const deliveryArea = await userProfile.deliveryAreaPromise
        const preferredLocation = await userProfile.preferredLocationPromise
        let isWellnessAtCenterActive = false
        try {
            if (AppUtil.isSugarfitWellnessAtCenterSupportedAppVersion(userContext)) {
                const sugarfitWellnessPackResponse: any = await this.serviceInterfaces.healthfaceService.getActiveWellnessPack(userContext.userProfile.userId)
                if (sugarfitWellnessPackResponse) {
                    isWellnessAtCenterActive = true
                }
            }
        } catch (e) {
            console.log(e)
        }

        const tabs = (await Promise.all(_.map(page.tabs, async tab => {
            let doesUserBelongToTabSegmentPromise, doesUserBelongToPageSegmentPromise

            if (!_.isEmpty(tab.segmentIds)) {
                doesUserBelongToTabSegmentPromise = this.segmentService.doesUserBelongToAnySegment(tab.segmentIds, userContext)
            }
            // Checking for tab segment pass
            if (doesUserBelongToTabSegmentPromise) {
                const doesUserBelongToTabSegment = await doesUserBelongToTabSegmentPromise
                // Skip the tab, if the user does not belong to tab segments
                if (!doesUserBelongToTabSegment) {
                    return undefined
                }
            }

            const subPage = await this.pageService.getPage(tab.subPageId, AppUtil.getTenantFromUserContext(userContext))
            if (!_.isEmpty(subPage.segmentIds)) {
                doesUserBelongToPageSegmentPromise = this.segmentService.doesUserBelongToAnySegment(subPage.segmentIds, userContext)
            }
            // Checking for page segment pass
            if (doesUserBelongToPageSegmentPromise) {
                const doesUserBelongToPageSegment = await doesUserBelongToPageSegmentPromise
                // Skip the tab if the user does not belong to the tagged segment
                if (!doesUserBelongToPageSegment) {
                    return undefined
                }
            }

            if (tab.subPageId !== "foodmpordernow" && hasOutletId) {
                return undefined
            }

            if (tab.subPageId === "foodmpordernow" && !hasOutletId) {
                return undefined
            }

            if (tab.subPageId === "eatlater" && _.get(deliveryArea, "scheduledCartDisabled", false)) {
                return undefined
            }

            if (tab.subPageId === "eatsubscribe" && _.get(deliveryArea, "subscriptionsDisabled", false)) {
                return undefined
            }

            if (tab.subPageId === "wellnessatcenter" && !isWellnessAtCenterActive) {
                return undefined
            }

            if (tab.subPageId === "sfnutritionmealspage") {
                const isSugarfitStoreTabSupportedUser = await AppUtil.isSugarfitStoreTabSupportedUser(userContext, this.segmentationCacheClient)
                if (!isSugarfitStoreTabSupportedUser) {
                    return undefined
                }
            }

            if (tab.subPageId === "eatmarketplace") {
                const isKioskAddress = !_.isNil(preferredLocation) && !_.isNil(preferredLocation.address) && preferredLocation.address.addressType === "KIOSK"
                const isCafe: boolean = !_.isNil(preferredLocation) && !_.isNil(preferredLocation.address) && preferredLocation.address.kioskType === "CAFE"
                if (isKioskAddress || isCafe) {
                    return undefined
                }
            }

            if (tab.subPageId === "wholefit") {
                // temp fix. Have to think of some better way to model data
                // Assumption -- Eat.fit will always be there where whole fit is present
                const preferredLocation = await userProfile.preferredLocationPromise
                if (_.isNil(preferredLocation.area) || AppUtil.isWholeFitV2ReviewSupported(userContext)) {
                    return undefined
                }
                // Condition when there is eat_fit but no whole fit
                let wholeFitPreferredLocation = await userProfile.wholeFitPreferredLocationPromise
                if (_.isNil(wholeFitPreferredLocation)) {
                    wholeFitPreferredLocation = await this.userBusiness.getPreferredLocation(userContext, userProfile.userId, userContext.sessionInfo.sessionData, userContext.sessionInfo.lon, userContext.sessionInfo.lat, undefined, undefined, "WHOLE_FIT")
                }
                // handled location disabled case separately
                if (_.isNaN(userContext.sessionInfo.lat) && _.isNaN(userContext.sessionInfo.lon)) {
                    if (!_.isNil(wholeFitPreferredLocation) && _.isNil(wholeFitPreferredLocation.area)) {
                        return undefined
                    }
                }
                if (!_.isNil(wholeFitPreferredLocation) && _.isNil(wholeFitPreferredLocation.area)) {
                    return undefined
                }
            }
            if (tab.subPageId === "wholefitv2" && page.pageId !== "wholefitclp") {
                // not filtering out wholefitv2 tab page for wholefitclp, only filtering for eatclp
                const preferredLocation = await userProfile.preferredLocationPromise
                if (preferredLocation && preferredLocation.address && preferredLocation.address.addressType === "KIOSK") {
                    return undefined
                }

                const wholeFitPreferredLocation = await userProfile.wholeFitPreferredLocationPromise

                if (_.isNil(wholeFitPreferredLocation)) {
                    this.logger.error("wholefit Preferred location broken")
                    return undefined
                }
                // this.logger.info(`WholefitPreferredLocation: ${JSON.stringify(wholeFitPreferredLocation)}`)
                // handled location disabled case separately
                if (_.isNaN(userContext.sessionInfo.lat) || _.isNaN(userContext.sessionInfo.lon)) {
                    if (!_.isNil(wholeFitPreferredLocation) && _.isNil(wholeFitPreferredLocation.area)) {
                        this.logger.info(`cityId: ${userProfile.cityId}`)
                        const city = await this.cityService.getCityById(userProfile.cityId)
                        if (_.isNil(city.defaultWholefitAreaId)) {
                            // this field should  be present for all the wholefit supporting cities
                            return undefined
                        } else {
                            return tab
                        }
                    } else {
                        return tab
                    }
                }
                if (!_.isNil(wholeFitPreferredLocation) && _.isNil(wholeFitPreferredLocation.area)) {
                    return undefined
                }
            }

            return tab
        }))).filter(tab => tab !== undefined)

        return tabs
    }

    public async getTabPageSection(userContext: UserContext, page: VMPage, queryParams: { [filterName: string]: string }): Promise<Section[]> {
        /* check if page requested for is a tab page, if not no section will be available */
        if (page.pageType !== "TAB_PAGE") {
            return []
        }
        const tabs = await this.getTabsForPage(userContext, page, queryParams)

        const sections: Section[] = (await Promise.all(_.map(tabs, async tab => {
            const subPage = await this.pageService.getPage(tab.subPageId, AppUtil.getTenantFromUserContext(userContext))
            this.logger.debug(`subPage ${subPage.pageId}`)
            // Get any override if present
            const pageOverrideResult = await this.getPageOverrideIfExists(subPage, userContext)
            return {
                name: tab.name,
                id: tab.subPageId,
                priority: tab.priority,
                pageMetric: {
                    pageId: subPage.pageId,
                    pageName: subPage.name,
                    pagesegmentId: pageOverrideResult && pageOverrideResult.segment.segmentId,
                    pageSegmentName: pageOverrideResult && pageOverrideResult.segment.name
                }
            } as Section
        }))).sort((a, b) => a.priority < b.priority ? -1 : 1)

        return sections
    }

    // check once
    private async buildPageSelector(userContext: UserContext, page: VMPage, userProfile: CFUserProfile, sessionInfo: SessionInfo, queryParams: { [filterName: string]: string }): Promise<PageSelector> {
        /* remove page selector of international app */
        if (AppUtil.isInternationalApp(userContext)) {
            return undefined
        }
        if (page.pageId === "wellnessclp" && await AppUtil.isFoodMarketplaceSupported(userContext, this.hamletBusiness)) {
            // todo: remove this for wellnessclp once a significant number has updated to 8.92 and above
            page.selector = "AREA_SELECTOR"
        }

        if (page.selector === "AREA_SELECTOR") {
            let pageSelector: AreaPageSelector
            if (page.pageId === "wholefitclp") {
                // using wholefitPreferredLocation for showing adddress in wholefitclp tab
                if (!userProfile.wholeFitPreferredLocationPromise)
                    userProfile.wholeFitPreferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userProfile.userId, sessionInfo.sessionData, sessionInfo.lon, sessionInfo.lat, undefined, undefined, "WHOLE_FIT")
                const wholefitPreferredLocation = await userProfile.wholeFitPreferredLocationPromise
                pageSelector = {
                    selector: "AREA_SELECTOR",
                    areaName: EatUtil.formatPreferredLocation(wholefitPreferredLocation)
                }
            } else {
                if (!userProfile.preferredLocationPromise)
                    userProfile.preferredLocationPromise = this.userBusiness.getPreferredLocation(userContext, userProfile.userId, sessionInfo.sessionData, sessionInfo.lon, sessionInfo.lat, undefined, undefined, "EAT_FIT")
                const preferredLocation = await userProfile.preferredLocationPromise
                pageSelector = {
                    selector: "AREA_SELECTOR",
                    areaName: EatUtil.formatPreferredLocation(preferredLocation),
                }
                if (_.includes(page.pageId, "foodmp") && AppUtil.isFoodMarketplaceLocationCompulsionSupported(userContext) && !FoodMarketplaceUtil.isWithinLocationSelectionThreshold(userContext)) {
                    // we do not want to nudge the user if he has recently updated the location preferrence
                    pageSelector.isAddressAvailable = !_.isEmpty(preferredLocation.address)
                    pageSelector.toolTip = {
                        title: "Delivering here.",
                        description: "You can change the delivery address if you want.",
                        action: [
                            {
                                title: "GOT IT",
                                actionType: "TOOLTIP_OK"
                            },
                            {
                                title: "CHANGE ADDRESS",
                                actionType: "CHANGE_ADDRESS"
                            }
                        ]
                    }
                }
            }
            return pageSelector
        } else if (page.selector === "CITY_SELECTOR") {
            const city = await this.serviceInterfaces.cityService.getCityById(userProfile.cityId)
            const pageSelector: CityPageSelector = {
                selector: "CITY_SELECTOR",
                cityName: city.name
            }
            return pageSelector
        }
        return undefined
    }

    private async buildActions(actions: Action[], userContext: UserContext): Promise<Action[]> {
        const actionPromises = _.map(actions, async action => {
            if (action.actionType === "BOOK_LIVE_PT_SESSION" || action.actionType === "BOOK_LIVE_SGT_SESSION") {
                const subCategoryCode = action.actionType === "BOOK_LIVE_SGT_SESSION" ? "LIVE_SGT" : "LIVE_PERSONAL_TRAINING"
                const productId = action.actionType === "BOOK_LIVE_SGT_SESSION" ? LIVE_SGT_SNC_PRODUCT_ID : LIVE_PT_SNC_PRODUCT_ID
                const newAction = <Action>await this.serviceInterfaces.careBusiness.getLivePTSessionBookAction(userContext, { productId, actionTitle: action.title, subCategoryCode })
                action.actionType = newAction.actionType
                action.url = newAction.url
                action.meta = newAction.meta
            }
            if (action.actionType === "UNPAUSE_CULT_MEMBERSHIP") {
                const userId = userContext.userProfile.userId
                const pausedMembershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED"]))).obj
                if (!_.isEmpty(pausedMembershipList)) {
                    const pausedMembership = pausedMembershipList[0]
                    if (pausedMembership) {
                        const newAction: Action = {
                            actionType: "NAVIGATION",
                            title: action.title || "UNPAUSE",
                            url: await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(pausedMembership.id.toString(), userContext, userContext.sessionInfo.userAgent, undefined, true)
                        }
                        return newAction
                    }
                }
            }
            if (!_.isEmpty(action.segmentIds)) {
                const segment = await this.segmentService.doesUserBelongToAnySegment(action.segmentIds, userContext)
                if (!_.isNil(segment)) {
                    return action
                } else {
                    return
                }
            } else {
                return action
            }
        })
        const actionsList = await Promise.all(actionPromises)
        const result = actionsList.filter(action => !_.isNil(action))
        return !_.isEmpty(result) ? result : undefined
    }

    private async buildPageLoadActions(actions: Action[], userContext: UserContext): Promise<Action[]> {
        const actionPromises = _.map(actions, async action => {
            if (action.actionType === "SHOW_SELECT_GYM_LOCATION") {
                const showGymLocalitySelector = await GymfitUtil.shouldShowGymLocalitySelector(this.serviceInterfaces, userContext)
                if (!showGymLocalitySelector) {
                    return
                }
            }
            if (!_.isEmpty(action.segmentIds)) {
                const segment = await this.segmentService.doesUserBelongToAnySegment(action.segmentIds, userContext)
                if (!_.isNil(segment)) {
                    return action
                } else {
                    return
                }
            } else {
                return action
            }
        })
        const actionsList = await Promise.all(actionPromises)
        const result = actionsList.filter(action => !_.isNil(action))
        return !_.isEmpty(result) ? result : undefined
    }


    public hasPageRequiresLocationAware(pageId: string): boolean {
        if (_.isNil(pageId)) {
            return false
        }
        const foundPageIndex = _.findIndex(VM_PAGE_LOCATION_AWARE, (supportedPage) => {
            return _.startsWith(supportedPage, pageId)
        })
        return (foundPageIndex >= 0)
    }

    private checkVal(val?: string): boolean {
        return !(val == null || val == "" || val.length < 1)
    }
}

export default VMPageBuilder
