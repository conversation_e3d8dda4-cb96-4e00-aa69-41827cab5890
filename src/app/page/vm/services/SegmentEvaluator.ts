import {
    ConditionFieldsType,
    ConditionField,
    DIYUserType,
    CultUserType,
    UserType,
    MindTherapyUserType, CultPTuserType, CareUserType, FitclubUserType, EmailSetType
} from "@curefit/segment-common"
import { SessionInfo, UserContext, UserProfile } from "@curefit/userinfo-common"
import { injectable, inject } from "inversify"
import { CUREFIT_API_TYPES } from "../../../../config/ioc/types"
import * as _ from "lodash"
import { CFUserProfile } from "../CFUserProfile"
import { Logger, BASE_TYPES } from "@curefit/base"
import { getCultTrialUserTypes, getCultUserTypes } from "../../../util/CultUtil"
import {
    getTherapyUserTypes,
    getCultPTUserTypes,
    getCareUserTypes,
    getDiyUserType,
    getLivePTUserTypes, getLiveSGTUserTypes
} from "@curefit/vm-models"
import AppUtil from "../../../util/AppUtil"
import { BulkOfferIdsResponse, PackOffersResponse, OfferV2, FoodPackOffersResponseV2, FoodSinglePriceOfferResponse, SubscriptionOffersResponse, BulkOfferAPIResponse, GetAllApplicableOfferIdsResponse } from "@curefit/offer-common"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { DeliverySubArea } from "@curefit/eat-common"
import {
    City,
    VerticalType
} from "@curefit/location-common"
import IUserBusiness from "../../../user/IUserBusiness"
import INavBarBusiness from "../../../user/INavBarBusiness"
import { WHOLE_FIT_API_CLIENT_TYPES, IWholeFitService } from "@curefit/wholefit-api-client"
import { IPromiseRequest, PromiseCache, SEGMENT_OVERRIDE } from "../../../util/VMUtil"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../../../error/ErrorCodes"
import { User } from "@curefit/user-common"
import { eternalPromise, TimeUtil } from "@curefit/util-common"
import { CacheHelper } from "../../../util/CacheHelper"
import { SegmentTestOverride } from "../segmentoverride/SegmentTestOverride"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { ProductType } from "@curefit/product-common"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import EatUtil from "../../../util/EatUtil"
import { OutletsForLocationAndCuisineRequest } from "@curefit/foodway-common"
import FoodMarketplaceUtil from "../../../util/FoodMarketplaceUtil"
import { FOODWAY_CLIENT_TYPES, IFoodwayApiService } from "@curefit/foodway-client"
import * as util from "util"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { CityService, LOCATION_TYPES } from "@curefit/location-mongo"

export interface ISegmentEvaluator {
    checkCondition(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<boolean>
}

export interface ConditionEvaluatorProps {
    condition: ConditionFieldsType
    value: any
    userProfile: CFUserProfile
    sessionInfo: SessionInfo
    userContext: UserContext
}

export interface SegmentEvaluatorProps extends ConditionEvaluatorProps {
    conditionField: ConditionField
}

@injectable()
abstract class BaseSegmentEvaluator implements ISegmentEvaluator {
    private redisCrudDao: ICrudKeyValue
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
        @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGMFClient,
        @inject(CUREFIT_API_TYPES.UserBusiness) public userBusiness: IUserBusiness,
        @inject(CUREFIT_API_TYPES.NavBarBusiness) public navBarBusiness: INavBarBusiness,
        @inject(WHOLE_FIT_API_CLIENT_TYPES.IWholeFitService) public wholefitService: IWholeFitService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) public errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(USER_CLIENT_TYPES.IUserService) public userService: IUserService,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(FOODWAY_CLIENT_TYPES.IFoodwayApiService) protected foodwayService: IFoodwayApiService,
        @inject(LOCATION_TYPES.CityService) protected cityService: CityService,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) protected membershipService: IMembershipService,
    ) {
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("DEFAULT")
    }

    protected abstract getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<any[]>

    protected compare(conditionValues: any[], userEligibleValues: any[]): any[] {
        return _.intersection(conditionValues, userEligibleValues)
    }

    async checkCondition(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<boolean> {
        try {
            if (_.isEmpty(segmentEvaluatorProps.value)) {
                return true
            }
            const conditionValues = _.isArray(segmentEvaluatorProps.value) ? segmentEvaluatorProps.value : [segmentEvaluatorProps.value]

            const userEligibleValues = await this.getUserEligibleValues(segmentEvaluatorProps)
            const intersection = this.compare(conditionValues, userEligibleValues)
            switch (segmentEvaluatorProps.condition) {
                case "BELONGS_TO_ALL":
                    return intersection.length === conditionValues.length
                case "NOT_BELONGS_TO_ANY":
                    return intersection.length !== conditionValues.length
                case "BELONGS_TO_ANY":
                    return intersection.length > 0
                case "NOT_BELONGS_TO_ALL":
                    return intersection.length === 0
            }
        } catch (err) {
            if (Math.random() < 0.01) {
                this.rollbarService.sendError(err)
            }
            this.logger.error("Error fetching details " + err.stack)
            return false
        }
    }
}
@injectable()
export class CitiesConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return [segmentEvaluatorProps.userProfile.cityId]
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }
}

@injectable()
export class AreasConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return [segmentEvaluatorProps.userProfile.areaId]
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }
}

@injectable()
export class DeliveryChannelConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userProfile.deliveryAreaPromise
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("delivery channel segment not supported for this page").build()
        }
        return [result.channel]
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }
}

@injectable()
export class EmailSetConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const user = await segmentEvaluatorProps.userContext.userPromise
        return user.email || !segmentEvaluatorProps.userContext.sessionInfo.isUserLoggedIn ? ["IS_SET"] : ["NOT_SET"]
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }
}


@injectable()
export class UserAgentsConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return [segmentEvaluatorProps.sessionInfo.userAgent]
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }
}

@injectable()
export class CultSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userContext.userProfile.promiseMapCache.getPromise("cult-mind-summary", { userId: segmentEvaluatorProps.userContext.userProfile.userId })
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("cultUserTypes segment not supported for this page").build()
        }
        const userTypes: CultUserType[] = []
        const userContext: UserContext = {
            userProfile: segmentEvaluatorProps.userProfile,
            sessionInfo: segmentEvaluatorProps.sessionInfo,
            userPromise: undefined
        }
        const membershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED", "PURCHASED"]))).obj
        userTypes.push(...await getCultUserTypes(userContext, result, membershipList))
        const trialEligibilityResult = await segmentEvaluatorProps.userProfile.cultUserTrialEligibilityPromise
        if (trialEligibilityResult) {
            userTypes.push(...getCultTrialUserTypes(userContext, trialEligibilityResult))
        }
        return userTypes
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return this.getUserEligibleValues(segmentEvaluatorProps)
    }

}

@injectable()
export class MindSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userContext.userProfile.promiseMapCache.getPromise("cult-mind-summary", { userId: segmentEvaluatorProps.userContext.userProfile.userId })
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("cultUserTypes segment not supported for this page").build()
        }
        const userContext: UserContext = {
            userProfile: segmentEvaluatorProps.userProfile,
            sessionInfo: segmentEvaluatorProps.sessionInfo,
            userPromise: undefined
        }
        // INFO: Using "CULT" as a benifit while calling membershipService function as "MIND_FIT" is depricated
        const membershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userContext.userProfile.userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED", "PURCHASED"]))).obj
        return await getCultUserTypes(userContext, result, membershipList)
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return this.getUserEligibleValues(segmentEvaluatorProps)
    }

}

@injectable()
export class MindTherapySegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userProfile.healthSegmentsPromise
        if (!result) {
            this.logger.error("No userProfile healthSegmentPromise exists for this condition - " + util.inspect(segmentEvaluatorProps.condition, {depth: 2}) + " and session info of " + util.inspect(segmentEvaluatorProps.sessionInfo, {depth: 2}) + " and value of " + util.inspect(segmentEvaluatorProps.value, {depth: 2}))
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("therapySegmentsPromise segment not supported for this page").build()
        }
        return getTherapyUserTypes(result)
    }
}

@injectable()
export class UserTypeConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const userTypes: UserType[] = []

        if (!segmentEvaluatorProps.sessionInfo.isUserLoggedIn) {
            userTypes.push("NOT_LOGGED_IN")
            return userTypes
        }
        try {
            const user: User = await segmentEvaluatorProps.userContext.userPromise
            const userCreatedMoment = TimeUtil.getMomentForDateString(user.createdAt, segmentEvaluatorProps.userContext.userProfile.timezone, "YYYY-MM-DDTHH:mm:ss Z")
            const momentNow = TimeUtil.getMomentNow(segmentEvaluatorProps.userContext.userProfile.timezone)
            if (userCreatedMoment.diff(momentNow, "days") >= 0) {
                userTypes.push("REGISTERED_IN_24_HOURS")
                return userTypes
            }
        } catch (e) {
            this.logger.error("Error in calculating userType segment", e)
        }
        return userTypes
    }
}

@injectable()
export class PTUserSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userProfile.cultPTSegmentsPromise
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("cultPTSegmentsPromise segment not supported for this page").build()
        }
        return getCultPTUserTypes(result)
    }

}

@injectable()
export class LivePTUserSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        let result = await segmentEvaluatorProps.userProfile.cultPTSegmentsPromise
        if (!result) {
            this.logger.error("cultPTSegmentsPromise not supported for this page")
            result = []
        }
        let doesRecommendationExist = false
        let isLivePTOnboardingComplete: boolean = false
        try {
            doesRecommendationExist = await segmentEvaluatorProps.userProfile.doesLivePTRecommendationExistPromise
        } catch (error) {
            this.logger.error("Error fetching recommendations promise for this page", error)
        }
        try {
            const livePTOnboardingComplete = await segmentEvaluatorProps.userProfile.livePTOnboardingCompletePromise
            if (livePTOnboardingComplete && livePTOnboardingComplete.isOnboarded) {
                isLivePTOnboardingComplete = true
            }
        } catch (error) {
            this.logger.error("Error fetching livePTOnboardingCompletePromise for this page", error)
        }
        return getLivePTUserTypes(result, isLivePTOnboardingComplete, doesRecommendationExist)
    }

}

@injectable()
export class LiveSGTUserSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        let result = await segmentEvaluatorProps.userProfile.cultPTSegmentsPromise
        if (!result) {
            this.logger.error("cultPTSegmentsPromise not supported for this page")
            result = []
        }
        let isLivePTOnboardingComplete: boolean = false
        try {
            const livePTOnboardingComplete = await segmentEvaluatorProps.userProfile.livePTOnboardingCompletePromise
            if (livePTOnboardingComplete && livePTOnboardingComplete.isOnboarded) {
                isLivePTOnboardingComplete = true
            }
        } catch (error) {
            this.logger.error("Error fetching livePTOnboardingCompletePromise for this page", error)
        }
        return getLiveSGTUserTypes(result, isLivePTOnboardingComplete)
    }
}

@injectable()
export class CareSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result = await segmentEvaluatorProps.userProfile.healthSegmentsPromise
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("careSegmentsPromise segment not supported for this page").build()
        }
        return getCareUserTypes(result)
    }

}

@injectable()
export class SourcesConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return [AppUtil.callSource(segmentEvaluatorProps.sessionInfo.apiKey)]
    }

}

@injectable()
export class CultDIYSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const cultDiyMembership = await segmentEvaluatorProps.userProfile.cultDiyMembershipPromise
        const dIYProductFulfilmentInfo = await segmentEvaluatorProps.userProfile.dIYProductFulfilmentInfo
        if (!cultDiyMembership && !dIYProductFulfilmentInfo) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("cultDiyMembershipPromise segment not supported for this page").build()
        }
        const userTypes: DIYUserType[] = []
        if (cultDiyMembership) {
            userTypes.push(getDiyUserType(cultDiyMembership))
        }
        if (dIYProductFulfilmentInfo) {
            const haveNotConsumedCultDIY = dIYProductFulfilmentInfo.find(entry => entry.productType === "DIY_FITNESS" && entry.contentConsumedCount === 0)
            if (haveNotConsumedCultDIY) {
                userTypes.push("NO_CONTENT_CONSUMED")
            }
        }
        return userTypes.concat("ALL")
    }

}

@injectable()
export class MindDIYSegmentsConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const mindDiyMembership = await segmentEvaluatorProps.userProfile.mindDiyMembershipPromise
        const dIYProductFulfilmentInfo = await segmentEvaluatorProps.userProfile.dIYProductFulfilmentInfo
        if (!mindDiyMembership && !dIYProductFulfilmentInfo) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("mindDiyMembershipPromise segment not supported for this page").build()
        }
        const userTypes: DIYUserType[] = []
        if (mindDiyMembership) {
            userTypes.push(getDiyUserType(mindDiyMembership))
        }
        if (dIYProductFulfilmentInfo) {
            const haveNotConsumedCultDIY = dIYProductFulfilmentInfo.find(entry => entry.productType === "DIY_MEDITATION" && entry.contentConsumedCount === 0)
            if (haveNotConsumedCultDIY) {
                userTypes.push("NO_CONTENT_CONSUMED")
            }
        }
        return userTypes.concat("ALL")
    }

}

@injectable()
export class VerticalsConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const vertical = _.isArray(segmentEvaluatorProps.value) ? segmentEvaluatorProps.value[0] : segmentEvaluatorProps.value
        const city: City = segmentEvaluatorProps.userProfile.city ?? this.cityService.getDefaultCity(AppUtil.getTenantFromUserContext(segmentEvaluatorProps.userContext))
        const supportedVerticals: VerticalType[] = await this.navBarBusiness.getSupportedVerticals(segmentEvaluatorProps.userContext, city)


        if (vertical === "WHOLE" && supportedVerticals.includes("WHOLE")) {
            const wholefitPreferredLocation = await segmentEvaluatorProps.userProfile.wholeFitPreferredLocationPromise
            const wholefitServiceabilityPromise = segmentEvaluatorProps.userContext.userProfile.promiseMapCache.getPromise("wholefit-serviceability", { latLong: wholefitPreferredLocation.latLong, cityId: city.cityId })
            try {
                const response: {
                    serviceableArea: DeliverySubArea;
                    defaultArea: DeliverySubArea;
                } = await wholefitServiceabilityPromise
                if (!wholefitPreferredLocation.latLong && response.defaultArea) {
                    return supportedVerticals
                }
                if (response && response.serviceableArea) {
                    return supportedVerticals
                } else {
                    return supportedVerticals.filter(vertical => vertical !== "WHOLE")
                }
            } catch (error) {
                return supportedVerticals.filter(vertical => vertical !== "WHOLE")
            }
        } else {
            return supportedVerticals
        }
    }

}

@injectable()
export class HamletConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<{ experiment: string, bucket: string }[]> {
        if (!segmentEvaluatorProps.userProfile.hamletUserExperimentPromise) {
            segmentEvaluatorProps.userProfile.hamletUserExperimentPromise = this.hamletBusiness.getUserAllocations(AppUtil.getHamletContext(segmentEvaluatorProps.userContext, []))
        }
        const hamletUserExperimentMap = (await segmentEvaluatorProps.userProfile.hamletUserExperimentPromise).assignmentsMap
        const experimentIds: string[] = Object.keys(hamletUserExperimentMap)
        return _.map(experimentIds, experimentId => {
            return {
                experiment: experimentId,
                bucket: hamletUserExperimentMap[experimentId].bucket.bucketId
            }
        })
    }

    protected compare(conditionValues: { experiment: string, bucket: string }[], userEligibleValues: { experiment: string, bucket: string }[]): { experiment: string, bucket: string }[] {
        return _.filter(userEligibleValues, experimentObj => !_.isEmpty(_.find(conditionValues, conditionValue => conditionValue.experiment === experimentObj.experiment && conditionValue.bucket === experimentObj.bucket)))
    }

}

@injectable()
export class PlatformSegmentConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        this.logger.debug("fetching userSegments from segmentation service")
        const reqObj: IPromiseRequest = { userId: segmentEvaluatorProps.userContext.userProfile.userId }
        const userSegmentsPromise: Promise<string[]> = segmentEvaluatorProps.userContext.userProfile.promiseMapCache.getPromise("user-platform-segments", reqObj)
        const userSegments: string[] = await userSegmentsPromise
        // this.logger.info("userSegments from segmentation service", { userSegments: userSegments })
        return userSegments
    }

}

@injectable()
export class ProductTypeSegmentConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const availableOfferings =  segmentEvaluatorProps?.userProfile?.city?.availableOfferings ?? []
        const result: string[] = []
        for (let i = 0; i < availableOfferings?.length; i = i + 1) {
            const eligible = await this.checkProductTypeEligibilityRequirements(availableOfferings[i], segmentEvaluatorProps)
            if (eligible) {
                result.push(availableOfferings[i])
            }
        }
        return result
    }

    private async checkProductTypeEligibilityRequirements(productType: ProductType, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<boolean> {
        switch (productType) {
            case "FOOD_MARKETPLACE":
                const { userContext } = segmentEvaluatorProps
                if (FoodMarketplaceUtil.getFoodMarketplaceSupportedCities(userContext).includes(userContext?.userProfile?.cityId)) {
                    try {
                        const preferredLocation = await userContext.userProfile.preferredLocationPromise
                        const latLong = EatUtil.getLatLong(preferredLocation, undefined, undefined)
                        const req: OutletsForLocationAndCuisineRequest = {
                            latitude: latLong.lat,
                            longitude: latLong.long
                        }
                        const { brandList, outletsList } = await this.foodwayService.getOutletsForLocationAndCuisine(req)
                        if (outletsList?.length > 0) {
                            return true
                        }
                    } catch (e) {
                        this.logger.error(`ProductTypeSegmentEvaluator: ${productType} eligibility evaluation failed.`)
                        return true // since we're still in the serviceable city
                    }
                }
                return false
            default:
                return true
        }
    }
}

@injectable()
export class OffersConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        if (!segmentEvaluatorProps.userProfile.customerOfferIds) {
            await this.setCustomOfferIds(segmentEvaluatorProps.userProfile)
        }
        return segmentEvaluatorProps.userProfile.customerOfferIds
    }

    private async setCustomOfferIds(userProfile: CFUserProfile) {
        const offerResult = await userProfile.bulkOffersPromise
        const offerIdsResult = await userProfile.bulkOfferIdsPromise
        const gearOffersResult = await userProfile.gearBulkOffersPromise

        const gearCartOffers = gearOffersResult ? gearOffersResult.cartOffers : undefined
        const gearProductOffers = gearOffersResult ? gearOffersResult.productOffers : undefined
        const careAllOffers = offerResult && offerResult.careOffers ? offerResult.careOffers : undefined
        const careOffer = await userProfile.careOffersPromise
        const careConsultaionOffers = await userProfile.careConsultationOffersPromise
        const cureLivePackOffers = await userProfile.curefitLiveOffersPromise
        const careOfferIds = await userProfile.careBulkOffersIdPromises

        const fitnessPacksForUser = await userProfile.cultFitnessPacksPromise
        const cultProductPrices = !_.isEmpty(fitnessPacksForUser) ? await userProfile.getCultProductPrices(fitnessPacksForUser.map(pack => pack.id)) : undefined

        const customerOfferIds: string[] = []
        if (offerIdsResult) {
            customerOfferIds.push(... this.getBulkOfferIds(offerIdsResult))
        }
        if (careAllOffers) {
            customerOfferIds.push(..._.map(careAllOffers, offer => offer?.offerId))
        }
        if (careOffer) {
            customerOfferIds.push(... this.getOfferIds(careOffer))
        }
        if (careConsultaionOffers) {
            customerOfferIds.push(... this.getOfferIds(careConsultaionOffers))
        }
        if (gearCartOffers) {
            customerOfferIds.push(... this.getOfferV2Ids(gearCartOffers))
        }
        if (gearProductOffers) {
            customerOfferIds.push(... this.getOfferV2Ids(gearProductOffers))
        }
        if (cureLivePackOffers) {
            customerOfferIds.push(... cureLivePackOffers.offerIds)
        }
        if (careOfferIds?.offerIds) {
            customerOfferIds.push(...careOfferIds.offerIds)
        }
        if (cultProductPrices) {
            const {offerMap} = cultProductPrices
            customerOfferIds.push(..._.keys(offerMap))
        }
        userProfile.customerOfferIds = customerOfferIds
    }

    private getBulkOfferIds(offerIdsResult: GetAllApplicableOfferIdsResponse): string[] {
        const offerIds: string[] = []

        if (offerIdsResult.cultOfferIds) {
            offerIds.push(...offerIdsResult.cultOfferIds)
        }
        if (offerIdsResult.mindOfferIds) {
            offerIds.push(...offerIdsResult.mindOfferIds)
        }
        return offerIds
    }

    private getOfferIds(packOffersResponse: PackOffersResponse): string[] {
        const offerIds: string[] = []
        if (Object.keys(packOffersResponse)) {
            Object.keys(packOffersResponse).forEach(productId => {
                const packOffer = packOffersResponse[productId]
                _.forEach(packOffer.offers, offer => {
                    offerIds.push(offer.offerId)
                })
                _.forEach(packOffer.preBuzzOffers, offer => {
                    offerIds.push(offer.offerId)
                })
                return offerIds
            })
        }
        return offerIds
    }

    private getOfferV2Ids(offers: OfferV2[]): string[] {
        const offerIds: string[] = []
        if (!_.isEmpty(offers)) {
            offers.forEach(cartOffer => {
                offerIds.push(cartOffer.offerId)
            })
        }
        return offerIds
    }

    private getEatOfferIds(packOffersResponse: FoodPackOffersResponseV2, singleOfferResponse: FoodSinglePriceOfferResponse, cartOffers: OfferV2[]): string[] {
        const allEatofferIds: string[] = packOffersResponse ? Object.keys(packOffersResponse.offers) : []
        if (singleOfferResponse) {
            Object.keys(singleOfferResponse.prices).forEach(date => {
                const offerOnDate = singleOfferResponse.prices[date]
                Object.keys(offerOnDate).forEach(mealSlot => {
                    const offerOnMealSlot = offerOnDate[mealSlot]
                    Object.keys(offerOnMealSlot).forEach(productId => {
                        const offerOnProdut = offerOnMealSlot[productId]
                        _.forEach(offerOnProdut.offerIds, offerId => {
                            allEatofferIds.push(offerId)
                        })
                        _.forEach(offerOnProdut.preBuzzOfferIds, offerId => {
                            allEatofferIds.push(offerId)
                        })
                    })
                })

            })
        }
        if (!_.isEmpty(cartOffers)) {
            cartOffers.forEach(cartOffer => {
                allEatofferIds.push(cartOffer.offerId)
            })
        }
        const offerIds = _.uniq(allEatofferIds)
        return offerIds
    }

    protected async getOverriddenValue(segmentOverride: SegmentTestOverride, segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return await this.getUserEligibleValues(segmentEvaluatorProps)
    }

}

@injectable()
export class FitclubConditionEvaluator extends BaseSegmentEvaluator {

    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const isFitclubMember = await segmentEvaluatorProps.userProfile.fitclubMembershipPromise
        const fitclubType = isFitclubMember ? "ACTIVE_FITCLUB_MEMBER" : "INACTIVE_FITCLUB_MEMBER"
        return [fitclubType, "ALL"]
    }

}

@injectable()
export class EmailSuffixConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const user = await segmentEvaluatorProps.userContext.userPromise
        const emailSuffix = user.email ? user.email.substring(user.email.indexOf("@") + 1) : undefined
        const workEmailSuffix = user.workEmail ? user.workEmail.substring(user.workEmail.indexOf("@") + 1) : undefined
        return [emailSuffix, workEmailSuffix]
    }

}

@injectable()
export class SupportedCodePushVersionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const { condition,
                value,
                userProfile,
                sessionInfo,
                userContext
        } = segmentEvaluatorProps

        const cpVersion = "" + _.get(sessionInfo, "cpVersion", "")
        const ans = []
        if (!_.isEmpty(cpVersion)) {
            ans.push(cpVersion)
        }
        return _.compact(ans)
    }

}

@injectable()
export class MealPlanSegmentsConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        const result: boolean = await this.gmfService.checkExistsUserPlan(
          segmentEvaluatorProps.userProfile.userId
        )
        const segments = []
        if (!result) {
            segments.push("MEAL_PLAN_UNSUBSCRIBED")
        } else {
            segments.push("MEAL_PLAN_SUBSCRIBED")
        }
        return segments
  }

}

@injectable()
export class GymSegmentsConditionEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<any[]> {
        const result = await segmentEvaluatorProps.userContext.userProfile.gymfitMembershipPromise
        if (!result) {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("gymfitMembershipPromise segment not supported for this page").build()
        }
        const trailUsage = result.trialUsage
        const segments = ["ALL"]
        if (result.membershipSummary.activeMembership) {
            segments.push("WITH_PACK")
        }
        if (trailUsage.maxCount - trailUsage.used > 0 && trailUsage.status === "ACTIVE") {
            segments.push("WITHOUT_PACK_WITH_FREE_TRAIL")
        }
        return segments
    }

}

@injectable()
export class CultsportArticleTypeSegmentEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return segmentEvaluatorProps?.userProfile?.cultsportSegmentsMetaData?.articleTypes ||  []
    }
}

@injectable()
export class CultsportBrandSegmentEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return segmentEvaluatorProps?.userProfile?.cultsportSegmentsMetaData?.brands ||  []
    }
}

@injectable()
export class CultsportCategorySegmentEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return segmentEvaluatorProps?.userProfile?.cultsportSegmentsMetaData?.categories ||  []
    }
}

@injectable()
export class CultsportCollectionTypeSegmentEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return segmentEvaluatorProps?.userProfile?.cultsportSegmentsMetaData?.collectionSlugs || []
    }
}

@injectable()
export class CultsportProductIdSegmentEvaluator extends BaseSegmentEvaluator {
    protected async getUserEligibleValues(segmentEvaluatorProps: ConditionEvaluatorProps): Promise<string[]> {
        return segmentEvaluatorProps?.userProfile?.cultsportSegmentsMetaData?.productIds || []
    }
}

@injectable()
export class SegmentEvaluator {
    CONDITION_FIELD_TO_SERVICE_MAP = new Map<ConditionField, ISegmentEvaluator>()
    constructor(@inject(CUREFIT_API_TYPES.CitiesConditionEvaluator) private citiesConditionEvaluator: CitiesConditionEvaluator,
        @inject(CUREFIT_API_TYPES.AreasConditionEvaluator) private areasConditionEvaluator: AreasConditionEvaluator,
        @inject(CUREFIT_API_TYPES.DeliveryChannelConditionEvaluator) private deliveryChannelConditionEvaluator: DeliveryChannelConditionEvaluator,
        @inject(CUREFIT_API_TYPES.EmailSetConditionEvaluator) private emailSetConditionEvaluator: EmailSetConditionEvaluator,
        @inject(CUREFIT_API_TYPES.UserAgentsConditionEvaluator) private userAgentsConditionEvaluator: UserAgentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.CultSegmentsConditionEvaluator) private cultSegmentsConditionEvaluator: CultSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.MindSegmentsConditionEvaluator) private mindSegmentsConditionEvaluator: MindSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.MindTherapySegmentsConditionEvaluator) private mindTherapySegmentsConditionEvaluator: MindTherapySegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.PTUserSegmentsConditionEvaluator) private pTUserSegmentsConditionEvaluator: PTUserSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.LivePTUserSegmentsConditionEvaluator) private livePTUserSegmentsConditionEvaluator: LivePTUserSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.CareSegmentsConditionEvaluator) private careSegmentsConditionEvaluator: CareSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.SourcesConditionEvaluator) private sourcesConditionEvaluator: SourcesConditionEvaluator,
        @inject(CUREFIT_API_TYPES.CultDIYSegmentsConditionEvaluator) private cultDIYSegmentsConditionEvaluator: CultDIYSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.MindDIYSegmentsConditionEvaluator) private mindDIYSegmentsConditionEvaluator: MindDIYSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.FitclubConditionEvaluator) private fitclubConditionEvaluator: FitclubConditionEvaluator,
        @inject(CUREFIT_API_TYPES.EmailSuffixConditionEvaluator) private emailSuffixConditionEvaluator: EmailSuffixConditionEvaluator,
        @inject(CUREFIT_API_TYPES.OffersConditionEvaluator) private offersConditionEvaluator: OffersConditionEvaluator,
        @inject(CUREFIT_API_TYPES.UserTypeConditionEvaluator) private userTypeConditionEvaluator: UserTypeConditionEvaluator,
        @inject(CUREFIT_API_TYPES.VerticalsConditionEvaluator) private verticalsConditionEvaluator: VerticalsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.GymSegmentsConditionEvaluator) private gymSegmentConditionEvaluator: GymSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.HamletConditionEvaluator) private hamletConditionEvaluator: HamletConditionEvaluator,
        @inject(CUREFIT_API_TYPES.PlatformSegmentConditionEvaluator) private platformSegmentConditionEvaluator: PlatformSegmentConditionEvaluator,
        @inject(CUREFIT_API_TYPES.MealPlanSegmentsConditionEvaluator) private mealPlanSegmentsConditionEvaluator: MealPlanSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.LiveSGTUserSegmentsConditionEvaluator) private liveSGTUserSegmentsConditionEvaluator: LiveSGTUserSegmentsConditionEvaluator,
        @inject(CUREFIT_API_TYPES.ProductTypeSegmentsConditionEvaluator) private productTypeSegmentConditionEvaluator: ProductTypeSegmentConditionEvaluator,
        @inject(CUREFIT_API_TYPES.SupportedCodePushVersionEvaluator) private supportedCodePushVersionEvaluator: SupportedCodePushVersionEvaluator,
        @inject(CUREFIT_API_TYPES.CultsportArticleTypeSegmentEvaluator) private cultsportArticleTypeSegmentEvaluator: CultsportArticleTypeSegmentEvaluator,
        @inject(CUREFIT_API_TYPES.CultsportCategorySegmentEvaluator) private cultsportCategorySegmentEvaluator: CultsportCategorySegmentEvaluator,
        @inject(CUREFIT_API_TYPES.CultsportBrandSegmentEvaluator) private cultsportBrandSegmentEvaluator: CultsportBrandSegmentEvaluator,
        @inject(CUREFIT_API_TYPES.CultsportCollectionTypeSegmentEvaluator) private cultsportCollectionTypeSegmentEvaluator: CultsportCollectionTypeSegmentEvaluator,
        @inject(CUREFIT_API_TYPES.CultsportProductIdSegmentEvaluator) private cultsportProductIdSegmentEvaluator: CultsportProductIdSegmentEvaluator
    ) {
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CITIES", citiesConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("AREAS", areasConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("USER_AGENTS", userAgentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULT_SEGMENTS", cultSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("MIND_SEGMENTS", mindSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("MIND_THERAPY_SEGMENTS", mindTherapySegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULT_PERSONAL_TRAINING_SEGMENTS", pTUserSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("LIVE_PERSONAL_TRAINING_SEGMENTS", livePTUserSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("LIVE_SGT_SEGMENTS", liveSGTUserSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CARE_SEGMENTS", careSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("SOURCES", sourcesConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULT_DIY_SEGMENTS", cultDIYSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("MIND_DIY_SEGMENTS", mindDIYSegmentsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("FITCLUB_SEGMENTS", fitclubConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("EMAIL_SUFFIX", emailSuffixConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("OFFERS", offersConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("VERTICALS", verticalsConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("GYM_SEGMENTS", gymSegmentConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("HAMLET", hamletConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("DELIVERY_CHANNEL", deliveryChannelConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("EMAIL_SET", emailSetConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("USER_TYPE", userTypeConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("PLATFORM_SEGMENTS", platformSegmentConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("PRODUCT_TYPE", productTypeSegmentConditionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("SUPPORTED_CODEPUSH_VERSION", supportedCodePushVersionEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULTSPORT_ARTICLE_TYPE_SEGMENTS", cultsportArticleTypeSegmentEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULTSPORT_CATEGORY_SEGMENTS", cultsportCategorySegmentEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULTSPORT_BRAND_SEGMENTS", cultsportBrandSegmentEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULTSPORT_COLLECTION_SLUG_SEGMENTS", cultsportCollectionTypeSegmentEvaluator)
        this.CONDITION_FIELD_TO_SERVICE_MAP.set("CULTSPORT_PRODUCT_ID_SEGMENTS", cultsportProductIdSegmentEvaluator)
    }

    async conditionEvaluator(segmentEvaluatorProps: SegmentEvaluatorProps): Promise<boolean> {
        return await this.CONDITION_FIELD_TO_SERVICE_MAP.get(segmentEvaluatorProps.conditionField).checkCondition(segmentEvaluatorProps)
    }

}
