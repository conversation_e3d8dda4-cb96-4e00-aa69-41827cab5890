import { injectable, inject } from "inversify"
import { IBannerService } from "@curefit/vm-models"
import { IBannern<PERSON>eadOnlyDao, VM_MODELS_TYPES } from "@curefit/vm-models"
import { PromUtil } from "@curefit/base"
import { Logger } from "@curefit/base"
import * as _ from "lodash"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { Banner, BannerActionType as ActionType } from "@curefit/vm-models"
import * as momentTz from "moment-timezone"
import { BannerQueryCondition } from "@curefit/vm-models"
import { CachingServiceType } from "@curefit/base"
import { BASE_TYPES } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { UserAgentType } from "@curefit/base-common"
import { ActionUtil, ActionUtilV1, SeoUrlParams } from "@curefit/base-utils"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { FoodPack } from "@curefit/eat-common"

export const ACTION_MAP = new Map<ActionType, string>()
ACTION_MAP.set("CULT_12_FIT", "curefit://cultofferbrowse?packId=product_id")
ACTION_MAP.set("MIND_12_FIT", "curefit://mindofferbrowse?packId=product_id")
ACTION_MAP.set("CULT_6_FIT", "curefit://cultofferbrowse?packId=product_id")
ACTION_MAP.set("MIND_6_FIT", "curefit://mindofferbrowse?packId=product_id")
ACTION_MAP.set("CULT_PRE_REGISTRATION", "curefit://cultofferbrowse")
ACTION_MAP.set("MIND_PRE_REGISTRATION", "curefit://mindofferbrowse")
ACTION_MAP.set("CULT_PACK_BROWSE", "curefit://cultbrowsepage?forceEnableBuy=true")
ACTION_MAP.set("MIND_PACK_BROWSE", "curefit://mindcenterpackbrowse?forceEnableBuy=true")
ACTION_MAP.set("CULT_PACK_PAGE", "curefit://cultpack?packId=product_id&forceEnableBuy=true")
ACTION_MAP.set("MIND_PACK_PAGE", "curefit://cultmindpack?packId=product_id&forceEnableBuy=true")
ACTION_MAP.set("CULT_PACK_PAGE", "curefit://cultpack?packId=product_id&forceEnableBuy=true")
ACTION_MAP.set("EAT_CLP", "curefit://eatfitclp")
ACTION_MAP.set("CULT_CLP", "curefit://cultfitclp")
ACTION_MAP.set("MIND_CLP", "curefit://mindfitclp")
ACTION_MAP.set("EAT_PACK_BROWSE", "curefit://foodbrowsepage")
ACTION_MAP.set("NONE", " ")

@injectable()
class BannerService extends InMemoryCacheService<Map<string, Banner>> implements IBannerService {

    constructor(
        @inject(VM_MODELS_TYPES.BannernReadOnlyDao) private bannerDao: IBannernReadOnlyDao,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(BASE_TYPES.ILogger) logger: Logger,
        @inject(BASE_TYPES.PromUtil) private promUtil: PromUtil,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
    ) {
        super(logger, 5 * 60)
        this.load("BannerService")
    }

    protected reportError(err: any): void {
        this.rollbarService.sendError(err)
    }

    async loadData(): Promise<Map<string, Banner>> {
        const banners = await this.bannerDao.find({
            condition: {
                status: "LIVE",
                "endTimeWithTz.date": { $gte: new Date() }
            }
        })
        const map = new Map<string, Banner>()
        banners.forEach(banner => map.set(banner.bannerId, banner))
        return map
    }

    getBanners(condition: BannerQueryCondition): Banner[] {
        this.promUtil.reportCacheAccess(CachingServiceType.BANNER)
        const banners = Array.from(this.cache.values())
        return this.filterBanners(banners, condition)
    }

    filterBanners(banners: Banner[], condition: BannerQueryCondition, skipConditionIfNotPresent: boolean = false): Banner[] {
        const filteredBanners = _.filter(banners, banner => {
            if (condition.bannerVersion && condition.bannerVersion !== banner.bannerVersion)
                return false
            if (condition.vertical && banner.vertical !== condition.vertical)
                return false
            if (condition.timeInstanceWithTz &&
                (momentTz(condition.timeInstanceWithTz.date).tz(condition.timeInstanceWithTz.timezone) < momentTz(banner.startTimeWithTz.date).tz(banner.startTimeWithTz.timezone)
                    || momentTz(condition.timeInstanceWithTz.date).tz(condition.timeInstanceWithTz.timezone) > momentTz(banner.endTimeWithTz.date).tz(banner.endTimeWithTz.timezone)))
                return false
            if (condition.status && banner.status !== condition.status)
                return false

            const userAgent = condition.userAgent
            if (userAgent) {
                if (_.isNil(banner.mWebImage) && userAgent === "MBROWSER") {
                    return false
                }
                if (_.isNil(banner.webImage) && userAgent === "DESKTOP") {
                    return false
                }
                if (_.isNil(banner.appImage) && userAgent === "APP") {
                    return false
                }
            }
            if (banner.condition) {
                if (!_.isEmpty(condition.cultUserTypes)) {
                    if (banner.condition.cultUserTypes.indexOf("ALL") < 0) {
                        const overlappingSegment = banner.condition.cultUserTypes.filter(value => -1 !== condition.cultUserTypes.indexOf(value))
                        if (_.isEmpty(overlappingSegment)) {
                            return false
                        }
                    }
                } else {
                    if (!skipConditionIfNotPresent && banner.condition.cultUserTypes.indexOf("ALL") < 0) {
                        return false
                    }
                }

                if (condition.eatUserType) {
                    if (banner.condition.eatUserTypes.indexOf("ALL") < 0
                        && banner.condition.eatUserTypes.indexOf(condition.eatUserType) < 0) {
                        return false
                    }
                } else {
                    if (!skipConditionIfNotPresent && banner.condition.eatUserTypes.indexOf("ALL") < 0) {
                        return false
                    }
                }

                if (condition.areaId) {
                    if (!_.isEmpty(banner.condition.areaIds) && banner.condition.areaIds.indexOf(condition.areaId) < 0) {
                        return false
                    }
                } else {
                    if (!skipConditionIfNotPresent && !_.isEmpty(banner.condition.areaIds)) {
                        return false
                    }
                }

                if (condition.cityId) {
                    if (!_.isEmpty(banner.condition.cityIds) && banner.condition.cityIds.indexOf(condition.cityId) < 0) {
                        return false
                    }
                } else {
                    if (!skipConditionIfNotPresent && !_.isEmpty(banner.condition.cityIds)) {
                        return false
                    }
                }

                if (!_.isEmpty(condition.campaignTypes)) {
                    if (!_.isEmpty(banner.condition.campaignTypes)) {
                        if (_.isEmpty(_.intersection(condition.campaignTypes, banner.condition.campaignTypes))) {
                            return false
                        }
                    }
                } else {
                    if (!skipConditionIfNotPresent && !_.isEmpty(banner.condition.campaignTypes)) {
                        return false
                    }
                }

                if (!_.isEmpty(condition.campaignTypes)) {
                    if (!_.isEmpty(banner.condition.campaignTypes)) {
                        if (_.isEmpty(_.intersection(condition.campaignTypes, banner.condition.campaignTypes))) {
                            return false
                        }
                    }
                } else {
                    if (!skipConditionIfNotPresent && !_.isEmpty(banner.condition.campaignTypes)) {
                        return false
                    }
                }


                if (!_.isEmpty(condition.offerIds)) {
                    if (!_.isEmpty(banner.condition.offerIds)) {
                        if (_.isEmpty(_.intersection(condition.offerIds, banner.condition.offerIds))) {
                            return false
                        }
                    }
                } else {
                    if (!skipConditionIfNotPresent && !_.isEmpty(banner.condition.offerIds)) {
                        return false
                    }
                }
            }

            return true
        })

        const sortedBanners = filteredBanners.sort((a, b) => {
            return a.priority < b.priority ? -1 : a.priority > b.priority ? 1 : 0
        })

        return sortedBanners
    }

    async getBannerAction(banner: Banner, userAgent: UserAgentType): Promise<string> {
        let action: string
        if (banner.actionAttribute.actionType === "CUSTOM_ACTION")
            action = banner.actionAttribute.customActionUrl
        else {
          let seoParams: SeoUrlParams
            switch (banner.actionAttribute.actionType) {
                case "EAT_PACK_PAGE":
                    const foodPack: FoodPack = await this.catalogueService.getFoodPack(banner.actionAttribute.productId)
                     seoParams = {
                        productName: foodPack.title
                    }
                    action = ActionUtil.foodPack(banner.actionAttribute.productId, undefined, undefined, true, userAgent, seoParams)
                return
                case "CULT_DIY_PACK_PAGE":
                    const diyCultpack = await this.catalogueService.getDIYFitnessProduct(banner.actionAttribute.productId)
                    seoParams = {
                        productName: diyCultpack.title
                    }
                    action = ActionUtil.cultDiyPack(banner.actionAttribute.productId, undefined, userAgent, seoParams)
                return
                case "MIND_DIY_PACK_PAGE":
                    const diyMindpack = await this.catalogueService.getDIYMeditationProduct(banner.actionAttribute.productId)
                    seoParams = {
                        productName: diyMindpack.title
                    }
                    action = ActionUtil.mindDiyPack(banner.actionAttribute.productId, undefined, userAgent, seoParams)
                return
                default:
                    this.logger.error("PMS::DEPR BannerService::getBannerAction called", {actiontype: banner.actionAttribute.actionType, pId: banner.actionAttribute.productId})
                    action = ACTION_MAP.get(banner.actionAttribute.actionType) // PMS::TODO Update to use new actions
            }
        }
        if (action.indexOf("?") < 0) {
            action = action + "?bId=" + banner.bannerId
        } else {
            action = action + "&bId=" + banner.bannerId
        }

        if (banner.condition && !_.isEmpty(banner.condition.offerIds)) {
            action = action + "&offerIds=" + banner.condition.offerIds.join(",")
        }
        action = action.replace("product_id", banner.actionAttribute.productId)
        return action
    }

    getBannerById(id: string): Banner {
        this.promUtil.reportCacheAccess(CachingServiceType.BANNER)
        return this.cache.get(id)
    }
}

export default BannerService
