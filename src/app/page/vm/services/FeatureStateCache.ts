import { inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

@injectable()
export class FeatureStateCache {

    private redisCrudDao: ICrudKeyValue
    private appPrefix: string = "cf-app:"

    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue
    ) {
        this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
    }

    private getRedisKey(userId: string, entity: string): string {
        return `${this.appPrefix}${entity}:${userId}`
    }

    async set(userId: string, entity: string, state: string): Promise<boolean> {
        return this.redisCrudDao.create(this.getRedisKey(userId, entity), state)
    }

    async setWithExpiry(userId: string, entity: string, state: string, expiryInSeconds: number): Promise<boolean> {
        return this.redisCrudDao.createWithExpiry(this.getRedisKey(userId, entity), state, expiryInSeconds)
    }

    async unset(userId: string, entity: string): Promise<boolean> {
        return this.redisCrudDao.delete(this.getRedisKey(userId, entity))
    }

    async get(userId: string, entity: string): Promise<string> {
        return this.redisCrudDao.read(this.getRedisKey(userId, entity))
    }


    async match(userId: string, entity: string, state: string): Promise<boolean> {
        const val = await this.redisCrudDao.read(this.getRedisKey(userId, entity))
        return val === state
    }

}
