import { Container, inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import { SegmentEvaluator } from "./SegmentEvaluator"
import { UserContext, UserProfile } from "@curefit/userinfo-common"
import { Segment } from "@curefit/segment-common"
import { CFUserProfile } from "../CFUserProfile"
import * as _ from "lodash"
import { WalletBalance } from "@curefit/fitcash-common"
import { UserMappingResponse } from "./PageService"
import { ISegmentService, REDIS_SEGMENT_PREFIX, UserActionMapping } from "@curefit/vm-models"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { CACHE_CLIENT_TYPES, cacheKey, CacheService, wrapWithMethodCache } from "@curefit/cache-client"
import { TimeUtil } from "@curefit/util-common"
import * as LRUCache from "lru-cache"

export function SegmentServiceFactory(kernel: Container) {

    @injectable()
    class SegmentService implements ISegmentService {
        private segmentRedisMemCache: LRUCache<string, string>
        private redisCrudDao: ICrudKeyValue

        constructor(
            @inject(CUREFIT_API_TYPES.SegmentEvaluator) private segmentEvaluator: SegmentEvaluator,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService
        ) {
            this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("VM-CACHE")

            this.segmentRedisMemCache = new LRUCache<string, string>({
                max: 1000,
                ttl: 600_000
            })
        }

        private async getCachedSegment(segmentId: string): Promise<string> {
            if (this.segmentRedisMemCache.has(segmentId)) {
                return this.segmentRedisMemCache.get(segmentId)
            }
            const stringifiedSegment = await this.redisCrudDao.read(segmentId, REDIS_SEGMENT_PREFIX)
            this.segmentRedisMemCache.set(segmentId, stringifiedSegment)
            return stringifiedSegment
        }

        async getSegment(segmentId: string): Promise<Segment> {
            const segment = <Segment>JSON.parse(await this.getCachedSegment(segmentId))
            return (segment != null && segment.archived === false) ? segment : null
        }

        async doesUserBelongToAnySegment(segmentIds: string[], userContext: UserContext): Promise<Segment> {
            const segmentEvaluationsPromise = _.map(segmentIds, async segmentId => this.doesUserBelongToSegment(segmentId, userContext))
            const segmentEvaluations = await Promise.all(segmentEvaluationsPromise)
            return segmentEvaluations.find(segmentEvaluation => !_.isNil(segmentEvaluation))
        }

        async doesUserBelongToSegment(segmentId: string, userContext: UserContext): Promise<Segment> {
            const userProfile: CFUserProfile = userContext.userProfile
            const sessionInfo = userContext.sessionInfo
            const segment = await this.getSegment(segmentId)
            if (!segment) {
                this.logger.error("Segment not found for " + segmentId)
                return undefined
            }
            const checkUserSegment = await this.checkUserSegmentMapping(userProfile, segmentId)
            // It will allow only user belonged segments
            if (checkUserSegment === "SEGMENT_EXIST") {
                return segment
            } else if (checkUserSegment === "SEGMENT_NOT_EXIST") {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "HAMLET",
                condition: "BELONGS_TO_ANY",
                value: segment.hamlet,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "USER_AGENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.userAgents,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "AREAS",
                condition: "BELONGS_TO_ANY",
                value: segment.areaIds,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CITIES",
                condition: "BELONGS_TO_ANY",
                value: segment.cityIds,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "VERTICALS",
                condition: "BELONGS_TO_ANY",
                value: segment.verticals,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULT_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "MIND_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.mindUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "MIND_THERAPY_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.mindTherapyUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULT_PERSONAL_TRAINING_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultPTUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "LIVE_PERSONAL_TRAINING_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.livePTUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "LIVE_SGT_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.liveSGTUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CARE_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.careUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "GYM_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.gymUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "DELIVERY_CHANNEL",
                condition: "BELONGS_TO_ANY",
                value: segment.deliveryChannels,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "EMAIL_SET",
                condition: "BELONGS_TO_ANY",
                value: segment.emailSet,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "USER_TYPE",
                condition: "BELONGS_TO_ANY",
                value: segment.userType,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "PRODUCT_TYPE",
                condition: "BELONGS_TO_ANY",
                value: segment.productType,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "OFFERS",
                condition: "BELONGS_TO_ANY",
                value: segment.offerIds,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "OFFERS",
                condition: "NOT_BELONGS_TO_ALL",
                value: segment.excludedOfferIds,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            // If user email doesnot matches with segmentEmailSuffix, then it will return undefined
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "EMAIL_SUFFIX",
                condition: "BELONGS_TO_ANY",
                value: segment.emailSuffixes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            // If user email matches with segmentExcludeEmailSuffix, then it will return undefined
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "EMAIL_SUFFIX",
                condition: "NOT_BELONGS_TO_ALL",
                value: segment.excludeEmailSuffixes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            // Checking for sources like phonepe etc
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "SOURCES",
                condition: "BELONGS_TO_ANY",
                value: segment.sources,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!_.isEmpty(segment.osName) && segment.osName.toString().toLowerCase() !== sessionInfo.osName) {
                return undefined
            }
            if (!_.isEmpty(segment.minSupportedAppVersion) && sessionInfo.clientVersion < Number(segment.minSupportedAppVersion)) {
                return undefined
            }
            if (!_.isEmpty(segment.maxSupportedAppVersion) && sessionInfo.clientVersion > Number(segment.maxSupportedAppVersion)) {
                return undefined
            }
            if (!_.isEmpty(segment.minCodepushVersion) && (_.isNil(sessionInfo.cpVersion) || sessionInfo.cpVersion < Number(segment.minCodepushVersion))) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULT_DIY_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultDIYUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "MIND_DIY_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.mindDIYUserTypes,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }
            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "PLATFORM_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.platformSegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULTSPORT_ARTICLE_TYPE_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultsportArticleTypeSegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULTSPORT_BRAND_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultsportBrandSegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULTSPORT_CATEGORY_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultsportCategorySegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULTSPORT_COLLECTION_SLUG_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultsportCollectionSlugSegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }

            if (!await this.segmentEvaluator.conditionEvaluator({
                conditionField: "CULTSPORT_PRODUCT_ID_SEGMENTS",
                condition: "BELONGS_TO_ANY",
                value: segment.cultsportProductIdSegments,
                userProfile,
                sessionInfo,
                userContext
            })) {
                return undefined
            }


            if (!_.isEmpty(segment.eatUserTypes) && segment.eatUserTypes.indexOf("ALL") < 0) {
                // no implementation as eat user types not supported anymore and treaing eligible in case of any segemnt tagged alose
            }

            if (!_.isEmpty(segment.fitcashBalance)) {
                const balance: WalletBalance = await userProfile.walletBalancePromise
                if (balance) {
                    const start: number = 100 * segment.fitcashBalance.start
                    const end: number = 100 * segment.fitcashBalance.end
                    if (balance.balance < start || balance.balance > end) {
                        return undefined
                    }
                } else {
                    // segment does not evaluate if no balance is available or pageId is not whitelisted for wallet segment in VMPageBuilder
                    return undefined
                }
            }

            // Check for conditional Fields
            if (!_.isEmpty(segment.conditionalFields)) {
                const conditionalFieldPromise = segment.conditionalFields.filter(conditionalSegment => !_.isEmpty(conditionalSegment.value))
                    .map(async (conditionalSegment) => {
                        return await this.segmentEvaluator.conditionEvaluator({
                            conditionField: conditionalSegment.field, condition: conditionalSegment.condition,
                            value: conditionalSegment.value, userProfile, sessionInfo, userContext
                        })
                    })
                const conditionalFieldResponse = await Promise.all(conditionalFieldPromise)
                if (conditionalFieldResponse.some(value => value === false)) {
                    return undefined
                }
            }
            return segment
        }

        private async checkUserSegmentMapping(userProfile: UserProfile, segmentId: string): Promise<UserMappingResponse> {
            if (!userProfile.testSegmentOverride) {
                const userMappedSegmentData = await userProfile.userMappedSegments
                if (userMappedSegmentData) {
                    userProfile.testSegmentOverride = {
                        "dataExist": true,
                        "segmentIds": userMappedSegmentData.segmentIds
                    }
                } else {
                    userProfile.testSegmentOverride = { "dataExist": false }
                }
            }
            if (userProfile.testSegmentOverride.segmentIds) {
                if (userProfile.testSegmentOverride.segmentIds.includes(segmentId)) {
                    return "SEGMENT_EXIST"
                }
                return "SEGMENT_NOT_EXIST"
            }
            return "NOT_A_TEST_USER"
        }
    }

    return SegmentService
}
