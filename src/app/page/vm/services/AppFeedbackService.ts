import { isEmpty, includes, compact, map } from "lodash"
import { inject, injectable } from "inversify"
import { IEvent, IFeedback } from "@curefit/vm-common"
import {
    IEventReadOnlyDao,
    IFeedbackReadOnlyDao, IPageService,
    ISegmentService,
    UserContext,
    VM_MODELS_TYPES,
} from "@curefit/vm-models"
import { IFindQuery } from "@curefit/mongo-utils"
import { ICrudKeyValue, IRedisDao, REDIS_TYPES } from "@curefit/redis-utils"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import { AppFeedbackCountKey, AppFeedbackKey, AppFeedbackSuccessCountKey } from "../../../feedback/Feedback"
import { UserFormRequest } from "@curefit/cfs-common"
import { Tenant } from "@curefit/user-common"
import { InMemoryCacheService } from "@curefit/memory-cache"
import { BASE_TYPES, Logger } from "@curefit/base"
import { PromiseCache } from "../../../util/VMUtil"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import AppUtil from "../../../util/AppUtil"

export interface IAppFeedback {
    getAppFeedbackConfig(userContext: UserContext, feedbackIds: string[]): Promise<IFeedback[]>
    doesFormBelongToAnyUserResearchFeedback(formId: string): boolean,
    getAppFeedbackForPage(userContext: UserContext, pageId: string): Promise<IFeedback[]>
}

const TENANTS = [Tenant.CUREFIT_APP, Tenant.LIVEFIT_APP]

@injectable()
class AppFeedbackService extends InMemoryCacheService<Map<string, IFeedback[]>> implements IAppFeedback {

    readonly APPFEEDBACKS: string = "APPFEEDBACKS"
    constructor(
        @inject(VM_MODELS_TYPES.FeedbackReadOnlyDao) private feedbackReadOnlyDao: IFeedbackReadOnlyDao,
        @inject(VM_MODELS_TYPES.EventReadOnlyDao) private eventReadOnlyDao: IEventReadOnlyDao,
        @inject(REDIS_TYPES.RedisDao) private feedbackCountCache: ICrudKeyValue,
        @inject(CUREFIT_API_TYPES.AppFeedbackRedisDao) private feedbackRedisDao: IRedisDao<AppFeedbackKey, UserFormRequest>,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(BASE_TYPES.ILogger) logger: Logger,
        @inject(CUREFIT_API_TYPES.PageService) protected pageService: IPageService,
        @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
    ) {
        super(logger, 11 * 60)
        this.load("AppFeedbackService")
    }

    async loadData(): Promise<Map<string, IFeedback[]>> {
        const query: IFindQuery = {
            condition: {
                status: "LIVE",
                tenant: {"$in": TENANTS},
                "startDate.date": {$exists: true, $lte: new Date()},
                "expiryDate.date": {$exists: true, $gt: new Date()}
            }
        }
        const appEventsQuery: IFindQuery = {
            condition: {}
        }
        const rawAppFeedbacks: IFeedback[] = await this.feedbackReadOnlyDao.find(query)
        this.logger.info(`${rawAppFeedbacks} rawappfeedback for user research`)
        const events: IEvent[] = await this.eventReadOnlyDao.find(appEventsQuery)
        const appFeedbacks: IFeedback[] = await Promise.all(rawAppFeedbacks.map(async feedback => {
            feedback.configuration = this.getConfiguration(feedback.configuration as string[][], events)
            return feedback
        }))
        this.logger.info(`${appFeedbacks} appfeedbacks for user research`)
        const cache = new Map<string, IFeedback[]>()
        cache.set(this.APPFEEDBACKS, appFeedbacks)
        return cache
    }

    async getAppFeedbackConfig(userContext: UserContext, feedbackIds: string[]): Promise<IFeedback[]> {
        if (feedbackIds.length) {
            const appFeedbacks: IFeedback[] = this.cache.get(this.APPFEEDBACKS).filter(feedback => {
              return includes(feedbackIds, feedback.feedbackId)
            })
            const hasSubmitted = await this.doesUserAlreadySubmitted(userContext)
            if (hasSubmitted) {
                this.logger.info(`${userContext.userProfile.userId} already submitted app feedback`)
                return []
            }
            return compact(await Promise.all(appFeedbacks.map(async feedback => {
                if (await this.hasReachedMaxSuccessUserCount(feedback)) {
                    this.logger.info(`${feedback.feedbackId} has reached to max success user count`)
                    return null
                }
                if (await this.hasReachedMaxUserCount(feedback)) {
                    this.logger.info(`${feedback.feedbackId} has reached to max user count`)
                    return null
                }
                if (!userContext.userProfile.promiseMapCache) {
                    userContext.userProfile.promiseMapCache ??= new PromiseCache(this.serviceInterfaces)
                }
                if (feedback.segment?.length && !(await this.segmentService.doesUserBelongToAnySegment(feedback.segment, userContext))) {
                    this.logger.info(`${userContext.userProfile.userId} doesn't belongs to ${feedback.feedbackId} segments`)
                    return null
                }
                return feedback
            })))
        }
        return []
    }

    doesFormBelongToAnyUserResearchFeedback(formId: string): boolean {
        return includes(map(this.cache.get(this.APPFEEDBACKS), "formId"), formId)
    }

    async getAppFeedbackForPage(userContext: UserContext, pageId: string): Promise<IFeedback[]> {
        const {feedbackIds = []} = await this.pageService.getPage(pageId, AppUtil.getTenantFromUserContext(userContext))
        return this.getAppFeedbackConfig(userContext, feedbackIds)
    }

    private getConfiguration (configuration: string[][], events: IEvent[]): IEvent[][] {
        return configuration.map(config => {
            return config.map(eventId => {
                return events.find(evt => evt.eventId === eventId)
            })
        })
    }

    private async hasReachedMaxUserCount ({formId, userCount}: IFeedback): Promise<boolean> {
        return parseInt(await this.feedbackCountCache.read(AppFeedbackCountKey.get(formId))) >= userCount
    }

    private async hasReachedMaxSuccessUserCount ({formId, successUserCount}: IFeedback): Promise<boolean> {
        return parseInt(await this.feedbackCountCache.read(AppFeedbackSuccessCountKey.get(formId))) >= successUserCount
    }

    private async doesUserAlreadySubmitted(userContext: UserContext): Promise<boolean> {
        return !isEmpty(await this.feedbackRedisDao.read(AppFeedbackKey.from(userContext.userProfile.userId)))
    }
}

export default AppFeedbackService
