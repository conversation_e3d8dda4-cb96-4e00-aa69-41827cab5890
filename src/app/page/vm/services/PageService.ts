import { Container, inject, injectable } from "inversify"
import { IEventInterventionMapping, Page, WidgetType } from "@curefit/vm-common"
import {
    getVMPageRedisKey,
    getVMWidgetRedisKey,
    IAnnouncementReadOnlyDao, IBaseWidget, ISegmentService,
    REDIS_ANNOUNCEMENT_KEY, REDIS_EVENT_INTERVENTION_MAPPING_KEY,
    REDIS_SEGMENT_PREFIX, REDIS_WIDGET_PREFIX, REDIS_WIDGET_TEMPLATE_PREFIX,
    WidgetInstance
} from "@curefit/vm-models"
import { Logger, BASE_TYPES } from "@curefit/base"
import { IPageReadOnlyDao } from "@curefit/vm-models"
import { IWidgetInstanceReadOnlyDao } from "@curefit/vm-models"
import * as _ from "lodash"
import { ISegmentReadOnlyDao, SEGMENT_TYPES } from "@curefit/segment-sdk"
import { Segment } from "@curefit/segment-common"
import { IWidgetTemplateReadOnlyDao, VM_MODELS_TYPES } from "@curefit/vm-models"
import { WidgetTemplate } from "@curefit/vm-common"
import { ErrorFactory } from "@curefit/error-client"
import { IPageService } from "@curefit/vm-models"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { SegmentEvaluator } from "./SegmentEvaluator"
import { ErrorCodes } from "../../../error/ErrorCodes"
import { IAnnouncement } from "@curefit/vm-common/dist/src/models/Announcement"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { REDIS_PAGE_PREFIX } from "@curefit/vm-models"
import { Tenant } from "@curefit/base-common"
import * as LRUCache from "lru-cache"
import { plainToClass } from "class-transformer"

export type UserMappingResponse = "SEGMENT_EXIST" | "SEGMENT_NOT_EXIST" | "NOT_A_TEST_USER"

export function PageServiceFactory(kernel: Container) {

    @injectable()
    class PageService implements IPageService {

        private redisCrudDao: ICrudKeyValue
        private pageRedisMemCache: LRUCache<string, Record<string, string>>
        private widgetRedisMemCache: LRUCache<string, string>
        private widgetTemplateRedisMemCache: LRUCache<string, string>

        constructor(
            @inject(VM_MODELS_TYPES.PageReadOnlyDao) private pageDao: IPageReadOnlyDao,
            @inject(VM_MODELS_TYPES.WidgetInstanceReadOnlyDao) private widgetInstanceReadOnlyDao: IWidgetInstanceReadOnlyDao,
            @inject(SEGMENT_TYPES.SegmentReadOnlyDao) private segmentReadOnlyDao: ISegmentReadOnlyDao,
            @inject(VM_MODELS_TYPES.WidgetTemplateReadOnlyDao) private widgetTemplateReadOnlyDao: IWidgetTemplateReadOnlyDao,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
            @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
            @inject(CUREFIT_API_TYPES.SegmentEvaluator) private segmentEvaluator: SegmentEvaluator,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
            @inject(VM_MODELS_TYPES.AnnouncementReadOnlyDao) private announcementReadOnlyDoa: IAnnouncementReadOnlyDao,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue
        ) {
            this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("VM-CACHE")

            this.pageRedisMemCache = new LRUCache<string, Record<string, string>>({
                max: 1000,
                ttl: 300_000
            })

            this.widgetRedisMemCache = new LRUCache<string, string>({
                max: 1000,
                ttl: 600_000
            })

            this.widgetTemplateRedisMemCache = new LRUCache<string, string>({
                max: 1000,
                ttl: 800_000
            })

        }

        protected reportError(err: any): void {
            this.rollbarService.sendError(err)
        }

        private async getCachedPages(key: string): Promise<Record<string, string>> {
            if (this.pageRedisMemCache.has(key)) {
                return this.pageRedisMemCache.get(key)
            }
            const stringifiedPagesRecord = await this.redisCrudDao.getHashAllFields(key, REDIS_PAGE_PREFIX)
            this.pageRedisMemCache.set(key, stringifiedPagesRecord)
            return stringifiedPagesRecord
        }

        private async getCachedWidget(key: string): Promise<string> {
            if (this.widgetRedisMemCache.has(key)) {
                return this.widgetRedisMemCache.get(key)
            }
            const stringifiedWidget = await this.redisCrudDao.read(key, REDIS_WIDGET_PREFIX)
            this.widgetRedisMemCache.set(key, stringifiedWidget)
            return stringifiedWidget
        }

        private async getCachedWidgetTemplate(key: string): Promise<string> {
            if (this.widgetTemplateRedisMemCache.has(key)) {
                return this.widgetTemplateRedisMemCache.get(key)
            }
            const stringifiedWidgetTemplate = await this.redisCrudDao.read(key, REDIS_WIDGET_TEMPLATE_PREFIX)
            this.widgetTemplateRedisMemCache.set(key, stringifiedWidgetTemplate)
            return stringifiedWidgetTemplate
        }


        async getPage(pageId: string, tenant: Tenant): Promise<Page> {
            const pagesRecord = await this.getCachedPages(getVMPageRedisKey(tenant, pageId))
            const pages: Page[] = _.map(Object.keys(pagesRecord), key => <Page>JSON.parse(pagesRecord[key]))
            if (_.isEmpty(pages)) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("pageId: " + pageId + " not found in cache").build()
            }
            const currentTime = new Date().getTime()
            const page = pages.find(page => {
                if ((page.startTimeWithTz && new Date(page.startTimeWithTz.date).getTime() <= currentTime) &&
                    (page.endTimeWithTz && new Date(page.endTimeWithTz.date).getTime() >= currentTime) &&
                    page.archived === false) {
                    if (process.env.APP_ENV === "ALPHA" && (page.status === "DOGFOODING" || page.status === "LIVE")) {
                        return true
                    } else if (page.status !== "DOGFOODING") {
                        return true
                    }
                } else {
                    return false
                }
            })
            if (!page) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withMessage(`Page ${pageId} not accessible currently`).build()
            }
            return page
        }

        async getAnnouncement(): Promise<IAnnouncement[]> {
            let announcementsRecord: Record<string, string>
            try {
                announcementsRecord = await this.redisCrudDao.getHashAllFields("", REDIS_ANNOUNCEMENT_KEY)
            } catch (e) {
                this.logger.error("Error getting announcements" + e.stack)
                this.rollbarService.sendError(e)
            }
            const announcements = _.map(Object.keys(announcementsRecord), key => <IAnnouncement>JSON.parse(announcementsRecord[key]))
            const currentTime = new Date().getTime()
            return announcements.filter(announcement => (announcement != null && new Date(announcement.startDate.date).getTime() <= currentTime) &&
                (announcement.expiryDate && new Date(announcement.expiryDate.date).getTime() >= currentTime) &&
                announcement.status === "LIVE").sort((a, b) => a.priority < b.priority ? 1 : -1)
        }

        async getSegment(segmentId: string): Promise<Segment> {
            return this.segmentService.getSegment(segmentId)
        }

        async getWidget(widgetId: string, tenant: Tenant): Promise<WidgetInstance> {
            let widgetStr: string
            try {
                widgetStr = await this.getCachedWidget(getVMWidgetRedisKey(tenant, widgetId))
            } catch (e) {
                this.logger.error("Error getting widget from redis for widgetId:" + widgetId + e.stack)
                this.reportError(e)
            }
            const widget = <WidgetInstance>JSON.parse(widgetStr)
            if (process.env.APP_ENV === "ALPHA")
                return (widget != null && (widget.status === "LIVE" || widget.status === "DOGFOODING") && widget.archived === false) ? widget : null
            return (widget != null && widget.status === "LIVE" && widget.archived === false) ? widget : null
        }

        async getWidgets(widgetIds: string[], tenant: Tenant): Promise<WidgetInstance[]> {
            return Promise.all(_.map(widgetIds, async widgetId => this.getWidget(widgetId, tenant)))
        }

        async getWidgetTemplates(templateIds: string[]): Promise<WidgetTemplate[]> {
            return Promise.all(_.map(templateIds, async templateId => this.getWidgetTemplate(templateId)))
        }

        async getWidgetTemplate(widgetTemplateId: string): Promise<WidgetTemplate> {
            let widgetTemplateStr: string
            try {
                widgetTemplateStr = await this.getCachedWidgetTemplate(widgetTemplateId)
            } catch (e) {
                this.logger.error("Error getting widget template from redis for templateId:" + widgetTemplateId + e.stack)
                this.reportError(e)
            }
            return <WidgetTemplate>JSON.parse(widgetTemplateStr)
        }

        public async getWidgetClass(widgetInstance: WidgetInstance, widgetClass: any): Promise<IBaseWidget> {
            const ret = new widgetClass()
            Object.assign(ret, _.get(widgetInstance, "widgetData", {}))
            return ret
        }
    }

    return PageService
}
