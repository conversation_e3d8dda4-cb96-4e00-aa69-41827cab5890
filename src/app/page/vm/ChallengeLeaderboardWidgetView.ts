import { injectable } from "inversify"
import { IBaseWidget, UserContext, ChallengeLeaderboardWidget, Action } from "@curefit/vm-models"
import { CFServiceInterfaces } from "../vm/ServiceInterfaces"
import { Challenge, EnrolmentResponse, LeaderBoardUIConfig, Standing } from "@curefit/riddler-common"
import ActionUtil from "../../util/ActionUtil"
import * as _ from "lodash"
import * as moment from "moment"

interface Rank {
    name: string,
    score: number,
    rank: number,
    imageUrl?: string
    isLoginUser: boolean
}
interface UserChallenge {
    name: string,
    userRanks: Rank[],
    topRanks: Rank[],
    action: Action
}

@injectable()
export class ChallengeLeaderboardWidgetView extends ChallengeLeaderboardWidget {
    data: UserChallenge[] = []

    async buildView(interfaces: CFServiceInterfaces, userContext: UserContext, queryParams: { [filterName: string]: string }): Promise<IBaseWidget> {
        const userId = userContext.userProfile.userId
        const {enrolments} = await interfaces.riddlerService.getAllEnrolments(userId, true, true)
        const filteredEnrolments = []
        const squadChallengeIds = new Set<string>()
        for (const enrolment of enrolments) {
            const goalBasedChallenge = _.get(enrolment, "challenge.goalBased", false) ?? false
            if (goalBasedChallenge === false) {
                // Hack to prevent duplicate squad entries
                if (enrolment.challenge.challengeType === "TEAM") {
                    if (!squadChallengeIds.has(enrolment.challengeId)) {
                        squadChallengeIds.add(enrolment.challengeId)
                        filteredEnrolments.push(enrolment)
                    }
                } else {
                    filteredEnrolments.push(enrolment)
                }
            }
        }

        const finalEnrolments = filteredEnrolments.filter(e => e.status !== "FORCE_EXIT")
        for (const enrolment of finalEnrolments) {
            const challenge = enrolment.challenge
            const challengeStandings = enrolment.challengeStandings
            if (!_.isEmpty(challengeStandings) && challenge.status === "ACTIVE") {
                const currentTimeStamp = moment()
                const endDtDiff = moment(challenge.endDate).endOf("day").diff(currentTimeStamp)
                interfaces.logger.info(`ChallengeLeaderboardWidgetView::enrolment ${enrolment}`)
                if (endDtDiff > 0) {
                    let leaderboardUiConfig
                    if (challenge.challengeType !== "TEAM") {
                        leaderboardUiConfig = this.getDefaultLearboardConfig(enrolment)
                    } else {
                        leaderboardUiConfig = enrolment.challenge.uiConfig.leaderboardUIConfigs.find(lconfig => lconfig.leaderBoardConfigId === "team")
                    }
                    const top3Standings = await interfaces.riddlerService.getLeaderboard(challenge.challengeId, 1, 3, leaderboardUiConfig.leaderBoardConfigId)
                    const topRanks: Rank[] = await this.getRankDetails(userId, interfaces, top3Standings.entries, top3Standings.challenge)

                    const standingsMap = _.keyBy(challengeStandings, s => s.leaderboardId.split("::")[0])
                    const standings = standingsMap[leaderboardUiConfig.leaderBoardConfigId]
                    const userRanks = await this.getRankDetails(userId, interfaces, this.filterNearbyStanding(standings.nearbyStandings, userId, standings.standings ? standings.standings.groupId : null), enrolment.challenge)
                    this.data.push({
                        name: challenge.title,
                        userRanks,
                        topRanks,
                        action: this.getAction(enrolment)
                    })
                }
            }
        }
        if (!_.isEmpty(this.data)) {
            return this
        } else {
            return null
        }
    }

    getDefaultLearboardConfig(enrolment: EnrolmentResponse): LeaderBoardUIConfig {
        return {
            displayText: "Global",
            leaderBoardConfigId: "DEFAULT",
            scoreText: enrolment.challenge.uiConfig.scoreText
        }
    }

    getAction(enrolment: EnrolmentResponse): Action {
        if (enrolment.challenge.challengeType === "TEAM") {
            return {
                actionType: "NAVIGATION",
                url: `curefit://squadchallengepage?challengeId=${enrolment.challengeId}&prevPage=enrolledchallenges`
            }
        }
        return {
            actionType: "NAVIGATION",
            url: ActionUtil.getChallengeDetailsUrl(enrolment.challengeId, "ENROLMENT", enrolment.enrolmentId)
        }
    }

    private async getGroupParticipantDetails(userId: string, interfaces: CFServiceInterfaces, entries: Standing[], challenge: Challenge): Promise<Rank[]> {
        if (_.isEmpty(entries))
            return []
        const groupIds: string[] = []
        for (const entry of entries) {
            groupIds.push(entry.groupId)
        }
        const { groups } = await interfaces.riddlerService.getGroups(_.uniq(_.compact(groupIds)))
        const groupMap = _.keyBy(groups, "groupId")
        const ranks: Rank[] = []
        for (const standing of entries) {
            ranks.push({
                name: groupMap[standing.groupId].name,
                score: standing.score,
                rank: standing.rank,
                imageUrl: null,
                isLoginUser: false
            })
        }
        return ranks
    }
    private async getParticipantDetails(userId: string, interfaces: CFServiceInterfaces, entries: Standing[], challenge: Challenge): Promise<Rank[]> {
        if (_.isEmpty(entries))
            return []
        const userIds: string[] = []
        for (const entry of entries) {
            userIds.push(entry.userId)
        }
        const userMap = _.keyBy(await interfaces.userService.getUsers(_.uniq(_.compact(userIds))), "id")
        const ranks: Rank[] = []
        for (const standing of entries) {
            ranks.push({
                name: standing.userId && standing.userId === userId ? "You" : `${userMap[standing.userId].firstName || "Curefit User"} ${(userMap[standing.userId].lastName || "").trim()}`,
                score: standing.score,
                rank: standing.rank,
                imageUrl: userMap[standing.userId]?.profilePictureUrl,
                isLoginUser: standing.userId && standing.userId === userId,
            })
        }
        return ranks
    }


    private async getRankDetails(userId: string, interfaces: CFServiceInterfaces, entries: Standing[], challenge: Challenge): Promise<Rank[]> {
        if (challenge.challengeType === "TEAM") {
            return await this.getGroupParticipantDetails(userId, interfaces, entries, challenge)
        } else {
            return await this.getParticipantDetails(userId, interfaces, entries, challenge)
        }
    }

    private filterNearbyStanding(nearbyStandings: Standing[], cfUserId: string, groupId: string): Standing[] {
        const filtered: Standing[] = []
        for (let i = 0; i < nearbyStandings.length; i++) {
            if ((!_.isEmpty(groupId) && groupId === nearbyStandings[i].groupId) || (cfUserId === nearbyStandings[i].userId)) {
                if (i > 0 && ((i + 1) < nearbyStandings.length)) { // with both left and right
                    filtered.push(nearbyStandings[i - 1])
                    filtered.push(nearbyStandings[i])
                    filtered.push(nearbyStandings[i + 1])
                } else if (i === nearbyStandings.length - 1) { // with only left
                    if ((i - 2) >= 0) {
                        filtered.push(nearbyStandings[i - 2])
                    }
                    if ((i - 1) >= 0) {
                        filtered.push(nearbyStandings[i - 1])
                    }
                    filtered.push(nearbyStandings[i])
                } else if (i === 0) { // with only right
                    filtered.push(nearbyStandings[i])
                    if ((i + 1) < nearbyStandings.length) {
                        filtered.push(nearbyStandings[i + 1])
                    }
                    if ((i + 2) < nearbyStandings.length) {
                        filtered.push(nearbyStandings[i + 2])
                    }
                }
                break
            }
        }
        return filtered
    }
}
