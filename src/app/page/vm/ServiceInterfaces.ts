import { IOfferServiceV2 } from "@curefit/offer-service-client"
import { ICultServiceOld, ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import {
    ICatalogueService,
    CATALOG_CLIENT_TYPES,
    ICatalogServiceV3Reader,
    ICatalogueServicePMS
} from "@curefit/catalog-client"
import { IBannerService, ISegmentService, VM_MODELS_TYPES } from "@curefit/vm-models"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES, ICPAService } from "@curefit/diy-client"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { IActivityStoreReadonlyDao, LOGGING_MODELS_TYPES } from "@curefit/logging-models"
import { inject, injectable } from "inversify"
import { IServiceInterfaces } from "@curefit/vm-models"
import { IUserTestService, USERTEST_CLIENT_TYPES } from "@curefit/user-test-client"
import { IHealthfaceService, ALBUS_CLIENT_TYPES } from "@curefit/albus-client"
import { ICapacityService } from "@curefit/masterchef-client"
import { IDeliveryAreaService, DELIVERY_CLIENT_TYPES, IDeliverySlotService } from "@curefit/delivery-client"
import { IMenuService, CAESAR_CLIENT_TYPES } from "@curefit/caesar-client"
import CUREFIT_API_TYPES from "../../../config/ioc/types"
import IProductBusiness from "../../product/IProductBusiness"
import { ICerberusService } from "@curefit/vm-models"
import MealPackPageConfig from "../../pack/MealPackPageConfig"
import { HERCULES_CLIENT_TYPES, IHerculesService } from "@curefit/hercules-client"
import { CLSUtil, Logger, BASE_TYPES } from "@curefit/base"
import { ProductCategoryService, PRODUCT_SERVICE_TYPES, IBrandService } from "@curefit/product-service"
import { IGearService, GEARVAULT_CLIENT_TYPES } from "@curefit/gearvault-client"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { IPageService } from "@curefit/vm-models"
import { IWebActionTransformer } from "@curefit/util-common"
import TeleconsultationDetailsPageConfig from "../../care/TeleconsultationDetailsPageConfig"
import { IGMFClient as IGoalManagementService, GMF_CLIENT_TYPES } from "@curefit/gmf-client"
import { EAT_TYPES, IFoodCategoryService, SalesCategoryService } from "@curefit/eat"
import { CacheHelper } from "../../util/CacheHelper"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import WidgetBuilder from "./WidgetBuilder"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { FITCLUB_CLIENT_TYPES, IFitClubService } from "@curefit/fitclub-client"
import { KiosksDemandService, MASTERCHEF_CLIENT_TYPES } from "@curefit/masterchef-client"
import { FITCASH_CLIENT_TYPES, IFitcashService } from "@curefit/fitcash-client"
import { RECOMMENDATION_CLIENT_TYPES, IRecommendationService } from "@curefit/recommendation-client"
import { ICrudKeyValue, IRedisDao, REDIS_TYPES } from "@curefit/redis-utils"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { ILoggingService, LOGGING_CLIENT_TYPES } from "@curefit/logging-client"
import { RollbarService, ERROR_COMMON_TYPES } from "@curefit/error-common"
import { IEatApiService, EAT_API_CLIENT_TYPES } from "@curefit/eat-api-client"
import { ICultBusiness } from "../../cult/CultBusiness"
import { AnnouncementBusiness } from "../../announcement/AnnouncementBusiness"
import IWidgetDataProvider from "../../eat/clp/IWidgetDataProvider"
import FitnessReportPageConfig from "../../cult/fitnessreport/FitnessReportPageConfig"
import { FLASH_CLIENT_TYPES, IFlashService } from "@curefit/flash-client"
import { ICartShipmentReadWriteDao, SHIPMENT_MODELS_TYPES } from "@curefit/shipment-models"
import GearCatalogueLandingPageService from "../GearCatalogueLandingPageService"
import { IMyGateService } from "@curefit/flash-client"
import { GYMFIT_CLIENT_TYPES, IGymfitService } from "@curefit/gymfit-client"
import GymfitPackPageConfig from "../../gymfit/GymfitPackPageConfig"
import { IGymfitBusiness } from "../../gymfit/GymfitBusiness"
import IMealPlannerBusiness from "../../mealplanner/IMealPlannerBusiness"
import { BranchService } from "../../common/branch/BranchService"
import { ClassInviteLinkCreator } from "../../cult/invitebuddy/ClassInviteLinkCreator"
import UserActionMappingBusiness from "../../error/UserActionMappingBusiness"
import { IRiddlerService, RIDDLER_CLIENT_TYPES, RiddlerCacheService } from "@curefit/riddler-client"
import IssueBusiness from "../../crm/IssueBusiness"
import { ILocationService, LOCATION_SERVICE_TYPES } from "@curefit/location-service"
import { IWholeFitService, WHOLE_FIT_API_CLIENT_TYPES } from "@curefit/wholefit-api-client"
import { ConfigService, PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { ExpressionBuilderFactory, EXPRESSION_TYPES } from "@curefit/expression-utils"
import { CFS_TYPES } from "@curefit/cfs-client"
import { ICFSClient as IFormService } from "@curefit/cfs-client/dist/src/ICFSClient"
import { IRIS_CLIENT_TYPES, InAppNotificationsService } from "@curefit/iris-client"
import { ISocialService, SOCIAL_CLIENT_TYPES, SocialService } from "@curefit/social-client"
import { FitclubBusiness } from "../../fitclub/FitclubBusiness"
import {
    ISegmentationCacheClient,
    SEGMENTATION_CLIENT_TYPES,
    ISegmentationClient
} from "@curefit/segmentation-service-client"
import { CareBusiness, ICareBusiness } from "../../care/CareBusiness"
import { Announcement, AnnouncementKey } from "../../announcement/Announcement"
import DigitalLeagueSectionViewBuilder from "../../digital/DigitalLeagueSectionViewBuilder"
import { IRewardService, REWARD_CLIENT_TYPES } from "@curefit/reward-client"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import { IDeepLinkService } from "../../deeplink/DeepLinkService"
import { OfferServiceV3 } from "@curefit/offer-service-client"
import DigitalSocialLeagueTabPageViewBuilder from "../../digital/DigitalSocialLeagueTabPageViewBuilder"
import { ChallengeCache, RIDDLER_CACHE_TYPES } from "@curefit/riddler-cache"
import { OLLIVANDER_CLIENT_TYPES, IOllivanderCityService } from "@curefit/ollivander-node-client"
import { REPORT_ISSUES_CLIENT_TYPES, ISupportArticleService } from "@curefit/report-issues-client"
import {
    RASHI_CLIENT_TYPES,
    UserAttributeClient,
    UserAttributeCacheClient,
    IUserAttributeCacheClient
} from "@curefit/rashi-client"
import { FUSE_CLIENT_TYPES, IDiagnosticService } from "@curefit/fuse-node-client"
import { UserFitnessPlanControllerServiceInterface, UFS_CLIENT_TYPES } from "@curefit/ufs-client"
import { PlatformSegmentWrapper } from "../../common/PlatformSegmentWrapper"
import { FOODWAY_CLIENT_TYPES, IFoodwayApiService } from "@curefit/foodway-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { ICultBikeService, CULT_BIKE_TYPES } from "@curefit/cult-bike-client"
import { CAFE_API_CLIENT_TYPES, ICafeProductService } from "@curefit/cafe-api-client"
import { CONSTELLO_CLIENT_TYPES, IGiftCardService } from "@curefit/constello-client"
import GiftCardViewBuilder from "../../giftcards/GiftCardViewBuilder"
import { IReferralReadOnlyDao, REFERRAL_CLIENT_TYPES } from "@curefit/referral-client"
import { GiftCardTransformationService } from "../../referral/myreferrals/GiftCardTransformationService"
import { ReferralTransformationService } from "../../referral/myreferrals/ReferralTransformationService"
import { OMS_API_CLIENT_TYPES, IOrderService } from "@curefit/oms-api-client"
import { FeatureStateCache } from "./services/FeatureStateCache"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"
import { IMaxmindService, MAXMIND_CLIENT_TYPES } from "@curefit/maxmind-client"

export interface CFServiceInterfaces extends IServiceInterfaces {
    cityService: ICityService
    inAppNotificationsService: InAppNotificationsService
    productBusiness: IProductBusiness
    mealPackPageConfig: MealPackPageConfig
    segmentationClient: ISegmentationClient
    tcDetailsPageConfig: TeleconsultationDetailsPageConfig
    gmfService: IGoalManagementService
    foodCategoryService?: IFoodCategoryService
    userCache: CacheHelper
    mealPlannerBusiness: IMealPlannerBusiness
    fitcashService: IFitcashService
    recommendationService: IRecommendationService
    loggingService: ILoggingService
    rollbarService: RollbarService
    deliverySlotService: IDeliverySlotService
    cultBusiness: ICultBusiness
    announcementBusiness: AnnouncementBusiness
    gymfitService: IGymfitService
    fitClubService: IFitClubService
    rewardService: IRewardService
    fitnessReportPageConfig: FitnessReportPageConfig
    gearCLPService: GearCatalogueLandingPageService
    marketplaceDataProvider: IWidgetDataProvider
    gymfitPackPageConfig: GymfitPackPageConfig
    gymfitBusiness: IGymfitBusiness
    wholefitService: IWholeFitService
    catalogService: ICatalogServiceV3Reader
    branchService: BranchService
    issueBusiness: IssueBusiness
    classInviteLinkCreator: ClassInviteLinkCreator
    herculeService: IHerculesService
    hamletBusiness: HamletBusiness
    locationService: ILocationService
    riddlerService: IRiddlerService
    salesCategoryService: SalesCategoryService
    brandService: IBrandService
    queueService: IQueueService
    userActionMappingBusiness: UserActionMappingBusiness
    configService: ConfigService
    expressionBuilderFactory: ExpressionBuilderFactory
    formService: IFormService
    socialService: ISocialService
    fitclubBusiness: FitclubBusiness
    careBusiness: ICareBusiness
    digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder
    deeplinkService: IDeepLinkService
    announcementDao?: IRedisDao<AnnouncementKey, Announcement>
    cpaService: ICPAService
    digitalSocialLeagueTabPageViewBuilder: DigitalSocialLeagueTabPageViewBuilder,
    challengeCache: ChallengeCache,
    riddlerCacheService: RiddlerCacheService,
    userAttributeClient: IUserAttributeCacheClient,
    supportArticleService: ISupportArticleService,
    offerServiceV3: OfferServiceV3,
    clsUtil: CLSUtil,
    userFitnessPlanControllerService: UserFitnessPlanControllerServiceInterface
    foodwayService: IFoodwayApiService,
    membershipService: IMembershipService
    cultBikeClient: ICultBikeService,
    cafeProductService: ICafeProductService,
    giftCardService: IGiftCardService,
    referralDao: IReferralReadOnlyDao,
    giftCardTransformationService: GiftCardTransformationService,
    referralTransformationService: ReferralTransformationService,
    featureStateCache: FeatureStateCache,
    offlineFitnessPackService: IOfflineFitnessPackService
    maxmindService: IMaxmindService
}

@injectable()
class ServiceInterfaces implements CFServiceInterfaces {
    constructor(@inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) public offerService: IOfferServiceV2,
                @inject(CULT_CLIENT_TYPES.CultFitService) public cultFitService: ICultServiceOld,
                @inject(CULT_CLIENT_TYPES.ICultService) public cultFitServiceNew: ICultService,
                @inject(IRIS_CLIENT_TYPES.InAppNotificationsService) public inAppNotificationsService: InAppNotificationsService,
                @inject(CULT_CLIENT_TYPES.MindFitService) public mindFitService: ICultServiceOld,
                @inject(GYMFIT_CLIENT_TYPES.GymfitService) public gymfitService: IGymfitService,
                @inject(QUEST_CLIENT_TYPES.IQuestService) public questService: IQuestService,
                @inject(LOCATION_TYPES.CityService) public cityService: ICityService,
                @inject(CUREFIT_API_TYPES.PageService) public pageService: IPageService,
                @inject(SQS_CLIENT_TYPES.QueueService) public queueService: IQueueService,
                @inject(SEGMENTATION_CLIENT_TYPES.SegmentationClient) public segmentationClient: ISegmentationClient,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) public catalogueService: ICatalogueService,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) public catalogueServicePMS: ICatalogueServicePMS,
                @inject(CATALOG_CLIENT_TYPES.CatalogServiceV3Reader) public catalogService: ICatalogServiceV3Reader,
                @inject(CUREFIT_API_TYPES.MealPlannerBusiness) public mealPlannerBusiness: IMealPlannerBusiness,
                @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) public diyService: IDIYFulfilmentService,
                @inject(MASTERCHEF_CLIENT_TYPES.CapacityService) public capacityService: ICapacityService,
                @inject(CUREFIT_API_TYPES.BannerService) public bannerService: IBannerService,
                @inject(USERTEST_CLIENT_TYPES.UserTestService) public userTestService: IUserTestService,
                @inject(HAMLET_TYPES.HamletBusiness) public hamletBusiness: HamletBusiness,
                @inject(DELIVERY_CLIENT_TYPES.DeliveryAreaService) public deliveryAreaService: IDeliveryAreaService,
                @inject(LOGGING_MODELS_TYPES.ActivityStoreReadonlyDao) public activityStoreDao: IActivityStoreReadonlyDao,
                @inject(CAESAR_CLIENT_TYPES.MenuService) public menuService: IMenuService,
                @inject(CUREFIT_API_TYPES.ProductBusiness) public productBusiness: IProductBusiness,
                @inject(CUREFIT_API_TYPES.CerberusServiceV2) public cerberusService: ICerberusService,
                @inject(CUREFIT_API_TYPES.MealPackPageConfig) public mealPackPageConfig: MealPackPageConfig,
                @inject(BASE_TYPES.ILogger) public logger: Logger,
                @inject(PRODUCT_SERVICE_TYPES.ProductCategoryService) public categoryService: ProductCategoryService,
                @inject(ALBUS_CLIENT_TYPES.HealthfaceService) public healthfaceService: IHealthfaceService,
                @inject(ALBUS_CLIENT_TYPES.CultPersonalTrainingService) public cultPTService: IHealthfaceService,
                @inject(GEARVAULT_CLIENT_TYPES.GearService) public gearService: IGearService,
                @inject(OMS_API_CLIENT_TYPES.OrderService) public orderService: IOrderService,
                @inject(USER_CLIENT_TYPES.IUserService) public userService: IUserService,
                @inject(EAT_TYPES.FoodCategoryService) public foodCategoryService: IFoodCategoryService,
                @inject(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig) public tcDetailsPageConfig: TeleconsultationDetailsPageConfig,
                @inject(GMF_CLIENT_TYPES.IGMFClient) public gmfService: IGoalManagementService,
                @inject(CUREFIT_API_TYPES.WebActionTransformerUtil) public webActionTransformerUtil: IWebActionTransformer,
                @inject(CUREFIT_API_TYPES.CacheHelper) public userCache: CacheHelper,
                @inject(FITCLUB_CLIENT_TYPES.IFitClubService) public fitClubService: IFitClubService,
                @inject(REWARD_CLIENT_TYPES.IRewardService) public rewardService: IRewardService,
                @inject(CUREFIT_API_TYPES.WidgetBuilder) public widgetBuilder: WidgetBuilder,
                @inject(REDIS_TYPES.RedisDao) public redisDao: ICrudKeyValue,
                @inject(MASTERCHEF_CLIENT_TYPES.KiosksDemandService) public kiosksDemandService: KiosksDemandService,
                @inject(FITCASH_CLIENT_TYPES.FitcashService) public fitcashService: IFitcashService,
                @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) public recommendationService: IRecommendationService,
                @inject(LOGGING_CLIENT_TYPES.LoggingService) public loggingService: ILoggingService,
                @inject(ERROR_COMMON_TYPES.RollbarService) public rollbarService: RollbarService,
                @inject(DELIVERY_CLIENT_TYPES.DeliverySlotService) public deliverySlotService: IDeliverySlotService,
                @inject(CUREFIT_API_TYPES.CultBusiness) public cultBusiness: ICultBusiness,
                @inject(EAT_API_CLIENT_TYPES.IEatApiService) public eatApiClientService: IEatApiService,
                @inject(CUREFIT_API_TYPES.AnnouncementBusiness) public announcementBusiness: AnnouncementBusiness,
                @inject(CUREFIT_API_TYPES.EatMarketplaceDataProvider) public marketplaceDataProvider: IWidgetDataProvider,
                @inject(CUREFIT_API_TYPES.FitnessReportPageConfig) public fitnessReportPageConfig: FitnessReportPageConfig,
                @inject(FLASH_CLIENT_TYPES.FlashService) public flashService: IFlashService,
                @inject(SHIPMENT_MODELS_TYPES.CartShipmentReadwriteDao) public cartShipmentDao: ICartShipmentReadWriteDao,
                @inject(FLASH_CLIENT_TYPES.MyGateService) public myGateService: IMyGateService,
                @inject(CUREFIT_API_TYPES.GearCatalogueLandingPageService) public gearCLPService: GearCatalogueLandingPageService,
                @inject(CUREFIT_API_TYPES.GymfitBusiness) public gymfitBusiness: IGymfitBusiness,
                @inject(CUREFIT_API_TYPES.GymfitPackPageConfig) public gymfitPackPageConfig: GymfitPackPageConfig,
                @inject(CUREFIT_API_TYPES.BranchService) public branchService: BranchService,
                @inject(CUREFIT_API_TYPES.IssueBusiness) public issueBusiness: IssueBusiness,
                @inject(WHOLE_FIT_API_CLIENT_TYPES.IWholeFitService) public wholefitService: IWholeFitService,
                @inject(CUREFIT_API_TYPES.ClassInviteLinkCreator) public classInviteLinkCreator: ClassInviteLinkCreator,
                @inject(EAT_TYPES.SalesCategoryService) public salesCategoryService: SalesCategoryService,
                @inject(LOCATION_SERVICE_TYPES.LocationService) public locationService: ILocationService,
                @inject(HERCULES_CLIENT_TYPES.IHerculesService) public herculeService: IHerculesService,
                @inject(RIDDLER_CLIENT_TYPES.RiddlerService) public riddlerService: IRiddlerService,
                @inject(PRODUCT_SERVICE_TYPES.BrandService) public brandService: IBrandService,
                @inject(CUREFIT_API_TYPES.UserActionMappingBusiness) public userActionMappingBusiness: UserActionMappingBusiness,
                @inject(PAGE_CONFIG_TYPES.ConfigService) public configService: ConfigService,
                @inject(EXPRESSION_TYPES.ExpressionBuilderFactory) public expressionBuilderFactory: ExpressionBuilderFactory,
                @inject(CFS_TYPES.ICFSClient) public formService: IFormService,
                @inject(SOCIAL_CLIENT_TYPES.SocialService) public socialService: ISocialService,
                @inject(CUREFIT_API_TYPES.FitclubBusiness) public fitclubBusiness: FitclubBusiness,
                @inject(BASE_TYPES.ClsUtil) public clsUtil: CLSUtil,
                @inject(CUREFIT_API_TYPES.DigitalLeagueSectionViewBuilder) public digitalLeagueSectionViewBuilder: DigitalLeagueSectionViewBuilder,
                @inject(CUREFIT_API_TYPES.DeepLinkService) public deeplinkService: IDeepLinkService,
                @inject(CUREFIT_API_TYPES.CareBusiness) public careBusiness: ICareBusiness,
                @inject(DIY_CLIENT_TYPES.CPAService) public cpaService: ICPAService,
                @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) public offerServiceV3: OfferServiceV3,
                @inject(CUREFIT_API_TYPES.DigitalSocialLeagueTabPageViewBuilder) public digitalSocialLeagueTabPageViewBuilder: any,
                @inject(RIDDLER_CACHE_TYPES.ChallengeCache) public challengeCache: ChallengeCache,
                @inject(RIDDLER_CLIENT_TYPES.RiddlerCacheService) public riddlerCacheService: RiddlerCacheService,
                @inject(OLLIVANDER_CLIENT_TYPES.IOllivanderCityService) public ollivanderService: IOllivanderCityService,
                @inject(CUREFIT_API_TYPES.SegmentService) public segmentService: ISegmentService,
                @inject(REPORT_ISSUES_CLIENT_TYPES.ISupportArticleService) public supportArticleService: ISupportArticleService,
                @inject(RASHI_CLIENT_TYPES.UserAttributeCacheClient) public userAttributeClient: IUserAttributeCacheClient,
                @inject(FUSE_CLIENT_TYPES.IDiagnosticSellerService) public diagnosticService: IDiagnosticService,
                @inject(UFS_CLIENT_TYPES.UserFitnessPlanControllerService) public userFitnessPlanControllerService: UserFitnessPlanControllerServiceInterface,
                @inject(FOODWAY_CLIENT_TYPES.IFoodwayApiService) public foodwayService: IFoodwayApiService,
                @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) public membershipService: IMembershipService,
                @inject(CULT_BIKE_TYPES.CultBikeService) public cultBikeClient: ICultBikeService,
                @inject(CAFE_API_CLIENT_TYPES.CafeProductService) public cafeProductService: ICafeProductService,
                @inject(CONSTELLO_CLIENT_TYPES.GiftCardService) public giftCardService: IGiftCardService,
                @inject(REFERRAL_CLIENT_TYPES.ReferralReadOnlyDao) public referralDao: IReferralReadOnlyDao,
                @inject(CUREFIT_API_TYPES.GiftCardTransformationService) public giftCardTransformationService: GiftCardTransformationService,
                @inject(CUREFIT_API_TYPES.ReferralTransformationService) public referralTransformationService: ReferralTransformationService,
                @inject(CUREFIT_API_TYPES.FeatureStateCache) public featureStateCache: FeatureStateCache,
                @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) public offlineFitnessPackService: IOfflineFitnessPackService,
                @inject(MAXMIND_CLIENT_TYPES.IMaxmindService) public maxmindService: IMaxmindService,
                @inject(CUREFIT_API_TYPES.AnnouncementRedisDao) public announcementDao?: IRedisDao<AnnouncementKey, Announcement>
    ) {

    }
}

export default ServiceInterfaces
