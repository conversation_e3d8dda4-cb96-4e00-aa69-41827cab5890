import { WidgetType } from "@curefit/vm-common"
import {
    getWidgetBasedOnWidgetType,
    GridImageWidget,
    SquadChallengesCarouselWidget,
} from "@curefit/vm-models"
import { EatMealsWidgetView } from "./widgets/EatMealsWidgetView"
import { RecipeTabWidgetView } from "./widgets/RecipeTabWidgetView"
import { MealCardWidget } from "./widgets/MealsCardWidget"
import { EatActiveSubscriptionWidget } from "./widgets/EatActiveSubscriptionWidget"
import { EatWhySubscribeWidget } from "./widgets/EatWhySubscribeWidget"
import { MealSlotSelectorWidget } from "./widgets/MealSlotSelectorWidget"
import { CareConsultationViewWidget } from "./widgets/CareConsultationViewWidget"
import { MembershipViewWidget } from "./widgets/MembershipViewWidget"
import { ProductBrowseWidgetView } from "./widgets/ProductBrowseWidget"
import { GearMicroappWidget } from "./widgets/GearMicroappWidget"
import { CareConsultationListViewWidget } from "./widgets/CareConsultationListViewWidget"
import { WodViewWidget } from "./widgets/WodViewWidget"
import { SaleWidgetV2View } from "./widgets/sale/SaleWidgetV2View"
import { CentreAddressDetailViewWidget } from "./widgets/CentreAddressDetailViewWidget"
import { ConsultationCardViewWidget } from "./widgets/ConsultationCardViewWidget"
import { PromoBannerViewWidget } from "./widgets/PromoBannerViewWidget"
import { InfoSeeMoreWidgetView } from "./widgets/InfoSeeMoreWidgetView"
import { TrainersViewWidget } from "./widgets/TrainersViewWidget"
import { BannerCarouselWidgetView, ImageListWidgetView, EmbeddedImageListWidgetView, WebCarouselWidgetView } from "./widgets/sale/BannerCarouselWidgetView"
import { ProductActionCardWidgetView } from "./widgets/ProductActionCardWidgetView"
import { RecipeBrowseWidgetView } from "./widgets/RecipeBrowseWidgetView"
import { UpcomingWidgetView } from "./widgets/timeline/upcoming/UpcomingWidgetView"
import { LiveWidgetView } from "./widgets/timeline/LiveWidgetView"
import { TryOutWidgetView } from "./widgets/timeline/TryOutWidgetView"
import { AddActivityWidgetView } from "./widgets/timeline/AddActivityWidgetView"
import { ProfileWidgetView } from "./widgets/timeline/ProfileWidgetView"
import { RecommendationWidgetView } from "./widgets/timeline/RecommendationWidgetView"
import { PresecriptionWidgetView } from "./widgets/timeline/PresecriptionWidgetView"
import { FitnessReportWidgetView } from "./widgets/timeline/FitnessReportWidgetView"
import { NotificationWidgetView } from "./widgets/timeline/NotificationWidgetView."
import { TrackYourHabitWidgetView } from "./widgets/timeline/TrackYourHabitWidgetView"
import { HorizontalCardListingWidgetView } from "./widgets/HorizontalCardListingWidgetView"
import { MedicalReportWidgetView } from "./widgets/timeline/MedicalReportWidgetView"
import { EatMealsWidgetViewV2 } from "./widgets/EatMealsWidgetViewV2"
import { EatMarketPlaceWidget } from "../../eat/clp/widget/EatMarketplaceWidget"

import { WholeFitMealsWidgetView } from "./widgets/WholeFitMealsWidgetView"
import { VideoCardCarouselWidgetView } from "./widgets/VideoCardCarouselWidgetView"
import { ChallengeWidgetView } from "./widgets/timeline/ChallengeWidgetView"
import { CompletedActivitiesWidgetView } from "./widgets/timeline/CompletedActivitiesWidgetView"
import { MealSlotSelectorWidgetV2 } from "./widgets/MealSlotSelectorWidgetV2"
import { RecentGearOrderWidget } from "./widgets/RecentGearOrderWidget"
import { RewardInfoWidgetView } from "./widgets/timeline/RewardInfoWidgetView"
import { WebVerticalInfoWidgetView } from "./widgets/WebVerticalInfoWidgetView"
import { AnnouncementWidgetView } from "./widgets/timeline/AnnouncementWidgetView"
import { FitnessReportSummaryWidgetView } from "./widgets/FitnessReportSummaryWidgetView"
import { PackBrowseWidgetView } from "./widgets/PackBrowseWidgetView"
import { ClpCalloutWidgetView } from "./widgets/timeline/ClpCalloutWidgetView"
import { CultMemoriesCarouselWidgetView } from "./widgets/CultMemoriesCarouselWidgetView"
import { OfferInfoWidget } from "../PageWidgets"
import { LaunchStoryWidget } from "./widgets/LaunchStoryWidget"
import { VerticalCardListingWidgetView } from "./widgets/VerticalCardListingWidgetView"
import { ProductListWidgetView } from "./widgets/ProductListWidgetView"
import { ProductGuranteeWidgetView } from "./widgets/ProductGuranteeWidgetView"
import { GymfitSearchWIdgetView } from "./widgets/GymfitSearchWIdgetView"
import { PackBrowseWidgetV2View } from "./widgets/PackBrowseWidgetV2View"
import { ProductSelectionWidgetView } from "./widgets/card/ProductSelectionWidgetView"
import { ImageInfoWidgetView } from "./widgets/ImageInfoWidgetView"
import { HeaderActionWidgetView } from "./widgets/HeaderActionWidgetView"
import { WholeFitMealsWidgetViewV2 } from "./widgets/WholeFitMealsWidgetViewV2"
import { SaleWidgetView } from "./widgets/sale/SaleWidgetView"
import { CultTrialTicketsWidgetView } from "./widgets/cult/CultTrialTicketsWidgetView"
import { OfferCalloutWidgetView } from "./widgets/OfferCalloutWidgetView"
import { VerticalOfferWidgetView } from "./widgets/VerticalOfferWidgetView"
import { CLPSaleContainerWidgetView } from "./widgets/CLPSaleContainerWidgetView"
import TimerWidgetV2View from "./widgets/TimerWidgetV2View"
import { CultPackBrowseWidgetView } from "./widgets/CultPackBrowseWidgetView"
import { FitnessPackBrowseWidgetView } from "./widgets/FitnessPackBrowseWidgetView"
import { CheckoutPageTimerWidgetView } from "./widgets/CheckoutPageTimerWidgetView"
import MealPlannerGetStarted from "./widgets/mealplanner/MealPlannerGetStarted"
import MealPlannerUpcoming from "./widgets/mealplanner/MealPlannerUpcoming"
import { PrivateChallengeWidgetView } from "./widgets/timeline/PrivateChallengeWidgetView"
import CardVideoWidgetView from "./widgets/CardVideoWidgetView"
import { MembershipViewWidgetV2 } from "./widgets/MembershipViewWidgetV2"
import { ProductBenefitWidgetView } from "./widgets/ProductBenefitWidgetView"
import { UserProfileSummaryWidgetView } from "./widgets/UserProfileSummaryWidgetView"
import { CultFindCenterWidgetView } from "./widgets/CultFindCenterWidgetView"
import { RoundedImageGridWidgetView } from "./widgets/RoundedImageGridWidgetView"
import { NotServiceableWidgetView } from "./widgets/NotServiceableWidgetView"
import { CultMemoriesCarouselWidgetViewV2 } from "./widgets/CultMemoriesCarouselWidgetViewV2"
import { CareConsultationListWidgetV2View } from "./widgets/CareConsultationListViewWidgetV2"
import { NuxBannerWidgetView } from "./widgets/cult/NuxBannerWidgetView"
import { WholefitTopSellingProductsViewBuilder } from "../../wholefit/WholefitTopSellingProductsBuilder"
import { TherapyPackBrowseWidgetView } from "./widgets/cult/TherapyPackBrowseWidgetView"
import { PTPackBrowseWidgetView } from "./widgets/cult/PTPackBrowseWidgetView"
import { BannerActionWidgetView } from "./widgets/BannerActionWidgetView"
import { OptInPermissionWidgetView } from "./widgets/timeline/OptInPermissionWidgetView"
import {
    EatProductCollectionMealsWidgetView
} from "./widgets/EatProductCollectionMealsWidgetView"
import { WODLiveCarouselWidgetView } from "../../digital/WODLiveCarouselWidgetView"
import { TabViewWidgetV2View } from "./widgets/TabViewWidgetV2View"
import { EatLiveRecipeWidgetBuilder } from "./widgets/EatLiveRecipeWidgetBuilder"
import { ConsultationProductCardV2WidgetView } from "./widgets/ConsultationProductCardV2WidgetView"
import { MyLiveClassesWidgetView } from "./widgets/MyLiveClassesWidgetView"
import { MyLiveClassesWidgetV2View } from "./widgets/MyLiveClassesWidgetV2View"
import { DIYPackCardWidgetView } from "./widgets/DIYPackCardWidgetView"
import { NewRecipesWidget, RecipeofthedayWidget, TrendingRecipesWidget } from "../../live/eatlive/EatLiveWidgets"
import { DaysRemainingWidgetView } from "../../digital/DaysRemainingWidgetView"
import { RecipeWebBrowseBuilder } from "../../recipe/RecipeWebBrowseBuilder"
import { TrainerSelectionWidgetView } from "./widgets/cult/TrainerSelectionWidgetView"
import { MyDoctorsWidgetView } from "./widgets/MyDoctorsWidgetView"
import { RoundedIconListWidgetView } from "./widgets/cult/RoundedIconListWidgetView"
import { PtGoalSummaryWidgetView } from "./widgets/cult/PtGoalSummaryWidgetView"
import { LiveBenefitsCarouselWidgetView } from "./widgets/LiveBenefitsCarouselWidgetView"
import { CollectionsListWidgetBuilder } from "./widgets/ondemand/CollectionsListWidgetBuilder"
import { EatLiveVideoWidgetBuilder } from "../../recipe/EatLiveVideoWidgetBuilder"
import { LeagueRequestStatusSectionListWidgetView } from "./widgets/LeagueRequestStatusSectionListWidgetView"
// import { RecipeCollectionWidgetBuilder } from "../../recipe/RecipeCollectionWidgetBuilder"
import { RecipeFilterContainerWidgetV2Builder } from "../../recipe/RecipeFilterContainerWidgetV2Builder"
import { TitleWidgetBuilder } from "./widgets/TitleWidgetBuilder"
import { EatSearchAutoCompleteWidgetBuilder } from "./widgets/EatSearchAutoCompleteWidgetBuilder"
import { DiyWodWidgetView } from "./widgets/DiyWodWidgetView"
import { NutritionistPageWidgetView } from "./widgets/eat/NutritionistPlanWidgetView"
import { RecipeCollectionWidgetBuilder } from "../../recipe/RecipeCollectionWidgetBuilder"
import { LivePremiereListWidgetBuilder } from "../ondemand/LivePremiereListWidgetBuilder"
import { LivePackPickerWidgetView } from "../../digital/LivePackPickerWidgetView"
import { LivePTTrialWidgetView } from "./widgets/cult/LivePTTrialWidgetView"
import { SquadChallengeCarouselWidgetView } from "./widgets/SquadChallengeCarouselWidgetView"
import { NowLiveWidgetViewBuilder } from "../../digital/NowLiveWidgetViewBuilder"
import { ClassesTodayWidgetView } from "../../digital/ClassesTodayWidgetView"
import { BannerGridWidgetView } from "./widgets/sale/BannerGridWidgetView"
import { NutritionistPackViewWidget } from "../../eat/nutritionist/NutritionistPackViewWidget"
import { PackDescriptionWidgetView } from "./widgets/eat/NCPackDescriptionWidgetView"
import { NCCustomBannerWidget } from "../../eat/nutritionist/NCCustomBannerWidget"
import { IconWidgetBuilder } from "./widgets/IconWidgetBuilder"
import { FAQWidgetView } from "./widgets/FAQWidgetView"
import { ClassCountProgressWidget } from "./widgets/ClassCountProgressWidget"
import { TabbedContainerWidgetView } from "./widgets/TabbedContainerWidgetView"
import { BookingWidgetView } from "./widgets/BookingWidgetView"
import { DigitalUserJourneyWidgetView } from "../../digital/DigitaluserJourneyWidgetView"
import { TestimonialWidgetViewV2 } from "../../common/views/TestimonialWidgetViewV2"
import { TherapyPackBrowseWidgetViewV2 } from "./widgets/cult/TherapyPackBrowseWidgetViewV2"
import { TrainerCustomerChatWidgetView } from "./widgets/TrainerCustomerChatWidgetView"
import { CoachPreferenceWidget } from "../../transform/widgets/CoachPreferenceWidget"
import { CultPassImageTextCarouselView } from "./widgets/cultPass/CultPassImageTextCarouselView"
import { VerticalExploreWidget } from "./widgets/VerticalExploreWidget"
import { WellnessCuisineWidget } from "../../eat/marketplace/widgets/WellnessCuisineWidget"
import { FMMealsWidgetView } from "../../eat/marketplace/FMMealsWidgetView"
import { BrandListingWidget } from "../../eat/marketplace/widgets/BrandListingWidget"
import { FeaturedTherapistWidget } from "./widgets/care/FeaturedTherapistWidget"
import { ChallengeLeaderboardWidgetView } from "./ChallengeLeaderboardWidgetView"
import { ShareActionWidget } from "../../transform/widgets/ShareActionWidget"
import { LevelPointsWidget } from "../../transform/widgets/LevelPointsWidget"
import { ReferralCardsWidget } from "../../transform/widgets/ReferralCardsWidget"
import { BannerCardWidget } from "../../transform/widgets/BannerCardWidget"

export function getWidgetByType(widgetType: WidgetType) {
    switch (widgetType) {
        case "EAT_LIVE_RECIPE_WIDGET":
            return EatLiveRecipeWidgetBuilder
        case "EAT_MEALS_WIDGET":
            return EatMealsWidgetView
        case "EAT_MEALS_WIDGET_V2":
            return EatMealsWidgetViewV2
         case "FM_MEALS_WIDGET":
             return FMMealsWidgetView
        case "EAT_TAG_BASED_MEALS_WIDGET_VIEW":
            return EatProductCollectionMealsWidgetView
        case "EAT_MARKETPLACE_WIDGET":
            return EatMarketPlaceWidget
        case "WHOLE_FIT_MEALS_WIDGET":
            return WholeFitMealsWidgetView
        case "WHOLE_FIT_MEALS_WIDGET_V2":
            return WholeFitMealsWidgetViewV2
        case "WHOLE_FIT_TOP_SELLING_PRODUCTS_WIDGET":
            return WholefitTopSellingProductsViewBuilder
        case "RECIPE_BROWSE_WIDGET":
            return RecipeBrowseWidgetView
        case "MEAL_CARD_WIDGET":
            return MealCardWidget
        case "OFFER_INFO_WIDGET":
            return OfferInfoWidget
        case "EAT_ACTIVE_SUBS_WIDGET":
            return EatActiveSubscriptionWidget
        case "EAT_WHY_SUBSCRIBE_WIDGET":
            return EatWhySubscribeWidget
        case "MEAL_SLOT_SELECTOR_WIDGET":
            return MealSlotSelectorWidget
        case "MEAL_SLOT_SELECTOR_WIDGET_V2":
            return MealSlotSelectorWidgetV2
        case "CARE_CONSULTATION_WIDGET":
            return CareConsultationViewWidget
        case "MEMBERSHIP_WIDGET":
            return MembershipViewWidget
        case "MEMBERSHIP_WIDGET_V2":
            return MembershipViewWidgetV2
        case "CARE_CONSULTATION_LIST_WIDGET":
            return CareConsultationListViewWidget
        case "GRID_IMAGE_WIDGET":
            return GridImageWidget
        case "PRODUCT_BROWSE_WIDGET":
            return ProductBrowseWidgetView
        case "LAUNCH_STORY_WIDGET":
            return LaunchStoryWidget
        case "RECIPE_TAB_WIDGET":
            return RecipeTabWidgetView
        case "WOD_WIDGET":
            return WodViewWidget
        case "CENTRE_ADDRESS_DETAIL_WIDGET":
            return CentreAddressDetailViewWidget
        case "CONSULTATION_CARD_WIDGET":
            return ConsultationCardViewWidget
        case "PROMO_BANNER_WIDGET":
            return PromoBannerViewWidget
        case "INFO_SEE_MORE_WIDGET":
            return InfoSeeMoreWidgetView
        case "TRAINERS_WIDGET":
            return TrainersViewWidget
        case "BANNER_CAROUSEL_WIDGET":
            return BannerCarouselWidgetView
        case "NUX_BANNER_WIDGET":
            return NuxBannerWidgetView
        case "VIDEO_CARD_CAROUSEL_WIDGET":
            return VideoCardCarouselWidgetView
        case "IMAGE_LIST_WIDGET":
            return ImageListWidgetView
        case "EMBEDDED_IMAGE_LIST_WIDGET":
            return EmbeddedImageListWidgetView
        case "WEB_CAROUSEL_WIDGET":
            return WebCarouselWidgetView
        case "UPCOMING_WIDGET":
            return UpcomingWidgetView
        case "LIVE_WIDGET":
            return LiveWidgetView
        case "TRY_OUT_WIDGET":
            return TryOutWidgetView
        case "ADD_ACITIVITES_WIDGET":
            return AddActivityWidgetView
        case "PROFILE_WIDGET":
            return ProfileWidgetView
        case "PRODUCT_ACTION_CARD_WIDGET":
            return ProductActionCardWidgetView
        case "RECOMMENDATION_WIDGET":
            return RecommendationWidgetView
        case "PRESCRIPTION_WIDGET":
            return PresecriptionWidgetView
        case "FITNESS_REPORT_WIDGET":
            return FitnessReportWidgetView
        case "NOTIFICATION_WIDGET":
            return NotificationWidgetView
        case "TRACK_YOUR_HABIT_WIDGET":
            return TrackYourHabitWidgetView
        case "HORIZONTAL_ACTIONAL_CARD_LISTING_WIDGET":
            return HorizontalCardListingWidgetView
        case "MEDICAL_REPORT_WIDGET":
            return MedicalReportWidgetView
        case "CHALLENGE_WIDGET":
            return ChallengeWidgetView
        case "NOT_SERVICEABLE_WIDGET":
            return NotServiceableWidgetView
        case "PRIVATE_CHALLENGE_WIDGET":
            return PrivateChallengeWidgetView
        case "COMPLETED_ACTIVITIES_WIDGET":
            return CompletedActivitiesWidgetView
        case "RECENT_GEAR_ORDER_WIDGET":
            return RecentGearOrderWidget
        case "REWARD_INFO_WIDGET":
            return RewardInfoWidgetView
        case "CP_IMAGE_TEXT_CAROUSEL_WDIGET":
            return CultPassImageTextCarouselView
        case "WEB_VERTICAL_INFO_WIDGET":
            return WebVerticalInfoWidgetView
        case "ANNOUNCEMENT_WIDGET":
            return AnnouncementWidgetView
        case "FITNESS_REPORT_SUMMARY_WIDGET":
            return FitnessReportSummaryWidgetView
        case "PACK_BROWSE_WIDGET":
            return PackBrowseWidgetView
        case "CLP_CALLOUT_WIDGET":
            return ClpCalloutWidgetView
        case "CULT_MEMORIES_CAROUSEL_WIDGET":
            return CultMemoriesCarouselWidgetView
        case "CULT_MEMORIES_CAROUSEL_WIDGET_V2":
            return CultMemoriesCarouselWidgetViewV2
        case "VERTICAL_ACTIONAL_CARD_LISTING_WIDGET":
            return VerticalCardListingWidgetView
        case "VM_PRODUCT_LIST_WIDGET":
            return ProductListWidgetView
        case "PRODUCT_GURANTEE_WIDGET":
            return ProductGuranteeWidgetView
        case "GYMFIT_SEARCH_WIDGET":
            return GymfitSearchWIdgetView
        case "PACK_BROWSE_WIDGET_V2":
            return PackBrowseWidgetV2View
        case "PRODUCT_SELECTION_WIDGET":
            return ProductSelectionWidgetView
        case "IMAGE_INFO_WIDGET":
            return ImageInfoWidgetView
        case "HEADER_ACTION_WIDGET":
            return HeaderActionWidgetView
        case "SALE_WIDGET":
            return SaleWidgetView
        case "CULT_TRIAL_TICKETS_WIDGET":
            return CultTrialTicketsWidgetView
        case "OFFER_CALLOUT_WIDGET":
            return OfferCalloutWidgetView
        case "VERTICAL_OFFER_WIDGET":
            return VerticalOfferWidgetView
        case "CLP_SALE_CONTAINER_WIDGET":
            return CLPSaleContainerWidgetView
        case "TIMER_WIDGET_V2":
            return TimerWidgetV2View
        case "CULT_PACK_BROWSE_WIDGET":
            return CultPackBrowseWidgetView
        case "FITNESS_PACK_BROWSE_WIDGET":
            return FitnessPackBrowseWidgetView
        case "CHECKOUT_PAGE_TIMER_WIDGET":
            return CheckoutPageTimerWidgetView
        case "MEAL_PLANNER_GET_STARTED":
            return MealPlannerGetStarted
        case "MEAL_PLANNER_UPCOMING":
            return MealPlannerUpcoming
        case "CARD_VIDEO_WIDGET":
            return CardVideoWidgetView
        case "CULT_FIND_CENTER_WIDGET":
            return CultFindCenterWidgetView
        case "USER_PROFILE_SUMMARY_WIDGET":
            return UserProfileSummaryWidgetView
        case "ROUNDED_IMAGE_GRID_WIDGET":
            return RoundedImageGridWidgetView
        case "GEAR_MICROAPP_WIDGET":
            return GearMicroappWidget
        case "CARE_CONSULTATION_LIST_WIDGET_V2":
            return CareConsultationListWidgetV2View
        case "MY_DOCTORS_WIDGET":
            return MyDoctorsWidgetView
        case "SALE_WIDGET_V2":
            return SaleWidgetV2View
        case "THERAPY_PACK_BROWSE_WIDGET":
            return TherapyPackBrowseWidgetView
        case "THERAPY_PACK_BROWSE_WIDGET_V2":
            return TherapyPackBrowseWidgetViewV2
        case "PT_PACK_BROWSE_WIDGET":
            return PTPackBrowseWidgetView
        case "BANNER_ACTION_WIDGET":
            return BannerActionWidgetView
        case "WOD_LIVE_CAROUSEL_WIDGET":
            return WODLiveCarouselWidgetView
        case "OPT_IN_PERMISSION_WIDGET":
            return OptInPermissionWidgetView
        case "TAB_VIEW_WIDGET_V2":
            return TabViewWidgetV2View
        case "CONSULTATION_PRODUCT_CARD_V2_WIDGET":
            return ConsultationProductCardV2WidgetView
        case "MY_LIVE_CLASSES_WIDGET":
            return MyLiveClassesWidgetView
        case "MY_LIVE_CLASSES_WIDGET_V2":
            return MyLiveClassesWidgetV2View
        case "DIY_PACK_CARD_WIDGET":
            return DIYPackCardWidgetView
        case "ROUNDED_ICON_LIST_WIDGET":
            return RoundedIconListWidgetView
        case "PT_GOAL_SUMMARY_WIDGET":
            return PtGoalSummaryWidgetView
        case "DAYS_REMAINING_WIDGET":
            return DaysRemainingWidgetView
        case "LIVE_BENEFITS_CAROUSEL_WIDGET":
            return LiveBenefitsCarouselWidgetView
        case "DIY_WOD":
            return DiyWodWidgetView
        case "NEW_RECIPES_WIDGET":
            return NewRecipesWidget
        case "RECIPE_OF_THE_DAY_WIDGET":
            return RecipeofthedayWidget
        case "TRENDING_RECIPES_WIDGET":
            return TrendingRecipesWidget
        case "RECIPE_BROWSE_WEB_WIDGET":
            return RecipeWebBrowseBuilder
        case "COLLECTIONS_LIST_WIDGET":
            return CollectionsListWidgetBuilder
        case "LIVE_PREMIERE_LIST_WIDGET":
            return LivePremiereListWidgetBuilder
        case "TRAINER_SELECTION_WIDGET":
            return TrainerSelectionWidgetView
        case "CONTENT_VIDEO_WIDGET":
            return EatLiveVideoWidgetBuilder
        case "LEAGUE_REQUEST_STATUS_SECTION_LIST_WIDGET":
            return LeagueRequestStatusSectionListWidgetView
        case "PRODUCT_BENEFIT_WIDGET":
            return ProductBenefitWidgetView
        case "RECIPE_COLLECTION_WIDGET":
            return RecipeCollectionWidgetBuilder
        case "EAT_TITLE_WIDGET":
            return TitleWidgetBuilder
        // can be used as generic title widget for vm pages based on pageId of the page
        case "RECIPE_FILTER_CONTAINER_WIDGET_V2":
            return RecipeFilterContainerWidgetV2Builder
        case "EAT_SEARCH_AUTO_COMPLETE_WIDGET":
            return EatSearchAutoCompleteWidgetBuilder
        case "NUTRITIONIST_PAGE_WIDGET":
            return NutritionistPageWidgetView
        case "NC_HORIZONTAL_PACK_VIEW_WIDGET":
            return NutritionistPackViewWidget
        case "NC_CUSTOM_BANNER_WIDGET":
            return NCCustomBannerWidget
        case "ICON_WIDGET":
            return IconWidgetBuilder
        case "LIVE_PACK_PICKER_WIDGET":
        case "LIVE_PACK_PICKER_WIDGET_DARK":
            return LivePackPickerWidgetView
        case "LIVE_PT_TRIAL_WIDGET":
            return LivePTTrialWidgetView
        case "NOW_LIVE_WIDGET":
            return NowLiveWidgetViewBuilder
        case "SQUAD_CHALLENGES_CAROUSEL_WIDGET":
            return SquadChallengeCarouselWidgetView
        case "CLASSES_TODAY_WIDGET":
            return ClassesTodayWidgetView
        case "BANNER_GRID_WIDGET":
            return BannerGridWidgetView
        case "PACK_DESCRIPTION_WIDGET":
            return PackDescriptionWidgetView
        case "FAQ_WIDGET_V2":
            return FAQWidgetView
        case "CLASS_COUNT_PROGRESS_WIDGET":
            return ClassCountProgressWidget
        case "DIGITAL_USER_JOURNEY_WIDGET":
            return DigitalUserJourneyWidgetView
        case "TABBED_CONTAINER_WIDGET":
            return TabbedContainerWidgetView
        case "BOOKING_WIDGET":
            return BookingWidgetView
        case "TESTIMONIAL_WIDGET_V2":
             return TestimonialWidgetViewV2
        case "TRAINER_CUSTOMER_CHAT_WIDGET":
            return TrainerCustomerChatWidgetView
        case "COACH_PREFERENCE_WIDGET":
            return CoachPreferenceWidget
        case "VERTICAL_EXPLORE_WIDGET":
            return VerticalExploreWidget
        case "WELLNESS_CUISINES_WIDGET":
            return WellnessCuisineWidget
        case "FM_BRAND_LISTING_WIDGET":
            return BrandListingWidget
        case "FEATURED_THERAPIST_WIDGET":
                return FeaturedTherapistWidget
        case "CHALLENGE_LEADERBOARD_WIDGET":
            return ChallengeLeaderboardWidgetView
        case "SHARE_ACTION_WIDGET":
            return ShareActionWidget
        case "LEVEL_POINTS_WIDGET":
            return LevelPointsWidget
        case "REFERRAL_CARDS_WIDGET":
            return ReferralCardsWidget
        case "BANNER_CARD_WIDGET":
            return BannerCardWidget
        default:
            return getWidgetBasedOnWidgetType(widgetType)
    }
}
