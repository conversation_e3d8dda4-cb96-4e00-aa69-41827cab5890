import { Container, inject } from "inversify"
import { controller, httpDelete, httpGet, httpPost, httpPut } from "inversify-express-utils"
import AuthMiddleware from "../../../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../../../config/ioc/types"
import * as express from "express"
import { CFServiceInterfaces } from "../ServiceInterfaces"
import { SegmentTestOverride } from "./SegmentTestOverride"
import * as _ from "lodash"
import { ICrudKeyValue, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { SEGMENT_OVERRIDE_REDIS_KEY } from "../../../util/VMUtil"
import { BASE_TYPES, ILogger } from "@curefit/base"

/*
    Rename file name, and add controller path
 */
export function SegmentOverrideControllerFactory(kernel: Container) {
    @controller("/segmentOverride", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class SegmentOverrideController {
        private redisCrudDao: ICrudKeyValue
        constructor(
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: CFServiceInterfaces,
            @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
            @inject(BASE_TYPES.ILogger) protected logger: ILogger
        ) {
            this.redisCrudDao = this.multiCrudKeyValueDao.getICrudKeyValue("DEFAULT")
        }

        @httpPost("/updateMapping")
        public async updateMapping(req: express.Request): Promise<any> {
            const segmentOverride: SegmentTestOverride = {
                userId: req.body.userId,
                testUserId: req.body.testUserId
            } as SegmentTestOverride
            this.logger.debug("SegmentOverride", segmentOverride)
            await this.redisCrudDao.updateHashField(SEGMENT_OVERRIDE_REDIS_KEY, segmentOverride.userId, JSON.stringify(segmentOverride))
            return segmentOverride
        }

        @httpPut("/updateAllMappings")
        public async updateAllMappings() {
            const segmentOverrides = await this.redisCrudDao.getHashAllFields(SEGMENT_OVERRIDE_REDIS_KEY)
            _.map(Object.keys(segmentOverrides), key => {
                const segmentOverride: SegmentTestOverride = <SegmentTestOverride>JSON.parse(segmentOverrides[key])
                this.redisCrudDao.updateHashField(SEGMENT_OVERRIDE_REDIS_KEY, key, JSON.stringify(segmentOverride))
            })
        }

        @httpDelete("/deleteMapping")
        public async deleteMapping(req: express.Request): Promise<any> {
            return this.redisCrudDao.deleteHashMulti(SEGMENT_OVERRIDE_REDIS_KEY, [req.body.userId])
        }

        @httpGet("/getMappings")
        public async getMappings(req: express.Request): Promise<SegmentTestOverride[]> {
            const segmentOverrides = await this.redisCrudDao.getHashAllFields(SEGMENT_OVERRIDE_REDIS_KEY)
            return _.map(Object.keys(segmentOverrides), key => <SegmentTestOverride>JSON.parse(segmentOverrides[key]))
        }

    }
}
