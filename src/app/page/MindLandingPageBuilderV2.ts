import { inject, injectable } from "inversify"
import { Page } from "./Page"
import { Banner } from "./PageWidgets"
import { Tab } from "./IPageBuilder"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { FitnessEvent } from "@curefit/cult-common"
import { ICultServiceOld as ICultService, CULT_CLIENT_TYPES } from "@curefit/cult-client"
import { ProductType } from "@curefit/product-common"
import { Session, SessionData } from "@curefit/userinfo-common"
import { TenantId } from "@curefit/product-common"
import { Vertical } from "@curefit/base-common"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ICatalogueService, CATALOG_CLIENT_TYPES, ICatalogueServicePMS } from "@curefit/catalog-client"
import EatLandingPageConfig from "./EatLandingPageConfig"
import { IBannerService } from "@curefit/vm-models"
import BaseCultLandingPageBuilder from "./BaseCultLandingPageBuilder"
import BaseCultLandingPageConfig from "./BaseCultLandingPageConfig"
import { IProgramService, PROGRAM_CLIENT_TYPES } from "@curefit/program-client"
import { IDIYFulfilmentService, DIY_CLIENT_TYPES } from "@curefit/diy-client"
import { IOfferServiceV2, OfferServiceV3 } from "@curefit/offer-service-client"
import { UserContext } from "@curefit/userinfo-common"
import GearLandingPageBuilder from "./GearLandingPageBuilder"
import IUserBusiness from "../user/IUserBusiness"
import { CultUserType } from "@curefit/vm-common"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { OFFER_SERVICE_CLIENT_TYPES } from "@curefit/offer-service-client"
import { IMembershipService, MEMBERSHIP_CLIENT_TYPES } from "@curefit/membership-client"
import { IOfflineFitnessPackService, PACK_CLIENT_TYPES } from "@curefit/pack-management-service-client"

const MIND_SUPPORTED_CITIES = ["Bangalore", "Gurgaon", "Hyderabad"]
@injectable()
class MindLandingPageBuilderV2 extends BaseCultLandingPageBuilder {
    constructor(@inject(CUREFIT_API_TYPES.MindLandingPageConfigV2) protected landingPageConfig: BaseCultLandingPageConfig,
        @inject(CUREFIT_API_TYPES.EatLandingPageConfig) protected eatLandingPageConfig: EatLandingPageConfig,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV2) protected offerService: IOfferServiceV2,
        @inject(OFFER_SERVICE_CLIENT_TYPES.OfferServiceV3) protected offerServiceV3: OfferServiceV3,
        @inject(CULT_CLIENT_TYPES.MindFitService) protected cultService: ICultService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) protected catalogueService: ICatalogueService,
        @inject(CATALOG_CLIENT_TYPES.CatalogServiceReaderPMS) protected catalogueServicePMS: ICatalogueServicePMS,
        @inject(PACK_CLIENT_TYPES.OfflineFitnessPackService) protected offlineFitnessPackService: IOfflineFitnessPackService,
        @inject(LOCATION_TYPES.CityService) protected cityService: ICityService,
        @inject(CUREFIT_API_TYPES.BannerService) protected bannerService: IBannerService,
        @inject(PROGRAM_CLIENT_TYPES.ProgramService) protected programService: IProgramService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) protected DIYFulfilmentService: IDIYFulfilmentService,
        @inject(CUREFIT_API_TYPES.GearLandingPageBuilder) protected gearLandingPageBuilder: GearLandingPageBuilder,
        @inject(CUREFIT_API_TYPES.UserBusiness) protected userBusiness: IUserBusiness,
        @inject(BASE_TYPES.ILogger) protected logger: Logger,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(MEMBERSHIP_CLIENT_TYPES.MembershipService) protected membershipService: IMembershipService) {
        super(landingPageConfig, eatLandingPageConfig, offerService, offerServiceV3,
            cultService, catalogueService, catalogueServicePMS, offlineFitnessPackService, cityService, bannerService,
            programService, userCache, DIYFulfilmentService, gearLandingPageBuilder, userBusiness,
            logger, cacheHelper, membershipService)
    }

    async getEventsListing(userId: string, cityId: number): Promise<FitnessEvent[]> {
        return await this.cultService.browseFitnessEvents(process.env.APP_NAME, userId, cityId)
    }

    getVertical(): Vertical {
        return "MIND_FIT"
    }

    getCenterSectionName(): string {
        return "At Center"
    }

    getAtHomeSectionName(): string {
        return "At Home"
    }

    getAtEventsSectionName(): string {
        return "Events"
    }

    getProductType(): ProductType {
        return "MIND"
    }

    getDiyPackProductType(): ProductType {
        return "DIY_MEDITATION_PACK"
    }

    getDiyProductType(): ProductType {
        return "DIY_MEDITATION"
    }

    getCenterId(sessionData: SessionData): string {
        return sessionData.mindCenterId
    }

    isCenterOperational(cityId: string): boolean {
        if (MIND_SUPPORTED_CITIES.indexOf(cityId) >= 0)
            return true
        else
            return false
    }

    isDiyApplicable(type: string): boolean {
        return type === "mental" ? true : false
    }

    getPreregistrationOfferBanner(): Banner {
        // const preRegOfferBannerBangalore: Banner = {
        //     id: "pre_reg_offer_bellandur",
        //     action: "curefit://mindofferbrowse",
        //     image: "/image/banners/mind/Pre_regist_App_mob_banner.jpg",
        //     cityId: "Bangalore"
        // }
        // return preRegOfferBannerBangalore
        return undefined
    }

    getTenantId(): TenantId {
        return "mind.fit"
    }

    async includeGearContent(userContext: UserContext, cultUserTypes: CultUserType[]): Promise<boolean> {
        return false
    }

    async build(userContext: UserContext, session: Session, lat: number, lon: number, appVersion: number, os: string, codePushVersion: number, selectedTab: Tab): Promise<Page> {
        return super.build(userContext, session, lat, lon, appVersion, os, codePushVersion, selectedTab)
    }
}

export default MindLandingPageBuilderV2
