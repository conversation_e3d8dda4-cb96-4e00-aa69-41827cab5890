import { injectable } from "inversify"
import { CultClpPageV2, <PERSON>, PageWidget, PageWidgetTitle } from "./Page"
import CenterView from "../cult/CenterView"
import { Action, Header, WidgetView } from "../common/views/WidgetView"

import {
    ActionCard,
    Banner,
    BannerWidget,
    CategoryTileWidget,
    CenterAreaActionCard,
    CenterAreaWidget,
    ClpFooterWidget,
    ClpImageWidget,
    CultCenterGridWidget,
    DiySeries,
    DiyWidget,
    EventGridWidget,
    EventItem,
    ExperienceWidget,
    GridWidget,
    HowItWorksWidget,
    MagazineGridWidget,
    MagazineListWidget,
    MagazineRowWidget,
    OfferInfoWidget,
    OfferWidget,
    OgmWidget,
    PackActionCard,
    PackProgress,
    PackProgressListWidget,
    ProfileListWidget,
    Video,
    VideoCarouselWidget
} from "./PageWidgets"
import { Tab } from "./IPageBuilder"
import { ICultServiceOld as ICultService } from "@curefit/cult-client"
import { CultPack, CultPackType, CultWorkout, CultWorkoutCategory, FitnessEvent } from "@curefit/cult-common"
import { City } from "@curefit/location-common"
import { MindPack } from "@curefit/mind-common"
import { ProductType, ProgramPackProduct, TenantId } from "@curefit/product-common"
import { HowItWorksItem } from "@curefit/product-common"
import { Session, SessionData } from "@curefit/userinfo-common"
import { User, Tenant } from "@curefit/user-common"
import { Vertical } from "@curefit/base-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { Logger } from "@curefit/base"
import { UrlPathBuilder } from "@curefit/product-common"
import { IPackService } from "@curefit/alfred-client"
import { CatalogueServiceUtilities } from "../util/CatalogueServiceUtilities"
import { ICatalogueService, CatalogueServiceV2Utilities, ICatalogueServicePMS } from "@curefit/catalog-client"
import LocationUtil from "../util/LocationUtil"
import CultUtil, {
    CultOrMindSummary,
    getCultUserTypes,
    isActiveOrFutureMembershipPresent,
    transformCultSummary,
    UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD
} from "../util/CultUtil"
import { ActionUtilV1, OfferUtil } from "@curefit/base-utils"
import AtlasUtil from "../util/AtlasUtil"
import AppUtil, { CULT_BANNER_RATIO_MAP, MIND_BANNER_RATIO_MAP } from "../util/AppUtil"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { eternalPromise, TimeUtil } from "@curefit/util-common"

import * as _ from "lodash"
import { RUPEE_SYMBOL } from "@curefit/finance-common"
import TrainerView from "../cult/TrainerView"
import EatLandingPageConfig from "./EatLandingPageConfig"
import { IBannerService, BannerQueryCondition } from "@curefit/vm-models"
import BaseCultLandingPageConfig from "./BaseCultLandingPageConfig"
import { IProgramService, ProgramMembership, ProgramPack } from "@curefit/program-client"
import { IDIYFulfilmentService } from "@curefit/diy-client"
import { DIYPack, DIYPackFulfilment, DIYSeries } from "@curefit/diy-common"
import { IOfferServiceV2, OfferServiceV3, PackOffersResponse } from "@curefit/offer-service-client"
import { BaseOfferRequestParams, CultProductPricesResponse } from "@curefit/offer-common"
import { IS_NEW_CULT_OFFER_VERSION } from "@curefit/cult-client"
import { CultUserType } from "@curefit/vm-common"
import { UserContext } from "@curefit/userinfo-common"
import GearLandingPageBuilder from "./GearLandingPageBuilder"
import IUserBusiness from "../user/IUserBusiness"
import { MembershipDetails } from "@curefit/cult-common"
import { CacheHelper } from "../util/CacheHelper"
import { ICityService } from "@curefit/location-mongo"
import LiveUtil from "../util/LiveUtil"
import { IMembershipService } from "@curefit/membership-client"
import { OfflineFitnessPack } from "@curefit/pack-management-service-common"
import { IOfflineFitnessPackService } from "@curefit/pack-management-service-client"

@injectable()
abstract class BaseCultLandingPageBuilder {
    constructor(protected landingPageConfig: BaseCultLandingPageConfig,
        protected eatLandingPageConfig: EatLandingPageConfig,
        protected offerService: IOfferServiceV2,
        protected offerServiceV3: OfferServiceV3,
        protected cultService: ICultService,
        protected catalogueService: ICatalogueService,
        protected catalogueServicePMS: ICatalogueServicePMS,
        protected offlineFitnessPackService: IOfflineFitnessPackService,
        protected cityService: ICityService,
        protected bannerService: IBannerService,
        protected programService: IProgramService,
        protected userCache: CacheHelper,
        protected DIYFulfilmentService: IDIYFulfilmentService,
        protected gearLandingPageBuilder: GearLandingPageBuilder,
        protected userBusiness: IUserBusiness,
        protected logger: Logger,
        protected cacheHelper: CacheHelper,
        protected membershipService: IMembershipService,
        ) {

    }
    abstract getEventsListing(userId: string, cityId: number): Promise<FitnessEvent[]>
    abstract getPreregistrationOfferBanner(): Banner
    abstract getCenterSectionName(): string
    abstract getAtHomeSectionName(): string
    abstract getAtEventsSectionName(): string
    abstract getProductType(): ProductType
    abstract getVertical(): Vertical
    abstract getDiyPackProductType(): ProductType
    abstract getDiyProductType(): ProductType
    abstract isCenterOperational(cityId: string): boolean
    abstract getCenterId(sessionData: SessionData): string
    abstract isDiyApplicable(type: string): boolean
    abstract getTenantId(): TenantId
    abstract includeGearContent(userContext: UserContext, cultUserTypes: CultUserType[]): Promise<boolean>

    getSections(isCenterOperational: boolean, userAgent: UserAgent, includeGearContent?: boolean) {
        const sections = []
        if (isCenterOperational) {
            sections.push({
                id: "CENTER",
                name: this.getCenterSectionName()
            })
        }
        sections.push({
            id: "DIY",
            name: this.getAtHomeSectionName()
        })
        if ((userAgent === "DESKTOP" || userAgent === "MBROWSER")) {
            sections.push({
                id: "EVENTS",
                name: this.getAtEventsSectionName()
            })
        }

        return sections
    }

    async build(userContext: UserContext, session: Session, lat: number, lon: number, appVersion: number, os: string,
        codePushVersion: number, selectedTab: Tab): Promise<Page> {
        const isWithinBangalore: boolean = LocationUtil.isWithinBangalore(lat, lon)
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)

        const userId: string = session.userId
        const cityId: string = session.sessionData.cityId
        const countryId: string = AppUtil.getCountryId(userContext)
        const deviceId: string = session.deviceId
        const userAgent: UserAgent = session.userAgent
        const cultWidgetPromises: Promise<PageWidget | WidgetView>[] = []
        const cultDIYWidgetPromises: Promise<PageWidget>[] = []
        const centerId: string = this.getCenterId(session.sessionData)
        const areaId = (session.sessionData && session.sessionData && session.sessionData.locationPreferenceData) ? session.sessionData.locationPreferenceData.areaId : "1"
        this.logger.info("cityId " + session.sessionData.cityId)
        const city = session.sessionData.cityId ? await this.cityService.getCityById(session.sessionData.cityId) : await this.cityService.getDefaultCity(tenant)
        const baseOfferRequestParam: BaseOfferRequestParams = {
            userId: userId,
            cityId: userContext.userProfile.cityId,
            deviceId: deviceId,
            source: AppUtil.callSourceFromContext(userContext)
        }
        // const programOffersPromise = this.offerService.getProgramPackOffers(baseOfferRequestParam)
        const cultSummaryPromise = this.cacheHelper.getCultSummary(userId)
        // const programMembershipPromise = eternalPromise(this.programService.activeMemberships(session.userId, undefined, this.getTenantId()))

        const newDiyActivePacksPromise = this.DIYFulfilmentService.getDIYPackFulfilmentsForUserForProductType(userId, this.getDiyPackProductType())

        // const cultPacksPromise = this.cultService.browsePacks("CUREFIT_API", userId, city.cultCityId, true)
        // const programPacksPromise = eternalPromise(this.programService.browsePacks(city.cultCityId, this.getTenantId(), false, userId))
        const userPromise = this.userCache.getUser(userId)

        const diyActiveFulfilments = await newDiyActivePacksPromise

        const cultCachedSummary = await cultSummaryPromise
        const cultOrMindSummary = transformCultSummary(await cultCachedSummary, this.getVertical())

        const membershipList = (await eternalPromise(this.membershipService.getCachedMembershipsForUser(userId, AppUtil.getTenantFromUserContext(userContext), ["CULT"], ["PAUSED", "PURCHASED"]))).obj
        const cultUserTypes = await getCultUserTypes(userContext, cultCachedSummary, membershipList)
        // const includeGearContent = await this.includeGearContent(userContext, cultUserTypes)
        const includeGearContent = false
        const gearWidgetsPromise: Promise<PageWidget[]> = includeGearContent ? this.gearLandingPageBuilder.build(userContext) : Promise.resolve([])

        const fitnessPacks: OfflineFitnessPack[] = await CatalogueServiceUtilities.getCultPMSPacks(this.offlineFitnessPackService, userId, city.cityId)
        const packOffersPromiseV3 = this.getProductType() === "FITNESS" ? this.offerServiceV3.getCultPackPrices({
            userInfo: {
                userId,
                deviceId
            },
            source: baseOfferRequestParam.source,
            cityId: baseOfferRequestParam.cityId,
            cultCityId: city.cultCityId,
            productIds: fitnessPacks.map(pack => pack.productId),
        }) : undefined
        const packOffersV3Response = await packOffersPromiseV3

        const user = await userPromise
        const derivedCity = await this.getDerivedCityDetails(cityId, userContext, cultOrMindSummary)
        const cultCityId = derivedCity.cultCityId
        const isCenterOperational = this.isCenterOperational(derivedCity.cityId)
        const activeOrFutureMembershipPresent = isActiveOrFutureMembershipPresent(userContext, membershipList)

        const isProgramPackUser = false
        // const isProgramPackUser = programMembershipsResult.obj && !_.isEmpty(programMembershipsResult.obj)
        // const upForRenewal = isUpForRenewal(cultOrMindSummary)
        const headerWidgetPromise: Promise<PageWidget> = this.getBannerWidgetV2(cultUserTypes, userAgent, city.cityId,
            undefined, packOffersV3Response)
        const footerWidgetPromise: Promise<PageWidget> = this.getFooterWidget()
        if (_.isEmpty(diyActiveFulfilments)) {
            cultDIYWidgetPromises.push(this.getIntroVideoWidget(userAgent, this.landingPageConfig.diyIntroVideo))
        } else {
            cultDIYWidgetPromises.push(this.diyPackProgressWidget(userContext, userId, userAgent, diyActiveFulfilments))
        }

        cultDIYWidgetPromises.push(this.getDiyPackGridWidgetV2(userAgent, session.userId, isProgramPackUser, countryId))

        if (userAgent === "DESKTOP")
            cultDIYWidgetPromises.push(this.getHowItWorksWidget(this.landingPageConfig.diyHowItWorksTitle,
                this.landingPageConfig.diyHowItWorksItems))


        if (isCenterOperational) {
            // if (membershipData.isEligibleForFreeClass && _.isEmpty(membershipData.memberships)) {
            //     cultWidgetPromises.push(this.getOffersWidget())
            // }
            cultWidgetPromises.push(this.cultPackProgressWidget(userContext, userId, city, userAgent, cultOrMindSummary, undefined, centerId, appVersion, os))
            if (!activeOrFutureMembershipPresent) {
                const isNotLoggedInUser = AppUtil.isNotLoggedinUser(session)
                if (city.isCultAvailableForBooking) {
                    cultWidgetPromises.push(this.classBookWidget(isNotLoggedInUser, cultOrMindSummary, cultCityId, centerId))
                }
                if (this.getProductType() === "MIND" && cultOrMindSummary.trialEligibility) {
                    cultWidgetPromises.push(this.getCultWorkoutCategoriesWidget(userAgent, centerId))
                }
                cultWidgetPromises.push(this.getCultPackWidget(fitnessPacks, userContext, user, cultCityId, packOffersV3Response))

                // if (!_.isEmpty(programPacksResult.obj) && CultUtil.isProgramSupported(user, userAgent, appVersion, codePushVersion, os)) {
                //     cultWidgetPromises.push(this.getProgramPackWidget(programPacksResult.obj, programOffers, userAgent, appVersion, userId))
                // }
            } else {
                // if (!_.isEmpty(programPacksResult.obj) && CultUtil.isProgramSupported(user, userAgent, appVersion, codePushVersion, os)) {
                //     cultWidgetPromises.push(this.getProgramPackWidget(programPacksResult.obj, programOffers, userAgent, appVersion, userId))
                // }
                cultWidgetPromises.push(this.getCultPackWidget(fitnessPacks, userContext, user, cultCityId, packOffersV3Response))
            }


            // if (CultUtil.isOgmSupported(userAgent, appVersion)) // DEPRECATED
                // cultWidgetPromises.push(this.getOgmWidget(cultPacks, userAgent, appVersion))
            if (!activeOrFutureMembershipPresent) {
                const isNotLoggedInUser = AppUtil.isNotLoggedinUser(session)
                // cultWidgetPromises.push(this.classBookWidget(isNotLoggedInUser, membershipData, cultCityId, centerId))
                cultWidgetPromises.push(this.getIntroVideoWidget(userAgent, this.landingPageConfig.introVideo))
            }
            if (this.getProductType() === "FITNESS" || !cultCachedSummary.trialEligibility) {
                cultWidgetPromises.push(this.getCultWorkoutCategoriesWidget(userAgent, centerId))
            }

            if (!activeOrFutureMembershipPresent) {
                cultWidgetPromises.push(this.getExperienceWidget())
            }
            cultWidgetPromises.push(this.getCentersWidget(cultCityId, userContext, lat, lon))
            cultWidgetPromises.push(this.getCultTrainerWidget(userAgent, cultCityId))
            if (AppUtil.isNotLoggedinUser(session))
                cultWidgetPromises.push(this.getClpImageWidget())
            if (userAgent === "DESKTOP")
                cultWidgetPromises.push(this.getHowItWorksWidget(this.landingPageConfig.howItWorksTitle,
                    this.landingPageConfig.howItWorksItems))

        }

        const headerWidget = await headerWidgetPromise
        const footerWidget = await footerWidgetPromise
        const cultWidgets = await Promise.all(cultWidgetPromises)
        const cultDIYWidgets = await Promise.all(cultDIYWidgetPromises)
        const gearWidgets = await gearWidgetsPromise

        const page: CultClpPageV2 = {
            userLocation: isWithinBangalore ? "bangalore" : "others",
            sections: this.getSections(isCenterOperational, userAgent, includeGearContent),
            header: headerWidget,
            footer: footerWidget,

            lists: {
                "CENTER": cultWidgets.filter(cultWidget => { return cultWidget }),
                "DIY": cultDIYWidgets.filter(cultDIYWidget => { return cultDIYWidget }),
                "GEAR": gearWidgets
            }
        }

        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
            const eventWidgetPromises: Promise<PageWidget>[] = []
            eventWidgetPromises.push(this.getEventsGridWidget(userContext, userAgent, session.userId, cultCityId))
            const eventWidgets = await Promise.all(eventWidgetPromises)
            if (!_.isEmpty((<EventGridWidget>(eventWidgets[0])).items))
                page.lists.EVENTS = eventWidgets.filter(eventWidget => { return eventWidget })
            else
                _.remove(page.sections, (section) => { if (section.id == "EVENTS") return true; return false })
        }
        page.selectedTabIndex = selectedTab ? Object.keys(page.lists).indexOf(selectedTab) : 0
        page.cityName = session.sessionData.cityId ? city.name : undefined
        return page
    }

    private async classBookWidget(isNotLoggedInUser: boolean, cultOrMindSummary: CultOrMindSummary, cultCityId: number, centerId?: string): Promise<OfferInfoWidget> {
        const action: Action = {
            actionType: "NAVIGATION",
            url: this.getProductType() === "FITNESS" ? ActionUtil.bookCultClass("clp", centerId) : ActionUtil.bookMindClass("clp", centerId)
        }
        if (isNotLoggedInUser || (cultOrMindSummary.trialEligibility)) {
            const offerSubtitle = this.getProductType() === "FITNESS" ? "At any cult.fit center" : "At any mind.fit center"
            const widget: OfferInfoWidget = new OfferInfoWidget("Book 2 classes FREE!",
                offerSubtitle, "/image/icon/howItWorks/CombinedShape.png", action, this.getProductType())
            widget.analyticsData = {
                bookType: "freeClass"
            }
            return widget
        } else {
            const cultCity = await this.catalogueService.getCultCity(cultCityId)
            const title = `Classes starting from ${RUPEE_SYMBOL} ${cultCity.minPPCPrice}`
            const widget: OfferInfoWidget = new OfferInfoWidget("Book a class",
                title, "/image/icon/howItWorks/CombinedShape.png", action,
                this.getProductType())
            widget.analyticsData = {
                bookType: "PPC"
            }
            return widget
        }
    }

    protected async getCultWorkoutCategoriesWidget(userAgent: UserAgent, centerId: string): Promise<CategoryTileWidget> {
        const workoutCategories = await this.cultService.workoutCategories("CUREFIT_API", false)
        const cultWorkoutCategories: (ActionCard & { expanded: boolean })[] = []
        const sortedWorkoutCategories = workoutCategories.sort((a, b) => {
            return this.landingPageConfig.workoutIds.indexOf(a.id) < this.landingPageConfig.workoutIds.indexOf(b.id) ? -1 : 1
        })
        sortedWorkoutCategories.forEach(workoutCategory => {
            let workoutCategoryAction
            if (userAgent === "APP") {
                workoutCategoryAction = this.getProductType() === "FITNESS" ? ActionUtil.cultWorkoutCategory(workoutCategory.id.toString(), "cultfitclp") : ActionUtil.mindWorkoutCategory(workoutCategory.id.toString(), "mindfitclp")
            } else {
                workoutCategoryAction = this.getProductType() === "FITNESS" ? ActionUtil.cultWorkoutCategoryV1(workoutCategory.id.toString(), workoutCategory.name) : ActionUtil.mindWorkoutCategoryV1(workoutCategory.id.toString(), workoutCategory.name)
            }

            const cultWorkoutCategory: (ActionCard & { expanded: boolean }) = {
                title: workoutCategory.name,
                action: workoutCategoryAction,
                image: this.getWorkoutImage(userAgent, workoutCategory),
                expanded: false
            }
            cultWorkoutCategories.push(cultWorkoutCategory)
        })


        const widget: CategoryTileWidget =
            new CategoryTileWidget(
                cultWorkoutCategories,
                this.landingPageConfig.workoutTitle)
        return Promise.resolve(widget)
    }

    private getWorkoutImage(userAgent: UserAgent, workout: CultWorkout | CultWorkoutCategory) {
        if (userAgent === "DESKTOP") {
            const document = _.find(workout.documents, { tagName: "D_CLP" })
            return document ? "/" + document.URL : undefined
        } else {
            const document = _.find(workout.documents, { tagName: "M_CLP" })
            return document ? "/" + document.URL : undefined
        }
    }
    private getOffersWidget(): Promise<OfferWidget> {
        const widget: OfferWidget = new OfferWidget(this.landingPageConfig.freeTrialOffer)
        return Promise.resolve(widget)
    }

    private async getDiyPackGridWidgetV2(userAgent: UserAgent, userId: string, isProgramPackUser: boolean, countryId: string): Promise<DiyWidget> {
        try {
            const diySeries: DIYSeries[] = await this.DIYFulfilmentService.getDIYSeries(userId, this.getDiyProductType(), countryId)
            const filteredSeries = _.filter(diySeries, series => {
                if (series.productType !== this.getDiyProductType()) {
                    return false
                }
                return true
            })
            filteredSeries.sort((a, b) => {
                return a.ordering < b.ordering ? -1 : 1
            })
            const seriesItems: DiySeries[] = []
            const productIds: string[] = filteredSeries.reduce((productIds, series) => {
                productIds.push(...series.packIds)
                return productIds
            }, [])
            const productsMap = await this.catalogueService.getProductMap(productIds)
            filteredSeries.forEach(series => {
                const packActionCards: ActionCard[] = []
                series.packIds.forEach(packId => {
                    const pack = <DIYPack>productsMap[packId]
                    let sessionType = this.getProductType() === "FITNESS" ? "fitness"
                        : "meditation"
                    if (!_.isEmpty(pack.tags)) {
                        sessionType = pack.tags[0]
                    }
                    const seoParams: SeoUrlParams = {
                        productName: pack.title
                    }
                    if (pack.displayStatus === "VISIBLE") {
                        packActionCards.push({
                            title: pack.title,
                            subTitle: `${pack.sessionIds.length} ${sessionType} sessions`,
                            image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? pack.imageDetails.magazineImage : pack.imageDetails.magazineWebImage)),
                            action: ActionUtilV1.diyPackProductPage(pack, userAgent)
                        })
                    }

                })
                if (!_.isEmpty(packActionCards)) {
                    const seriesItem: DiySeries = {
                        title: series.name,
                        actionCards: packActionCards
                    }
                    seriesItems.push(seriesItem)
                }

            })
            const diyWidget: DiyWidget = {
                widgetType: "GROUPED_MAGAZINE_LIST_WIDGET",
                hasBottomPadding: false,
                hasTopPadding: false,
                items: seriesItems,
                widgetTitle: this.landingPageConfig.DIYPacksTitle
                // moreWidget: this.landingPageConfig.DIYPacksMoreAction
            }
            return diyWidget
        } catch (error) {
            this.logger.error("Error fetching diy pack details " + error)
            return null
        }
    }
    private async getIntroVideoWidget(userAgent: string, video: Video): Promise<VideoCarouselWidget> {
        const clonedVideo: Video = _.clone(video)
        clonedVideo.image = userAgent === "DESKTOP" ? video.desktopWebImage : video.image
        const videoCarouselWidget: VideoCarouselWidget = {
            widgetType: "VIDEO_CAROUSEL_WIDGET",
            hasBottomPadding: false,
            hasTopPadding: false,
            items: [clonedVideo]
        }
        return videoCarouselWidget
    }

    private async getCultPackWidget(fitnessPacks: OfflineFitnessPack[], userContext: UserContext, user: User, cultCityId: number,
        packOffersV3Response?: CultProductPricesResponse): Promise<GridWidget | MagazineListWidget> {
        let isOfferAvailable = false
        const packActionCards: PackActionCard[] = []
        const city = await this.cityService.getCityByCultCityId(cultCityId)
        _.map(fitnessPacks, fitnessPack => {
            // Don't show 12 months pack at CLP
            if (fitnessPack.status === "ACTIVE" && !["CULTPACK16", "CULTPACK23", "CULTPACK26"].includes(fitnessPack.productId)) {
                const offerDetails = CultUtil.getOfferDetailsPMS(fitnessPack, packOffersV3Response)
                let price
                if (IS_NEW_CULT_OFFER_VERSION) {
                    price = offerDetails.price
                } else {
                    price = fitnessPack.price
                }
                const seoParams: SeoUrlParams = {
                    city: city ? city.name : undefined,
                    productName: fitnessPack.title
                }
                const actionCard: PackActionCard = {
                    title: fitnessPack.title,
                    subTitle: CultUtil.getDescriptionFromPackId(fitnessPack.productId),
                    image: CatalogueServiceUtilities.getFitnessProductImage(fitnessPack, "MAGAZINE", userContext.sessionInfo.userAgent),
                    action: CatalogueServiceUtilities.getPackPageAction(fitnessPack, userContext.sessionInfo.userAgent, seoParams, false, "cultfitclp"),
                    price: price,
                    analyticsData: {
                        // packId: CatalogueServiceUtilities.extractPackId(fitnessPack.productId),
                        productId: fitnessPack.productId
                    },
                    productType: this.getProductType()
                }
                packActionCards.push(actionCard)
                if (actionCard.price.listingPrice < actionCard.price.mrp)
                    isOfferAvailable = true
            }
        })
        const widgetSubTitle: string = "Unlimited access to workouts at all centres"
        if (userContext.sessionInfo.userAgent === "DESKTOP" || userContext.sessionInfo.userAgent === "MBROWSER")
            return new MagazineGridWidget(packActionCards, this.landingPageConfig.packsTitle)
        else {
            const widget = new MagazineListWidget(packActionCards, this.landingPageConfig.packsTitle, isOfferAvailable)
            widget.analyticsData = {
                widgetType: "CLP_PACK_BROWSE"
            }
            return widget
        }

    }

    private async getCultTrainerWidget(userAgent: UserAgent, cultCityId: number): Promise<ProfileListWidget> {
        const trainers = await this.cultService.browseFitnessTrainer(undefined, "CUREFIT_API", cultCityId)
        const actionCards: ActionCard[] = []
        trainers.forEach(trainer => {
            const trainerView = new TrainerView(trainer, userAgent, true)
            actionCards.push({
                title: trainerView.name,
                subTitle: "",
                description: trainerView.description,
                image: trainerView.image,
                action: ActionUtil.trainerInfoCard(trainerView.name, "", trainerView.description, trainerView.image)
            })
        })
        const profileListWidget: ProfileListWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "PROFILE_LIST_WIDGET",
            widgetTitle: this.landingPageConfig.trainersTitle,
            actioncards: actionCards.slice(0, 8),
            moreWidget: actionCards.length > 8 ? this.landingPageConfig.trainersMoreAction : undefined
        }
        if (trainers.length > 0)
            return profileListWidget
        else
            return undefined
    }

    private async getExperienceWidget(): Promise<ExperienceWidget> {
        const experienceWidget: ExperienceWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "EXPERIENCE_WIDGET",
            widgetTitle: this.landingPageConfig.experienceSpaceTitle,
            actioncards: this.landingPageConfig.experienceItems,
            moreWidget: this.landingPageConfig.experienceMoreAction
        }
        return experienceWidget
    }

    private async getCentersWidget(cultCityId: number, userContext: UserContext, lat?: number, lon?: number): Promise<CenterAreaWidget | CultCenterGridWidget> {
        if (userContext.sessionInfo.userAgent === "APP") {
            return this.getCultCenterAppWidget(userContext, cultCityId, lat, lon)
        } else {
            return this.getCultCenterWebWidget(cultCityId, userContext)
        }
    }

    private async getCultCenterWebWidget(cultCityId: number, userContext?: UserContext): Promise<CenterAreaWidget> {
        const centers = await this.cultService.browseFitnessCenter(userContext.userProfile.timezone, "CUREFIT_API", false, cultCityId, true, undefined, false)
        const centerByLocation = _.groupBy(centers, center => { return center.locality })
        const centerAreaActionCards: CenterAreaActionCard[] = []
        const city = await this.cityService.getCityByCultCityId(cultCityId)
        Object.keys(centerByLocation).forEach(location => {
            const centers = centerByLocation[location]
            const centerViews = _.map(centers, center => { return new CenterView(userContext, center, this.getProductType(), city.name) })
            const centerAreaActionCard: CenterAreaActionCard = {
                action: "",
                title: location,
                subTitle: centers.length === 1 ? `${centers.length} center` : `${centers.length} centers`,
                centers: centerViews
            }
            centerAreaActionCards.push(centerAreaActionCard)
        })
        const centerAreaWidget: CenterAreaWidget = {
            widgetTitle: this.landingPageConfig.centerTitle,
            actioncards: centerAreaActionCards,
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "CULT_CENTER_AREA_WIDGET"
        }
        return centerAreaWidget
    }

    private getCultCenterAppWidget(userContext: UserContext, cultCityId: number, lat?: number, lon?: number): Promise<CultCenterGridWidget> {
        return this.cultService.browseFitnessCenter(userContext.userProfile.timezone, "CUREFIT_API", false, cultCityId, true).then(centers => {
            if (!_.isNil(lat) && !_.isNil(lon))
                LocationUtil.sortCentersByLocation(lat, lon, centers)
            const cultCenterActionCards: ActionCard[] = []
            centers.forEach(center => {
                const seoParams: SeoUrlParams = {
                    locality: center.locality,
                    productName: center.name
                }
                const action = this.getProductType() === "FITNESS" ? ActionUtil.cultCenter(center.id.toString(), "cultfitclp", userContext.sessionInfo.userAgent, seoParams) : ActionUtil.mindCenter(center.id.toString(), "mindfitclp", userContext.sessionInfo.userAgent, seoParams)
                const cultCenterActionCard: ActionCard = {
                    title: center.name,
                    subTitle: center.info,
                    action: action,
                    image: center.documents[0].URL
                }
                cultCenterActionCards.push(cultCenterActionCard)
            })
            const widget: CultCenterGridWidget =
                new CultCenterGridWidget(
                    cultCenterActionCards.slice(0, 6),
                    this.landingPageConfig.centerTitle,
                    this.landingPageConfig.centerMoreAction
                )

            return Promise.resolve(widget)
        })
    }

    private async getHowItWorksWidget(howItWorksTitle: PageWidgetTitle, howItWorksItems: HowItWorksItem[]): Promise<HowItWorksWidget> {
        const howItWorksWidget: HowItWorksWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            widgetType: "HOW_IT_WORKS_WIDGET",
            widgetTitle: howItWorksTitle,
            items: howItWorksItems
        }
        return howItWorksWidget
    }

    private async getClpImageWidget(): Promise<ClpImageWidget> {
        const clpImageWidget: ClpImageWidget = {
            widgetType: "CLP_IMAGE_WIDGET",
            hasBottomPadding: false,
            hasTopPadding: false,
            title: this.landingPageConfig.becomeMemberItem.title,
            action: {
                title: this.landingPageConfig.becomeMemberItem.subTitle,
                actionType: "NAVIGATION",
                url: this.getProductType() === "FITNESS" ? ActionUtil.bookCultClass("clp") : ActionUtil.bookMindClass("clp")
            }
        }
        return clpImageWidget
    }

    private async getBannerWidgetV2(cultUserTypes: CultUserType[], userAgent: UserAgent, cityId: string,
        packOffersResponse: PackOffersResponse, packOffersV3Response?: CultProductPricesResponse): Promise<BannerWidget> {

        const offerIds: string[] = []
        if (packOffersV3Response) {
            offerIds.push(..._.keys(packOffersV3Response.offerMap))
        } else if (Object.keys(packOffersResponse)) {
            Object.keys(packOffersResponse).forEach(productId => {
                const packOffer = packOffersResponse[productId]
                offerIds.push(..._.map(packOffer.offers, offer => { return offer.offerId }))
                offerIds.push(..._.map(packOffer.preBuzzOffers, offer => { return offer.offerId }))
                return offerIds
            })
        }

        // let cultUserType: CultUserType = "ALL"
        // if (isUpForRenewal) {
        //     cultUserType = "UP_FOR_RENEWAL"
        // } else if (isActiveOfFutureMembershipPresent) {
        //     cultUserType = "WITH_PACK"
        // } else if (isEligibleForAPreRegistrationOffer) {
        //     cultUserType = "PRE_REGISTRATION_USER"
        // } else {
        //     cultUserType = "WITHOUT_PACK"
        // }
        const bannerCondition: BannerQueryCondition = {
            cityId: cityId,
            status: "LIVE",
            bannerVersion: 1,
            userAgent: userAgent,
            vertical: this.getProductType() === "FITNESS" ? "CULT_FIT" : "MIND_FIT",
            cultUserTypes: cultUserTypes,
            offerIds: offerIds,
            timeInstanceWithTz: { date: new Date(), timezone: TimeUtil.IST_TIMEZONE }
        }
        const bannersBasedOnCondition = this.bannerService.getBanners(bannerCondition)
        const banners: Promise<Banner>[] = _.map(bannersBasedOnCondition, async (bannerBasedOnCondition) => {
            let bannerImage
            if (userAgent === "DESKTOP")
                bannerImage = "/" + bannerBasedOnCondition.webImage
            else if (userAgent === "MBROWSER")
                bannerImage = "/" + bannerBasedOnCondition.mWebImage
            else
                bannerImage = "/" + bannerBasedOnCondition.appImage
            const banner: Banner = {
                id: bannerBasedOnCondition.bannerId,
                action: await this.bannerService.getBannerAction(bannerBasedOnCondition, userAgent),
                image: bannerImage
            }
            return banner
        })
        const bannerRatio = this.getProductType() === "FITNESS" ? CULT_BANNER_RATIO_MAP.get(userAgent) :
            MIND_BANNER_RATIO_MAP.get(userAgent)
        const widget: BannerWidget = new BannerWidget((await Promise.all(banners)).slice(0, 3), bannerRatio)
        return Promise.resolve(widget)
    }

    private async cultPackProgressWidget(userContext: UserContext, userId: string, currentCity: City, userAgent: UserAgent,
        cultOrMindSummary: CultOrMindSummary, programMemberships: ProgramMembership[], centerId: string,
        appVersion: number, os: string): Promise<PackProgressListWidget> {
        const tz = userContext.userProfile.timezone
        const baseBookingAction = this.getProductType() === "FITNESS" ? ActionUtil.bookCultClass("cultCLP") : ActionUtil.bookMindClass("cultCLP")

        // push memberships to be built
        const memberships: MembershipDetails[] = []
        if (_.isNil(cultOrMindSummary.currentMembership) && _.isNil(cultOrMindSummary.upcomingMembership)
            && !_.isNil(cultOrMindSummary.previousMembership)) {
            // render past membership only if no current or future membership
            memberships.push(cultOrMindSummary.previousMembership)
        } else {
            if (!_.isNil(cultOrMindSummary.currentMembership)) {
                memberships.push(cultOrMindSummary.currentMembership)
            }
            if (!_.isNil(cultOrMindSummary.upcomingMembership)) {
                memberships.push(cultOrMindSummary.upcomingMembership)
            }
        }

        const packProgressPromises = _.map(memberships, async (membershipDetails) => {
            const isExpired = cultOrMindSummary.previousMembership === membershipDetails
            const isUpcoming = cultOrMindSummary.upcomingMembership === membershipDetails
            const packProductId = membershipDetails.productId
            const pack = await this.catalogueServicePMS.getProduct(packProductId)
            const membershipId = await CultUtil.getMembershipIdByCultMembershipId(userContext, membershipDetails.id.toString(), this.membershipService)
            const packPageAction = await CatalogueServiceUtilities.getCultMembershipDetailsPageAction(membershipId, userContext, undefined, "cultfitclp")
            const packBrowseAction = this.getProductType() === "FITNESS" ? `curefit://cultbrowsepage` : `curefit://mindcenterpackbrowse`
            const todayDateString = TimeUtil.todaysDateWithTimezone(tz)
            const daysRemainingInMembership = TimeUtil.diffInDays(tz, membershipDetails.endDate, todayDateString)

            const today = TimeUtil.getMomentForDateString(todayDateString, tz)
            const endDate = TimeUtil.getMomentForDateString(membershipDetails.endDate, tz)
            const startDate = TimeUtil.getMomentForDateString(membershipDetails.startDate, tz)
            const total = endDate.diff(startDate, "days")
            const current = today.diff(startDate, "days")
            const packProgressView: PackProgress = {
                productType: this.getProductType(),
                title: CultUtil.getDescriptionFromPackId(membershipDetails.productId, this.getProductType()),
                subTitle: isExpired ? daysRemainingInMembership + " days left" : "Ends on " + TimeUtil.formatDateStringInTimeZone(membershipDetails.endDate, tz, "DD MMM"),
                action: {
                    actionType: "NAVIGATION",
                    url: packPageAction
                },
                state: membershipDetails.state,
                image: CatalogueServiceUtilities.getFitnessProductImage(pack, "MAGAZINE", userAgent),
                widgetActions: [{
                    title: "Book a Class",
                    viewType: "PRIMARY_BUTTON",
                    actionType: "NAVIGATION",
                    url: centerId && centerId !== "undefined" ? `${baseBookingAction}&centerId=${centerId}` : baseBookingAction
                }],
                total: total,
                completed: current,
                // packId: CatalogueServiceUtilities.extractPackId(packProductId).toString(),
                clientState: membershipDetails.state
            }

            // For Paused Pack
            if (membershipDetails.state === "PAUSED" && CultUtil.isPausePackOnCLPSupported(appVersion, os)) {
                packProgressView.widgetActions = [{
                    title: "Resume Pack",
                    viewType: "PRIMARY_BUTTON",
                    actionType: "RESUME_CULT_MEMEBERSHIP",
                    meta: {
                        membershipId: membershipDetails.id,
                        productId: packProductId,
                        productType: pack.productType,
                    }
                }]
            }

            // For Expired Pack
            if (endDate.isBefore(today)) {
                packProgressView.action = {
                    actionType: "NAVIGATION",
                    url: packBrowseAction
                }
                packProgressView.widgetActions = [{
                    title: "Renew Pack",
                    actionType: "NAVIGATION",
                    url: packBrowseAction,
                    viewType: "PRIMARY_BUTTON"
                }]
                const diffDays = today.diff(endDate, "days")
                if (diffDays == 1) {
                    packProgressView.subTitle = "Ended 1 day ago"
                } else if (diffDays > 1) {
                    packProgressView.subTitle = `Ended ${diffDays} days ago`
                } else {
                    packProgressView.subTitle = ""
                }
            }

            if (startDate.isAfter(today)) {
                packProgressView.subTitle = "Starting from " + TimeUtil.formatDateStringInTimeZone(membershipDetails.startDate, tz, "DD MMM")
            }


            if (currentCity.cultCityId !== membershipDetails.cityId) {
                const detailedMembership = await this.cultService.getMembershipById(membershipDetails.id, userId)
                if (detailedMembership.freeAwayClassesRemainingForMonth < UNLIMITED_FREE_AWAY_CLASSES_THRESHOLD) {
                    packProgressView.awayCityMessage = `${currentCity.name} | ${detailedMembership.freeAwayClassesRemainingForMonth} classes free`
                }
            }

            if (membershipDetails.isPreReg) {
                packProgressView.widgetActions.push({
                    title: "Complete your payment",
                    actionType: "NAVIGATION",
                    url: packPageAction,
                    viewType: "SECONDARY_BUTTON"
                })
            }
            return packProgressView
        })
        let packProgress: PackProgress[] = await Promise.all(packProgressPromises)

        const hasFuture = _.filter(packProgress, { clientState: "FUTURE" })
        const hasActive = _.filter(packProgress, { clientState: "ACTIVE" })
        if (hasFuture.length > 0 || hasActive.length > 0) {
            // removing EXPIRED Membership, if packProgress has future and active memberships.
            packProgress = _.filter(packProgress, (obj) => {
                return obj && obj.clientState !== "EXPIRED"
            })
        }
        if (hasFuture.length > 0 && hasActive.length > 0) { // remove renew action if has future pack.
            const activeMembership = hasActive[0]
            _.remove(activeMembership.widgetActions, { viewType: "SECONDARY_BUTTON" })
        }

        const itemList = packProgress.filter(item => item)

        // if (programMemberships) { Always undefined
        //     const programPackProgressPromises = _.map(programMemberships, async (programMembership) => {
        //         const packId = programMembership.packId
        //         const productId = CatalogueServiceV2Utilities.getProgramPackProductId(packId)
        //         const programPackProduct = <ProgramPackProduct>await this.catalogueService.getProduct(productId)
        //         const startDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(programMembership.startAt))
        //         const endDate = TimeUtil.formatDateInTimeZone(tz, TimeUtil.parseDateFromEpoch(programMembership.endAt))
        //         const today = TimeUtil.todaysDateWithTimezone(tz)
        //         const packDuration = TimeUtil.diffInDays(tz, startDate, endDate)
        //         const daysCompleted = today > startDate ? TimeUtil.diffInDays(tz, startDate, today) : 0


        //         let subTitle
        //         if (startDate > today) {
        //             subTitle = "Starting from " + TimeUtil.formatDateStringInTimeZone(startDate, tz, "DD MMM")
        //         } else {
        //             subTitle = "Ends on " + TimeUtil.formatDateStringInTimeZone(endDate, tz, "DD MMM")

        //         }
        //         const packProgressView: PackProgress = {
        //             productType: "PROGRAM",
        //             title: programPackProduct.title,
        //             subTitle: subTitle,
        //             image: UrlPathBuilder.getPackImagePath(programPackProduct.productId, programPackProduct.productType, "MAGAZINE", programPackProduct.imageVersion, userAgent),
        //             total: TimeUtil.diffInDays(tz, startDate, endDate),
        //             completed: daysCompleted,
        //             packId: programPackProduct.packId,
        //             action: {
        //                 actionType: "NAVIGATION",
        //                 url: ActionUtil.programPack(programPackProduct.packId, this.getProductType(), programMembership.id)
        //             },
        //             state: "ACTIVE",
        //             widgetActions: [{
        //                 title: "Book a Class",
        //                 viewType: "PRIMARY_BUTTON",
        //                 actionType: "NAVIGATION",
        //                 url: centerId && centerId !== "undefined" ? `${baseBookingAction}&centerId=${centerId}` : baseBookingAction
        //             }]
        //         }
        //         return packProgressView
        //     })
        //     const programPackProgress = await Promise.all(programPackProgressPromises)
        //     itemList.push(...programPackProgress)
        // }

        if (itemList.length > 0) {
            const packProgressWidget: PackProgressListWidget = {
                widgetType: "PACK_PROGRESS_LIST_WIDGET",
                items: itemList,
                hasBottomPadding: false,
                hasTopPadding: false
            }
            return packProgressWidget
        }
        return undefined

    }


    private async getDerivedCityDetails(cityId: string, userContext: UserContext, cultOrMindSummary: CultOrMindSummary): Promise<{ cultCityId: number, cityId: string }> {
        const tenant: Tenant = AppUtil.getTenantFromUserContext(userContext)
        const city = cityId ? await this.cityService.getCityById(cityId) : await this.cityService.getDefaultCity(tenant)
        if (this.cityService.checkIfCityIsOtherCity(city.cityId)) {

            if (!_.isNil(cultOrMindSummary.currentMembership)) {
                const derivedCity = await this.cityService.getCityByCultCityId(cultOrMindSummary.currentMembership.cityId)
                return { cultCityId: derivedCity.cultCityId, cityId: derivedCity.cityId }
            }
        }
        return { cultCityId: city.cultCityId, cityId: city.cityId }
    }

    private async diyPackProgressWidget(userContext: UserContext, userId: string, userAgent: UserAgent, fulfilments: DIYPackFulfilment[]): Promise<PackProgressListWidget> {
        const packProgressPromises = _.map(fulfilments, async (fulfilment) => {
            const packs = this.getDiyPackProductType() === "DIY_FITNESS_PACK" ? await this.DIYFulfilmentService.getDIYFitnessPacksForIds(userId, [fulfilment.productId]) : await this.DIYFulfilmentService.getDIYMeditationPacksForIds(userId, [fulfilment.productId])
            const pack = packs[0]
            const packPageAction = ActionUtilV1.diyPackProductPage(pack, userAgent, this.getDiyPackProductType() === "DIY_FITNESS_PACK" ? "cultfitclp" : "mindfitclp")
            const totalSessions = pack.sessionIds.length
            const sessionCompleted = fulfilment.completedProductIds ? fulfilment.completedProductIds.length : 0
            const packProgressView: PackProgress = {
                productType: this.getProductType(),
                title: pack.title,
                subTitle: `${sessionCompleted} of ${pack.sessionIds.length} sessions completed`,
                action: {
                    actionType: "NAVIGATION",
                    url: packPageAction
                },
                widgetActions: [],
                state: fulfilment.status,
                image: UrlPathBuilder.prefixSlash((userAgent === "APP" ? pack.imageDetails.magazineImage : pack.imageDetails.magazineWebImage)),
                total: pack.sessionIds.length,
                completed: fulfilment.completedProductIds ? fulfilment.completedProductIds.length : 0,
            }
            if (totalSessions !== sessionCompleted) {
                const nextSessionId = pack.sessionIds[LiveUtil.getNextDIYSessionIdx(pack, fulfilment)]
                const tenant = AppUtil.getTenantFromUserContext(userContext)
                const nextSessions = this.getDiyPackProductType() === "DIY_FITNESS_PACK" ? await this.DIYFulfilmentService.getDIYFitnessProductsByProductIds(userId, [nextSessionId], tenant) : await this.DIYFulfilmentService.getDIYMeditationProductsByProductIds(userId, [nextSessionId], tenant)
                const nextSession = nextSessions[0]
                if (userAgent === "APP") {
                    const nextSessionAction = this.getDiyPackProductType() === "DIY_FITNESS_PACK" ? ActionUtil.cultDiySingle(pack.productId, nextSession.productId, nextSession.productType, "cultfitclp") : ActionUtil.mindDiySingle(pack.productId, nextSession.productId, nextSession.productType, "mindfitclp")
                    packProgressView.widgetActions.push({
                        title: "Start your next session",
                        actionType: "NAVIGATION",
                        url: nextSessionAction,
                        viewType: "PRIMARY_BUTTON"
                    })
                } else {
                    packProgressView.widgetActions.push({
                        title: "Start your next session",
                        actionType: "PLAY_VIDEO",
                        viewType: "PRIMARY_BUTTON",
                        image: UrlPathBuilder.prefixSlash(nextSession.imageDetails.heroImage),
                        content: AtlasUtil.getContentDetailV2(nextSession),
                        meta: AtlasUtil.getContentMetaV2(nextSession, pack)
                    })
                }

            }
            return packProgressView
        })
        const packProgress: PackProgress[] = await Promise.all(packProgressPromises)
        const packProgressWidget: PackProgressListWidget = {
            widgetType: "PACK_PROGRESS_LIST_WIDGET",
            items: packProgress,
            hasBottomPadding: false,
            hasTopPadding: false
        }
        return packProgressWidget
    }
    private async getFooterWidget(): Promise<ClpFooterWidget> {
        const footerWidget: ClpFooterWidget = {
            hasBottomPadding: false,
            hasTopPadding: false,
            texts: this.landingPageConfig.footerTexts,
            widgetType: "CLP_FOOTER_WIDGET",
            image: this.landingPageConfig.footerImage
        }
        return footerWidget
    }
    private async getEventsGridWidget(userContext: UserContext, userAgent: UserAgent, userId: string, cityId: number): Promise<EventGridWidget> {

        const fitnessEvents: FitnessEvent[] = await this.getEventsListing(userId, cityId)
        const currentEvents: EventItem[] = []

        for (let i: number = 0; i < fitnessEvents.length; i++) {
            const actions: Action[] = []
            const fitevent: FitnessEvent = fitnessEvents[i]
            let actionurl: string = "curefit://eventdetail?eventId=" + fitevent.id.toString()
            if (this.getProductType() === "MIND") {
                actionurl = "curefit://mindeventdetail?eventId=" + fitevent.id.toString()
            }
            let ctaText: string = undefined
            if (fitevent.metaDataObject && fitevent.metaDataObject.ctaText) {
                ctaText = fitevent.metaDataObject.ctaText
            }
            if (fitevent.cultAppAvailableSeats > 0) {
                actions.push({
                    title: ctaText ? ctaText : "Join Event",
                    actionType: "JOIN_EVENT",
                    url: actionurl
                })
            } else {
                actions.push({
                    title: "Event Closed",
                    actionType: "CLOSED_EVENT"
                })
            }

            const tagName = userAgent === "MBROWSER" ? "M_CLP" : "D_CLP"

            const imageDoc = fitevent.documents.filter((x) => { if (x.tagName === tagName) return true; return false })
            const eventitem: EventItem = {
                stock: fitevent.cultAppAvailableSeats,
                actions: actions,
                eventDate: _.isNil(fitevent.launchDate) ? null : TimeUtil.getMomentForDateString(fitevent.launchDate, userContext.userProfile.timezone).format("Do MMM, YYYY"),
                eventLocation: _.isNil(fitevent.Address) ? null : fitevent.Address.locality,
                eventName: fitevent.name,
                eventPrice: { listingPrice: fitevent.price, mrp: fitevent.price, currency: "INR" },
                image: _.isEmpty(imageDoc) ? null : imageDoc[0].URL
            }
            currentEvents.push(eventitem)
        }
        const eventGridWidget: EventGridWidget = {
            titleWidget: { title: "Upcoming Events" },
            widgetType: "EVENT_GRID_WIDGET",
            items: currentEvents,
            hasTopPadding: false,
            hasBottomPadding: false
        }
        return eventGridWidget

    }
}

export default BaseCultLandingPageBuilder
