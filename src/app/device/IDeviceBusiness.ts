import { Device, DeviceInfo } from "@curefit/device-common"
import { Session, SessionData } from "@curefit/userinfo-common"
import { Tenant } from "@curefit/base-common"
import { AttributeAction, EventType } from "@curefit/rashi-client"

interface IDeviceBusiness {
    getAllUserDevices(userId: string, expireRegistrationDoneBefore: Date, tenant: Tenant): Promise<Device[]>
    createNewDevice(deviceId: string, deviceInfo: DeviceInfo, isLoggedIn: boolean, at: string): Promise<Device>
    getDeviceByDeviceId(deviceId: string, tenant: Tenant): Promise<Device>
    updateDevice(existingDevice: Device, tenant: Tenant): Promise<Device>
    updateSessionInformation(deviceId: string, sessionData: SessionData, tenant: Tenant, appVersion?: number, codePushVersion?: string, lat?: number, long?: number, advertiserId?: string): Promise<boolean>
    updatePushNotificationToken(session: Session, deviceId: string, pushNotificationToken: string, tenant: Tenant, isInternalUser: boolean): Promise<{ isSessionUpdateNeeded: boolean }>
    sendNotification(userId: string, payload: any, tenant: Tenant): Promise<boolean>
    logoutDevice(deviceId: string, tenant: Tenant): Promise<boolean>
    getActiveDevice(userId: string, tenant: Tenant): Promise<DeviceInfo[]>
    bulkLogoutDevice(userId: string, expireRegistrationDoneBefore: Date, tenant: Tenant): Promise<Boolean>
    deRegisterPushNotificationToken(userId: string, deviceId: string, pushNotificationToken: string): Promise<Device>
    updateDeviceAndDeviceInfo(device: Device, deviceInfo: DeviceInfo): Promise<Device>
    sendDeviceToRashi(deviceId: string, oldDeviceUserId: string, newDeviceData: DeviceInfo, isNotLoggedInFlow: boolean, tenant: Tenant): Promise<boolean>
    publishUserEventToRashi(userId: string | Number, body: any, action: AttributeAction, tenant: Tenant): Promise<boolean>
}

export default IDeviceBusiness
