import * as express from "express"
import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { Session, UserContext } from "@curefit/userinfo-common"
import IDeviceBusiness from "./IDeviceBusiness"
import AuthMiddleware from "../auth/AuthMiddleware"
import AppUtil from "../util/AppUtil"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ISessionBusiness } from "@curefit/base-utils"
import { Tenant } from "@curefit/base-common"

export function controllerFactory(kernel: Container) {
    @controller("/device",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class DeviceController {

        constructor(@inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
            @inject(BASE_TYPES.ILogger) protected logger: Logger,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionBusiness
        ) {
        }

        @httpPost("/updatePushNotificationToken")
        public async updatePushNotificationToken(req: express.Request): Promise<boolean> {
            const pushNotificationToken: string = req.body.pushNotificationToken
            const deviceId: string = req.headers["deviceid"] as string
            const session: Session = req.session
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            this.logger.info("deviceid " + deviceId)
            const userContext: UserContext = req.userContext as UserContext
            const isInternalUser = (await userContext.userPromise).isInternalUser
            const result = await this.deviceBusiness.updatePushNotificationToken(session, deviceId, pushNotificationToken, tenant, isInternalUser)
            if (result && result.isSessionUpdateNeeded) {
                await this.sessionBusiness.updateDeviceIdInSession(session.at, deviceId, session.isNotLoggedIn)
            }
            return true
        }

    }

    return DeviceController
}

export default controllerFactory

