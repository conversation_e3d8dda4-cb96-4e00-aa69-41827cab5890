import IDeviceBusiness from "./IDeviceBusiness"
import { Session, SessionData } from "@curefit/userinfo-common"
import { DetectedCity, Device, DeviceInfo, DeviceState } from "@curefit/device-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { inject, injectable } from "inversify"
import { ILockAccess, RedlockAccess } from "@curefit/lock-utils"
import IFirebaseService from "../common/firebase/IFirebaseService"
import { BASE_TYPES, Logger } from "@curefit/base"
import { ErrorFactory } from "@curefit/error-client"
import { NotificationPayload } from "../ugc/NotificationQueueListener"
import * as _ from "lodash"
import { ErrorCodes } from "../error/ErrorCodes"
import { AttributeAction, EventType, IRashiSnsClient, RASHI_CLIENT_TYPES, UserEvent } from "@curefit/rashi-client"
import AppUtil from "../util/AppUtil"
import { AppTenant, Tenant } from "@curefit/base-common"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { DeviceDetail } from "@curefit/user-common/dist/src/DeviceDetail"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"

const NUM_OLD_DEVICES_TO_STORE: number = 20
const ANDROID_APP_VERSION_FOR_NOTIFICATION = 4.23
@injectable()
class DeviceBusiness implements IDeviceBusiness {

    private lockAccess: ILockAccess

    constructor(
        @inject(CUREFIT_API_TYPES.FirebaseService) private firebaseService: IFirebaseService,
        @inject(REDIS_TYPES.MultiRedisAccess) private multiRedisAccess: MultiRedisAccess,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(ERROR_COMMON_TYPES.RollbarService) private rollbarService: RollbarService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(RASHI_CLIENT_TYPES.RashiSnsClient) private rashiSnsClient: IRashiSnsClient
    ) {
        this.lockAccess = new RedlockAccess(this.multiRedisAccess, logger, "CFAPI-CACHE")
    }

    async getAllUserDevices(userId: string, expireRegistrationDoneBefore: Date, tenant: Tenant): Promise<Device[]> {
        try {
            const expireRegistrationDoneBeforeS: string = expireRegistrationDoneBefore.valueOf().toString()
            const devicesDetail: DeviceDetail[] = await this.userService.getLoggedInDevicesForUserRegisterDate(userId, tenant.toString(), expireRegistrationDoneBeforeS)
            const devices: Device[] = []
            for (const deviceDetail of devicesDetail) {
                devices.push(this.createDevicefromDeviceDetail(deviceDetail))
            }
            return devices
        } catch (err) {
            return null
        }
    }

    async getDeviceByDeviceId(deviceId: string, tenant: Tenant): Promise<Device> {
        try {
            const deviceDetail: DeviceDetail = await this.userService.getDeviceByIdAndTenant(deviceId, tenant.toString())
            return this.createDevicefromDeviceDetail(deviceDetail)
        } catch (err) {
            return null
        }
    }

    async createNewDevice(deviceId: string, deviceInfo: DeviceInfo, isLoggedIn: boolean, at: string): Promise<Device> {
        const device: Device = {
            deviceId: deviceId,
            activeDevice: deviceInfo,
            userId: deviceInfo.userId,
            isLoggedIn: isLoggedIn,
            at: at
        }
        await this.persistDeviceInUserService(device)
        return device
    }

    async bulkLogoutDevice(userId: string, expireRegistrationDoneBefore: Date, tenant: Tenant): Promise<Boolean> {
        const maxLoginTimeMs: number = new Date().valueOf()
        const expireRegistrationDoneBeforeS: string = expireRegistrationDoneBefore.valueOf().toString()
        return this.userService.logoutAllDevicesForUser(userId, tenant, maxLoginTimeMs, expireRegistrationDoneBeforeS)
    }

    async updateDevice(existingDevice: Device, tenant: Tenant): Promise<Device> {
        const updatedDevice: DeviceDetail = await this.persistDeviceInUserService(existingDevice)
        return this.createDevicefromDeviceDetail(updatedDevice)
    }

    async updateSessionInformation(deviceId: string, sessionData: SessionData, tenant: Tenant, appVersion?: number, codePushVersion?: string, lat?: number, long?: number, advertiserId?: string): Promise<boolean> {
        const lock = await this.lockAccess.lockResource(deviceId, 5000)
        try {
            const device = await this.getDeviceByDeviceId(deviceId, tenant)
            if (device) {
                device.activeDevice.gateId = sessionData.gateId
                device.activeDevice.locationName = sessionData.locationName
                device.activeDevice.locationAddress = sessionData.locationAddress
                device.activeDevice.cultCenterId = sessionData.cultCenterId
                device.activeDevice.mindCenterId = sessionData.mindCenterId
                device.activeDevice.cityId = sessionData.cityId
                if (appVersion)
                    device.activeDevice.appVersion = appVersion
                if (codePushVersion && codePushVersion !== "undefined") {
                    device.activeDevice.codePushVersion = codePushVersion
                }
                if (sessionData.locationPreferenceData && sessionData.locationPreferenceData.latLong) {
                    device.activeDevice.preferredLatLong = [sessionData.locationPreferenceData.latLong.long, sessionData.locationPreferenceData.latLong.lat]
                } else {
                    device.activeDevice.preferredLatLong = undefined
                }
                if (lat && long)
                    device.activeDevice.currentLatLong = [long, lat]
                if (advertiserId) {
                    device.activeDevice.advertiserId = advertiserId
                }
                await this.updateDevice(device, tenant)
                return true
            }
            else {
                return Promise.resolve<boolean>(false)
            }
        } finally {
            this.lockAccess.unlockResource(lock)
        }
    }

    public async sendDeviceToRashi(deviceId: string, oldDeviceUserId: string, newDeviceData: DeviceInfo, isNotLoggedInFlow: boolean, tenant: Tenant): Promise<boolean> {
        if (!deviceId) return false
        if (oldDeviceUserId && newDeviceData.userId != oldDeviceUserId) {
            // perform delete
            await this.publishUserEventToRashi(oldDeviceUserId, {
                devices: [{
                    deviceId
                }]
            }, AttributeAction.DELETE, tenant)
        }
        // perform append
        return this.publishUserEventToRashi(newDeviceData.userId, {
            devices: [{
                deviceId,
                isLoggedIn: !isNotLoggedInFlow,
                appId: newDeviceData.appId,
                brand: newDeviceData.brand,
                deviceModel: newDeviceData.deviceModel,
                osName: newDeviceData.osName,
                osVersion: newDeviceData.osVersion,
                pushNotificationToken: newDeviceData.pushNotificationToken,
                registerDate: newDeviceData.registerDate,
                appVersion: newDeviceData.appVersion,
                tenant: newDeviceData.tenant,
                lastOpenDate: Date.now()
            }]
        }, AttributeAction.APPEND, tenant)
    }


    public async publishUserEventToRashi(userId: string | Number, body: any, action: AttributeAction, tenant: Tenant): Promise<boolean> {
        if (!Number(userId)) {
            this.logger.error(`Rashi user Event failed for userId : ${userId} and body ${JSON.stringify(body)} and action: ${action}`)
            return false
        }
        const rashiEvent: UserEvent = {
            userId: Number(userId),
            type: EventType.USER_PROFILE_EVENT,
            eventTime: new Date().getTime(),
            action,
            body
        }
        // TODO : Update the event with Tenant changes in Rashi
        const appTenant: AppTenant = AppUtil.getAppTenantFromString(tenant.toString())
        return this.rashiSnsClient.publishUserEvent(rashiEvent, appTenant)
    }

    async logoutDevice(deviceId: string, tenant: Tenant, state?: DeviceState): Promise<boolean> {
        const device: Device = await this.getDeviceByDeviceId(deviceId, tenant)
        if (device != null && device != undefined) {
            device.isLoggedIn = false
            device.at = null
            if (state) {
                device.state = state
            }
            await this.updateDevice(device, tenant)
            return true
        }
        else
            return false

    }

    public async updateDeviceAndDeviceInfo(device: Device, deviceInfo: DeviceInfo): Promise<Device> {
        if (deviceInfo.userId === device.activeDevice.userId) {
            deviceInfo.gateId = device.activeDevice.gateId
            deviceInfo.locationName = device.activeDevice.locationName
            deviceInfo.locationAddress = device.activeDevice.locationAddress
            deviceInfo.cultCenterId = device.activeDevice.cultCenterId
            deviceInfo.mindCenterId = device.activeDevice.mindCenterId
        }
        device.activeDevice = deviceInfo
        device.userId = deviceInfo.userId
        device.isLoggedIn = true
        return device
    }

    async updatePushNotificationToken(session: Session, deviceId: string, pushNotificationToken: string, tenant: Tenant, isInternalUser: boolean): Promise<{ isSessionUpdateNeeded: boolean }> {
        const lock = await this.lockAccess.lockResource(deviceId, 5000)
        try {
            let numRetries = 0
            do {
                try {
                    const userId = session.userId
                    let updatedDevice
                    let isSessionUpdateNeeded = false
                    if (session.deviceId != deviceId) {
                        // Due to a login bug,  server generared uuid got stored at time of login as device in session
                        // where as actual device id sent by client is different. Hence app launch call ended up updating data against uuid device id,
                        // where as pn token got stored against the actual device id. Following change handles merging the data properly
                        isSessionUpdateNeeded = true
                        let oldDeviceUserId
                        const actualDevice = await this.getDeviceByDeviceId(deviceId, tenant)
                        const sessionBasedDevice = await this.getDeviceByDeviceId(session.deviceId, tenant)
                        const sessionBasedDeviceId = sessionBasedDevice.deviceId
                        this.logger.info(`Copying over ${sessionBasedDeviceId} info to ${deviceId}`)
                        const deviceInfo = sessionBasedDevice.activeDevice
                        deviceInfo.pushNotificationToken = pushNotificationToken
                        deviceInfo.pushNotificationTokenState = "Registered"
                        if (actualDevice) {
                            this.logger.info("updating device Id " + deviceId)
                            oldDeviceUserId = actualDevice.userId
                            actualDevice.at = session.at
                            this.updateDeviceAndDeviceInfo(actualDevice, deviceInfo)
                            updatedDevice = await this.updateDevice(actualDevice, tenant)
                        } else {
                            this.logger.info("creating device Id " + deviceId)
                            await this.createNewDevice(deviceId, deviceInfo, !session.isNotLoggedIn, session.at)
                        }
                        this.sendDeviceToRashi(deviceId, oldDeviceUserId, deviceInfo, session.isNotLoggedIn, tenant)

                        // Clean up the device create by error during login
                        this.logger.info(`Marking ${sessionBasedDeviceId} as deleted`)
                        await this.logoutDevice(sessionBasedDevice.deviceId, tenant, "DeletedDueToLoginIssue")
                        this.publishUserEventToRashi(sessionBasedDevice.userId, {
                            devices: [{
                                deviceId: sessionBasedDeviceId
                            }]
                        }, AttributeAction.DELETE, tenant)

                    } else if (!isInternalUser) {
                        const device = await this.getDeviceByDeviceId(deviceId, tenant)
                        if (device.userId != session.userId) {
                            this.logger.error(`Logging out ${device.deviceId} as session user id ${session.userId} does not match with device user id ${device.userId}`)
                            await this.logoutDevice(deviceId, tenant)
                            this.publishUserEventToRashi(device.userId, {
                                devices: [{
                                    deviceId
                                }]
                            }, AttributeAction.DELETE, tenant)
                            throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_ERR, 401).withDebugMessage("Not authorized").build()
                        }
                        if (pushNotificationToken != device.activeDevice.pushNotificationToken) {
                            device.activeDevice.pushNotificationToken = pushNotificationToken
                            device.activeDevice.pushNotificationTokenState = "Registered"
                            await this.updateDevice(device, tenant)
                            this.sendDeviceToRashi(deviceId, undefined, updatedDevice.activeDevice, session.isNotLoggedIn, tenant)
                        }
                    }
                    return { isSessionUpdateNeeded: isSessionUpdateNeeded }
                } catch (err) {
                    if (err.statusCode == 401 || numRetries == 3) {
                        throw err
                    }
                    numRetries++
                }
            } while (numRetries < 3)
        } finally {
            this.lockAccess.unlockResource(lock)
        }
    }

    async deRegisterPushNotificationToken(userId: string, deviceId: string, pushNotificationToken: string): Promise<Device> {
        const deviceDetail: DeviceDetail = await this.userService.getDeviceByIdUserAndPNToken(deviceId, userId, pushNotificationToken)
        const device: Device = this.createDevicefromDeviceDetail(deviceDetail)
        device.activeDevice.pushNotificationTokenState = "NotRegistered"
        return this.updateDevice(device, device.activeDevice.tenant)
    }

    sendNotification(userId: string, payload: NotificationPayload, tenant: Tenant): Promise<boolean> {
        let success: boolean = true
        return this.getActiveDevice(userId, tenant).then(activeDevices => {
            if (activeDevices) {
                const minAppVersion = payload && payload.data && payload.data.analyticData
                    && payload.data.analyticData.minAppVersion ? payload.data.analyticData.minAppVersion : undefined
                const eligibleDevices = minAppVersion ? _.filter(activeDevices, device => { return device.appVersion >= minAppVersion }) : activeDevices
                const promises: Promise<{ success: boolean }>[] = []
                eligibleDevices.forEach(activeDevice => {
                    this.logger.info(`Sending the nudge to ${activeDevice.userId} and token ${activeDevice.pushNotificationToken} and appId ${activeDevice.appId}`)
                    let sendPayloadInsideData: boolean = false
                    if (activeDevice.osName.toLowerCase() === "android" && Number(activeDevice.appVersion) >= Number(ANDROID_APP_VERSION_FOR_NOTIFICATION)) {
                        sendPayloadInsideData = true
                    }
                    promises.push(this.firebaseService.sendNudge(activeDevice.pushNotificationToken, payload, sendPayloadInsideData))
                })
                Promise.all(promises).then(result => {
                    for (let i = 0; i < result.length; i++) {
                        success = success && result[i].success
                    }
                })
            }
            return success
        })
    }

    getActiveDevice(userId: string, tenant: Tenant): Promise<DeviceInfo[]> {
        const activeDevices: DeviceInfo[] = []
        return this.userService.getDevicesForUser(userId, tenant.toString()).then(devicesDetail => {
            devicesDetail.forEach(deviceDetail => {
                const device: Device = this.createDevicefromDeviceDetail(deviceDetail)
                if (device.activeDevice && device.activeDevice.pushNotificationToken && device.isLoggedIn) {
                    activeDevices.push(device.activeDevice)
                }
            })
            return activeDevices
        })
    }

    async persistDeviceInUserService(device: Device): Promise<DeviceDetail> {
        const deviceDetail = this.createDeviceDetailfromDevice(device)
        return this.userService.upsertDevice(deviceDetail)
    }

    createDeviceDetailfromDevice(device: Device): DeviceDetail {
        return  {
            userId: device.userId,
            at: device.at,
            deviceId: device.deviceId,
            tenant: device.activeDevice?.tenant,
            appId: device.activeDevice?.appId,
            brand: device.activeDevice?.brand,
            deviceModel: device.activeDevice?.deviceModel,
            osName: device.activeDevice?.osName,
            osVersion: device.activeDevice?.osVersion,
            pushNotificationToken: device.activeDevice?.pushNotificationToken,
            pushNotificationTokenState: device.activeDevice?.pushNotificationTokenState,
            appVersion: device.activeDevice?.appVersion?.toFixed(2),
            advertiserId: device.activeDevice?.advertiserId,
            locationAddress: device.activeDevice?.locationAddress,
            codePushVersion: device.activeDevice?.codePushVersion,
            isLoggedIn: device.isLoggedIn,
            cityId: device.activeDevice?.cityId,
            cultCenterId: device.activeDevice?.cultCenterId,
            mindCenterId: device.activeDevice?.mindCenterId,
            currentLatLong: {
                latitude: device.activeDevice?.currentLatLong?.[1],
                longitude: device.activeDevice?.currentLatLong?.[0]
            },
            gateId: device.activeDevice?.gateId,
            detectedCity:  {
                city: device.activeDevice?.detectedCity?.city,
                country: device.activeDevice?.detectedCity?.country,
                location: {
                    longitude: device.activeDevice?.detectedCity?.location?.long,
                    latitude: device.activeDevice?.detectedCity?.location?.lat
                }
            },
            ip: device.activeDevice?.ip,
            registerDate: device.activeDevice?.registerDate ? new Date(device.activeDevice?.registerDate).getTime() : undefined,
            locationName: device.activeDevice?.locationName,
            preferredLatLong: {
                latitude: device.activeDevice?.preferredLatLong?.[1],
                longitude: device.activeDevice?.preferredLatLong?.[0]
            },
            uninstalled: device.activeDevice?.uninstalled
        } as DeviceDetail
    }

    createDevicefromDeviceDetail(deviceDetail: DeviceDetail): Device {
        const device = {} as Device
        if (deviceDetail.deviceId) { device.deviceId = deviceDetail.deviceId }
        if (deviceDetail.userId) { device.userId = deviceDetail.userId }
        if (deviceDetail.at) { device.at = deviceDetail.at }
        if (deviceDetail.isLoggedIn != undefined && deviceDetail.isLoggedIn != null) { device.isLoggedIn = deviceDetail.isLoggedIn }
        device.activeDevice = {tenant: deviceDetail.tenant} as DeviceInfo
        if (deviceDetail.userId) { device.activeDevice.userId = deviceDetail.userId }
        if (deviceDetail.gateId) { device.activeDevice.gateId = deviceDetail.gateId }
        if (deviceDetail.locationName) { device.activeDevice.locationName = deviceDetail.locationName }
        if (deviceDetail.locationAddress) { device.activeDevice.locationAddress = deviceDetail.locationAddress }
        if (deviceDetail.cultCenterId) { device.activeDevice.cultCenterId = deviceDetail.cultCenterId }
        if (deviceDetail.mindCenterId) { device.activeDevice.mindCenterId = deviceDetail.mindCenterId }
        if (deviceDetail.appId) { device.activeDevice.appId = deviceDetail.appId }
        if (deviceDetail.codePushVersion) { device.activeDevice.codePushVersion = deviceDetail.codePushVersion }
        if (deviceDetail.brand) { device.activeDevice.brand = deviceDetail.brand }
        if (deviceDetail.deviceModel) { device.activeDevice.deviceModel = deviceDetail.deviceModel }
        if (deviceDetail.osName) { device.activeDevice.osName = deviceDetail.osName }
        if (deviceDetail.osVersion) { device.activeDevice.osVersion = deviceDetail.osVersion }
        if (deviceDetail.pushNotificationToken) { device.activeDevice.pushNotificationToken = deviceDetail.pushNotificationToken }
        if (deviceDetail.cityId) { device.activeDevice.cityId = deviceDetail.cityId }
        if (deviceDetail.pushNotificationTokenState) { device.activeDevice.pushNotificationTokenState = deviceDetail.pushNotificationTokenState }
        if (deviceDetail.advertiserId) { device.activeDevice.advertiserId = deviceDetail.advertiserId }
        if (deviceDetail.uninstalled != undefined && deviceDetail.uninstalled != null) { device.activeDevice.uninstalled = deviceDetail.uninstalled }
        if (deviceDetail.ip) { device.activeDevice.ip = deviceDetail.ip }
        if (deviceDetail.appVersion) {
            device.activeDevice.appVersion = Number(deviceDetail.appVersion)
        }
        if (deviceDetail.registerDate) {
            device.activeDevice.registerDate = new Date(deviceDetail.registerDate)
        }
        if (deviceDetail.currentLatLong?.latitude && deviceDetail.currentLatLong?.longitude) {
            device.activeDevice.currentLatLong = new Array(deviceDetail.currentLatLong.longitude, deviceDetail.currentLatLong.latitude)
        }
        if (deviceDetail.preferredLatLong?.latitude && deviceDetail.preferredLatLong?.longitude) {
            device.activeDevice.preferredLatLong = new Array(deviceDetail.preferredLatLong.longitude, deviceDetail.preferredLatLong.latitude)
        }
        if (deviceDetail.detectedCity?.city || deviceDetail.detectedCity?.country || (deviceDetail.detectedCity?.location?.latitude && deviceDetail.detectedCity?.location?.longitude)) {
            device.activeDevice.detectedCity = {} as DetectedCity
            if (deviceDetail.detectedCity.city) { device.activeDevice.detectedCity.city = deviceDetail.detectedCity.city }
            if (deviceDetail.detectedCity.country) { device.activeDevice.detectedCity.country = deviceDetail.detectedCity.country }
            if (deviceDetail.detectedCity?.location?.latitude && deviceDetail.detectedCity?.location?.longitude) {
                device.activeDevice.detectedCity.location = {
                    lat: deviceDetail.detectedCity.location.latitude,
                    long: deviceDetail.detectedCity.location.longitude
                }
            }
        }
        return device
    }

    copyValuesFrom(src: Device, target: Device): Device {
        target.deviceId = src.deviceId
        target.userId = src.userId
        target.at = src.at
        target.activeDevice = src.activeDevice
        target.isLoggedIn = src.isLoggedIn
        if (src.state != null && src.state != undefined) {
            target.state = src.state
        }
        if (_.isEmpty(target.oldDevices)) {
            target.oldDevices = src.oldDevices
        }
        return target
    }
}

export default DeviceBusiness
