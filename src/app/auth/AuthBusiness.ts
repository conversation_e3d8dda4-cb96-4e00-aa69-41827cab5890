import IAuthBusiness, { AppsFlyerAnalyticsData, BranchAnalyticsData, IRegisterBody, LoginResponse, UserSessionDetail } from "./IAuthBusiness"
import { VerticalType } from "@curefit/location-common"
import { Device, DeviceInfo } from "@curefit/device-common"
import { Session, SessionData, UserContext } from "@curefit/userinfo-common"
import { Tenant, User } from "@curefit/user-common"
import { AppTenant, UserAgentType as UserAgent, UserAgentType } from "@curefit/base-common"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { inject, injectable } from "inversify"
import { ILockAccess, LOCK_TYPES } from "@curefit/lock-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import * as _ from "lodash"
import * as express from "express"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { Constants, ISessionBusiness } from "@curefit/base-utils"
import SessionView from "./SessionView"
import UserView from "../user/UserView"
import { AuthUtil } from "../util/AuthUtil"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { DetectedCityResponseByIp, ICFAPICityService } from "../city/ICFAPICityService"
import { HAMLET_TYPES, HamletBusiness } from "@curefit/hamlet-node-sdk"
import { SessionExpiryPayload } from "./SessionQueueListener"
import { IQueueService, SQS_CLIENT_TYPES } from "@curefit/sqs-client"
import IUserBusiness, { UserCity } from "../user/IUserBusiness"
import AppUtil from "../util/AppUtil"
import INavBarBusiness from "../user/INavBarBusiness"
import IAuthMiddleware from "./IAuthMiddleware"
import { CULT_CLIENT_TYPES, ICultServiceOld as ICultService } from "@curefit/cult-client"
import { ICultBusiness } from "../cult/CultBusiness"
import IUserPreferenceBussiness from "../user/IUserPreferenceBussiness"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { CustomEventEmitter, eventName } from "../externalSource/CustomEventEmitter"
import { ExternalSourceUtil } from "../externalSource/ExternalSourceUtil"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import { AttributeAction, IUserAttributeClient, RASHI_CLIENT_TYPES } from "@curefit/rashi-client"
import { AppLayout } from "@curefit/apps-common"
import AppLayoutBuilder from "../user/AppLayoutBuilder"
import { ISegmentationCacheClient, IUserSegmentClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import CFAPIJavaService from "../CFAPIJavaService"
import { ISegmentService } from "@curefit/vm-models"
import LocationUtil from "../util/LocationUtil"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { UserCitySetReasonAnalyticsEvent } from "../cfAnalytics/UserCitySetReasonAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import SugarfitUtil from "../util/SugarfitUtil"
import { RuntimeError } from "@curefit/base/dist/src/Errors"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import EmailValidator from "../common/validator/EmailValidator"
import { OTP_MEDIUM } from "../user/UserController"
import { ICaptchaBusiness } from "../referral/CaptchaBusiness"
import MetricsUtil from "../metrics/MetricsUtil"
import { BlockingType } from "../metrics/models/BlockingType"

const crypto = require("crypto")

@injectable()
class AuthBusiness implements IAuthBusiness {

    constructor(
        @inject(CUREFIT_API_TYPES.NavBarBusiness) private navBarBusiness: INavBarBusiness,
        @inject(SQS_CLIENT_TYPES.QueueService) private queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionBusiness,
        @inject(CUREFIT_API_TYPES.AuthMiddleware) private authMiddleWare: IAuthMiddleware,
        @inject(LOCK_TYPES.LockAccess) private lockAccess: ILockAccess,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(HAMLET_TYPES.HamletBusiness) private hamletBusiness: HamletBusiness,
        @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(CULT_CLIENT_TYPES.CultFitService) private cultFitService: ICultService,
        @inject(CULT_CLIENT_TYPES.MindFitService) private mindFitService: ICultService,
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(CUREFIT_API_TYPES.CultBusiness) private cultBusiness: ICultBusiness,
        @inject(CUREFIT_API_TYPES.UserPreferenceBusiness) private userPreferenceBussiness: IUserPreferenceBussiness,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.CustomEventEmitter) private customEventEmitter: CustomEventEmitter,
        @inject(CUREFIT_API_TYPES.ExternalSourceUtil) private externalSourceUtil: ExternalSourceUtil,
        @inject(CUREFIT_API_TYPES.AppLayoutBuilder) private appLayoutBuilder: AppLayoutBuilder,
        @inject(SEGMENTATION_CLIENT_TYPES.UserSegmentClient) public segmentationClient: IUserSegmentClient,
        @inject(CUREFIT_API_TYPES.CFAPIJavaService) private cFAPIJavaService: CFAPIJavaService,
        @inject(CUREFIT_API_TYPES.SegmentService) private segmentService: ISegmentService,
        @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics,
        @inject(RASHI_CLIENT_TYPES.UserAttributeClient) public userAttributeClient: IUserAttributeClient,
        @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
        @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
        @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore: IAppConfigStoreService,
        @inject(REDIS_TYPES.RedisDao) private crudDao: ICrudKeyValue,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) private segmentationCacheClient: ISegmentationCacheClient,
    ) {
    }

    async publishSessionExpiryMsgToQueue(userId: string, expireRegistrationDoneBefore: number, tenant: Tenant, deviceId?: string): Promise<boolean> {
        const queueName = Constants.getSQSQueue("SESSION_EXPIRY")
        const sessionExpiryPayload: SessionExpiryPayload = {
            userId: userId,
            expireRegistrationDoneBefore: expireRegistrationDoneBefore,
            deviceId: deviceId,
            tenant: tenant
        }
        return this.queueService.sendMessage(queueName, sessionExpiryPayload).then(result => {
            if (result)
                return result
            else
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Error Publishing session expiration message for userId: " + userId).build()
        })
    }

    async getAllActiveUserSessionDetails(userId: string, tenant: Tenant): Promise<UserSessionDetail[]> {
        const userDevices = await this.deviceBusiness.getAllUserDevices(userId, new Date(), tenant)
        const userSessionDetails: UserSessionDetail[] = []
        for (const userDevice of userDevices) {
            const userSessionDetail: UserSessionDetail = {
                deviceId: userDevice.deviceId,
                osName: userDevice.activeDevice.osName,
                registerDate: new Date(userDevice.activeDevice.registerDate)
            }
            userSessionDetails.push(userSessionDetail)
        }
        return userSessionDetails
    }

    async deleteActiveUserSessionsBasedOnTimestamp(userId: string, expireRegistrationDoneBefore: number, tenant: Tenant, deviceId?: string): Promise<boolean> {
        try {
            let userDevices: Device[] = []
            if (_.isNil(deviceId))
                userDevices = await this.deviceBusiness.getAllUserDevices(userId, new Date(Number(expireRegistrationDoneBefore)), tenant)
            else {
                const userDevice = await this.deviceBusiness.getDeviceByDeviceId(deviceId, tenant)
                userDevices.push(userDevice)
            }
            // Clearing redis cache
            if (_.isEmpty(userDevices))
                return true
            await Promise.all(userDevices.map(device => this.sessionBusiness.expireSessionBasedOnId(device.at)))
            this.logger.info("Cached Sessions cleared")

            // Updating at and marking isLoggedIn as false in device collection
            if (_.isNil(deviceId)) {
                await this.deviceBusiness.bulkLogoutDevice(userId, new Date(Number(expireRegistrationDoneBefore)), tenant)
                await this.userService.logoutAllDevicesForUser(userId, tenant, expireRegistrationDoneBefore)
            } else {
                await this.deviceBusiness.logoutDevice(deviceId, tenant)
                await this.userService.logoutDevice(deviceId, tenant)
            }
            this.logger.info("Device authTokens updated")
            return true
        } catch (err) {
            this.logger.info("Error while updation sessions:" + err)
        }
    }

    async deleteActiveUserSession(session: Session, res: express.Response, tenant: Tenant, req: express.Request): Promise<boolean> {
        await this.sessionBusiness.expireSession(session)
        const deviceId = session.deviceId
        AuthUtil.clearCookies(res, req)
        await this.userService.logoutDevice(deviceId, tenant)
        return this.deviceBusiness.logoutDevice(deviceId, tenant)
    }

    async savePreferenceForRecentLoggedIn(req: express.Request, oldAuthToken: string, session: Session) {
        const oldSession = await this.sessionBusiness.verifyAuthToken(oldAuthToken)
        if (oldSession && oldSession.sessionData) {
            const oldSessionData = oldSession.sessionData
            const { cultPreference, mindPreference, userPreference } = oldSessionData
            const userContext = req.userContext as UserContext
            const userId: string = session.userId
            const cityId: string = session.sessionData.cityId
            const cultCityId = await this.cultBusiness.getCultCityId(userContext, cityId, userId)
            const mindCityId = await this.cultBusiness.getMindCityId(userContext, cityId, userId)
            if (!_.isEmpty(cultPreference)) {
                this.cultFitService.savePreference(cultCityId, userId, cultPreference, "CUREFIT_API", userContext.userProfile.subUserId)
            }
            if (!_.isEmpty(mindPreference)) {
                this.mindFitService.savePreference(mindCityId, userId, mindPreference, "CUREFIT_API", userContext.userProfile.subUserId)
            }
            if (!_.isEmpty(userPreference)) {
                userPreference.userId = userId
                await this.userPreferenceBussiness.createOrUpdateUserPreference(userContext, userPreference)
            }
        }
    }

    async registerDeviceAndCreateSession(
        user: User,
        deviceId: string,
        appVersion: string,
        req: express.Request,
        res: express.Response,
        isNotLoggedInFlow: boolean
    ): Promise<{ loginResponse: LoginResponse, session: Session }> {
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        let requestDeviceInfo = req.body.deviceInfo
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        const cityId: string = req.headers["cityid"] as string
        const advertiserId: string = req.header("advertiserId") as string
        const ip: string = req.ip ?? req.connection.remoteAddress
        let countryCode: string | undefined = undefined
        let appLayout: AppLayout
        let detectedCityResponseByIp: DetectedCityResponseByIp
        this.logger.info(`advertiserId in registerDeviceAndCreateSession, userId: ${user?.id}, advertiserId: ${advertiserId}, appVersion: ${appVersion}, reqUrl: ${req.url}`, {reqHeaders: req.headers})
        if (ip) {
            detectedCityResponseByIp = await this.CFAPICityService.getCityAndCountryByIp(tenant, ip)
            if (detectedCityResponseByIp?.detectedCountryCode)
                countryCode = detectedCityResponseByIp.detectedCountryCode
        }
        if (_.isNil(requestDeviceInfo)) {
            if ((userAgent === "DESKTOP" || userAgent === "MBROWSER")) {
                requestDeviceInfo = {
                    appId: "web",
                    brand: "browser",
                    model: "browser",
                    osName: "browser",
                    osVersion: 5.1,
                    appVersion: 8.0
                }
            } else {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid request params in registerDeviceAndCreateSession").build()
            }
        }
        const userCity: UserCity = await LocationUtil.getUserCityFromReq(req, this.cityService, this.logger, this.CFAPICityService, this.deviceBusiness)
        requestDeviceInfo.cityId = userCity?.city?.cityId
        const isCityManuallySelected = userCity?.isCityManuallySelected
        const deviceInfo = {
            userId: user.id.toString(),
            appId: requestDeviceInfo.appId,
            brand: requestDeviceInfo.brand,
            deviceModel: requestDeviceInfo.model,
            osName: requestDeviceInfo.osName,
            osVersion: requestDeviceInfo.osVersion,
            pushNotificationToken: requestDeviceInfo.pushNotificationToken,
            registerDate: new Date(),
            appVersion: Number(appVersion),
            tenant: tenant,
            cityId: requestDeviceInfo?.cityId,
            advertiserId: _.isEmpty(advertiserId) ? undefined : advertiserId,
        } as DeviceInfo

        let device = await this.deviceBusiness.getDeviceByDeviceId(deviceId, tenant)
        const oldDeviceUserId = device?.activeDevice?.userId
        if (device) {
            deviceInfo.pushNotificationToken = device?.activeDevice?.pushNotificationToken
            deviceInfo.pushNotificationTokenState = device?.activeDevice?.pushNotificationTokenState
            device = await this.deviceBusiness.updateDeviceAndDeviceInfo(device, deviceInfo)
        }
        const oldAuthToken = AuthUtil.getAuthTokenFromRequest(req)
        const sessionData: SessionData = this.getSessionDataFromDevice(deviceInfo, userCity?.city?.cityId)
        this.logger.info("cityId " + requestDeviceInfo.cityId)
        sessionData.cityId = userCity?.city?.cityId
        sessionData.isCityManuallySelected = isCityManuallySelected
        sessionData.citySelectedTimeInMillis = new Date().getTime()
        sessionData.tlaSemusnoc = crypto.randomUUID()

        const session = await this.sessionBusiness.createSession(deviceId, deviceInfo.userId, isNotLoggedInFlow, sessionData)
        session.userAgent = userAgent

        req.session = session
        const attributionSource = session.sessionData?.attributionSource
        // send login/sign_up event to attributed external source
        if (user.isSignUpEvent) {
            this.customEventEmitter.emitEvent(eventName.SIGNUP, user.id, attributionSource)
        }
        this.customEventEmitter.emitEvent(eventName.LOGIN, user.id, attributionSource)
        AuthUtil.setCookies(session.st, session.at, deviceId, req, res)
        const userContext = await this.authMiddleWare.getUserContextFromReq(req)
        req.userContext = userContext
        this.cfAnalytics.sendEventFromUserContext(<UserCitySetReasonAnalyticsEvent>{
            reason: userCity?.reason,
            cityId: userCity?.city?.cityId,
            cityName: userCity?.city?.name,
            isCitySelectedManually: userCity?.isCityManuallySelected,
            latitude: Number(req.headers["lat"]),
            longitude: Number(req.headers["lon"]),
            userIp: req.ip ?? req.connection.remoteAddress,
            analyticsEventName: AnalyticsEventName.USER_CITY_SET_REASON,
            from: "AuthBusiness.registerDeviceAndCreateSession",
            isReasonSession: userCity?.reason === "SESSION"
        }, userContext, false, true, false, false)
        if (!AppUtil.isWeb(req.userContext)) {
            this.deviceBusiness.sendDeviceToRashi(deviceId, oldDeviceUserId, deviceInfo, isNotLoggedInFlow, tenant)
        }
        if (!AuthUtil.isFixedGuestUser(userContext)) {  // For guest users, skip adding device entries
            if (device) {
                device.at = session.at
                await this.deviceBusiness.updateDevice(device, tenant)
            } else {
                await this.deviceBusiness.createNewDevice(deviceId, deviceInfo, true, session.at)
            }
        }
        if (!isNotLoggedInFlow && oldAuthToken) { // for recent loggedin user , check for preference in the redis
            try {
                await this.savePreferenceForRecentLoggedIn(req, oldAuthToken, session)
            } catch (err) {
                this.logger.error("save preference failed", err)
            }
        }

        let supportedVerticals: VerticalType[] = await this.navBarBusiness.getSupportedVerticalsForNavBar(req.userContext, userCity?.city)

        if (AppUtil.isWeb(userContext)) {
            try {
                appLayout = await this.appLayoutBuilder.fetchPageLayout(userContext, supportedVerticals)
            } catch (err) {
                this.logger.error("Failed to fetch app layout " + err)
            }
        }

        if (!(userAgent === "DESKTOP" || userAgent === "MBROWSER")) {
            supportedVerticals = supportedVerticals.slice(0, 4) // appTabs can only handle 5 tabs for mobile
        }
        const isCultWatchTenant = tenant === Tenant.CULTWATCH
        let cultWatchAppTabs = ["WATCH_HOME", "WATCH_FITNESS", "WATCH_SETTINGS"]
        if (isCultWatchTenant && await AppUtil.doesUserPartOfChallengeTab(this.segmentService, userContext)) {
            cultWatchAppTabs = ["WATCH_HOME", "WATCH_FITNESS", "WATCH_CHALLENGE", "WATCH_SETTINGS"]
        }
        const appTabs = isCultWatchTenant ? cultWatchAppTabs : ["PLAN", ...supportedVerticals]
        const verticalInfo = await this.navBarBusiness.getUserAndSupportedVerticalsInfo(req.userContext as UserContext, userCity?.city, user.id.toString())
        const loginResponse = {
            user: new UserView(user, session.isNotLoggedIn),
            session: new SessionView(session),
            cityName: userCity?.city?.name,
            cityId: userCity?.city?.cityId,
            redirectUri: await AuthUtil.getRedirectUrlIfNeeded(this.cFAPIJavaService, req, session.at, session.isNotLoggedIn),
            appTabs: appTabs,
            appLayout,
            verticals: verticalInfo.verticals,
            isMoreToBeHighlighted: verticalInfo.isMoreToBeHighlighted,
            isPhoneNumberUpdateRequired: AppUtil.isPhoneNumberUpdateRequired(countryCode),
            showFlutterHomeCLP: await AppUtil.doesUserBelongToNewHomeAuroraCLP(this.segmentService, userContext),
            isSfAppLocalisationSupportedUser: await AppUtil.shouldEnableAppLanguageForSugarfit(this.segmentationCacheClient, userContext),
        } as LoginResponse
        return { loginResponse, session }
    }

    validateAndGetDeviceId(req: express.Request): string {
        let deviceId: string = req.headers["deviceid"] || req.headers["deviceId"] || req.signedCookies["deviceId"] || req.cookies["deviceId"]
        if (deviceId === "undefined") {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Invalid device id ").build()
        }
        if (!_.isEmpty(deviceId) && deviceId.includes(",")) {
            const values = deviceId.split(",")
            if (values.length > 0) {
                deviceId = values[0]
            }
        }
        return deviceId
    }
    setEmail(cfUserId: string, newEmail: string): Promise<User> {
        return new EmailValidator().validate(newEmail).then(done => {
            return this.userService.setEmail(cfUserId, newEmail)
        })
    }

    setWorkEmail(cfUserId: string, workEmail: string): Promise<User> {
        return new EmailValidator().validate(workEmail).then(done => {
            return this.userService.setWorkEmail(cfUserId, workEmail)
        })
    }
    async setPhoneNumber(res: express.Response, userId: string, newPhoneNumber: number, medium: OTP_MEDIUM, appVersion: number,
        osName: string, userAgent: UserAgentType, countryCallingCode?: string, session?: Session, captchaResponse?: string, userContext?: UserContext, apiKey?: string
    ): Promise<User> {
        const user = await this.userService.getUser(userId)
        if (!_.isEmpty(user.phone) && userContext && !AppUtil.isSugarFitOrUltraFitApp(userContext)) {
            throw this.errorFactory.withCode(ErrorCodes.PHONE_NUMBER_UPDATE_NOT_ALLOWED_ERR, 403).withDebugMessage("Phone update not allowed").build()
        }

        if (session && session.sessionData && session.sessionData.failedOtpTry) {
            if (session.sessionData.failedOtpTry > 3 &&
                (new Date().getTime() - session.sessionData.lastFailedOtpTime.getTime()) / (60 * 1000) < 5) {
                throw this.errorFactory.withCode(ErrorCodes.OTP_LIMIT_REACHED_ERR, 400).withDebugMessage("OTP limit reached").build()
            }
        }

        if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {

            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(apiKey))
            this.logger.info(` /setPhoneNumber  verifyCaptchaResp`, verifyCaptchaResp)
            if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                return this.userService.setPhone(userId, newPhoneNumber.toString(), medium, countryCallingCode,
                    appVersion, osName).then(user => {
                    return user
                })
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                res.status(400).send({ statusCode: 400, error: "Bad Request", message: "captcha not valid" })
            }
        }

        return this.userService.setPhone(userId, newPhoneNumber.toString(), medium, countryCallingCode,
            appVersion, osName).then(user => {
            return user
        })
    }
    async truecallerLoginMWebPolling(requestId: String, appTenant: AppTenant): Promise<User> {
        const loginModes = AppUtil.getLoginModesForAppTenant(appTenant, this.appConfigStore)
        if (loginModes.truecaller) {
            this.logger.error("Truecaller is disabled!!")
            throw new RuntimeError("Truecaller is disabled!!")
        }
        const redisKey = `truecallerLoginStatus_${requestId}`
        let redisRespJson
        const redisResponse = await this.crudDao.read(redisKey)
        redisRespJson = JSON.parse(redisResponse)
        if (!redisRespJson) {
            throw this.errorFactory.withCode(ErrorCodes.ERR_TRUECALLER_WAITING_CALLBACK, 202).withDebugMessage("Truecaller callback not yet received").build()
        }
        if (redisRespJson.status !== "verified") {
            this.logger.debug("Truecaller user verification failed!! " + JSON.stringify(redisRespJson))
            throw this.errorFactory.withCode(ErrorCodes.ERR_TRUECALLER_LOGIN_FAILED, 401).withDebugMessage("Truecaller login failed").build()
        }
        const userId = redisRespJson.userId
        return this.userService.getUser(userId)
    }

    private getSessionDataFromDevice(deviceInfo: DeviceInfo, cityId: string): SessionData {
        const sessionData: SessionData = {}
        if (deviceInfo.gateId) {
            sessionData.gateId = deviceInfo.gateId
            sessionData.locationName = deviceInfo.locationName
            sessionData.locationAddress = deviceInfo.locationAddress
        }
        if (deviceInfo.cultCenterId) {
            sessionData.cultCenterId = deviceInfo.cultCenterId
            sessionData.mindCenterId = deviceInfo.mindCenterId
        }
        // Temp hack to avoid crash after closing cult hsr 27th main
        if (sessionData.cultCenterId === "8") {
            sessionData.cultCenterId = "3"
        }

        if (!_.isNil(cityId)) {
            sessionData.cityId = cityId
        }
        return sessionData
    }

    async sendBranchAnalyticsDataToRashi(userId: string, req: express.Request): Promise<void> {
        const branchAnalyticsData: BranchAnalyticsData = req.body.branchAnalyticsData
        const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
        const appsFlyerAnalyticsData: AppsFlyerAnalyticsData = req.body.appsFlyerAnalyticsData
        if (appTenant == AppTenant.SUGARFIT || appTenant == AppTenant.ULTRAFIT) {
            if (!_.isEmpty(appsFlyerAnalyticsData) && (!_.isEmpty(appsFlyerAnalyticsData?.campaign) || !_.isEmpty(appsFlyerAnalyticsData?.utm_campaign))) {
                this.logger.info(`for userId: ${userId} Received Appsflyer Analytics Data for osname: ${req.headers["osname"]} and app version: ${AppUtil.getAppVersion(req)} is ${JSON.stringify(appsFlyerAnalyticsData)}`)
                if (userId) {
                    await this.userBusiness.publishUserEventToRashi(userId, {
                        install_campaign: appsFlyerAnalyticsData.utm_campaign || appsFlyerAnalyticsData?.campaign,
                        install_source: appsFlyerAnalyticsData.utm_source,
                        install_medium: appsFlyerAnalyticsData.utm_medium,
                        install_channel: appsFlyerAnalyticsData.media_source,
                        install_adSetName: appsFlyerAnalyticsData.af_adset,
                        install_originSource: appsFlyerAnalyticsData.af_status,
                        install_dataOriginSource: "appsflyer",
                    }, AttributeAction.REPLACE, appTenant)

                    try {
                        if (appsFlyerAnalyticsData?.media_source) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_channel",
                                attrValue: appsFlyerAnalyticsData.media_source,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install channel for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (appsFlyerAnalyticsData?.utm_source) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_source",
                                attrValue: appsFlyerAnalyticsData.utm_source,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_source for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (appsFlyerAnalyticsData?.utm_campaign || appsFlyerAnalyticsData?.campaign) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_campaign",
                                attrValue: appsFlyerAnalyticsData.utm_campaign || appsFlyerAnalyticsData?.campaign,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_campaign for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (appsFlyerAnalyticsData?.af_adset) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_adSetName",
                                attrValue: appsFlyerAnalyticsData.af_adset,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_adSetName for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                    } catch (e) {
                        this.logger.info(`Failed to send appsflyer events for userId: ${userId}`)
                    }
                }
            } else if (!_.isEmpty(branchAnalyticsData)) {
                this.logger.info(`for userId: ${userId} Received Branch Analytics Data for osname: ${req.headers["osname"]} and app version: ${AppUtil.getAppVersion(req)} is ${JSON.stringify(branchAnalyticsData)}`)
                if (userId) {
                    await this.userBusiness.publishUserEventToRashi(userId, {
                        install_campaign: branchAnalyticsData.utm_campaign,
                        install_source: branchAnalyticsData.utm_source,
                        install_vertical: branchAnalyticsData.campaign_vertical,
                        install_medium: branchAnalyticsData.utm_medium,
                        install_audienceGroup: branchAnalyticsData.campaign_audience,
                        install_channel: branchAnalyticsData.advertising_partner_name,
                        install_adSetName: branchAnalyticsData.ad_set_name
                    }, AttributeAction.REPLACE, appTenant)

                    try {
                        if (branchAnalyticsData?.advertising_partner_name) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_channel",
                                attrValue: branchAnalyticsData.advertising_partner_name,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install channel for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (branchAnalyticsData?.utm_source) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_source",
                                attrValue: branchAnalyticsData.utm_source,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_source for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (branchAnalyticsData?.utm_campaign) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_campaign",
                                attrValue: branchAnalyticsData.utm_campaign,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_campaign for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                        if (branchAnalyticsData?.ad_set_name) {
                            this.userAttributeClient.setUserAttributes({
                                userId: Number(userId),
                                attribute: "install_adSetName",
                                attrValue: branchAnalyticsData.ad_set_name,
                                namespace: SugarfitUtil.getRashiNamespace(appTenant),
                                description: `install_adSetName for LSQ`,
                                dataType: "STRING",
                                occuredAt: new Date().getTime()
                            }, undefined, appTenant)
                        }
                    } catch (e) {
                        this.logger.info(`Failed to send branch lead events for userId: ${userId}`)
                    }
                }
            } else {
                this.logger.info(`for userId: ${userId} Missing Branch Analytics Data for osname: ${req.headers["osname"]} and app version: ${AppUtil.getAppVersion(req)}`)
            }
        } else {
            if (!_.isEmpty(branchAnalyticsData)) {
                this.logger.info(`for userId: ${userId} Received Branch Analytics Data for osname: ${req.headers["osname"]} and app version: ${AppUtil.getAppVersion(req)} is ${JSON.stringify(branchAnalyticsData)}`)
                if (userId) {
                    await this.userBusiness.publishUserEventToRashi(userId, {
                        install_campaign: branchAnalyticsData.utm_campaign,
                        install_source: branchAnalyticsData.utm_source,
                        install_vertical: branchAnalyticsData.campaign_vertical,
                        install_medium: branchAnalyticsData.utm_medium,
                        install_audienceGroup: branchAnalyticsData.campaign_audience,
                        install_channel: branchAnalyticsData.advertising_partner_name,
                        install_adSetName: branchAnalyticsData.ad_set_name
                    }, AttributeAction.REPLACE, appTenant)
                }
            } else {
                this.logger.info(`for userId: ${userId} Missing Branch Analytics Data for osname: ${req.headers["osname"]} and app version: ${AppUtil.getAppVersion(req)}`)
            }
        }
    }

    async registerUserEmailSignUp(reqBody: IRegisterBody) {
        let response
        if (reqBody.token) {
            response = await this.userService.register(reqBody.firstName, reqBody.lastName, reqBody.email, reqBody.password, reqBody.token, null, reqBody.deviceId)
        } else {
            response = await this.userService.register(reqBody.firstName, reqBody.lastName, reqBody.email, reqBody.password, null, null, reqBody.deviceId)
        }
        return response
    }

    temporaryLogsForDetectingCaptchaError(req: express.Request, verifyCaptchaResp: any): void {
        const userAgent: UserAgent = AuthUtil.getUserAgent(req)
        const appVersion: string = req.headers["appversion"] as string
        this.logger.info(`Captcha Logs For Various Metrics ${req.originalUrl.split("?")[0]} userAgent ${userAgent} appVersion ${appVersion} captchaResponse ${req.body.captchaResponse} verifyCaptchaResp ${JSON.stringify(verifyCaptchaResp)}`)
    }

    public getInstallDeferDeeplinkUrl(branchAnalyticsData: BranchAnalyticsData): string {
        if (branchAnalyticsData?.utm_campaign) {
            const campaignName = branchAnalyticsData.utm_campaign
            if (campaignName.includes("cult_offline")) {
                return "curefit://cultfitclp?pageId=cult"
            } else if (campaignName.includes("live")) {
                return "curefit://livefitclp?pageId=live"
            }
        }
        return undefined
    }
}

export default AuthBusiness
