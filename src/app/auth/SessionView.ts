import { DeliveryGate } from "@curefit/eat-common"
import { Session } from "@curefit/userinfo-common"

export type GATE_STATUS = "open" | "closed" | "unknown" | "unselected"
class SessionView {
    public st: string
    public at: string
    public gate?: {
        gateId: string
        name: string
        address: string
        type?: GATE_STATUS
    }
    public deviceId?: string
    public tlaSemusnoc?: string // tlaSemusnoc = reverse(consumeSalt)
    public isLocationServiceable?: boolean

    constructor(model: Session, gate?: DeliveryGate) {
        this.at = model.at
        this.st = model.st
        this.gate = {
            gateId: model.sessionData.gateId,
            name: model.sessionData.locationName,
            address: model.sessionData.locationAddress
        }
        if (gate) {
            this.gate.type = gate.status === "LIVE" ? "open" : "closed"
        } else {
            if (model.sessionData.gateId) {
                this.gate.type = "unknown"
            } else {
                this.gate.type = "unselected"
            }
        }
        this.deviceId = model.deviceId
        this.tlaSemusnoc = model.sessionData.tlaSemusnoc
        this.isLocationServiceable = model.sessionData.isLocationServiceable
    }
}

export default SessionView
