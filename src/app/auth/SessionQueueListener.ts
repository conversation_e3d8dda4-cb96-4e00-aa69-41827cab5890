import { inject, injectable } from "inversify"
import {
    BaseDelayedBatchedQ<PERSON><PERSON><PERSON><PERSON><PERSON>,
    BaseDelayedQueueHandler,
    IQueueService,
    Message,
    SQS_CLIENT_TYPES
} from "@curefit/sqs-client"
import { Constants } from "@curefit/base-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IAuthBusiness from "./IAuthBusiness"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Tenant } from "@curefit/base-common"

export interface SessionExpiryPayload {
    userId: string
    expireRegistrationDoneBefore: number            // number datatype will be parsed as string. So use Number()
    deviceId?: string
    tenant: Tenant
}

@injectable()
class SessionQueueListener extends BaseDelayedBatchedQueueHandler {
    constructor(
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
        super(Constants.getSQSQueue("SESSION_EXPIRY"), 10, queueService, 60 * 1000)
    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        this.logger.debug("Session message: " + message)
        const sessionExpiryPayload = <SessionExpiryPayload>JSON.parse(message)
        return this.expireSession(sessionExpiryPayload)
    }

    async expireSession(sessionExpiryPayload: SessionExpiryPayload): Promise<boolean> {
        try {
            return this.authBusiness.deleteActiveUserSessionsBasedOnTimestamp(sessionExpiryPayload.userId, sessionExpiryPayload.expireRegistrationDoneBefore, sessionExpiryPayload.tenant, sessionExpiryPayload.deviceId)
        }
        catch (err) {
            return false
        }
    }
}
export default SessionQueueListener
