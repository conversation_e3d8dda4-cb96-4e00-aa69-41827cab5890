import { inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ErrorFactory } from "@curefit/error-client"
import * as crypto from "crypto"

export interface IRedisAuthTokenService {
    generateToken(clientId: string, apiIdentifier: string, expiryInSeconds?: number): Promise<string>
    isTokenValid(token: string, apiIdentifier: string): Promise<boolean>
    getValue(key: string): Promise<string>
}

@injectable()
export class RedisAuthTokenService implements IRedisAuthTokenService {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.RedisDao) public redisDao: ICrudKeyValue,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory
    ) {
    }

    async generateToken(clientId: string, apiIdentifier: string, expiryInSeconds?: number): Promise<string> {
        const randomString = crypto.randomBytes(20).toString("hex")
        const token = `${clientId}.${randomString}`
        const encodedToken = Buffer.from(token).toString("base64")
        const signedToken = crypto.createHash("sha256").update(encodedToken).digest("hex")
        const authToken = `${encodedToken}.${signedToken}`
        const cachedToken = `${apiIdentifier}.${authToken}`
        if (expiryInSeconds) {
            await this.redisDao.createWithExpiry(cachedToken, clientId, expiryInSeconds)
        } else {
            await this.redisDao.create(cachedToken, clientId)
        }
        return authToken
    }

    async isTokenValid(token: string, apiIdentifier: string): Promise<boolean> {
        return await this.redisDao.exists(`${apiIdentifier}.${token}`)
    }

    async getValue(key: string): Promise<string> {
        return await this.redisDao.read(key)
    }
}