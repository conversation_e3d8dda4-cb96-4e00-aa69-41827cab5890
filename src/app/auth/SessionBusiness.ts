import { ISessionBusiness, Constants } from "@curefit/base-utils"
import { injectable, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { SessionData, Session } from "@curefit/userinfo-common"
import { IEventsService, EVENTS_TYPES, EventData } from "@curefit/events-util"
import { ILogger, BASE_TYPES } from "@curefit/base"
import { ExternalSourceUtil } from "../externalSource/ExternalSourceUtil"

interface UserProfileData {
    cityId: string
    areaId: string
}

export interface ICFApiSessionBusiness extends ISessionBusiness {
    updateSessionData(at: string, sessionData: SessionData, isNotLoggedIn?: boolean, isDataChanged?: boolean): Promise<Session>

}
@injectable()
export class SessionBusiness implements ICFApiSessionBusiness {
    constructor(
        @inject(BASE_TYPES.ILogger) protected logger: ILogger,
        @inject(EVENTS_TYPES.EventsService) private eventsService: IEventsService,
        @inject(CUREFIT_API_TYPES.BaseSessionService) private baseSessionService: ISessionBusiness,
        @inject(CUREFIT_API_TYPES.ExternalSourceUtil) private externalSourceUtil: ExternalSourceUtil) {
    }

    async createSession(deviceId: string, userId: string, isNotLoggedIn: boolean, sessionData: SessionData): Promise<Session> {
        try {
            sessionData.attributionSource = await this.externalSourceUtil.getExternalSourceValue(userId)
        } catch (err) {
            this.logger.info("Error while attribution source: " + err)
        }
        return this.baseSessionService.createSession(deviceId, userId, isNotLoggedIn, sessionData)
    }

    async updateSessionData(at: string, sessionData: SessionData, isNotLoggedIn: boolean = false, isDataChanged: boolean = true): Promise<Session> {
        const session = await this.baseSessionService.updateSessionData(at, sessionData, isNotLoggedIn)
        // Fire the sns to update hansel asynchronously
        if (isDataChanged && !isNotLoggedIn)
            this.notifyAppEventSNS(session.userId, session.sessionData)
        return session
    }

    async updateSessionDataWithUserId(at: string, sessionData: SessionData, userId: string, isNotLoggedIn?: boolean): Promise<Session> {
        const session = await this.baseSessionService.updateSessionDataWithUserId(at, sessionData, userId, isNotLoggedIn)
        // Fire the sns to update hansel asynchronously
        if (!isNotLoggedIn)
            this.notifyAppEventSNS(session.userId, session.sessionData)
        return session
    }
    expireSession(session: Session): Promise<boolean> {
        return this.baseSessionService.expireSession(session)
    }

    expireSessionBasedOnId(at: string): Promise<boolean> {
        return this.baseSessionService.expireSessionBasedOnId(at)
    }

    verifySecureToken(at: string, st: string): Promise<Session> {
        return this.baseSessionService.verifySecureToken(at, st)

    }
    verifyAuthToken(at: string): Promise<Session> {
        return this.baseSessionService.verifyAuthToken(at)
    }

    updateDeviceIdInSession(at: string, deviceId: string, isNotLoggedIn: boolean): Promise<Session> {
        return this.baseSessionService.updateDeviceIdInSession(at, deviceId, isNotLoggedIn)
    }


    private notifyAppEventSNS(userId: string, sessionData: SessionData) {
        const attributes: Map<string, any> = new Map<string, any>()
        attributes.set("USER_ID", userId)
        attributes.set("EVENT_TYPE", "USER_PROFILE_UPDATE")
        const userProfileData: UserProfileData = {
            cityId: sessionData.cityId,
            areaId: sessionData.locationPreferenceData ? sessionData.locationPreferenceData.areaId : undefined
        }
        const eventData: EventData<UserProfileData> = new EventData("USER_PROFILE_UPDATE", [],
            new Date().getTime(), userProfileData, attributes)
        return this.eventsService.publishMessage(Constants.getSNSTopic("APP_EVENTS"), eventData)
    }

}