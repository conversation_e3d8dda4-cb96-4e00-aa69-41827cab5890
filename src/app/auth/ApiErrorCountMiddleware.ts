import { <PERSON><PERSON><PERSON><PERSON><PERSON>Value, IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { inject, injectable } from "inversify"
import { BASE_TYPES, ILogger } from "@curefit/base"
import { TimeUtil } from "@curefit/util-common"
import * as express from "express"
import MetricsUtil from "../metrics/MetricsUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { BlockingType } from "../metrics/models/BlockingType"
import { GenericError } from "@curefit/error-client"

interface ApiErrorConfig {
    statusCodes: number[]
    threshold: number
    message: string,
    keyGetter?: (req: express.Request, res: express.Response) => string,
    errorCountExpiryInSec?: number,
    httpMethods?: string[]
}
const ERROR_COUNT_EXPIRY_IN_SECONDS = 2 * TimeUtil.TIME_IN_SECONDS.HOUR

const URL_PATH_TO_ERROR_CONFIG: Map<string, ApiErrorConfig> = new Map<string, ApiErrorConfig>()
URL_PATH_TO_ERROR_CONFIG.set("/auth/login", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "User not found" //  TODO: Change to error code when the downstream service migrate to error code
})
URL_PATH_TO_ERROR_CONFIG.set("/auth/loginPhoneSendOtp", {
    statusCodes: [200, 400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "failed to send otp"
})
URL_PATH_TO_ERROR_CONFIG.set("/auth/loginPhoneVerifyOtp", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "otp - verification failed" //  TODO: Change to error code when the downstream service migrate to error code
})
URL_PATH_TO_ERROR_CONFIG.set("/user/verifyPhoneNumber", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "verification failed" //  TODO: Change to error code when the downstream service migrate to error code
})
URL_PATH_TO_ERROR_CONFIG.set("/auth/email/otprequest", {
    statusCodes: [200, 400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "failed to send otp"
})
URL_PATH_TO_ERROR_CONFIG.set("/auth/email/otplogin", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 100 : 20,
    message: "otp - verification failed"
})
URL_PATH_TO_ERROR_CONFIG.set("/fitnessPass/eligibility", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 20,
    message: "attempts exhausted for coupon code check",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    }
})
URL_PATH_TO_ERROR_CONFIG.set("/user/address", {
    statusCodes: [200, 201, 400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 10,
    message: "Too many attempts. Retry after some time.",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.HOUR,
    httpMethods: ["POST"]
})
URL_PATH_TO_ERROR_CONFIG.set("/user/address/:id", {
    statusCodes: [200, 201, 400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 10,
    message: "Too many attempts. Retry after some time.",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.HOUR
})
URL_PATH_TO_ERROR_CONFIG.set("/care/createPatient", {
    statusCodes: [200, 201, 400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 10,
    message: "Too many attempts. Retry after some time.",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.HOUR
})
URL_PATH_TO_ERROR_CONFIG.set("/fitnessPass/activate", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 5,
    message: "attempts exhausted for coupon code activation",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.HOUR
})
URL_PATH_TO_ERROR_CONFIG.set("/fitnessPass/v2/activate", {
    statusCodes: [400, 401],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 200 : 5,
    message: "attempts exhausted for coupon code activation",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.HOUR
})
URL_PATH_TO_ERROR_CONFIG.set("/cult/class/:classId/book", {
    statusCodes: [200, 400, 401, 500],
    threshold: process.env.ENVIRONMENT === "STAGE" ? 10 : 4,
    message: "Too many attempts. Retry after some time.",
    keyGetter: (req: express.Request, res: express.Response) => {
        return req?.session?.userId ?? res.locals.ip
    },
    errorCountExpiryInSec: TimeUtil.TIME_IN_SECONDS.MINUTE
})
export const TESSERACT_IP = "*************"
const whitelistedIPs = new Set<string>([
    "**************", "**************", "**************", // curefit office
    "**************", "**************", "*************", "**************", // sugarfit office
    "**************", "***********", "*************", "***********", "************", "*************",
    "*************", "************", "*************",
    TESSERACT_IP
])

@injectable()
export class ApiErrorCountMiddleware {
    private crudDao: ICrudKeyValue
    constructor(
        @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
        @inject(BASE_TYPES.ILogger) private logger: ILogger,
        @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil
    ) {
        this.crudDao = this.multiCrudKeyValueDao.getICrudKeyValue("CFAPI-CACHE")
        this.updateStatusCountMiddleware = this.updateStatusCountMiddleware.bind(this)
        this.checkErrorCountMiddleware = this.checkErrorCountMiddleware.bind(this)
    }

    async updateStatusCountMiddleware(statusCode: number, req: express.Request, res: express.Response) {
        if (whitelistedIPs.has(res.locals.ip)) return
        const path: string = this.getPath(req)
        const errorConfig = URL_PATH_TO_ERROR_CONFIG.get(path)
        if (errorConfig && errorConfig.statusCodes.includes(statusCode)
            && (!errorConfig.httpMethods || errorConfig.httpMethods.includes(req.method))) {
            const keyIdentifier: string = (errorConfig.keyGetter && errorConfig.keyGetter(req, res)) || res.locals.ip
            const key = this.errorCountKey(path, keyIdentifier)
            await this.crudDao.incrementBy(key, 1)
            await this.crudDao.setExpiry(key, errorConfig.errorCountExpiryInSec ?? ERROR_COUNT_EXPIRY_IN_SECONDS)
        }
    }

    getPath(req: express.Request): string {
        return req.route?.path || req.path
    }

    async checkErrorCountMiddleware(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
        if (whitelistedIPs.has(res.locals.ip)) return next()
        const path: string = this.getPath(req)
        const errorConfig = URL_PATH_TO_ERROR_CONFIG.get(path)
        if (errorConfig) {
            const keyIdentifier: string = (errorConfig.keyGetter && errorConfig.keyGetter(req, res)) || res.locals.ip
            const key = this.errorCountKey(path, keyIdentifier)
            const errorCount = await this.crudDao.read(key)
            if (errorCount && Number(errorCount) > errorConfig.threshold) {
                this.metricsUtil.reportBlockedRequest(BlockingType.API_ERROR_RATE_LIMITED)
                const err = new GenericError({message: "error rate limit"})
                err.statusCode = 400
                this.logger.error(`error rate limit`, {keyIdentifier, path, key, errorCount})
                if (!res.headersSent) {
                    res.status(400).send({ statusCode: 400, error: "Bad Request", message: errorConfig.message, title: "Something went wrong", subTitle: errorConfig.message, actions: [{actionType: "HIDE_ALERT_MODAL", title: "Ok"}] })
                }
                next(err)
            }
        }
        next()
    }

    private errorCountKey(path: string, ip: string): string {
        return "CFAPP:api_error_count:" + path + ":" + ip
    }

}
