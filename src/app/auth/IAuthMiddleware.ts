import * as express from "express"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import { UserContext } from "@curefit/vm-models"

interface IAuthMiddleware {
    validateSession: (sessionBusiness: ISessionService) => (req: express.Request, res: express.Response, next: express.NextFunction) => void
    getUserContextFromReq(req: express.Request): Promise<UserContext>
}

export default IAuthMiddleware