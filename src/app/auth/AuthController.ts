import * as express from "express"
import { Session, SessionData, UserContext } from "@curefit/userinfo-common"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import SessionView from "./SessionView"
import IDeviceBusiness from "../device/IDeviceBusiness"
import * as _ from "lodash"
import { BlockingType } from "../metrics/models/BlockingType"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import AuthMiddleware from "../auth/AuthMiddleware"
import AppUtil, { ENTERPRISE_SSO_API_KEYS, ONBOARDING_FORM_ID } from "../util/AppUtil"
import AuthUtil from "../util/AuthUtil"
import { BASE_TYPES, DataError, FetchUtilV2, ForbiddenError, Logger } from "@curefit/base"
import IAuthBusiness, { IRegisterBody, LoginResponse } from "./IAuthBusiness"
import { OrderSource } from "@curefit/order-common"
import { IUserService, PhoneOtpLoginResponse, USER_CLIENT_TYPES } from "@curefit/user-client"
import UserView from "../user/UserView"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { AtlasActivityService } from "../atlas/AtlasActivityService"
import ActionUtil from "../util/ActionUtil"
import MetricsUtil from "../metrics/MetricsUtil"
import { ICaptchaBusiness } from "../referral/CaptchaBusiness"
import { AppTenant, UserAgentType as UserAgent } from "@curefit/base-common"
import { OTP_MEDIUM } from "../user/UserController"
import { AttributionDetails, Tenant, User } from "@curefit/user-common"
import { ApiErrorCountMiddleware } from "./ApiErrorCountMiddleware"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"
import { CustomEventEmitter, eventName } from "../externalSource/CustomEventEmitter"
import { IThirdPartyService, THIRD_PARTY_CLIENT_TYPES } from "@curefit/third-party-integrations-client"
import { EventData, EVENTS_TYPES, IEventsService } from "@curefit/events-util"
import { ICrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import fetch from "node-fetch"
import { IAppConfigStoreService } from "../appConfig/AppConfigStoreService"
import { RuntimeError } from "@curefit/base/dist/src/Errors"
import { UserCity } from "../user/IUserBusiness"
import LocationUtil from "../util/LocationUtil"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { UserCitySetReasonAnalyticsEvent } from "../cfAnalytics/UserCitySetReasonAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { ISegmentService } from "@curefit/vm-models"
import { ENTERPISE_CLIENT_TYPES, IEnterpriseClientService } from "@curefit/enterprise-client"
import { EmployeeInfo } from "@curefit/enterprise-common"

const pemtools = require("pemtools")
const crypto = require("crypto")

export function controllerFactory(kernel: Container) {
    @controller("/auth", kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware).checkErrorCountMiddleware)
    class AuthController {
        TRUECALLER_REDIS_KEY_TTL_IN_SEC: number = 10 * 60
        TRUECALLER_PAYLOAD_TIMEOUT_IN_MILLIS: number = 10 * 60 * 1000
        B2B_REDIS_KEY_TTL_IN_SEC: number = 10 * 60
        ALGO_MAP: { [key: string]: string } = {
            "SHA512withRSA": "RSA-SHA512"
        }
        TRUECALLER_APP_KEY_API_ENDPOINT = "https://api4.truecaller.com/v1/key"

        constructor(
            @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
            @inject(CUREFIT_API_TYPES.CaptchaBusiness) private captchaBusiness: ICaptchaBusiness,
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
            @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
            @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
            @inject(CUREFIT_API_TYPES.AtlasActivityService) private atlasActivityService: AtlasActivityService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.CustomEventEmitter) private customEventEmitter: CustomEventEmitter,
            @inject(EVENTS_TYPES.EventsService) private eventsService: IEventsService,
            @inject(REDIS_TYPES.RedisDao) private crudDao: ICrudKeyValue,
            @inject(BASE_TYPES.FetchUtilV2) private fetchHelper: FetchUtilV2,
            @inject(CUREFIT_API_TYPES.AppConfigStoreService) private appConfigStore: IAppConfigStoreService,
            @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
            @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics,
            @inject(CUREFIT_API_TYPES.SegmentService) protected segmentService: ISegmentService,
            @inject(ENTERPISE_CLIENT_TYPES.IEnterpriseClientService) private enterpriseClient: IEnterpriseClientService
        ) {
        }

        @httpPost("/createSession")
        async createSession(req: express.Request, res: express.Response, ): Promise<{ session: SessionView, cityName: string }> {
            const deviceId: string = req.headers["deviceid"] as string
            const userId: string = req.headers["userid"] as string
            if (!AppUtil.isNotLoggedinUserId(userId)) {
                res.status(401).send({ message: "Authorization failure. Not a valid user id" })
                return
            }
            const cityId = req.headers["cityid"] as string
            const sessionData: SessionData = await this.setCityInSessionData(req)
            const session = await this.sessionBusiness.createSession(deviceId, userId, true, sessionData)
            return { session: new SessionView(session), cityName: await this.getCityName(sessionData.cityId) }
        }

        async getCityName(cityId: string): Promise<string> {
            const cityPromise = !_.isNil(cityId) ? this.cityService.getCityById(cityId) : undefined
            if (cityPromise != undefined) {
                const city = await cityPromise
                return city ? city.name : undefined
            }
            return null
        }

        private validateAppleLogin(email: string) {
            if (email === "<EMAIL>" && process.env.ENVIRONMENT !== "PRODUCTION") {
                this.logger.error("This email allowed only in production.")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("This email allowed only in production.").build()
            }
        }

        @httpPost("/googleLogin")
        async googleLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const accessToken: string = req.body.accessToken
            const idToken: string = req.body.idToken
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            const tenant: string = AppUtil.getTenantFromReq(req)
            this.assertAppVersionSupported(req)
            const user: User = await this.userService.googleLogin(idToken, accessToken, deviceId, tenant, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info(`google login done for user Id ${user.id} and device ${deviceId} for ip ${res.locals.ip}`)
            this.validateAppleLogin(user.email)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            const userContext = req.userContext as UserContext
            const isUserOnboardingFormSupported: boolean =  await AppUtil.isUserOnboardingFormSupported(this.segmentService, userContext, true)
            const loginResponse: LoginResponse = response.loginResponse
            if (isUserOnboardingFormSupported) {
                return {...loginResponse, onboardingFormId: ONBOARDING_FORM_ID}
            }
            return loginResponse
        }

        @httpPost("/appleLogin")
        async appleLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const idToken: string = req.body.idToken
            const authorizationCode = req.body.authorizationCode
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const tenant: string = AppUtil.getTenantFromReq(req)
            const attributionDetails: AttributionDetails = req.body.attributionDetails

            const user: User = await this.userService.appleLogin(idToken, authorizationCode, deviceId, tenant, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info(`apple login done for user Id ${user.id} and device ${deviceId} for ip ${res.locals.ip}`)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            const userContext = req.userContext as UserContext
            const isUserOnboardingFormSupported: boolean =  await AppUtil.isUserOnboardingFormSupported(this.segmentService, userContext, true)
            const loginResponse: LoginResponse = response.loginResponse
            if (isUserOnboardingFormSupported) {
                return {...loginResponse, onboardingFormId: ONBOARDING_FORM_ID}
            }
            return loginResponse
        }

        @httpPost("/googleWebLogin")
        async googleWebLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const authorizationCode: string = req.body.authorizationCode
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            this.assertAppVersionSupported(req)
            const user = await this.userService.googleWebLogin(authorizationCode, deviceId)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info(`google web login done for user Id ${user.id} and device ${deviceId} for ip ${res.locals.ip}`)

            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/phonePeLogin")
        async phonePeLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const grantToken: string = req.body.grantToken
            const apiKey: string = req.headers["apikey"] as string
            const miniAppId = AppUtil.getMiniAppId(apiKey)
            this.logger.info(`api Key is ${apiKey} and miniAppId is ${miniAppId}`)
            const phonepeLoginResponse = await this.userService.phonePeLogin(grantToken, miniAppId)
            const user = phonepeLoginResponse.user
            const deviceId = crypto.randomUUID()
            const cityId = req.headers["cityid"] as string
            let session
            const sessionData = await this.setCityInSessionData(req)
            if (phonepeLoginResponse.action === "LOGIN") {
                session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
                AuthUtil.setCookies(session.st, session.at, deviceId, req, res, this.logger)
                this.logger.info(`phone pe login done for user Id ${user.id} and device ${deviceId} and ip ${res.locals.ip}`)
            }
            return {
                user: user ? new UserView(user, false) : undefined,
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: sessionData.cityId,
                redirectUri: undefined,
                sessionId: phonepeLoginResponse.sessionId,
                action: phonepeLoginResponse.action
            } as LoginResponse
        }

        // This is called only from GooglePay -> Eat.Fit Micro website
        @httpPost("/googlePayLogin")
        async googlePayLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            this.logger.info(`Executing /auth/googlePayLogin.`)
            const identityToken: string = req.body.identityToken
            const phoneNumberToken: string = req.body.phoneNumberToken
            this.logger.info(`googlePay identityToken= ${identityToken}, phoneNumberToken= ${phoneNumberToken}, calling user service`)
            const googlePayLoginResponse = await this.userService.googlePayLogin(identityToken, phoneNumberToken)
            if (_.isNil(googlePayLoginResponse)) {
                this.logger.error("Could not login to google pay account")
                throw this.errorFactory.withCode(ErrorCodes.GOOGLE_PAY_LOGIN_ERR, 400).withDebugMessage("Could not login to google pay account").withContext({ googlePayLoginRequest: req }).build()
            }
            const user = googlePayLoginResponse.user
            const deviceId = crypto.randomUUID()
            let session
            const cityId = req.headers["cityid"] as string
            const sessionData = await this.setCityInSessionData(req)
            if (!_.isNil(user) && googlePayLoginResponse.action === "LOGIN") {
                session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
                AuthUtil.setCookies(session.st, session.at, deviceId, req, res, this.logger)
                this.logger.info(`GooglePay login done for user Id ${user.id} and device ${deviceId} and ip ${res.locals.ip}`)
            }
            const loginResponse: LoginResponse = {
                user: user ? new UserView(user, false) : undefined,
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: sessionData.cityId,
                redirectUri: undefined,
                action: googlePayLoginResponse.action,
                sessionId: googlePayLoginResponse.sessionId,
                gPayEmail: googlePayLoginResponse.gPayEmail,
                gPayPhone: googlePayLoginResponse.gPayPhone
            }
            return loginResponse
        }

        @httpPost("/phonePeVerifyEmail")
        async phonePeVerifyEmail(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const grantToken: string = req.body.grantToken
            const OTP: string = req.body.OTP
            const response = await this.userService.phonePeVerifyEmail(grantToken, OTP)
            const user = response.user
            const deviceId = crypto.randomUUID()
            const cityId = req.headers["cityid"] as string
            const sessionData = await this.setCityInSessionData(req)
            const session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
            AuthUtil.setCookies(session.st, session.at, deviceId, req, res)
            return {
                user: new UserView(user, false),
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: sessionData.cityId,
                redirectUri: undefined
            } as LoginResponse
        }

        @httpPost("/googlePayVerifyEmail")
        async googlePayVerifyEmail(req: express.Request, res: express.Response): Promise<LoginResponse> {
            this.logger.info(`Executing /auth/googlePayVerifyEmail`)
            const identityToken: string = req.body.identityToken
            const phoneNumberToken: string = req.body.phoneNumberToken
            const OTP: string = req.body.OTP
            const response = await this.userService.googlePayVerifyEmail(identityToken, phoneNumberToken, OTP)
            const user = response.user
            const deviceId = crypto.randomUUID()
            const cityId = req.headers["cityid"] as string
            const sessionData = await this.setCityInSessionData(req)
            const session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
            AuthUtil.setCookies(session.st, session.at, deviceId, req, res)
            const loginResponse: LoginResponse = {
                user: new UserView(user, false),
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: sessionData.cityId,
                redirectUri: undefined
            }
            return loginResponse
        }

        @httpPost("/paytmPWALogin")
        async paytmPWALogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            this.logger.info(`Executing /auth/paytmPWALogin.`)
            const authCode: string = req.body.authCode
            const openId: string = req.body.openId
            this.logger.info(`paytm PWA authCode= ${authCode}, openId= ${openId}, calling user service`)
            const paytmPWALoginResponse = await this.userService.paytmLogin(authCode)
            if (_.isNil(paytmPWALoginResponse)) {
                this.logger.error("Could not login to paytm PWA")
                throw this.errorFactory.withCode(ErrorCodes.PAYTM_PWA_LOGIN_ERR, 400).withDebugMessage("Could not login to paytm PWA").withContext(req).build()
            }
            const user = paytmPWALoginResponse.user
            const deviceId = crypto.randomUUID()
            let session
            const cityId = req.headers["cityid"] as string
            const sessionData = await this.setCityInSessionData(req)
            if (!_.isNil(user) && paytmPWALoginResponse.action === "LOGIN") {
                session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
                AuthUtil.setCookies(session.st, session.at, deviceId, req, res, this.logger)
                this.logger.info(`Paytm PWA login done for user Id ${user.id} and device ${deviceId} and ip ${res.locals.ip}`)
            }
            const loginResponse: LoginResponse = {
                user: user ? new UserView(user, false) : undefined,
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: sessionData.cityId,
                redirectUri: undefined,
                action: paytmPWALoginResponse.action,
                sessionId: paytmPWALoginResponse.sessionId
            }
            return loginResponse
        }

        @httpPost("/tataPWALogin")
        async tataPWALogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const authCode: string = req.query.authCode
            const codeVerifier: string = req.query.codeVerifier
            this.logger.info(`/tataPWALogin  with params`, {authCode, codeVerifier})
            const deviceId: string = crypto.randomUUID()
            const userId = (await this.thirdPartyService.resolveUserForPWALogin(authCode, codeVerifier)).userId
            const user: User = await this.userService.getUser(userId)
            const session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, {})
            AuthUtil.setCookies(session.st, session.at, deviceId, req, res, this.logger)
            return {
                user: user ? new UserView(user, false) : undefined,
                session: session ? new SessionView(session) : undefined,
                cityName: undefined,
                cityId: undefined,
                redirectUri: undefined
            }
        }
        @httpPost("/b2bGetPWAUrl")
        async b2bPWAUrl(req: express.Request, res: express.Response): Promise<any> {
            const secretKey: string = req.headers["api-key"] as string
            const firstName: string = req.body?.firstName
            const lastName: string = req.body?.lastName
            const email: string = req.body?.email
            const phoneNumber = req.body.phoneNumber

            if (!secretKey) {
                res.status(401).send({ message: "Authorization failure. Key not found" })
                return
            }

            const sso_keys = JSON.parse(this.appConfigStore.getConfig("ENTERPRISE_SSO_API_KEYS", "{}"))

            if (!sso_keys.hasOwnProperty(secretKey)) {
                res.status(401).send({ message: "Authorization failure. Not valid key" })
                return
            }

            const b2bMap = sso_keys[secretKey]

            if (b2bMap == null || _.isEmpty(b2bMap)) {
                res.status(401).send({ message: "Authorization failure. Not valid key" })
                return
            }

            if (b2bMap["corporateCode"] == null || b2bMap["base_url"] == null || _.isEmpty(b2bMap["corporateCode"]) || _.isEmpty(b2bMap["base_url"])) {
                res.status(401).send({ message: "Authorization failure. CorporateCode OR BaseURL not found" })
                return
            }

            if (!phoneNumber) {
                res.status(400).send({ message: "Bad request - phoneNumber param is required" })
                return
            }

            const employeeInfo: EmployeeInfo = {}
            if (email) {
                employeeInfo["email"] = email
            }

            if (phoneNumber) {
                employeeInfo["phoneNumber"] = phoneNumber
            }

            const employeeForCorporate = await this.enterpriseClient.findActiveEmployeesForCorporate({"employeeInfo": employeeInfo, "corpCode": b2bMap["corporateCode"] })
            this.logger.info(`b2bGetPWAUrl employeeForCorporate : ${JSON.stringify(employeeForCorporate)}`)
            if (!employeeForCorporate) {
                res.status(401).send({ message: "Authorization failure. No employee found, please enrol employee" })
                return
            }

            const userPhoneNumber: string = phoneNumber.countryCode + "-" + phoneNumber.number
            this.logger.info(`b2bGetPWAUrl with params`, {secretKey, firstName, lastName, email, userPhoneNumber})
            let user = await this.userService.getUserUsingPhone(userPhoneNumber)
            this.logger.info(`b2bGetPWAUrl user fetched from user-service: ${JSON.stringify(user)}`)
            if (_.isNil(user)) {
                user = await this.userService.registerWalkin(firstName, lastName, phoneNumber.number, phoneNumber.countryCode)
            }

            if (_.isNil(user)) {
                res.status(500).send({ message: "Some error occured while enrolling" })
                return
            }

            const userId = String(user.id)
            const currentTime = new Date().getTime()
            const authExpiryTime = currentTime + 5 * 60000
            const cityId = employeeForCorporate["cityId"]

            const hash = crypto.createHmac("sha256", "ABC").update(userId).digest("base64")
            const key = crypto.randomBytes(32)
            const iv = crypto.randomBytes(16)

            // Store key securely
            const keyId = "B2B_KEY_" + crypto.randomBytes(4).toString("hex")

            await this.crudDao.create(keyId, key.toString("base64"))
            await this.crudDao.setExpiry(keyId, this.B2B_REDIS_KEY_TTL_IN_SEC)

            const ivId = "B2B_IV_" + crypto.randomBytes(4).toString("hex")
            await this.crudDao.create(ivId, iv.toString("base64"))
            await this.crudDao.setExpiry(ivId, this.B2B_REDIS_KEY_TTL_IN_SEC)

            const requestBody = {
                userId : userId,
                cityId : cityId,
                hash : hash,
                time : authExpiryTime
            }

            const cipher = crypto.createCipheriv("aes-256-cbc", key, iv)
            let encryptedString = cipher.update(JSON.stringify(requestBody), "utf-8", "hex")
            encryptedString += cipher.final("hex")
            console.log("The encrypted message is:", encryptedString)

            const objectData = {
                encryptedString: encryptedString,
                keyId: keyId,
                ivId: ivId
            }

            const base64encoded = Buffer.from(JSON.stringify(objectData)).toString("base64")

            return  {
                "gymUrl": b2bMap["base_url"] + "?authCode=" + base64encoded + "&gymPage=true",
                "cultCenterUrl": b2bMap["base_url"] + "?authCode=" + base64encoded + "&gymPage=false",
                "authCode": base64encoded
            }
        }

        @httpPost("/b2bPWALogin")
        async b2bPWALogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const authCode: string = req.query.authCode
            this.logger.info(`/b2bPWALogin with params`, {authCode})
            const decryptedString = JSON.parse(Buffer.from(authCode, "base64").toString())

            const encryptedString = decryptedString["encryptedString"]
            const keyId = decryptedString["keyId"]
            const ivId = decryptedString["ivId"]
            const deviceId: string = crypto.randomUUID()
            const keyString = await this.crudDao.read(keyId)
            const ivString = await this.crudDao.read(ivId)

            const key = Buffer.from(keyString, "base64")
            const iv = Buffer.from(ivString, "base64")

            if (!key || !iv) {
                return {
                    user:  undefined,
                    session: undefined,
                    cityName: undefined,
                    cityId: undefined,
                    redirectUri: undefined
                }
            }

            // Decrypt the message
            const decipher = crypto.createDecipheriv("aes-256-cbc", key, iv)
            let decryptedDataString = decipher.update(encryptedString, "hex", "utf-8")
            decryptedDataString += decipher.final("utf8")
            const decryptedData = JSON.parse(decryptedDataString)
            const userId = decryptedData.userId
            const cityId = decryptedData.cityId
            this.logger.info(`b2bPWALogin cityId : ${cityId}`)

            const newHash = crypto.createHmac("sha256", "ABC").update(userId).digest("base64")
            req.headers["cityid"] = cityId
            const sessionData = await this.setCityInSessionData(req)
            this.logger.info(`b2bPWALogin sessionData : ${JSON.stringify(sessionData)}`)
            if (newHash === decryptedData.hash && decryptedData.time > new Date().getTime()) {
                const user: User = await this.userService.getUser(userId)
                const session = await this.sessionBusiness.createSession(deviceId, user.id.toString(), false, sessionData)
                AuthUtil.setCookies( session.st, session.at, deviceId, req, res, this.logger )
                return {
                    user: user ? new UserView(user, false) : undefined,
                    session: session ? new SessionView(session) : undefined,
                    cityName: undefined,
                    cityId: sessionData.cityId,
                    redirectUri: undefined
                }
            }
        }

        @httpPost("/truecallerDummyCallback")
        async truecallerLoginDummyCallback(req: express.Request, res: express.Response): Promise<any> {
            const header = {"virtual-cluster-name": "truecaller-login"}
            return fetch("https://stage.cult.fit/api/auth/truecaller/callback", this.fetchHelper.post({headers: header, body: req.body}))
        }

        // callback api truecaller will hit upon successful user login
        @httpPost("/truecaller/callback")
        async truecallerLoginCallback(req: express.Request, res: express.Response): Promise<{}> {
            this.logger.debug("Callback received from truecaller with req.body - ", req.body)
            if (req.body === undefined) {
                this.logger.error("Received no body truecaller callback")
                throw new DataError("Received no body truecaller callback")
            }
            const requestId: string = req.body.requestId
            const accessToken: string = req.body.accessToken
            const endpoint: string = req.body.endpoint
            const status: string = req.body.status
            const redisKey = `truecallerLoginStatus_${requestId}`
            if (status === "flow_invoked") {
                return {}
            } else if (status !== undefined) {
                this.logger.debug("User rejected login with truecaller. Redis key " + redisKey)
                await this.crudDao.create(redisKey, `{"status":"rejected", "message":"${status}"}`)
                await this.crudDao.setExpiry(redisKey, this.TRUECALLER_REDIS_KEY_TTL_IN_SEC)
                return {}
            }
            if (!this.validateTruecallerUrl(endpoint)) {
                this.logger.error("Unknown endpoint given in truecaller callback!!")
                throw new ForbiddenError("Unknown endpoint given in truecaller callback!!")
            }
            this.fetchTruecallerUserProfile(accessToken, endpoint, redisKey)
            return {}
        }

        private validateTruecallerUrl(endpoint: string): boolean {
            const url: URL = new URL(endpoint)
            const domain = url.hostname.split(".").slice(-2).join(".")
            return domain === "truecaller.com"
        }

        private async fetchTruecallerUserProfile(accessToken: string, endpoint: string, redisKey: string): Promise<any> {
            try {
                this.logger.debug("Trying to fetch user form Truecaller api")
                const header = {"Authorization": `Bearer ${accessToken}`}
                const response: any = await fetch(endpoint, this.fetchHelper.get({headers: header}))
                this.logger.debug("truecaller Fetch user profile response: " + JSON.stringify(response))
                const truecallerUser = await this.fetchHelper.parseResponse<any>(response)
                this.logger.debug("truecaller Fetch user profile: " + JSON.stringify(truecallerUser))
                truecallerUser.phoneNumbers[0] = truecallerUser.phoneNumbers[0].toString()
                const phoneNumber = truecallerUser.phoneNumbers[0].slice(-10)
                const countryCallingCode = truecallerUser.phoneNumbers[0].substring(0, truecallerUser.phoneNumbers[0].length - 10)
                const userPhoneNumber: string = "+" + countryCallingCode + "-" + phoneNumber
                this.logger.debug("User phone number: " + userPhoneNumber)
                let user = await this.userService.getUserUsingPhone(userPhoneNumber)
                this.logger.debug("user fetched from user-service: " + JSON.stringify(truecallerUser))
                if (_.isNil(user)) {
                    user = await this.userService.registerTruecaller(truecallerUser.name?.first, truecallerUser.name?.last, userPhoneNumber, truecallerUser.gender)
                }
                this.logger.debug("User verified with truecaller. Redis key " + redisKey)
                await this.crudDao.create(redisKey, `{"status":"verified", "userId":"${user.id}", "phoneNumber":"${userPhoneNumber}"}`)
                await this.crudDao.setExpiry(redisKey, this.TRUECALLER_REDIS_KEY_TTL_IN_SEC)
            } catch (e) {
                this.logger.debug("User failed to verify with truecaller. Redis key " + redisKey)
                this.logger.error(e)
                await this.crudDao.create(redisKey, `{"status":"failed", "message":"Failed to fetch user profile form truecaller"}`)
                await this.crudDao.setExpiry(redisKey, this.TRUECALLER_REDIS_KEY_TTL_IN_SEC)
            }
        }

        // web will poll to get status of login
        @httpPost("/truecallerLoginMWeb")
        async truecallerLoginMWeb(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const requestId = req.body.requestId
            const user: User = await this.authBusiness.truecallerLoginMWebPolling(requestId, appTenant)
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/truecallerLogin")
        async truecallerLogin(req: express.Request, res: express.Response): Promise<any> {
            const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const loginModes = AppUtil.getLoginModesForAppTenant(appTenant, this.appConfigStore)
            if (loginModes.truecaller) {
                this.logger.error("Truecaller is disabled!!")
                throw new RuntimeError("Truecaller is disabled!!")
            }
            this.validateTruecallerPayload(req)
            this.logger.debug("User Payload verified!!")
            const phoneNumber = req.body.userProfile.phoneNumber.slice(-10)
            const countryCallingCode = req.body.userProfile.phoneNumber.substring(0, req.body.userProfile.phoneNumber.length - 10)
            const userPhoneNumber = countryCallingCode + "-" + phoneNumber
            let user = await this.userService.getUserUsingPhone(userPhoneNumber)
            if (_.isNil(user)) {
                this.logger.debug("Creating new user")
                user = await this.userService.registerTruecaller(req.body.userProfile.firstName, req.body.userProfile.lastName, userPhoneNumber, req.body.userProfile.gender)
            }
            this.logger.debug("truecaller User found: " + JSON.stringify(user))
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            const userContext = req.userContext as UserContext
            const isUserOnboardingFormSupported: boolean =  await AppUtil.isUserOnboardingFormSupported(this.segmentService, userContext, true)
            const loginResponse: LoginResponse = response.loginResponse
            if (isUserOnboardingFormSupported) {
                return {...loginResponse, onboardingFormId: ONBOARDING_FORM_ID}
            }
            return loginResponse
        }

        private async validateTruecallerPayload(req: express.Request) {
            const appKeyResponse = await fetch(this.TRUECALLER_APP_KEY_API_ENDPOINT, this.fetchHelper.get())
            const truecallerUser: any = await this.fetchHelper.parseResponse<any>(appKeyResponse)
            const keyStr = truecallerUser[0].key
            const keyBytes = Buffer.from(pemtools(Buffer.from(keyStr, "base64"), "PUBLIC KEY").pem)
            const payload = Buffer.from(req.body.payload)
            const signature = Buffer.from(req.body.signature, "base64")
            const signatureAlgorithm: string = (this.ALGO_MAP)[req.body.signatureAlgorithm]
            const payloadJson = JSON.parse(Buffer.from(req.body.payload, "base64").toString())
            const timeElapsedInMillis = (new Date().getTime()) - (new Date(payloadJson.requestTime).getTime()) * 1000

            const verifier = crypto.createVerify(signatureAlgorithm)
            verifier.update(payload)
            if (timeElapsedInMillis > this.TRUECALLER_PAYLOAD_TIMEOUT_IN_MILLIS || req.body.userProfile.phoneNumber !== payloadJson.phoneNumber || verifier.verify(keyBytes, signature) === false) {
                throw this.errorFactory.withCode(ErrorCodes.ERR_TRUECALLER_LOGIN_FAILED, 401).withDebugMessage("Truecaller login failed").build()
            }
        }

        private async setCityInSessionData(req: express.Request): Promise<SessionData> {
            const sessionData: SessionData = {}
            const userCity: UserCity = await LocationUtil.getUserCityFromReq(req, this.cityService, this.logger, this.CFAPICityService, this.deviceBusiness)
            this.cfAnalytics.sendEventFromReq(<UserCitySetReasonAnalyticsEvent>{
                reason: userCity?.reason,
                cityId: userCity?.city?.cityId,
                cityName: userCity?.city?.name,
                isCitySelectedManually: userCity?.isCityManuallySelected,
                latitude: Number(req.headers["lat"]),
                longitude: Number(req.headers["lon"]),
                userIp: req.ip ?? req.connection.remoteAddress,
                analyticsEventName: AnalyticsEventName.USER_CITY_SET_REASON,
                from: "AuthController.setCityInSessionData",
                isReasonSession: userCity?.reason === "SESSION"
            }, req, false, true, false, false)
            sessionData.cityId = userCity.city.cityId
            sessionData.isCityManuallySelected = userCity.isCityManuallySelected
            sessionData.citySelectedTimeInMillis = new Date().getTime()
            return sessionData
        }


        @httpPost("/loginPhoneSendOtp")
        async loginPhoneSendOtp(req: express.Request, res: express.Response): Promise<boolean> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const medium: OTP_MEDIUM = req.body.medium || "sms"
            const appVersion: string = req.headers["appversion"] as string
            const { captchaResponse, countryCallingCode, phone } = req.body

            this.logger.info(`loginPhoneSendOtp req.ip: ${JSON.stringify(req.ip)}, remoteAddress: ${JSON.stringify(req.connection?.remoteAddress)}, reqHeaders: ${JSON.stringify(req.headers)}, requestBody: ${JSON.stringify(req.body)} `)
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)
                this.logger.info(`/loginPhoneSendOtp  verifyCaptchaResp`, verifyCaptchaResp)
                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    const response = await this.userService.loginPhoneSendOtp(phone, countryCallingCode, tenant, medium)
                    return response
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha on /loginPhoneSendOtp Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            }
            const response = await this.userService.loginPhoneSendOtp(phone, countryCallingCode, tenant, medium)
            return response
        }

        @httpPost("/sfGuest/login")
        async guestLoginAuth(req: express.Request, res: express.Response): Promise<PhoneOtpLoginResponse | LoginResponse> {
            // const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(req.body.token, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
            // this.logger.info(`Captcha score :: ${verifyCaptchaResp.score}`)
            // if (!verifyCaptchaResp.success || verifyCaptchaResp.score < 0.6) {
            //     throw new UnauthorizedError("Recaptcha not validated")
            // }
            const phoneNumber = req.body.phoneNumber
            let name = req.body.name
            let existingUser = await this.userService.getUserUsingPhoneAndTenantV3(`+91-${phoneNumber}`, AppTenant.SUGARFIT)
            this.logger.info(`Existing user for phoneNumber :: ${phoneNumber}, user :: ${JSON.stringify(existingUser)}`)
            if (!name) {
                name = "Sugarfit User"
            }
            if (!existingUser) {
                const [firstName, lastName] = name.split(/\s+(.*)/)
                existingUser = await this.userService.registerWalkin(firstName, lastName, phoneNumber, "+91")
            } else {
                if (!existingUser.firstName) {
                    const [firstName, lastName] = name.split(/\s+(.*)/)
                    existingUser = await this.userService.setName(existingUser.id, firstName, lastName)
                }
            }
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const appVersion: string = req.headers["appversion"] as string
            const response = await this.authBusiness.registerDeviceAndCreateSession(existingUser, deviceId, appVersion, req, res, false)
            response.loginResponse.user = null
            response.loginResponse.session = null
            return response.loginResponse
        }

        @httpPost("/loginPhoneVerifyOtp")
        async mobileLoginVerifyOtp(req: express.Request, res: express.Response): Promise<PhoneOtpLoginResponse | LoginResponse> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const appVersion: string = req.headers["appversion"] as string
            const { phone, countryCallingCode, otp, captchaResponse } = req.body
            let phoneOtpLoginResponse: PhoneOtpLoginResponse
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)

            this.logger.info("loginPhoneVerifyOtp Request Body: " + JSON.stringify(req.body))
            // API test automation credentials
            // Munaf's numbers "8971892270", "9164066394", "7349732015", "9945074035", "9148000897", "9900380225", "8310618170"
            const sugarfitTestUsers = ["8971892270", "9164066394", "7349732015", "9945074035", "9148000897", "9900380225", "8310618170", "9966756175", "9004516401", "8919165156", "9987771198", "8826924425", "8802461208", "9980050431", "9461400000", "8197471516", "7799579187", "7483457760", "8951857708", "8951859787", "8951859495", "8951856343", "8951856210"]
            if ((sugarfitTestUsers.includes(phone) || phone === "9999999996" || phone === "9999999998" || phone === "9686226968" || phone === "9343582162" || phone === "9916740308" || phone === "7909068613" || phone === "9916192220" || phone === "8660897043" || phone === "9353695363" || phone === "9632235009" || phone === "9980009767" || phone === "7503649650" || phone === "8792933933" || phone === "8792514524" || phone === "8792514525" || phone === "8951901644" || phone === "8792514511") && otp === "132465" && (tenant === Tenant.SUGARFIT_APP || tenant === Tenant.ULTRAFIT_APP || tenant === Tenant.CUREFIT_APP || tenant === Tenant.CULTWATCH || tenant === Tenant.CULTSPORT_APP)) {
                const user = await this.userService.getUsersUsingPhone(phone, countryCallingCode, null, AppUtil.getAppTenantFromReq(req))
                const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
                const appVersion: string = req.headers["appversion"] as string
                const response = await this.authBusiness.registerDeviceAndCreateSession(user[0], deviceId, appVersion, req, res, false)
                try {
                    this.logger.info(`user object for ${phone} => ${JSON.stringify(user)}`)
                } catch (error) {
                    // Ignore
                }
                return response.loginResponse
            }

            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)

                this.logger.info(`/loginPhoneVerifyOtp  verifyCaptchaResp`, verifyCaptchaResp)

                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    phoneOtpLoginResponse = await this.userService.loginPhoneVerifyOtp(phone, countryCallingCode, otp, tenant, deviceId)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha on /loginPhoneVerifyOtp Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            } else {
                phoneOtpLoginResponse = await this.userService.loginPhoneVerifyOtp(phone, countryCallingCode, otp, tenant, deviceId)
            }

            this.assertAppVersionSupported(req)
            // Note: Do not register device and create a session if the request is from cultstore-shopify
            if (!phoneOtpLoginResponse.isPhoneChurned && !AppUtil.isCultstoreShopifyApiKey(AuthUtil.getApiKeyFromReq(req))) {
                this.authBusiness.sendBranchAnalyticsDataToRashi(phoneOtpLoginResponse.user.id, req)
                this.logger.info(`loginPhoneVerifyOtp done for user Id ${phoneOtpLoginResponse.user.id} and device ${deviceId} for ip ${res.locals.ip}`)
                const response = await this.authBusiness.registerDeviceAndCreateSession(phoneOtpLoginResponse.user, deviceId, appVersion, req, res, false)
                return response.loginResponse
            }
            const churnedUserId = phoneOtpLoginResponse.user.id
            const accountRetrievalUrl = ActionUtil.formServiceUrl(this.retrieveOldAccountFormId(), { phone, countryCallingCode, churnedUserId })
            const installDeferDeeplink = this.authBusiness.getInstallDeferDeeplinkUrl(req.body.branchAnalyticsData)
            return Object.assign({}, phoneOtpLoginResponse, { accountRetrievalUrl }, { installDeferDeeplink })

        }

        private retrieveOldAccountFormId() {
            if (process.env.ENVIRONMENT === "PRODUCTION") {
                return "ACCOUNTRETRIEVAL_CHURNFLOW"
            } else {
                return "ACCOUNTRETRIEVAL_CHURNFLOW"
            }
        }

        @httpPost("/verifyWithEmailLogin")
        async verifyWithEmailLogin(req: express.Request, res: express.Response): Promise<boolean> {
            const user = await this.userService.getUserUsingEmail(req.body.email)
            const response = await this.userService.emailOtpLoginSendInSession(req.body.email, req.body.sessionId)
            if (response && user) {
                this.customEventEmitter.emitEvent(eventName.LOGIN, user.id, req.session?.sessionData?.attributionSource)
            }
            return response
        }


        @httpPost("/verifyWithEmailLoginOtp")
        async verifyWithEmailLoginOtp(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const appVersion: string = req.headers["appversion"] as string
            const {email, otp, sessionId} = req.body
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            this.assertAppVersionSupported(req)
            this.validateAppleLogin(email)

            // App store login credentials
            if (["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"].includes(email) && otp === "132465" && (tenant === Tenant.SUGARFIT_APP || tenant === Tenant.ULTRAFIT_APP || tenant === Tenant.CUREFIT_APP)) {
                const user = await this.userService.getUserUsingEmail(email)
                const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
                const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
                return response.loginResponse
            }

            const user: User = await this.userService.emailOtpLoginVerifyInSession(email, otp, sessionId, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/verifyWithFacebook")
        async verifyWithFacebook(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const id: string = req.body.id
            const accessToken: string = req.body.accessToken
            const expiresIn: number = req.body.expiresIn
            const sessionId: string = req.body.sessionId
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            this.assertAppVersionSupported(req)
            const user: User = await this.userService.fbLoginSession(id, accessToken, expiresIn, deviceId, sessionId, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/verifyWithGoogleLogin")
        async verifyWithGoogleLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const accessToken: string = req.body.accessToken
            const idToken: string = req.body.idToken
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const sessionId: string = req.body.sessionId
            const tenant: string = AppUtil.getTenantFromReq(req)
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            this.assertAppVersionSupported(req)
            const user: User = await this.userService.googleLoginInSession(idToken, accessToken, deviceId, sessionId, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.validateAppleLogin(user.email)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/verifyWithGoogleWebLogin")
        async verifyWithGoogleWebLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const authorizationCode: string = req.body.authorizationCode
            const appVersion: string = req.headers["appversion"] as string
            const sessionId: string = req.body.sessionId
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            this.assertAppVersionSupported(req)

            const user = await this.userService.WebLoginInSession(authorizationCode, deviceId, sessionId)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info(`google web login done for user Id ${user.id} and device ${deviceId} for ip ${res.locals.ip}`)

            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }


        @httpPost("/createAccount")
        async createAccountInSessionId(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const session: Session = req.session

            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const appVersion: string = req.headers["appversion"] as string

            this.assertAppVersionSupported(req)
            const user = await this.userService.createAccountWithPhoneInSession(req.body.phone, req.body.countryCallingCode, req.body.sessionId)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/fbLogin")
        async facebookLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const id: string = req.body.id
            const accessToken: string = req.body.accessToken
            const expiresIn: number = req.body.expiresIn
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
            const tenant: string = AppUtil.getTenantFromReq(req)
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            this.assertAppVersionSupported(req)
            const user: User = await this.userService.fbLogin(id, accessToken, expiresIn, deviceId, tenant, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info(`FB login done for user Id ${user.id} and device ${deviceId} for ip ${res.locals.ip}`)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            const userContext = req.userContext as UserContext
            const isUserOnboardingFormSupported: boolean =  await AppUtil.isUserOnboardingFormSupported(this.segmentService, userContext, true)
            const loginResponse: LoginResponse = response.loginResponse
            if (isUserOnboardingFormSupported) {
                return {...loginResponse, onboardingFormId: ONBOARDING_FORM_ID}
            }
            return loginResponse
        }

        @httpPost("/deviceLogin")
        async deviceLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const id: string = req.body.id
            const accessToken: string = req.body.accessToken
            const expiresIn: number = req.body.expiresIn
            const osName: string = req.headers["osname"] as string
            const appVersion: string = req.headers["appversion"] as string
            const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)

            const user = await this.userService.deviceLogin(deviceId)
            this.logger.info(`Device login done for user Id ${user.id} and device ${deviceId} and reqBody is ${JSON.stringify(req.body)}`)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, true)
            return response.loginResponse
        }


        @httpPost("/login")
        async emailLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            throw new DataError("Server Error - Please use phone otp login")
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const tenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const clientVersion: string = req.headers["clientversion"] as string
            const osName: string = req.headers["osname"] as string
            const captchaResponse = req.body.captchaResponse
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)
                if (!(verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6)) {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            }
            // if (osName === "ios") {
            //     throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR, 456).withDebugMessage("App version not supported").build()
            // } else {
            //     throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR, 456).withDebugMessage("App version not supported").build()
            // }

            const email: string = req.body["email"] as string
            const password: string = req.body["password"] as string
            let deviceId: string = req.headers["deviceid"] as string
            deviceId = req.signedCookies["deviceId"] || req.cookies["deviceId"] || deviceId
            // To handle the case where login from phone pe app not calling user status api
            // and hence device is not coming from cookie
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            deviceId = this.authBusiness.validateAndGetDeviceId(req)
            deviceId = (orderSource === "PHONEPE_APP" && _.isNil(deviceId)) ? crypto.randomUUID() : deviceId
            this.validateAppleLogin(email)
            const user = await this.userService.emaillogin(email, password, tenant)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info("Email login done for user Id " + user.id)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, clientVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/email/otprequest")
        async emailOTPRequested(req: express.Request, res: express.Response): Promise<boolean> {
            const email: string = req.body["email"] as string
            const attributionDetails: AttributionDetails = req.body.attributionDetails
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            this.logger.info("Email otp request on " + tenant + "for email:: " + email)
            return await this.userService.loginEmailSendOtp(email, tenant, attributionDetails)
        }

        @httpPost("/email/otplogin")
        async emailOTPLogin(req: express.Request, res: express.Response): Promise<LoginResponse> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const otp: string = req.body["otp"] as string
            const email: string = req.body["email"] as string
            const attributionDetails: AttributionDetails = req.body.attributionDetails

            const appVersion: string = AppUtil.getAppVersion(req)
            if (!email && !otp) {
                throw ("Email and OTP are mandatory fields")
            }

            let deviceId: string = req.headers["deviceid"] as string
            deviceId = !_.isNil(req.signedCookies["deviceId"]) ? req.signedCookies["deviceId"] : deviceId
            // To handle the case where login from phone pe app not calling user status api
            // and hence device is not coming from cookie
            const apiKey: string = req.headers["apikey"] as string
            const orderSource: OrderSource = AppUtil.callSource(apiKey)
            deviceId = this.authBusiness.validateAndGetDeviceId(req)
            deviceId = (orderSource === "PHONEPE_APP" && _.isNil(deviceId)) ? crypto.randomUUID() : deviceId

            if (["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"].includes(email) && otp === "132465" && (tenant === Tenant.SUGARFIT_APP || tenant === Tenant.ULTRAFIT_APP || tenant === Tenant.CULTSPORT_APP || tenant === Tenant.CUREFIT_APP)) {
                const user = await this.userService.getUserUsingEmail(email)
                const deviceId: string = this.authBusiness.validateAndGetDeviceId(req)
                const appVersion: string = req.headers["appversion"] as string
                const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
                return response.loginResponse
            }
            const user: User = await this.userService.loginEmailVerifyOtp(email, otp, tenant, deviceId, attributionDetails)
            this.authBusiness.sendBranchAnalyticsDataToRashi(user.id, req)
            this.logger.info("Email login done for user Id " + user.id)
            const response = await this.authBusiness.registerDeviceAndCreateSession(user, deviceId, appVersion, req, res, false)
            return response.loginResponse
        }

        @httpPost("/forgotpassword")
        async forgotPassword(req: express.Request, res: express.Response): Promise<any> {
            const email: string = req.body["email"] as string
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)
            const appVersion: string = req.headers["appversion"] as string
            if (userAgent === "DESKTOP" || userAgent === "MBROWSER") {
                const captchaResponse = req.body.captchaResponse
                const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
                this.logger.info(` /forgotpassword  verifyCaptchaResp`, verifyCaptchaResp)
                // this.authBusiness.temporaryLogsForDetectingCaptchaError(req, verifyCaptchaResp)
                if (verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6) {
                    return this.userService.forgotPassword(email)
                } else {
                    this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                    this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                    this.metricsUtil.reportCaptchaFailure(userAgent, req.originalUrl.split("?")[0])
                    throw this.errorFactory.withCode(ErrorCodes.CAPTCHA_INVALID, 400).withDebugMessage("captcha not valid").build()
                }
            }
            return this.userService.forgotPassword(email)
        }

        @httpPost("/changepassword")
        async changePassword(req: express.Request, res: express.Response): Promise<{}> {
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const password: string = req.body["password"] as string
            const verificationToken: string = req.signedCookies["verificationToken"] || req.body["token"] as string
            const response = await this.userService.changePassword(verificationToken, password, appTenant)
            const expireRegistrationDoneBefore = new Date().getTime()
            const userId = response.user.id
            AuthUtil.clearChangePasswordToken(res, req)
            return this.authBusiness.publishSessionExpiryMsgToQueue(userId, expireRegistrationDoneBefore, tenant)
        }

        @httpPost("/fitbitLogin")
        async fitbitLogin(req: express.Request): Promise<any> {
            if (!req.body.state) {
                this.logger.error("no state passed in fitbit login")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("no state passed in fitbit login").build()
            }
            return this.atlasActivityService.setFitbitDataForUser({
                userId: req.body.state, fitbitToken: req.body.access_token, fitbitId: req.body.user_id
            }).then((response) => {
                return { "loggedIn": true }
            })
        }

        @httpGet("/logout", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        logout(req: express.Request, res: express.Response): Promise<boolean> {
            const session: Session = req.session
            const tenant: Tenant = AppUtil.getTenantFromReq(req)
            const response = this.authBusiness.deleteActiveUserSession(session, res, tenant, req)
            response && this.customEventEmitter.emitEvent(eventName.LOGOUT, session.userId, session.sessionData?.attributionSource, tenant)
            if (response) {
                this.customEventEmitter.emitEvent(eventName.LOGOUT, session.userId, session.sessionData?.attributionSource, tenant)
                this.publishAuthSQSEvent(session, "LOGOUT", tenant)
            }
            return response
            // return this.sessionBusiness.expireSession(session).then(done => {
            //     AuthUtil.clearCookies(res)
            //     return this.deviceBusiness.logoutDevice(deviceId)
            // })
        }

        @httpGet("/validateCaptcha")
        async checkValidCaptcha(req: express.Request): Promise<any> {
            const { captchaResponse } = req.query
            const verifyCaptchaResp = await this.captchaBusiness.verifyCaptchaLogin(captchaResponse, AppUtil.callSource(AuthUtil.getApiKeyFromReq(req)))
            if (!(verifyCaptchaResp.success && verifyCaptchaResp.score > 0.6)) {
                this.metricsUtil.reportBlockedRequest(BlockingType.CAPTCHA_FAILURE)
                this.logger.error(`AuthController:Failed to verify captcha Error: Failed to verify. Please try again`)
                return { validUserScore: false }
            }
            return { validUserScore: true }
        }

        private publishAuthSQSEvent(session: Session, eventName: string, tenant: Tenant) {
            const authEventPayload: AuthEventData = {
                userId: session.userId,
                tenant: tenant,
                eventName: eventName
            }
            const attributes: Map<string, any> = new Map<string, any>()
            attributes.set("tenant", tenant)
            attributes.set("EVENT_TYPE", eventName)
            const topic: string = process.env.ENVIRONMENT === "PRODUCTION" ? "production-cf-api-user-auth-events" : "stage-cf-api-user-auth-events"
            const eventData: EventData<AuthEventData> = new EventData("USER_AUTH_EVENT", [],
                new Date().getTime(), authEventPayload, attributes)
            this.eventsService.publishMessage(topic, eventData)
        }

        private assertAppVersionSupported(req: express.Request) {
            const osName: string = req.headers["osname"] as string
            if (!AppUtil.isAppVersionSupported(req)) {
                if (osName === "ios" || osName === "tvOS") {
                    throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR, 456).withDebugMessage("App version not supported").build()
                } else {
                    throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR, 456).withDebugMessage("App version not supported").build()
                }
            }
        }

    }
    return AuthController
}

interface AuthEventData {
    userId: string
    tenant: Tenant
    eventName: string
}

export default controllerFactory
