import { inject, injectable } from "inversify"
import {
    BaseDelayedBatchedQ<PERSON>ue<PERSON><PERSON><PERSON>,
    BaseDelayedQueueHandler,
    IQueueService,
    Message,
    SQS_CLIENT_TYPES
} from "@curefit/sqs-client"
import { Constants } from "@curefit/base-utils"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import IAuthBusiness from "./IAuthBusiness"
import { BASE_TYPES, Logger } from "@curefit/base"
import UserBusiness from "../user/UserBusiness"
import DeviceBusiness from "../device/DeviceBusiness"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { Tenant } from "@curefit/base-common"

export interface TokenExpiryPayload {
    userId: string
    deviceId: string
    appId: string
    pushNotificationToken: string
}

@injectable()
class PNTokenEventQueueListener extends BaseDelayedBatchedQueueHandler {
    constructor(
        @inject(SQS_CLIENT_TYPES.QueueService) queueService: IQueueService,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
        super(Constants.getSQSQueue("PN_TOKEN_EVENTS"), 10, queueService, 60 * 1000)
    }

    async handle(body: Message[]): Promise<boolean[]> {
        return Promise.all(body.map(message => this.handleMessage(message.data, message.attributes)))
    }

    async handleMessage(message: string, attributes: { [key: string]: any }): Promise<boolean> {
        this.logger.debug("Session message: " + message)
        const tokenExpiryPayload = <TokenExpiryPayload>JSON.parse(message)
        await this.deviceBusiness.deRegisterPushNotificationToken(tokenExpiryPayload.userId, tokenExpiryPayload.deviceId, tokenExpiryPayload.pushNotificationToken)
        return true
    }
}
export default PNTokenEventQueueListener
