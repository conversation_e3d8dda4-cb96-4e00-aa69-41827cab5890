import { controller, httpGet } from "inversify-express-utils"
import AuthMiddleware from "./AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import * as express from "express"
import IAuthBusiness, { UserSessionDetail } from "./IAuthBusiness"
import { Session } from "@curefit/userinfo-common"
import { Container, inject } from "inversify"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import AppUtil from "../util/AppUtil"
import { Tenant } from "@curefit/base-common"

export function controllerFactory(kernel: Container) {
@controller("/session", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
class SessionController {
    constructor(
            @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
            @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
    ) {
    }
    @httpGet("/userSessionDetails")
    async getSessionDetails(req: express.Request): Promise<UserSessionDetail[]> {
        const userId = req.query.userId
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        return this.authBusiness.getAllActiveUserSessionDetails(userId, tenant)
    }

    @httpGet("/clearUserSessions")
    async clearSessions(req: express.Request, res: express.Response): Promise<boolean> {
        const expireDeviceId: string = req.query.expireDeviceId
        const session: Session = req.session
        const tenant: Tenant = AppUtil.getTenantFromReq(req)
        if (expireDeviceId) {
            if (expireDeviceId === session.deviceId) {
                return this.authBusiness.deleteActiveUserSession(session, res, tenant, req)
            }
            return this.deviceBusiness.getDeviceByDeviceId(expireDeviceId, tenant).then(device => {
                const timeStamp = new Date(device.activeDevice.registerDate).getTime()
                return this.authBusiness.publishSessionExpiryMsgToQueue(session.userId, timeStamp, tenant, expireDeviceId)
            })
        }
        else {
            return this.authBusiness.deleteActiveUserSession(session, res, tenant, req).then( result => {
                const expireRegistrationDoneBefore = new Date().getTime()
                return this.authBusiness.publishSessionExpiryMsgToQueue(session.userId, expireRegistrationDoneBefore, tenant)
            })
        }
    }
}
}
export default controllerFactory
