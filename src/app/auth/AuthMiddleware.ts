import * as express from "express"
import { inject, injectable } from "inversify"

import { BASE_UTILS_TYPES, Constants, IApiKeyService, ISessionBusiness as ISessionService } from "@curefit/base-utils"
import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import AuthUtil from "../util/AuthUtil"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { AppSourceType, Session, SessionInfo } from "@curefit/userinfo-common"
import { UserAgentType as UserAgent } from "@curefit/base-common"
import { ErrorFactory, UNAUTHORIZED_ERROR_CODE, UNKNOWN_LOCATION_ERROR_CODE } from "@curefit/error-client"
import * as _ from "lodash"
import IUserBusiness, { UserCity } from "../user/IUserBusiness"
import { CacheHelper } from "../util/CacheHelper"
import MetricsUtil from "../metrics/MetricsUtil"
import { BlockingType } from "../metrics/models/BlockingType"
import { ICityService, LOCATION_TYPES } from "@curefit/location-mongo"
import { Tenant, User } from "@curefit/user-common"
import { ICrudKeyValue, MultiRedisAccess, REDIS_TYPES } from "@curefit/redis-utils"
import { OrderSource } from "@curefit/order-common"
import IDeviceBusiness from "../device/IDeviceBusiness"
import { UserContext } from "@curefit/vm-models"
import { Timezone } from "@curefit/util-common"
import { ErrorCodes } from "../error/ErrorCodes"
import { ExternalSourceUtil } from "../externalSource/ExternalSourceUtil"
import { ISegmentationCacheClient, SEGMENTATION_CLIENT_TYPES } from "@curefit/segmentation-service-client"
import { PlatformSegmentWrapper } from "../common/PlatformSegmentWrapper"
import { SEGMENT_OVERRIDE_REDIS_KEY } from "../util/VMUtil"
import { SegmentTestOverride } from "../page/vm/segmentoverride/SegmentTestOverride"
import LocationUtil from "../util/LocationUtil"
import { ICFAPICityService } from "../city/ICFAPICityService"
import { ICFAnalytics } from "../cfAnalytics/CFAnalytics"
import { UserCitySetReasonAnalyticsEvent } from "../cfAnalytics/UserCitySetReasonAnalyticsEvent"
import { AnalyticsEventName } from "../cfAnalytics/AnalyticsEventName"
import { TESSERACT_IP } from "./ApiErrorCountMiddleware"

const ipaddr = require("ipaddr.js")

const ONLY_FOR_LOGGEN_IN_USERS = ["/order", "/fitnessPass/activate", "/fitnessPass/v2/activate",
    "/book", "/cart", "/createEvaluation", "/live/start/trial", "/user", "/playbackMetrics", "/updateScoreMetrics", "/payment/apple/updateReceipt", "/payment/apple/refreshReceipt", "/cart/review/v1"]
/**
 * TODO : to remove "/cult/booking/v2" by mid-May 2020
 */
const EXCLUDED_LOGGED_IN_URLS_APP: string[] = ["/cart/review", "/cult/booking/v2", "/digital/detailpage", "/eat/getFirebaseCustomToken", "/cart/setfeaturestate"]
const EXCLUDED_LOGGED_IN_URLS: string[] = ["/user/getUser", "/user/addActivities/v2", "/user/timelineV4",
    "/user/cities/v2", "/user/areas", "/user/updateCityPreference", "/user/gates", "/user/fetchOnDemandAnnouncement", "/user/countries",
    "/user/updateLocation", "/user/updateBrowseLocation", "/user/updateCultCenter", "/user/updateMindCenter", "/user/sendAppLink",
    "/user/recommendation", "/user/location", "/user/bottomBarInteraction", "/user/verifyemail", "/user/stepsData", "/user/sleep", "/care/diagnostic/cart/web", "auth/sfGuest/login",
    "/user/getShopifyMultipassAction", "/user/getShopifyUrlWithMultipassToken"]

const USER_ME_OR_STATUS_CALL = ["/user/me", "/user/status"]
const CITIES_V2_CALL = "/cities/v2"
const SF_GUEST_URLS: string[] = ["/chroniccare/guest/cart/review/v1", "/chroniccare/guest/cart/checkout/v1", "/chroniccare/guest/add/address", "/chroniccare/payment/options/v2", "chroniccare/order", "/guest/juspay/initiatePayload", "/guest/juspay/processPayload", "/ew/chat/post", "/ew/chat/post/smart", "/ew/chat/reply", "/ip/details"]

const LOCATION_WHITE_LIST_ROUTES = [
    ...USER_ME_OR_STATUS_CALL,
    "/device/updatePushNotificationToken",
    "/notifications/",
    "/user/setName",
    "/auth/logout"
]

@injectable()
class AuthMiddleware {
    constructor(
        @inject(BASE_TYPES.ClsUtil) private clsUtil: CLSUtil,
        @inject(CUREFIT_API_TYPES.MetricsUtil) private metricsUtil: MetricsUtil,
        @inject(BASE_UTILS_TYPES.ApiKeyService) private apiKeyService: IApiKeyService,
        @inject(CUREFIT_API_TYPES.DeviceBusiness) private deviceBusiness: IDeviceBusiness,
        @inject(CUREFIT_API_TYPES.UserBusiness) private userBusiness: IUserBusiness,
        @inject(REDIS_TYPES.MultiRedisAccess) multiRedisAccess: MultiRedisAccess,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected cacheHelper: CacheHelper,
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(REDIS_TYPES.RedisDao) public redisDao: ICrudKeyValue,
        @inject(LOCATION_TYPES.CityService) private cityService: ICityService,
        @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionService,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        @inject(CUREFIT_API_TYPES.ExternalSourceUtil) private externalSourceUtil: ExternalSourceUtil,
        @inject(SEGMENTATION_CLIENT_TYPES.SegmentationCacheClient) public segmentationCacheClient: ISegmentationCacheClient,
        @inject(CUREFIT_API_TYPES.CFAPICityService) private CFAPICityService: ICFAPICityService,
        @inject(CUREFIT_API_TYPES.CFAnalytics) private cfAnalytics: ICFAnalytics
    ) {
        this.validateSession = this.validateSession.bind(this)
        this.validateCultstoreShopifySession = this.validateCultstoreShopifySession.bind(this)
    }

    public async validateCultstoreShopifySession(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
        try {
            this.blockBlacklistedIp(req, res)
            const apiKey: string = AuthUtil.getApiKeyFromReq(req)
            const isWhitelistedRoute = this.isEndpointWhitelistedForCultstoreSession(req)
            // ToDo:: Handle blocking in values yaml file for external dns hosts post migration of tesseract to same aws account
            const isTesseractClientCall = TESSERACT_IP === res.locals.ip && AppUtil.isCultstoreShopifyApiKey(apiKey)
            if (isWhitelistedRoute || isTesseractClientCall) {
                next()
            } else {
                res.status(401).send({ message: "Authorization header incorrect with api-key = " + apiKey })
            }
        } catch (err) {
            next(err)
        }
    }
    private isEndpointWhitelistedForCultstoreSession(req: express.Request) {
        const whitelistedEndpoints = ["/shopify/googleOAuth/callback"]
        return _.includes(whitelistedEndpoints, req.path)
    }


    public async validateSession(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
        try {
            this.blockBlacklistedIp(req, res)
            const ip: string = res.locals.ip
            const appVersion: string = req.headers["appversion"] as string
            const clientVersion: string = req.headers["clientversion"] as string
            const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
            let deviceId: string = req.headers["deviceid"] as string
            const osName: string = req.headers["osname"] as string
            const tenant: Tenant = AppUtil.getTenantFromReq(req)

            let apiKey: string = req.headers["api-key"] as string || req.headers["apikey"] as string || req.query.apikey
            // third parties send apiKey in authorization header in this format : (Bearer $token)
            if (!apiKey && req.headers["authorization"]) {
                apiKey = req.headers["authorization"].split(" ")?.[1]?.trim()
                const apiKeyObject = await this.apiKeyService.getApiKey(apiKey)
                if (!apiKeyObject) {
                    res.status(401).send({ status: "failure", message: "Invalid Authorization header" })
                }
            }
            const source: string = req.headers["user-agent"] as string
            const userAgent: UserAgent = AuthUtil.getUserAgent(req)

            const authToken: string = AuthUtil.getAuthTokenFromRequest(req)
            if (userAgent == "DESKTOP" || userAgent == "MBROWSER") {
                deviceId = deviceId || req.signedCookies["deviceId"] || req.cookies["deviceId"]
            }

            if (authToken) {
                try {
                    // Disabling as its blocking some valid request for OGM
                    // this.validateQueryParams(req.query)
                    const session = await this.sessionBusiness.verifyAuthToken(authToken)
                    // this.logger.debug("Session Obtained on validate session *** " + JSON.stringify(session))
                    // Disabling Location Check
                    // /*
                    //  * for the international app check if the location
                    //  * is serviceable. If not throw Location error
                    //  */
                    // const serviceable = _.get(session, "sessionData.isLocationServiceable", false)
                    // const isInternationalApp = AppUtil.isInternationalAppFromReq(req)
                    // if (isInternationalApp && !this.isLocationWhitelistedRoute(req.originalUrl) && !serviceable) {
                    //     this.logger.info("Location Unserviceable:: Request for userid:: " + session.userId + "with session details :: " + JSON.stringify(session))
                    //     throw this.errorFactory.withCode(ErrorCodes.AUTH_LOCATION_UNSERVICEABLE, 494).withDebugMessage("Location Not serviceable").build()
                    // }

                    session.userAgent = userAgent
                    // session.attributionSource = await this.externalSourceUtil.getExternalSourceValue(session.userId)
                    req.session = session
                    req.userContext = await this.getUserContextFromReq(req)
                    this.checkAppVersion(osName, req)
                    this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, session.userId)

                    const mockSegmentUserId = await this.getMockSegmentUserId(session.userId)
                    this.clsUtil.getNamespace().set(CLSUtil.USER_PLATFORM_SEGMENTS, new PlatformSegmentWrapper(mockSegmentUserId ?? session.userId, this.segmentationCacheClient))

                    this.logger.info(`reqUrl: ${req.originalUrl}, appVersion: ${appVersion}, cpVersion: ${codePushVersion}, osName: ${osName}, apiKey: ${apiKey}, ip: ${ip}, userAgent: ${source}, reqBody: ` + JSON.stringify(req.body))
                    if (AppUtil.isNotLoggedinUser(session) && this.isUrlOnlyForLoggedInUser(req.originalUrl, userAgent)) {
                        this.metricsUtil.reportBlockedRequest(BlockingType.UNAUTHORIZED)
                        throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_ERR, 401).withDebugMessage("Not authorized").build()
                    }
                    next()
                } catch (err) {
                    this.logger.debug("auth failed", err)
                    if (err.statusCode && err.statusCode === UNAUTHORIZED_ERROR_CODE) {
                        this.logger.info("DeviceId: " + JSON.stringify(deviceId))
                        if (deviceId) {
                            this.deviceBusiness.logoutDevice(deviceId, tenant)
                        } else {
                            this.logger.warn("invalid deviceId")
                        }
                        if ((apiKey === Constants.getCurefitWebApiKey() || apiKey === Constants.getJanusApiKey()
                            || apiKey === Constants.getGooglePayWebApiKey() || apiKey === Constants.getPhonePeWebApiKey()
                            || apiKey === Constants.getPhonePeCareApiKey() || apiKey === Constants.getPhonePeCultApiKey()
                            || apiKey === Constants.getPhonePeMindApiKey() || apiKey === Constants.getPhonePeWholefitApiKey()
                            || apiKey === Constants.getPaytmPwaApiKey() || apiKey === Constants.getInternationWebsiteApiKey()
                            || apiKey === Constants.getCureFitTVApiKey() || apiKey === Constants.getCureFitTVIntlApiKey()
                            || apiKey === Constants.getTataNeuWebApiKey() || AppUtil.isCultSportWebApiKey(apiKey)
                            )
                            && this.isUserMeOrStatusCall(req.originalUrl)
                        ) {
                            req.session = {
                                userAgent: userAgent
                            }
                            req.userContext = {
                                sessionInfo: this.getSessionInfoInUserContext(req)
                            }
                            this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, "not_logged_in")
                            this.logger.info(`Request URL: ${req.originalUrl}, Body: ` + JSON.stringify(req.body))
                            next()
                            return
                        }
                    }
                    if (err.statusCode && err.statusCode === UNKNOWN_LOCATION_ERROR_CODE) {
                        throw this.errorFactory.withCode(err.code, err.statusCode).withDebugMessage(err.debugMessage).build()
                    }
                    // if someone throws a generic error in this flow, throw it as it is.
                    // If there is any unexpected error in this flow, it will default to ErrorCodes.UNAUTHORIZED_ERR
                    if (err.namespace) {
                        throw err
                    }
                    this.logger.error(`Err: originalUrl  ${req.originalUrl} apiKey ${apiKey} ip ${ip} source ${source} responseCode:${err.statusCode}, name:${err.name}, title:${err.title}, message:${err.message}, trace:${err.stack}`)
                    throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_ERR, 401).withDebugMessage("Not authorized").build()
                }
            } else if (req.originalUrl.indexOf(CITIES_V2_CALL) >= 0 || this.isSFGuestUserUrls(apiKey, req.originalUrl)) {
                this.logger.info(`Request URL: ${req.originalUrl}, Body: ` + JSON.stringify(req.body), "for not logged in user")
                req.session = {
                    userAgent: userAgent
                }
                req.userContext = {
                    sessionInfo: this.getSessionInfoInUserContext(req),
                    userProfile: {
                        timezone: req.headers["timezone"] as string
                    }
                }
                next()
            } else if (this.isAPIKeyWhitelisted(apiKey, req)) {
                req.session = {
                    userAgent: userAgent
                }
                req.userContext = {
                    sessionInfo: this.getSessionInfoInUserContext(req)
                }
                this.clsUtil.getNamespace().set(CLSUtil.USER_ID_FIELD, "not_logged_in")
                this.logger.info(`Request URL: ${req.originalUrl}, Body: ` + JSON.stringify(req.body))
                next()
            } else {
                this.metricsUtil.reportBlockedRequest(BlockingType.UNAUTHORIZED)
                if (!res.headersSent) {
                    res.status(401).send({ message: "Authorization header missing" })
                }
            }
        } catch (err) {
            next(err)
        }
    }

    private blockBlacklistedIp(req: express.Request, res: express.Response): void {
        if (res.locals.isBlacklisted) {
            this.logger.warn("Request from blacklisted ip " + res.locals.ip)
            if (Math.random() < 0.05) {
                this.logger.error("Request from blacklisted ip " + res.locals.ip + ", body: " + JSON.stringify(req.body))
            }
            this.metricsUtil.reportBlockedRequest(BlockingType.BLACKLISTED)
            throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_ERR, 400).withDebugMessage("blacklisted IP").build()
        }
    }
    private isAPIKeyWhitelisted(apiKey: string, req: express.Request): boolean {
        return apiKey &&
            ((this.isUserMeOrStatusCall(req.originalUrl) &&
                (apiKey === Constants.getGooglePayWebApiKey()
                    || apiKey === Constants.getTataNeuWebApiKey()
                    || apiKey === Constants.getPhonePeWebApiKey()
                    || apiKey === Constants.getPhonePeCareApiKey()
                    || apiKey === Constants.getPhonePeCultApiKey()
                    || apiKey === Constants.getPhonePeMindApiKey()
                    || apiKey === Constants.getWebsiteApiKey()
                    || apiKey === Constants.getCurefitWebApiKey()
                    || apiKey === Constants.getJanusApiKey()
                    || apiKey === Constants.getPhonePeWholefitApiKey()
                    || apiKey === Constants.getPaytmPwaApiKey()
                    || apiKey === Constants.getCureFitTVApiKey()
                    || apiKey === Constants.getCureFitTVIntlApiKey()
                    || AppUtil.isCultSportWebApiKey(apiKey))
                    || apiKey === Constants.getInternationWebsiteApiKey()

                )
                || this.apiKeyService.isValid(apiKey))
    }

    public async getUserContextFromReq(req: express.Request): Promise<UserContext> {
        const session: Session = (<any>req).session
        const sessionInfo = this.getSessionInfoInUserContext(req)
        const userCity: UserCity = await LocationUtil.getUserCityFromReq(req, this.cityService, this.logger, this.CFAPICityService, this.deviceBusiness)
        const userPromise = this.cacheHelper.getUser(session.userId)
        // If subuserid present in header validate the user is authorized to book for the sub user
        const subUserId: string = req.headers["subuserid"] as string
        if (subUserId) {
            const user: User = await userPromise
            const subUserRelation = _.find(user.subUserRelations, relation => {
                return relation.subUserId === subUserId
            })
            if (!subUserRelation) {
                throw this.errorFactory.withCode(ErrorCodes.UNAUTHORIZED_SUB_USER_ERR, 401).withDebugMessage("Not authorized to operate on this sub user").build()
            }
        }
        let timezone = userCity.city.timezone
        if (AppUtil.isInternationalAppFromReq(req) && req.headers["timezone"]) {
            timezone = req.headers["timezone"] as Timezone
        }
        this.cfAnalytics.sendEventFromReq(<UserCitySetReasonAnalyticsEvent>{
            reason: userCity?.reason,
            cityId: userCity?.city?.cityId,
            cityName: userCity?.city?.name,
            isCitySelectedManually: userCity?.isCityManuallySelected,
            latitude: Number(req.headers["lat"]),
            longitude: Number(req.headers["lon"]),
            userIp: req.ip ?? req.connection.remoteAddress,
            analyticsEventName: AnalyticsEventName.USER_CITY_SET_REASON,
            from: "AuthMiddleware.getUserContextFromReq",
            isReasonSession: userCity?.reason === "SESSION"
        }, req, false, true, false, false)
        return {
            userPromise: userPromise,
            userProfile: {
                subUserId: subUserId,
                userId: session.userId,
                cityId: userCity.city.cityId,
                timezone: timezone,
                cultCenterId: session.sessionData.cultCenterId,
                mindCenterId: session.sessionData.mindCenterId,
                areaId: session.sessionData?.locationPreferenceData?.areaId ?? "1",
                city: userCity.city
            },
            sessionInfo: sessionInfo
        }
    }

    private getSessionInfoInUserContext(req: express.Request): SessionInfo {
        const session: Session = (<any>req).session
        const ip: string = req.ip
        const userAgent: UserAgent = session.userAgent
        const osName: string = req.headers["osname"] as string
        const appVersion: number = Number(req.headers["appversion"])
        const isDark: boolean = Boolean(req.header("isDarkMode"))
        const clientVersion: number = Number(req.headers["clientversion"])
        const codePushVersion: number = req.headers["codepushversion"] && req.headers["codepushversion"] !== "undefined" ? Number(req.headers["codepushversion"]) : 0
        const lat: number = Number(req.headers["lat"])
        const lon: number = Number(req.headers["lon"])
        let apiKey: string = req.headers["apikey"] as string
        const appSource: AppSourceType = req.header("appsource") as AppSourceType
        // flipkart sends apiKey in authorization header in this format : (Bearer $token)
        if (!apiKey && req.headers["authorization"]) {
            apiKey = req.headers["authorization"].split(" ")?.[1]?.trim()
        }
        const orderSource: OrderSource = AppUtil.callSource(apiKey)
        return {
            osName: osName,
            appVersion: appVersion,
            clientVersion: clientVersion,
            userAgent: userAgent,
            cpVersion: codePushVersion,
            deviceId: session.deviceId,
            isUserLoggedIn: session.isNotLoggedIn !== true,
            lat: lat,
            lon: lon,
            sessionData: session.sessionData,
            at: session.at,
            apiKey,
            orderSource: orderSource,
            isDark,
            appSource,
            ip: ip
            // attributionSource: session.attributionSource
        }
    }

    private validateQueryParams(query: any) {
        _.forEach(Object.keys(query), key => {
            if (key.indexOf("/") >= 0 || query[key].indexOf("/") >= 0) {
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("invalid query params").build()
            }
        })
    }

    private checkAppVersion(osName: string, req: express.Request) {
        if (!AppUtil.isAppVersionSupported(req)) {
            if (osName === "ios" || osName === "tvOS") {
                throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_IOS_ERR, 456).withDebugMessage("App version not supported").build()
            } else {
                throw this.errorFactory.withCode(ErrorCodes.APP_VERSION_NOT_SUPPORTED_ANDROID_ERR, 456).withDebugMessage("App version not supported").build()
            }
        }
    }

    private isSFGuestUserUrls(apiKey: string, requestUrl: string) {
        if (apiKey === Constants.getSugarFitWebApiKey()) {
            let isGuestUrl = false
            SF_GUEST_URLS.forEach(url => {
                if (requestUrl.indexOf(url) >= 0)
                    isGuestUrl = true
            })
            return isGuestUrl
        } else return false
    }
    private isUrlOnlyForLoggedInUser(requestUrl: string, userAgent: UserAgent) {
        let isUrlOnlyForLoggedInUser: boolean = false
        let hasExcludedUrl: boolean = false
        if (!AppUtil.isWebUserAgent(userAgent)) {
            EXCLUDED_LOGGED_IN_URLS_APP.forEach(url => {
                if (requestUrl.indexOf(url) >= 0)
                    hasExcludedUrl = true
            })
        }
        EXCLUDED_LOGGED_IN_URLS.forEach(url => {
            if (requestUrl.indexOf(url) >= 0)
                hasExcludedUrl = true
        })
        if (hasExcludedUrl) {
            return false
        }
        ONLY_FOR_LOGGEN_IN_USERS.forEach(url => {
            if (requestUrl.indexOf(url) >= 0)
                isUrlOnlyForLoggedInUser = true
        })
        return isUrlOnlyForLoggedInUser
    }

    private isUserMeOrStatusCall(requestUrl: string) {
        let isUserMeOrStatusCall: boolean = false
        USER_ME_OR_STATUS_CALL.forEach(url => {
            if (requestUrl.indexOf(url) >= 0)
                isUserMeOrStatusCall = true
        })
        return isUserMeOrStatusCall
    }

    private isLocationWhitelistedRoute(requestUrl: string): boolean {
        let isWhitelistedRoute: boolean = false
        LOCATION_WHITE_LIST_ROUTES.forEach(url => {
            if (_.includes(requestUrl, url)) {
                isWhitelistedRoute = true
            }
        })
        return isWhitelistedRoute
    }

    private async isBlacklisted(req: express.Request): Promise<boolean> {
        const ip: string = req.ip
        try {
            // FIXME: check ipv6 addresses also
            if (!ipaddr.IPv4.isValid(ip)) {
                return false
            }
            const addr = ipaddr.parse(ip)
            const rateLimitConfig = await this.cacheHelper.getCfApiRateLimitConfig()
            return (rateLimitConfig.configs && rateLimitConfig.configs.blacklistedCIDRs || [])
                .some((cidr: string) => addr.match(ipaddr.parseCIDR(cidr)))
        } catch (err) {
            this.logger.error("could not perform blacklist check", err)
        }
        return false
    }

    private isBot(userAgent: string): boolean {
        const botRegex = new RegExp("bot|google|baidu|bing|msn|duckduckbot|teoma|slurp|yandex", "i")
        return botRegex.test(userAgent)
    }


    private async getMockSegmentUserId(userId: string): Promise<string|null> {
        try {
            const segmentOverride = <SegmentTestOverride>JSON.parse(await this.redisDao.getHashField(SEGMENT_OVERRIDE_REDIS_KEY, userId))
            if (segmentOverride) {
                return segmentOverride.testUserId
            }
        } catch (e) {
            this.logger.error("Error for segmentOverride " + e)
            return null
        }
    }
}

export default AuthMiddleware