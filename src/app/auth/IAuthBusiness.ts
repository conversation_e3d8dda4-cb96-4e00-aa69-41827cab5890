import { Session, UserContext } from "@curefit/userinfo-common"
import { User } from "@curefit/user-common"
import * as express from "express"
import UserView from "../user/UserView"
import SessionView from "./SessionView"
import { LoginResponseAction } from "@curefit/user-client"
import { OTP_MEDIUM, TabTypes } from "../user/UserController"
import {  VerticalInfo } from "../user/INavBarBusiness"
import { AppLayout } from "@curefit/apps-common"
import { AppTenant, Tenant, UserAgentType } from "@curefit/base-common"

export interface LoginResponse {
    user: UserView
    session: SessionView
    cityName: string
    cityId: string
    redirectUri: string
    appTabs?: TabTypes[]
    appLayout?: AppLayout
    action?: LoginResponseAction,
    sessionId?: string,
    gPayEmail?: string,
    gPayPhone?: string
    verticals?: VerticalInfo[]
    isMoreToBeHighlighted?: Boolean
    isPhoneNumberUpdateRequired?: Boolean
    onboardingFormId?: string
    showFlutterHomeCLP?: boolean
    isSfAppLocalisationSupportedUser?: boolean
}
export interface UserSessionDetail {
    deviceId: string,
    osName: string,
    registerDate: Date
}

export interface IRegisterBody {
    firstName: string
    lastName: string
    email: string
    password: string
    token?: string
    deviceId: string
}

export interface BranchAnalyticsData {
    campaign_audience: string
    utm_campaign: string
    utm_source: string
    campaign_vertical: string
    utm_medium: string
    advertising_partner_name: string
    ad_set_name: string
}

export interface AppsFlyerAnalyticsData {
    utm_campaign: string
    utm_source: string
    utm_medium: string
    campaign: string
    media_source: string
    af_adset: string
    af_status: string
}

interface IAuthBusiness {
    publishSessionExpiryMsgToQueue(userId: string, expireRegistrationDoneBefore: number, tenant: Tenant, deviceId?: string): Promise<boolean>
    registerDeviceAndCreateSession(
        user: User,
        deviceId: string,
        appVersion: string,
        req: express.Request,
        res: express.Response,
        isNotLoggedInFlow: boolean
    ): Promise<{ loginResponse: LoginResponse, session: Session }>
    getAllActiveUserSessionDetails(userId: string, tenant: Tenant): Promise<UserSessionDetail[]>
    deleteActiveUserSessionsBasedOnTimestamp(userId: string, expireRegistrationDoneBefore: number, tenant: Tenant, deviceId?: string): Promise<boolean>
    deleteActiveUserSession(session: Session, res: express.Response, tenant: Tenant, req: express.Request): Promise<boolean>
    validateAndGetDeviceId(req: express.Request): string
    registerUserEmailSignUp(body: IRegisterBody): any
    sendBranchAnalyticsDataToRashi(userId: string, req: express.Request): Promise<void>
    temporaryLogsForDetectingCaptchaError(req: express.Request, verifyCaptchaResp: any): void
    getInstallDeferDeeplinkUrl(branchAnalyticsData: BranchAnalyticsData): string
    truecallerLoginMWebPolling(requestId: String, appTenant: AppTenant): Promise<User>
    setWorkEmail(cfUserId: string, workEmail: string): Promise<User>
    setEmail(cfUserId: string, workEmail: string): Promise<User>
    setPhoneNumber(res: express.Response, userId: string, newPhoneNumber: number, medium: OTP_MEDIUM, appVersion: number,
        osName: string, userAgent: UserAgentType, countryCallingCode?: string, session?: Session, captchaResponse?: string, userContext?: UserContext, apiKey?: string
    ): Promise<User>
}
export default IAuthBusiness
