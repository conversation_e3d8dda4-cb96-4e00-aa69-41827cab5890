import { injectable, inject } from "inversify"
import IMealPlannerBusiness from "./IMealPlannerBusiness"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { IRecommendationService, RECOMMENDATION_CLIENT_TYPES } from "@curefit/recommendation-client"
import { UserContext } from "@curefit/vm-models"
import { PrescriptionItemStatus } from "@curefit/gmf-common"
import { UrlPathBuilder } from "@curefit/product-common"
import { ActionUtil, SeoUrlParams } from "@curefit/base-utils"
import { BASE_TYPES, Logger } from "@curefit/base"
import { UserMealPlanSummary } from "@curefit/gmf-client"
import { DIY_CLIENT_TYPES, IDIYFulfilmentService } from "@curefit/diy-client"
import AppUtil from "../util/AppUtil"
const moment = require("moment")

@injectable()
class MealPlannerBusiness implements IMealPlannerBusiness {
  constructor(
    @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfService: IGMFClient,
    @inject(BASE_TYPES.ILogger) private logger: Logger,
    @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
    @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) private recommendationService: IRecommendationService,
    @inject(DIY_CLIENT_TYPES.DIYFulfilmentService) private diyService: IDIYFulfilmentService
  ) {
  }

  async getProducts(userContext: UserContext, startDate?: any): Promise<any> {
    // this.gmfService.checkExistsUserPlan(userContext.userProfile.userId)
    const activitiesPromise = this.getActivities(userContext, startDate)
    const userId = userContext.userProfile.userId
    const prescriptionsPromise = this.gmfService.searchDietPrescriptions({
      userId: userContext.userProfile.userId,
      statuses: [PrescriptionItemStatus.LIVE]
    })
    const userInfoPromise: Promise<UserMealPlanSummary> = this.gmfService.getUserPlanDetails(userContext.userProfile.userId)
    const [activities, prescriptions, userDetails] = await Promise.all(
      [
        Promise.resolve(activitiesPromise),
        Promise.resolve(prescriptionsPromise),
        Promise.resolve(userInfoPromise)
      ]
    )
    const foodRecoProductIds: any = []
    const selfRecoProductIds: any = []
    const diyRecoProductIds: any = []
    activities.forEach((activity: any) => {
      activity.recommendations.forEach((recommendation: any) => {
        recommendation.productIds.forEach((productId: any) => {
          if (recommendation.catalogueProductType === "FOOD_RECOMMENDATION") {
            foodRecoProductIds.push({
              productId,
              slot: activity.meta.food.slot
            })
          } else if (recommendation.catalogueProductType === "DISH") {
            selfRecoProductIds.push({
              productId,
              slot: activity.meta.food.slot
            })
          } else if (recommendation.catalogueProductType === "DIY_RECIPE") {
            diyRecoProductIds.push({
              productId,
              slot: activity.meta.food.slot
            })
          }
        })
      })
    })
    let eatProducts: any = await this.catalogueService.getProducts(selfRecoProductIds.map((productIdObj: any) => productIdObj.productId))
    this.logger.info("[MEAL PLANNER] eat products fetched")
    eatProducts = eatProducts.map((product: any) => {
      const productWithSlot: any = product
      const slot = selfRecoProductIds.find((productIdObj: any) => {
        return productIdObj.productId === product.productId
      }).slot
      productWithSlot.slot = slot
      productWithSlot.isVeg = product.attributes.isVeg === "TRUE"
      productWithSlot.image = UrlPathBuilder.getSingleImagePath(product.parentProductId ? product.parentProductId : product.productId, product.productType, "THUMBNAIL", product.imageVersion)
      const currentDate = moment().format("YYYY-MM-DD")
      const seoParams: SeoUrlParams = {
        productName: product.title
      }
      productWithSlot.action = {
        actionType: "WIDGET_NAVIGATION",
        url: ActionUtil.foodSingle(product.productId, currentDate, slot, undefined, undefined, userContext.sessionInfo.userAgent, seoParams)
      }
      return productWithSlot
    })
    let allProductsValues: any = []
    try {
      const selfProductsPromise = this.recommendationService.getMealsByIds(foodRecoProductIds.map((productIdObj: any) => productIdObj.productId))
      const diyProductsPromise = this.diyService.getDIYRecipeProducts(userId,
        diyRecoProductIds.map((diyRecipe: any) => diyRecipe.productId), AppUtil.getTenantFromUserContext(userContext)
      )
      allProductsValues = await Promise.all([Promise.resolve(selfProductsPromise), Promise.resolve(diyProductsPromise)])
    } catch (e) {
      this.logger.info(`[MEAL PLANNER] selfProducts, diys before: ${JSON.stringify(e)}`)
      console.log(e)
    }

    let [selfProducts, diyProducts] = allProductsValues

    selfProducts = selfProducts.map((product: any) => {
      const productWithSlot: any = product
      const slot = foodRecoProductIds.find((productIdObj: any) => {
        return productIdObj.productId === product.mealId
      }).slot
      productWithSlot.slot = slot
      return productWithSlot
    })


    diyProducts = diyProducts.map((product: any) => {
      const productWithSlot: any = product
      const slot = diyRecoProductIds.find((productIdObj: any) => {
        return productIdObj.productId === product.productId
      }).slot
      productWithSlot.slot = slot
      return productWithSlot
    })
    const slotWiseProducts: any = {
      userDetails,
      "BREAKFAST": {
        caloriesInfo: {},
        items: []
      },
      "LUNCH": {
        caloriesInfo: {},
        items: []
      },
      "SNACKS": {
        caloriesInfo: {},
        items: []
      },
      "DINNER": {
        caloriesInfo: {},
        items: []
      }
    }
    prescriptions[0].meals.forEach((meal: any) => {
      slotWiseProducts[meal.mealSlot].caloriesInfo.calories = meal.details.calories
      slotWiseProducts[meal.mealSlot].caloriesInfo.calorieBreakup = meal.details.calorieBreakup[0]
      slotWiseProducts[meal.mealSlot].caloriesInfo.recommendedTimeOfDay = meal.recommendedTimeOfDay
    })
    diyProducts.forEach((product: any) => {
      slotWiseProducts[product.slot].items.push(product)
    })
    selfProducts.forEach((product: any) => {
      slotWiseProducts[product.slot].items.push(product)
    })
    eatProducts.forEach((product: any) => {
      slotWiseProducts[product.slot].items.push(product)
    })
    return slotWiseProducts
  }

  async getActivities(userContext: UserContext, startDate?: any): Promise<any> {
    const startEpoch = startDate ? moment(new Date(startDate)).hour(0).minute(0).second(0).toDate().getTime() : moment().hour(0).minute(0).second(0).toDate().getTime()
    const endEpoch = startDate ? moment(new Date(startDate)).hour(23).minute(59).second(59).toDate().getTime() : moment().hour(23).minute(59).second(59).toDate().getTime()
    this.logger.info(`[MEAL PLANNER] startEpoch: ${startEpoch}`)
    const activities: any = await this.gmfService.getTodoActivities({
      userId: userContext.userProfile.userId,
      dateSearchEpochs: {
        startEpoch,
        endEpoch,
        numberOfDays: 1
      }
    })
    this.logger.info(`[MEAL PLANNER] activities fetched ${JSON.stringify(activities)}`)
    return activities
  }
}

export default MealPlannerBusiness
