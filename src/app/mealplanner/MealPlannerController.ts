import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IMultiCrudKeyValue, REDIS_TYPES } from "@curefit/redis-utils"
import { ICrudKeyValue } from "@curefit/redis-utils"
import { IRecommendationService, RECOMMENDATION_CLIENT_TYPES } from "@curefit/recommendation-client"
import IMealPlannerBusiness from "./IMealPlannerBusiness"
import AuthMiddleware from "../auth/AuthMiddleware"
import { GMF_CLIENT_TYPES, IGMFClient } from "@curefit/gmf-client"
import { UserContext } from "@curefit/vm-models"
import { UserPlanGenRequest, PlanType } from "@curefit/gmf-common"
import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"
import { BASE_TYPES, Logger } from "@curefit/base"

export function controllerFactory(kernel: Container) {
  @controller("/meal-planner", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
  class MealPlannerController {
    private crudDao: ICrudKeyValue
    constructor(
      @inject(GMF_CLIENT_TYPES.IGMFClient) private gmfService: IGMFClient,
      @inject(BASE_TYPES.ILogger) private logger: Logger,
      @inject(CUREFIT_API_TYPES.MealPlannerBusiness) private mealPlannerBusiness: IMealPlannerBusiness,
      @inject(REDIS_TYPES.MultiCrudKeyValueDao) private multiCrudKeyValueDao: IMultiCrudKeyValue,
      @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
      @inject(RECOMMENDATION_CLIENT_TYPES.IRecommendationService) private recommendationService: IRecommendationService
    ) {
      this.crudDao = multiCrudKeyValueDao.getICrudKeyValue("REDIS_CACHE")
    }

    @httpPost("/trigger-meal-plan")
    async triggerMealPlan(req: express.Request): Promise<any> {
      // get id from gmfClient
      const param: UserPlanGenRequest = {
        planType: <PlanType>req.body.planType,
        userInfo: req.body.userInfo,
        productFamilyInfo: req.body.productFamilyInfo
      }
      try {
        const aiPlanData = await this.gmfService.triggerAIPlan(param)
        this.logger.info(`[MEAL PLANNER] aiPlanData: ${JSON.stringify(aiPlanData)} ${JSON.stringify(param)}`)
        if (aiPlanData.success) {
          return await this.mealPlannerBusiness.getProducts(req.userContext)
        } else {
          this.logger.info("[MEAL PLANNER] unable to create plan")
          throw new Error("meal plan couldn't be created")
        }
      } catch (e) {
        this.logger.info(`[MEAL PLANNER] triggerAiPlan failed ${JSON.stringify(e)}`)
      }
    }

    @httpPost("/get-products")
    async getProductsForDay(req: express.Request): Promise<any> {
      return await this.mealPlannerBusiness.getProducts(req.userContext, req.body.startDate)
    }

    @httpPost("/cancel")
      async cancelMealPlan(req: express.Request): Promise<any> {
        return await this.gmfService.cancelUserPlan(req.userContext.userProfile.userId, `cancel-${req.userContext.userProfile.userId}`, "AUTO")
      }
  }

  return MealPlannerController
}

export default controllerFactory
