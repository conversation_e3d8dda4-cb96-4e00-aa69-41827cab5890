import { Container, inject, injectable } from "inversify"
import * as _ from "lodash"

import { CATALOG_CLIENT_TYPES, ICatalogueService } from "@curefit/catalog-client"

import {
    ConsultationBookingRoutes, DiagnosticBookingRoutes,
    IBreadCrumb,
    LiveDiyPageRoutes,
    RouteNames,
    VmPageRoutes,
    VmPageSectionRoutes
} from "@curefit/apps-common"
import { IBreadCrumbService, IMatchRouteResult } from "./interface"

import { SupportedRoutes, RouteDetailMapping } from "./constants"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { IServiceInterfaces } from "@curefit/vm-models"
import { ProductType } from "@curefit/product-common"
import { ActionUtilV1, CareUtil } from "@curefit/base-utils"
import { CACHE_CLIENT_TYPES, cacheKey, CacheService, wrapWithMethodCache } from "@curefit/cache-client"
import { TimeUtil } from "@curefit/util-common"
import { ConsultationProduct } from "@curefit/care-common"
import { BASE_TYPES, Logger } from "@curefit/base"

const urlParse = require("url-parse")
const UrlPattern = require("url-pattern")


export function BreadCrumbFactory(kernel: Container) {

    @injectable()
    class BreadCrumbService implements IBreadCrumbService {
        private homeBreadCrumb: IBreadCrumb
        private urlPatternObjectMap: Record<string, typeof UrlPattern>

        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(CATALOG_CLIENT_TYPES.CatalogServiceReader) private catalogueService: ICatalogueService,
            @inject(CUREFIT_API_TYPES.ServiceInterfaces) private serviceInterfaces: IServiceInterfaces
        ) {
            this.homeBreadCrumb = { text: "Home", link: "/"}
            this.urlPatternObjectMap = {}

            for (const routeKey of Object.keys(SupportedRoutes)) {
                this.urlPatternObjectMap[routeKey] = new UrlPattern(SupportedRoutes[routeKey])
            }
        }

        private getPathNameFromReferrer(referrer: string): string {
            return urlParse(referrer, true).pathname
        }

        /**
         * Filter matching path value and request URL params for given Referrer
         * @param referrer {string}: Complete URL requesting breadcrumbs [eg: /live/fitness/hrx-workout/FIT_SERIES_1/s]
         * @return {IMatchRouteResult} containing
         *   route {string}: Matching apps-common Route-name value. [eg: /live/fitness/:category/:seriesId/s]
         *   params {Record<string, string | undefined>}: Record containing URL params for the matching pathname. [eg: { seriesId: "FIT_SERIES_1, category: "hrx-workout" }]
         */
        private getMatchingPathFromSupportedRoutes(referrer: string): IMatchRouteResult | null {
            const pathname = this.getPathNameFromReferrer(referrer)
            let patternObject,
                matchResult

            if (!pathname) {
                return
            }

            for (const routeKey of Object.keys(SupportedRoutes)) {
                patternObject = this.urlPatternObjectMap[routeKey]

                if (!patternObject?.match) {
                    return null
                }

                matchResult = patternObject.match(pathname)

                if (matchResult !== null) {
                    return {
                        route: SupportedRoutes[routeKey],
                        params: matchResult
                    }
                }
            }

            return null
        }

        /**
         * Return breadcrumb name for pages with static breadcrumb value (CLP, Home, etc).
         * @param pathname: Pathname pointing to supported VM-pages and sub-tabs.
         */
        private getMappedRouteName(pathname: any): string {
            return _.get(RouteDetailMapping, `${pathname}.breadcrumbName`, "")
        }

        /**
         * Filter out sub-directory for given pathname.
         * @param pathname: Valid URL path value. [eg: /live/fitness]
         * @param directoryLevel: Number. Parent level to be filtered. Default 1.
         * @return parent pathname {string}. [eg: /live for /live/fitness]
         */
        private getDirectorFromPathname(pathname: string, directoryLevel: number = 1): string {
            const parentDirectory = pathname.substring(0, pathname.lastIndexOf("/"))

            if (directoryLevel === 1) {
                return parentDirectory
            }

            return this.getDirectorFromPathname(parentDirectory, directoryLevel - 1)
        }

        /**
         * Generate breadcrumbs for matching VM-Page path. Recursive call from clp sub-domain to Home page.
         * @param pathname: Pathname mapping from apps-common VM-Page route. [eg: /live/fitness]
         * @returns breadCrumbs {IBreadCrumb[]}: BreadCrumbs for VM-Page. [eg: Home > CLP (Cult/Care/Live) > CLP-Tab (Fitness/Diagnostic)]
         */
        private getBreadcrumbForVmPageRoute(pathname: string): IBreadCrumb[] {
            const mappedRouteName = this.getMappedRouteName(pathname),
                parentDirectory = this.getDirectorFromPathname(pathname)
            let baseBreadcrumbs: IBreadCrumb[]

            if (["/", ""].includes(pathname)) {
                return [this.homeBreadCrumb]
            }

            for (const routeName of Object.values({ ...VmPageRoutes, ...VmPageSectionRoutes })) {
                if (routeName === pathname) {
                    baseBreadcrumbs = this.getBreadcrumbForVmPageRoute(parentDirectory)

                    return [ ...baseBreadcrumbs, { text: mappedRouteName, link: pathname }]
                }
            }

            return []
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.HOUR)
        private async getBreadCrumbsForLiveSeriesPage (@cacheKey seriesId: string, @cacheKey productType: ProductType, pathname: string) {
            const diySeriesData = await this.serviceInterfaces.diyService.getDIYSeriesById("0", seriesId, productType),
                parentDirectory = this.getDirectorFromPathname(pathname, 3),
                baseBreadCrumbList = this.getBreadcrumbForVmPageRoute(parentDirectory),
                diyLink = ActionUtilV1.diySeriesPage(diySeriesData, "DESKTOP")

            return [ ...baseBreadCrumbList, { text: diySeriesData.name, link: diyLink }]
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.HOUR)
        private async getBreadCrumbsForLiveSubCategoryPage (@cacheKey packId: string, @cacheKey category: string, @cacheKey productType: ProductType, pathname: string) {
            const diyPackPromise = productType === "DIY_FITNESS" ? this.catalogueService.getDIYFitnessPack(packId) : this.catalogueService.getDIYMeditationPack(packId)

            let diyPack,
                diySeries,
                parentSeriesBreadCrumbs,
                parentDirectory: string = this.getDirectorFromPathname(pathname)

            try {
                diySeries = await this.serviceInterfaces.diyService.getDIYSeriesByCategory(category, productType)
            } catch (err) {
                this.logger.error(err)
            }

            if (diySeries) {
                ([ diyPack, parentSeriesBreadCrumbs ] = await Promise.all([
                    diyPackPromise,
                    this.getBreadCrumbsForLiveSeriesPage(diySeries.seriesId, productType, parentDirectory)
                ]))
            } else {
                parentDirectory = this.getDirectorFromPathname(pathname, 4)

                diyPack = await diyPackPromise
                parentSeriesBreadCrumbs = this.getBreadcrumbForVmPageRoute(parentDirectory)
            }

            return [ ...parentSeriesBreadCrumbs, { text: diyPack.title, link: ActionUtilV1.diyPackProductPage(diyPack, "DESKTOP") } ]
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.HOUR)
        private async getDoctorListingPageBreadCrumbs (@cacheKey productSlug: string, pathname: string): Promise<IBreadCrumb[]> {
            const parentDirectory = pathname.includes(RouteNames.Mind) ? RouteNames.MindTherapy : this.getDirectorFromPathname(pathname),
                baseBreadCrumbList = this.getBreadcrumbForVmPageRoute(parentDirectory),
                consultationProduct = await this.serviceInterfaces.healthfaceService.getConsultationProductDetailsByUrlPath(productSlug)
            let productName,
                consultationWordIndex

            if (!consultationProduct) {
                return []
            }

            productName = (consultationProduct.title || consultationProduct.name)
            consultationWordIndex = productName.lastIndexOf("Consultation")

            // Remove "Consultation" from the end of product
            if (consultationWordIndex !== -1 && (consultationWordIndex + "Consultation".length === productName.length)) {
                productName = productName.substr(0, consultationWordIndex).trim()
            }

            return [ ...baseBreadCrumbList, { text: productName, link: CareUtil.getDoctorListingWebRoute(consultationProduct) }]
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.HOUR)
        private async getDoctorBookingPageBreadCrumbs (@cacheKey doctorSlug: string, pathname: string): Promise<IBreadCrumb[]> {
            const doctorSlugData = await this.serviceInterfaces.healthfaceService.getAllDoctorDataBySlugName(doctorSlug)
            const slugConsultationProduct: any = doctorSlugData ? doctorSlugData.consultationProducts.find(({ doctorType, consultationMode }) => (
                doctorType === doctorSlugData.primarySubServiceType.code && consultationMode === "ONLINE")) || doctorSlugData.consultationProducts[0] : {}

            let consultationProduct: ConsultationProduct,
                baseBreadCrumbList: IBreadCrumb[] = [],
                parentDirectory: string

            if (!doctorSlugData) {
                return []
            }

            consultationProduct = <ConsultationProduct>await this.catalogueService.getProduct(slugConsultationProduct.productCode)

            if (consultationProduct && consultationProduct?.urlPath) {
                parentDirectory = (pathname.includes(RouteNames.Mind) ? ConsultationBookingRoutes.MindDoctorListing : ConsultationBookingRoutes.CareDoctorListing).replace(":productSlug", consultationProduct.urlPath)
                baseBreadCrumbList = await this.getDoctorListingPageBreadCrumbs(consultationProduct.urlPath, parentDirectory)
            } else {
                parentDirectory = pathname.includes(RouteNames.Mind) ? VmPageSectionRoutes.MindTherapy : VmPageSectionRoutes.CareDoctorConsultation
                baseBreadCrumbList = this.getBreadcrumbForVmPageRoute(parentDirectory)
            }

            return [ ...baseBreadCrumbList, { text: doctorSlugData.name, link: pathname.replace(":slugValue", doctorSlug) }]
        }

        @wrapWithMethodCache(kernel.get<CacheService>(CACHE_CLIENT_TYPES.RedisCacheService), 1 * TimeUtil.TIME_IN_SECONDS.HOUR)
        private async getDiagnosticProductBreadCrumbs (@cacheKey slugValue: string, pathname: string): Promise<IBreadCrumb[]> {
            const diagnosticProduct = await this.serviceInterfaces.diagnosticService.getDiagnosticProductDetailFromSlug(slugValue),
                parentDirectory = this.getDirectorFromPathname(pathname),
                baseBreadCrumbList = this.getBreadcrumbForVmPageRoute(parentDirectory)

            if (!diagnosticProduct || !diagnosticProduct.product) {
                return []
            }

            return [ ...baseBreadCrumbList, { text: diagnosticProduct.product.name, link: ActionUtilV1.diagnosticTestProductPage(slugValue) } ]
        }

        /**
         * Generate breadcrumbs for the provided product page's pathname
         * @param pathname {string}: Pathname from apps-common's Route-name mapping [eg: /live/fitness/:category/:seriesId/s]
         * @param matchParams {Record<string, string | undefined>}: Record containing URL params for the matching pathname. [eg: { seriesId: "FIT_SERIES_1, category: "hrx-workout" }]
         * @returns breadCrumbs {IBreadCrumb[]} from @curefit/apps-common
         */
        private async getBreadCrumbsForProductPages(pathname: string, matchParams: Record<string, string>): Promise<IBreadCrumb[]> {
            switch (pathname) {
                case LiveDiyPageRoutes.LiveMindFulnessSeriesPage:
                    return this.getBreadCrumbsForLiveSeriesPage(matchParams.seriesId, "DIY_MEDITATION", pathname)
                case LiveDiyPageRoutes.LiveFitnessSeriesPage:
                    return this.getBreadCrumbsForLiveSeriesPage(matchParams.seriesId, "DIY_FITNESS", pathname)
                case LiveDiyPageRoutes.LiveFitnessPackPage:
                    return this.getBreadCrumbsForLiveSubCategoryPage(matchParams.packId, matchParams.category, "DIY_FITNESS", pathname)
                case LiveDiyPageRoutes.LiveMindFulnessPackPage:
                    return this.getBreadCrumbsForLiveSubCategoryPage(matchParams.packId, matchParams.category, "DIY_MEDITATION", pathname)
                case ConsultationBookingRoutes.CareDoctorListing:
                case ConsultationBookingRoutes.MindDoctorListing:
                    return this.getDoctorListingPageBreadCrumbs(matchParams.productSlug, pathname)
                case ConsultationBookingRoutes.CareDoctorBookingByName:
                    return this.getDoctorBookingPageBreadCrumbs(matchParams.slugValue, pathname)
                case DiagnosticBookingRoutes.CareDiagnosticProductPage:
                    return this.getDiagnosticProductBreadCrumbs(matchParams.slugValue, pathname)
                default:
                    return []
            }
        }

        /**
         * Generate breadcrumbs for the given web-page url
         * @param referrer {string}: Complete URL requesting breadcrumbs [eg: /live/fitness/hrx-workout/FIT_SERIES_1/s]
         * @returns breadCrumbs {IBreadCrumb[]} from @curefit/apps-common
         */
        public async getBreadcrumbForRoute(referrer: string): Promise<IBreadCrumb[]> {
            const matchResult = this.getMatchingPathFromSupportedRoutes(referrer),
                isVmPageRoute = Object.values(VmPageRoutes).includes(matchResult?.route as VmPageRoutes)
                    || Object.values(VmPageSectionRoutes).includes(matchResult?.route as VmPageSectionRoutes)

            let breadCrumbs: IBreadCrumb[] = []

            if (!matchResult || !matchResult.route) {
                return null
            }

            try {
                breadCrumbs = isVmPageRoute ? this.getBreadcrumbForVmPageRoute(matchResult.route) :
                    await this.getBreadCrumbsForProductPages(matchResult.route, matchResult.params)
            } catch (err) {
                this.logger.error(err)
            }

            return breadCrumbs
        }
    }

    return BreadCrumbService
}
