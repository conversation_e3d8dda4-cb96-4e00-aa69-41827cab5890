import {
    IBreadCrumb,
    VmPageSectionRoutes,
    VmPageRoutes,
    LiveDiyPageRoutes,
    ConsultationBookingRoutes,
    DiagnosticBookingRoutes
} from "@curefit/apps-common"

export interface IBreadCrumbService {
    getBreadcrumbForRoute: (referrer: string) => Promise<IBreadCrumb[]>
}

export interface IMatchRouteResult {
    route: VmPageSectionRoutes | VmPageRoutes | LiveDiyPageRoutes | ConsultationBookingRoutes | DiagnosticBookingRoutes
    params: Record<string, string> | undefined
}
