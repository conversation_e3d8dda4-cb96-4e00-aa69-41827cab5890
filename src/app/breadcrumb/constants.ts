import {
    VmPageSectionRoutes,
    VmPageRoutes,
    LiveDiyPageRoutes,
    ConsultationBookingRoutes,
    DiagnosticBookingRoutes
} from "@curefit/apps-common"

export const SupportedRoutes: Record<string, VmPageSectionRoutes | VmPageRoutes | LiveDiyPageRoutes | ConsultationBookingRoutes | DiagnosticBookingRoutes> = {
    ...VmPageSectionRoutes,
    ...VmPageRoutes,
    ...LiveDiyPageRoutes,
    ...ConsultationBookingRoutes,
    ...DiagnosticBookingRoutes
}

type IRouteDetailMapping = {
    [routeName in VmPageRoutes | VmPageSectionRoutes]: { breadcrumbName: string }
}

export const RouteDetailMapping: IRouteDetailMapping  = {
    [VmPageRoutes.Livefit]: { breadcrumbName: "Live" },
    [VmPageRoutes.Eat]: { breadcrumbName: "Eat" },
    [VmPageRoutes.Cult]: { breadcrumbName: "Cult" },
    [VmPageRoutes.Mind]: { breadcrumbName: "Mind" },
    [VmPageRoutes.Care]: { breadcrumbName: "Care" },
    [VmPageRoutes.Store]: { breadcrumbName: "Fit Store" },
    [VmPageRoutes.Fitness]: { breadcrumbName: "Fitness" },

    [VmPageSectionRoutes.EatOrderNow]: { breadcrumbName: "Order Now" },
    [VmPageSectionRoutes.EatNow]: { breadcrumbName: "Eat Now" },
    [VmPageSectionRoutes.EatLater]: { breadcrumbName: "Eat Later" },
    [VmPageSectionRoutes.EatSubscribe]: { breadcrumbName: "Eat Subscribe" },

    [VmPageSectionRoutes.CultGym]: { breadcrumbName: "Cult Centers" },
    [VmPageSectionRoutes.CultDIY]: { breadcrumbName: "Cult DIY" },
    [VmPageSectionRoutes.CultOtherGyms]: { breadcrumbName: "Cult Premium Gyms" },
    [VmPageSectionRoutes.CultEvents]: { breadcrumbName: "Cult Events" },
    [VmPageSectionRoutes.CultGear]: { breadcrumbName: "Cult Gear" },
    [VmPageSectionRoutes.CultLivePT]: { breadcrumbName: "Online PT"},
    [VmPageSectionRoutes.LiveSGT]: {  breadcrumbName: "Fitness Classes" },

    [VmPageSectionRoutes.WholefitCategory]: { breadcrumbName: "Wholefit Category" },

    [VmPageSectionRoutes.LiveCult]: { breadcrumbName: "Cult Live" },
    [VmPageSectionRoutes.LiveMind]: { breadcrumbName: "Meditation Live" },
    [VmPageSectionRoutes.MindLive]: { breadcrumbName: "Mind Live" },
    [VmPageSectionRoutes.CareLive]: { breadcrumbName: "Care Live" },
    [VmPageSectionRoutes.CultLive]: { breadcrumbName: "Fitness" },
    [VmPageSectionRoutes.LiveFitness]: { breadcrumbName: "Fitness" },
    [VmPageSectionRoutes.LiveMindfulness]: { breadcrumbName: "Mindfulness" },

    [VmPageSectionRoutes.MindGym]: { breadcrumbName: "Mind Center" },
    [VmPageSectionRoutes.MindDIY]: { breadcrumbName: "Mind DIY" },
    [VmPageSectionRoutes.MindTherapy]: { breadcrumbName: "Therapy" },
    [VmPageSectionRoutes.MindEvents]: { breadcrumbName: "Mind Events" },
    [VmPageSectionRoutes.MindLivefit]: { breadcrumbName: "Mind Livefit" },

    [VmPageSectionRoutes.CareDoctorConsultation]: { breadcrumbName: "Doctor Consultation" },
    [VmPageSectionRoutes.CareDiagnosticTests]: { breadcrumbName: "Diagnostic Tests" },
    [VmPageSectionRoutes.CareConsult]: { breadcrumbName: "Consultation" },
    [VmPageSectionRoutes.CareCheckup]: { breadcrumbName: "Checkup" },
    [VmPageSectionRoutes.CareSkinHair]: { breadcrumbName: "Skin And Hair" },

    [VmPageSectionRoutes.StoreGear]: { breadcrumbName: "Cult Gear" },
    [VmPageSectionRoutes.LiveRecipie]: { breadcrumbName: "Live Recipe" },
    [VmPageSectionRoutes.CareDietician]: { breadcrumbName: "Dietician" },

    [VmPageSectionRoutes.CultPassElite]: { breadcrumbName: "cultpass Elite" },
    [VmPageSectionRoutes.CultPassPro]: { breadcrumbName: "cultpass Pro" },
    [VmPageSectionRoutes.CultPassLive]: { breadcrumbName: "cultpass HOME" },
    [VmPageSectionRoutes.CultTransform]: { breadcrumbName: "cult Transform" }
}
