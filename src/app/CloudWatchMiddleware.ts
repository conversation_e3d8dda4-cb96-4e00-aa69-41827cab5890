import { inject, injectable } from "inversify"
import * as express from "express"
import { CloudWatchService, CLOUDWATCH_CLIENT_TYPES } from "@curefit/cloudwatch-client"


@injectable()
class CloudWatchMiddleware {
    private namespace: string
    constructor( @inject(CLOUDWATCH_CLIENT_TYPES.CloudWatchService) private cloudWatchService: CloudWatchService) {
        this.setCloudWatch = this.setCloudWatch.bind(this)
    }

    public setCloudWatch(req: express.Request, res: express.Response, next: express.NextFunction): void {
        next()
    }

    public setCloudWatchLegacy(req: express.Request, res: express.Response, next: express.NextFunction): void {
        if (process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "STAGE") {
            const start = new Date().getTime()
            this.namespace = "CF_API"
            const dimensions = [{ Name: "Environment", Value: process.env.ENVIRONMENT }]
            let metricName = req.originalUrl.replace(/\//g, ".")
            metricName = metricName.replace(/\.\./g, ".")
            if (metricName.startsWith("\.")) {
                metricName = metricName.substring(1, metricName.length)
            }
            metricName = metricName.split("?")[0]
            const topLevelMetricName = metricName.split("\.")[0]
            res.on("finish", () => {
                const end = new Date().getTime()
                const diff = end - start
                this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: metricName + ".latency", Value: diff, Unit: "Milliseconds" })
                this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: topLevelMetricName + ".latency", Value: diff, Unit: "Milliseconds" })
                if (res.statusCode === 200) {
                    this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: metricName + ".count.success", Value: 1, Unit: "Count" })
                    this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: topLevelMetricName + ".count.success", Value: 1, Unit: "Count" })
                } else {
                    this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: metricName + ".count.failure", Value: 1, Unit: "Count" })
                    this.cloudWatchService.pushMetric({ Namespace: this.namespace, Dimensions: dimensions, MetricName: topLevelMetricName + ".count.failure", Value: 1, Unit: "Count" })
                }
            })
        }
        next()
    }
}

export default CloudWatchMiddleware
