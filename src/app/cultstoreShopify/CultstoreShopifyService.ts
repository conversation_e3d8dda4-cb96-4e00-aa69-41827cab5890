import { inject, injectable } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { ICultstoreShopifyConf, ICultstoreShopifyService, ICultstoreShopifyUser, IMystiqueServiceConf, ITesseractServiceConf } from "./CultstoreShopifyInterfaces"
import { BASE_TYPES, FetchUtilV2, Logger } from "@curefit/base"
import * as express from "express"
import { OAuth2Client } from "google-auth-library"
import { User } from "@curefit/user-common"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import fetch from "node-fetch"


@injectable()
class CultstoreShopifyService implements ICultstoreShopifyService {
    constructor(
        @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
        @inject(CUREFIT_API_TYPES.TesseractServiceConf) private tesseractServiceConf: ITesseractServiceConf,
        @inject(CUREFIT_API_TYPES.MystiqueServiceConf) private mystiqueServiceConf: IMystiqueServiceConf,
        @inject(CUREFIT_API_TYPES.CultstoreShopifyConf) private cultstoreShopifyConf: ICultstoreShopifyConf,
        @inject(BASE_TYPES.FetchUtilV2) protected fetchHelper: FetchUtilV2,
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {
    }
    public async getShopifyMultipassUrlForGoogleLogin(cfUserId: string): Promise<string> {
        const url = this.tesseractServiceConf.url + "/google/oauth/callback/multipass?cfUserId=" + cfUserId
        const headers = {
            "api-key": this.tesseractServiceConf.apiKey,
        }
        try {
            const response = await fetch(url, this.fetchHelper.get({
                headers
            }))
            const responseJson = await response.json()
            return responseJson?.data?.url
        } catch (e) {
            this.logger.error("getShopifyMultipassUrlForGoogleLogin service error cfUserId = " + cfUserId + " with error = " + e.message)
        }
    }

    getOAuth2ClientInstance(req: express.Request): OAuth2Client {
        const clientId = this.cultstoreShopifyConf.googleOAuth2.clientId
        const googleClientSecret = this.cultstoreShopifyConf.googleOAuth2.clientSecret
        const baseUrl = "https://" + req.hostname
        const redirectUrl = baseUrl + "/api/shopify/googleOAuth/callback"
        return new OAuth2Client(clientId, googleClientSecret, redirectUrl)
    }
    convertUserToCultstoreShopifyUser(user: User): ICultstoreShopifyUser {
        return {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            countryCallingCode: user.countryCallingCode,
            phone: user.phone,
            profilePictureUrl: user.profilePictureUrl,
            birthday: user.birthday,
            gender: user.gender,
            googleUserId: user.googleUserId,
            isInternalUser: user.isInternalUser,
            workEmail: user.workEmail
        }
    }
    async getShopifyUrlWithMultipassToken(cfUserId: string, shopifyRedirectUrl: string): Promise<string> {
        const url = this.tesseractServiceConf.url + "/cult/multipass"
        if (cfUserId == null || cfUserId == "0") {
            return shopifyRedirectUrl
        }
        const user = await this.userService.getUser(cfUserId)
        const cfUserDetails: ICultstoreShopifyUser = {
            "id": user.id,
            "firstName": user.firstName,
            "lastName": user.lastName,
            "birthday": user.birthday,
            "gender": user.gender,
            "phone": user.phone,
            "email": user.email,
            "workEmail": user.workEmail,
            "countryCallingCode": user.countryCallingCode,
            "profilePictureUrl": user.profilePictureUrl,
            "googleUserId": user.googleUserId,
            "isInternalUser": user.isInternalUser
        }
        const body: any = {
            "cfUserId": user.id,
            cfUserDetails,
            "return_to": shopifyRedirectUrl
        }
        const headers = {
            "api-key": this.tesseractServiceConf.apiKey,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }
        try {
            const response: any = await fetch(url, this.fetchHelper.post({
                body,
                headers
            }))
            const parsedResponse = await this.fetchHelper.parseResponse<any>(response)
            return parsedResponse?.data?.multipassUrl
        } catch (e) {
            this.logger.error("getShopifyMultipassAction service error cfUserId = " + cfUserId + " with error = " + e.message)
        }
    }

    async getMystiqueOrderItemsForCfUserId(cfUserId: string, pageNumber: number, limit: number): Promise<any> {
        const url = this.mystiqueServiceConf.url + "/orderLedger/itemsList?cultUserId=" + cfUserId + "&pageNumber=" + pageNumber + "&limit=" + limit
        try {
            const response = await fetch(url, this.fetchHelper.get())
            return this.fetchHelper.parseResponse<any>(response)
        } catch (e) {
            this.logger.error("getMystiqueOrderItemsForCfUserId service error cfUserId = " + cfUserId + " with error = " + e.message)
        }
    }

}
export default CultstoreShopifyService