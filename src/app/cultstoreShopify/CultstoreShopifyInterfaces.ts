import { UserUpdateRequest } from "@curefit/user-client"
import { Gender, User } from "@curefit/user-common"
import * as express from "express"
import { OAuth2Client } from "google-auth-library"

export interface ICultstoreShopifyUser {
    id: string
    firstName: string
    lastName: string
    email: string
    countryCallingCode?: string
    phone: string
    profilePictureUrl: string
    birthday: string
    gender: Gender
    googleUserId?: string
    isInternalUser: boolean
    workEmail: string
}
export interface ICultstoreShopifyUserUpdateRequest extends UserUpdateRequest {
    profilePictureUrl?: string
}
export interface ICultstoreShopifyConf {
    googleOAuth2: {
        clientId: string
        clientSecret: string
    }
    truecaller: {
        appKey: string
        partnerName: string
        lang: string
        defaultUiConfig: ITruecallerUiConfig
        uiConfigMap?: Map<String, ITruecallerUiConfig>
    }
}
export interface ITruecallerUiConfig {
    loginPrefix: string
    loginSuffix: string
    ctaPrefix: string
    ctaColor: string
    ctaTextColor: string
    btnShape: string
    skipOption: string
}
export interface ITruecallerDeeplinkResponse {
    deepLink: string
    requestId: string
}
export interface ITesseractServiceConf {
    url: string,
    apiKey: string
}
export interface IMystiqueServiceConf {
    url: string
}
export interface ICultstoreShopifyService {
    getShopifyMultipassUrlForGoogleLogin(cfUserId: string): Promise<string>
    getOAuth2ClientInstance(req: express.Request): OAuth2Client
    convertUserToCultstoreShopifyUser(user: User): ICultstoreShopifyUser
    getShopifyUrlWithMultipassToken(cfUserId: string, shopifyRedirectUrl: string): Promise<string>
    getMystiqueOrderItemsForCfUserId(cfUserId: string, pageNumber: number, limit: number): Promise<any>
}