import * as express from "express"
import { Action } from "../common/views/WidgetView"
import { Container, inject } from "inversify"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { IUserService, USER_CLIENT_TYPES } from "@curefit/user-client"
import AuthMiddleware from "../auth/AuthMiddleware"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import AppUtil from "../util/AppUtil"
import IAuthBusiness from "../auth/IAuthBusiness"
import { AppTenant, UserAgentType } from "@curefit/base-common"
import UserNameValidator from "../common/validator/UserNameValidator"
import GenderValidator from "../common/validator/GenderValidator"
import DateValidator from "../common/validator/DateValidator"
import { ICultstoreShopifyConf, ICultstoreShopifyService, ICultstoreShopifyUser, ICultstoreShopifyUserUpdateRequest, ITesseractServiceConf, ITruecallerDeeplinkResponse } from "./CultstoreShopifyInterfaces"
import uuid = require("uuid")
import { ErrorFactory } from "@curefit/error-client"
import _ = require("lodash")
import { User } from "@curefit/user-common"
import { ISessionBusiness } from "@curefit/base-utils"
import { ISSOBusiness } from "../sso/ISSOBusiness"
import { OTP_MEDIUM } from "../user/UserController"
import AuthUtil from "../util/AuthUtil"


export function controllerFactory(kernel: Container) {
    @controller("/shopify",
        kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateCultstoreShopifySession
        )
    class CultstoreShopifyController {
        constructor(
            @inject(USER_CLIENT_TYPES.IUserService) private userService: IUserService,
            @inject(CUREFIT_API_TYPES.AuthBusiness) private authBusiness: IAuthBusiness,
            @inject(CUREFIT_API_TYPES.SessionService) private sessionBusiness: ISessionBusiness,
            @inject(CUREFIT_API_TYPES.SSOBusiness) private ssoBusiness: ISSOBusiness,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
            @inject(CUREFIT_API_TYPES.CultstoreShopifyConf) private cultstoreShopifyConf: ICultstoreShopifyConf,
            @inject(CUREFIT_API_TYPES.TesseractServiceConf) private tesseractServiceConf: ITesseractServiceConf,
            @inject(CUREFIT_API_TYPES.CultstoreShopifyService) private cultstoreShopifyService: ICultstoreShopifyService,
            @inject(BASE_TYPES.ILogger) private logger: Logger
        ) {
        }
        @httpPost("/getUserForEmail")
        async getUserForEmail(req: express.Request): Promise<ICultstoreShopifyUser> {
            const emailId = req.body.emailId
            const user = await this.userService.getUserUsingEmail(emailId)
            return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        }
        @httpPost("/getUserForPhone")
        async getUserForPhone(req: express.Request): Promise<ICultstoreShopifyUser> {
            const phoneWithCountryCallingCode = req.body.phoneWithCountryCallingCode
            const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const user = await this.userService.getUserUsingPhone(phoneWithCountryCallingCode, appTenant)
            return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        }
        @httpGet("/getUserForCfUserId/:cfUserId")
        async getUserForCfUserId(req: express.Request): Promise<ICultstoreShopifyUser> {
            const cfUserId = req.params.cfUserId
            const user = await this.userService.getUser(cfUserId)
            return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        }
        @httpPost("/updateUserProfile/:cfUserId")
        async updateUserProfile(req: express.Request, res: express.Response): Promise<ICultstoreShopifyUser> {
            const cfUserId = req.params.cfUserId
            this.logger.info("updateUserProfile cfUserId = " + cfUserId + " with requestBody = " + req.body)
            const userProfile: ICultstoreShopifyUserUpdateRequest = req.body
            if (cfUserId !== userProfile?.userId) {
                res.status(401).send({ status: "failure", message: "Please login to continue," })
            }

            let dobValidator, genderValidatorPromise, userNameValidatorPromise
            const promises = []
            const {firstName, lastName, gender, birthday, profilePictureUrl} = userProfile
            if (firstName && lastName) {
                userNameValidatorPromise = new UserNameValidator().validateFullName(firstName, lastName)
                promises.push(userNameValidatorPromise)
            } else if (firstName) {
                userNameValidatorPromise = new UserNameValidator().validate(firstName, "First Name")
                promises.push(userNameValidatorPromise)
            } else if (lastName) {
                userNameValidatorPromise = new UserNameValidator().validate(lastName, "Last Name")
                promises.push(userNameValidatorPromise)
            }
            if (gender) {
                genderValidatorPromise = new GenderValidator().validate(gender)
                promises.push(genderValidatorPromise)
            }
            if (birthday) {
                dobValidator = new DateValidator().validate(birthday)
                promises.push(dobValidator)
            }
            const user: User = await Promise.all(promises).then(() => this.userService.updateUserProfile(userProfile)).catch((ex) => {
                this.logger.error("updateUserProfile from cultstoreShopify failed with error = ", ex)
                throw ex
            })
            // if (profilePictureUrl) {
            //     user = await this.userService.uploadProfilePicture(cfUserId, profilePictureUrl)
            // }
            return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        }


        // @httpPost("/setEmail/:cfUserId")
        // setEmail(req: express.Request): Promise<ICultstoreShopifyUser> {
        //     const cfUserId = req.params.cfUserId
        //     const newEmail = req.body.email
        //     return this.authBusiness.setEmail(cfUserId, newEmail).then(user => {
        //         return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        //     })
        // }

        // @httpPost("/setWorkEmail/:cfUserId")
        // setWorkEmail(req: express.Request): Promise<ICultstoreShopifyUser> {
        //     const cfUserId = req.params.cfUserId
        //     const newEmail = req.body.email
        //     return this.authBusiness.setWorkEmail(cfUserId, newEmail).then(user => {
        //         return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        //     })
        // }

        @httpPost("/setPhoneNumber/:cfUserId")
        async setPhoneNumber(req: express.Request, res: express.Response): Promise<any> {
            const cfUserId = req.params.cfUserId
            const newPhoneNumber: number = req.body.phoneNumber
            const medium: OTP_MEDIUM = req.body.medium || "sms"
            const appVersion: number = req.headers["appversion"] as string ? Number(req.headers["appversion"] as string) : undefined
            const osName: string = req.headers["osname"] as string
            const userAgent: UserAgentType = AuthUtil.getUserAgent(req)
            const captchaResponse = req.body.captchaResponse
            const countryCallingCode: string = req.body.countryCallingCode || "+91"
            const userContext = req?.userContext
            const apiKey = AuthUtil.getApiKeyFromReq(req)
            this.logger.info(`setPhoneNumber for cfUserId: ${cfUserId} with newPhoneNumber: ${newPhoneNumber} and userAgent: ${userAgent}`)
            await this.userService.getUser(cfUserId)
            return this.authBusiness.setPhoneNumber(res, cfUserId, newPhoneNumber, medium, appVersion,
                osName, userAgent, countryCallingCode, null, captchaResponse, userContext, apiKey).then(user => {
                return {
                    success: true
                }
            }).catch((ex) => {
                this.logger.error("setPhoneNumber from cultstoreShopify failed with error = " + ex.message)
                return {success: false, error: ex.message}
            })
        }
        @httpPost("/verifyPhoneNumber/:cfUserId")
        async verifyPhoneNumber(req: express.Request): Promise<{user: ICultstoreShopifyUser}> {
            const cfUserId = req.params.cfUserId
            const otp: string = req.body.otp
            const cultUser = await this.userService.verifyPhoneOtp(cfUserId, otp)
            return {
                user: this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(cultUser?.user)
            }
        }

        @httpGet("/googleOAuth/callback")
        async googleOAuthCallback(req: express.Request, res: express.Response): Promise<void> {
            const code: string = req.query.code
            const tenant: string = AppUtil.getTenantFromReq(req)
            const oAuth2Client = this.cultstoreShopifyService.getOAuth2ClientInstance(req)
            const { tokens } = await oAuth2Client.getToken(code)
            const user = await this.userService.googleLogin(tokens.id_token, tokens.access_token, undefined, tenant)
            const url = await this.cultstoreShopifyService.getShopifyMultipassUrlForGoogleLogin(user.id)
            res.redirect(301, url)
        }
        @httpGet("/googleOAuth/generateUrl")
        async generateGoogleOAuthUrl(req: express.Request, res: express.Response) {
            try {
                const oAuth2Client = this.cultstoreShopifyService.getOAuth2ClientInstance(req)
                const options = {
                    access_type: "offline",
                    scope: ["https://www.googleapis.com/auth/userinfo.profile", "https://www.googleapis.com/auth/userinfo.email"],
                }
                return oAuth2Client.generateAuthUrl(options)
            } catch (error) {
                this.logger.error(error)
                throw error
            }
        }

        @httpGet("/generateTruecallerDeepLink")
        async generateTruecallerDeepLink(req: express.Request, res: express.Response): Promise<ITruecallerDeeplinkResponse> {
            try {
                const requestId = uuid.v4().toString()
                const truecallerConfig = this.cultstoreShopifyConf.truecaller
                const uiConfig = truecallerConfig.defaultUiConfig
                let deepLink = "truecallersdk://truesdk/web_verify?type=btmsheet"
                deepLink += `&requestNonce=${requestId}`
                deepLink += `&partnerKey=${truecallerConfig.appKey}`
                deepLink += `&partnerName=${truecallerConfig.partnerName}`
                deepLink += `&lang=${truecallerConfig.lang}`
                deepLink += `&loginPrefix=${uiConfig.loginPrefix}`
                deepLink += `&loginSuffix=${uiConfig.loginSuffix}`
                deepLink += `&ctaPrefix=${uiConfig.ctaPrefix}`
                deepLink += `&ctaColor=${uiConfig.ctaColor}`
                deepLink += `&ctaTextColor=${uiConfig.ctaTextColor}`
                deepLink += `&btnShape=${uiConfig.btnShape}`
                deepLink += `&skipOption=${uiConfig.skipOption}`
                return {deepLink, requestId}
            } catch (error) {
                this.logger.error(error)
                throw error
            }
        }

        // web will poll to get status of login
        @httpPost("/truecallerLoginPolling")
        async truecallerLoginPolling(req: express.Request, res: express.Response): Promise<ICultstoreShopifyUser> {
            const appTenant: AppTenant = AppUtil.getAppTenantFromReq(req)
            const requestId = req.body.requestId
            const user = await this.authBusiness.truecallerLoginMWebPolling(requestId, appTenant)
            return this.cultstoreShopifyService.convertUserToCultstoreShopifyUser(user)
        }

        @httpGet("/getMystiqueOrderItemsForCfUserId/:cfUserId")
        async getMystiqueOrderItemsForCfUserId(req: express.Request, res: express.Response): Promise<any> {
             const cfUserId = req.params.cfUserId, pageNumber = (req.query.pageNumber && parseInt(req.query.pageNumber)) || 0, limit = (req.query.limit && parseInt(req.query.limit)) || 5
            return this.cultstoreShopifyService.getMystiqueOrderItemsForCfUserId(cfUserId, pageNumber, limit)
        }

        // Note:: To expire after 100% rollout of shopify and expiry of cultsport.com
        @httpPost("/createSessionAndGetSsoToken/:cfUserId")
        async createSession(req: express.Request, res: express.Response, ): Promise<string> {
            const deviceId: string = req.headers["deviceid"] as string || uuid.v4().toString()
            const cfUserId = req.params.cfUserId
            await this.userService.getUser(cfUserId)
            const session = await this.sessionBusiness.createSession(deviceId, cfUserId, false, {})
            const ssoToken = await this.ssoBusiness.createSession(cfUserId, session, req.body.url)
            return ssoToken.token
        }
    }
}
export default controllerFactory