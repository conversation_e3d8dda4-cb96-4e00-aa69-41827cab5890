import * as express from "express"
import { controller, httpGet, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import AuthMiddleware from "../auth/AuthMiddleware"
import { BASE_TYPES, Logger } from "@curefit/base"
import { Session, UserContext } from "@curefit/userinfo-common"
import { ChallengeInviteDetail, Metric } from "@curefit/maximus-common"
import { Action, WidgetView } from "../common/views/WidgetView"
import { ChallengeVM, IChallengeDetail, IInvite, IInviteWidget, SocialColors } from "../common/views/Social"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import { BasicUserActivityInfo } from "@curefit/quest-common"
import { ChallengeImageTypes, UrlPathBuilder } from "@curefit/product-common"
import { <PERSON>acheHelper } from "../../app/util/CacheHelper"
import { TimeUtil } from "@curefit/util-common"
import { _getJoinChallengeAction } from "./Helper"
import { MaximusService } from "./MaximusService"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"


// const CHALLENGE_COMPLETION_LOST = "Better Luck Next Time"
// removing as a temp hack as we don't want to show this message for cult score final challenge created manually

export function MaximusControllerFactory(kernel: Container) {

    @controller("/maximus", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
    class MaximusController {
        constructor(
            @inject(BASE_TYPES.ILogger) private logger: Logger,
            @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
            @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
            @inject(CUREFIT_API_TYPES.MaximusService) private commonService: MaximusService,
            @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
        ) {
        }

        @httpGet("/challenge/:challengeId")
        async getNewLevels(req: express.Request): Promise<any> {
            return {
                title: "Sorry, the challenge is not visible to you.",
                subTitle: "Please update your app."
            }
        }

        @httpGet("/browsechallenge")
        async getChallengesList(req: express.Request): Promise<{ challenges: Array<ChallengeVM> }> {
            return {
                challenges: []
            }
        }


        _isCricManiaChallenge(challengeId: string) {
            return challengeId === "external_eatfit_world_cup_challenge_2019"
        }

        _getInvitesWidget(invites: Array<ChallengeInviteDetail>, userContext: UserContext): IInviteWidget {
            const inviteViewArray: Array<IInvite> = invites.map((invite) => {
                const tz = userContext.userProfile.timezone
                const actions: Array<Action> = []
                if (invite.isAlreadyInChallenge) {
                    actions.push({
                        actionType: "ACCEPT_INVITE",
                        title: "Accept",
                        meta: {
                            textColor: SocialColors.reddishPink,
                            inviteId: invite.inviteId,
                            challengeId: invite.challenge.challengeId
                        }
                    })
                } else {
                    actions.push(_getJoinChallengeAction(invite.challenge.challengeId, {
                        actionType: "ACCEPT_INVITE_AND_JOIN",
                        title: "Accept & Join",
                        meta: {
                            textColor: SocialColors.reddishPink,
                            inviteId: invite.inviteId,
                            challengeId: invite.challenge.challengeId
                        }
                    }, userContext))
                }
                actions.push({
                    actionType: "REJECT_INVITE",
                    title: "Decline",
                    meta: {
                        textColor: SocialColors.purple,
                        inviteId: invite.inviteId,
                        challengeId: invite.challenge.challengeId
                    }
                })


                const inviteView: IInvite = {
                    challengeId: invite.challenge.challengeId,
                    title: invite.challenge.title,
                    subTitle: `${TimeUtil.formatDateInTimeZone(tz, invite.challenge.startDate, "DD MMM")} - ${TimeUtil.formatDateInTimeZone(tz, invite.challenge.endDate, "DD MMM")}`,
                    imageUrl: UrlPathBuilder.getChallengesImagePath(ChallengeImageTypes.INVITE, invite.challenge.challengeId),
                    from: `${invite.inviterFirstName} ${invite.inviterLastName}`,
                    topText: `New challenge invite | By ${invite.inviterFirstName} ${invite.inviterLastName}`,
                    actions
                }
                return inviteView
            })

            const inviteWidget: IInviteWidget = {
                widgetType: "INVITES_WIDGET",
                invites: inviteViewArray
            }

            return inviteWidget
        }

        @httpGet("/todayChallenges")
        async getTodayResponse(req: express.Request): Promise<{
            widgets: Array<WidgetView>,
            action: Action
        }> {
            return {
                widgets: [],
                action: {
                    actionType: "NAVIGATION",
                    url: "curefit://enrolledchallenges",
                    title: "My Challenges",
                    meta: {
                        showPlaceholder: true
                    }
                }
            }
        }

        @httpGet("/completedChallenges")
        async getAllCompletedChallenges(req: express.Request) {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone
            return await this.commonService.getCompletedChallenges(userId, tz)
        }

        _getRightSubText(challengeType: Metric, userData: BasicUserActivityInfo, score: Number): string {
            switch (challengeType) {
                case Metric.ACTIVITY_POINT:
                case Metric.LEVEL:
                case Metric.STREAK:
                    return `Points ${userData.activityPoints}`
                case Metric.ACTIVITY_TIME:
                    return score ? `${score} min` : "0 min"
                case Metric.CULT_SCORE:
                    return score ? `Score: ${score}` : "Score: --"
                case Metric.REFERRAL:
                    return score ? `${score} points` : "0 points"
                case Metric.STEPS:
                    return score ? `${score} steps` : "- steps"
                case Metric.RUNS:
                    return score ? `${score} Runs` : "0 Runs"
                case Metric.CALORIES:
                    return score ? `${score} Calories saved` : "0 Calories"
            }
            return ""
        }


        @httpGet("/challengev2/:challengeId")
        async getChallengeDetails(req: express.Request): Promise<IChallengeDetail> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus challenge details accessed").build()
        }

        @httpPost("/inviteFriends")
        async inviteFriends(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus inviteFriends accessed").build()
        }

        @httpPost("/acceptInvite")
        async acceptInvite(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus acceptInvite accessed").build()
        }

        @httpPost("/joinChallenge")
        async joinChallenge(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus joinChallenge accessed").build()
        }

        @httpPost("/leaveChallenge")
        async leaveChallenge(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus leaveChallenge accessed").build()
        }

        @httpPost("/rejectInvite")
        async rejectInvite(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus rejectInvite accessed").build()
        }

        @httpPost("/removeFriend")
        async removeFriend(req: express.Request): Promise<any> {
            throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus removeFriend accessed").build()
        }

        @httpGet("/leaderboard/:leaderboardId")
        async getLeaderBoard(req: express.Request): Promise<any> {
            const session: Session = req.session
            const userId: string = session.userId
            const userContext: UserContext = req.userContext as UserContext
            const tz = userContext.userProfile.timezone

            const leaderboardId: string = req.params.leaderboardId

            if (req.query.challengeId === undefined) {
                this.logger.error("Please select a challengeId while querying leaderboard from maximus")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please select a challengeId while querying leaderboard from maximus").build()
            }
            if (req.query.offset === undefined) {
                this.logger.error("Please select a offset while querying leaderboard from maximus")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please select a offset while querying leaderboard from maximus").build()
            }
            if (req.query.limit === undefined) {
                this.logger.error("Please select a offset while querying limit from maximus")
                throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 400).withDebugMessage("Please select a offset while querying limit from maximus").build()
            }

            const challengeId = req.query.challengeId
            const offset = req.query.offset
            const limit = req.query.limit
            const isFriendsData = req.query.isFriendsData ? req.query.isFriendsDat : false

            return await this.commonService.getLeaderboard(challengeId, userId, offset, limit, isFriendsData, leaderboardId, tz)
        }
    }

    return MaximusController
}

export default MaximusControllerFactory
