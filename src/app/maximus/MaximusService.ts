import { inject, injectable } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import { IQuestService, QUEST_CLIENT_TYPES } from "@curefit/quest-client"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { CacheHelper } from "../util/CacheHelper"
import { TimeUtil, Timezone } from "@curefit/util-common"
import {
    IActiveChallengeWidget,
    IActivityData,
    IChallengeActionWidget,
    IChallengeParticipants,
    IGoalWidget,
    IRewardsWidget,
    ITableWidget,
    IUserChallengeActivityData,
    IUserPerformanceWidget,
    IWidgetSection,
    ParticipantType,
    Reward
} from "../common/views/Social"
import {
    Challenge,
    ChallengeWithDetails,
    LeaderBoardRewardWithBadgeDetail,
    LeaderBoardType,
    LeaderBoardWithDetails,
    LeaderBoardWithEnrollmentDetail,
    Metric,
    MetricCap,
    UserLeaderBoardDetail,
    UserScoreDetail
} from "@curefit/maximus-common"
import * as _ from "lodash"
import { _isStepsChallenge } from "./Helper"
import { UrlPathBuilder } from "@curefit/product-common"
import * as momentTz from "moment-timezone"
import { Action, DescriptionWidget, InfoCard, ProductListWidget, WidgetView } from "../common/views/WidgetView"
import { UserContext } from "@curefit/userinfo-common"
import { CULTSCORE_TESTID } from "@curefit/user-common"
import { BasicUserActivityInfo, Level, Vertical } from "@curefit/quest-common"
import { getShareText } from "../util/QuestUtils"
import { ActivityTypeDS } from "@curefit/logging-common"
import { ActionUtil, ActivityStoreUtil, EtherLeaderboardUtil } from "@curefit/base-utils"
import { ErrorFactory } from "@curefit/error-client"
import { ErrorCodes } from "../error/ErrorCodes"

const CHALLENGE_COMPLETION_LOST = ""

@injectable()
export class MaximusService {
    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(QUEST_CLIENT_TYPES.IQuestService) private questService: IQuestService,
        @inject(CUREFIT_API_TYPES.CacheHelper) protected userCache: CacheHelper,
        @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
    ) {
    }

    public async getLeaderboard(challengeId: string, userId: string, offset: number, limit: number, isFriendsData: boolean, leaderboardId: string, tz: Timezone) {
        throw this.errorFactory.withCode(ErrorCodes.GENERIC_SOMETHING_WENT_WRONG_ERROR, 500).withDebugMessage("Maximus leaderboard accessed").build()
    }

    public async getCompletedChallenges(userId: string, tz: Timezone) {
        return {
            challenges: [] as Array<IActiveChallengeWidget>
        }
    }

    _getResultsCalcSection(): IWidgetSection {
        const resultsCalcWidget: WidgetView = {
            widgetType: "RESULT_CALC_WIDGET"
        }
        return {
            data: [resultsCalcWidget]
        }
    }

    _getRulesSection(challengeDetails: ChallengeWithDetails): IWidgetSection {
        if (challengeDetails.rules && challengeDetails.rules.length > 0) {
            const rules = challengeDetails.rules.filter((rule) => !_.isNil(rule))

            const infoCards: InfoCard[] = _.map(rules, rule => {
                return {
                    icon: `/image/icons/howItWorks/${rule.icon}.png`,
                    title: rule.title,
                    subTitle: rule.description
                }
            })
            const productListWidget: ProductListWidget = {
                widgetType: "PRODUCT_LIST_WIDGET",
                type: "SMALL",
                hideSepratorLines: false,
                header: {title: "RULES"},
                items: infoCards
            }
            return {
                data: [productListWidget]
            }
        }
        return {
            data: []
        }
    }

    _getExpiredChallengeSection(): IWidgetSection {
        const expireChallengeWidget: IChallengeActionWidget = {
            widgetType: "CHALLENGE_ACTION_WIDGET",
            action: {
                actionType: "NAVIGATION",
                title: "Joining date has expired",
                url: ""
            }
        }
        return {
            data: [expireChallengeWidget]
        }
    }

    _getLeaveChallengeSection(challengeId: string): IWidgetSection {
        const leaveChallengeWidget: IChallengeActionWidget = {
            widgetType: "CHALLENGE_ACTION_WIDGET",
            action: {
                actionType: "LEAVE_CHALLENGE",
                title: "Leave Challenge",
                meta: {
                    challengeId
                }
            }
        }
        return {
            data: [leaveChallengeWidget]
        }
    }

    async _getChallengeCompletionSection(userDetail: UserLeaderBoardDetail, userData: BasicUserActivityInfo,
                                         leaderboard: LeaderBoardWithDetails, userContext: UserContext, challengeId: string): Promise<IWidgetSection> {
        const userPromise = this.userCache.getUser(userDetail.userId)

        const userLeaderBoardDetail = userDetail.leaderBoardDetails[0]
        const rewardWon = !_.isEmpty(userLeaderBoardDetail.rewards) ? leaderboard.rewards.find(reward =>
            reward.id === userLeaderBoardDetail.rewards[0].id) : undefined

        let completionTitle = CHALLENGE_COMPLETION_LOST
        if (rewardWon) {
            completionTitle = "Congratulation!!"
        }

        const performanceWidget: IUserPerformanceWidget = {
            widgetType: "CHALLENGE_PERFORMANCE",
            title: completionTitle,
            userDetails: {
                name: `${userDetail.firstName} ${userDetail.lastName}`,
                profileImageUrl: userDetail.userImageUrl,
                result: _isStepsChallenge(challengeId) ? `Total Steps: ${userLeaderBoardDetail.score}` : `Rank ${userLeaderBoardDetail.rank} | Points ${userLeaderBoardDetail.score}`,
                rank: userLeaderBoardDetail.rank,
                score: userLeaderBoardDetail.score
            }
        }

        if (rewardWon) {
            const isRewardExpired = rewardWon.lastDateToClaim &&
                momentTz.tz(rewardWon.lastDateToClaim, userContext.userProfile.timezone).diff(momentTz.tz(userContext.userProfile.timezone), "seconds") < 0
            if (!isRewardExpired && rewardWon.rewardClaimUrl) {
                performanceWidget.reward = {
                    title: rewardWon.rewardClaimTitle,
                    subTitle: rewardWon.rewardClaimSubTitle,
                    action: {
                        title: rewardWon.rewardClaimActionText,
                        actionType: "NAVIGATION",
                        url: rewardWon.rewardClaimUrl
                    }
                }
            }

            const badgeWon = rewardWon.badge
            performanceWidget.badge = {
                ...badgeWon,
                url: UrlPathBuilder.getBadgeImagePath(badgeWon.badgeId),
                largeImgUrl: UrlPathBuilder.getBadgeImagePath(badgeWon.badgeId, true),
                shareImgUrl: UrlPathBuilder.getShareImageUrl(badgeWon.badgeId, badgeWon.name),
                shareText: getShareText(badgeWon.vertical, badgeWon.type),
                isAchieved: true
            }
            performanceWidget.badgeUrl = UrlPathBuilder.getBadgeImagePath(badgeWon.badgeId)
        }

        return {
            data: [performanceWidget]
        }
    }

    async _getActiveChallengeStatusSection(userDetail: UserLeaderBoardDetail): Promise<IWidgetSection> {
        const userLeaderBoardDetail = userDetail.leaderBoardDetails[0]
        const infoAction: Action = {
            actionType: "INTERNAL_LINK",
            title: "Step count syncs every 30 mins",
            url: "https://s3.ap-south-1.amazonaws.com/vm-html-pages/Steps%20Challenge%20FAQ-39cfaa76-9693-4f99-bf2a-b00e4d6fa6ba.html",
            meta: {
                linkText: "Know more"
            }
        }
        const performanceWidget: IUserPerformanceWidget = {
            widgetType: "CHALLENGE_PERFORMANCE",
            title: undefined,
            userDetails: {
                name: `${userDetail.firstName} ${userDetail.lastName}`,
                profileImageUrl: userDetail.userImageUrl,
                result: `Total Steps: ${userLeaderBoardDetail.score}`,
                rank: userLeaderBoardDetail.rank,
                score: userLeaderBoardDetail.score
            },
            data: {
                infoAction: infoAction
            }
        }
        return {
            data: [performanceWidget]
        }

    }

    _shouldShowActivityData(challengeType: Metric) {
        switch (challengeType) {
            case Metric.LEVEL:
                return true
            case Metric.CULT_SCORE:
            case Metric.ACTIVITY_POINT:
            case Metric.REFERRAL:
                return false
        }
        return false
    }

    /*
        table widget
    */

    _getTableWidget(activities: Array<(ActivityTypeDS | string)>, metric: Metric): IWidgetSection {
        switch (metric) {
            case Metric.LEVEL:
            case Metric.ACTIVITY_POINT: {
                if (metric === Metric.LEVEL) {
                    activities = ["CULT_CLASS", "MIND_CLASS", "CULT_SCORE_DIY", "EATFIT_MEAL", "DIY_FITNESS", "DIY_MEDITATION", "SLEEP", "WALK", "CONSULTATION"]
                }
                const tableWidget: ITableWidget = {
                    widgetType: "TABLE_WIDGET",
                    title: {key: "ACTIVITY", value: "POINTS"},
                    contents: activities.map((activity) => {
                        return {
                            key: this._getDisplayTextForActivity(activity as ActivityTypeDS),
                            value: String(ActivityStoreUtil.getActivityScore(activity as ActivityTypeDS, 10000)) // adding hack for steps
                        }
                    })
                }
                return {data: [tableWidget]}
            }
        }
        return {data: []}
    }


    /*
        goal widget
    */

    _getGoalSection(startDate: Date, endDate: Date, leaderboard: LeaderBoardWithDetails, userContext: UserContext): IWidgetSection {
        const tz = userContext.userProfile.timezone
        let goal = ""
        switch (leaderboard.metric) {
            case Metric.ACTIVITY_POINT: {
                switch (leaderboard.leaderBoardType) {
                    case LeaderBoardType.MAXIMUM:
                        goal = "Most cure.fit activities"
                        break
                }
            }
                break

            case Metric.LEVEL: {
                switch (leaderboard.leaderBoardType) {
                    case LeaderBoardType.MAXIMUM:
                        goal = "Achieve the Highest Level"
                        break
                    case LeaderBoardType.FIRST_TO:
                        goal = "Fastest to the Level"
                        break
                }
            }
                break

            case Metric.CULT_SCORE: {
                goal = "Highest Cult Score"
                break
            }
            case Metric.ACTIVITY_TIME: {
                goal = "Highest Activity Duration"
                break
            }

            case Metric.REFERRAL: {
                goal = "Earn Maximum Points"
                break
            }
            case Metric.STEPS: {
                goal = "15000 steps"
                break
            }
            case Metric.RUNS: {
                goal = "Maximum Runs"
                break
            }
            case Metric.CALORIES: {
                goal = "Dodge Maximum Bad Calories"
                break
            }
        }

        const goalWidget: IGoalWidget = {
            widgetType: "GOAL_WIDGET",
            list: [
                {
                    icon: "GOAL",
                    key: "Goal",
                    value: goal
                },
                {
                    icon: "DATE",
                    key: "Date",
                    value: `${TimeUtil.formatDateInTimeZone(tz, startDate, "DD MMM")} - ${TimeUtil.formatDateInTimeZone(tz, endDate, "DD MMM")}`
                }
            ]
        }

        return {data: [goalWidget]}
    }

    /*
        rewards widget
    */

    _getRewardSection(leaderboard: LeaderBoardWithDetails): IWidgetSection {
        const metric: Metric = leaderboard.metric
        let rewards: Array<Reward> = []

        rewards = leaderboard.rewards.map((reward: LeaderBoardRewardWithBadgeDetail, index: number) => {
            let title, name, imageUrl

            name = reward.badge ? reward.badge.name : ""
            imageUrl = reward.badge ? UrlPathBuilder.getBadgeImagePath(reward.badge.badgeId) : ""

            switch (metric) {
                case Metric.ACTIVITY_POINT:
                    title = `Rank ${reward.rankRange.min} - ${reward.rankRange.max}`
                    break

                case Metric.LEVEL:
                    title = `Level ${reward.minScoreThreshold}`
                    if (index === 0) {
                        title = `${title} +`
                    }
                    break

                case Metric.CULT_SCORE:
                    if (reward.title) {
                        title = reward.title
                    } else if (reward.minScoreThreshold) {
                        title = `Score ${reward.minScoreThreshold}+`
                    }
                    name = reward.imageName ? "" : reward.badge ? reward.badge.name : ""
                    imageUrl = reward.imageName ? `/image/rewards/${reward.imageName}.png` : reward.badge ? UrlPathBuilder.getBadgeImagePath(reward.badge.badgeId) : ""
                    break

                case Metric.ACTIVITY_TIME:
                    if (reward.title) {
                        title = reward.title
                    } else if (reward.minScoreThreshold) {
                        title = `${reward.minScoreThreshold} mins`
                    }
                    break

                case Metric.STEPS:
                case Metric.RUNS:
                case Metric.CALORIES:
                case Metric.REFERRAL:
                    if (reward.title) {
                        title = reward.title
                    } else if (reward.minScoreThreshold) {
                        title = `${reward.minScoreThreshold}+`
                    }
                    name = reward.imageName ? "" : reward.badge ? reward.badge.name : ""
                    imageUrl = reward.imageName ? `/image/rewards/${reward.imageName}.png` : reward.badge ? UrlPathBuilder.getBadgeImagePath(reward.badge.badgeId) : ""
                    break
            }

            const challengeReward: Reward = {
                badge: {
                    name,
                    imageUrl
                },
                title,
                subTitle: "",
                description: reward.name,
                bgColor: reward.color
            }
            return challengeReward
        })

        const rewardWidget: IRewardsWidget = {
            widgetType: "REWARDS_WIDGET",
            title: "REWARDS",
            rewards: rewards
        }

        if (metric === Metric.LEVEL) {
            rewardWidget.action = {
                actionType: "NAVIGATION",
                title: "What are Levels?",
                url: "curefit://viewlevels"
            }
        } else if (metric === Metric.CULT_SCORE) {
            rewardWidget.action = {
                actionType: "NAVIGATION",
                title: "What is Cult Score?",
                url: ActionUtil.userTestIntroPage(CULTSCORE_TESTID)
            }
        }

        return {data: [rewardWidget]}
    }

    /*
        description widget
    */

    _getDescriptionWidget(description: string, tnc?: Array<string>): IWidgetSection {
        const descriptions: InfoCard[] = []
        descriptions.push({
            title: "ABOUT",
            subTitle: description
        })

        const descriptionWidget: DescriptionWidget = new DescriptionWidget(descriptions, null, tnc)
        return {data: [descriptionWidget]}
    }

    _getDisplayTextForActivity(activityType: ActivityTypeDS | string): string {
        switch (activityType) {
            case "EATFIT_MEAL":
                return "eat.fit meal"
            case "CULT_CLASS":
                return "cult.fit center class"
            case "DIY_FITNESS":
                return "at home cult workout"
            case "DIY_MEDITATION":
                return "mind.fit meditation"
            case "MIND_CLASS":
                return "mind.fit center class"
            case "SLEEP":
                return "over 7 hrs sleep"
            case "WALK":
                return "10K steps"
            case "CONSULTATION":
                return "care.fit activity"
            case "CULT_SCORE_DIY":
                return "cult score assessment"
        }
        return ""
    }

    public _getProgressData(
        leaderBoard: LeaderBoardWithDetails,
        rank: number,
        latestSlotScore: number,
        totalParticipants: number,
        userData?: BasicUserActivityInfo | IUserChallengeActivityData,
        levels?: Array<Level>
    ): {
        completed: number
        colors?: Array<string>
    } {
        const metricCap: MetricCap = leaderBoard.metricCap
        const challengeType: Metric = leaderBoard.metric

        switch (challengeType) {
            case Metric.ACTIVITY_POINT:
            case Metric.STREAK:
            case Metric.CULT_SCORE:
                return {
                    completed: Math.round(((totalParticipants - rank) / totalParticipants) * 100) / 100,
                }

            case Metric.LEVEL: {
                // get level data here to calculate
                const userPoints = userData.activityPoints
                let presentLevel = levels[userData.currentLevelId]

                let completed
                if (userData.currentLevelId < (levels.length - 1)) {
                    let nextLevel = levels[presentLevel.levelId + 1]

                    if (userPoints < presentLevel.activityCount) {
                        // level is dirty, update for display
                        nextLevel = levels[presentLevel.levelId]
                        presentLevel = levels[presentLevel.levelId - 1]
                    }

                    completed = (userPoints - presentLevel.activityCount) / (nextLevel.activityCount - presentLevel.activityCount)

                    return {
                        completed: Math.round(completed * 100) / 100,
                        colors: [presentLevel.color, nextLevel.color]
                    }
                } else {
                    return {
                        completed: 1,
                        colors: [presentLevel.color, presentLevel.color]
                    }
                }
            }
            case Metric.ACTIVITY_TIME: {
                const scoreForLatestSlot: number = _.isFinite(latestSlotScore) ? latestSlotScore : 0
                const completionPercent = Math.round((scoreForLatestSlot / metricCap.capValue) * 100)
                return {
                    completed: completionPercent / 100,
                }
            }

            case Metric.REFERRAL:
                return undefined
            case Metric.STEPS:
                return undefined
            case Metric.RUNS:
                return undefined
            case Metric.CALORIES:
                return undefined
        }
        return {
            completed: 0
        }
    }

    _getScoreBasedOnMetric(scoreDetails: UserScoreDetail, metric: Metric): string {
        switch (metric) {
            case Metric.ACTIVITY_TIME:
            case Metric.ACTIVITY_POINT:
            case Metric.LEVEL:
            case Metric.STEPS:
            case Metric.RUNS:
            case Metric.CALORIES:
            case Metric.CULT_SCORE:
                return `${scoreDetails.score}`

            case Metric.STREAK:
                return ""
        }

        return ""
    }

    _getValueBasedOnMetric(
        rank: number,
        score: number,
        latestSlotScore: number,
        leaderboard: LeaderBoardWithDetails,
        totalEnrollments: number,
        userData?: BasicUserActivityInfo,
        level?: number,
        addPoints?: boolean
    ): string {
        const metric: Metric = leaderboard.metric
        switch (metric) {
            case Metric.ACTIVITY_POINT:
            case Metric.STREAK: {
                if (addPoints) {
                    return `${Math.round(((totalEnrollments - rank) / totalEnrollments) * 100)}% | Points ${score}`
                }
                return `${Math.round(((totalEnrollments - rank) / totalEnrollments) * 100)}%`
            }

            case Metric.LEVEL: {
                const value = level ? level : userData.currentLevelId
                if (addPoints) {
                    return `Level ${value} | Points ${score}`
                }
                return `Level ${value}`
            }
            case Metric.CULT_SCORE: {
                const percentile = Math.round(((totalEnrollments - rank) / totalEnrollments) * 100)
                if (addPoints) {
                    const displayText = score ? `${percentile}% | Score: ${score}` : "Score: --"
                    return displayText
                }
                return score ? `Above ${percentile}% people` : ""
            }
            case Metric.ACTIVITY_TIME: {
                const metricCap: MetricCap = leaderboard.metricCap
                const totalScore: number = _.isFinite(score) ? score : 0
                const scoreForLatestSlot: number = _.isFinite(latestSlotScore) ? latestSlotScore : 0
                const displayText: string = addPoints ? `${scoreForLatestSlot} | ${totalScore}` : `${scoreForLatestSlot} / ${metricCap.capValue} mins`
                return displayText
            }

            case Metric.REFERRAL: {
                const displayText = score ? `${score} points` : "0 points"
                return displayText
            }
            case Metric.STEPS: {
                const displayText = score ? `${score} steps` : "- steps"
                return displayText
            }
            case Metric.RUNS: {
                const displayText = score ? `${score} Runs` : "0 Runs"
                return displayText
            }
            case Metric.CALORIES: {
                const displayText = score ? `${score} bad Cal saved` : "0 bad Cal saved"
                return displayText
            }
        }

        return ""
    }

    _getLeaderboardParticipantList(
        leaderboard: LeaderBoardWithEnrollmentDetail | LeaderBoardWithDetails,
        challengeType: Metric,
        totalEnrollments: number,
        isFriendsData: boolean,
        levels?: Array<Level>,
        usersData?: { [id: string]: BasicUserActivityInfo },
        monthlyUserStats?: { [id: string]: BasicUserActivityInfo },
        currentWeekStats?: { [id: string]: BasicUserActivityInfo },
        lastWeekStats?: { [id: string]: BasicUserActivityInfo },
        timezone: Timezone = TimeUtil.IST_TIMEZONE
    ): Array<IChallengeParticipants> {
        const participantList: Array<IChallengeParticipants> = leaderboard.scores.map((score) => {

            const userScore = this._getScoreBasedOnMetric(score, leaderboard.metric)
            let latestSlotScore: number = 0
            if (!_.isNil(leaderboard.metricCap)) {
                const currEpoch: number = TimeUtil.getCurrentEpoch()
                const currHash: number = EtherLeaderboardUtil.hashTimestamp(currEpoch, leaderboard.metricCap, timezone)
                latestSlotScore = _.get(score.scoreMeta, currHash + "", 0)
            }
            const userValue = this._getValueBasedOnMetric(
                score.rank,
                score.score,
                latestSlotScore,
                leaderboard,
                totalEnrollments,
                usersData ? usersData[score.userId] : null,
                score.level,
                true
            )
            const userProgress = this._getProgressData(
                leaderboard,
                score.rank,
                latestSlotScore,
                totalEnrollments,
                monthlyUserStats ? monthlyUserStats[score.userId] : null,
                levels
            )

            const participants: IChallengeParticipants = {
                rank: score.rank,
                name: `${score.firstName} ${score.lastName}`,
                imageUrl: score.userImageUrl,
                score: userScore,
                value: userValue,
                type: isFriendsData ? ParticipantType.FRIEND : ParticipantType.GLOBAL,
                progress: userProgress
            }

            if (isFriendsData) {
                const currentWeekActivities = currentWeekStats[score.userId] ? currentWeekStats[score.userId].activityPoints : 0
                const lastWeekActivities = currentWeekStats[score.userId] ? lastWeekStats[score.userId].activityPoints : 0
                const updates: {
                    title: string
                    value: number
                    increase?: string
                    decrease?: string
                } = {
                    title: "ACTIVITY POINTS (LAST WEEK)",
                    value: lastWeekActivities
                }

                if (currentWeekActivities > lastWeekActivities) {
                    updates.increase = `${Math.round(((currentWeekActivities - lastWeekActivities) / lastWeekActivities) * 100)}% ↑`
                } else if (currentWeekActivities < lastWeekActivities) {
                    updates.decrease = `${Math.round(((lastWeekActivities - currentWeekActivities) / lastWeekActivities) * 100)}% ↓`
                }

                // adding participants and activities for friends
                participants.updates = updates
                participants.activities = this._getParticipantActivities(challengeType, usersData[score.userId], currentWeekStats[score.userId], lastWeekStats[score.userId])
            }

            return participants
        })

        return participantList
    }

    _getParticipantActivities(
        metric: Metric,
        userData: BasicUserActivityInfo,
        currentWeekStats: BasicUserActivityInfo,
        lastWeekStats: BasicUserActivityInfo
    ): Array<IActivityData> {
        switch (metric) {
            case Metric.LEVEL:
            case Metric.ACTIVITY_POINT:
            case Metric.STREAK: {
                return userData.activitiesBreakUp.map(activity => {
                    return {
                        type: activity.vertical,
                        score: activity.activityPoints,
                        title: this._getProgressWidgetTitle(activity.vertical),
                        status: this.getTrend(currentWeekStats, lastWeekStats, activity.vertical)
                    }
                }).filter(activity => {
                    return activity.type !== "GLOBAL"
                })
            }
        }
        return []
    }

    _getProgressWidgetTitle(vertical: Vertical): string {
        switch (vertical) {
            case "CULT":
                return "Workout\npoints"
            case "EAT":
                return "Meals\npoints"
            case "MIND":
                return "Meditation\npoints"
            case "SLEEP":
                return "Sleep\npoints"
            case "STEPS":
                return "Steps\npoints"
            case "CARE":
                return "Care\npoints"
        }
        return ""
    }

    private getTrend(
        thisWeekData: BasicUserActivityInfo,
        lastWeekData: BasicUserActivityInfo,
        vertical: Vertical
    ): number {
        const thisWeekActivityCount = thisWeekData && _.find(thisWeekData.activitiesBreakUp, data => {
            return data.vertical === vertical
        }) ? _.find(thisWeekData.activitiesBreakUp, data => {
            return data.vertical === vertical
        }).numActivities : 0
        const lastWeekActivityCount = lastWeekData && _.find(lastWeekData.activitiesBreakUp, data => {
            return data.vertical === vertical
        }) ? _.find(lastWeekData.activitiesBreakUp, data => {
            return data.vertical === vertical
        }).numActivities : 0
        return thisWeekActivityCount - lastWeekActivityCount
    }

    _isChallengeEnrollmentOpen(challengeDetails: ChallengeWithDetails | Challenge, userContext: UserContext) {
        if (challengeDetails.lastJoinDate && momentTz.tz(userContext.userProfile.timezone)
            .diff(momentTz.tz(challengeDetails.lastJoinDate, userContext.userProfile.timezone)) >= 1) {
            return false
        }
        return true
    }


    // TODO: move to modeling of challenges/leaderboard
    _isProgressApplicableForMetric(metric: Metric): boolean {
        if (metric === "REFERRAL") {
            return false
        }
        return true
    }

    _isCricManiaChallenge(challengeId: string) {
        return challengeId === "external_eatfit_world_cup_challenge_2019"
    }

}
