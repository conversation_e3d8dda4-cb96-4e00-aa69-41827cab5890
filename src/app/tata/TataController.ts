import * as express from "express"
import { controller, httpPost } from "inversify-express-utils"
import { Container, inject } from "inversify"
import CUREFIT_API_TYPES from "../../config/ioc/types"
import { BASE_TYPES, Logger } from "@curefit/base"
import AuthMiddleware from "../auth/AuthMiddleware"
import { ErrorFactory } from "@curefit/error-client"
import { IThirdPartyService, NeuPassActivationResponse, NeuPassClickAction, THIRD_PARTY_CLIENT_TYPES } from "@curefit/third-party-integrations-client"
import BaseOrderConfirmationViewBuilder, { ConfirmationRequestParams, ConfirmationView } from "../order/BaseOrderConfirmationViewBuilder"
import OrderConfirmationViewBuilderV1 from "../order/OrderConfirmationViewBuilderV1"
import { UserContext } from "@curefit/userinfo-common"
import { Action } from "@curefit/apps-common"
import { TataNeuUtil } from "../util/TataNeuUtil"

export function tataControllerFactory(kernel: Container) {

    @controller("/tata")
    class TataController {
        constructor(@inject(BASE_TYPES.ILogger) private logger: Logger,
                    @inject(CUREFIT_API_TYPES.ErrorFactory) private errorFactory: ErrorFactory,
                    @inject(THIRD_PARTY_CLIENT_TYPES.ThirdPartyService) private thirdPartyService: IThirdPartyService,
            @inject(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1) private orderConfirmationViewBuilderV1: OrderConfirmationViewBuilderV1,
        ) {

        }
        @httpPost("/activateNeuPass", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async activateNeuPass(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            await this.thirdPartyService.activateNeuPass(userId)
            if (await TataNeuUtil.isNeuPassBenefitsToastMessageSupported(userContext)) {
                return TataNeuUtil.getNeuPassBenefitsToastMessageAction()
            }
            const neuPassClickAction: NeuPassClickAction = await this.thirdPartyService.getActionOnMyNeuPassClick(userId)
            return await this.orderConfirmationViewBuilderV1.buildTataNeuActivatedConfirmationView(neuPassClickAction, userContext, false)
        }

        @httpPost("/consentForNeuPass", kernel.get<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).validateSession)
        async consentForNeuPass(req: express.Request): Promise<any> {
            const userContext = req.userContext as UserContext
            const userId: string = userContext.userProfile.userId
            await this.thirdPartyService.provideConsentForNeuPass(userId)
            if (await TataNeuUtil.isNeuPassBenefitsToastMessageSupported(userContext)) {
                return TataNeuUtil.getNeuPassBenefitsToastMessageAction()
            }
            const neuPassClickAction: NeuPassClickAction = await this.thirdPartyService.getActionOnMyNeuPassClick(userId)
            return await this.orderConfirmationViewBuilderV1.buildTataNeuActivatedConfirmationView(neuPassClickAction, userContext, true)
        }
    }

    return TataController
}

export default tataControllerFactory