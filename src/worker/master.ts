import { Loader } from "../config/ioc/loader"

const loader = new Loader()
loader.loadWorkerBindings()

import kernel from "../config/ioc/ioc"
import { Logger, BASE_TYPES } from "@curefit/base"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import CUREFIT_API_TYPES from "../config/ioc/types"
import NotificationQueueListener from "../app/ugc/NotificationQueueListener"
import InterventionQueueListener from "../app/intervention/InterventionQueueListener"
import FitnessReportQueueListener from "../app/ugc/FitnessReportQueueListener"
import SessionQueueListener from "../app/auth/SessionQueueListener"
import PNTokenEventQueueListener from "../app/auth/PNTokenEventQueueListener"
import GymfitCheckinQueueListener from "../app/gymfit/GymfitCheckinQueueListener"
import * as express from "express"

const cookieParser = require("cookie-parser")
const http = require("http")
const https = require("http")

import { APIServer as Server } from "../server"
import { collectDefaultMetrics, register } from "prom-client"
import { errorMiddleware } from "../config/middlewares/errorMiddleware"
import { corsMiddleware } from "../config/middlewares/corsMiddleware"
import { clientMiddleware } from "../config/middlewares/clientMiddleware"
import { clsMiddleware } from "../config/middlewares/clsMiddleware"
import { requestIdMiddleware } from "../config/middlewares/requestIdMiddleware"
import { useragentMiddleware } from "../config/middlewares/useragentMiddleware"
import * as bodyParser from "body-parser"
import { postRequestMiddleware } from "../config/middlewares/postRequestMiddleware"
import { ipFilterMiddleware } from "../config/middlewares/ipFilterMiddleware"
import { Socket } from "node:net"
import { concurrencyLimiter } from "../config/middlewares/concurrencyMiddleware"

const COOKIE_SECRET_KEY = "wXHnVPrTFr"

const logger: Logger = kernel.get<Logger>(BASE_TYPES.ILogger)

if (["STAGE", "PRODUCTION"].includes(process.env.ENVIRONMENT)) {
// Dummy usage to trigger queue listeners
    const notificationQueueListener: NotificationQueueListener = kernel.get<NotificationQueueListener>(CUREFIT_API_TYPES.NotificationQueueListener)
    const interventionQueueListener: InterventionQueueListener = kernel.get<InterventionQueueListener>(CUREFIT_API_TYPES.InterventionQueueListener)
    const fitnessReportQueueListener: FitnessReportQueueListener = kernel.get<FitnessReportQueueListener>(CUREFIT_API_TYPES.FitnessReportQueueListener)
    const sessionQueueListener: SessionQueueListener = kernel.get<SessionQueueListener>(CUREFIT_API_TYPES.SessionQueueListener)
    const pNTokenEventQueueListener: PNTokenEventQueueListener = kernel.get<PNTokenEventQueueListener>(CUREFIT_API_TYPES.PNTokenEventQueueListener)
    const gymfitCheckinQueueListener: GymfitCheckinQueueListener = kernel.get<GymfitCheckinQueueListener>(CUREFIT_API_TYPES.GymfitCheckinQueueListener)
}


logger.debug(`Worker ${process.env.CLUSTER_WORKER_TYPE} ${process.pid} is running`)
const server = new Server(kernel)

server.setConfig((app: express.Application) => {
    app.use(concurrencyLimiter({max: 100}))
    app.set("trust proxy", true)
    app.use(cookieParser(COOKIE_SECRET_KEY))
    app.use(requestIdMiddleware)
    app.use(corsMiddleware)
    app.use(clientMiddleware)
    app.use(useragentMiddleware)
    app.use(bodyParser.json({ limit: "2mb" }))
    app.use(bodyParser.urlencoded({ extended: true, limit: "2mb" }))
    app.use(bodyParser.text({ limit: "2mb" }))
    app.use(clsMiddleware)
    app.use(ipFilterMiddleware)
})

server.setErrorConfig((app: express.Application) => {
    app.use(postRequestMiddleware)
    app.use(errorMiddleware)
})

const app = server.build()
app.disable("x-powered-by")

const port = parseInt(process.env.PORT ?? "3000", 10)

const httpServer = http.createServer({keepAlive: true, noDelay: true}, app).listen(port, () => {
    console.log("CureFit API is running on:" + port)
})

httpServer.on("connection", function (socket: Socket) {
    socket.setKeepAlive(true, 0)
    socket.setTimeout(600 * 1000)
    // 2 min keep alive timeout, to be more than LB's timeout
    socket.on("timeout", () => {
        logger.error("socket timeout")
        socket.end()
    })
})

httpServer.keepAliveTimeout = 600 * 1000
httpServer.headersTimeout = 600 * 1000

http.globalAgent.keepAlive = true
http.globalAgent.options.keepAlive = true
https.globalAgent.keepAlive = true
https.globalAgent.options.keepAlive = true

const metricsServer = express()

metricsServer.get("/cluster_metrics", async (req, res) => {
    res.set("Content-Type", register.contentType)
    res.send(await register.metrics())
})

const metricsPort = parseInt(process.env.METRICS_PORT ?? "9102", 10)
metricsServer.listen(metricsPort)
logger.info(`Cluster metrics initialized on port ${metricsPort}`)
