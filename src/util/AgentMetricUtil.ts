import { Agent } from "http"
import { injectable, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../config/ioc/types"
import { PromUtil } from "./PromUtil"
import * as util from "util"

@injectable()
export class AgentMetricUtil {

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger,
        @inject(CUREFIT_API_TYPES.PromUtil) private promUtil: PromUtil,
    ) {
        setInterval(() => {
            const handleMap = new Map<string, number>()

            // @ts-ignore
            for (const handle of process._getActiveHandles()) {
                const host = `${handle._host ?? handle._peername?.address}`
                if (!host) {
                    continue
                }
                if (host === "::ffff:*********" || host === "undefined") {
                    this.logger.warn(host, util.inspect(handle, {depth: 4}))
                }
                if (!handleMap.has(host)) {
                    handleMap.set(host, 1)
                } else {
                    handleMap.set(host, handleMap.get(host) + 1)
                }
            }
            for (const [key, value] of handleMap.entries()) {
                this.promUtil.reportActiveHandlesCount(key, value)
            }

        }, 20_000)
    }
}
