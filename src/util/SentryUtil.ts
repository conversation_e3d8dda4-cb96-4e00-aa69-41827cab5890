const path = require("path")
const Sentry = require("@sentry/node")

export function getSentryOptions() {
    return {
        enabled: process.env.ENVIRONMENT === "PRODUCTION" || process.env.ENVIRONMENT === "STAGE",
        dsn: process.env.ENVIRONMENT === "PRODUCTION" ? "https://<EMAIL>/1359021" :
                process.env.ENVIRONMENT === "ALPHA" ? "https://<EMAIL>/1359021" :
                    process.env.ENVIRONMENT === "STAGE" ? "https://<EMAIL>/1359021" :
                        "https://<EMAIL>/1359021",
        attachStacktrace: true,
        environment: process.env.ENVIRONMENT || "development",
        release: process.env.APP_VERSION,
        debug: process.env.ENVIRONMENT !== "PRODUCTION",
        integrations: [new Sentry.Integrations.RewriteFrames({
            root: path.join(__dirname, "../..") || process.cwd(),
        })]
    }
}
