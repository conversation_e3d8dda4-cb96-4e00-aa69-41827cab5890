import { Gauge, Counter } from "prom-client"
import { injectable, inject } from "inversify"
import { BASE_TYPES, Logger } from "@curefit/base"

@injectable()
export class PromUtil {

    private widgetRenderGauge: Gauge<string>
    private serverLoadRunningGauge: Gauge<string>
    private serverLoadQueuedGauge: Gauge<string>
    private widgetRenderCount: Counter<string>
    private pageRenderGauge: Gauge<string>
    private memoryLoadGauge: Gauge<string>
    private agentSocketCountGauge: Gauge<string>
    private activeHandlesCountGauge: Gauge<string>
    private agentRequestCountGauge: Gauge<string>
    private inboundStatusCodeCounter: Counter<string>
    private notificationCounter: Counter<string>

    constructor(
        @inject(BASE_TYPES.ILogger) private logger: Logger
    ) {

        this.widgetRenderGauge = new Gauge({
            name: "widgetRenderTime",
            help: "Gauge of widget render time",
            labelNames: ["widgetId", "pageId", "exceptionClass", "slaOwner"],
            aggregator: "average",
        })

        this.serverLoadRunningGauge = new Gauge({
            name: "serverLoadRunningRequestsGauge",
            help: "Gauge of requests running currently",
            aggregator: "average",
        })

        this.serverLoadQueuedGauge = new Gauge({
            name: "serverLoadQueuedRequestsGauge",
            help: "Gauge of requests queued currently",
            aggregator: "average",
        })

        this.widgetRenderCount = new Counter({
            name: "widgetRenderCount_total",
            help: "Count of widget render",
            labelNames: ["widgetId", "pageId", "exceptionClass", "slaOwner"],
            aggregator: "sum"
        })

        this.pageRenderGauge = new Gauge({
            name: "pageRenderTime",
            help: "Gauge of page render time",
            labelNames: ["pageId"],
            aggregator: "average",
        })

        this.memoryLoadGauge = new Gauge({
            name: this.getMetricsKey("memoryLoadTime"),
            help: "Gauge of cache load time",
            labelNames: ["service"],
            aggregator: "average",
        })

        this.agentSocketCountGauge = new Gauge({
            name: this.getMetricsKey("agentSocketCount"),
            help: "Gauge of agent socket count",
            labelNames: ["service"],
            aggregator: "average",
        })

        this.activeHandlesCountGauge = new Gauge({
            name: this.getMetricsKey("activeHandlesCount"),
            help: "Gauge of active handles count",
            labelNames: ["service"],
            aggregator: "average",
        })

        this.agentRequestCountGauge = new Gauge({
            name: this.getMetricsKey("agentRequestCount"),
            help: "Gauge of agent request count",
            labelNames: ["service"],
            aggregator: "average",
        })

        this.inboundStatusCodeCounter = new Counter({
            name: "inbound_req_total",
            help: "Number of inbound hits per code",
            labelNames: ["code", "path"],
            aggregator: "sum"
        })

        this.notificationCounter = new Counter({
            name: this.getMetricsKey("notification"),
            help: "Number of notifications",
            labelNames: ["type"],
            aggregator: "sum"
        })

    }

    private getMetricsKey(event: string): string {
        return process.env.ENVIRONMENT + "_cfapi_" + event
    }

    public reportWidgetRenderMetrics(widgetId: string, pageId: string, exceptionClass: string, slaOwner: string, value: number) {
        try {
            this.widgetRenderGauge.labels(widgetId, pageId, exceptionClass, slaOwner).set(value)
            this.widgetRenderCount.labels(widgetId, pageId, exceptionClass, slaOwner).inc()
        } catch (e) {
            this.logger.error("Error while reporting widget render metrics " + e)
        }
    }

    public reportPageRenderTime(pageId: string, value: number) {
        try {
            this.pageRenderGauge.labels(pageId).set(value)
        } catch (e) {
            this.logger.error("Error while reporting page render time metrics " + e)
        }
    }

    public reportServerLoadRunningRequests(count: number) {
        try {
            this.serverLoadRunningGauge.set(count)
        } catch (e) {
            this.logger.error("Error while reporting running requests" + e)
        }
    }

    public reportServerLoadQueuedRequests(count: number) {
        try {
            this.serverLoadQueuedGauge.set(count)
        } catch (e) {
            this.logger.error("Error while reporting queued requests" + e)
        }
    }

    public reportCacheLoadTime(cachingService: string, value: number) {
        try {
            this.memoryLoadGauge.labels(cachingService).set(value)
        } catch (e) {
            this.logger.error("Error while reporting cache load time metrics " + e)
        }
    }

    public reportAgentSocketCount(agentService: string, value: number) {
        try {
            this.agentSocketCountGauge.labels(agentService).set(value)
        } catch (e) {
            this.logger.error("Error while reporting agent socket count metrics " + e)
        }
    }

    public reportActiveHandlesCount(agentService: string, value: number) {
        try {
            this.activeHandlesCountGauge.labels(agentService).set(value)
        } catch (e) {
            this.logger.error("Error while reporting active handles count metrics " + e)
        }
    }

    public reportAgentRequestCount(agentService: string, value: number) {
        try {
            this.agentRequestCountGauge.labels(agentService).set(value)
        } catch (e) {
            this.logger.error("Error while reporting agent request count metrics " + e)
        }
    }

    public reportInboundReqStatus(statusCode: string, urlPath: string) {
        try {
            this.inboundStatusCodeCounter.labels(statusCode, urlPath).inc()
        } catch (e) {
            this.logger.error("Error while updating reportInboundReqStatus metrics " + JSON.stringify(e, null, 2))
        }
    }

    public reportNotificationCount(notificationType: string) {
        try {
            this.notificationCounter.labels(notificationType).inc()
        } catch (e) {
            this.logger.error("Error while updating reportNotificationCount metrics " + JSON.stringify(e))
        }
    }

}
