export const CUREFIT_API_TYPES = {
    AuthBusiness: "curefit-api:AuthBusiness",
    SSOBusiness: "curefit-api:SSOBusiness",
    SSOMiddleware: "curefit-api:SSOMiddleware",
    MultiDeviceHandlingMiddleware: "curefit-api:MultiDeviceHandlingMiddleware",
    SSOConfig: "curefit-api:SSOConfig",
    TimelineBusinessV1: "curefit-api:TimelineBusinessV1",
    TimelineBusinessV4: "curefit-api:TimelineBusinessV4",
    ProductBusiness: "curefit-api:ProductBusiness",
    DeviceBusiness: "curefit-api:DeviceBusiness",
    UserBusiness: "curefit-api:UserBusiness",
    UserPreferenceBusiness: "curefit-api:UserPreferenceBusiness",
    NavBarBusiness: "curefit-api:NavBarBusiness",
    MealPlannerBusiness: "curefit-api:MealPlannerBusiness",
    CultBusiness: "curefit-api:CultBusiness",
    UserPlanBusiness: "curefit-api:UserPlanBusiness",
    UserGoalBusiness: "curefit-api:UserGoalBusiness",
    OtpBusiness: "curefit-api:OtpBusiness",
    RecommendationBusiness: "curefit-api:RecommendationBusiness",
    InfoPageBuilder: "curefit-api:InfoPageBuilder",
    MindLandingPageBuilderV1: "curefit-api:MindLandingPageBuilderV1",
    MindLandingPageBuilderV2: "curefit-api:MindLandingPageBuilderV2",
    CultLandingPageBuilder: "curefit-api:CultLandingPageBuilder",
    ChallengeReportViewBuilder: "curefit-api:ChallengeReportViewBuilder",
    CultLandingPageBuilderV1: "curefit-api:CultLandingPageBuilderV1",
    CultLandingPageBuilderV2: "curefit-api:CultLandingPageBuilderV2",
    EatLandingPageBuilderV3: "curefit-api:EatLandingPageBuilderV3",
    EatLandingPageBuilderV4: "curefit-api:EatLandingPageBuilderV4",
    EatLandingPageBuilderV5: "curefit-api:EatLandingPageBuilderV5",
    HomePageConfig: "curefit-api:HomePageConfig",
    MealPackPageConfig: "curefit-api:MealPackPageConfig",
    CultPackPageConfig: "curefit-api:CultPackPageConfig",
    FitnessFirstPackPageConfig: "curefit-api:FitnessFirstPackPageConfig",
    CultDIYPackPageConfig: "curefit-api:CultDIYPackPageConfig",
    MindDIYPackPageConfig: "curefit-api:MindDIYPackPageConfig",
    CultLandingPageConfig: "curefit-api:CultLandingPageConfig",
    GearLandingPageConfig: "curefit-api:GearLandingPageConfig",
    CultLandingPageConfigV2: "curefit-api:CultLandingPageConfigV2",
    OnboardingPageConfig: "curefit-api:OnboardingPageConfig",
    EatLandingPageConfig: "curefit-api:EatLandingPageConfig",
    MindLandingPageConfig: "curefit-api:MindLandingPageConfig",
    MindLandingPageConfigV2: "curefit-api:MindLandingPageConfigV2",
    MeasurementUnitCache: "curefit-api:MeasurementUnitCache",
    LoggableSlotsMetaCache: "curefit-api:LoggableSlotsMetaCache",
    FeedbackPageConfig: "curefit-api:FeedbackPageConfig",
    FeedbackPageConfigV2Cache: "curefit-api:FeedbackPageConfigV2Cache",
    PaymentPageConfig: "curefit-api:PaymentPageConfig",
    TeleconsultationDetailsPageConfig: "curefit-api:TeleconsultationDetailsPageConfig",
    RecommendationPageConfig: "curefit-api:RecommendationPageConfig",
    BookingConfirmationPageConfig: "curefit-api:BookingConfirmationPageConfig",
    HCUDetailsPageConfig: "curefit-api:HCUDetailsPageConfig",
    OrderViewBuilder: "curefit-api:OrderViewBuilder",
    CartViewBuilder: "curefit-api:CartViewBuilder",
    GearCartViewBuilder: "curefit-api:GearCartViewBuilder",
    OrderProductDetailBuilder: "curefit-api:OrderProductDetailBuilder",
    CultOfferDetailViewBuilder: "curefit-api:CultOfferDetailViewBuilder",
    OrderConfirmationViewBuilder: "curefit-api:OrderConfirmationViewBuilder",
    OrderConfirmationViewBuilderV1: "curefit-api:OrderConfirmationViewBuilderV1",
    ActivePackViewBuilderV1: "curefit-api:ActivePackViewBuilderV1",
    RecommendationViewBuilder: "curefit-api:RecommendationViewBuilder",
    SessionDao: "curefit-api:SessionDao",
    FirebaseService: "curefit-api:FirebaseService",
    ClassListViewBuilder: "curefit-api:ClassListViewBuilder",
    ClassListViewBuilderV1: "curefit-api:ClassListViewBuilderV1",
    ClassListViewBuilderV2: "curefit-api:ClassListViewBuilderV2",
    CenterBookingCache: "curefit-api:CenterBookingCache",
    FeedbackBusiness: "curefit-api:FeedbackBusiness",
    FeedbackRedisCache: "curefit-api:FeedbackRedisCache",
    CfsFormCache: "curefit-api:CfsFormCache",
    SessionQueueListener: "curefit-api:SessionQueueListener",
    GymfitCheckinQueueListener: "curefit-api:GymfitCheckinQueueListener",
    FitnessReportQueueListener: "curefit-api:FitnessReportQueueListener",
    NotificationQueueListener: "curefit-api:NotificationQueueListener",
    FeedbackViewBuilder: "curefit-api:FeedbackViewBuilder",
    MealDetailViewBuilder: "curefit-api:MealDetailViewBuilder",
    ChangeMealViewBuilder: "curefit-api:ChangeMealViewBuilder",
    AddChangeMealViewBuilder: "curefit-api:AddChangeMealViewBuilder",
    CultFitPackDetailViewBuilder: "curefit-api:CultFitPackDetailViewBuilder",
    CultFitPackDetailViewBuilderV2: "curefit-api:CultFitPackDetailViewBuilderV2",
    CultPrePurchaseDetailViewBuilder: "curefit-api:CultPrePurchaseDetailViewBuilder",
    CultPostPurchaseDetailViewBuilder: "curefit-api:CultPostPurchaseDetailViewBuilder",
    CultUserProfileViewBuilder: "curefit-api:CultUserProfileViewBuilder",
    LivePtGoalPageViewBuilder: "curefit-api:LivePtGoalPageViewBuilder",
    CultMomentsViewBuilder: "curefit-api:CultMomentsViewBuilder",
    CultPausePackViewBuilder: "curefit-api:CultPausePackViewBuilder",
    GymPausePackViewBuilder: "curefit-api:GymPausePackViewBuilder",
    PlayPausePackViewBuilder: "curefit-api:PlayPausePackViewBuilder",
    ManagedPlanProductPageViewBuilder: "curefit-api:ManagedPlanProductPageViewBuilder",
    CareCartProductPageViewBuilder: "curefit-api:CareCartProductPageViewBuilder",
    NutritionistPlanProductPageViewBuilder: "curefit-api:NutritionistPlanProductPageViewBuilder",
    FitnessFirstPackDetailViewBuilder: "curefit-api:FitnessFirstPackDetailViewBuilder",
    DiyFavoritesViewBuilder: "curefit-api:DiyFavoritesViewBuilder",
    CultMindPackDetailViewBuilder: "curefit-api:CultMindPackDetailViewBuilder",
    CultMindPackDetailViewBuilderV2: "curefit-api:CultMindPackDetailViewBuilderV2",
    PulseViewBuilder: "curefit-api:PulseViewBuilder",
    ProgramPackDetailViewBuilder: "curefit-api:ProgramPackDetailViewBuilder",
    MealSubscriptionViewBuilder: "curefit-api:MealSubscriptionViewBuilder",
    BookingDetailViewBuilder: "curefit-api:BookingDetailViewBuilder",
    BookingDetailViewBuilderV2: "curefit-api:BookingDetailViewBuilderV2",
    WodViewBuilder: "curefit-api:WodViewBuilder",
    AuthMiddleware: "curefit-api:AuthMiddleware",
    CRMIssueService: "curefit-api:CRMIssueService",
    CRMBusiness: "curefit-api:CRMBusiness",
    IssueService: "curefit-api:IssueService",
    IssueBusiness: "curefit-api:IssueBusiness",
    CloudWatchMiddleware: "curefit-api:CloudWatchMiddleware",
    BlackListMiddleware: "curefit-api:BlackListMiddleware",
    ApiErrorCountMiddleware: "curefit-api:ApiErrorCountMiddleware",
    CareLandingPageConfig: "curefit-api:CareLandingPageConfig",
    CareLandingPageBuilderV1: "curefit-api:CareLandingPageBuilderV1",
    ActivityStoreQueueListener: "curefit-api:ActivityStoreQueueListener",
    CapacityServiceWrapper: "curefit-api:CapacityServiceWrapper",
    InterventionQueueListener: "curefit-api:InterventionQueueListener",
    CultNoShowHandler: "curefit-api:CultNoShowHandler",
    PTSGTNoShowHandler: "curefit-api:PTSGTNoShowHandler",
    MessageInterventionHandler: "curefit-api:MessageInterventionHandler",
    InterventionBusiness: "curefit-api:InterventionBusiness",
    InterventionRedisCache: "curefit-api:InterventionRedisCache",
    InterventionHandlerFactory: "curefit-api:InterventionHandlerFactory",
    EventDetailViewBuilder: "curefit-api:EventDetailViewBuilder",
    StatusHandler: "curefit-api:StatusHandler",
    SpotHandler: "curefit-api:SpotHandler",
    ProgramBusiness: "curefit-api:ProgramBusiness",
    OrderConfirmationBusiness: "curefit-api:OrderConfirmationBusiness",
    DiagnosticReportViewBuilder: "curefit-api:DiagnosticReportViewBuilder",
    DiagnosticReportViewBuilderV2: "curefit-api:DiagnosticReportViewBuilderV2",
    ConsultationListPageViewBuilder: "curefit-api:ConsultationListPageViewBuilder",
    MealListPageViewBuilder: "curefit-api:MealListPageViewBuilder",
    ClassListPageViewBuilder: "curefit-api:ClassListPageViewBuilder",
    LiveClassDetailViewBuilder: "curefit-api:LiveClassDetailViewBuilder",
    LiveClassDetailViewBuilderV2: "curefit-api:LiveClassDetailViewBuilderV2",
    DIYClassDetailViewBuilder: "curefit-api:DIYClassDetailViewBuilder",
    RecommendedFirstClassesViewBuilder: "curefit-api:RecommendedFirstClassesViewBuilder",
    MeDiagnosticTestListPageViewBuilder: "curefit-api:MeDiagnosticTestListPageViewBuilder",
    GearListPageViewBuilder: "curefit-api:GearListPageViewBuilder",
    StoreListPageBuilder: "curefit-api:StoreListPageViewBuilder",
    GymListPageViewBuilder: "curefit-api:GymListPageViewBuilder",
    SportListPageViewBuilder: "curefit-api:SportListPageViewBuilder",
    SessionService: "curefit-api:SessionService",
    RedisAuthTokenService: "curefit-api:RedisAuthTokenService",
    BaseSessionService: "curefit-api:BaseSessionService",
    CFAPICityService: "curefit-api:CFAPICityService",
    ServiceInterfaces: "curefit-api:ServiceInterfaces",
    VMPageBuilder: "curefit-api:VMPageBuilder",
    WidgetBuilder: "curefit-api:WidgetBuilder",
    CultScoreViewBuilder: "curefit-api:CultScoreViewBuilder",
    TestViewBuilderFactory: "curefit-api:TestViewBuilderFactory",
    SEOService: "curefit-api:SEOService",
    BreadCrumbService: "curefit-api:BreadCrumbService",
    CyclingService: "curefit-api:CyclingService",
    TherapyPageConfig: "curefit-api:TherapyPageConfig",
    AtlasActivityService: "curefit-api:AtlasActivityService",
    TPOffersBusiness: "curefit-api:TPOffersBusiness",
    CultFitSubscriptionDetailViewBuilder: "curefit-api:CultFitSubscriptionDetailViewBuilder",
    CultMindSubscriptionDetailViewBuilder: "curefit-api:CultMindSubscriptionDetailViewBuilder",
    GearOrderViewBuilder: "curefit-api:GearOrderViewBuilder",
    GearOrderWithShipmentViewBuilder: "curefit-api:GearOrderWithShipmentViewBuilder",
    CultClpPageHook: "curefit-api:CultClpPageHook",
    GearProductViewBuilder: "curefit-api:GearProductViewBuilder",
    GearLandingPageBuilder: "curefit-api:GearLandingPageBuilder",
    QSRFeedbackSchema: "curefit-api:QSRFeedbackSchema",
    QSRFeedbackReadWriteDao: "curefit-api:QSRFeedbackReadWriteDao",
    QSRFeedbackReadOnlyDao: "curefit-api:QSRFeedbackReadOnlyDao",
    ActivityLoggingBusiness: "curefit-api:ActivityLoggingBusiness",
    PaymentOptionsBusinessV2: "curefit-api:PaymentOptionsBusinessV2",
    PaymentOptionsHandler: "curefit-api:PaymentOptionsHandler",
    EatClpPageHook: "curefit-api:EatClpPageHook",
    EatNowPageHook: "curefit-api:EatNowPageHook",
    FitclubMembershipPageHook: "curefit-api:FitclubMembershipPageHook",
    FitclubMembershipV2PageHook: "curefit-api:FitclubMembershipV2PageHook",
    FitClubActiveMembershipPageHook: "curefit-api:FitClubActiveMembershipPageHook",
    EatLaterPageHook: "curefit-api:EatLaterPageHook",
    DropoutHIWPageHook: "curefit-api:DropoutHIWPageHook",
    WellnessClpHook: "curefit-api:WellnessClpHook",
    CaptchaBusiness: "curefit-api:CaptchaBusiness",
    ReferralDetailsPageConfig: "curefit-api:ReferralDetailsPageConfig",
    ReferPageConfig: "curefit-api:ReferPageConfig",
    GearOrderItemViewBuilder: "curefit-api:GearOrderItemViewBuilder",
    GearShipmentViewBuilder: "curefit-api:GearShipmentViewBuilder",
    GearShipmentViewBuilderV2: "curefit-api:GearShipmentViewBuilderV2",
    CerberusServiceV2: "curefit-api:CerberusServiceV2",
    GearInventoryUnitViewBuilder: "curefit-api:GearInventoryUnitViewBuilder",
    GearInventoryUnitCancelViewBuilder: "curefit-api:GearInventoryUnitCancelViewBuilder",
    GearInventoryUnitViewBuilderV2: "curefit-api:GearInventoryUnitViewBuilderV2",
    GearCatalogueLandingPageService: "curefit-api:GearCatalogueLandingPageService",
    PageService: "curefit-api:PageService",
    BannerService: "curefit-api:BannerService",
    FeatureStateCache: "curefit-api:FeatureStateCache",
    CoachMarkConfig: "curefit-api:CoachMarkConfig",
    CacheHelper: "curefit-api:CacheHelper",
    WebActionTransformerUtil: "curefit-api:WebActionTransformerUtil",
    OrderTrackFirebaseService: "curefit-api:OrderTrackFirebaseService",
    MetricsUtil: "curefit-api:MetricsUtil",
    EnterpriseUtil: "curefit-api:EnterpriseUtil",
    SupportPageConfig: "curefit-api:SupportPageConfig",
    MePageSideBarPageConfig: "curefit-api:MePageSideBarPageConfig",
    CreateUserFormViewBuilder: "curefit-api:CreateUserFormViewBuilder",
    AnnouncementRedisDao: "curefit-api:AnnouncementRedisDao",
    AnnouncementDisplayTimeRedisDao: "curefit-api:AnnouncementDisplayTimeRedisDao",
    AnnouncementBusiness: "curefit-api:AnnouncementBusiness",
    AnnouncementViewBuilder: "curefit-api:AnnouncementViewBuilder",
    OrderNowPageHook: "curefit-api:OrderNowPageHook",
    VoiceBusiness: "curefit-api:VoiceBusiness",
    GymfitCenterPageConfig: "curefit-api:GymfitCenterPageConfig",
    GymsViewBuilder: "curefit-apiGymsViewBuilder",
    UpgradeMembershipViewBuilder: "curefit-api: UpgradeMembershipViewBuilder",
    MembershipAuditTrailViewBuilder: "curefit-api: MembershipAuditTrailViewBuilder",
    WholeFitPageHook: "curefit-api:WholeFitPageHook",
    TransferMembershipViewBuilder: "curefit-api:TransferMembershipViewBuilder",
    FitnessTransferMembershipViewBuilder: "curefit-api:FitnessTransferMembershipViewBuilder",
    NuxViewBuilder: "curefit-api:NuxViewBuilder",
    PtAtHomeViewBuilder: "curefit-api:PtAtHomeViewBuilder",
    FitclubBusiness: "curefit-api:FitclubBusiness",
    PromUtil: "curefit-api:PromUtil",
    CultClassUtil: "curefit-api:CultClassUtil",
    AgentMetricUtil: "curefit-api:AgentMetricUtil",
    CareCenterViewBuilder: "curefit-api:CareCenterViewBuilder",
    AddChangeMealViewBuilderV2: "curefit-api:AddChangeMealViewBuilderV2",
    SelectSpecialityPageViewBuilder: "curefit-api:SelectSpecialityPageViewBuilder",
    CareCenterBrowseViewBuilder: "curefit-api:CareCenterBrowseViewBuilder",
    FitnessReportPageViewBuilder: "curefit-api:FitnessReportPageViewBuilder",
    FitnessReportPageConfig: "curefit-api:FitnessReportPageConfig",
    Cafe: "curefit-api: Cafe",
    OnlineKiosk: "curefit-api: OnlineKiosk",
    FoodMarketplace: "curefit-api: FoodMarketplace",
    WholeFit: "curefit-api: WholeFit",
    UserSleepSchema: "curefit-api:UserSleepSchema",
    UserSleepReadonlyDao: "curefit-api:UserSleepReadonlyDao",
    UserSleepAdapter: "curefit-api:UserSleepAdapter",
    WidgetRankingDao: "curefit-api:WidgetRankingDao",
    WidgetRankingBusiness: "curefit-api:WidgetRankingBusiness",
    UserTokenSchema: "curefit-api:UserTokenSchema",
    UserTokenReadonlyDao: "curefit-api:UserTokenReadonlyDao",
    UserTokenReadWriteDao: "curefit-api:UserTokenReadWriteDao",
    UserTokenAdapter: "curefit-api:UserTokenAdapter",
    UserDeviceSchema: "curefit-api:UserDeviceSchema",
    UserDeviceReadonlyDao: "curefit-api:UserDeviceReadonlyDao",
    UserDeviceReadWriteDao: "curefit-api:UserDeviceReadWriteDao",
    UserDeviceAdapter: "curefit-api:UserDeviceAdapter",
    FitbitCronService: "curefit-api:FitbitCronService",
    SupportFAQSchema: "curefit-api:SupportFAQSchema",
    SupportFAQReadOnlyDaoMongoImpl: "curefit-api:SupportFAQReadOnlyDaoMongoImpl",
    SupportFAQReadWriteDaoMongoImpl: "curefit-api:SupportFAQReadWriteDaoMongoImpl",
    SupportFAQConfigCache: "curefit-api:SupportFAQConfigCache",
    ChatEventLogSchema: "curefit-api:ChatEventLogSchema",
    ChatEventLogReadOnlyDaoMongoImpl: "curefit-api:ChatEventLogReadOnlyDaoMongoImpl",
    ChatEventLogReadWriteDaoMongoImpl: "curefit-api:ChatEventLogReadWriteDaoMongoImpl",
    UserActionMappingBusiness: "curefit-api:UserActionMappingBusiness",
    CultMemoriesViewBuilder: "curefit-api:CultMemoriesViewBuilder",
    EatMarketplaceDataProvider: "curefit-api:EatMarketplaceDataProvider",
    Marketplace: "curefit-api:Marketplace",
    FCPage: "curefit-api: FCPage",
    FMRestaurantListViewBuilder: "curefit-api:FMRestaurantListViewBuilder",
    DigitalCalendarViewBuilder: "curefit-api:DigitalCalendarViewBuilder",
    DigitalReportViewBuilder: "curefit-api:DigitalReportViewBuilder",
    SocialKnowMoreViewBuilder: "curefit-api:SocialKnowMoreViewBuilder",
    DigitalWorkoutMemberHistoryBuilder: "curefit-api:DigitalWorkoutMemberHistoryBuilder",
    DigitalSocialLeagueTabPageViewBuilder: "curefit-api:DigitalSocialLeagueTabPageViewBuilder",
    DigitalLeagueSectionViewBuilder: "curefit-api:DigitalLeagueSectionViewBuilder",
    DigitalMomentsViewBuilder: "curefit-api:DigitalMomentsViewBuilder",
    UserYearEndReportSchema: "curefit-api:UserYearEndReportSchema",
    UserYearEndReportReadOnlyDaoMongoImpl: "curefit-api:UserYearEndReportReadOnlyDaoMongoImpl",
    UserYearEndReportReadWriteDaoMongoImpl: "curefit-api:UserYearEndReportReadWriteDaoMongoImpl",
    UserYearEndSummarySchema: "curefit-api:UserYearEndSummarySchema",
    UserAchievmentShowcaseSchema: "curefit-api:UserAchievmentShowcaseSchema",
    UserYearEndSummaryReadOnlyDaoMongoImpl: "curefit-api:UserYearEndSummaryReadOnlyDaoMongoImpl",
    UserAchievmentShowcaseReadOnlyDaoMongoImpl: "curefit-api:UserAchievmentShowcaseReadOnlyDaoMongoImpl",
    UserYearEndSummaryReadWriteDaoMongoImpl: "curefit-api:UserYearEndSummaryReadWriteDaoMongoImpl",
    UserAchievmentShowcaseReadWriteDaoMongoImpl: "curefit-api:UserAchievmentShowcaseReadWriteDaoMongoImpl",
    LivePtPackPageViewBuilder: "curefit-api:LivePtPackPageViewBuilder",
    DigitalLeagueChallengeViewBuilder: "curefit-api:DigitalLeagueChallengeViewBuilder",
    YogaReportCardWidgetViewBuilder: "curefit-api:YogaReportCardViewBuilder",
    YogaBarGraphWidgetViewBuilder: "curefit-api:YogaBarGraphWidgetBuilder",
    YogaTimeStampedProgressPills: "curefit-api:YogaTimeStampedProgressPills",
    EventInterventionMappingBusiness: "curefit-api:EventInterventionMappingBusiness",

    UserYERCharacterRevealReportSchema: "curefit-api:UserYERCharacterRevealReportSchema",
    UserYERCharacterRevealReportReadOnlyDaoMongoImpl: "curefit-api:UserYERCharacterRevealReportReadOnlyDaoMongoImpl",
    UserYERCharacterRevealReportReadWriteDaoMongoImpl: "curefit-api:UserYERCharacterRevealReportReadWriteDaoMongoImpl",

    // SegmentEvaluator types
    SegmentEvaluator: "curefit-api:SegmentEvaluator",
    CitiesConditionEvaluator: "curefit-api:CitiesConditionEvaluator",
    AreasConditionEvaluator: "curefit-api:AreasConditionEvaluator",
    MealPlanSegmentsConditionEvaluator: "curefit-api:MealPlanSegmentsConditionEvaluator",
    DeliveryChannelConditionEvaluator: "curefit-api:DeliveryChannelConditionEvaluator",
    EmailSetConditionEvaluator: "curefit-api:EmailSetConditionEvaluator",
    UserAgentsConditionEvaluator: "curefit-api:UserAgentsConditionEvaluator",
    CultSegmentsConditionEvaluator: "curefit-api:CultSegmentsConditionEvaluator",
    MindSegmentsConditionEvaluator: "curefit-api:MindSegmentsConditionEvaluator",
    MindTherapySegmentsConditionEvaluator: "curefit-api:MindTherapySegmentsConditionEvaluator",
    PTUserSegmentsConditionEvaluator: "curefit-api:PTUserSegmentsConditionEvaluator",
    LivePTUserSegmentsConditionEvaluator: "curefit-api:LivePTUserSegmentsConditionEvaluator",
    CareSegmentsConditionEvaluator: "curefit-api:CareSegmentsConditionEvaluator",
    SourcesConditionEvaluator: "curefit-api:SourcesConditionEvaluator",
    OSNameConditionEvaluator: "curefit-api:OSNameConditionEvaluator",
    MinAppVersionConditionEvaluator: "curefit-api:MinAppVersionConditionEvaluator",
    MaxAppVersionConditionEvaluator: "curefit-api:MaxAppVersionConditionEvaluator",
    MinCodepushVersionConditionEvaluator: "curefit-api:MinCodepushVersionConditionEvaluator",
    CultDIYSegmentsConditionEvaluator: "curefit-api:CultDIYSegmentsConditionEvaluator",
    MindDIYSegmentsConditionEvaluator: "curefit-api:MindDIYSegmentsConditionEvaluator",
    FitclubConditionEvaluator: "curefit-api:FitclubConditionEvaluator",
    HamletConditionEvaluator: "curefit-api:HamletConditionEvaluator",
    PlatformSegmentConditionEvaluator: "curefit-api:PlatformSegmentConditionEvaluator",
    UserTypeConditionEvaluator: "curefit-api:UserTypeConditionEvaluator",
    EmailSuffixConditionEvaluator: "curefit-api:EmailSuffixConditionEvaluator",
    OffersConditionEvaluator: "curefit-api:OffersConditionEvaluator",
    VerticalsConditionEvaluator: "curefit-api:VerticalsConditionEvaluator",
    GymSegmentsConditionEvaluator: "curefit-api:GymSegmentsConditionEvaluator",
    LiveSGTUserSegmentsConditionEvaluator: "curefit-api:LiveSGTUserSegmentsConditionEvaluator",
    ProductTypeSegmentsConditionEvaluator: "curefit-api:ProductTypeSegmentsConditionEvaluator",
    SupportedCodePushVersionEvaluator: "curefit-api:SupportedCodePushVersionEvaluator",
    CultsportArticleTypeSegmentEvaluator: "curefit-api:CultsportArticleTypeSegmentEvaluator",
    CultsportBrandSegmentEvaluator: "curefit-api:CultsportBrandSegmentEvaluator",
    CultsportCategorySegmentEvaluator: "curefit-api:CultsportCategorySegmentEvaluator",
    CultsportCollectionTypeSegmentEvaluator: "curefit-api:CultsportCollectionTypeSegmentEvaluator",
    CultsportProductIdSegmentEvaluator: "curefit-api:CultsportProductIdSegmentEvaluator",

    GymfitPackPageConfig: "curefit-api:GymfitPackPageConfig",
    GymfitCheckinConfirmationPageConfig: "curefit-api: GymfitCheckinConfirmationPageConfig",
    GymfitCenterDetailsWebViewBuilder: "curefit-api:GymfitCenterDetailsWebViewBuilder",
    GymfitCenterDetailsAppViewBuilder: "curefit-api:GymfitCenterDetailsAppViewBuilder",
    GymfitPackDetailViewBuilder: "curefit-api:GymfitPackDetailViewBuilder",
    PlayPackDetailViewBuilder: "curefit-api:PlayPackDetailViewBuilder",
    PlaySelectPackViewBuilder: "curefit-api:PlaySelectPackViewBuilder",
    PlayPackViewBuilder: "curefit-api:PlayPackViewBuilder",
    PlayUpgradeDetailViewBuilder: "curefit-api:PlayUpgradeDetailViewBuilder",
    GymfitClassScheduleViewBuilder: "curefit-api:GymfitClassScheduleViewBuilder",
    GymfitCheckinViewBuilder: "curefit-api:GymfitCheckinViewBuilder",
    GymLocalitySelectorViewBuilder: "curefit-api:GymLocalitySelectorViewBuilder",
    GymUniversalCheckinViewBuilder: "curefit-api:GymUniversalCheckinViewBuilder",
    GymfitBusiness: "curefit-api:GymfitBusiness",
    SoftBookingBuilder: "curefit-api:SoftBookingBuilder",
    GymfitCheckinConfirmationViewBuilder: "curefit-api:GymfitCheckinConfirmationViewBuilder",
    BranchService: "curefit-api:BranchService",
    GiftCardViewBuilder: "curefit-api:GiftCardViewBuilder",
    AppLayoutBuilder: "curefit-api:AppLayoutBuilder",
    CultCenterPageViewBuilder: "curefit-api:CultCenterPageViewBuilder",
    EatMktPlaceOrderConfirmationBuilder: "curefit-api:EatMktPlaceOrderConfirmationPageBuilder",
    ClassInviteLinkRedisDao: "curefit-api:ClassInviteLinkRedisDao",
    PackInviteLinkRedisDao: "curefit-api:PackInviteLinkRedisDao",
    LiveClassSlotRedisDao: "curefit-api:LiveClassSlotRedisDao",
    ClassInviteLinkCreator: "curefit-api:ClassInviteLinkCreator",
    LiveClassSlotCreator: "curefit-api:LiveClassSlotCreator",
    LivePTBookingDetailViewBuilder: "curefit-api:LivePTBookingDetailViewBuilder",
    LivePTReportPageViewBuilder: "curefit-api:LivePTReportPageViewBuilder",
    SGTOneStepBookingPageViewBuilder: "curefit-api:SGTOneStepBookingPageViewBuilder",
    GiftCardTransformationService: "curefit-api:GiftCardTransformationService",
    ReferralTransformationService: "curefit-api:ReferralTransformationService",
    UserErrorBusiness: "curefit-api:UserErrorBusiness",
    MyReferralViewBuilder: "curefit-api:MyReferralViewBuilder",
    ChallengesViewBuilder: "curefit-api:ChallengesViewBuilder",
    ChallengeDetailsViewBuilder: "curefit-api:ChallengeDetailsViewBuilder",
    MaximusService: "curefit-api:MaximusService",
    // wholefitv2
    WholefitBusiness: "curefit-api: WholefitBusiness",
    WholefitBrandPageBuilder: "curefit-api: WholefitPageBuilder",
    WholefitV2PageHook: "curefit-api: WholefitV2PageHook",
    WholefitProductPageBuilder: "curefit-api: WholefitProductPageBuilder",
    WholefitSearchPageBuilder: "curefit-api: WholefitSearchPageBuilder",
    WholefitProductDetailPageBuilder: "curefit-api: WholefitProducDetailPAgeBuilder",
    CultWorkoutsViewBuilder: "curefit-api:CultWorkoutsViewBuilder",
    CultSchedulePageConfig: "curefit-api:CultSchedulePageConfig",
    CultWorkoutDetailViewBuilder: "curefit-api:CultWorkoutDetailViewBuilder",
    CFAPIJavaService: "curefit-api:CFAPIJavaService",
    // recipe/eat.live
    RecipeClpProductsViewBuilder: "curefit-api:RecipeClpProductsViewBuilder",
    RecipeSearchBuilder: "curefit-api:RecipeSearchBuilder",
    RecipeCollectionDetailViewBuilder: "curefit-api:RecipeCollectionDetailViewBuilder",

    // digital payments
    LivePacksViewBuilder: "curefit-api:LivePacksViewBuilder",
    LiveMembershipExpiredModalViewBuilder: "curefit-api:LiveMembershipExpiredModalViewBuilder",
    TLLivePacksModalViewBuilder: "curefit-api:TLLivePacksModalViewBuilder",
    LiveMembershipViewBuilder: "curefit-api:LiveMembershipViewBuilder",
    DigitalSessionListViewBuilder: "curefit-api:DigitalSessionListViewBuilder",
    OnDemandProductViewBuilder: "curefit-api: OnDemandProductViewBuilder",
    CareCultDayTransferPageViewBuilder: "curefit-api:CareCultDayTransferPageViewBuilder",
    OnDemandAutoPlayBuilder: "curefit-api: OnDemandAutoPlayBuilder",

    ErrorFactory: "curefit-api:ErrorFactory",
    CareBusiness: "curefit-api:CareBusiness",
    MindTherapyPhysioPacksProductPageViewBuilder: "curefit-api:MindTherapyPhysioPacksProductPageViewBuilder",
    GP99ProductPageViewBuilder: "curefit-api:GP99ProductPageViewBuilder",

    LivePremiereProductViewBuilder: "curefit-api: LivePremiereProductViewBuilder",
    // deeeplink
    DeepLinkService: "curefit-api: DeepLinkService",

    // feedback
    AppFeedbackRedisDao: "curefit-api:AppFeedbackRedisDao",
    AppFeedbackService: "curefit-api:AppFeedbackService",

    // segment as a service
    SegmentService: "curefit-api:SegmentService",

    // external sources integration
    ExternalSourceUtil: "curefit-api:ExternalSourceUtil",
    CustomEventEmitter: "curefit-api:CustomEventEmitter",
    GympassConf: "curefit-api:GympassConf",

    LiveClassDetailViewBuilderTV: "curefit-api:LiveClassDetailViewBuilderTV",

    // App Config Service
    AppConfigServiceUtil: "curefit-api:AppConfigServiceUtil",
    AppConfigStoreService: "curefit-api:AppConfigStoreService",
    // payment config
    PaymentOptionsViewBuilder: "curefit-api:PaymentOptionsViewBuilder",
    PaymentOptionViewFactory: "curefit-api:PaymentOptionViewFactory",
    SeriesViewBuilder: "curefit-api:SeriesViewBuilder",

    PNTokenEventQueueListener: "curefit-api:PNTokenEventQueueListener",

    SegmentOverrideBusiness: "curefit-api:SegmentOverrideBusiness",
    UserYearlyReportBusiness: "curefit-api:UserYearlyReportBusiness",
    UserQuiz2021Business: "curefit-api:UserQuiz2021Business",
    UserYearReport2023Business: "curefit-api:UserYearReport2023Business",
    SGTPreBookingDetailPageViewBuilder: "curefit-api:SGTPreBookingDetailPageViewBuilder",

    FitnesshubPageHook: "curefit-api:FitnesshubPageHook",
    SportshubPageHook: "curefit-api:SportshubPageHook",
    CultBike: "curefit-api:Cultbike",

    ProgramPageHook: "curefit-api:ProgramPageHook",
    CoachProgramPageHook: "curefit-api:CoachProgramPageHook",

    // vertical specific feedback consumers
    VerticleSpecificFeedbackConsumer: "curefit-api:VerticleSpecificFeedbackConsumer",
    GearFeedbackSNSProducer: "curefit-api:GearFeedbackSNSProducer",

    TataNeuService: "curefit-api:TataNeuService",
    TataNeuAuthMiddleware: "curefit-api:TataNeuAuthMiddleware",
    MixpanelEventService: "curefit-api:MixpanelEventService",
    IndusService: "curefit-api:IndusService",
    CFAnalytics: "curefit-api:Analytics",
    CFAnalyticsConfig: "curefit-api:CFAnalyticsConfig",
    NestService: "curefit-api:NestService",
    DIYPackService: "curefit-api:DIYPackService",

    TesseractServiceConf: "curefit-api:TesseractServiceConf",
    MystiqueServiceConf: "curefit-api:MystiqueServiceConf",
    CultstoreShopifyConf: "curefit-api:CultstoreShopifyConf",
    CultstoreShopifyService: "curefit-api:CultstoreShopifyService"
}

export default CUREFIT_API_TYPES
