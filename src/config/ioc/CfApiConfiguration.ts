import { inject, injectable } from "inversify"
import { AbstractConfiguration, BASE_TYPES, Logger } from "@curefit/base"

@injectable()
export class CfApiConfiguration extends AbstractConfiguration {
    constructor(
        @inject(BASE_TYPES.ConfigurationDirectory) protected appConfDir: string,
        @inject(BASE_TYPES.ConfigurationFilePath) protected appConfFilePath: string
    ) {
        super(appConfDir, appConfFilePath)
    }

}
