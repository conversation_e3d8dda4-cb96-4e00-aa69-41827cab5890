import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express"
import TAGS from "./tags"
import * as Inversify from "inversify"
import { TYPE, interfaces } from "inversify-express-utils"

import authControllerFactory from "../../app/auth/AuthController"
import cleverControllerFactory from "../../app/cleverTap/CleverTapController"
import userControllerFactory from "../../app/user/UserController"
import CRMControllerFactory from "../../app/crm/CRMController"
import cartControllerFactory from "../../app/cart/CartController"
import mealPlannerControllerFactory from "../../app/mealplanner/MealPlannerController"
import onboardingControllerFactory from "../../app/onboarding/OnboardingController"
import gearCartControllerFactory from "../../app/cart/GearCartController"
import pageControllerFactory from "../../app/page/PageController"
import cronControllerFactory from "../../app/cron/CronController"

import deviceControllerFactory from "../../app/device/DeviceController"
import productControllerFactory from "../../app/product/ProductController"
import logisticsControllerFactory from "../../app/logistics/LogisticsController"
import urbanPiperControllerFactory from "../../app/order/external/urbanpiper/UrbanPiperController"
import kitchensAtControllerFactory from "../../app/order/external/kitchensat/KitchensAtController"


import paymentControllerFactory from "../../app/payment/PaymentController"
import fuseControllerFactory from "../../app/care/FuseController"
import twilioControllerFactory from "../../app/care/TwilioController"
import recipeControllerFactory from "../../app/recipe/RecipeController"
import packControllerFactory from "../../app/pack/PackController"
import programControllerFactory from "../../app/program/ProgramController"
import cultControllerFactory from "../../app/cult/CultController"
import cyclingControllerFactory from "../../app/cycling/CyclingController"
import pulseControllerFactory from "../../app/pulse/PulseController"
import cityControllerFactory from "../../app/city/CityController"
import countryControllerFactory from "../../app/city/CountryController"

import orderControllerFactory from "../../app/order/OrderController"
import feedbackControllerFactory from "../../app/ugc/FeedbackController"
import notificationControllerFactory from "../../app/ugc/NotificationController"
import leadControllerFactory from "../../app/lead/LeadController"

import careControllerFactory from "../../app/care/CareController"
import chronicCareControllerFactory from "../../app/care/ChronicCareController"
import userFormControllerFactory from "../../app/cfs/UserFormController"
import atlasControllerFactory from "../../app/atlas/AtlasController"

import { Container, ContainerModule } from "inversify"
import QuestControllerFactory from "../../app/quest/QuestController"
import SitemapControllerFactory from "../../app/sitemap/SitemapController"
import statusControllerFactory from "../../app/server/StatusController"
import InterventionController from "../../app/intervention/InterventionController"
import kernel from "./ioc"
import MaximusControllerFactory from "../../app/maximus/MaximusController"
import MetricControllerFactory from "../../app/metrics/MetricsController"
import ExtLogisticsControllerFactory from "../../app/order/external/logistics/ExtLogisticsController"
import UserTestControllerfactory from "../../app/usertest/UserTestController"
import ProductFeedControllerFactory from "../../app/productFeed/ProductFeedController"
import TPOffersControllerFactory from "../../app/offer/external/TPOffersController"
import ReferralControllerFactory from "../../app/referral/ReferralController"
import orderItemControllerFactory from "../../app/gear/orderItem/GearOrderItemController"
import GearInventoryUnitControllerFactory from "../../app/gear/inventoryUnit/GearInventoryUnitController"
import GearInventoryUnitControllerV2Factory from "../../app/gear/inventoryUnit/GearInventoryUnitControllerV2"
import GearWebhooksControllerFactory from "../../app/external/GearWebhooksController"
import GearShipmentControllerFactory from "../../app/gear/shipments/GearShipmentController"
import QSRControllerFactory from "../../app/order/qsr/QSRController"
import FitcashControllerFactory from "../../app/fitcash/FitcashController"
import UserGoalControllerFactory from "../../app/user/UserGoalController"
import { LiveTrackingControllerFactory } from "../../app/eat/LiveTrackingController"
import CouponControllerFactory from "../../app/coupon/CouponController"
import ssoAuthControllerFactory from "../../app/sso/SSOAuthenticatedController"
import gymfitControllerFactory from "../../app/gymfit/GymfitController"
import VoiceControllerFactory from "../../app/voice/VoiceController"
import FitclubControllerFactory from "../../app/fitclub/FitclubController"
import GiftCardControllerFactory from "../../app/giftcards/GiftCardController"
import { MyGateControllerFactory } from "../../app/eat/MyGateController"
import { EatMarketplaceControllerFactory } from "../../app/eat/MarketPlaceController"
import digitalControllerFactory from "../../app/digital/DigitalController"
import ChallengesControllerFactory from "../../app/challenges/ChallengesController"
import { WholefitControllerFactory } from "../../app/wholefit/WholefitController"
import SessionControllerFactory from "../../app/auth/SessionController"
import digitalInSessionControllerFactory from "../../app/digital/DigitalInSessionController"
import { onDemandControllerFactory } from "../../app/page/ondemand/OnDemandLibraryController"
import { AdvertisementControllerFactory } from "../../app/advertisement/AdvertisementController"
import FaqControllerFactory from "../../app/common/faq/FaqController"
import { SegmentOverrideControllerFactory } from "../../app/page/vm/segmentoverride/SegmentOverrideController"
import trainerLedControllerFactory  from "../../app/trainerLed/controller"
import tataControllerFactory from "../../app/tata/TataController"
import GearControllerFactory from "../../app/gear/GearController"
import playControllerFactory from "../../app/play/PlayController"
import tataNeuControllerFactory from "../../app/external/TataNeuController"
import transformControllerFactory from "../../app/transform/TransformController"
import VoucherControllerFactory from "../../app/voucher/VoucherController"
import GiftVoucherControllerFactory from "../../app/giftVoucher/GiftVoucherController"
import fitnessControllerFactory from "../../app/fitness/FitnessController"
import cultstoreShopifyControllerFactory from "../../app/cultstoreShopify/CultstoreShopifyController"
import openAIControllerfactory from "../../app/transform/OpenAIController"

function controllerBindingFactory(kernel: Inversify.Container) {
    const ControllerKernelModule = new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {
        const ssoController = ssoAuthControllerFactory(kernel)
        const sitemapController = SitemapControllerFactory(kernel)
        const authController = authControllerFactory(kernel)
        const cleverController = cleverControllerFactory(kernel)
        const CRMController = CRMControllerFactory(kernel)
        const cartController = cartControllerFactory(kernel)
        const onboardingController = onboardingControllerFactory(kernel)
        const gearCartController = gearCartControllerFactory(kernel)
        const deviceController = deviceControllerFactory(kernel)
        const productController = productControllerFactory(kernel)
        const logisticsController = logisticsControllerFactory(kernel)
        const urbanPiperController = urbanPiperControllerFactory(kernel)
        const kitchensAtController = kitchensAtControllerFactory(kernel)
        const ExtLogisticsController = ExtLogisticsControllerFactory(kernel)
        const paymentController = paymentControllerFactory(kernel)
        const fuseController = fuseControllerFactory(kernel)
        const twilioController = twilioControllerFactory(kernel)
        const recipeController = recipeControllerFactory(kernel)
        const programController = programControllerFactory(kernel)
        const packController = packControllerFactory(kernel)
        const pulseController = pulseControllerFactory(kernel)
        const cityController = cityControllerFactory(kernel)
        const countryController = countryControllerFactory(kernel)
        const orderController = orderControllerFactory(kernel)
        const QSRController = QSRControllerFactory(kernel)
        const careController = careControllerFactory(kernel)
        const chronicCareController = chronicCareControllerFactory(kernel)
        const leadController = leadControllerFactory(kernel)
        const QuestController = QuestControllerFactory(kernel)
        const ondemandController = onDemandControllerFactory(kernel)
        const advertisementController = AdvertisementControllerFactory(kernel)
        const faqController = FaqControllerFactory(kernel)
        const segmentOverrideController = SegmentOverrideControllerFactory(kernel)
        const trainerLedController = trainerLedControllerFactory(kernel)
        const interventionController = InterventionController(kernel)
        const MaximusController = MaximusControllerFactory(kernel)
        const MetricController = MetricControllerFactory(kernel)
        const UserTestController = UserTestControllerfactory(kernel)
        const ProductFeedController = ProductFeedControllerFactory(kernel)
        const TPOffersController = TPOffersControllerFactory(kernel)
        const orderItemController = orderItemControllerFactory(kernel)
        const GearInventoryUnitController = GearInventoryUnitControllerFactory(kernel)
        const GearInventoryUnitControllerV2 = GearInventoryUnitControllerV2Factory(kernel)
        const GearWebhooksController = GearWebhooksControllerFactory(kernel)
        const GearShipmentController = GearShipmentControllerFactory(kernel)
        const UserGoalController = UserGoalControllerFactory(kernel)
        const userFormController = userFormControllerFactory(kernel)
        const atlasController = atlasControllerFactory(kernel)
        const LiveTrackingController = LiveTrackingControllerFactory(kernel)
        const referralController = ReferralControllerFactory(kernel)
        const fitcashController = FitcashControllerFactory(kernel)
        const CouponController = CouponControllerFactory(kernel)
        const gymfitController = gymfitControllerFactory(kernel)
        const playController = playControllerFactory(kernel)
        const VoiceController = VoiceControllerFactory(kernel)
        const FitclubConroller = FitclubControllerFactory(kernel)
        const MyGateController = MyGateControllerFactory(kernel)
        const cronController = cronControllerFactory(kernel)
        const eatmarketplaceController = EatMarketplaceControllerFactory(kernel)
        const digitalController = digitalControllerFactory(kernel)
        const wholefitController = WholefitControllerFactory(kernel)
        const GiftCardController = GiftCardControllerFactory(kernel)
        const MealPlannerController = mealPlannerControllerFactory(kernel)
        const ChallengesController = ChallengesControllerFactory(kernel)
        const sessionController = SessionControllerFactory(kernel)
        const statusController = statusControllerFactory(kernel)
        const cultController = cultControllerFactory(kernel)
        const cyclingController = cyclingControllerFactory(kernel)
        const digitalInSessionController = digitalInSessionControllerFactory(kernel)
        const userController = userControllerFactory(kernel)
        const pageController = pageControllerFactory(kernel)
        const feedbackController = feedbackControllerFactory(kernel)
        const notificationController = notificationControllerFactory(kernel)
        const tataController = tataControllerFactory(kernel)
        const GearController = GearControllerFactory(kernel)
        const TransformController = transformControllerFactory(kernel)
        const OpenAICOntroller = openAIControllerfactory(kernel)
        const VoucherController = VoucherControllerFactory(kernel)
        const GiftVoucherController = GiftVoucherControllerFactory(kernel)
        const fitnessController = fitnessControllerFactory(kernel)
        tataNeuControllerFactory(kernel)
        cultstoreShopifyControllerFactory(kernel)
        // NOTE: add controllers in increasing throughput order, ensures lesser regex match operations
    })
    return ControllerKernelModule
}

export default controllerBindingFactory
