import "reflect-metadata"
import * as Inversify from "inversify"
import { BASE_TYPES, Configuration } from "@curefit/base"
import { CfApiConfiguration } from "./CfApiConfiguration"
import CUREFIT_API_TYPES from "./types"
import { AppConfigStoreService, IAppConfigStoreService } from "../../app/appConfig/AppConfigStoreService"

export function AppConfigKernelModule(kernel: Inversify.Container) {
    return new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {
        bind<string>(BASE_TYPES.ConfigurationDirectory).toConstantValue("conf")
        bind<string>(BASE_TYPES.ConfigurationFilePath).toConstantValue(`conf/${process.env.APP_ENV}.json`)
        bind<CfApiConfiguration>(BASE_TYPES.Configuration).to(CfApiConfiguration).inSingletonScope()
        bind<IAppConfigStoreService>(CUREFIT_API_TYPES.AppConfigStoreService).to(AppConfigStoreService).inSingletonScope()
    })
}
