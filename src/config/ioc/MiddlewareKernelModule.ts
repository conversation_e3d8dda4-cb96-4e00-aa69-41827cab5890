import AuthMiddleware from "../../app/auth/AuthMiddleware"
import CUREFIT_API_TYPES from "./types"
import * as Inversify from "inversify"
import { ApiErrorCountMiddleware } from "../../app/auth/ApiErrorCountMiddleware"
import { TataNeuAuthMiddleware } from "../../app/external/TataNeuAuthMiddleware"
import { MultiDeviceHandlingMiddleware } from "../../app/middleware/MultiDeviceHandlingMiddleware"

const MiddlewareKernelModule = new Inversify.ContainerModule((bind: Inversify.interfaces.Bind) => {
    bind<AuthMiddleware>(CUREFIT_API_TYPES.AuthMiddleware).to(AuthMiddleware).inSingletonScope()
    bind<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware).to(ApiErrorCountMiddleware).inSingletonScope()
    bind<TataNeuAuthMiddleware>(CUREFIT_API_TYPES.TataNeuAuthMiddleware).to(TataNeuAuthMiddleware).inSingletonScope()
    bind<MultiDeviceHandlingMiddleware>(CUREFIT_API_TYPES.MultiDeviceHandlingMiddleware).to(MultiDeviceHandlingMiddleware).inSingletonScope()
})

export default MiddlewareKernelModule
