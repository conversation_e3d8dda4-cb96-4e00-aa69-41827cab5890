import kernel from "./ioc"
import businessKernelModuleFactory from "./BusinessKernelModule"
import MiddlewareKernelModule from "./MiddlewareKernelModule"
import controllerBindingFactory from "./ControllerKernelModule"
import { AppConfigKernelModule } from "./AppConfigModule"
import { FitcashClientModule } from "@curefit/fitcash-client"
import { ReferralClientModule } from "@curefit/referral-client"
import { BaseKernelModule } from "@curefit/base"
import { RedisModule } from "@curefit/redis-utils"
import { CacheModule } from "@curefit/cache-utils"
import { RecommendationClientModule } from "@curefit/recommendation-client"
import { EatModule } from "@curefit/eat"
import { MongoModule } from "@curefit/mongo-utils"
import { ConstelloClientModule } from "@curefit/constello-client"
import { HamletModule } from "@curefit/hamlet-node-sdk"
import { LocationModule } from "@curefit/location-mongo"
import { FitClubClientModule } from "@curefit/fitclub-client"
import { EatApiClientModule } from "@curefit/eat-api-client"
import { FoodModule } from "@curefit/food-mongo"
import { ReportIssuesClientModule } from "@curefit/report-issues-client"
import { UserModelsModule } from "@curefit/user-models"
import { HofundModule } from "@curefit/hofund"
import { ErrorCommonModule } from "@curefit/error-common"
import { InvoiceModelsModule } from "@curefit/invoice-models"
import { ShipmentModelsModule } from "@curefit/shipment-models"
import { DeliveryClientModule } from "@curefit/delivery-client"
import { CatalogClientModule } from "@curefit/catalog-client"
import { MasterchefModelsModule } from "@curefit/masterchef-models"
import { MysqlModule } from "@curefit/mysql-utils"
import { SqsClientModule } from "@curefit/sqs-client"
import { CacheClientModule } from "@curefit/cache-client"
import { BaseUtilsModule } from "@curefit/base-utils"
import { AuthModelsModule } from "@curefit/auth-models"
import { DeliveryModelsClientModule } from "@curefit/delivery-models"
import { AlfredClientModule } from "@curefit/alfred-client"
import { OrderModelsModule } from "@curefit/order-models"
import { CaesarClientModule } from "@curefit/caesar-client"
import { CaesarModelsModule } from "@curefit/caesar-models"
import { CounterModule } from "@curefit/counter-mongo"
import { MasterchefClientModule } from "@curefit/masterchef-client"
import { LockModule } from "@curefit/lock-utils"
import { CultClientModule } from "@curefit/cult-client"
import { SocialClientModule } from "@curefit/social-client"
import { ProductMongoModule } from "@curefit/product-mongo"
import { DiyClientModule } from "@curefit/diy-client"
import { UserClientModule } from "@curefit/user-client"
import { DeviceModelsModule } from "@curefit/device-models"
import { KitchenModule } from "@curefit/kitchen"
import { PageConfigModule } from "@curefit/config-mongo"
import { OfferServiceClientModule } from "@curefit/offer-service-client"
import { UserTestClientModule } from "@curefit/user-test-client"
import { CloudwatchClientModule } from "@curefit/cloudwatch-client"
import { IssueModelsModule } from "@curefit/issue-models"
import { AlbusClientModule } from "@curefit/albus-client"
import { ProductServiceModule } from "@curefit/product-service"
import { ProgramClientModule } from "@curefit/program-client"
import { BaseMongoModule } from "@curefit/base-mongo"
import { LocationServiceModule } from "@curefit/location-service"
import { VmModelsModule } from "@curefit/vm-models"
import { FlashClientModule } from "@curefit/flash-client"
import { MaverickModule } from "@curefit/maverick-client"
import { CFSClientModule } from "@curefit/cfs-client"
import { GMFClientModule } from "@curefit/gmf-client"
import { MetricServiceModule } from "@curefit/metrics"
import { GearVaultClientModule } from "@curefit/gearvault-client"
import { SmsClientModule } from "@curefit/sms-client"
import { QuestClientModule } from "@curefit/quest-client"
import { InterventionModelsModule } from "@curefit/intervention-models"
import { QuestModelsModule } from "@curefit/quest-models"
import { FeedbackMongoModule } from "@curefit/feedback-mongo"
import { AtlasClientModule } from "@curefit/atlas-client"
import { LoggingModelsModule } from "@curefit/logging-models"
import { HerculesModelsModule } from "@curefit/hercules-models"
import { LoggingClientModule } from "@curefit/logging-client"
import { IrisClientModule } from "@curefit/iris-client"
import { RamsayClientModule } from "@curefit/ramsay-client"
import { PaymentModelsModule } from "@curefit/payment-models"
import { PaymentClientModule } from "@curefit/payment-client"
import { MaxmindClientModule } from "@curefit/maxmind-client"
import { FlashModelsModule } from "@curefit/flash-models"
import { ConstelloModule } from "@curefit/constello-models"
import { SegmentationModule } from "@curefit/segmentation-client"
import { EventsModule } from "@curefit/events-util"
import { GymfitClientModule } from "@curefit/gymfit-client"
import { FinanceModelsModule } from "@curefit/finance-models"
import { UserMessageModule } from "@curefit/user-message-mongo"
import { UserMessageTranslatorModule } from "@curefit/user-message-translator"
import { RiddlerClientModule } from "@curefit/riddler-client"
import { WholefitApiClientModule } from "@curefit/wholefit-api-client"
import { ExpressionManagedFunctionsModule } from "@curefit/expression-managed-functions"
import { ExpressionModule } from "@curefit/expression-utils"
import { RiddlerCacheModule } from "@curefit/riddler-cache"
import { RiddlerModelsModule } from "@curefit/riddler-common"
import { RewardClientModule } from "@curefit/reward-client"
import { SegmentationClientModule } from "@curefit/segmentation-service-client"
import { CacheServiceClientModule } from "@curefit/cache-service-client"
import { FuseClientModule } from "@curefit/fuse-node-client"
import { OllivanderClientModule } from "@curefit/ollivander-node-client"
import { EhrClientModule } from "@curefit/ehr-client"
import { RashiClientModule } from "@curefit/rashi-client"
import { AppConfigClientModule } from "@curefit/app-config-client"
import { MediaGatewayClientModule } from "@curefit/media-gateway-js-client"
import { HerculesClientModule } from "@curefit/hercules-client"
import { ufsClientModule } from "@curefit/ufs-client"
import { FoodwayClientModule } from "@curefit/foodway-client"
import { MembershipClientModule } from "@curefit/membership-client"
import { CenterServiceClientModule } from "@curefit/center-service-client"
import { IdentityClientModule } from "@curefit/identity-client"
import { OmsAPIClientModule } from "@curefit/oms-api-client"
import { CurioClientModule } from "@curefit/curio-client"
import { HamletClientModule } from "@curefit/hamlet-client"
import { CultBikeApiModule } from "@curefit/cult-bike-client"
import { EnterpriseModule } from "@curefit/enterprise-client"
import { ThirdPartyClientModule } from "@curefit/third-party-integrations-client"
import { PersonalTrainingClientModule } from "@curefit/personal-training-v2-client"
import { MagnetoClientModule } from "@curefit/magneto-client"
import { ClientModule as CultsportFeedbackClientModule } from "@curefit/cultsport-feedback-client"
import { SportsApiClientModule } from "@curefit/sports-api-client-node"
import { CafeApiClientKernelModule } from "@curefit/cafe-api-client"
import { ConfigStoreClientModule } from "@curefit/config-store-client"
import { IndusClientModule } from "@curefit/indus-client"
import { NestClientModule } from "@curefit/nest-node-client"
import { PackManagementClientModule } from "@curefit/pack-management-service-client"
import { SprinklrClientModule } from "@curefit/sprinklr-node-client"
export class Loader {

    loadWorkerBindings(): void {
        console.log("WorkerLoader Binding started")

        // Load app config
        const AppConfigModule = AppConfigKernelModule(kernel)
        kernel.load(AppConfigModule)

        kernel.load(MongoModule(kernel))
        kernel.load(BaseKernelModule(kernel))

        kernel.load(RedisModule(kernel))
        kernel.load(CacheClientModule(kernel))
        kernel.load(CounterModule(kernel))
        kernel.load(LockModule(kernel))
        kernel.load(CloudwatchClientModule(kernel))
        kernel.load(ProductMongoModule(kernel))
        kernel.load(BaseUtilsModule(kernel))
        kernel.load(UserMessageModule(kernel))
        kernel.load(AuthModelsModule(kernel))
        kernel.load(BaseMongoModule(kernel))
        kernel.load(PageConfigModule(kernel))
        kernel.load(CacheModule(kernel))
        kernel.load(ProductServiceModule(kernel))
        kernel.load(KitchenModule(kernel))
        kernel.load(DiyClientModule(kernel))
        kernel.load(AppConfigClientModule(kernel))
        kernel.load(CenterServiceClientModule(kernel))
        kernel.load(CultClientModule(kernel))
        kernel.load(SocialClientModule(kernel))
        kernel.load(OrderModelsModule(kernel))
        kernel.load(DeviceModelsModule(kernel))
        kernel.load(CacheServiceClientModule(kernel))
        kernel.load(CaesarModelsModule(kernel))
        kernel.load(CaesarClientModule(kernel))
        kernel.load(FoodModule(kernel))
        kernel.load(MasterchefModelsModule(kernel))
        kernel.load(MasterchefClientModule(kernel))
        kernel.load(UserModelsModule(kernel))
        kernel.load(HofundModule(kernel))
        kernel.load(ErrorCommonModule(kernel))
        kernel.load(ShipmentModelsModule(kernel))
        kernel.load(DeliveryModelsClientModule(kernel))
        kernel.load(DeliveryClientModule(kernel))
        kernel.load(CatalogClientModule(kernel))
        kernel.load(MysqlModule(kernel))
        kernel.load(SqsClientModule(kernel))
        kernel.load(SmsClientModule(kernel))
        kernel.load(ProgramClientModule(kernel))
        kernel.load(AlfredClientModule(kernel))
        kernel.load(UserClientModule(kernel))
        kernel.load(OfferServiceClientModule(kernel))
        kernel.load(IssueModelsModule(kernel))
        kernel.load(UserTestClientModule(kernel))
        kernel.load(LocationServiceModule(kernel))
        kernel.load(VmModelsModule(kernel))
        kernel.load(FlashClientModule(kernel))
        kernel.load(MaverickModule(kernel))
        kernel.load(CFSClientModule(kernel))
        kernel.load(GMFClientModule(kernel))
        kernel.load(MetricServiceModule(kernel))
        kernel.load(GearVaultClientModule(kernel))
        kernel.load(QuestClientModule(kernel))
        kernel.load(InterventionModelsModule(kernel))
        kernel.load(QuestModelsModule(kernel))
        kernel.load(FeedbackMongoModule(kernel))
        kernel.load(EnterpriseModule())
        kernel.load(AtlasClientModule(kernel))
        kernel.load(LoggingModelsModule(kernel))
        kernel.load(LoggingClientModule(kernel))
        kernel.load(HerculesModelsModule(kernel))
        kernel.load(IrisClientModule(kernel))
        kernel.load(RamsayClientModule(kernel))
        kernel.load(FitcashClientModule(kernel))
        kernel.load(ReferralClientModule(kernel))
        kernel.load(ConstelloClientModule(kernel))
        kernel.load(EatModule(kernel))
        kernel.load(EatApiClientModule(kernel))
        kernel.load(RecommendationClientModule(kernel))
        kernel.load(HamletModule(kernel))
        kernel.load(LocationModule(kernel))
        kernel.load(FitClubClientModule(kernel))
        kernel.load(ReportIssuesClientModule(kernel))
        kernel.load(InvoiceModelsModule(kernel))
        kernel.load(PaymentModelsModule(kernel))
        kernel.load(PaymentClientModule(kernel))
        kernel.load(MaxmindClientModule(kernel))
        kernel.load(FlashModelsModule(kernel))
        kernel.load(ConstelloModule(kernel))
        kernel.load(SegmentationModule(kernel))
        kernel.load(EventsModule(kernel))
        kernel.load(GymfitClientModule(kernel))
        kernel.load(ufsClientModule(kernel))
        kernel.load(FinanceModelsModule(kernel))
        kernel.load(RiddlerClientModule(kernel))
        kernel.load(UserMessageTranslatorModule(kernel))
        kernel.load(WholefitApiClientModule(kernel))
        kernel.load(SegmentationClientModule(kernel))
        kernel.load(
            ExpressionModule(kernel),
            ExpressionManagedFunctionsModule(kernel),
            RiddlerCacheModule(kernel),
            RiddlerModelsModule(kernel),
            RewardClientModule(kernel)
        )
        kernel.load(AlbusClientModule(kernel))
        kernel.load(HerculesClientModule(kernel))
        kernel.load(RashiClientModule(kernel))
        kernel.load(FuseClientModule(kernel))
        kernel.load(OllivanderClientModule(kernel))
        kernel.load(EhrClientModule(kernel))
        kernel.load(MediaGatewayClientModule(kernel))
        kernel.load(MembershipClientModule(kernel))
        kernel.load(FoodwayClientModule(kernel))
        kernel.load(OmsAPIClientModule(kernel))
        kernel.load(CurioClientModule(kernel))
        kernel.load(HamletClientModule(kernel))
        kernel.load(CultBikeApiModule(kernel))
        kernel.load(IdentityClientModule(kernel))
        kernel.load(ThirdPartyClientModule(kernel))
        kernel.load(PersonalTrainingClientModule(kernel))
        kernel.load(MagnetoClientModule(kernel))
        kernel.load(CultsportFeedbackClientModule(kernel))
        kernel.load(SportsApiClientModule(kernel))
        kernel.load(CafeApiClientKernelModule(kernel))
        kernel.load(ConfigStoreClientModule(kernel))
        kernel.load(IndusClientModule(kernel))
        kernel.load(NestClientModule(kernel))
        kernel.load(PackManagementClientModule(kernel))
        kernel.load(SprinklrClientModule(kernel))

        // Next load Business
        const BusinessKernelModule = businessKernelModuleFactory(kernel)
        kernel.load(BusinessKernelModule)

        // Next load Middleware
        kernel.load(MiddlewareKernelModule)

        // And lastly load all Controllers
        const ControllerKernelModule = controllerBindingFactory(kernel)
        kernel.load(ControllerKernelModule)
        console.log("WorkerLoader Binding finished")

    }
}
