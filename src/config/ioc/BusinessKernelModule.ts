import CUREFIT_API_TYPES from "./types"
import { ContainerModule, interfaces, Container } from "inversify"

import IDeviceBusiness from "../../app/device/IDeviceBusiness"
import DeviceBusiness from "../../app/device/DeviceBusiness"
import IProductBusiness from "../../app/product/IProductBusiness"
import ProductBusiness from "../../app/product/ProductBusiness"
import ProgramBusiness from "../../app/program/ProgramBusiness"
import IUserBusiness from "../../app/user/IUserBusiness"
import UserBusiness from "../../app/user/UserBusiness"
import IRecommendationBusiness from "../../app/user/IRecommendationBusiness"
import RecommendationBusiness from "../../app/user/RecommendationBusiness"
import FirebaseService from "../../app/common/firebase/FirebaseService"
import IFirebaseService from "../../app/common/firebase/IFirebaseService"
import IPageBuilder from "../../app/page/IPageBuilder"
import IInfoPageBuilder from "../../app/page/InfoPageBuilder"
import InfoPageBuilder from "../../app/page/InfoPageBuilder"
import HomePageConfig from "../../app/page/HomePageConfig"
import MealPackPageConfig from "../../app/pack/MealPackPageConfig"
import CultPackPageConfig from "../../app/pack/CultPackPageConfig"
import CultDIYPackPageConfig from "../../app/pack/CultDIYPackPageConfig"
import MindDIYPackPageConfig from "../../app/pack/MindDIYPackPageConfig"
import CultLandingPageBuilderV2 from "../../app/page/CultLandingPageBuilderV2"
import BaseCultLandingPageConfig from "../../app/page/BaseCultLandingPageConfig"
import CultLandingPageConfigV2 from "../../app/page/CultLandingPageConfigV2"
import MindLandingPageConfigV2 from "../../app/page/MindLandingPageConfigV2"
import EatLandingPageBuilderV3 from "../../app/page/EatLandingPageBuilderV3"
import EatLandingPageBuilderV4 from "../../app/page/EatLandingPageBuilderV4"
import MindLandingPageBuilderV2 from "../../app/page/MindLandingPageBuilderV2"
import EatLandingPageConfig from "../../app/page/EatLandingPageConfig"
import { IClassListViewBuilder } from "../../app/cult/ClassListViewBuilder"
import { CenterBookingCache, CenterBookingCacheImpl } from "../../app/cult/CenterBookingCache"
import ClassListViewBuilder from "../../app/cult/ClassListViewBuilder"
import ClassListViewBuilderV1 from "../../app/cult/ClassListViewBuilderV1"
import ClassListViewBuilderV2 from "../../app/cult/ClassListViewBuilderV2"
import ITimelineBusiness from "../../app/user/ITimelineBusiness"
import { TimelineBusinessFactoryV1 } from "../../app/user/TimelineBusinessV1"
import { TimelineBusinessV4 } from "../../app/user/TimelineBusinessV4"
import OrderViewBuilder from "../../app/order/OrderViewBuilder"
import CartViewBuilder from "../../app/cart/CartViewBuilder"
import GearCartViewBuilder from "../../app/cart/GearCartViewBuilder"
import OrderProductDetailBuilder from "../../app/order/OrderProductDetailBuilder"
import BaseOrderConfirmationViewBuilder from "../../app/order/BaseOrderConfirmationViewBuilder"
import OrderConfirmationViewBuilder from "../../app/order/OrderConfirmationViewBuilder"
import OrderConfirmationViewBuilderV1 from "../../app/order/OrderConfirmationViewBuilderV1"
import GearOrderViewBuilder from "../../app/order/GearOrderViewBuilder"
import GearOrderWithShipmentViewBuilder from "../../app/order/GearOrderWithShipmentViewBuilder"
import GearShipmentViewBuilderV2 from "../../app/order/GearShipmentViewBuilderV2"
import ActivePackViewBuilderV1 from "../../app/user/ActivePackViewBuilderV1"
import RecommendationViewBuilder from "../../app/user/RecommendationViewBuilder"
import FeedbackBusiness from "../../app/ugc/FeedbackBusiness"
import IFeedbackBusiness from "../../app/ugc/IFeedbackBusiness"
import { IBatchedQueueHandler } from "@curefit/sqs-client"
import NotificationQueueListener from "../../app/ugc/NotificationQueueListener"
import FeedbackViewBuilder from "../../app/ugc/FeedbackViewBuilder"
import MealDetailViewBuilder from "../../app/product/MealDetailViewBuilder"
import EventDetailViewBuilder from "../../app/pack/EventDetailViewBuilder"
import { ProgramPackDetailViewBuilder } from "../../app/program/ProgramPackDetailViewBuilder"
import MealSubscriptionViewBuilder from "../../app/pack/MealSubscriptionViewBuilder"
import BookingDetailViewBuilder from "../../app/cult/BookingDetailViewBuilder"
import PulseViewBuilder from "../../app/pulse/PulseViewBuilder"
import WodViewBuilder from "../../app/cult/WodViewBuilder"
import ICRMIssueService from "../../app/crm/ICRMIssueService"
import CRMIssueService from "../../app/crm/CRMIssueService"
import OrderTrackFirebaseService from "../../app/eat/OrderTrackFirebaseService"
import IssueService from "../../app/crm/IssueService"
import IIssueService from "../../app/crm/IssueService"
import IssueBusiness from "../../app/crm/IssueBusiness"
import CRMBusiness from "../../app/crm/CRMBusiness"
import FeedbackPageConfig from "../../app/ugc/FeedbackPageConfig"
import FeedbackPageConfigV2Cache from "../../app/ugc/FeedbackPageConfigV2Cache"
import LoggableSlotsMetaCache from "../../app/logging/LoggableSlotsMetaCache"
import { PaymentPageConfig } from "../../app/payment/PaymentPageConfig"
import TeleconsultationDetailsPageConfig from "../../app/care/TeleconsultationDetailsPageConfig"
import HCUDetailsPageConfig from "../../app/care/HCUDetailsPageConfig"
import CareLandingPageConfig from "../../app/page/CareLandingPageConfig"
import RecommendationPageConfig from "../../app/user/RecommendationPageConfig"
import BookingConfirmationPageConfig from "../../app/order/BookingConfirmationPageConfig"
import CareLandingPageBuilderV1 from "../../app/page/CareLandingPageBuilderV1"
import CultOfferDetailViewBuilder from "../../app/cult/CultOfferDetailViewBuilder"
import CapacityServiceWrapper from "../../app/product/CapacityServiceWrapper"
import { StatusHandler } from "../../app/server/StatusHandler"
import { IProgramBusiness } from "../../app/program/IProgramBusiness"
import InterventionQueueListener from "../../app/intervention/InterventionQueueListener"
import InterventionHandler from "../../app/intervention/InterventionHandler"
import CultNoShowHandler from "../../app/intervention/CultNoShowHandler"
import InterventionHandlerFactory from "../../app/intervention/InterventionHandlerFactory"
import IInterventionBusiness from "../../app/intervention/IInterventionBusiness"
import InterventionBusiness from "../../app/intervention/InterventionBusiness"
import IOrderConfirmationBusiness from "../../app/order/IOrderConfirmationBusiness"
import OrderConfirmationBusiness from "../../app/order/OrderConfirmationBusiness"
import IAuthBusiness from "../../app/auth/IAuthBusiness"
import AuthBusiness from "../../app/auth/AuthBusiness"
import DiagnosticReportViewBuilder from "../../app/care/DiagnosticReportViewBuilder"
import { ISessionBusiness } from "@curefit/base-utils"
import { SessionServiceFactory } from "@curefit/base-utils"
import { ICFAPICityService } from "../../app/city/ICFAPICityService"
import { CFAPICityService } from "../../app/city/CFAPICityService"
import VMPageBuilder from "../../app/page/vm/VMPageBuilder"
import { IServiceInterfaces, IBannerService, ISegmentService } from "@curefit/vm-models"
import ServiceInterfaces from "../../app/page/vm/ServiceInterfaces"
import UserTestViewBuilder from "../../app/usertest/UserTestViewBuilder"
import TestViewBuilderFactory from "../../app/usertest/TestViewBuilderFactory"
import CultScoreViewBuilder from "../../app/usertest/CultScoreViewBuilder"
import EatLandingPageBuilderV5 from "../../app/page/EatLandingPageBuilderV5"
import SEOService from "../../app/seo/SEOService"
import MixpanelEventService from "../../app/cultsport/MixpanelEventService"
import { ISEOService } from "../../app/seo/ISEOService"
import { BreadCrumbFactory } from "../../app/breadcrumb/BreadCrumbService"
import { IBreadCrumbService } from "../../app/breadcrumb/interface"
import TherapyPageConfig from "../../app/therapy/TherapyPageConfig"
import FitnessFirstPackDetailViewBuilder from "../../app/pack/FitnessFirstPackDetailViewBuilder"
import FitnessFirstPackPageConfig from "../../app/pack/FitnessFirstPackPageConfig"
import { ITPOffersBusiness } from "../../app/offer/external/ITPOffersBusiness"
import TPOffersBusiness from "../../app/offer/external/TPOffersBusiness"
import { CultFitSubscriptionDetailViewBuilder, CultMindSubscriptionDetailViewBuilder } from "../../app/pack/CultSubscriptionDetailViewBuilder"
import GearProductViewBuilder from "../../app/product/GearProductViewBuilder"
import { IUserPlanBusiness, UserPlanBusiness } from "../../app/userplan/UserPlanBusiness"
import { EatClpHook } from "../../app/page/pageHooks/EatClpHook"
import { CaptchaBusiness, ICaptchaBusiness } from "../../app/referral/CaptchaBusiness"
import { ICultBusiness, CultBusiness } from "../../app/cult/CultBusiness"
import { IUserGoalBusiness, UserGoalBusiness } from "../../app/goals/UserGoalBusiness"
import { ManagedPlanProductPageViewBuilder } from "../../app/care/ManagedPlanProductPageViewBuilder"
import { CareCartProductPageViewBuilder } from "../../app/care/CareCartProductPageViewBuilder"
import ReferPageConfig from "../../app/referral/ReferPageConfig"
import { ActivityLoggingBusiness, IActivityLoggingBusiness } from "../../app/logging/ActivityLoggingBusiness"
import CerberusServiceV2 from "../../app/recommendation/CerberusServiceV2"
import { ICerberusService } from "@curefit/vm-models"
import GearLandingPageBuilder from "../../app/page/GearLandingPageBuilder"
import GearOrderItemViewBuilder from "../../app/gear/orderItem/GearOrderItemViewBuilder"
import GearShipmentViewBuilder from "../../app/gear/shipments/GearShipmentViewBuilder"
import GearInventoryUnitViewBuilder from "../../app/gear/inventoryUnit/GearInventoryUnitViewBuilder"
import ReferralDetailsPageConfig from "../../app/referral/ReferralDetailsPageConfig"
import { IPageService } from "@curefit/vm-models"
import { CacheHelper } from "../../app/util/CacheHelper"
import MessageInterventionHandler from "../../app/intervention/MessageInterventionHandler"
import { WebActionTransformerUtil } from "../../app/util/WebActionTransformerUtil"
import { IWebActionTransformer } from "@curefit/util-common"
import GearLandingPageConfig from "../../app/page/GearLandingPageConfig"
import MetricsUtil from "../../app/metrics/MetricsUtil"
import EnterpriseUtil from "../../app/util/EnterpriseUtil"
import OnboardingPageConfig from "../../app/page/OnboardingPageConfig"
import SupportPageConfig from "../../app/user/SupportPageConfig"
import MePageSideBarPageConfig from "../../app/user/MePageSideBarPageConfig"
import { CultFitPackDetailViewBuilder, CultMindPackDetailViewBuilder } from "../../app/pack/CultPackDetailViewBuilder"
import CreateUserFormViewBuilder from "../../app/care/CreateUserFormViewBuilder"
import { EatNowHook } from "../../app/page/pageHooks/EatNowHook"
import { FitclubMembershipHook } from "../../app/page/pageHooks/FitclubMembershipHook"
import { FitClubActiveMembershipHook } from "../../app/page/pageHooks/FitClubActiveMembershipHook"
import { EatLaterHook } from "../../app/page/pageHooks/EatLaterHook"
import { Announcement, AnnouncementKey, AnnouncementDisplayTimeKey } from "../../app/announcement/Announcement"
import { IRedisDao } from "@curefit/redis-utils"
import { AnnouncementRedisDao, AnnouncementDisplayTimeRedisDao } from "../../app/announcement/AnnouncementRedisDao"
import { AnnouncementBusiness } from "../../app/announcement/AnnouncementBusiness"
import { AnnouncementViewBuilder } from "../../app/announcement/AnnouncementViewBuilder"
import ConsultationListPageViewBuilder from "../../app/care/ConsultationListPageViewBuilder"
import MeDiagnosticTestListPageViewBuilder from "../../app/care/MeDiagnosticTestListPageView"
import MealListPageViewBuilder from "../../app/order/MealListPageViewBuilder"
import ClassListPageViewBuilder from "../../app/cult/ClassListPageViewBuilder"
import WidgetBuilder from "../../app/page/vm/WidgetBuilder"
import { IWidgetBuilder } from "@curefit/vm-models"
import BookingDetailViewBuilderV2 from "../../app/cult/BookingDetailViewBuilderV2"
import { IPaymentOptionsBusinessV2, PaymentOptionsBusinessV2 } from "../../app/payment/PaymentOptionsBusinessV2"
import CultPausePackViewBuilder from "../../app/pack/CultPausePackViewBuilder"
import { ISSOBusiness } from "../../app/sso/ISSOBusiness"
import SSOBusiness from "../../app/sso/SSOBusiness"
import { SSOMiddleware } from "../../app/sso/SSOMiddleware"
import GearListPageViewBuilder from "../../app/gear/support/GearListPageViewBuilder"
import GymfitCenterDetailsWebViewBuilder from "../../app/gymfit/GymfitCenterDetailsWebViewBuilder"
import GymfitCenterDetailsAppViewBuilder from "../../app/gymfit/GymfitCenterDetailsAppViewBuilder"
import GymfitPackPageConfig from "../../app/gymfit/GymfitPackPageConfig"
import GymfitPackDetailViewBuilder from "../../app/pack/GymfitPackDetailViewBuilder"
import GymfitCenterPageConfig from "../../app/gymfit/GymfitCenterPageConfig"
import { DropoutHIWPageHook } from "../../app/page/pageHooks/DropoutHIWPageHook"
import { OrderNowHook } from "../../app/page/pageHooks/OrderNowHook"
import BannerService from "../../app/page/vm/services/BannerService"
import { WholeFitHook } from "../../app/page/pageHooks/WholeFitHook"
import IVoiceBusiness from "../../app/voice/IVoiceBusiness"
import VoiceBusiness from "../../app/voice/VoiceBusiness"
import MealPlannerBusiness from "../../app/mealplanner/MealPlannerBusiness"
import IMealPlannerBusiness from "../../app/mealplanner/IMealPlannerBusiness"
import TransferMembershipViewBuilder from "../../app/cult/TransferMembershipViewBuilder"
import UpgradeMembershipViewBuilder from "../../app/cult/UpgradeMembershipViewBuilder"
import MembershipAuditTrailViewBuilder from "../../app/cult/MembershipAuditTrailViewBuilder"
import LiveClassDetailViewBuilder from "../../app/cult/LiveClassDetailViewBuilder"
import LiveClassDetailViewBuilderV2 from "../../app/cult/LiveClassDetailViewBuilderV2"
import CareCenterViewBuilder from "../../app/care/CareCenterViewBuilder"
import { BackendConf, BASE_TYPES } from "@curefit/base"
import { SpotHandler } from "../../app/server/SpotHandler"
import NuxViewBuilder from "../../app/cult/NuxViewBuilder"
import PtAtHomeViewBuilder from "../../app/cult/ptathome/PtAtHomeViewBuilder"
import { FitclubBusiness } from "../../app/fitclub/FitclubBusiness"
import CareCenterBrowseViewBuilder from "../../app/care/CareCenterBrowseViewBuilder"
import { PromUtil } from "../../util/PromUtil"
import GymsViewBuilder from "../../app/gymfit/GymsViewBuilder"
import SelectSpecialityPageViewBuilder from "../../app/care/SelectSpecialityPageViewBuilder"
import { FitclubMembershipV2Hook } from "../../app/page/pageHooks/FitclubMembershipV2Hook"
import { Cafe, CultBike, FoodMarketplace, MarketPlace, OnlineKiosk, WholeFit } from "../../app/cart/EatBrand"
import { IEatBrand } from "../../app/cart/IEatBrand"
import FitnessReportPageViewBuilder from "../../app/cult/fitnessreport/FitnessReportPageViewBuilder"
import GearCatalogueLandingPageService from "../../app/page/GearCatalogueLandingPageService"
import GearInventoryUnitCancelViewBuilder from "../../app/gear/inventoryUnit/GearInventoryUnitCancelViewBuilder"
import FitnessReportPageConfig from "../../app/cult/fitnessreport/FitnessReportPageConfig"
import FitnessReportQueueListener from "../../app/ugc/FitnessReportQueueListener"
import { AtlasActivityService } from "../../app/atlas/AtlasActivityService"
import { WidgetRankingBusiness } from "../../app/cron/widgetRanking/WidgetRankingBusiness"
import { UserSleepAdapter } from "../../app/daos/mysql/atlas/sleep/UserSleepAdapter"
import { UserTokenReadWriteDaoMysqlImpl } from "../../app/daos/mysql/atlas/token/UserTokenReadWriteDaoMysqlImpl"
import { IUserTokenReadonlyDao, IUserTokenReadWriteDao } from "../../app/daos/mysql/atlas/token/IUserTokenDao"
import { UserTokenReadonlyDaoMysqlImpl } from "../../app/daos/mysql/atlas/token/UserTokenReadonlyDaoMysqlImpl"
import { UserTokenAdapter } from "../../app/daos/mysql/atlas/token/UserTokenAdapter"
import { UserTokenSchema } from "../../app/daos/mysql/atlas/token/UserTokenSchema"
import { UserDeviceReadWriteDaoMysqlImpl } from "../../app/daos/mysql/atlas/device/UserDeviceReadWriteDaoMysqlImpl"
import { IUserDeviceReadonlyDao, IUserDeviceReadWriteDao } from "../../app/daos/mysql/atlas/device/IUserDeviceDao"
import { UserDeviceReadonlyDaoMysqlImpl } from "../../app/daos/mysql/atlas/device/UserDeviceReadonlyDaoMysqlImpl"
import { UserDeviceAdapter } from "../../app/daos/mysql/atlas/device/UserDeviceAdapter"
import { UserDeviceSchema } from "../../app/daos/mysql/atlas/device/UserDeviceSchema"
import { FitbitCronService } from "../../app/atlas/FitbitCronService"
import { SupportFAQSchema } from "../../app/crm/FAQ/SupportFAQSchema"
import { SupportFAQReadOnlyDaoMongoImpl } from "../../app/crm/FAQ/SupportFAQReadOnlyDaoMongoImpl"
import { SupportFAQReadWriteDaoMongoImpl } from "../../app/crm/FAQ/SupportFAQReadWriteDaoMongoImpl"
import SupportFAQConfigCache from "../../app/crm/FAQ/SupportFAQConfigCache"
import CultMemoriesViewBuilder from "../../app/cult/viewbuilder/CultMemoriesViewBuilder"
import IWidgetDataProvider from "../../app/eat/clp/IWidgetDataProvider"
import EatMarketplaceDataProvider from "../../app/eat/clp/EatMarketplaceDataProvider"
import { IFCPage } from "../../app/eat/FCPage/IFCPage"
import { FCPageBuilder } from "../../app/eat/FCPage/FCPageBuilder"
import {
    CitiesConditionEvaluator,
    SegmentEvaluator,
    AreasConditionEvaluator,
    UserAgentsConditionEvaluator,
    MindTherapySegmentsConditionEvaluator,
    MindSegmentsConditionEvaluator,
    CultSegmentsConditionEvaluator,
    PTUserSegmentsConditionEvaluator,
    CareSegmentsConditionEvaluator,
    SourcesConditionEvaluator,
    OffersConditionEvaluator,
    EmailSuffixConditionEvaluator,
    FitclubConditionEvaluator,
    MindDIYSegmentsConditionEvaluator,
    CultDIYSegmentsConditionEvaluator,
    VerticalsConditionEvaluator,
    GymSegmentsConditionEvaluator,
    HamletConditionEvaluator,
    UserTypeConditionEvaluator,
    DeliveryChannelConditionEvaluator,
    LivePTUserSegmentsConditionEvaluator,
    EmailSetConditionEvaluator,
    PlatformSegmentConditionEvaluator,
    MealPlanSegmentsConditionEvaluator,
    ProductTypeSegmentConditionEvaluator,
    LiveSGTUserSegmentsConditionEvaluator,
    SupportedCodePushVersionEvaluator,
    CultsportArticleTypeSegmentEvaluator,
    CultsportBrandSegmentEvaluator,
    CultsportCategorySegmentEvaluator,
    CultsportCollectionTypeSegmentEvaluator,
    CultsportProductIdSegmentEvaluator
} from "../../app/page/vm/services/SegmentEvaluator"
import { CultFitPackDetailViewBuilderV2, CultMindPackDetailViewBuilderv2 } from "../../app/cult/cultpackpage/CultPackDetailViewBuilderV2"
import GymfitCheckinConfirmationPageConfig from "../../app/gymfit/GymfitCheckinConfirmationPageConfig"
import { GymfitBusiness, IGymfitBusiness } from "../../app/gymfit/GymfitBusiness"
import { BranchService } from "../../app/common/branch/BranchService"
import IBranchService from "../../app/common/branch/IBranchService"
import DigitalCalendarViewBuilder from "../../app/digital/DigitalCalendarViewBuilder"
import GiftCardViewBuilder from "../../app/giftcards/GiftCardViewBuilder"
import GearInventoryUnitViewBuilderV2 from "../../app/gear/inventoryUnit/GearInventoryUnitViewBuilderV2"
import AppLayoutBuilder from "../../app/user/AppLayoutBuilder"
import { WholefitBusiness } from "../../app/wholefit/WholefitBusiness"
import { WholefitBrandPageBuilder } from "../../app/wholefit/WholefitBrandPageBuilder"
import { WholefitV2Hook } from "../../app/page/pageHooks/WholefitV2Hook"
import CultCenterPageViewBuilder from "../../app/cult/viewbuilder/CultCenterPageViewBuilder"
import { ReferralTransformationService } from "../../app/referral/myreferrals/ReferralTransformationService"
import { GiftCardTransformationService } from "../../app/referral/myreferrals/GiftCardTransformationService"
import MyReferralViewBuilder from "../../app/referral/myreferrals/MyReferralViewBuilder"
import { EatMktPlaceOrderConfirmationPageBuilder } from "../../app/eat/orderConfirmationPage/EatMktPlaceOrderConfirmationPageBuilder"
import { IEatMktPlaceOrderConfirmationPage } from "../../app/eat/orderConfirmationPage/IEatMktPlaceOrderConfirmationPage"
import CultPostPurchaseDetailViewBuilder from "../../app/cult/cultpackpage/CultPostPurchaseDetailViewBuilder"
import CultUserProfileViewBuilder from "../../app/cult/cultSocial/CultUserProfileViewBuilder"
import CultMomentsViewBuilder from "../../app/cult/cultSocial/CultMomentsViewBuilder"
import CultPrePurchaseDetailViewBuilder from "../../app/cult/cultpackpage/CultPrePurchaseDetailViewBuilder"
import { WholefitSearchPageBuilder } from "../../app/wholefit/WholefitSearchPageBuilder"
import { WholefitProductDetailViewBuilder } from "../../app/wholefit/WholefitProductDetailViewBuilder"
import {
    ClassInviteLink,
    ClassInviteLinkKey
} from "../../app/cult/invitebuddy/ClassInviteLink"
import { ClassInviteLinkCreator } from "../../app/cult/invitebuddy/ClassInviteLinkCreator"
import { ClassInviteLinkRedisDao } from "../../app/cult/invitebuddy/ClassInviteLinkRedisDao"
import DigitalReportViewBuilder from "../../app/digital/DigitalReportViewBuilder"
import DigitalMomentsViewBuilder from "../../app/digital/DigitalMomentsViewBuilder"
import ChallengesViewBuilder from "../../app/challenges/ChallengesViewBuilder"
import ChallengeDetailsViewBuilder from "../../app/challenges/ChallengeDetailsViewBuilder"
import { MaximusService } from "../../app/maximus/MaximusService"
import { UserYearEndReportSchema } from "../../app/user/userYearEndReport/UserYearEndReportSchema"
import { UserYearEndReportReadOnlyDaoMongoImpl } from "../../app/user/userYearEndReport/UserYearEndReportReadOnlyDaoMongoImpl"
import { UserYearEndReportReadWriteDaoMongoImpl } from "../../app/user/userYearEndReport/UserYearEndReportReadWriteDaoMongoImpl"
import SessionQueueListener from "../../app/auth/SessionQueueListener"
import GymfitCheckinQueueListener from "../../app/gymfit/GymfitCheckinQueueListener"
import UserActionMappingBusiness from "../../app/error/UserActionMappingBusiness"
import UserErrorBusiness from "../../app/error/UserErrorBusiness"
import { SessionBusiness, ICFApiSessionBusiness } from "../../app/auth/SessionBusiness"
import DiagnosticReportViewBuilderV2 from "../../app/care/DiagnosticReportViewBuilderV2"
import { CultWorkoutsViewBuilder } from "../../app/cult/CultWorkoutsViewBuilder"
import { CultWorkoutDetailViewBuilder } from "../../app/cult/CultWorkoutDetailViewBuilder"
import INavBarBusiness from "../../app/user/INavBarBusiness"
import NavBarBusiness from "../../app/user/NavBarBusiness"
import IUserPreferenceBussiness from "../../app/user/IUserPreferenceBussiness"
import UserPreferenceBussiness from "../../app/user/UserPreferenceBussiness"
import GymfitClassScheduleViewBuilder from "../../app/gymfit/GymfitClassScheduleViewBuilder"
import GymListPageViewBuilder from "../../app/gymfit/GymListPageViewBuilder"
import SportListPageViewBuilder from "../../app/fitsoSports/SportsListPageViewBuilder"
import GymfitCheckinViewBuilder from "../../app/gymfit/GymfitCheckinViewBuilder"
import SocialKnowMoreViewBuilder from "../../app/digital/SocialKnowMoreViewBuilder"
import { AgentMetricUtil } from "../../util/AgentMetricUtil"
import { IFeedbackRedisCache, FeedbackRedisCache } from "../../app/ugc/FeedbackRedisCache"
import { IInterventionRedisCache, InterventionRedisCache } from "../../app/intervention/InterventionRedisCache"
import { LiveClassSlotCreator } from "../../app/cult/liveClassSlot/LiveClassSlotCreator"
import { LiveClassSlotRedisDao } from "../../app/cult/liveClassSlot/LiveClassSlotRedisDao"
import { LiveClassSlotKey } from "../../app/cult/liveClassSlot/LiveClassSlot"
import { LiveClass } from "@curefit/diy-common"
import DigitalWorkoutMemberHistoryBuilder from "../../app/digital/DigitalWorkoutMemberHistoryBuilder"
import CFAPIJavaService from "../../app/CFAPIJavaService"
import { RecipeClpProductsViewBuilder } from "../../app/recipe/RecipeClpProductsViewBuilder"
import LivePtPackPageViewBuilder from "../../app/cult/ptathome/LivePtPackPageViewBuilder"
import LivePacksViewBuilder from "../../app/pack/LivePacksViewBuilder"
import { NutritionistPlanProductPageViewBuilder } from "../../app/eat/nutritionist/NutritionistPlanProductPageViewBuilder"
import LiveMembershipExpiredModalViewBuilder from "../../app/digital/LiveMembershipExpiredModalViewBuilder"
import { RecipeSearchBuilder } from "../../app/recipe/RecipeSearchBuilder"
import { OnDemandProductCollectionViewBuilder } from "../../app/page/vm/widgets/ondemand/OnDemandProductCollectionViewBuilder"
import LiveMembershipViewBuilder from "../../app/digital/LiveMembershipViewBuilder"
import { CF_API_NAMESPACE, ErrorFactory, ExpressHttpError } from "@curefit/error-client"
import LivePtGoalPageViewBuilder from "../../app/cult/ptathome/LivePtGoalPageViewBuilder"
import { CareBusiness, ICareBusiness } from "../../app/care/CareBusiness"
import DigitalLeagueSectionViewBuilder from "../../app/digital/DigitalLeagueSectionViewBuilder"
import DigitalSocialLeagueTabPageViewBuilder from "../../app/digital/DigitalSocialLeagueTabPageViewBuilder"
import { DeepLinkService, IDeepLinkService } from "../../app/deeplink/DeepLinkService"
import { CareCultDayTransferPageViewBuilder } from "../../app/care/CareCultDayTransferProductPageBuilder"
import { OnDemandAutoPlayBuilder } from "../../app/page/vm/widgets/ondemand/OnDemandAutoPlayBuilder"
import { RecipeCollectionDetailViewBuilder } from "../../app/recipe/RecipeCollectionDetailViewBuilder"
import { MindTherapyPhysioPacksProductPageViewBuilder } from "../../app/care/MindTherapyAndPhysioPacksProductPageBuilder"
import DiyFavoritesViewBuilder from "../../app/pack/DiyFavoritesViewBuilder"
import { LivePremiereProductViewBuilder } from "../../app/page/ondemand/LivePremiereProductViewBuilder"
import LivePTBookingDetailViewBuilder from "../../app/care/LivePTBookingDetailViewBuilder"
import ChallengeReportViewBuilder from "../../app/challenges/ChallengeReportViewBuilder"
import DigitalLeagueChallengeViewBuilder from "../../app/digital/DigitalLeagueChallengeViewBuilder"
import { AppFeedbackKey } from "../../app/feedback/Feedback"
import { FeedbackRedisDao } from "../../app/feedback/FeedbackRedisDao"
import { GP99ProductPageViewBuilder } from "../../app/care/CareGP99ProductPageBuilder"
import LivePTReportPageViewBuilder from "../../app/cult/ptathome/LivePTReportPageViewBuilder"
import AppFeedbackService, { IAppFeedback } from "../../app/page/vm/services/AppFeedbackService"
import { UserFormRequest } from "@curefit/cfs-common"
import { SegmentServiceFactory } from "../../app/page/vm/services/SegmentService"
import { CustomEventEmitter } from "../../app/externalSource/CustomEventEmitter"
import { PAGE_CONFIG_TYPES } from "@curefit/config-mongo"
import { ExternalSourceUtil } from "../../app/externalSource/ExternalSourceUtil"
import LiveClassDetailViewBuilderTV from "../../app/cult/LiveClassDetailViewBuilderTV"

import { RecommendedFirstClassesViewBuilder } from "../../app/digital/find/RecommendedFirstClassesViewBuilder"
import PTSGTNoShowHandler from "../../app/intervention/PTSGTNoShowHandler"
import YogaReportCardWidgetViewBuilder from "../../app/digital/YogaReportCardWidgetViewBuilder"
import YogaBarGraphWidgetViewBuilder from "../../app/digital/YogaBarGraphWidgetViewBuilder"
import { StoreListPageViewBuilder } from "../../app/store/support/StoreListPageViewBuilder"
import { ChatEventLogSchema } from "../../app/care/events/ChatEventLogSchema"
import { ChatEventLogReadOnlyDaoMongoImpl } from "../../app/care/events/ChatEventLogReadOnlyDaoMongoImpl"
import { ChatEventLogReadWriteDaoMongoImpl } from "../../app/care/events/ChatEventLogReadWriteDaoMongoImpl"
import { AppConfigUtil } from "../../app/appConfig/AppConfigService"
import { EventInterventionMappingBusiness } from "../../app/intervention/EventInterventionMappingBusiness"
import { PaymentOptionsViewBuilder } from "../../app/payment/PaymentOptionsViewBuilder"
import { PaymentOptionViewFactory } from "../../app/payment/PaymentOptionViewFactory"
import GymLocalitySelectorViewBuilder from "../../app/gymfit/GymLocalitySelectorViewBuilder"
import GymUniversalCheckinViewBuilder from "../../app/gymfit/GymUniversalCheckinViewBuilder"
import SeriesViewBuilder from "../../app/digital/diy/SeriesViewBuilder"
import YogaTimeStampedProgressPills from "../../app/digital/YogaTimeStampedProgressPills"
import SGTOneStepBookingPageViewBuilder from "../../app/cult/SGTOneStepBookingPageViewBuilder"
import { UserYearlyReportBusiness } from "../../app/user/UserYearlyReportBusiness"
import { PageServiceFactory } from "../../app/page/vm/services/PageService"
import TLLivePacksModalViewBuilder from "../../app/digital/TLLivePacksModalViewBuilder"
import SoftBookingBuilder from "../../app/gymfit/SoftBookingBuilder"
import PNTokenEventQueueListener from "../../app/auth/PNTokenEventQueueListener"
import GymfitCheckinConfirmationViewBuilder from "../../app/gymfit/GymfitCheckinConfirmationViewBuilder"
import DigitalSessionListViewBuilder from "../../app/digital/DigitalSessionListViewBuilder"
import DIYClassDetailViewBuilder from "../../app/digital/DIYClassDetailViewBuilder"
import SGTPreBookingDetailPageViewBuilder from "../../app/cult/SGTPreBookingDetailPageViewBuilder"
import { FMRestaurantListBuilder } from "../../app/eat/FCPage/FMRestaurantListBuilder"
import { WellnessClpHook } from "../../app/page/pageHooks/WellnessClpHook"
import { FitnesshubPageHook } from "../../app/page/pageHooks/FitnesshubPageHook"
import { ProgramPageHook } from "../../app/page/pageHooks/ProgramPageHook"
import GymPausePackViewBuilder from "../../app/pack/GymPausePackViewBuilder"
import { UserYearReport2022Business } from "../../app/user/UserYearReport2022Business"
import { UserYearReport2023Business } from "../../app/user/UserYearReport2023Business"
import { UserYearEndSummarySchema } from "../../app/user/userYearEndSummary/UserYearEndSummarySchema"
import { UserYearEndSummaryReadOnlyDaoMongoImpl } from "../../app/user/userYearEndSummary/UserYearEndSummaryReadOnlyDaoMongoImpl"
import { UserAchievmentShowcaseReadOnlyDaoMongoImpl } from "../../app/user/userYearEndSummary/UserAchievmentShowcaseReadOnlyDaoMongoImpl"
import {
    UserYearEndSummaryReadWriteDaoMongoImpl
} from "../../app/user/userYearEndSummary/UserYearEndSummaryReadWriteDaoMongoImpl"
import {
    UserAchievmentShowcaseReadWriteDaoMongoImpl
} from "../../app/user/userYearEndSummary/UserAchievmentShowcaseReadWriteDaoMongoImpl"
import { FeatureStateCache } from "../../app/page/vm/services/FeatureStateCache"
import { CoachMarkConfig } from "../../app/page/CoachMarkConfig"
import { CfsFormCache, ICfsFormCache } from "../../app/cfs/CfFormCache"
import { CoachProgramPageHook } from "../../app/page/pageHooks/CoachProgramPageHook"
import PlayPackDetailViewBuilder from "../../app/pack/PlayPackDetailViewBuilder"
import { GearFeedbackSNSProducer } from "../../app/gear/GearFeedbackSNSProducer"
import { VerticalSpecificFeedbackConsumer } from "../../app/feedback/VerticalSpecificFeedbackConsumer"
import PlayPausePackViewBuilder from "../../app/pack/PlayPausePackViewBuilder"
import { ICyclingService } from "../../app/cycling/CyclingInterface"
import { CyclingService } from "../../app/cycling/CyclingService"
import { TataNeuService } from "../../app/external/TataNeuService"
import PlaySelectPackViewBuilder from "../../app/pack/PlaySelectPackViewBuilder"
import PlayUpgradeDetailViewBuilder from "../../app/pack/PlayUpgradeDetailViewBuilder"
import { IRedisAuthTokenService, RedisAuthTokenService } from "../../app/auth/RedisAuthTokenService"
import { SportshubPageHook } from "../../app/page/pageHooks/SportshubPageHook"
import CultSchedulePageConfig from "../../app/cult/CultSchedulePageConfig"
import FitnessTransferMembershipViewBuilder from "../../app/fitness/FitnessTransferMembershipViewBuilder"
import { CFAnalytics, ICFAnalytics, ICFAnalyticsConfig } from "../../app/cfAnalytics/CFAnalytics"
import { UserYERCharacterRevealReportSchema } from "../../app/user/userYERCharacterRevealReport/userYERCharacterRevealReportSchema"
import { UserYERCharacterRevealReportReadOnlyDaoMongoImpl } from "../../app/user/userYERCharacterRevealReport/userYERCharacterRevealReportReadOnlyDaoMongoImpl"
import { UserYERCharacterRevealReportReadWriteDaoMongoImpl } from "../../app/user/userYERCharacterRevealReport/userYERCharacterRevealReportReadWriteDaoMongoImpl"
import DIYPackService from "../../app/digital/diy/DIYPackService"
import PlayPackViewBuilder from "../../app/pack/PlayPackViewBuilder"
import CultClassUtil from "../../app/util/CultClassUtil"
import { ICultstoreShopifyConf, ICultstoreShopifyService, IMystiqueServiceConf, ITesseractServiceConf } from "../../app/cultstoreShopify/CultstoreShopifyInterfaces"
import CultstoreShopifyService from "../../app/cultstoreShopify/CultstoreShopifyService"
import { UserAchievmentShowcaseSchema } from "../../app/user/userYearEndSummary/UserAchievmentShowcaseSchema"
export function BusinessKernelModule(kernel: Container) {
    return new ContainerModule((bind: interfaces.Bind) => {
        bind<IDeviceBusiness>(CUREFIT_API_TYPES.DeviceBusiness).to(DeviceBusiness).inSingletonScope()
        bind<IAuthBusiness>(CUREFIT_API_TYPES.AuthBusiness).to(AuthBusiness).inSingletonScope()
        bind<ISSOBusiness>(CUREFIT_API_TYPES.SSOBusiness).to(SSOBusiness).inSingletonScope()
        bind<SSOMiddleware>(CUREFIT_API_TYPES.SSOMiddleware).to(SSOMiddleware).inSingletonScope()
        bind<IProductBusiness>(CUREFIT_API_TYPES.ProductBusiness).to(ProductBusiness).inSingletonScope()
        bind<IUserBusiness>(CUREFIT_API_TYPES.UserBusiness).to(UserBusiness).inSingletonScope()
        bind<ICultBusiness>(CUREFIT_API_TYPES.CultBusiness).to(CultBusiness).inSingletonScope()
        bind<IUserPlanBusiness>(CUREFIT_API_TYPES.UserPlanBusiness).to(UserPlanBusiness).inSingletonScope()
        bind<IRecommendationBusiness>(CUREFIT_API_TYPES.RecommendationBusiness).to(RecommendationBusiness).inSingletonScope()
        bind<IOrderConfirmationBusiness>(CUREFIT_API_TYPES.OrderConfirmationBusiness).to(OrderConfirmationBusiness).inSingletonScope()
        bind<HomePageConfig>(CUREFIT_API_TYPES.HomePageConfig).to(HomePageConfig).inSingletonScope()
        bind<MealPlanSegmentsConditionEvaluator>(
            CUREFIT_API_TYPES.MealPlanSegmentsConditionEvaluator
        ).to(MealPlanSegmentsConditionEvaluator).inSingletonScope()
        bind<FeedbackPageConfig>(CUREFIT_API_TYPES.FeedbackPageConfig).to(FeedbackPageConfig).inSingletonScope()
        bind<FeedbackPageConfigV2Cache>(CUREFIT_API_TYPES.FeedbackPageConfigV2Cache).to(FeedbackPageConfigV2Cache).inSingletonScope()
        bind<PaymentPageConfig>(CUREFIT_API_TYPES.PaymentPageConfig).to(PaymentPageConfig).inSingletonScope()
        bind<MealPackPageConfig>(CUREFIT_API_TYPES.MealPackPageConfig).to(MealPackPageConfig).inSingletonScope()
        bind<CultPackPageConfig>(CUREFIT_API_TYPES.CultPackPageConfig).to(CultPackPageConfig).inSingletonScope()
        bind<CultDIYPackPageConfig>(CUREFIT_API_TYPES.CultDIYPackPageConfig).to(CultDIYPackPageConfig).inSingletonScope()
        bind<MindDIYPackPageConfig>(CUREFIT_API_TYPES.MindDIYPackPageConfig).to(MindDIYPackPageConfig).inSingletonScope()
        bind<CareLandingPageConfig>(CUREFIT_API_TYPES.CareLandingPageConfig).to(CareLandingPageConfig).inSingletonScope()
        bind<GearLandingPageConfig>(CUREFIT_API_TYPES.GearLandingPageConfig).to(GearLandingPageConfig).inSingletonScope()
        bind<TeleconsultationDetailsPageConfig>(CUREFIT_API_TYPES.TeleconsultationDetailsPageConfig).to(TeleconsultationDetailsPageConfig).inSingletonScope()
        bind<RecommendationPageConfig>(CUREFIT_API_TYPES.RecommendationPageConfig).to(RecommendationPageConfig).inSingletonScope()
        bind<BookingConfirmationPageConfig>(CUREFIT_API_TYPES.BookingConfirmationPageConfig).to(BookingConfirmationPageConfig).inSingletonScope()
        bind<HCUDetailsPageConfig>(CUREFIT_API_TYPES.HCUDetailsPageConfig).to(HCUDetailsPageConfig).inSingletonScope()
        bind<IInfoPageBuilder>(CUREFIT_API_TYPES.InfoPageBuilder).to(InfoPageBuilder).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.MindLandingPageBuilderV2).to(MindLandingPageBuilderV2).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.CareLandingPageBuilderV1).to(CareLandingPageBuilderV1).inSingletonScope()
        bind<BaseCultLandingPageConfig>(CUREFIT_API_TYPES.CultLandingPageConfigV2).to(CultLandingPageConfigV2).inSingletonScope()
        bind<OnboardingPageConfig>(CUREFIT_API_TYPES.OnboardingPageConfig).to(OnboardingPageConfig).inSingletonScope()
        bind<BaseCultLandingPageConfig>(CUREFIT_API_TYPES.MindLandingPageConfigV2).to(MindLandingPageConfigV2).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.CultLandingPageBuilderV2).to(CultLandingPageBuilderV2).inSingletonScope()
        bind<EatLandingPageConfig>(CUREFIT_API_TYPES.EatLandingPageConfig).to(EatLandingPageConfig).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.EatLandingPageBuilderV3).to(EatLandingPageBuilderV3).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.EatLandingPageBuilderV4).to(EatLandingPageBuilderV4).inSingletonScope()
        bind<IPageBuilder>(CUREFIT_API_TYPES.EatLandingPageBuilderV5).to(EatLandingPageBuilderV5).inSingletonScope()
        bind<IFirebaseService>(CUREFIT_API_TYPES.FirebaseService).to(FirebaseService).inSingletonScope()
        bind<IClassListViewBuilder>(CUREFIT_API_TYPES.ClassListViewBuilder).to(ClassListViewBuilder).inSingletonScope()
        bind<IClassListViewBuilder>(CUREFIT_API_TYPES.ClassListViewBuilderV1).to(ClassListViewBuilderV1).inSingletonScope()
        bind<CenterBookingCache>(CUREFIT_API_TYPES.CenterBookingCache).to(CenterBookingCacheImpl).inSingletonScope()
        bind<ClassListViewBuilderV2>(CUREFIT_API_TYPES.ClassListViewBuilderV2).to(ClassListViewBuilderV2).inSingletonScope()
        bind<ITimelineBusiness>(CUREFIT_API_TYPES.TimelineBusinessV1).to(TimelineBusinessFactoryV1(kernel)).inSingletonScope()
        bind<ChallengeReportViewBuilder>(CUREFIT_API_TYPES.ChallengeReportViewBuilder).to(ChallengeReportViewBuilder).inSingletonScope()
        bind<ITimelineBusiness>(CUREFIT_API_TYPES.TimelineBusinessV4).to(TimelineBusinessV4).inSingletonScope()
        bind<OrderViewBuilder>(CUREFIT_API_TYPES.OrderViewBuilder).to(OrderViewBuilder).inSingletonScope()
        bind<CartViewBuilder>(CUREFIT_API_TYPES.CartViewBuilder).to(CartViewBuilder).inSingletonScope()
        bind<OrderProductDetailBuilder>(CUREFIT_API_TYPES.OrderProductDetailBuilder).to(OrderProductDetailBuilder).inSingletonScope()
        bind<CultOfferDetailViewBuilder>(CUREFIT_API_TYPES.CultOfferDetailViewBuilder).to(CultOfferDetailViewBuilder).inSingletonScope()
        bind<BaseOrderConfirmationViewBuilder>(CUREFIT_API_TYPES.OrderConfirmationViewBuilder).to(OrderConfirmationViewBuilder).inSingletonScope()
        bind<BaseOrderConfirmationViewBuilder>(CUREFIT_API_TYPES.OrderConfirmationViewBuilderV1).to(OrderConfirmationViewBuilderV1).inSingletonScope()
        bind<ActivePackViewBuilderV1>(CUREFIT_API_TYPES.ActivePackViewBuilderV1).to(ActivePackViewBuilderV1).inSingletonScope()
        bind<RecommendationViewBuilder>(CUREFIT_API_TYPES.RecommendationViewBuilder).to(RecommendationViewBuilder).inSingletonScope()
        bind<FeedbackViewBuilder>(CUREFIT_API_TYPES.FeedbackViewBuilder).to(FeedbackViewBuilder).inSingletonScope()
        bind<MealDetailViewBuilder>(CUREFIT_API_TYPES.MealDetailViewBuilder).to(MealDetailViewBuilder).inSingletonScope()
        bind<BookingDetailViewBuilder>(CUREFIT_API_TYPES.BookingDetailViewBuilder).to(BookingDetailViewBuilder).inSingletonScope()
        bind<BookingDetailViewBuilderV2>(CUREFIT_API_TYPES.BookingDetailViewBuilderV2).to(BookingDetailViewBuilderV2).inSingletonScope()
        bind<IMealPlannerBusiness>(CUREFIT_API_TYPES.MealPlannerBusiness).to(MealPlannerBusiness).inSingletonScope()
        bind<INavBarBusiness>(CUREFIT_API_TYPES.NavBarBusiness).to(NavBarBusiness).inSingletonScope()
        bind<IUserPreferenceBussiness>(CUREFIT_API_TYPES.UserPreferenceBusiness).to(UserPreferenceBussiness).inSingletonScope()
        bind<WodViewBuilder>(CUREFIT_API_TYPES.WodViewBuilder).to(WodViewBuilder).inSingletonScope()
        bind<CultPausePackViewBuilder>(CUREFIT_API_TYPES.CultPausePackViewBuilder).to(CultPausePackViewBuilder).inSingletonScope()
        bind<GymPausePackViewBuilder>(CUREFIT_API_TYPES.GymPausePackViewBuilder).to(GymPausePackViewBuilder).inSingletonScope()
        bind<PlayPausePackViewBuilder>(CUREFIT_API_TYPES.PlayPausePackViewBuilder).to(PlayPausePackViewBuilder).inSingletonScope()
        bind<CultFitPackDetailViewBuilder>(CUREFIT_API_TYPES.CultFitPackDetailViewBuilder).to(CultFitPackDetailViewBuilder).inSingletonScope()
        bind<CultFitPackDetailViewBuilderV2>(CUREFIT_API_TYPES.CultFitPackDetailViewBuilderV2).to(CultFitPackDetailViewBuilderV2).inSingletonScope()
        bind<CultPrePurchaseDetailViewBuilder>(CUREFIT_API_TYPES.CultPrePurchaseDetailViewBuilder).to(CultPrePurchaseDetailViewBuilder).inSingletonScope()
        bind<CultPostPurchaseDetailViewBuilder>(CUREFIT_API_TYPES.CultPostPurchaseDetailViewBuilder).to(CultPostPurchaseDetailViewBuilder).inSingletonScope()
        bind<CultUserProfileViewBuilder>(CUREFIT_API_TYPES.CultUserProfileViewBuilder).to(CultUserProfileViewBuilder).inSingletonScope()
        bind<LivePtGoalPageViewBuilder>(CUREFIT_API_TYPES.LivePtGoalPageViewBuilder).to(LivePtGoalPageViewBuilder).inSingletonScope()
        bind<SGTOneStepBookingPageViewBuilder>(CUREFIT_API_TYPES.SGTOneStepBookingPageViewBuilder).to(SGTOneStepBookingPageViewBuilder).inSingletonScope()
        bind<CultMomentsViewBuilder>(CUREFIT_API_TYPES.CultMomentsViewBuilder).to(CultMomentsViewBuilder).inSingletonScope()
        bind<CultMindPackDetailViewBuilder>(CUREFIT_API_TYPES.CultMindPackDetailViewBuilder).to(CultMindPackDetailViewBuilder).inSingletonScope()
        bind<CultMindPackDetailViewBuilderv2>(CUREFIT_API_TYPES.CultMindPackDetailViewBuilderV2).to(CultMindPackDetailViewBuilderv2).inSingletonScope()
        bind<PulseViewBuilder>(CUREFIT_API_TYPES.PulseViewBuilder).to(PulseViewBuilder).inSingletonScope()
        bind<ProgramPackDetailViewBuilder>(CUREFIT_API_TYPES.ProgramPackDetailViewBuilder).to(ProgramPackDetailViewBuilder).inSingletonScope()
        bind<DiagnosticReportViewBuilder>(CUREFIT_API_TYPES.DiagnosticReportViewBuilder).to(DiagnosticReportViewBuilder).inSingletonScope()
        bind<DiagnosticReportViewBuilderV2>(CUREFIT_API_TYPES.DiagnosticReportViewBuilderV2).to(DiagnosticReportViewBuilderV2).inSingletonScope()
        bind<ConsultationListPageViewBuilder>(CUREFIT_API_TYPES.ConsultationListPageViewBuilder).to(ConsultationListPageViewBuilder).inSingletonScope()
        bind<MealListPageViewBuilder>(CUREFIT_API_TYPES.MealListPageViewBuilder).to(MealListPageViewBuilder).inSingletonScope()
        bind<ClassListPageViewBuilder>(CUREFIT_API_TYPES.ClassListPageViewBuilder).to(ClassListPageViewBuilder).inSingletonScope()
        bind<LiveClassDetailViewBuilder>(CUREFIT_API_TYPES.LiveClassDetailViewBuilder).to(LiveClassDetailViewBuilder)
        bind<LiveClassDetailViewBuilderV2>(CUREFIT_API_TYPES.LiveClassDetailViewBuilderV2).to(LiveClassDetailViewBuilderV2)
        bind<GearListPageViewBuilder>(CUREFIT_API_TYPES.GearListPageViewBuilder).to(GearListPageViewBuilder).inSingletonScope()
        bind<StoreListPageViewBuilder>(CUREFIT_API_TYPES.StoreListPageBuilder).to(StoreListPageViewBuilder).inSingletonScope()
        bind<GymListPageViewBuilder>(CUREFIT_API_TYPES.GymListPageViewBuilder).to(GymListPageViewBuilder).inSingletonScope()
        bind<MeDiagnosticTestListPageViewBuilder>(CUREFIT_API_TYPES.MeDiagnosticTestListPageViewBuilder).to(MeDiagnosticTestListPageViewBuilder).inSingletonScope()
        bind<ManagedPlanProductPageViewBuilder>(CUREFIT_API_TYPES.ManagedPlanProductPageViewBuilder).to(ManagedPlanProductPageViewBuilder).inSingletonScope()
        bind<CareCartProductPageViewBuilder>(CUREFIT_API_TYPES.CareCartProductPageViewBuilder).to(CareCartProductPageViewBuilder).inSingletonScope()
        bind<LivePtPackPageViewBuilder>(CUREFIT_API_TYPES.LivePtPackPageViewBuilder).to(LivePtPackPageViewBuilder).inSingletonScope()
        bind<MealSubscriptionViewBuilder>(CUREFIT_API_TYPES.MealSubscriptionViewBuilder).to(MealSubscriptionViewBuilder).inSingletonScope()
        bind<EventDetailViewBuilder>(CUREFIT_API_TYPES.EventDetailViewBuilder).to(EventDetailViewBuilder).inSingletonScope()
        bind<LoggableSlotsMetaCache>(CUREFIT_API_TYPES.LoggableSlotsMetaCache).to(LoggableSlotsMetaCache).inSingletonScope()
        bind<IFeedbackBusiness>(CUREFIT_API_TYPES.FeedbackBusiness).to(FeedbackBusiness).inSingletonScope()
        bind<IFeedbackRedisCache>(CUREFIT_API_TYPES.FeedbackRedisCache).to(FeedbackRedisCache).inSingletonScope()
        bind<ICfsFormCache>(CUREFIT_API_TYPES.CfsFormCache).to(CfsFormCache).inSingletonScope()
        bind<IBatchedQueueHandler>(CUREFIT_API_TYPES.SessionQueueListener).to(SessionQueueListener).inSingletonScope()
        bind<IBatchedQueueHandler>(CUREFIT_API_TYPES.FitnessReportQueueListener).to(FitnessReportQueueListener).inSingletonScope()
        bind<IBatchedQueueHandler>(CUREFIT_API_TYPES.GymfitCheckinQueueListener).to(GymfitCheckinQueueListener).inSingletonScope()
        bind<IBatchedQueueHandler>(CUREFIT_API_TYPES.NotificationQueueListener).to(NotificationQueueListener).inSingletonScope()
        bind<ICRMIssueService>(CUREFIT_API_TYPES.CRMIssueService).to(CRMIssueService).inSingletonScope()
        bind<OrderTrackFirebaseService>(CUREFIT_API_TYPES.OrderTrackFirebaseService).to(OrderTrackFirebaseService).inSingletonScope()
        bind<CapacityServiceWrapper>(CUREFIT_API_TYPES.CapacityServiceWrapper).to(CapacityServiceWrapper).inSingletonScope()
        bind<StatusHandler>(CUREFIT_API_TYPES.StatusHandler).to(StatusHandler).inSingletonScope()
        bind<IBatchedQueueHandler>(CUREFIT_API_TYPES.InterventionQueueListener).to(InterventionQueueListener).inSingletonScope()
        bind<InterventionHandler>(CUREFIT_API_TYPES.CultNoShowHandler).to(CultNoShowHandler).inSingletonScope()
        bind<InterventionHandler>(CUREFIT_API_TYPES.MessageInterventionHandler).to(MessageInterventionHandler).inSingletonScope()
        bind<IInterventionBusiness>(CUREFIT_API_TYPES.InterventionBusiness).to(InterventionBusiness).inSingletonScope()
        bind<IInterventionRedisCache>(CUREFIT_API_TYPES.InterventionRedisCache).to(InterventionRedisCache).inSingletonScope()
        bind<InterventionHandlerFactory>(CUREFIT_API_TYPES.InterventionHandlerFactory).to(InterventionHandlerFactory).inSingletonScope()
        bind<IProgramBusiness>(CUREFIT_API_TYPES.ProgramBusiness).to(ProgramBusiness).inSingletonScope()
        bind<ISessionBusiness>(CUREFIT_API_TYPES.BaseSessionService).to(SessionServiceFactory(kernel, "CFAPP:")).inSingletonScope()
        bind<ICFApiSessionBusiness>(CUREFIT_API_TYPES.SessionService).to(SessionBusiness).inSingletonScope()
        bind<IRedisAuthTokenService>(CUREFIT_API_TYPES.RedisAuthTokenService).to(RedisAuthTokenService).inSingletonScope()
        bind<ICFAPICityService>(CUREFIT_API_TYPES.CFAPICityService).to(CFAPICityService).inSingletonScope()
        bind<IPageService>(CUREFIT_API_TYPES.PageService).to(PageServiceFactory(kernel)).inSingletonScope()
        bind<IBannerService>(CUREFIT_API_TYPES.BannerService).to(BannerService).inSingletonScope()
        bind<FeatureStateCache>(CUREFIT_API_TYPES.FeatureStateCache).to(FeatureStateCache).inSingletonScope()
        bind<CoachMarkConfig>(CUREFIT_API_TYPES.CoachMarkConfig).to(CoachMarkConfig).inSingletonScope()
        bind<IServiceInterfaces>(CUREFIT_API_TYPES.ServiceInterfaces).to(ServiceInterfaces).inSingletonScope()
        bind<VMPageBuilder>(CUREFIT_API_TYPES.VMPageBuilder).to(VMPageBuilder).inSingletonScope()
        bind<IWidgetBuilder>(CUREFIT_API_TYPES.WidgetBuilder).to(WidgetBuilder).inSingletonScope()
        bind<UserTestViewBuilder>(CUREFIT_API_TYPES.CultScoreViewBuilder).to(CultScoreViewBuilder).inSingletonScope()
        bind<TestViewBuilderFactory>(CUREFIT_API_TYPES.TestViewBuilderFactory).to(TestViewBuilderFactory).inSingletonScope()
        bind<ISEOService>(CUREFIT_API_TYPES.SEOService).to(SEOService).inSingletonScope()
        bind<IBreadCrumbService>(CUREFIT_API_TYPES.BreadCrumbService).to(BreadCrumbFactory(kernel)).inSingletonScope()
        bind<ICyclingService>(CUREFIT_API_TYPES.CyclingService).to(CyclingService).inSingletonScope()
        bind<AtlasActivityService>(CUREFIT_API_TYPES.AtlasActivityService).to(AtlasActivityService).inSingletonScope()
        bind<FitbitCronService>(CUREFIT_API_TYPES.FitbitCronService).to(FitbitCronService).inSingletonScope()
        bind<FitnessFirstPackDetailViewBuilder>(CUREFIT_API_TYPES.FitnessFirstPackDetailViewBuilder).to(FitnessFirstPackDetailViewBuilder).inSingletonScope()
        bind<DiyFavoritesViewBuilder>(CUREFIT_API_TYPES.DiyFavoritesViewBuilder).to(DiyFavoritesViewBuilder).inSingletonScope()
        bind<FitnessFirstPackPageConfig>(CUREFIT_API_TYPES.FitnessFirstPackPageConfig).to(FitnessFirstPackPageConfig).inSingletonScope()
        bind<ITPOffersBusiness>(CUREFIT_API_TYPES.TPOffersBusiness).to(TPOffersBusiness).inSingletonScope()
        bind<GearProductViewBuilder>(CUREFIT_API_TYPES.GearProductViewBuilder).to(GearProductViewBuilder).inSingletonScope()
        bind<IActivityLoggingBusiness>(CUREFIT_API_TYPES.ActivityLoggingBusiness).to(ActivityLoggingBusiness).inSingletonScope()
        bind<IPaymentOptionsBusinessV2>(CUREFIT_API_TYPES.PaymentOptionsBusinessV2).to(PaymentOptionsBusinessV2).inSingletonScope()
        bind<PaymentOptionsViewBuilder>(CUREFIT_API_TYPES.PaymentOptionsViewBuilder).to(PaymentOptionsViewBuilder).inSingletonScope()
        bind<PaymentOptionViewFactory>(CUREFIT_API_TYPES.PaymentOptionViewFactory).to(PaymentOptionViewFactory).inSingletonScope()
        bind<EatClpHook>(CUREFIT_API_TYPES.EatClpPageHook).to(EatClpHook).inSingletonScope()
        bind<EatNowHook>(CUREFIT_API_TYPES.EatNowPageHook).to(EatNowHook).inSingletonScope()
        bind<FitclubMembershipHook>(CUREFIT_API_TYPES.FitclubMembershipPageHook).to(FitclubMembershipHook).inSingletonScope()
        bind<FitclubMembershipV2Hook>(CUREFIT_API_TYPES.FitclubMembershipV2PageHook).to(FitclubMembershipV2Hook).inSingletonScope()
        bind<FitClubActiveMembershipHook>(CUREFIT_API_TYPES.FitClubActiveMembershipPageHook).to(FitClubActiveMembershipHook).inSingletonScope()
        bind<EatLaterHook>(CUREFIT_API_TYPES.EatLaterPageHook).to(EatLaterHook).inSingletonScope()
        bind<DropoutHIWPageHook>(CUREFIT_API_TYPES.DropoutHIWPageHook).to(DropoutHIWPageHook).inSingletonScope()
        bind<IIssueService>(CUREFIT_API_TYPES.IssueService).to(IssueService).inSingletonScope()
        bind<IssueBusiness>(CUREFIT_API_TYPES.IssueBusiness).to(IssueBusiness).inSingletonScope()
        bind<CRMBusiness>(CUREFIT_API_TYPES.CRMBusiness).to(CRMBusiness).inSingletonScope()
        bind<ICaptchaBusiness>(CUREFIT_API_TYPES.CaptchaBusiness).to(CaptchaBusiness).inSingletonScope()
        bind<TherapyPageConfig>(CUREFIT_API_TYPES.TherapyPageConfig).to(TherapyPageConfig).inSingletonScope()
        bind<ReferralDetailsPageConfig>(CUREFIT_API_TYPES.ReferralDetailsPageConfig).to(ReferralDetailsPageConfig).inSingletonScope()
        bind<ReferPageConfig>(CUREFIT_API_TYPES.ReferPageConfig).to(ReferPageConfig).inSingletonScope()
        bind<ICerberusService>(CUREFIT_API_TYPES.CerberusServiceV2).to(CerberusServiceV2).inSingletonScope()
        bind<GearCartViewBuilder>(CUREFIT_API_TYPES.GearCartViewBuilder).to(GearCartViewBuilder).inSingletonScope()
        bind<GearOrderViewBuilder>(CUREFIT_API_TYPES.GearOrderViewBuilder).to(GearOrderViewBuilder).inSingletonScope()
        bind<GearOrderWithShipmentViewBuilder>(CUREFIT_API_TYPES.GearOrderWithShipmentViewBuilder).to(GearOrderWithShipmentViewBuilder).inSingletonScope()
        bind<GearLandingPageBuilder>(CUREFIT_API_TYPES.GearLandingPageBuilder).to(GearLandingPageBuilder).inSingletonScope()
        bind<GearOrderItemViewBuilder>(CUREFIT_API_TYPES.GearOrderItemViewBuilder).to(GearOrderItemViewBuilder).inSingletonScope()
        bind<GearShipmentViewBuilder>(CUREFIT_API_TYPES.GearShipmentViewBuilder).to(GearShipmentViewBuilder).inSingletonScope()
        bind<GearShipmentViewBuilderV2>(CUREFIT_API_TYPES.GearShipmentViewBuilderV2).to(GearShipmentViewBuilderV2).inSingletonScope()
        bind<GearInventoryUnitViewBuilder>(CUREFIT_API_TYPES.GearInventoryUnitViewBuilder).to(GearInventoryUnitViewBuilder).inSingletonScope()
        bind<GearInventoryUnitCancelViewBuilder>(CUREFIT_API_TYPES.GearInventoryUnitCancelViewBuilder).to(GearInventoryUnitCancelViewBuilder).inSingletonScope()
        bind<GearInventoryUnitViewBuilderV2>(CUREFIT_API_TYPES.GearInventoryUnitViewBuilderV2).to(GearInventoryUnitViewBuilderV2).inSingletonScope()
        bind<GearCatalogueLandingPageService>(CUREFIT_API_TYPES.GearCatalogueLandingPageService).to(GearCatalogueLandingPageService).inSingletonScope()
        bind<CultFitSubscriptionDetailViewBuilder>(CUREFIT_API_TYPES.CultFitSubscriptionDetailViewBuilder).to(CultFitSubscriptionDetailViewBuilder).inSingletonScope()
        bind<CultMindSubscriptionDetailViewBuilder>(CUREFIT_API_TYPES.CultMindSubscriptionDetailViewBuilder).to(CultMindSubscriptionDetailViewBuilder).inSingletonScope()
        bind<TransferMembershipViewBuilder>(CUREFIT_API_TYPES.TransferMembershipViewBuilder).to(TransferMembershipViewBuilder).inSingletonScope()
        bind<FitnessTransferMembershipViewBuilder>(CUREFIT_API_TYPES.FitnessTransferMembershipViewBuilder).to(FitnessTransferMembershipViewBuilder).inSingletonScope()
        bind<UpgradeMembershipViewBuilder>(CUREFIT_API_TYPES.UpgradeMembershipViewBuilder).to(UpgradeMembershipViewBuilder).inSingletonScope()
        bind<MembershipAuditTrailViewBuilder>(CUREFIT_API_TYPES.MembershipAuditTrailViewBuilder).to(MembershipAuditTrailViewBuilder).inSingletonScope()
        bind<IUserGoalBusiness>(CUREFIT_API_TYPES.UserGoalBusiness).to(UserGoalBusiness).inSingletonScope()
        bind<CacheHelper>(CUREFIT_API_TYPES.CacheHelper).to(CacheHelper).inSingletonScope()
        bind<IWebActionTransformer>(CUREFIT_API_TYPES.WebActionTransformerUtil).to(WebActionTransformerUtil).inSingletonScope()
        bind<MetricsUtil>(CUREFIT_API_TYPES.MetricsUtil).to(MetricsUtil).inSingletonScope()
        bind<EnterpriseUtil>(CUREFIT_API_TYPES.EnterpriseUtil).to(EnterpriseUtil).inSingletonScope()
        bind<SupportPageConfig>(CUREFIT_API_TYPES.SupportPageConfig).to(SupportPageConfig).inSingletonScope()
        bind<MePageSideBarPageConfig>(CUREFIT_API_TYPES.MePageSideBarPageConfig).to(MePageSideBarPageConfig).inSingletonScope()
        bind<CreateUserFormViewBuilder>(CUREFIT_API_TYPES.CreateUserFormViewBuilder).to(CreateUserFormViewBuilder).inSingletonScope()
        bind<IRedisDao<AnnouncementKey, Announcement>>(CUREFIT_API_TYPES.AnnouncementRedisDao).to(AnnouncementRedisDao).inSingletonScope()
        bind<IRedisDao<AnnouncementDisplayTimeKey, Number>>(CUREFIT_API_TYPES.AnnouncementDisplayTimeRedisDao).to(AnnouncementDisplayTimeRedisDao).inSingletonScope()
        bind<AnnouncementBusiness>(CUREFIT_API_TYPES.AnnouncementBusiness).to(AnnouncementBusiness).inSingletonScope()
        bind<EventInterventionMappingBusiness>(CUREFIT_API_TYPES.EventInterventionMappingBusiness).to(EventInterventionMappingBusiness).inSingletonScope()
        bind<AnnouncementViewBuilder>(CUREFIT_API_TYPES.AnnouncementViewBuilder).to(AnnouncementViewBuilder).inSingletonScope()
        bind<OrderNowHook>(CUREFIT_API_TYPES.OrderNowPageHook).to(OrderNowHook).inSingletonScope()
        bind<WholeFitHook>(CUREFIT_API_TYPES.WholeFitPageHook).to(WholeFitHook).inSingletonScope()
        bind<GymfitCenterPageConfig>(CUREFIT_API_TYPES.GymfitCenterPageConfig).to(GymfitCenterPageConfig).inSingletonScope()
        bind<GymsViewBuilder>(CUREFIT_API_TYPES.GymsViewBuilder).to(GymsViewBuilder).inSingletonScope()
        bind<IVoiceBusiness>(CUREFIT_API_TYPES.VoiceBusiness).to(VoiceBusiness).inSingletonScope()
        bind<NuxViewBuilder>(CUREFIT_API_TYPES.NuxViewBuilder).to(NuxViewBuilder).inSingletonScope()
        bind<PtAtHomeViewBuilder>(CUREFIT_API_TYPES.PtAtHomeViewBuilder).to(PtAtHomeViewBuilder).inSingletonScope()
        bind<FitclubBusiness>(CUREFIT_API_TYPES.FitclubBusiness).to(FitclubBusiness).inSingletonScope()
        bind<PromUtil>(CUREFIT_API_TYPES.PromUtil).to(PromUtil).inSingletonScope()
        bind<CultClassUtil>(CUREFIT_API_TYPES.CultClassUtil).to(CultClassUtil).inSingletonScope()
        bind<AgentMetricUtil>(CUREFIT_API_TYPES.AgentMetricUtil).to(AgentMetricUtil).inSingletonScope()
        bind<CareCenterViewBuilder>(CUREFIT_API_TYPES.CareCenterViewBuilder).to(CareCenterViewBuilder).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.Cafe).to(Cafe).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.OnlineKiosk).to(OnlineKiosk).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.FoodMarketplace).to(FoodMarketplace).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.WholeFit).to(WholeFit).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.Marketplace).to(MarketPlace).inSingletonScope()
        bind<SelectSpecialityPageViewBuilder>(CUREFIT_API_TYPES.SelectSpecialityPageViewBuilder).to(SelectSpecialityPageViewBuilder).inSingletonScope()
        bind<CareCenterBrowseViewBuilder>(CUREFIT_API_TYPES.CareCenterBrowseViewBuilder).to(CareCenterBrowseViewBuilder).inSingletonScope()
        bind<FitnessReportPageViewBuilder>(CUREFIT_API_TYPES.FitnessReportPageViewBuilder).to(FitnessReportPageViewBuilder).inSingletonScope()
        bind<FitnessReportPageConfig>(CUREFIT_API_TYPES.FitnessReportPageConfig).to(FitnessReportPageConfig).inSingletonScope()

        bind<UserSleepAdapter>(CUREFIT_API_TYPES.UserSleepAdapter).to(UserSleepAdapter).inSingletonScope()
        bind<WidgetRankingBusiness>(CUREFIT_API_TYPES.WidgetRankingBusiness).to(WidgetRankingBusiness).inSingletonScope()
        bind<UserTokenSchema>(CUREFIT_API_TYPES.UserTokenSchema).to(UserTokenSchema).inSingletonScope()
        bind<UserTokenAdapter>(CUREFIT_API_TYPES.UserTokenAdapter).to(UserTokenAdapter).inSingletonScope()
        bind<IUserTokenReadonlyDao>(CUREFIT_API_TYPES.UserTokenReadonlyDao).to(UserTokenReadonlyDaoMysqlImpl).inSingletonScope()
        bind<IUserTokenReadWriteDao>(CUREFIT_API_TYPES.UserTokenReadWriteDao).to(UserTokenReadWriteDaoMysqlImpl).inSingletonScope()
        bind<UserDeviceSchema>(CUREFIT_API_TYPES.UserDeviceSchema).to(UserDeviceSchema).inSingletonScope()
        bind<UserDeviceAdapter>(CUREFIT_API_TYPES.UserDeviceAdapter).to(UserDeviceAdapter).inSingletonScope()
        bind<IUserDeviceReadonlyDao>(CUREFIT_API_TYPES.UserDeviceReadonlyDao).to(UserDeviceReadonlyDaoMysqlImpl).inSingletonScope()
        bind<IUserDeviceReadWriteDao>(CUREFIT_API_TYPES.UserDeviceReadWriteDao).to(UserDeviceReadWriteDaoMysqlImpl).inSingletonScope()
        bind<SupportFAQSchema>(CUREFIT_API_TYPES.SupportFAQSchema).to(SupportFAQSchema).inSingletonScope()
        bind<SupportFAQReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.SupportFAQReadOnlyDaoMongoImpl).to(SupportFAQReadOnlyDaoMongoImpl).inSingletonScope()
        bind<SupportFAQReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.SupportFAQReadWriteDaoMongoImpl).to(SupportFAQReadWriteDaoMongoImpl).inSingletonScope()
        bind<ChatEventLogSchema>(CUREFIT_API_TYPES.ChatEventLogSchema).to(ChatEventLogSchema).inSingletonScope()
        bind<ChatEventLogReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.ChatEventLogReadOnlyDaoMongoImpl).to(ChatEventLogReadOnlyDaoMongoImpl).inSingletonScope()
        bind<ChatEventLogReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.ChatEventLogReadWriteDaoMongoImpl).to(ChatEventLogReadWriteDaoMongoImpl).inSingletonScope()
        bind<SupportFAQConfigCache>(CUREFIT_API_TYPES.SupportFAQConfigCache).to(SupportFAQConfigCache).inSingletonScope()
        bind<UserActionMappingBusiness>(CUREFIT_API_TYPES.UserActionMappingBusiness).to(UserActionMappingBusiness).inSingletonScope()
        bind<CultMemoriesViewBuilder>(CUREFIT_API_TYPES.CultMemoriesViewBuilder).to(CultMemoriesViewBuilder).inSingletonScope()
        bind<IWidgetDataProvider>(CUREFIT_API_TYPES.EatMarketplaceDataProvider).to(EatMarketplaceDataProvider).inSingletonScope()
        bind<IFCPage>(CUREFIT_API_TYPES.FCPage).to(FCPageBuilder).inSingletonScope()
        bind<FMRestaurantListBuilder>(CUREFIT_API_TYPES.FMRestaurantListViewBuilder).to(FMRestaurantListBuilder).inSingletonScope()
        bind<IBranchService>(CUREFIT_API_TYPES.BranchService).to(BranchService).inSingletonScope()
        bind<IEatMktPlaceOrderConfirmationPage>(CUREFIT_API_TYPES.EatMktPlaceOrderConfirmationBuilder).to(EatMktPlaceOrderConfirmationPageBuilder).inSingletonScope()
        bind<UserYearEndReportSchema>(CUREFIT_API_TYPES.UserYearEndReportSchema).to(UserYearEndReportSchema).inSingletonScope()
        bind<UserYearEndReportReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.UserYearEndReportReadOnlyDaoMongoImpl).to(UserYearEndReportReadOnlyDaoMongoImpl).inSingletonScope()
        bind<UserYearEndReportReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.UserYearEndReportReadWriteDaoMongoImpl).to(UserYearEndReportReadWriteDaoMongoImpl).inSingletonScope()

        bind<UserYearEndSummarySchema>(CUREFIT_API_TYPES.UserYearEndSummarySchema).to(UserYearEndSummarySchema).inSingletonScope()
        bind<UserAchievmentShowcaseSchema>(CUREFIT_API_TYPES.UserAchievmentShowcaseSchema).to(UserAchievmentShowcaseSchema).inSingletonScope()
        bind<UserYearEndSummaryReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.UserYearEndSummaryReadOnlyDaoMongoImpl).to(UserYearEndSummaryReadOnlyDaoMongoImpl).inSingletonScope()
        bind<UserAchievmentShowcaseReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.UserAchievmentShowcaseReadOnlyDaoMongoImpl).to(UserAchievmentShowcaseReadOnlyDaoMongoImpl).inSingletonScope()
        bind<UserYearEndSummaryReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.UserYearEndSummaryReadWriteDaoMongoImpl).to(UserYearEndSummaryReadWriteDaoMongoImpl).inSingletonScope()
        bind<UserAchievmentShowcaseReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.UserAchievmentShowcaseReadWriteDaoMongoImpl).to(UserAchievmentShowcaseReadWriteDaoMongoImpl).inSingletonScope()
        // YER Character Reveal
        bind<UserYERCharacterRevealReportSchema>(CUREFIT_API_TYPES.UserYERCharacterRevealReportSchema).to(UserYERCharacterRevealReportSchema).inSingletonScope()
        bind<UserYERCharacterRevealReportReadOnlyDaoMongoImpl>(CUREFIT_API_TYPES.UserYERCharacterRevealReportReadOnlyDaoMongoImpl).to(UserYERCharacterRevealReportReadOnlyDaoMongoImpl).inSingletonScope()
        bind<UserYERCharacterRevealReportReadWriteDaoMongoImpl>(CUREFIT_API_TYPES.UserYERCharacterRevealReportReadWriteDaoMongoImpl).to(UserYERCharacterRevealReportReadWriteDaoMongoImpl).inSingletonScope()

        // Segment Evaluators
        bind<CitiesConditionEvaluator>(CUREFIT_API_TYPES.CitiesConditionEvaluator).to(CitiesConditionEvaluator).inSingletonScope()
        bind<AreasConditionEvaluator>(CUREFIT_API_TYPES.AreasConditionEvaluator).to(AreasConditionEvaluator).inSingletonScope()
        bind<DeliveryChannelConditionEvaluator>(CUREFIT_API_TYPES.DeliveryChannelConditionEvaluator).to(DeliveryChannelConditionEvaluator).inSingletonScope()
        bind<EmailSetConditionEvaluator>(CUREFIT_API_TYPES.EmailSetConditionEvaluator).to(EmailSetConditionEvaluator).inSingletonScope()
        bind<UserAgentsConditionEvaluator>(CUREFIT_API_TYPES.UserAgentsConditionEvaluator).to(UserAgentsConditionEvaluator).inSingletonScope()
        bind<CultSegmentsConditionEvaluator>(CUREFIT_API_TYPES.CultSegmentsConditionEvaluator).to(CultSegmentsConditionEvaluator).inSingletonScope()
        bind<MindSegmentsConditionEvaluator>(CUREFIT_API_TYPES.MindSegmentsConditionEvaluator).to(MindSegmentsConditionEvaluator).inSingletonScope()
        bind<MindTherapySegmentsConditionEvaluator>(CUREFIT_API_TYPES.MindTherapySegmentsConditionEvaluator).to(MindTherapySegmentsConditionEvaluator).inSingletonScope()
        bind<PTUserSegmentsConditionEvaluator>(CUREFIT_API_TYPES.PTUserSegmentsConditionEvaluator).to(PTUserSegmentsConditionEvaluator).inSingletonScope()
        bind<LivePTUserSegmentsConditionEvaluator>(CUREFIT_API_TYPES.LivePTUserSegmentsConditionEvaluator).to(LivePTUserSegmentsConditionEvaluator).inSingletonScope()
        bind<CareSegmentsConditionEvaluator>(CUREFIT_API_TYPES.CareSegmentsConditionEvaluator).to(CareSegmentsConditionEvaluator).inSingletonScope()
        bind<SourcesConditionEvaluator>(CUREFIT_API_TYPES.SourcesConditionEvaluator).to(SourcesConditionEvaluator).inSingletonScope()
        bind<CultDIYSegmentsConditionEvaluator>(CUREFIT_API_TYPES.CultDIYSegmentsConditionEvaluator).to(CultDIYSegmentsConditionEvaluator).inSingletonScope()
        bind<MindDIYSegmentsConditionEvaluator>(CUREFIT_API_TYPES.MindDIYSegmentsConditionEvaluator).to(MindDIYSegmentsConditionEvaluator).inSingletonScope()
        bind<FitclubConditionEvaluator>(CUREFIT_API_TYPES.FitclubConditionEvaluator).to(FitclubConditionEvaluator).inSingletonScope()
        bind<EmailSuffixConditionEvaluator>(CUREFIT_API_TYPES.EmailSuffixConditionEvaluator).to(EmailSuffixConditionEvaluator).inSingletonScope()
        bind<OffersConditionEvaluator>(CUREFIT_API_TYPES.OffersConditionEvaluator).to(OffersConditionEvaluator).inSingletonScope()
        bind<VerticalsConditionEvaluator>(CUREFIT_API_TYPES.VerticalsConditionEvaluator).to(VerticalsConditionEvaluator).inSingletonScope()
        bind<GymSegmentsConditionEvaluator>(CUREFIT_API_TYPES.GymSegmentsConditionEvaluator).to(GymSegmentsConditionEvaluator).inSingletonScope()
        bind<SegmentEvaluator>(CUREFIT_API_TYPES.SegmentEvaluator).to(SegmentEvaluator).inSingletonScope()
        bind<HamletConditionEvaluator>(CUREFIT_API_TYPES.HamletConditionEvaluator).to(HamletConditionEvaluator).inSingletonScope()
        bind<PlatformSegmentConditionEvaluator>(CUREFIT_API_TYPES.PlatformSegmentConditionEvaluator).to(PlatformSegmentConditionEvaluator).inSingletonScope()
        bind<ProductTypeSegmentConditionEvaluator>(CUREFIT_API_TYPES.ProductTypeSegmentsConditionEvaluator).to(ProductTypeSegmentConditionEvaluator).inSingletonScope()
        bind<UserTypeConditionEvaluator>(CUREFIT_API_TYPES.UserTypeConditionEvaluator).to(UserTypeConditionEvaluator).inSingletonScope()
        bind<SupportedCodePushVersionEvaluator>(CUREFIT_API_TYPES.SupportedCodePushVersionEvaluator).to(SupportedCodePushVersionEvaluator).inSingletonScope()
        bind<CultsportArticleTypeSegmentEvaluator>(CUREFIT_API_TYPES.CultsportArticleTypeSegmentEvaluator).to(CultsportArticleTypeSegmentEvaluator).inSingletonScope()
        bind<CultsportBrandSegmentEvaluator>(CUREFIT_API_TYPES.CultsportBrandSegmentEvaluator).to(CultsportBrandSegmentEvaluator).inSingletonScope()
        bind<CultsportCategorySegmentEvaluator>(CUREFIT_API_TYPES.CultsportCategorySegmentEvaluator).to(CultsportCategorySegmentEvaluator).inSingletonScope()
        bind<CultsportCollectionTypeSegmentEvaluator>(CUREFIT_API_TYPES.CultsportCollectionTypeSegmentEvaluator).to(CultsportCollectionTypeSegmentEvaluator).inSingletonScope()
        bind<CultsportProductIdSegmentEvaluator>(CUREFIT_API_TYPES.CultsportProductIdSegmentEvaluator).to(CultsportProductIdSegmentEvaluator).inSingletonScope()
        bind<DigitalCalendarViewBuilder>(CUREFIT_API_TYPES.DigitalCalendarViewBuilder).to(DigitalCalendarViewBuilder).inSingletonScope()
        bind<DigitalLeagueSectionViewBuilder>(CUREFIT_API_TYPES.DigitalLeagueSectionViewBuilder).to(DigitalLeagueSectionViewBuilder).inSingletonScope()
        bind<DigitalSocialLeagueTabPageViewBuilder>(CUREFIT_API_TYPES.DigitalSocialLeagueTabPageViewBuilder).to(DigitalSocialLeagueTabPageViewBuilder).inSingletonScope()
        bind<DigitalMomentsViewBuilder>(CUREFIT_API_TYPES.DigitalMomentsViewBuilder).to(DigitalMomentsViewBuilder).inSingletonScope()
        bind<DigitalWorkoutMemberHistoryBuilder>(CUREFIT_API_TYPES.DigitalWorkoutMemberHistoryBuilder).to(DigitalWorkoutMemberHistoryBuilder).inSingletonScope()
        bind<DigitalReportViewBuilder>(CUREFIT_API_TYPES.DigitalReportViewBuilder).to(DigitalReportViewBuilder).inSingletonScope()
        bind<SocialKnowMoreViewBuilder>(CUREFIT_API_TYPES.SocialKnowMoreViewBuilder).to(SocialKnowMoreViewBuilder).inSingletonScope()
        bind<GymfitPackPageConfig>(CUREFIT_API_TYPES.GymfitPackPageConfig).to(GymfitPackPageConfig).inSingletonScope()
        bind<GymfitCheckinConfirmationPageConfig>(CUREFIT_API_TYPES.GymfitCheckinConfirmationPageConfig).to(GymfitCheckinConfirmationPageConfig).inSingletonScope()
        bind<GymfitCenterDetailsWebViewBuilder>(CUREFIT_API_TYPES.GymfitCenterDetailsWebViewBuilder).to(GymfitCenterDetailsWebViewBuilder).inSingletonScope()
        bind<GymfitCenterDetailsAppViewBuilder>(CUREFIT_API_TYPES.GymfitCenterDetailsAppViewBuilder).to(GymfitCenterDetailsAppViewBuilder).inSingletonScope()
        bind<GymfitPackDetailViewBuilder>(CUREFIT_API_TYPES.GymfitPackDetailViewBuilder).to(GymfitPackDetailViewBuilder).inSingletonScope()
        bind<PlayPackDetailViewBuilder>(CUREFIT_API_TYPES.PlayPackDetailViewBuilder).to(PlayPackDetailViewBuilder).inSingletonScope()
        bind<PlaySelectPackViewBuilder>(CUREFIT_API_TYPES.PlaySelectPackViewBuilder).to(PlaySelectPackViewBuilder).inSingletonScope()
        bind<PlayPackViewBuilder>(CUREFIT_API_TYPES.PlayPackViewBuilder).to(PlayPackViewBuilder).inSingletonScope()
        bind<PlayUpgradeDetailViewBuilder>(CUREFIT_API_TYPES.PlayUpgradeDetailViewBuilder).to(PlayUpgradeDetailViewBuilder).inSingletonScope()
        bind<GymfitClassScheduleViewBuilder>(CUREFIT_API_TYPES.GymfitClassScheduleViewBuilder).to(GymfitClassScheduleViewBuilder).inSingletonScope()
        bind<GymfitCheckinViewBuilder>(CUREFIT_API_TYPES.GymfitCheckinViewBuilder).to(GymfitCheckinViewBuilder).inSingletonScope()
        bind<GymLocalitySelectorViewBuilder>(CUREFIT_API_TYPES.GymLocalitySelectorViewBuilder).to(GymLocalitySelectorViewBuilder).inSingletonScope()
        bind<GymUniversalCheckinViewBuilder>(CUREFIT_API_TYPES.GymUniversalCheckinViewBuilder).to(GymUniversalCheckinViewBuilder).inSingletonScope()
        bind<IGymfitBusiness>(CUREFIT_API_TYPES.GymfitBusiness).to(GymfitBusiness).inSingletonScope()
        bind<GiftCardViewBuilder>(CUREFIT_API_TYPES.GiftCardViewBuilder).to(GiftCardViewBuilder).inSingletonScope()
        bind<AppLayoutBuilder>(CUREFIT_API_TYPES.AppLayoutBuilder).to(AppLayoutBuilder).inSingletonScope()
        bind<CultCenterPageViewBuilder>(CUREFIT_API_TYPES.CultCenterPageViewBuilder).to(CultCenterPageViewBuilder).inSingletonScope()
        bind<IRedisDao<ClassInviteLinkKey, ClassInviteLink>>(CUREFIT_API_TYPES.ClassInviteLinkRedisDao).to(ClassInviteLinkRedisDao).inSingletonScope()
        bind<IRedisDao<LiveClassSlotKey, LiveClass>>(CUREFIT_API_TYPES.LiveClassSlotRedisDao).to(LiveClassSlotRedisDao).inSingletonScope()
        bind<ClassInviteLinkCreator>(CUREFIT_API_TYPES.ClassInviteLinkCreator).to(ClassInviteLinkCreator).inSingletonScope()
        bind<LiveClassSlotCreator>(CUREFIT_API_TYPES.LiveClassSlotCreator).to(LiveClassSlotCreator).inSingletonScope()
        bind<GiftCardTransformationService>(CUREFIT_API_TYPES.GiftCardTransformationService).to(GiftCardTransformationService).inSingletonScope()
        bind<ReferralTransformationService>(CUREFIT_API_TYPES.ReferralTransformationService).to(ReferralTransformationService).inSingletonScope()
        bind<MyReferralViewBuilder>(CUREFIT_API_TYPES.MyReferralViewBuilder).to(MyReferralViewBuilder).inSingletonScope()
        bind<UserErrorBusiness>(CUREFIT_API_TYPES.UserErrorBusiness).to(UserErrorBusiness).inSingletonScope()
        bind<ChallengesViewBuilder>(CUREFIT_API_TYPES.ChallengesViewBuilder).to(ChallengesViewBuilder).inSingletonScope()
        bind<ChallengeDetailsViewBuilder>(CUREFIT_API_TYPES.ChallengeDetailsViewBuilder).to(ChallengeDetailsViewBuilder).inSingletonScope()
        bind<MaximusService>(CUREFIT_API_TYPES.MaximusService).to(MaximusService).inSingletonScope()
        // wholefitv2
        bind<WholefitBusiness>(CUREFIT_API_TYPES.WholefitBusiness).to(WholefitBusiness).inSingletonScope()
        bind<WholefitBrandPageBuilder>(CUREFIT_API_TYPES.WholefitBrandPageBuilder).to(WholefitBrandPageBuilder).inSingletonScope()
        bind<WholefitV2Hook>(CUREFIT_API_TYPES.WholefitV2PageHook).to(WholefitV2Hook).inSingletonScope()
        bind<WellnessClpHook>(CUREFIT_API_TYPES.WellnessClpHook).to(WellnessClpHook).inSingletonScope()
        bind<WholefitSearchPageBuilder>(CUREFIT_API_TYPES.WholefitSearchPageBuilder).to(WholefitSearchPageBuilder).inSingletonScope()
        bind<WholefitProductDetailViewBuilder>(CUREFIT_API_TYPES.WholefitProductDetailPageBuilder).to(WholefitProductDetailViewBuilder).inSingletonScope()
        bind<CultWorkoutsViewBuilder>(CUREFIT_API_TYPES.CultWorkoutsViewBuilder).to(CultWorkoutsViewBuilder).inSingletonScope()
        bind<CultSchedulePageConfig>(CUREFIT_API_TYPES.CultSchedulePageConfig).to(CultSchedulePageConfig).inSingletonScope()
        bind<CultWorkoutDetailViewBuilder>(CUREFIT_API_TYPES.CultWorkoutDetailViewBuilder).to(CultWorkoutDetailViewBuilder).inSingletonScope()
        bind<CFAPIJavaService>(CUREFIT_API_TYPES.CFAPIJavaService).to(CFAPIJavaService).inSingletonScope()
        // recipe/eat.live
        bind<RecipeClpProductsViewBuilder>(CUREFIT_API_TYPES.RecipeClpProductsViewBuilder).to(RecipeClpProductsViewBuilder).inSingletonScope()
        bind<LivePacksViewBuilder>(CUREFIT_API_TYPES.LivePacksViewBuilder).to(LivePacksViewBuilder).inSingletonScope()

        bind<NutritionistPlanProductPageViewBuilder>(CUREFIT_API_TYPES.NutritionistPlanProductPageViewBuilder).to(NutritionistPlanProductPageViewBuilder).inSingletonScope()
        bind<IDeepLinkService>(CUREFIT_API_TYPES.DeepLinkService).to(DeepLinkService).inSingletonScope()
        bind<LiveMembershipExpiredModalViewBuilder>(CUREFIT_API_TYPES.LiveMembershipExpiredModalViewBuilder).to(LiveMembershipExpiredModalViewBuilder).inSingletonScope()
        bind<TLLivePacksModalViewBuilder>(CUREFIT_API_TYPES.TLLivePacksModalViewBuilder).to(TLLivePacksModalViewBuilder).inSingletonScope()
        bind<LiveMembershipViewBuilder>(CUREFIT_API_TYPES.LiveMembershipViewBuilder).to(LiveMembershipViewBuilder).inSingletonScope()
        bind<DigitalSessionListViewBuilder>(CUREFIT_API_TYPES.DigitalSessionListViewBuilder).to(DigitalSessionListViewBuilder).inSingletonScope()
        bind<DIYClassDetailViewBuilder>(CUREFIT_API_TYPES.DIYClassDetailViewBuilder).to(DIYClassDetailViewBuilder).inSingletonScope()
        bind<RecipeSearchBuilder>(CUREFIT_API_TYPES.RecipeSearchBuilder).to(RecipeSearchBuilder).inSingletonScope()
        bind<OnDemandProductCollectionViewBuilder>(CUREFIT_API_TYPES.OnDemandProductViewBuilder).to(OnDemandProductCollectionViewBuilder).inSingletonScope()
        bind<RecipeCollectionDetailViewBuilder>(CUREFIT_API_TYPES.RecipeCollectionDetailViewBuilder).to(RecipeCollectionDetailViewBuilder).inSingletonScope()
        bind<LivePremiereProductViewBuilder>(CUREFIT_API_TYPES.LivePremiereProductViewBuilder).to(LivePremiereProductViewBuilder).inSingletonScope()

        bind<CareCultDayTransferPageViewBuilder>(CUREFIT_API_TYPES.CareCultDayTransferPageViewBuilder).to(CareCultDayTransferPageViewBuilder).inSingletonScope()
        bind<OnDemandAutoPlayBuilder>(CUREFIT_API_TYPES.OnDemandAutoPlayBuilder).to(OnDemandAutoPlayBuilder).inSingletonScope()
        const errorFactory: ErrorFactory = new ErrorFactory(CF_API_NAMESPACE, new ExpressHttpError())
        bind<ErrorFactory>(CUREFIT_API_TYPES.ErrorFactory).toConstantValue(errorFactory)
        bind<ICareBusiness>(CUREFIT_API_TYPES.CareBusiness).to(CareBusiness).inSingletonScope()
        bind<MindTherapyPhysioPacksProductPageViewBuilder>(CUREFIT_API_TYPES.MindTherapyPhysioPacksProductPageViewBuilder).to(MindTherapyPhysioPacksProductPageViewBuilder).inSingletonScope()
        bind<LivePTBookingDetailViewBuilder>(CUREFIT_API_TYPES.LivePTBookingDetailViewBuilder).to(LivePTBookingDetailViewBuilder).inSingletonScope()
        bind<LiveSGTUserSegmentsConditionEvaluator>(CUREFIT_API_TYPES.LiveSGTUserSegmentsConditionEvaluator).to(LiveSGTUserSegmentsConditionEvaluator).inSingletonScope()
        bind<DigitalLeagueChallengeViewBuilder>(CUREFIT_API_TYPES.DigitalLeagueChallengeViewBuilder).to(DigitalLeagueChallengeViewBuilder).inSingletonScope()
        bind<IRedisDao<AppFeedbackKey, UserFormRequest>>(CUREFIT_API_TYPES.AppFeedbackRedisDao).to(FeedbackRedisDao).inSingletonScope()
        bind<GP99ProductPageViewBuilder>(CUREFIT_API_TYPES.GP99ProductPageViewBuilder).to(GP99ProductPageViewBuilder).inSingletonScope()
        bind<LivePTReportPageViewBuilder>(CUREFIT_API_TYPES.LivePTReportPageViewBuilder).to(LivePTReportPageViewBuilder).inSingletonScope()
        bind<IAppFeedback>(CUREFIT_API_TYPES.AppFeedbackService).to(AppFeedbackService).inSingletonScope()
        bind<ISegmentService>(CUREFIT_API_TYPES.SegmentService).to(SegmentServiceFactory(kernel)).inSingletonScope()
        bind<CustomEventEmitter>(CUREFIT_API_TYPES.CustomEventEmitter).to(CustomEventEmitter).inSingletonScope()
        bind<ExternalSourceUtil>(CUREFIT_API_TYPES.ExternalSourceUtil).to(ExternalSourceUtil).inSingletonScope()
        bind<any>(CUREFIT_API_TYPES.GympassConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig && appConfig.backendServices
        }).inSingletonScope()
        bind<RecommendedFirstClassesViewBuilder>(CUREFIT_API_TYPES.RecommendedFirstClassesViewBuilder).to(RecommendedFirstClassesViewBuilder).inSingletonScope()
        bind<InterventionHandler>(CUREFIT_API_TYPES.PTSGTNoShowHandler).to(PTSGTNoShowHandler).inSingletonScope()
        bind<LiveClassDetailViewBuilderTV>(CUREFIT_API_TYPES.LiveClassDetailViewBuilderTV).to(LiveClassDetailViewBuilderTV)
        bind<YogaReportCardWidgetViewBuilder>(CUREFIT_API_TYPES.YogaReportCardWidgetViewBuilder).to(YogaReportCardWidgetViewBuilder).inSingletonScope()
        bind<YogaBarGraphWidgetViewBuilder>(CUREFIT_API_TYPES.YogaBarGraphWidgetViewBuilder).to(YogaBarGraphWidgetViewBuilder).inSingletonScope()
        bind<SeriesViewBuilder>(CUREFIT_API_TYPES.SeriesViewBuilder).to(SeriesViewBuilder).inSingletonScope()
        bind<YogaTimeStampedProgressPills>(CUREFIT_API_TYPES.YogaTimeStampedProgressPills).to(YogaTimeStampedProgressPills).inSingletonScope()
        // AppConfig Service
        AppConfigUtil._initialize()
        bind<SoftBookingBuilder>(CUREFIT_API_TYPES.SoftBookingBuilder).to(SoftBookingBuilder).inSingletonScope()
        bind<GymfitCheckinConfirmationViewBuilder>(CUREFIT_API_TYPES.GymfitCheckinConfirmationViewBuilder).to(GymfitCheckinConfirmationViewBuilder).inSingletonScope()
        bind<PNTokenEventQueueListener>(CUREFIT_API_TYPES.PNTokenEventQueueListener).to(PNTokenEventQueueListener).inSingletonScope()
        bind<UserYearlyReportBusiness>(CUREFIT_API_TYPES.UserYearlyReportBusiness).to(UserYearlyReportBusiness).inSingletonScope()
        bind<UserYearReport2022Business>(CUREFIT_API_TYPES.UserQuiz2021Business).to(UserYearReport2022Business).inSingletonScope()
        bind<UserYearReport2023Business>(CUREFIT_API_TYPES.UserYearReport2023Business).to(UserYearReport2023Business).inSingletonScope()
        bind<SGTPreBookingDetailPageViewBuilder>(CUREFIT_API_TYPES.SGTPreBookingDetailPageViewBuilder).to(SGTPreBookingDetailPageViewBuilder).inSingletonScope()
        bind<FitnesshubPageHook>(CUREFIT_API_TYPES.FitnesshubPageHook).to(FitnesshubPageHook).inSingletonScope()
        bind<SportshubPageHook>(CUREFIT_API_TYPES.SportshubPageHook).to(SportshubPageHook).inSingletonScope()
        bind<IEatBrand>(CUREFIT_API_TYPES.CultBike).to(CultBike).inSingletonScope()
        bind<ProgramPageHook>(CUREFIT_API_TYPES.ProgramPageHook).to(ProgramPageHook).inSingletonScope()
        bind<CoachProgramPageHook>(CUREFIT_API_TYPES.CoachProgramPageHook).to(CoachProgramPageHook).inSingletonScope()

        // vertical specific feedback consumers
        bind<VerticalSpecificFeedbackConsumer>(CUREFIT_API_TYPES.VerticleSpecificFeedbackConsumer).to(VerticalSpecificFeedbackConsumer).inSingletonScope()
        bind<SportListPageViewBuilder>(CUREFIT_API_TYPES.SportListPageViewBuilder).to(SportListPageViewBuilder).inSingletonScope()
        bind<GearFeedbackSNSProducer>(CUREFIT_API_TYPES.GearFeedbackSNSProducer).to(GearFeedbackSNSProducer).inSingletonScope()
        bind<TataNeuService>(CUREFIT_API_TYPES.TataNeuService).to(TataNeuService).inSingletonScope()

        // mixpanel event service
        bind<MixpanelEventService>(CUREFIT_API_TYPES.MixpanelEventService).to(MixpanelEventService).inSingletonScope()
        bind<ICFAnalyticsConfig>(CUREFIT_API_TYPES.CFAnalyticsConfig).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig.backendServices.cfApiAnalyticsConfig
        }).inSingletonScope()
        bind<ICFAnalytics>(CUREFIT_API_TYPES.CFAnalytics).to(CFAnalytics).inSingletonScope()

        bind<DIYPackService>(CUREFIT_API_TYPES.DIYPackService).to(DIYPackService).inSingletonScope()
        bind<ICultstoreShopifyService>(CUREFIT_API_TYPES.CultstoreShopifyService).to(CultstoreShopifyService).inSingletonScope()
        bind<ICultstoreShopifyConf>(CUREFIT_API_TYPES.CultstoreShopifyConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig && appConfig.cultstoreShopify
        }).inSingletonScope()

        bind<ITesseractServiceConf>(CUREFIT_API_TYPES.TesseractServiceConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig.backendServices.tesseractService
        }).inSingletonScope()
        bind<IMystiqueServiceConf>(CUREFIT_API_TYPES.MystiqueServiceConf).toDynamicValue((context: interfaces.Context) => {
            const appConfig = context.container.get<any>(BASE_TYPES.Configuration).getConfiguration()
            return appConfig.backendServices.mystiqueService
        }).inSingletonScope()
    })
}

export default BusinessKernelModule
