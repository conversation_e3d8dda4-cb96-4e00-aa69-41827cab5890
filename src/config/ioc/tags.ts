const TAGS = {
    AuthController: "Auth<PERSON><PERSON>roller",
    CR<PERSON>ontroller: "<PERSON><PERSON><PERSON>roller",
    <PERSON>leverTapController: "CleverTapController",
    SitemapController: "SitemapController",
    UserController: "User<PERSON><PERSON>roller",
    <PERSON><PERSON><PERSON><PERSON>roller: "<PERSON><PERSON><PERSON><PERSON>roller",
    ProductController: "ProductController",
    LogisticsController: "LogisticsController",
    UrbanPiperOrderController: "UrbanPiperOrderController",
    KitchensAtController: "KitchensAtController",
    ExtLogisticsController: "ExtLogisticsController",
    <PERSON><PERSON><PERSON><PERSON>roller: "CartController",
    OnboardingController: "OnboardingController",
    <PERSON><PERSON>art<PERSON><PERSON>roller: "GearCartController",
    PaymentController: "PaymentController",
    <PERSON><PERSON><PERSON>ontroller: "FuseController",
    TwilioController: "<PERSON>wi<PERSON><PERSON>ontroller",
    StatusController: "Status<PERSON><PERSON>roller",
    PackController: "Pack<PERSON>ontroller",
    Page<PERSON>ontroller: "<PERSON><PERSON><PERSON>roll<PERSON>",
    <PERSON><PERSON><PERSON><PERSON>roll<PERSON>: "<PERSON><PERSON><PERSON><PERSON>roller",
    <PERSON><PERSON><PERSON>roller: "<PERSON><PERSON><PERSON>roll<PERSON>",
    <PERSON><PERSON><PERSON><PERSON>roller: "PulseController",
    CityController: "CityController",
    OrderController: "OrderController",
    FeedbackController: "FeedbackController",
    NotificationController: "NotificationController",
    CareController: "CareController",
    ChronicCareController: "ChronicCareController",
    UserFormController: "UserFormController",
    LiveTrackingController: "LiveTrackingController",
    LeadController: "LeadController",
    QuestController: "QuestController",
    RecipeController: "RecipeController",
    ProgramController: "ProgramController",
    TPOffersController: "TPOffersController",
    QSRController: "QSRController",
    InterventionController: "InterventionController",
    MaximusController: "MaximusController",
    MetricController: "MetricController",
    UserTestController: "UserTestController",
    ProductFeedController: "ProductFeedController",
    GearOrderItemController: "GearOrderItemController",
    GearInventoryUnitController: "GearInventoryUnitController",
    GearInventoryUnitControllerV2: "GearInventoryUnitControllerV2",
    GearWebhooksController: "GearWebhooksController",
    GearShipmentController: "GearShipmentController",
    UserGoalController: "UserGoalController",
    ReferralController: "ReferralController",
    FitcashController: "FitcashController",
    DigitalController: "DigitalController",
    GiftCardController: "GiftCardController",
    GearController: "GearController"
}

export default TAGS
