import * as express from "express"

const usrg = require("express-useragent/lib/express-useragent")
const UserAgent = usrg.UserAgent

export const useragentMiddleware = async function cfApiUseragentMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const source = req.headers["user-agent"] || "unknown"
        const ua = new UserAgent()
        ua.Agent.source = source.trim()
        ua.Agent.browser = ua.getBrowser(ua.Agent.source)
        ua.testBot()
        ua.testMobile()
        req.useragent = ua.Agent
        res.locals.useragent = ua.Agent
        next()
    } catch (e) {
        next()
    }
}
