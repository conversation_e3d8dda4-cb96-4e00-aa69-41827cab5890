import AppUtil from "../../app/util/AppUtil"
import * as express from "express"


export const clientMiddleware = async function cfApiClientMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    req.headers["appversion"] = AppUtil.getAppVersion(req)
    /**
     * clientversion will be used in future for all apps to identify version  from the requests coming from multiple apps.
     * To ensure appversion is always set, as it is used at multiple places, setting it equal to clientversion
     */
    if (req.headers["clientversion"] && !req.headers["appversion"]) {
        req.headers["appversion"] = req.headers["clientversion"] as string
    }

    if (!req.headers["clientversion"]) {
        req.headers["clientversion"] = req.headers["appversion"]
    }
    next()
}
