import * as express from "express"
import * as bodyParser from "body-parser"

class AllMiddlewares {

    static get configuration() {
        const app = express()
        app.use(bodyParser.json({ limit: "2mb" }))
        app.use(bodyParser.urlencoded({ extended: true, limit: "2mb" }))
        app.use(bodyParser.text({ limit: "2mb" }))
        return app
    }
}

Object.seal(AllMiddlewares)
export default AllMiddlewares
