import * as express from "express"
import kernel from "../ioc/ioc"
import { BASE_TYPES, Logger } from "@curefit/base"
import CUREFIT_API_TYPES from "../ioc/types"
import { MultiRedisAccess, REDIS_TYPES, IMultiCrudKeyValue } from "@curefit/redis-utils"
import { BlockingType } from "../../app/metrics/models/BlockingType"
import MetricsUtil from "../../app/metrics/MetricsUtil"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"

const crypto = require("crypto")

const logger = kernel.get<Logger>(BASE_TYPES.ILogger)
const metricsUtil = kernel.get<MetricsUtil>(CUREFIT_API_TYPES.MetricsUtil)
const rollbarService = kernel.get<RollbarService>(ERROR_COMMON_TYPES.RollbarService)
const multiCrudKeyValueDao = kernel.get<IMultiCrudKeyValue>(REDIS_TYPES.MultiCrudKeyValueDao)

const redisCrudDao = multiCrudKeyValueDao.getICrudKeyValue("RATE-LIMIT")

interface RateLimitOptions {
    total: number
    expire: number
    reset?: number
    remaining?: number
}

function createHash(data: string, len: number) {
    return crypto.createHash("shake256", { outputLength: len })
        .update(data)
        .digest("hex")
}

export const ipRateLimitMiddleware = async function cfApiIpRateLimitMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        if (res.locals.isWhitelisted) {
            return next()
        }

        const ip: string = res.locals.ip ?? req.ip
        const isBot = req.useragent.isBot

        const key = `iprl:${createHash(ip, 6)}`

        const opts: RateLimitOptions = {
            total: isBot ? 10_000 : 2_000,
            expire: 60 * 2,
        }

        const limitString = await redisCrudDao.read(key)
        const now = Date.now()
        const limit: RateLimitOptions = limitString ? JSON.parse(limitString) : {
            total: opts.total,
            remaining: opts.total,
            reset: now + opts.expire * 1000
        }

        if (now > limit.reset) {
            limit.reset = now + opts.expire
            limit.remaining = opts.total
        }

        // do not allow negative remaining
        limit.remaining = Math.max(Number(limit.remaining) - 1, -1)
        redisCrudDao.createWithExpiry(key, JSON.stringify(limit), opts.expire)
            .catch(e => {
                logger.error("could not update ratelimit config" + e)
                rollbarService.sendError(e)
            })

        if (limit.remaining >= 0) {
            return next()
        }

        // rate limited
        const userAgent = req.header("user-agent") as string
        logger.error(`Rate limit exceeded for ip - ${ip}; userAgent: ${userAgent}; reqPath: ${req.originalUrl}`)
        metricsUtil.reportBlockedRequest(BlockingType.RATE_LIMITED)
        res.status(429).send({ "message": "Rate limit exceeded" })

        return next(new Error(`Rate limit exceeded`))
    } catch (err) {
        logger.error("could not perform ratelimit check", err)
        next()
    }

}
