import type { NextFunction, Request, Response } from "express"
import kernel from "../ioc/ioc"
import { PromUtil } from "../../util/PromUtil"
import CUREFIT_API_TYPES from "../ioc/types"

const promUtil = kernel.get<PromUtil>(CUREFIT_API_TYPES.PromUtil)

export const concurrencyLimiter = (options: { max: number }) => {
    if (options.max < 1) {
        throw new TypeError(
            "Maximum concurrent requests must be greater or equal than 1"
        )
    }

    const queue: NextFunction[] = []

    const running = new Set<NextFunction>()

    const maxConcurrentRequests = options.max

    const processQueue = () => {
        if (!queue.length) {
            // queue is empty, we stop processing.
            return
        }
        if (running.size >= maxConcurrentRequests) {
            // running requests have reach maximum, we delay processing.
            promUtil.reportServerLoadQueuedRequests(queue.length)
            return
        }
        const next = queue.shift() as NextFunction
        running.add(next)
        promUtil.reportServerLoadRunningRequests(running.size)
        next()
    }

    return async function concurrencyMiddleware(req: Request, res: Response, next: NextFunction) {
        req.on("close", () => {
            // The request is closed. Either it responded or timed out.
            running.delete(next)
            processQueue()
        })
        queue.push(next)
        processQueue()
    }
}
