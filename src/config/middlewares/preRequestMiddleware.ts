import * as express from "express"
import { BASE_TYPES, CLSUtil } from "@curefit/base"
import { ISessionBusiness as ISessionService } from "@curefit/base-utils"
import { inject } from "inversify"
import CUREFIT_API_TYPES from "../ioc/types"
import kernel from "../ioc/ioc"
import AuthUtil from "../../app/util/AuthUtil"


export const preRequestMiddleware = async function cfApiRequestIdMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    next()
}
