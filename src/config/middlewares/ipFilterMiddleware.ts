import AppUtil from "../../app/util/AppUtil"
import * as express from "express"
import kernel from "../ioc/ioc"
import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import { inject } from "inversify"
import CUREFIT_API_TYPES from "../ioc/types"
import { CacheHelper } from "../../app/util/CacheHelper"
import MetricsUtil from "../../app/metrics/MetricsUtil"
import { ErrorCodes } from "../../app/error/ErrorCodes"
import { BlockingType } from "../../app/metrics/models/BlockingType"
import { ErrorFactory } from "@curefit/error-client"

const net = require("net")
const ipaddr = require("ipaddr.js")
const cacheHelper = kernel.get<CacheHelper>(CUREFIT_API_TYPES.CacheHelper)
const logger = kernel.get<Logger>(BASE_TYPES.ILogger)
const metricsUtil = kernel.get<MetricsUtil>(CUREFIT_API_TYPES.MetricsUtil)
const errorFactory = kernel.get<ErrorFactory>(CUREFIT_API_TYPES.ErrorFactory)

export const ipFilterMiddleware = async function cfApiIpFilterMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const ip: string = req.ips.length > 0 ? req.ips[0] : req.ip

        // NOTE: used in api error count middleware & other places
        res.locals.ip = ip

        return next()
    } catch (err) {
        logger.error("error in ipFilterMiddleware", err)
        return next()
    }

}
