import * as express from "express"

const prodOrigins = [
    "https://www.cure.fit",
    "https://us.cure.fit",
    "http://www.cure.fit",
    "https://cure.fit",
    "http://cure.fit",
    "https://alpha.cure.fit",
    "https://us-alpha.cure.fit",
    "https://local.staging.cure.fit:8080",
    "https://us-local.staging.cure.fit:8080",
    "https://local.tata-neu.cult.fit:8080",
    "https://local.tata-neu.cult.fit",
    "https://local.staging.cult.fit:8080",
    "https://local.staging.cult.fit",
    "https://production-curefit-website.f3pcwfkdwv.ap-south-1.elasticbeanstalk.com",
    "https://dy3gd5w7d.cure.fit",
    "http://dy3gd5w7d.cure.fit",
    "https://8b7oq0j8ji.execute-api.ap-south-1.amazonaws.com",
    "https://webcache.googleusercontent.com",
    "https://beta.cure.fit",
    "https://production.cure.fit",
    "https://hjofzxzex4.execute-api.ap-south-1.amazonaws.com",
    "https://cast-receiver.cure.fit",
    "https://cast-receiver-beta.cure.fit",
    "https://alpha.cult.fit",
    "https://alpha.tata-neu.cult.fit",
    "https://tata-neu.cult.fit",
    "https://www.tata-neu.cult.fit",
    "https://www.cult.fit",
    "https://www.sugarfit.com",
    "https://www.app.sugarfit.com",
    "https://app.sugarfit.com",
    "https://app-stage.sugarfit.com",
    "https://app-alpha.sugarfit.com",
    "https://www.webinar.sugarfit.com",
    "https://live.sugarfit.com",
    "https://webinar.sugarfit.com",
    "https://stage.sugarfit.com",
    "https://alpha.sugarfit.com",
    "https://local.stage.cultsport.com:8080",
    "https://local.stage.cultsport.com",
    "https://cultsport.com",
    "https://alpha.cultsport.com",
    "https://www.cultsport.com",
    "https://local-tata-neu.cultsport.com:8080",
    "https://local-tata-neu.cultsport.com",
    "https://tata-neu.cultsport.com",
    "https://alpha-tata-neu.cultsport.com",
    "https://local-cult-fit.cultsport.com:8080",
    "https://local-cult-fit.cultsport.com",
    "https://cult-fit.cultsport.com",
    "https://alpha-cult-fit.cultsport.com",
    "https://cs-app.cultsport.com",
    "https://alpha-cs-app.cultsport.com",
    "https://local-cs-app.cultsport.com:8080",
    "https://local-cs-app.cultsport.com",
    "https://local.bajajfinserv.curefit.co:8080",
    "https://local.bajajfinserv.curefit.co",
    "https://bajajfinserv.cult.fit",
    "https://www.bajajfinserv.cult.fit",
    "https://alpha.bajajfinserv.cult.fit",
    "https://main.d2dzrnyg66mne6.amplifyapp.com",
    "https://gpt.sugarfit.com",
    "https://www.zencare.fit"
]

const nonProdOrigins = [
    "https://stage1234.cure.fit",
    "https://us-stage1234.cure.fit",
    "https://local.staging.cure.fit",
    "https://us-local.staging.cure.fit",
    "https://local.staging.cure.fit:8080",
    "https://us-local.staging.cure.fit:8080",
    "http://local.staging.cure.fit:8080",
    "http://us-local.staging.cure.fit:8080",
    "http://local.staging.cure.fit",
    "https://hjofzxzex4.execute-api.ap-south-1.amazonaws.com",
    "https://cast-receiver.cure.fit",
    "https://cast-receiver-beta.cure.fit",
    "https://local.staging.cult.fit:8080",
    "https://local.tata-neu.cult.fit:8080",
    "https://local.tata-neu.cult.fit",
    "https://local.staging.cult.fit",
    "https://stage.tata-neu.cult.fit",
    "https://stage.cult.fit",
    "https://www.sugarfit.com",
    "https://www.app.sugarfit.com",
    "https://app.sugarfit.com",
    "https://app-stage.sugarfit.com",
    "https://app-alpha.sugarfit.com",
    "https://www.webinar.sugarfit.com",
    "https://webinar.sugarfit.com",
    "https://stage.sugarfit.com",
    "https://alpha.sugarfit.com",
    "https://local.stage.cultsport.com:8080",
    "https://fitstore.stage.curefit.co",
    "https://stage.cultsport.com",
    "https://local-tata-neu.cultsport.com:8080",
    "https://local-tata-neu.cultsport.com",
    "https://stage-tata-neu.cultsport.com",
    "https://local-cult-fit.cultsport.com:8080",
    "https://local-cult-fit.cultsport.com",
    "https://stage-cult-fit.cultsport.com",
    "https://stage-cs-app.cultsport.com",
    "https://local-cs-app.cultsport.com:8080",
    "https://local-cs-app.cultsport.com",
    "https://visit.stage.cult.fit",
    "https://local.visit.cult.fit:8080",
    "https://local.visit.cult.fit",
    "https://bajajfinserv.stage.curefit.co",
    "https://local.bajajfinserv.curefit.co:8080",
    "https://local.bajajfinserv.curefit.co",
    "https://main.d2dzrnyg66mne6.amplifyapp.com",
    "https://gpt.sugarfit.com",
    "https://www.zencare.fit"
]

export const corsMiddleware = async function cfApiCorsMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    if (process.env.ENVIRONMENT === "PRODUCTION") {
        const origin = req.headers["origin"] as string
        if (prodOrigins.includes(origin)) {
            res.header("Access-Control-Allow-Origin", origin)
        }
    } else {
        const origin = req.headers["origin"] as string
        if (nonProdOrigins.includes(origin)) {
            res.header("Access-Control-Allow-Origin", origin)
        }
    }
    res.header("Access-Control-Allow-Credentials", "true")
    res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE")
    res.header("Access-Control-Allow-Headers", "Content-Type , Origin, deviceId, st, at, appversion, osname, apiKey, userId, latitude, longitude, lat, lon, cityId, content-type, checksum, age, apikey, appversion, content-type, createdat, curefituserid, dateofbirth, description, displayimage, emailid, emergencycontactemailid, emergencycontactname, emergencycontactrelation, emergencyphonenumber, familydoctorid, firstname, formattedage, gender, governmentidnumber, governmentidtype, guardianemailid, guardianname, guardianphonenumber, guardianrelation, id, lastname, middlename, name, osname, phonenumber, preferredtherapycentre, recordedage, recordedbirthdate, relationship, residentialaddress, tncsigned, uhid, updatedat, videocallnumber, ssotoken, clientversion, localeCountryCode, timezone, browsername, x-request-id, appsource, virtual-cluster-name, mixpanel-distinct-id")
    if (req.method === "OPTIONS") {
        res.sendStatus(200)
    } else {
        next()
    }
}
