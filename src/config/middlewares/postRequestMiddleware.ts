import * as express from "express"
import { BASE_TYPES, CLSUtil, Lo<PERSON> } from "@curefit/base"
import kernel from "../ioc/ioc"
import CUREFIT_API_TYPES from "../ioc/types"
import { PromUtil } from "../../util/PromUtil"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import { modifiedRoute } from "../../app/util/RouteUtil"
import { ApiErrorCountMiddleware } from "../../app/auth/ApiErrorCountMiddleware"

const promUtil = kernel.get<PromUtil>(CUREFIT_API_TYPES.PromUtil)
const logger: Logger = kernel.get<Logger>(BASE_TYPES.ILogger)
const rollbarService: RollbarService = kernel.get<RollbarService>(ERROR_COMMON_TYPES.RollbarService)
const errorCountMiddleware: ApiErrorCountMiddleware = kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware)

export const postRequestMiddleware = async function cfApiRequestIdMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const statusCode: number = res.statusCode ?? 500
        promUtil.reportInboundReqStatus(statusCode.toString(), modifiedRoute(req, res) ?? "anon")
        errorCountMiddleware.updateStatusCountMiddleware(statusCode, req, res)
    } catch (e) {
        logger.error("failed to send status code to prom", e)
        rollbarService.sendError(e)
    }
    next()
}
