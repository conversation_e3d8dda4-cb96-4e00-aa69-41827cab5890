import * as express from "express"
import { BASE_TYPES, CLSUtil, Logger } from "@curefit/base"
import kernel from "../ioc/ioc"
import * as util from "util"
import { ERROR_COMMON_TYPES, RollbarService } from "@curefit/error-common"
import * as _ from "lodash"
import CUREFIT_API_TYPES from "../ioc/types"
import { UserContext } from "@curefit/userinfo-common"
import UserErrorBusiness from "../../app/error/UserErrorBusiness"
import { PromUtil } from "../../util/PromUtil"
import { modifiedRoute } from "../../app/util/RouteUtil"
import { ApiErrorCountMiddleware } from "../../app/auth/ApiErrorCountMiddleware"

const logger: Logger = kernel.get<Logger>(BASE_TYPES.ILogger)
const clsUtil: CLSUtil = kernel.get<CLSUtil>(BASE_TYPES.ClsUtil)
const rollbarService: RollbarService = kernel.get<RollbarService>(ERROR_COMMON_TYPES.RollbarService)
const promUtil = kernel.get<PromUtil>(CUREFIT_API_TYPES.PromUtil)
const errorCountMiddleware: ApiErrorCountMiddleware = kernel.get<ApiErrorCountMiddleware>(CUREFIT_API_TYPES.ApiErrorCountMiddleware)

const getStatusCodeFromError = (error: any): number => {
    const statusCode = error.status || error.statusCode || error.status_code || (error.output && error.output.statusCode)
    return statusCode ? parseInt(statusCode, 10) : 500
}

export const errorMiddleware = async function cfApiErrorMiddleware(err: any, req: express.Request, res: express.Response, next: express.NextFunction) {
    const error: any = err as any
    const isServerError = !error.statusCode ? true : error.statusCode >= 500

    logger.error(`errorMiddleware`, {
        reqId: req.id,
        reqUrl: req.originalUrl,
        responseCode: error.statusCode,
        errName: error.name,
        errTitle: error.title,
        errMessage: error.message,
        errTrace: error.stack,
        isServerError: isServerError,
        errDebugMessage: error.debugMessage,
        reqHeaders: req.headers,
        reqBody: util.inspect(req.body, false, 12, false),
        reqIp: req.ip,
        deviceId: req.headers?.deviceid ?? req.cookies?.deviceId ?? req.signedCookies?.deviceId,
    })

    if (error.statusCode) {
        // circuit open for some API or downstream service not available
        if (error.statusCode === 598 || error.statusCode === 501  || error.statusCode === 502 || error.statusCode === 503 || error.statusCode === 504) {
            err.title = "Something went wrong"
            err.subTitle = "Please try again in sometime"
        }
    } else {
        logger.error(util.inspect(error, false, 12, false))
    }

    if (!res.headersSent) {
        const userErrorBusiness = kernel.get<UserErrorBusiness>(CUREFIT_API_TYPES.UserErrorBusiness)
        const userContext: UserContext = req.userContext as UserContext
        const templatedError = await userErrorBusiness.getTemplatedError(err, userContext)
        if (!_.isEmpty(templatedError)) {
            err.title = templatedError.title
            err.subTitle = templatedError.message
            err.actions = templatedError.actions
        }

        // remove for security purposes
        if (err?.meta?.stack) {
            delete err.meta.stack
        }

        if (!err.message || err.message.length > 150 || err.name === "TypeError" ||
            err.name === "FetchError" || err.message.includes("FetchError") || err.message.includes("Exception") ||
            err.message.includes("TypeError") || err.message.includes("Timeout") || err.message.includes("version is modified")
            || err.message.includes("suspended") || err.message.includes("mongo") || err.message.includes("mysql") || err.message.includes("redis")
        ) {
            err.clientMessage = "Something went wrong"
        } else {
            err.clientMessage = err.message
        }
        res.status(error.statusCode || 500).send({
            title: !_.isEmpty(err.title) ? err.title : "Something went wrong",
            subTitle: !_.isEmpty(err.subTitle) ? err.subTitle : err.clientMessage,
            actions: !_.isEmpty(err.actions) ? err.actions : [{actionType: "HIDE_ALERT_MODAL", title: "Ok"}],
            meta: err.meta,
            type: err.name
        })
    }

    const userId = clsUtil.getUserId()
    const requestId = clsUtil.getRequestId()

    const statusCode = getStatusCodeFromError(error)

    errorCountMiddleware.updateStatusCountMiddleware(statusCode, req, res)

    promUtil.reportInboundReqStatus(statusCode?.toString() ?? "500", modifiedRoute(req, res) ?? "anon")

    if (statusCode < 500) {
        return next(error)
    }

    rollbarService.sendError(error, {
        user: {
            id: userId,
            ip_address: res.locals.ip,
        },
        tags: {
            "user_id": userId,
            "ip_address": res.locals.ip,
            "request_id": requestId,
        }
    })

    next(error)
}
