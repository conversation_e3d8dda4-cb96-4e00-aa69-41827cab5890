import * as express from "express"
import { CLSUtil } from "@curefit/base"

const { randomUUID } = require("crypto")
const requestIdHeader = CLSUtil.REQUEST_ID_FIELD.toLowerCase()

export const requestIdMiddleware = async function cfApiRequestIdMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    req.id = req.header(requestIdHeader) ?? randomUUID()
    res.setHeader(requestIdHeader, req.id)
    next()
}
