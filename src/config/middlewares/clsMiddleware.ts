import { AuthUtil, CLS_UTIL_REQ_HEADERS } from "../../app/util/AuthUtil"
import * as express from "express"
import kernel from "../../config/ioc/ioc"
import { BASE_TYPES, CLSUtil } from "@curefit/base"
import { AppTenant } from "@curefit/base-common"
import AppUtil from "../../app/util/AppUtil"

const clsUtil = kernel.get<CLSUtil>(BASE_TYPES.ClsUtil)

export const clsMiddleware = async function cfapiClsMiddleware(req: express.Request, res: express.Response, next: express.NextFunction) {
    try {
        const ns = clsUtil.getAsyncLocalStorage()
        ns.run({
            [CLSUtil.REQUEST_ID_FIELD]: res.get(CLSUtil.REQUEST_ID_FIELD),
            [CLSUtil.SERVICE_USER_TYPE]: "CFAPI",
            [CLS_UTIL_REQ_HEADERS]: AuthUtil.getClsReqStringifedHeaders(req),
            [CLSUtil.TENANT]: AppUtil.getTenantFromReq(req) ?? req.get(CLSUtil.TENANT) ?? AppTenant.CUREFIT,
            [CLSUtil.DEVICE_ID]:  req.get("deviceid") ?? undefined,
            [CLSUtil.VIRTUAL_CLUSTER_NAME]: req.get(CLSUtil.VIRTUAL_CLUSTER_NAME) ?? undefined,
            "serverRequest": req, // used by RollbarService
            "serverResponse": res // used by RollbarService
        }, () => {
            next()
        })
    } catch (ex) {
        next()
    }
}
