istio:
  allOutboundInterception: false
  external:
    hosts:
      - us-alpha.cure.fit
      - alpha.cult.fit
      - alpha.tata-neu.cult.fit
      - alpha.bajajfinserv.cult.fit
    matchAdditional:
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /

  alb:
    hosts:
      - us-alpha.cure.fit
      - alpha.cult.fit
      - alpha.tata-neu.cult.fit
      - alpha.bajajfinserv.cult.fit
    matchAdditional:
      - match:
          - exact: /partner
        redirect:
          domain: partner.cult.fit
          uri: /
          https_redirect: "false"
      - match:
          - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
          - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
          - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
          - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
          - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
          - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
          - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
          - exact: /api/page/widget
        rewrite: /page/widget
        route: curefit-api
      - match:
          - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
          - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
          - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
          - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
          - prefix: /api/
        rewrite: /

  internal:
    hosts:
      - cfapi.alpha.cure.fit.internal
    matchAdditional:
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        - prefix: /nodeimpl/
        rewrite: /
        route: curefit-api

  custom:
    type: alb
    hosts:
      - api.alpha.cure.fit
      - api.alpha.curefit.co

metrics:
  enabled: true
  interval: 45s
  path: /cluster_metrics
  port: 9102

service:
  expose:
    - 3000
deployment:
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
  dnsPolicy: "None"
  voyager:
    rightSize: "true"
  labels:
    billing: cfapi
    sub-billing: curefit-api
  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/CF-API-Prod
  probePath: /status
  probePort: 3000
  resources:
    limits:
      cpu: 1000m
      memory: 4Gi
    requests:
      cpu: 500m
      memory: 2Gi
  env:
    - name: ENVIRONMENT
      value: "PRODUCTION"
    - name: K8S_ENV
      value: "PRODUCTION"
    - name: APP_ENV
      value: "ALPHA"
    - name: APP_NAME
      value: "curefit-api"
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: 3000
    - name: MASTER_PORT
      value: 13000
    - name: TZ
      value: "Asia/Kolkata"
    - name: UV_THREADPOOL_SIZE
      value: 120

externalSecrets:
  enabled: "true"
  names:
    - prod/redis/curefit-cf-api-cache
    - prod/redis/cult-prod-cache
    - prod/redis/curefit-prod-default-cache
    - prod/redis/production-session-transient-cache
    - prod/redis/prod-eatfit-redis
    - prod/redis/riddler-service-cache
    - prod/redis/catalog-service-cache
    - prod/redis/platforms-segmentation-cache

scaling:
  scaleUpAtCPU: 0.55
  minReplicas: 1
  maxReplicas: 5
  keda:
    triggers:
      - type: cpu
        metadata:
          value: "550m"
        metricType: AverageValue # Allowed types are 'Utilization' or 'AverageValue'
      - type: cron # scale up from 8:40am-8:20pm
        metadata:
          timezone: Asia/Kolkata
          start: 40 8 * * *
          end: 20 20 * * *
          desiredReplicas: "2"
