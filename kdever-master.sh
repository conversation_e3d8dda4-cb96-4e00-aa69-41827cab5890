set -x
set -e

BINDIR=~/bin
export VIRTUAL_CLUSTER=$1
export APP_NAME=$(awk 'f {print;f=0} /name: APP_NAME/ {f=1}' values-prod.yaml | cut -d : -f2 | tr -d '"' | tr -d ' ')
BINDIR=~/bin
DEVSBIN=devspace6
BASEDEVSCONF=devspace-base.yaml
VALUESDEVSCONF=values-devspace.yaml
DEVSCONF=devspace-master.yaml

npm list -g @alexlafroscia/yaml-merge || npm install @alexlafroscia/yaml-merge -g

# check if devspace is installed
if [ -f ${BINDIR}/${DEVSBIN} ]; then
    echo "${BINDIR}/${DEVSBIN} exists."
else
    mkdir -p ${BINDIR}
    # check if intel or apple silicon
    if [[ $(uname -m) == "x86_64" ]]; then
        curl -L https://github.com/devspace-sh/devspace/releases/download/v6.3.2/devspace-darwin-amd64 -o ${BINDIR}/${DEVSBIN}
    else
        curl -L https://github.com/devspace-sh/devspace/releases/download/v6.3.2/devspace-darwin-arm64 -o ${BINDIR}/${DEVSBIN}
    fi
    chmod +x ${BINDIR}/${DEVSBIN}
fi
export PATH=${BINDIR}:$PATH


DEV_KUBECONFIG=~/.kube/stage-config
if [ -f "$DEV_KUBECONFIG" ]; then
    echo "$DEV_KUBECONFIG exists."
else
    echo "$DEV_KUBECONFIG does not exist... Please add kube-config for stage at ~/.kube/stage-config from https://github.com/curefit/port-forward/blob/master/kube-config.conf"
    exit 1
fi
export KUBECONFIG=${DEV_KUBECONFIG}


export user=$(logname)
curl --location --request POST 'https://voyager-vpn.curefit.co/kdever/sync/count' --header 'Content-Type: application/json' --data-raw "{ \"user\" : \"${user}\", \"virtualClusterName\" : \"${VIRTUAL_CLUSTER}\", \"appName\" : \"${APP_NAME}\", \"version\": \"v2\" }"

${DEVSBIN} use context curefit-stage-primary
${DEVSBIN} use namespace ${APP_NAME}

yaml-merge ${BASEDEVSCONF} ${VALUESDEVSCONF}  > ${DEVSCONF}
DEVSPACE_CONFIG=${DEVSCONF} ${DEVSBIN} print
DEVSPACE_CONFIG=${DEVSCONF} ${DEVSBIN} run-pipeline dev --override-name=${APP_NAME}-${VIRTUAL_CLUSTER}
#DEVSPACE_CONFIG=${DEVSCONF} ${DEVSBIN} run-pipeline dev --override-name=${APP_NAME}-${VIRTUAL_CLUSTER} --debug

set +e
set +x
