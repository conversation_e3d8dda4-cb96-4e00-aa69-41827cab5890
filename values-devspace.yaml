dev:
  app:
    # Search for the container that runs this image
    workingDir: /curefit-api
    args:
      - "node --inspect --max-old-space-size=4096 --max_semi_space_size=512 dist/index.js"
    ports:
      # Map localhost port 8080 to container port 80
      # - port: "8080:80"
      # app port
      - port: "3000:3000"
      # debug port
      - port: "9229:9229"

    # Sync files between the local filesystem and the development container
    sync:
      - path: ./dist:dist
        printLogs: true
        disableDownload: true
        initialSync: preferLocal
        onUpload:
          restartContainer: true