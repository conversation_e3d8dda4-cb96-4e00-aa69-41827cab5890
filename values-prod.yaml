podDisruptionBudget:
  enabled: true
  minAvailable: 10%

istio:
  allOutboundInterception: true
  outlierDetection:
    enabled: false
  external:
    hosts:
      - us.cure.fit
      - www.cult.fit
      - tata-neu.cult.fit
      - bajajfinserv.cult.fit
    matchAdditional:
      - match:
        - exact: /partner
        redirect:
          domain: partner.cult.fit
          uri: /
          https_redirect: "false"
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - exact: /api/page/widget
        rewrite: /page/widget
        route: curefit-api
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /

  alb:
    hosts:
      - us.cure.fit
      - www.cult.fit
      - tata-neu.cult.fit
      - bajajfinserv.cult.fit
    matchAdditional:
      - match:
        - exact: /partner
        redirect:
          domain: partner.cult.fit
          uri: /
          https_redirect: "false"
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - exact: /api/page/widget
        rewrite: /page/widget
        route: curefit-api
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /

  custom:
    type: alb
    hosts:
      - api.cure.fit
      - api.curefit.co

  internal:
    hosts:
      - cfapi.production.cure.fit.internal
    matchAdditional:
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        - prefix: /nodeimpl/
        rewrite: /

metrics:
  enabled: true
  interval: 90s
  path: /cluster_metrics
  port: 9102

pager:
  service-name: curefit-api
  provider: opsgenie # opsgenie/pagerduty

bugtracker:
  provider: sentry # rollbar/sentry
  service-name: curefit-api-node

support:
  slack-channel: cfapi
  mailing-list: <EMAIL> # refer https://groups.google.com/all-groups

apm:
  provider: datadog # datadog/newrelic
  service-name: curefit-api

pod-id: platform # refer https://github.com/curefit/curefit-billing/blob/master/pod.yaml

repository:
  url: https://github.com/curefit/curefit-api

tags:
  billing: platform # refer https://github.com/curefit/curefit-billing/blob/master/billing.yaml

service:
  expose:
    - 3000
deployment:
  tolerations:
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
  dnsPolicy: "None"
  voyager:
    rightSize: "true"
  labels:
    billing: cfapi
    sub-billing: curefit-api
  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/CF-API-Prod
  probePath: /status
  probePort: 3000
  readinessProbe:
    initialDelaySeconds: 45
    periodSeconds: 5
    timeoutSeconds: 2
    failureThreshold: 2
  livenessProbe:
    probePath: /status/live
    initialDelaySeconds: 60
    periodSeconds: 60
    timeoutSeconds: 10
    failureThreshold: 5
    terminationGracePeriodSeconds: 30
  resources:
    limits:
      cpu: 2000m
      memory: 5Gi
    requests:
      cpu: 400m
      memory: 2Gi
  env:
    - name: ENVIRONMENT
      value: "PRODUCTION"
    - name: APP_ENV
      value: "PRODUCTION"
    - name: K8S_ENV
      value: "PRODUCTION"
    - name: APP_NAME
      value: "curefit-api"
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: 3000
    - name: MASTER_PORT
      value: 13000
    - name: TZ
      value: "Asia/Kolkata"
    - name: UV_THREADPOOL_SIZE
      value: 64

externalSecrets:
  enabled: "true"
  names:
    - prod/redis/curefit-cf-api-cache
    - prod/redis/cult-prod-cache
    - prod/redis/curefit-prod-default-cache
    - prod/redis/production-session-transient-cache
    - prod/redis/prod-eatfit-redis
    - prod/redis/riddler-service-cache
    - prod/redis/catalog-service-cache
    - prod/redis/platforms-segmentation-cache

scaling:
  scaleUpAtCPU: 0.55
  targetCPUUtilPercentage: 100
  minReplicas: 3
  maxReplicas: 50
  behavior:
    scaleDown:
      selectPolicy: Min
      policies:
        - type: Percent
          value: 10
          periodSeconds: 30
        - type: Pods
          value: 5
          periodSeconds: 30
  keda:
    triggers:
      - type: cpu
        metadata:
          value: "550m"
        metricType: AverageValue # Allowed types are 'Utilization' or 'AverageValue'
      - type: cron # scale up from 9:50pm-10:10pm
        metadata:
          timezone: Asia/Kolkata
          start: 50 21 * * *
          end: 10 22 * * *
          desiredReplicas: "45"
      - type: cron # scale up from 5:40am-9:20am
        metadata:
          timezone: Asia/Kolkata
          start: 40 5 * * *
          end: 20 9 * * *
          desiredReplicas: "10"
      - type: cron # scale up from 5:40pm-8:20pm
        metadata:
          timezone: Asia/Kolkata
          start: 40 17 * * *
          end: 20 20 * * *
          desiredReplicas: "10"
      - type: cron # scale up from 4:40am-11:40pm
        metadata:
          timezone: Asia/Kolkata
          start: 40 4 * * *
          end: 40 23 * * *
          desiredReplicas: "5"

