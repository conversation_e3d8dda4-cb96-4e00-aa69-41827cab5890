redirect: "true"
istio:
  external:
    hosts:
      - alpha.cure.fit
    matchAdditional:
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /
        route: curefit-api
      - match:
        - prefix: /
        redirect:
          domain: alpha.cult.fit
          https_redirect: "false"
