name: Alpha Build and Deploy

on:
  push:
    branches:
      - "alpha"
    paths-ignore:
      - values-stage.yaml
      - values-alpha.yaml
      - values-prod.yaml

jobs:
  call-template:
    uses: curefit/actions-template/.github/workflows/build-and-deploy.yml@master
    secrets: inherit
    with:
      branch: "alpha"
      build_registry: "035243212545.dkr.ecr.ap-south-1.amazonaws.com/curefit/alpha/curefit-api"
      app_name: "curefit-api"
      environment: "alpha"
      org: "curefit"
      spinnaker_webhook: "curefit-api-alpha"
