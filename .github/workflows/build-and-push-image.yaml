name: Build and Push Multi-Arch Docker Images to ECR

on:
  push:
    branches:
      - "github-action-test"

env:
  REGISTRY_IMAGE: 035243212545.dkr.ecr.ap-south-1.amazonaws.com/test-repository
  APP_NAME: curefit-api
  ORG: curefit
  ENVIRONMENT: stage
  AWS_REGION : "ap-south-1"

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

jobs:
  build:
    runs-on: ubuntu-latest-m
    strategy:
      fail-fast: false

    steps:
      -
        name: Checkout
        uses: actions/checkout@v4
      -
        name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY_IMAGE }}
          # generate Docker tags based on the following events/attributes
          tags: |
            type=schedule
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=sha
      -
        name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      -
        name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      -
        name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::035243212545:role/GitHubActionsECRRole
          role-session-name: githubactionsrolesession
          aws-region: ${{ env.AWS_REGION }}
      -
        name: Login to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      -
        name: Build and push by digest
        id: build
        uses: docker/build-push-action@v5
        with:
          build-args: |
            NPM_TOKEN=${{ secrets.npm_token }}
            GITHUB_NPM_TOKEN=${{ secrets.github_npm_token }}
            APP_NAME=${{ env.APP_NAME }}        
            ENVIRONMENT=${{ env.ENVIRONMENT }}
          context: .
          platforms: linux/amd64,linux/arm64
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.meta.outputs.labels }}
          tags: ${{ steps.meta.outputs.tags }}
          provenance: false