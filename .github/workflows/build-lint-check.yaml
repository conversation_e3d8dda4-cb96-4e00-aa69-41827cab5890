name: Validate

on:
  push:
    branches:
      - master
      - alpha
      - stage
  pull_request:
    branches:
      - master
      - alpha

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ github.token }}
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
          registry-url: https://npm.pkg.github.com
          scope: '@curefit'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - run: yarn install --silent --production=false
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN_GITHUB }}
      - run: yarn run build
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN_GITHUB }}
      - run: yarn test --json --outputFile="jest.results.json"
      - name: Add Coverage Comment
        uses: mattallty/jest-github-action@v1.0.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          changes-only: true
          test-command: "yarn test"
