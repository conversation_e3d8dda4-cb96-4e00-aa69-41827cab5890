# name: Build Image and Trigger Webhook

# on:
#   push:
#     branches:
#       - "master"
#     paths-ignore:    
#       - values-stage.yaml
#       - values-alpha.yaml
#       - values-prod.yaml  

# env:
#   BUILD_REGISTRY: "035243212545.dkr.ecr.ap-south-1.amazonaws.com/curefit/prod/curefit-api"

# jobs:
#   prerequisites:
#     runs-on: [ubuntu-self-hosted]
#     steps:
#       - uses: actions/checkout@v3
#         with:
#           fetch-depth: 0
          
#       - name: Create Sentry Pre Release
#         uses: getsentry/action-release@v1
#         env:
#           SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
#           SENTRY_ORG: "curefit"
#           SENTRY_PROJECT: "curefit-api-node"
#         with:
#           environment: master
    

#   arm-build:
#     runs-on: [arc-runner-kubernetes-ci-arm]
#     needs: prerequisites
#     container:
#       image: 035243212545.dkr.ecr.ap-south-1.amazonaws.com/kaniko-github-actions:0.0.8-arm
#     permissions:
#       contents: read
#       packages: write

#     steps:
#       - name: Set up Environment Variables for Image Tag
#         id: vars
#         run: |
#           echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
#           echo "BRANCH_NAME=${{ github.ref_name }}" >> $GITHUB_ENV
#           PREVIEW_VERSION="${{ github.run_number }}-${{ github.ref_name }}"
#           echo "PREVIEW_VERSION=${PREVIEW_VERSION}" >> $GITHUB_ENV
#           VERSION="${{ github.run_number }}-${{ github.ref_name }}"
#           echo "VERSION=${VERSION}" >> $GITHUB_ENV

#       - name: Build and push image to ECR
#         env:
#           ORG: 'curefit'
#           APP_NAME: 'curefit-api'
#           GITHUB_MVN_TOKEN: ${{ secrets.HUB_NPM_TOKEN }}
#           NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
#           APP_ENV: 'stage'
#         run: |
#           # Write config file, change to your destination registry
#           AUTH=$(echo -n "${{ github.actor }}:${{ secrets.GITHUB_TOKEN }}" | base64 | tr -d '\n')
#           echo "{\"auths\":{\"ghcr.io\":{\"auth\":\"${AUTH}\"}}}" > /kaniko/.docker/config.json

#           # Configure git
#           export GIT_USERNAME="actions-pipeline"
#           export GIT_PASSWORD="${{ secrets.GIT_PASSWORD }}"

#           /kaniko/executor --dockerfile="Dockerfile" \
#             --context="${{ github.repositoryUrl }}#${{ github.ref }}#${{ github.sha }}" \
#             --build-arg NPM_TOKEN="${{ secrets.NPM_TOKEN }}" \
#             --build-arg GITHUB_NPM_TOKEN="${{ secrets.HUB_NPM_TOKEN }}" \
#             --build-arg APP_NAME="curefit-api" \
#             --build-arg ENVIRONMENT="stage" \
#             --no-push
#             # --destination="${{ env.BUILD_REGISTRY }}:${{ env.PREVIEW_VERSION }}"

            
#       # - name: Trigger Spinnaker
#       #   env:
#       #     pipeline_name: "curefit-api-alpha"
#       #   run: |
#       #     /busybox/curl "https://spinnaker-gate.curefit.co/webhooks/webhook/${{ env.pipeline_name }}" \
#       #     -X POST \
#       #     -H "Content-Type: application/json" \
#       #     -d '{"parameters": {"tag": "${{ env.VERSION }}", "registry": "${{ env.BUILD_REGISTRY }}" }}'
