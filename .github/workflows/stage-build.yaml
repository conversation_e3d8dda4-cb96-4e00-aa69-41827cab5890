name: Stage Build and Deploy

on:
  push:
    branches:
      - "stage"
    paths-ignore:
      - values-stage.yaml
      - values-alpha.yaml
      - values-prod.yaml

jobs:
  call-template:
    uses: curefit/actions-template/.github/workflows/build-and-deploy.yml@master
    secrets: inherit
    with:
      branch: "stage"
      build_registry: "035243212545.dkr.ecr.ap-south-1.amazonaws.com/curefit/stage/curefit-api"
      app_name: "curefit-api"
      environment: "stage"
      org: "curefit"
      spinnaker_webhook: "curefit-api-stage"
