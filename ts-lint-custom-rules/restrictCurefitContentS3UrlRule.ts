import * as <PERSON><PERSON> from "tslint"
import * as ts from "typescript"
import * as tsutils from "tsutils"

export class Rule extends Lint.Rules.AbstractRule {

    public static FAILURE_STRING = "Use CDN url instead of s3 url"

    public apply(sourceFile: ts.SourceFile): Lint.RuleFailure[] {
        return this.applyWithFunction(sourceFile, walk)
    }
}

function walk(ctx: Lint.WalkContext<void>) {
    function cb(node: ts.Node): void {
        if (tsutils.isStringLiteral(node)) {
            const text = node.getText()
            if (text.includes("https://curefit-content.s3.ap-south-1.amazonaws.com") || text.includes("https://s3.ap-south-1.amazonaws.com/curefit-content")) {
                ctx.addFailureAt(node.getStart(), node.getWidth(), Rule.FAILURE_STRING)
            }
        }

        return ts.forEachChild(node, cb)
    }

    return ts.forEachChild(ctx.sourceFile, cb)
}
