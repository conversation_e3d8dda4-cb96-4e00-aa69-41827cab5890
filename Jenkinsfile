pipeline {
  agent {
    label "k8s-kaniko-amd64"
  } // agent
  environment {
    ORG = 'curefit'
    APP_NAME = 'curefit-api'
    NPM_TOKEN = credentials('npm-token')
    GITHUB_NPM_TOKEN = credentials('github-npm-token')
  } // environment
  stages {
    stage('Prepare Docker Image for Production Environment') {
      when{ branch 'master' }
      environment {
        VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_','-')
      } // environment
      parallel {
        stage('Prepare arm64 Docker Image for Production Environment') {
          agent {
            label 'k8s-kaniko-arm64'
          } // agent
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              prepareRelease("${APP_NAME}", "PRODUCTION")
              buildAndPushDockerImage("prod", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/prod/${APP_NAME}", "${VERSION}", "prod")
            } // container
          } // steps
        } // stage('Prepare arm64 Docker Image for Production Environment')
        stage('Prepare amd64 Docker Image for Production Environment') {
          environment {
            PREVIEW_VERSION = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
          } // environment
          steps {
            container(name: 'kaniko', shell: '/busybox/sh') {
              prepareRelease("${APP_NAME}", "PRODUCTION")
              buildAndPushDockerImage("prod", "${PREVIEW_VERSION}")
              updateArtifact("${DOCKER_REGISTRY}/${ORG}/prod/${APP_NAME}", "${VERSION}", "prod")
            } // container
          } // steps
        } // stage('Prepare amd64 Docker Image for Production Environment')
      } // parallel
    }; // stage('Prepare Docker Image for Production Environment')

    stage('Build Docker Manifest and push') {
      when { anyOf { branch 'master'; branch 'stage'; branch 'alpha'; } }
      agent {
        label 'teleport-db-agent'
      } // agent
      environment {
        VERSION = "$BUILD_NUMBER-$BRANCH_NAME".replaceAll('_', '-')
        VERSION_ARM = "$BUILD_NUMBER-$BRANCH_NAME-arm64".replaceAll('_', '-')
        VERSION_AMD = "$BUILD_NUMBER-$BRANCH_NAME-amd64".replaceAll('_', '-')
      } // environment
      steps {
        script {
          env = "$BRANCH_NAME" == 'master' ? 'prod' : ("$BRANCH_NAME" == 'alpha' ? 'alpha' : 'stage')
          def URL = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION}"
          def URL_ARM = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION_ARM}"
          def URL_AMD = "${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${VERSION_AMD}"
          dockerManifest(URL, URL_ARM, URL_AMD)
        } // script
      } // steps
    }; // stage('Build Docker Manifest and push')
    stage ('Preparing Docker Image for Dev testing Environment') {
      when {
        not {
          anyOf {
            expression { params.branchName == null }
            expression { params.branchName == "stage" }
            expression { params.branchName == "alpha" }
            expression { params.branchName == "master" }
          }
        }
      } // when
      environment {
        VC_DOCKER_IMAGE_PATH = "${DOCKER_REGISTRY}/${ORG}/vc-artifacts"
        PREVIEW_VERSION = "${APP_NAME}-$BUILD_NUMBER-$virtualClusterName".replaceAll('_','-')
        VOYAGER_URL = 'http://voyager.production.cure.fit.internal/echidna/deployment'
      } // environment
      steps {
        container(name: 'kaniko', shell: '/busybox/sh') {
          sh "echo building ${virtualClusterName} ${VC_DOCKER_IMAGE_PATH}:${PREVIEW_VERSION}"
          prepareRelease("${APP_NAME}", "STAGE")
          buildDockerfileDevspace("stage", "${PREVIEW_VERSION}")
          updateArtifact("${VC_DOCKER_IMAGE_PATH}", "${PREVIEW_VERSION}", "stage")
          sh "curl -sf -X POST \"${VOYAGER_URL}/${params.deploymentId}/trigger\" -H 'Content-Type: application/json;charset=UTF-8' --data-raw '{\"appName\": \"${APP_NAME}\", \"repoName\": \"${params.repoName}\", \"virtualClusterName\": \"${params.virtualClusterName}\", \"imageUrl\": \"${VC_DOCKER_IMAGE_PATH}\", \"imageTag\": \"${PREVIEW_VERSION}\"}'"
        } // container
      } // steps
    }; // stage ('Preparing Docker Image for Dev testing Environment')
  } //
  post {
    success {
      cleanWs()
    }
  }
}

void buildAndPushDockerImage(env, tag) {
  sh """
  #!/busybox/sh -xe
  /kaniko/executor \
  --dockerfile Dockerfile \
  --context `pwd`/ \
  --build-arg NPM_TOKEN=${NPM_TOKEN} \
  --build-arg GITHUB_NPM_TOKEN=${GITHUB_NPM_TOKEN} \
  --build-arg ENVIRONMENT=${env} \
  --build-arg APP_NAME=${APP_NAME} \
  --destination ${DOCKER_REGISTRY}/${ORG}/${env}/${APP_NAME}:${tag}
  """
}

void prepareRelease(appName, appEnv){
  sh "sh ci/scripts/pre-release.sh ${appEnv}"
}

void updateArtifact(repo, tag, env) {
  sh """
  touch build.properties
  echo repo=${repo} >> build.properties
  echo tag=${tag} >> build.properties
  echo env=${env} >> build.properties
  """
  archiveArtifacts 'build.properties'
}

void buildDockerfileDevspace(env, tag){
  sh "VIRTUAL_CLUSTER=${virtualClusterName} PREVIEW_VERSION=${tag} VC_DOCKER_IMAGE_PATH=${VC_DOCKER_IMAGE_PATH} ORG=${ORG} APP_NAME=${APP_NAME} GITHUB_NPM_TOKEN=${GITHUB_NPM_TOKEN} NPM_TOKEN=${NPM_TOKEN} ENVIRONMENT=${env} devspace build"
}

void dockerManifest(tag, tag_arm64, tag_amd64) {
  sh "aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${DOCKER_REGISTRY}"
  sh "docker manifest create ${tag} ${tag_arm64} ${tag_amd64}"
  sh "docker manifest annotate --arch arm64 ${tag} ${tag_arm64}"
  sh "docker manifest annotate --arch amd64 ${tag} ${tag_amd64}"
  sh "docker manifest push ${tag}"
}
