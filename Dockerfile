FROM public.ecr.aws/docker/library/node:20.9.0-bookworm-slim as intermediate

RUN apt update && apt install python3 make g++ util-linux git -y

ARG NPM_TOKEN
ARG GITHUB_NPM_TOKEN
ARG APP_NAME
ARG ENVIRONMENT

WORKDIR /${APP_NAME}

RUN echo ${NPM_TOKEN} > /root/.npmrc
RUN echo ${GITHUB_NPM_TOKEN} >> /root/.npmrc
RUN echo '@curefit:registry=https://npm.pkg.github.com/' >> /root/.npmrc
RUN echo 'registry=https://registry.npmjs.org/' >> /root/.npmrc
RUN echo 'unsafe-perm=true' >> /root/.npmrc

ADD . /${APP_NAME}

RUN bash ci/scripts/build.sh


FROM public.ecr.aws/docker/library/node:20.9.0-bookworm-slim

RUN apt update && apt install supervisor tzdata util-linux git -y

ARG APP_NAME

RUN mkdir -p /logs/${APP_NAME} /config/${APP_NAME} /tmp

WORKDIR /${APP_NAME}

COPY --from=intermediate /${APP_NAME}/  /${APP_NAME}/

COPY --from=intermediate /${APP_NAME}/ci/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

CMD ["/usr/bin/supervisord", "-n",  "-c",  "/etc/supervisor/conf.d/supervisord.conf"]
