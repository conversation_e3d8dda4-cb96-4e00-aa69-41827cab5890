{"compileOnSave": true, "compilerOptions": {"outDir": "dist", "target": "ES2021", "lib": ["ES2021"], "types": ["reflect-metadata", "jest"], "module": "commonjs", "moduleResolution": "node", "sourceMap": true, "removeComments": true, "noImplicitAny": true, "preserveConstEnums": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true}, "include": ["./src/**/*.ts"], "exclude": ["node_modules", "dist", "typings"]}