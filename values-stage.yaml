istio:
  external:
    hosts:
      - us-stage1234.cure.fit
      - stage.cult.fit
      - stage.tata-neu.cult.fit
      - stage.visit.cult.fit
      - bajajfinserv.stage.curefit.co
    matchAdditional:
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /

  alb:
    hosts:
      - us-stage1234.cure.fit
      - stage.cult.fit
      - stage.tata-neu.cult.fit
      - stage.visit.cult.fit
      - bajajfinserv.stage.curefit.co
    matchAdditional:
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /

  custom:
    type: alb
    hosts:
      - api.stage.cure.fit
      - api.stage.curefit.co

  internal:
    hosts:
      - cfapi.stage.cure.fit.internal
    matchAdditional:
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2
      - match:
        - prefix: /api/
        - prefix: /nodeimpl/
        rewrite: /
        route: curefit-api

metrics:
  enabled: true
  interval: 45s
  path: /cluster_metrics
  port: 9102

service:
  expose:
    - 3000
deployment:
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
        - key: karpenter.sh/nodepool
          operator: In
          values:
            - default-arm64-al2023
  dnsPolicy: "None"
  labels:
    billing: cfapi
    sub-billing: curefit-api
  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/k8s-cf-api-stage
  probePath: /status
  probePort: 3000
  resources:
    limits:
      cpu: 1000m
      memory: 4Gi
    requests:
      cpu: 100m
      memory: 1Gi
  env:
    - name: ENVIRONMENT
      value: "STAGE"
    - name: APP_ENV
      value: "STAGE"
    - name: APP_NAME
      value: "curefit-api"
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: 3000
    - name: MASTER_PORT
      value: 13000
    - name: TZ
      value: "Asia/Kolkata"
    - name: UV_THREADPOOL_SIZE
      value: 120

externalSecrets:
  enabled: "true"
  names:
    - stage/redis/curefit
    - stage/redis/cf-api-cache
    - stage/redis/curefit-stage-platforms-segmentation
    - stage/redis/cult-stage-cache

scaling:
  minReplicas: 1
  maxReplicas: 3
  keda:
    triggers:
      - type: cpu
        metadata:
          value: "500m"
        metricType: AverageValue # Allowed types are 'Utilization' or 'AverageValue'
      - type: cron # scale up from 9:40am-6:20pm
        metadata:
          timezone: Asia/Kolkata
          start: 40 9 * * *
          end: 20 18 * * *
          desiredReplicas: "2"

