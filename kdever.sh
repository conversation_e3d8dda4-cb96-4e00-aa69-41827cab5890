set -x
set -e

VIRTUAL_CLUSTER=$1

FILE=~/.kube/stage-config
if [ -f "$FILE" ]; then
    echo "$FILE exists."
else
    echo "$FILE does not exist... Please add kube-config for stage at ~/.kube/stage-config from https://github.com/curefit/port-forward/blob/master/kube-config.conf"
    exit 1
fi
export KUBECONFIG=~/.kube/stage-config


export VIRTUAL_CLUSTER=${VIRTUAL_CLUSTER}
export APP_NAME=curefit-api

export DOCKER_REGISTRY="DOCKER_REGISTRY"
export PREVIEW_VERSION="PREVIEW_VERSION"
export ENVIRONMENT="stage"
export NPM_TOKEN="NPM_TOKEN"
export GITHUB_NPM_TOKEN="GITHUB_NPM_TOKEN"
export ORG="curefit"

export user=$(logname)
curl --location --request POST 'https://voyager-vpn.curefit.co/kdever/sync/count' --header 'Content-Type: application/json' --data-raw "{ \"user\" : \"${user}\", \"virtualClusterName\" : \"${VIRTUAL_CLUSTER}\" }"

devspace use context curefit-stage-primary
devspace use namespace curefit-api

devspace dev --skip-pipeline

set +e
set +x
