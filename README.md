# curefit-api
Gateway server to all upstream CureFit services.

## Setup

- Install [Homebrew](https://brew.sh)
- `brew install node@16`
- `brew link --force --overwrite node@16`
- `brew install yarn`
- Follow the steps on [curefit/port-forward](https://github.com/curefit/port-forward) to configure access to upstream services
- Configure `~/.ssh/config` as:
```
Host stage
    HostName stage.curefit.co
    Port 22
    User first.last.at.curefit.com

    LocalForward 3306 cfdb.cw5efoqjpxhj.ap-south-1.rds.amazonaws.com:3306
    LocalForward 6379 curefit.y66lea.0001.aps1.cache.amazonaws.com:6379
    LocalForward 6374 curefit-cf-api-cache.stage.cure.fit.internal:6379
    LocalForward 6381 platforms-segmentation-cache.stage.cure.fit.internal:6379
    LocalForward 6366 platforms-cache.stage.cure.fit.internal:6379
```

## Run

- Databases: `ssh -N stage` to run the SSH tunnel to MySQL and Redis instances above
- Services: `yarn forward` inside [curefit/port-forward](https://github.com/curefit/port-forward)
- Dependencies: `yarn install`
- Server: `yarn start`, runs on localhost:3000

## kdever

1. Create a Virtual Cluster and deploy your branch using Voyager: https://voyager-ui.curefit.co

2. Start the sync
    ```
    sh ./kdever.sh vcName
    ```
3. Build the code on local and it will sync all changes to Virtual Cluster
    ```
    npm run build --max-old-space-size=8000
    ```
4. For testing, you can add header as we do for Virtual cluster or kdever starts port-forwarding too.
   ```
   curl -H "virtual-cluster-name: kdever-onboarding" https://stage.cult.fit/api/cart
   ```

