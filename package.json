{"name": "curefit-api", "version": "1.0.0", "description": "Gateway server to all upstream CureFit services", "main": "index.js", "scripts": {"start": "yarn run clean && yarn run build && yarn run watch", "build": "tsc --project tsconfig.json && yarn run copy-configs && yarn run tslint", "copy-configs": "mkdir -p dist/conf/; cp src/conf/*.json dist/conf/; cp src/app/eat/*.json dist/app/eat/;", "clean": "rm -rf dist && mkdir -p dist", "serve": "APP_NAME=curefit-api node --max-old-space-size=4096 --max_semi_space_size=256 dist/index.js", "serve-inspect": "APP_NAME=curefit APP_ENV=LOCAL ENVIRONMENT=LOCAL NODE_ENV=development NODE_HEAPDUMP_OPTIONS=nosignal nodemon --inspect --delay 2000ms dist/index.js", "watch": "concurrently --kill-others --names \"typescript,test,cf-api\" --prefix-colors \"cyan.bold,blue.bold,green.bold\" \"yarn run watch-ts\" \"yarn test:watch\" \"yarn run serve-inspect\"", "tslint": "tslint -c tslint.json -p tsconfig.json", "tslint-fix": "tslint -c tslint.json -p tsconfig.json --fix", "watch-ts": "yarn run copy-configs && tsc --project tsconfig.json -w", "test": "jest --maxWorkers=10", "test:watch": "jest --watch --maxWorkers=10", "start-dev": "APP_NAME=curefit APP_ENV=LOCAL ENVIRONMENT=LOCAL ts-node-dev --inspect --respawn --transpile-only --ignore-watch node_modules dist/index.js"}, "author": "Kunal Chourasia", "license": "ISC", "repository": {"type": "git", "url": "git+https://github.com/curefit/curefit-api.git"}, "engines": {"node": ">=16"}, "private": true, "dependencies": {"@curefit/sprinklr-node-client": "2.5.0", "@curefit/access-common": "^1.1.0", "@curefit/albus-client": "6.173.0", "@curefit/alfred-client": "11.42.0", "@curefit/app-config-client": "1.2.0", "@curefit/app-config-common": "1.0.0", "@curefit/apps-common": "3.15.0", "@curefit/atlas-client": "^1.1.0", "@curefit/auth-models": "^1.3.1", "@curefit/base": "^7.12.0", "@curefit/base-common": "2.74.0", "@curefit/base-mongo": "^3.1.0", "@curefit/base-utils": "10.63.0", "@curefit/cache-client": "2.5.0", "@curefit/cache-service-client": "^0.2.0", "@curefit/cache-utils": "4.1.0", "@curefit/caesar-client": "^9.13.0", "@curefit/caesar-models": "^2.12.0", "@curefit/cafe-api-client": "^1.2.0", "@curefit/care-common": "4.40.0", "@curefit/catalog-client": "5.18.0", "@curefit/catalog-common": "1.0.0", "@curefit/center-service-client": "0.56.0", "@curefit/center-service-common": "0.54.0", "@curefit/cfs-client": "1.6.0", "@curefit/cfs-common": "^1.0.0", "@curefit/cloudwatch-client": "^1.2.0", "@curefit/config-mongo": "^1.40.0", "@curefit/config-store-client": "2.0.1", "@curefit/constello-client": "2.8.0", "@curefit/constello-common": "1.56.0", "@curefit/counter-mongo": "^1.1.0", "@curefit/cult-bike-client": "0.1.0", "@curefit/cult-bike-common": "0.1.0", "@curefit/cult-client": "9.12.0", "@curefit/cult-common": "3.103.0", "@curefit/cult-models": "^1.2.0", "@curefit/cultsport-feedback-client": "0.17.0", "@curefit/curio-client": "1.8.0", "@curefit/delivery-client": "^8.36.0", "@curefit/delivery-models": "1.13.0", "@curefit/device-common": "^1.8.0", "@curefit/device-models": "1.15.0", "@curefit/diy-client": "5.80.1", "@curefit/diy-common": "4.80.1", "@curefit/diy-mongo": "^5.5.0", "@curefit/eat": "2.55.0", "@curefit/eat-api-client": "5.2.0", "@curefit/eat-api-common": "^2.0.9", "@curefit/eat-common": "8.23.0", "@curefit/eat-util": "^3.10.0", "@curefit/ehr-client": "0.2.0", "@curefit/email-client": "^1.6.0", "@curefit/enterprise-client": "1.9.0", "@curefit/enterprise-common": "^2.78.3", "@curefit/error-client": "4.20.0", "@curefit/error-common": "2.21.0", "@curefit/events-util": "2.0.4", "@curefit/expression-managed-functions": "0.11.0", "@curefit/feedback-common": "1.49.0", "@curefit/feedback-mongo": "1.19.0", "@curefit/finance-common": "1.16.0", "@curefit/finance-models": "^0.3.0", "@curefit/fitcash-client": "3.4.0", "@curefit/fitcash-common": "1.2.1", "@curefit/fitclub-client": "6.2.0", "@curefit/fitclub-models": "3.7.1", "@curefit/fitness-common": "4.76.0", "@curefit/flash-client": "^4.14.0", "@curefit/flash-common": "^1.11.3", "@curefit/flash-models": "^1.13.0", "@curefit/food-common": "^1.9.0", "@curefit/foodway-client": "1.9.0", "@curefit/foodway-common": "1.8.0", "@curefit/fulfilment-common": "^1.7.1", "@curefit/fuse-node-client": "0.15.1", "@curefit/gandalf-common": "0.5.2", "@curefit/gear-common": "3.45.0", "@curefit/gearvault-client": "5.44.0", "@curefit/gmf-client": "^2.2.3", "@curefit/gmf-common": "^3.5.5", "@curefit/gymfit-client": "2.81.0", "@curefit/gymfit-common": "2.15.0", "@curefit/hamlet-client": "2.16.0", "@curefit/hamlet-common": "0.26.1", "@curefit/hamlet-node-sdk": "4.7.2", "@curefit/hercules-client": "1.36.0", "@curefit/hercules-models": "^1.2.0", "@curefit/hofund": "^3.7.1", "@curefit/identity-client": "2.3.0", "@curefit/indus-client": "4.8.0", "@curefit/intervention-common": "1.11.0", "@curefit/intervention-models": "1.3.4", "@curefit/invoice-common": "^1.2.1", "@curefit/invoice-models": "^1.8.0", "@curefit/iris-client": "^3.1.0", "@curefit/iris-common": "^1.26.0", "@curefit/iris-models": "^2.0.6", "@curefit/issue-common": "1.48.0", "@curefit/issue-models": "^1.4.0", "@curefit/kitchen": "^1.8.0", "@curefit/location-common": "4.14.0", "@curefit/location-mongo": "4.16.0", "@curefit/location-service": "1.25.0", "@curefit/lock-utils": "3.0.0-alpha.1", "@curefit/logging-client": "3.10.0", "@curefit/logging-common": "2.45.0", "@curefit/logging-models": "3.37.0", "@curefit/logistics-common": "^0.2.18", "@curefit/magneto-client": "1.0.0", "@curefit/marketplace-common": "^1.2.3", "@curefit/masterchef-client": "^17.36.0", "@curefit/masterchef-models": "^9.27.0", "@curefit/maverick-client": "^1.0.2", "@curefit/maverick-common": "^1.0.0", "@curefit/maximus-common": "1.0.8", "@curefit/maxmind-client": "^1.2.0", "@curefit/media-gateway-js-client": "^1.9.0", "@curefit/membership-client": "2.5.2", "@curefit/membership-commons": "1.13.0", "@curefit/memory-cache": "3.4.0", "@curefit/metrics": "1.7.0", "@curefit/metrics-common": "^1.0.3", "@curefit/mind-common": "^1.6.0", "@curefit/mongo-utils": "4.21.0", "@curefit/mysql-utils": "3.1.1-alpha.1", "@curefit/neo-common": "^1.1.0", "@curefit/neo-models": "^1.1.0", "@curefit/nest-node-client": "0.1.7", "@curefit/offer-common": "3.2.0", "@curefit/offer-service-client": "8.8.0", "@curefit/ollivander-node-client": "3.6.0", "@curefit/oms-api-client": "7.26.0", "@curefit/order-common": "7.94.0", "@curefit/order-models": "4.93.0", "@curefit/pack-management-service-client": "2.35.0", "@curefit/pack-management-service-common": "2.28.0", "@curefit/payment-client": "11.22.0", "@curefit/payment-common": "9.69.0", "@curefit/payment-models": "5.30.0", "@curefit/personal-training-v2-client": "1.24.0", "@curefit/personal-training-v2-common": "1.21.0", "@curefit/product-common": "5.85.0", "@curefit/product-mongo": "1.71.0", "@curefit/product-service": "^1.10.0", "@curefit/program-client": "^1.2.0", "@curefit/quest-client": "2.21.0", "@curefit/quest-common": "3.14.0", "@curefit/quest-models": "^4.8.0", "@curefit/ramsay-client": "^1.5.0", "@curefit/rashi-client": "3.14.0-alpha.1", "@curefit/recommendation-client": "2.0.0", "@curefit/redis-utils": "^6.10.0", "@curefit/referral-client": "2.6.0", "@curefit/referral-common": "1.10.0", "@curefit/report-issues-client": "2.40.0", "@curefit/reward-client": "1.27.0", "@curefit/reward-common": "1.43.0", "@curefit/riddler-cache": "0.12.1", "@curefit/riddler-client": "3.0.1", "@curefit/riddler-common": "^1.0.0", "@curefit/schema-mongo": "^1.3.0", "@curefit/segment-common": "1.13.0", "@curefit/segment-sdk": "2.2.0", "@curefit/segmentation-client": "3.0.0", "@curefit/segmentation-service-client": "2.29.0", "@curefit/shipment-common": "^3.1.0", "@curefit/shipment-models": "^1.30.0", "@curefit/sms-client": "^1.1.0", "@curefit/social-client": "1.20.0", "@curefit/social-common": "1.12.0", "@curefit/sports-api-client-node": "1.21.0", "@curefit/sqs-client": "2.6.0", "@curefit/third-party-integrations-client": "2.3.0", "@curefit/ufs-client": "1.39.0", "@curefit/user-client": "9.14.0", "@curefit/user-common": "2.26.0", "@curefit/user-message-mongo": "2.0.0", "@curefit/user-message-translator": "0.6.0", "@curefit/user-models": "1.3.2", "@curefit/user-test-client": "^1.1.0", "@curefit/userinfo-common": "5.1.0", "@curefit/util-common": "3.12.0-alpha.11", "@curefit/vm-common": "3.122.0", "@curefit/vm-models": "11.15.0", "@curefit/wholefit-api-client": "^1.0.3", "@curefit/wholefit-common": "^1.1.1", "@curefit/workflow-common": "^0.0.4", "@sentry/node": "^7.64.0", "@types/url-parse": "^1.4.8", "async": "^3.2.4", "aws-sdk": "^2.1438.0", "body-parser": "^1.19.0", "bookshelf": "1.0.1", "class-transformer": "^0.5.1", "class-transformer-validator": "^0.9.1", "class-validator": "^0.14.0", "clone": "^2.1.2", "cls-hooked": "4.2.2", "cls-mongoose": "^2.0.1", "cookie-parser": "^1.4.3", "csv-parse": "^4.6.1", "dd-trace": "^5.21.0", "deep-diff-pizza": "^1.0.2", "denque": "2.1.0", "es6-dynamic-template": "2.0.0", "express": "^4.18.2", "express-useragent": "^1.0.13", "firebase": "10.13.0", "openai": "^4.20.1", "firebase-admin": "12.4.0", "form-data": "^2.1.1", "inversify": "^6.0.1", "inversify-express-utils": "^6.4.3", "ioredis": "^4.28.5", "ipaddr.js": "^1.9.1", "json-schema": "0.4.0", "knex": "0.20.1", "lodash": "^4.17.21", "lru-cache": "^7.14.1", "maxmind": "^2.6.0", "method-override": "^2.3.5", "mime-types": "^2.1.21", "mixpanel": "0.17.0", "moment": "^2.29.4", "moment-timezone": "^0.5.14", "mustache": "^2.3.0", "mysql": "2.18.1", "node-cache": "^5.1.2", "node-cron": "3.0.2", "node-dogstatsd": "^0.0.7", "node-fetch": "2.6.7", "nodemailer": "^6.7.1", "nodemailer-ses-transport": "^1.5.1", "number-to-words": "^1.2.3", "object-hash": "^1.2.0", "pemtools": "^0.4.7", "prom-client": "^14.2.0", "qs": "6.7.3", "query-string": "^6.1.0", "redis": "^3.1.2", "redlock": "5.0.0-beta.2", "reflect-metadata": "^0.1.12", "sitemap": "6.3.2", "tsutils": "3.17.0", "twilio": "^3.19.1", "url-parse": "^1.5.10", "url-pattern": "^1.0.3", "uuid": "^7.0.3", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "google-auth-library": "^9.14.0"}, "devDependencies": {"@curefit/base": "^7.12.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@types/async": "^3.2.20", "@types/aws-sdk": "^2.7.0", "@types/bluebird": "^3.0.36", "@types/body-parser": "^0.0.34", "@types/bookshelf": "0.13.0", "@types/cls-hooked": "4.2.1", "@types/compression": "^0.0.33", "@types/continuation-local-storage": "^3.2.1", "@types/crypto-js": "^3.1.43", "@types/express": "^4.16.1", "@types/express-useragent": "^1.0.0", "@types/form-data": "^2.2.1", "@types/helmet": "^0.0.33", "@types/html-pdf": "^2.1.2", "@types/ioredis": "^4.27.4", "@types/isomorphic-fetch": "^0.0.34", "@types/jest": "^29.5.6", "@types/knex": "0.16.1", "@types/lodash": "^4.14.96", "@types/lru-cache": "^7.10.10", "@types/maxmind": "^2.0.2", "@types/method-override": "^0.0.29", "@types/mime": "^0.0.29", "@types/mime-types": "^2.1.0", "@types/moment-timezone": "^0.5.3", "@types/morgan": "^1.7.32", "@types/mustache": "^0.8.31", "@types/mysql": "^2.15.5", "@types/node": "^18.15.11", "@types/node-cache": "^4.2.5", "@types/node-fetch": "^2.5.7", "@types/nodemailer": "^6.4.4", "@types/nodemailer-ses-transport": "^1.5.1", "@types/object-hash": "^1.1.0", "@types/opossum": "^6.2.2", "@types/query-string": "^6.1.0", "@types/redis": "^2.8.32", "@types/reflect-metadata": "^0.1.0", "@types/shortid": "^0.0.29", "@types/uuid": "^3.4.3", "@types/winston": "^2.4.4", "babel-jest": "^29.7.0", "concurrently": "^4.1.2", "husky": "^4.2.0", "jest": "^29.7.0", "node-gyp": "^9.3.0", "nodemon": "^2.0.16", "ts-jest": "^29.1.1", "ts-node-dev": "^1.1.8", "tslint": "^6.1.3", "typescript": "4.8.4"}, "resolutions": {"@curefit/base": "^7.5.0", "@aws-sdk/client-sqs": "3.36.1", "@curefit/albus-client": "6.173.0", "@curefit/order-models": "4.93.0", "@curefit/alfred-client": "11.42.0", "@curefit/indus-client": "4.8.0", "@curefit/app-config-client": "1.2.0", "@curefit/app-config-common": "1.0.0", "@curefit/apps-common": "3.15.0", "@curefit/auth-models": "^1.4.3", "@curefit/base-common": "2.74.0", "@curefit/base-mongo": "3.1.0", "@curefit/base-utils": "10.63.0", "@curefit/cache-client": "2.5.0", "@curefit/cache-service-client": "^0.2.0", "@curefit/cache-utils": "4.1.0", "@curefit/caesar-client": "^9.13.0", "@curefit/caesar-models": "^2.12.0", "@curefit/care-common": "4.40.0", "@curefit/catalog-client": "5.18.0", "@curefit/catalog-common": "1.0.0", "@curefit/center-service-client": "0.56.0", "@curefit/center-service-common": "0.54.0", "@curefit/cfs-client": "1.6.0", "@curefit/config-mongo": "^1.40.0", "@curefit/constello-common": "1.56.0", "@curefit/cult-bike-client": "0.1.0", "@curefit/cult-bike-common": "0.1.0", "@curefit/cult-client": "9.12.0", "@curefit/sports-api-client-node": "1.18.0", "@curefit/cult-common": "3.103.0", "@curefit/cultsport-feedback-client": "0.17.0", "@curefit/delivery-client": "^8.36.0", "@curefit/delivery-models": "1.13.0", "@curefit/device-common": "^1.8.0", "@curefit/device-models": "1.15.0", "@curefit/diy-client": "5.80.1", "@curefit/diy-common": "4.80.1", "@curefit/diy-mongo": "5.5.0", "@curefit/eat-api-client": "5.2.0", "@curefit/eat-api-common": "^2.0.9", "@curefit/eat-common": "8.23.0", "@curefit/eat-util": "^3.10.0", "@curefit/eat": "2.55.0", "@curefit/ehr-client": "0.2.0", "@curefit/error-client": "4.20.0", "@curefit/error-common": "2.21.0", "@curefit/events-util": "2.0.4", "@curefit/expression-utils": "1.3.0", "@curefit/feedback-common": "1.49.0", "@curefit/feedback-mongo": "1.19.0", "@curefit/finance-common": "1.16.0", "@curefit/finance-models": "^0.3.0", "@curefit/fitcash-client": "3.4.0", "@curefit/fitcash-common": "1.2.1", "@curefit/fitclub-client": "6.2.0", "@curefit/fitclub-models": "3.7.1", "@curefit/fitness-common": "4.76.0", "@curefit/flash-client": "^4.14.0", "@curefit/flash-common": "^1.11.3", "@curefit/flash-models": "^1.13.0", "@curefit/food-common": "^1.9.0", "@curefit/food-mongo": "^0.9.0", "@curefit/foodway-client": "1.9.0", "@curefit/foodway-common": "1.8.0", "@curefit/freshdesk-common": "0.0.7", "@curefit/fuse-node-client": "0.15.1", "@curefit/gandalf-common": "0.5.2", "@curefit/gear-common": "3.45.0", "@curefit/gearvault-client": "5.44.0", "@curefit/gmf-common": "^3.5.5", "@curefit/gymfit-client": "2.81.0", "@curefit/gymfit-common": "2.15.0", "@curefit/hamlet-client": "2.16.0", "@curefit/hamlet-common": "0.26.1", "@curefit/hamlet-node-sdk": "4.7.2", "@curefit/hercules-client": "1.36.0", "@curefit/hofund": "^3.7.1", "@curefit/identity-client": "2.3.0", "@curefit/intervention-common": "1.11.0", "@curefit/invoice-common": "^1.2.1", "@curefit/invoice-models": "^1.8.0", "@curefit/iris-client": "^3.1.0", "@curefit/iris-common": "^1.26.0", "@curefit/issue-common": "1.48.0", "@curefit/issue-models": "1.4.0", "@curefit/offer-service-client": "8.8.0", "@curefit/kitchen": "^1.8.0", "@curefit/location-common": "4.14.0", "@curefit/location-mongo": "4.16.0", "@curefit/lock-utils": "3.0.0-alpha.1", "@curefit/logging-client": "3.10.0", "@curefit/logging-common": "2.45.0", "@curefit/logging-models": "3.37.0", "@curefit/logistics-common": "^0.2.18", "@curefit/magneto-client": "1.0.0", "@curefit/marketplace-common": "^1.2.3", "@curefit/masterchef-client": "^17.36.0", "@curefit/masterchef-models": "^9.27.0", "@curefit/membership-client": "2.5.2", "@curefit/membership-commons": "1.13.0", "@curefit/memory-cache": "3.4.0", "@curefit/metrics-common": "^1.0.3", "@curefit/metrics": "1.7.0", "@curefit/mind-common": "^1.6.0", "@curefit/mongo-utils": "4.21.0", "@curefit/mysql-utils": "3.1.1-alpha.1", "@curefit/offer-common": "3.2.0", "@curefit/ollivander-node-client": "3.6.0", "@curefit/oms-api-client": "7.26.0", "@curefit/order-common": "7.94.0", "@curefit/pack-management-service-common": "2.28.0", "@curefit/payment-client": "11.22.0", "@curefit/payment-common": "9.69.0", "@curefit/payment-models": "5.30.0", "@curefit/personal-training-v2-client": "1.24.0", "@curefit/personal-training-v2-common": "1.21.0", "@curefit/product-common": "5.85.0", "@curefit/product-mongo": "1.71.0", "@curefit/product-service": "^1.10.0", "@curefit/program-client": "^1.2.0", "@curefit/quest-client": "2.21.0", "@curefit/quest-common": "3.14.0", "@curefit/quest-models": "4.13.0", "@curefit/rashi-client": "3.14.0-alpha.1", "@curefit/recommendation-client": "2.0.0", "@curefit/redis-utils": "^6.10.0", "@curefit/referral-client": "2.6.0", "@curefit/referral-common": "1.10.0", "@curefit/reward-common": "1.43.0", "@curefit/riddler-cache": "0.12.1", "@curefit/riddler-client": "3.0.1", "@curefit/segment-common": "1.13.0", "@curefit/segment-sdk": "2.2.0", "@curefit/segmentation-client": "3.0.0", "@curefit/segmentation-service-client": "2.29.0", "@curefit/shipment-common": "^3.1.0", "@curefit/shipment-models": "^1.30.0", "@curefit/social-client": "1.20.0", "@curefit/social-common": "1.12.0", "@curefit/sqs-client": "2.6.0", "@curefit/user-client": "9.14.0", "@curefit/user-common": "2.26.0", "@curefit/user-message-mongo": "2.0.0", "@curefit/user-message-translator": "0.6.0", "@curefit/user-models": "1.3.2", "@curefit/userinfo-common": "5.1.0", "@curefit/util-common": "3.12.0-alpha.11", "@curefit/vm-common": "3.122.0", "@curefit/vm-models": "11.15.0", "@curefit/wholefit-common": "^1.1.1", "@sentry/node": "^7.64.0", "@types/async": "^3.2.20", "@types/bookshelf": "0.13.0", "@types/knex": "0.16.1", "async": "^3.2.4", "aws-sdk": "^2.1438.0", "axios": "^0.25.0", "bookshelf": "1.0.1", "class-transformer-validator": "^0.9.1", "class-transformer": "^0.5.1", "dd-trace": "^5.21.0", "class-validator": "^0.14.0", "express": "^4.18.2", "firebase": "10.13.0", "firebase-admin": "12.4.0", "@firebase/firestore": "^4.7.0", "inversify-express-utils": "6.4.3", "inversify": "6.0.1", "json-schema": "0.4.0", "knex": "0.20.1", "lodash": "^4.17.21", "minimist": "^1.2.6", "mongoose": "6.11.5", "node-cache": "^5.1.2", "node-fetch": "2.6.7", "nodemailer": "^6.7.1", "prebuild-install": "^7.0.1", "prom-client": "^14.2.0", "redlock": "5.0.0-beta.2", "snappy": "7.2.2", "tar": "^6.1.11", "typescript": "4.4.4", "underscore": "^1.13.1", "url-parse": "^1.5.10", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "y18n": "^4.0.3", "denque": "2.1.0", "mysql2": "3.9.7"}}