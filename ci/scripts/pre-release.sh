#!/usr/bin/env bash

set -e

APP_ENV=$1

export APP_VERSION=$(sentry-cli releases propose-version)

echo "Configuring release on sentry ..."

export SENTRY_ORG="curefit"
export SENTRY_PROJECT="curefit-api-node"
export SENTRY_AUTH_TOKEN="****************************************************************"

sentry-cli releases new "$APP_VERSION"
sentry-cli releases deploys "$APP_VERSION" new -e "$APP_ENV"
sentry-cli releases set-commits "$APP_VERSION" --auto

sentry-cli releases finalize "$APP_VERSION"
