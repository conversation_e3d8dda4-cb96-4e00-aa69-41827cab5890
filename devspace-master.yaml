version: v2beta1
name: updated-by-kdever-master
pipelines:
  dev:
    run: start_dev app
dev:
  app:
    workingDir: /curefit-api
    labelSelector:
      app: ${APP_NAME}
      version: ${VIRTUAL_CLUSTER}
    args:
      - '-c'
      - >-
        node --inspect --max-old-space-size=4096 --max_semi_space_size=512
        dist/index.js
    container: ${APP_NAME}
    command:
      - /bin/bash
    restartHelper:
      inject: true
    env:
      - name: VERSION
        value: ${VIRTUAL_CLUSTER}
    ssh:
      enabled: true
      useInclude: true
    logs: {}
    ports:
      - port: '3000:3000'
      - port: '9229:9229'
    sync:
      - path: ./dist:dist
        printLogs: true
        disableDownload: true
        initialSync: preferLocal
        onUpload:
          restartContainer: true
