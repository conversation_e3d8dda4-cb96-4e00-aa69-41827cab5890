version: v1beta10

images:
  app:
    image: ${VC_DOCKER_IMAGE_PATH}
    tags:
      - ${PREVIEW_VERSION}
    entrypoint:
      - "/bin/bash"
    cmd:
      - "-c"
      - "APP_NAME=curefit-api node --inspect --max-old-space-size=4096 --max_semi_space_size=512 dist/index.js"
    injectRestartHelper: true
    build:
      kaniko:
        cache: false
        initImage: public.ecr.aws/docker/library/alpine:latest
        options:
          buildArgs:
            ENVIRONMENT: ${ENVIRONMENT}
            APP_NAME: ${APP_NAME}
            NPM_TOKEN: ${NPM_TOKEN}
            GITHUB_NPM_TOKEN: ${GITHUB_NPM_TOKEN}

dev:
  ports:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      forward:
        - port: 3000
          remotePort: 3000
        - port: 9229
          remotePort: 9229
  sync:
    - labelSelector:
        app: ${APP_NAME}
        version: ${VIRTUAL_CLUSTER}
      containerName: ${APP_NAME}
      localSubPath: ./dist
      containerPath: /curefit-api/dist
      disableDownload: true
      initialSync: preferLocal
      onUpload:
        restartContainer: true
  logs:
    disabled: true
