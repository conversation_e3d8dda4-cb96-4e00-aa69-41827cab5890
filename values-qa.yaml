istio:
  allOutboundInterception: true
  external:
    hosts:
      - qa.cult.fit
    matchAdditional:
      - match:
        - exact: /api/liveInSession/playbackMetrics
        rewrite: /liveInSession/playbackMetrics
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - exact: /api/liveInSession/updateScoreMetrics
        rewrite: /liveInSession/updateScoreMetrics
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - exact: /api/page/widget
        rewrite: /page/widget
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - exact: /api/digital/getVideoData
        rewrite: /digital/getVideoData
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/
        rewrite: /
        route: curefit-api-qa
        namespace: curefit-api

  internal:
    hosts:
      - cfapi-qa.alpha.cure.fit.internal
    matchAdditional:
      - match:
        - exact: /api/user/status
        rewrite: /user/status
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/user/state
        rewrite: /user/state
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/page/gearLanding/
        rewrite: /page/gearLanding/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/live
        rewrite: /page/live/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/CultAtHome
        rewrite: /page/CultAtHome/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - exact: /api/digital/live/search
        rewrite: /digital/live/search
        route: curefit-api-v2
        namespace: curefit-api-v2
      - match:
        - prefix: /api/page/widget/
        rewrite: /page/widget/
        route: curefit-api-qa
        namespace: curefit-api
      - match:
        - prefix: /api/page/
        rewrite: /page/
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/v2/
        rewrite: /
        route: curefit-api-v2-qa
        namespace: curefit-api-v2
      - match:
        - prefix: /api/
        - prefix: /nodeimpl/
        rewrite: /
        route: curefit-api-qa
        namespace: curefit-api

service:
  expose:
    - 3000
deployment:
  tolerations:
    # this toleration is to have the app runnable on arm nodes
    - key: graviton
      operator: Equal
      value: 'true'
      effect: NoSchedule
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/arch
          operator: In
          values:
            - arm64
  dnsPolicy: "None"
  labels:
    billing: cfapi
    sub-billing: curefit-api
  logDir: curefit-api
  podAnnotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::035243212545:role/CF-API-Prod
  probePath: /status
  probePort: 3000
  resources:
    limits:
      cpu: 1000m
      memory: 4Gi
    requests:
      cpu: 100m
      memory: 1Gi
  env:
    - name: ENVIRONMENT
      value: "PRODUCTION"
    - name: APP_ENV
      value: "ALPHA"
    - name: APP_NAME
      value: "curefit-api"
    - name: NODE_ENV
      value: "production"
    - name: PORT
      value: 3000
    - name: MASTER_PORT
      value: 13000
    - name: TZ
      value: "Asia/Kolkata"
    - name: UV_THREADPOOL_SIZE
      value: 120

externalSecrets:
  enabled: "true"
  names:
    - prod/redis/curefit-cf-api-cache
    - prod/redis/cult-prod-cache
    - prod/redis/curefit-prod-default-cache
    - prod/redis/production-session-transient-cache
    - prod/redis/prod-eatfit-redis
    - prod/redis/riddler-service-cache
    - prod/redis/catalog-service-cache
    - prod/redis/platforms-segmentation-cache
